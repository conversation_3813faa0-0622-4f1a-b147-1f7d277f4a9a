import {
    <PERSON><PERSON>ty,
    Column,
    BaseEntity,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    ManyToOne,
    JoinColumn,
    OneToOne
  } from 'typeorm';
  import { Property_Usage_Details_Entity } from './property-usage-details.entity';
  import { PropertyEntity } from './property.entity';
  
  @Entity('deleted_property_usage_details')
  export class DeletedPropertyUsageEntity extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    deleted_property_usage_details_id: string;

    // @Column({type: 'string', name: 'deletedBy'})
    // deletedBy:  string;

    @ManyToOne(() => PropertyEntity, (property) => property.property_id, { nullable: false  })
    @JoinColumn({ name: 'property_id' })
    property: PropertyEntity;
  
    @OneToOne(() => Property_Usage_Details_Entity, { nullable: false, cascade: false })
    @JoinColumn({ name: 'property_usage_details_id' })
    property_usage: Property_Usage_Details_Entity;
  
    @CreateDateColumn({ type: 'timestamp' })
    createdAt: Date;
  }
  