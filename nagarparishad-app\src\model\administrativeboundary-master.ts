export interface AdministrativeBoundaryObjectInterface {
  adminstrativeBoundary_id: string;
  adminstrativeBoundary_name: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string | null;
}

export interface AdministrativeBoundaryApiResponse {
  statusCode?: number;
  message: string;
  data?: AdministrativeBoundaryObjectInterface[] | null;
}

export interface AdministrativeBoundaryCreateObject {
  adminstrativeBoundary_name: string;
  description?: string | null;
}

export interface AdministrativeBoundaryCreateResObject {
  statusCode: number;
  message: string;
}

export interface AdministrativeBoundaryUpdateObject {
  adminstrativeBoundary_name?: string;
  description?: string | null;
}

export interface AdministrativeBoundaryUpdateApiResponse {
  statusCode: number;
  message: string;
}
