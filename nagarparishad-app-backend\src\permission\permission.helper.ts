import { Injectable } from '@nestjs/common';

@Injectable()
export class PermissionHelper {
  async formatData(permissionsData, moduleData) {
    const permissionsMap = new Map<number, any>();

    //Format Data According to the Form Master
    permissionsData.forEach((item) => {
      const formId = item.form.form_id;
      if (!permissionsMap.has(formId)) {
        permissionsMap.set(formId, {
          form_id: formId,
          formName: item.form.formName,
          permissions: [],
        });
      }
      permissionsMap.get(formId).permissions.push({
        action_id: item.action_id,
        can_read: item.can_read,
        can_write: item.can_write,
        can_update: item.can_update,
        can_delete: item.can_delete,
        is_valid: item.is_valid,
        role: item.role,
      });
    });

    // Combine permissions with module data
    const mergedData = moduleData.map((module: any) => {
      const formsWithPermissions = module.forms.map((form: any) => {
        const permissions = permissionsMap.get(form.form_id);
        return permissions ? permissions : form;
      });

      return {
        module_id: module.module_id,
        moduleName: module.moduleName,
        forms: formsWithPermissions,
      };
    });

    return mergedData;
  }
}
