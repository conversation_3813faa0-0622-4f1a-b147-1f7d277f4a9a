import { PaginationDto } from "@helper/helpers/Pagination";
import { IsNotEmpty, IsOptional, IsString, Length, ValidateIf } from "class-validator";

export class PublicUserDto extends PaginationDto {

    @IsOptional()
    @IsNotEmpty()
    @IsString()
    @Length(10, 10, { message: 'Mobile number must be exactly 10 digits' })
    mobile_number: string;

    @IsOptional()
    @IsNotEmpty()
    @IsString()
    propertyNumber: string;

    @IsOptional()
    @IsString()
    ward: string;

    @IsOptional()
    @IsString()
    zone: string;

    @ValidateIf(o => o.ward || o.zone) // If ward or zone is provided, name is required
    @IsNotEmpty({ message: 'Name must be provided if ward or zone is specified' })
    @IsString()
    name: string;



}

export class ValidateOtp {

    @IsNotEmpty()
    @IsString({message: 'VerificationId must be 12 digits'})
    @Length(12, 12, { message: 'VerificationId must be exactly 12 digits' })
    verificationId: string;

    @IsNotEmpty()
    @Length(6,6, {message: 'Otp Must be of 6 Digit'})
    otp: string;
}