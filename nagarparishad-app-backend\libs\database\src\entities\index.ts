import { CommonFiledsOfPropertyEntity } from './commonFiledsOfProperty.entity';
import { AdminstrativeBoundaryMasterEntity } from './adminstrative-boundaryMaster.entity';
import { AreaLocalityMasterEntity } from './area-localityMaster.entity';
import { ConstructionClassEnitity } from './construction_class.entity';
import { ElectionBoundaryMasterEntity } from './election-boundaryMaster.entity';
import { FormMasterEntity } from './formMaster.entity';
import { LocationEntity } from './locationMaster.entity';
import { ModuleMasterEntity } from './module-master.entity';
import { PropertySubTypeMasterEntity } from './property-sub-type-master.entity';
import { PropertyTypeMasterEntity } from './property-type-master.entity';
import { PropertyEntity } from './property.entity';
import { RoleMasterEntity } from './roleMaster.entity';
import { RolewiseFormPermissionEntity } from './rolewise-form-permission.entity';
import { StreetMasterEntity } from './streetMaster.entity';
import { UsageTypeMasterEntity } from './usage-type-master.entity';
import { UsageSubTypeMasterEntity } from './usage-sub-type-master.entity';
import { UserOtpEntity } from './user-otp.entity';
import { UserMasterEntity } from './userMaster.entity';
import { Ward_Master } from './ward_master.entity';
import { ZoneMaster } from './zoneMaster.entity';
import { Tax_PropertyWiseEntity} from './tax_propertywise.entity';
import { Tax_PropertyEntity} from './tax_property.entity';
import { Tax_FY_RecordsEntity } from './tax_fy_records.entity';
import { Property_Usage_Details_Entity } from './property-usage-details.entity';
import { Tax_Type_Master} from './tax_type_master.entity';
import { Financial_year} from './financial_year.entity';
import { Property_Owner_Details_Entity } from './property-owner-details.entity';
import { Owner_type_master} from './owner_type_master.entity';
import { Tax_PropertyEntity_Other_Taxes } from './tax_property_other_taxes.entity';
import { Import_PropertyEntity } from './Import_property.entity'
import { Import_PropertyStatsEntity } from './Import_property_stats.entity'
import { GIS_data_Entity } from './GIS_data.entity';
import { MilkatKarEntity} from './milkat-kar.entity';
import { MilkatKarTaxEntity} from './milkatKarTax.entity';
import { Master_rr_rateEntity } from './master_rr_rate.entity';
import { Master_rr_construction_rate } from './master_rr_construction_rate.entity';
import { Master_tax_rateEntity } from './master_tax_rate.entity';
import { Master_weighting_rateEntity } from './master_weighting_rate.entity';
import { Master_depreciation_rate } from './master_depreciation_rate.entity';

import { Logs } from './logs.entity';
import { WarshilKarEntity } from './warshik_kar.entity';
import { WarshilKarTaxEntity } from './warshik_karTax.entity';
import {Property_type_class_master} from './property_type_classMaster.entity'

import {Wrong_property_typeMaster} from './wrong_property_typeMaster.entity';
import { Floor_Master } from './floorMaster.entity';
import { Master_ghanKachra_rateEntity } from './master_ghanKachra_rate.entity';
import { Property_Fod_Details_Entity } from './property_fod_details.entity';
import { Property_Ferfar_Detail_Entity } from './property_ferfar_details.entity';
import {TaxPendingDuesEntity} from './tax_pendingDues.entity';
import { BillDataEntity} from './billData.entity'

import { PaymentInfoEntity } from './payment_info.entity';
import { ReceiptEntity } from './receipt.entity';
import { PaidDataEntity } from './paid_data.entity';
import { PaidAndNonPaidDataEntity } from './paid-and-non-paid-data.entity';

import { BookNumberMasterEntity } from './book_numberMaster.entity';
import { DeletedPropertyUsageEntity } from './deleted_property_usage_details.entity';
import { DeletedPropertyEntity } from './deleted_property.entity';
import { BackupPropertyUsageDetailsEntity } from './backup_property_usage_details';
import { BackupPaymentDetailsEntity } from './backup_payment_details.entity';
import { PreviousOwnerEntity } from './previous_property_owners.entity';
import { DemandReportData } from './demandeReportData.entity';
import { CollectorMaster } from './collectorMaster.entity';
import { ReassessmentRange } from './reassesment_range.entity';
import { PenaltyFeeYearWiseEntity } from './penalty_fee_yearWise.entity';
import { CronJobFailureEntity } from './cron-job-failure.entity';
import { OfflineNotificationEntity } from './offline-notification.entity';
import { RegisterNumberEntity } from './register_number.entity';

export const entities = [
  Ward_Master,
  ZoneMaster,
  LocationEntity,
  StreetMasterEntity,
  AreaLocalityMasterEntity,
  AdminstrativeBoundaryMasterEntity,
  ElectionBoundaryMasterEntity,
  PropertyTypeMasterEntity,
  PropertySubTypeMasterEntity,
  UsageTypeMasterEntity,
  UsageSubTypeMasterEntity,
  ConstructionClassEnitity,
  PropertyEntity,
  RoleMasterEntity,
  FormMasterEntity,
  UserMasterEntity,
  UserOtpEntity,
  RolewiseFormPermissionEntity,
  ModuleMasterEntity,
  Tax_PropertyWiseEntity,
  Tax_PropertyEntity,
  Tax_FY_RecordsEntity,
  Property_Usage_Details_Entity,
  Tax_Type_Master,
  Financial_year,
  Property_Owner_Details_Entity,
  Owner_type_master,
  Tax_PropertyEntity_Other_Taxes,
  Import_PropertyEntity,
  Import_PropertyStatsEntity,
  GIS_data_Entity,
  MilkatKarEntity,
  MilkatKarTaxEntity,
  Logs,
  Master_rr_rateEntity,
  WarshilKarEntity,
  WarshilKarTaxEntity,
  Master_rr_construction_rate,
  Master_tax_rateEntity,
  Master_weighting_rateEntity,
  Master_depreciation_rate,
  CommonFiledsOfPropertyEntity,
  Property_type_class_master,
  Wrong_property_typeMaster,Floor_Master,
  Master_ghanKachra_rateEntity,
  Property_Fod_Details_Entity,
  Property_Ferfar_Detail_Entity,
  TaxPendingDuesEntity,
  BillDataEntity,
  PaymentInfoEntity,
  ReceiptEntity,
  PaidDataEntity,
  PaidAndNonPaidDataEntity,
  BookNumberMasterEntity,
  DeletedPropertyUsageEntity,
  DeletedPropertyEntity,
  BackupPropertyUsageDetailsEntity,
  BackupPaymentDetailsEntity,
  PreviousOwnerEntity,
  DemandReportData,
  CollectorMaster,
  ReassessmentRange,
  PenaltyFeeYearWiseEntity,
  CronJobFailureEntity,
  OfflineNotificationEntity,
  RegisterNumberEntity
];

export * from './ward_master.entity';
export * from './locationMaster.entity';
export * from './zoneMaster.entity';
export * from './streetMaster.entity';
export * from './area-localityMaster.entity';
export * from './adminstrative-boundaryMaster.entity';
export * from './election-boundaryMaster.entity';
export * from './property-type-master.entity';
export * from './property-sub-type-master.entity';
export * from './usage-type-master.entity';
export * from './usage-sub-type-master.entity';
export * from './construction_class.entity';
export * from './property.entity';
export * from './roleMaster.entity';
export * from './formMaster.entity';
export * from './userMaster.entity';
export * from './user-otp.entity';
export * from './rolewise-form-permission.entity';
export * from './module-master.entity';
export * from './tax_propertywise.entity';
export * from './tax_property.entity';
export * from './tax_fy_records.entity';
export * from './property-usage-details.entity';
export * from './tax_type_master.entity';
export * from './financial_year.entity'
export * from './property-owner-details.entity';
export * from './owner_type_master.entity';
export * from  './tax_property_other_taxes.entity';
export * from './tax_type_master.entity';
export * from './Import_property.entity';
export * from './Import_property_stats.entity';
export * from './GIS_data.entity';
export * from './milkat-kar.entity';
export * from './milkatKarTax.entity';
export * from './logs.entity';
export * from './master_rr_rate.entity'
export * from './warshik_kar.entity';
export * from './warshik_karTax.entity';
export * from './master_rr_construction_rate.entity';
export * from './master_tax_rate.entity';
export * from './master_weighting_rate.entity';
export * from './master_depreciation_rate.entity'
export * from './commonFiledsOfProperty.entity'
export * from './property_type_classMaster.entity'
export * from './wrong_property_typeMaster.entity';

export * from  './floorMaster.entity';

export * from './master_ghanKachra_rate.entity';
export * from './property_fod_details.entity';

export * from './property_ferfar_details.entity';
export * from './tax_pendingDues.entity';
export * from './billData.entity';
export * from './payment_info.entity';
export * from './receipt.entity';
export *  from './paid_data.entity';
export * from './paid-and-non-paid-data.entity';

export * from './book_numberMaster.entity';

export * from './deleted_property_usage_details.entity';
export * from './deleted_property.entity';
export * from './backup_property_usage_details';
export * from './backup_payment_details.entity';
export * from './previous_property_owners.entity';
export * from './demandeReportData.entity';

export * from './collectorMaster.entity'
export * from './reassesment_range.entity';
export * from './penalty_fee_yearWise.entity';
export * from './cron-job-failure.entity';
export * from './offline-notification.entity';
export * from './register_number.entity';
