<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    /* Your existing CSS styles here */
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }
    body{
      margin-left: 25px !important;
      margin-right: 25px !important;
    }

    .header img {
      width: 6rem;
      height: 6rem;
    }

    .header .text-center {
      text-align: center;
    }

    .header h1 {
      font-size: 1.25rem;
      font-weight: bold;
    }

    .header p {
      font-size: 0.875rem;
    }

    .header .bold {
      font-weight: bold;
    }

    .divider {
      height: 2px;
      background-color: black;
      margin-bottom: 1rem;
    }

    .info-section {
      margin-bottom: 1rem;
    }

    .info-section .flex {
      display: flex;
      justify-content: space-between;
    }

    .info-section p {
      margin: 0;
    }

    .info-section .font-semibold {
      font-weight: 600;
    }

    .table-container {
      overflow-x: auto;
    }

    .table {
      min-width: 100%;
      border-collapse: collapse;
      border: 1px solid #71717a;
    }

    .table th,
    .table td {
      border: 1px solid #71717a;
      padding: 0.5rem;
    }

    thead,
    tbody {
      vertical-align: baseline;
    }

    .table th {
      text-align: left;
    }

    .footer {
      margin-bottom: 1rem;
    }

    .flex {
      display: flex;
    }

    .border-zinc-400 {
      border-color: #dae1e7;
    }

    .h-2 {
      height: 2px;
    }

    .max-w-4xl {
      max-width: 768px;
    }

    .sign-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: end;
      margin-top: 30px;
      text-align: center;
      padding-right: 30px;
      font-size: 15px;
    }

    .mx-auto {
      margin-left: auto;
      margin-right: auto;
    }

    .p-4 {
      padding: 16px;
    }

    .border-2 {
      border-width: 2px;
    }

    .border-black {
      border-color: black;
    }

    .flex {
      display: flex;
    }

    .justify-between {
      justify-content: space-between;
    }

    .items-center {
      align-items: center;
    }

    .flex-direction-column {
      flex-direction: column;
    }

    .mb-4 {
      margin-bottom: 16px;
    }

    .w-24 {
      width: 96px;
    }

    .h-24 {
      height: 96px;
    }

    .text-center {
      text-align: center;
    }

    .text-xl {
      font-size: 1.25rem;
    }

    .font-bold {
      font-weight: bold;
    }

    .text-sm {
      font-size: 0.875rem;
    }

    .text-xs {
      font-size: 0.675rem;
    }

    .h-2 {
      height: 8px;
    }

    .overflow-x-auto {
      overflow-x: auto;
    }

    .min-w-full {
      min-width: fit-content;
    }

    .border-collapse {
      border-collapse: collapse;
    }

    .border {
      border: 0.5px solid #525252;
    }

    .border-zinc-400 {
      border-color: #525252;
    }

    .px-4 {
      padding-left: 8px;
      padding-right: 8px;
    }

    .px-1 {
      padding-left: 5px;
      padding-right: 5px;
    }

    .py-2 {
      /* padding-top: 2px;
      padding-bottom: 10px; */
    }

    p {
      margin-top: 3px;
      margin-bottom: 3px;
    }

    .pb-0 {
      padding-bottom: 0;
    }

    .border-red {
      border: 1px solid red;
    }

    .gap15 {
      gap: 20px;
    }

    . {
      border-right: 1px solid black;
      margin-left: 1px;
      height: fit-content;
    }

    .th-subtitle {
      width: 20%;
      word-wrap: break-word;
      margin-right: 1px;
    }

    .th-subtitle1 {
      width: 4%;
      word-wrap: break-word;
    }

    .th-subtitle1-1 {
      width: 4%;
      word-wrap: break-word;
      margin-right: -1px;

    }

    .th-title-sm {
      width: 35px;
      min-width: 35px;
      max-width: 35px;
      word-wrap: break-word;
      margin-right: 1px;
    }

    .th-title-md {
      width: 99px;
      min-width: 99px;
      max-width: 99px;
      word-wrap: break-word;
      margin-right: 1px;
    }

    .th-title-lg {
      width: 12%;
      word-wrap: break-word;
      margin-right: 1px;
    }

    .th-title-xl {
      width: 270px;
      word-wrap: break-word;
      margin-right: 1px;
    }

    .margin-top-20 {
      margin-top: 20px;
    }

    .rotate-number {
      display: flex;
      gap: 10px;
      transform: rotate(90deg);
      word-wrap: break-word;
    }

    .border-left {
      border: 1px solid #525252;
    }

    .border-top {
      border-top: 1px solid #525252;
    }

    .grid-font {
      font-size: 12px;
    }

    div {
      margin-left: 0;
      margin-right: 0 !important;
    }

    .flex-align-justify-center {
      display: flex;
      align-items: center;
      height: 100%;
    }

    .grid-font-numbers {
      font-size: 12px;
    }

    .th-title-height {
      min-height: 120px;
    }

    .flex-align-justify-center {
      display: flex;
      align-items: center;
      height: 100%;
    }

    .th-subtitle1 {
      display: flex;
      align-items: center;
      justify-content: center;
      border-right: 1px solid #525252;
      height: 100%;
    }
    .th-subtitle1-1 {
      display: flex;
      align-items: center;
      justify-content: center;
      border-right: 1px solid #525252;
      height: 100%;
    }
    .th-subtitle1-sub1-1 {
      display: flex;
      align-items: center;
      justify-content: center;
      border-right: 1px solid #525252;
      height: 100%;
      margin: 2.5px !important;
    }

    .th-subtitle1:last-child {
      border-right: none;
    }

    .rotate-number {
      white-space: nowrap;
      font-size: 12px;
      text-align: center;
    }

    .my-1 {
      margin-top: 2px;
      margin-bottom: 2px;
    }

    .width-90 {
      width: 90px;
      min-width: 90px;
      max-width: 90px;
    }

    .width-80 {
      width: 80px;
      min-width: 80px;
      max-width: 80px;
    }

    .gap_20 {
      gap: 20px;
    }
    /* In your existing CSS */
    .min-w-full {
      border-collapse: collapse;
      border: 1px solid #525252; /* Apply border to the container only */
    }

    /* Remove left border from first column */
    .flex > div:first-child {
      border-left: none;
    }

    /* Remove individual borders and only keep what's needed */
    .border-left {
      border-left: 1px solid #525252;
      border-right: none;
      border-top: none;
      border-bottom: none;
    }

    /* For row divisions */
    .min-w-full + .min-w-full {
      border-top: none; /* Remove top border from subsequent table rows */
    }
    .right{
      margin-right: 10px;
    }

    /* NEW STYLES - Added to merge property types into a single row */
    .property-types {
      display: flex;
      flex-direction: column;
    }

    .property-type-item {
      /* padding-bottom: 3px; */
    border-bottom: 1px solid #525252;
    /* padding-top: 3px; */
    height: 100px;
    text-align: center;

    }
    .property-type-item-2 {
      /* padding-bottom: 3px; */
    border-bottom: 1px solid #525252;
    /* padding-top: 3px; */
    height: 100px;
    width: 29px;
    margin-left: -8px;

    }

    .property-type-item1 {
      /* padding-bottom: 6px; */
    border-bottom: 1px solid #525252;
    height: 100px;
    }

    /* Remove borders between multiple property rows */
    .property-row {
      border-top: none;
    }

    .property-row:first-child {
      border-top: 1px solid #525252;
    }

    /* Single tax display area for combined property types */
    .combined-tax-area {
      height: 100%;
    }

    /* Remove the individual borders for multiple property types */
    .hidden-border {
      border-top: none !important;
    }
    .p0{
      padding: 0 !important;
    }
    .th-title-md-sub {
      width: 110px;
      min-width: 115px;
      max-width: 110px;
      word-wrap: break-word;
      margin-right: 1px;
    }
    .th-title-sm-sub {
      width: 45px;
      min-width: 51px;
      max-width: 45px;
      word-wrap: break-word;
      margin-right: 1px;
    }
    .th-title-sm-sub-2 {
      width: 45px;
      min-width: 52px;
      max-width: 45px;
      word-wrap: break-word;
      margin-right: 1px;
    }
    .th-title-sm-sub-1 {
      width: 45px;
      min-width: 50px;
      max-width: 45px;
      word-wrap: break-word;
      margin-right: 1px;
    }
    .width-80-sub {
      width: 70px;
      min-width: 96px;
      max-width: 70px;
    }
    .th-subtitle1-sub {
      width: 7%;
      word-wrap: break-word;
      margin-right: -1px;
    }
    .th-subtitle1-sub1{
      display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #525252;
    height: 100%;
    margin: 3.5px !important;
    }
    .th-subtitle1-sub12{
      display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #525252;
    height: 100%;
    margin: 1.5px !important;
    }

    .th-subtitle1-sub-2{
      width: 7%;
      word-wrap: break-word;
      margin-right: -1px;
    }

    /* Add page break before each container */
    .container {
      page-break-before: always;
    }

    /* Hide header on the first page */

  </style>
</head>
<body>
  <%
    const properties = fileData; // Assuming fileData contains an array of properties
    properties.forEach((property, propertyIndex) => {
      const milkatKarTax = property.milkatKarTax;
      const propertyDetails = property.property;
      const chunkSize = 4;
      const totalPages = Math.ceil(milkatKarTax.length / chunkSize);
  %>

  <% for (let page = 0; page < totalPages; page++) { %>
    <div class="container">
      <div class="header flex justify-center items-center flex-direction-column">
        <h1 class="text-xl font-bold my-1">नगरपरिषद शिरोळ जि. कोल्हापूर</h1>
        <h2 class="text-xl font-bold my-1">नमुना ४३ नियम क्र ७४</h2>
        <p class="text-md font-bold">
        २०२५-२०२६ या वर्षाकरीता करपात्र इमारती व जमिनीची कर निर्धारण सूची
        </p>
      </div>

      <div class="mb-4 px-1 margin-top-20">
        <div class="flex justify-between font-bold">
          <div>
            <p><span class="font-semibold">नगरपरिषद</span>: नगरपरिषद शिरोळ</p>
          </div>
          <div>
            <p><span class="font-semibold">तालुका</span>: शिरोळ</p>
          </div>
          <div>
            <p>
              <span class="font-semibold">जिल्हा: कोल्हापूर</span>
            </p>
          </div>
          <div>
            <p><span class="font-semibold">वॉर्ड क्रमांक:</span>
              <%= propertyDetails.ward.ward_name %>
            </p>
          </div>
        </div>
      </div>

      <div class="min-w-full border-collapse border grid-font">
        <div class="flex justify-center">
          <div class="border-left px-4 py-2 th-title-sm">अ. क्र.</div>
          <div class="border-left px-4 py-2 th-title-sm">
            रस्त्याचे नाव/गल्लीचे नाव
          </div>
          <div class="border-left px-4 py-2 th-title-sm">
            गट क्र. / भूमापन क्र. / न भूमापन क्र.
          </div>
          <div class="border-left px-4 py-2 th-title-sm">जुना मालमत्ता क्रमांक</div>
          <div class="border-left px-4 py-2 th-title-sm">मालमत्ता क्रमांक</div>
          <div class="border-left px-4 py-2 th-title-md">
            मालकाचे ( धारण करणाऱ्याचे) नाव
          </div>
          <div class="border-left px-4 py-2 th-title-sm width-90">
            भोगवटा करणाऱ्याचे नाव
          </div>
          <div class="border-left px-4 py-2 th-title-md">मालमत्तेचे वर्णन</div>
          <div class="border-left px-4 py-2 th-title-sm">
            मिळकत बांधकामचे वर्ष
          </div>
          <div class="border-left px-4 py-2 th-title-md width-80">
            क्षेत्रफळ चौ मी / (चौ. फू)
          </div>
          <div class="flex flex-direction-column border-left px-4 th-title-md">
            <div>रेडीरेकनर दर प्रती चौ मी</div>
            <hr />
            <div class="flex-align-justify-center border-top gap15">
              <p class="th-subtitle1 px-4">
                <span class="rotate-number">जमीन</span>
              </p>
              <p class="th-subtitle1 px-4">
                <span class="rotate-number">इमारत कर</span>
              </p>
              <p class="th-subtitle1 px-4">
                <span class="rotate-number">बांधकाम</span>
              </p>
            </div>
          </div>
          <div class="border-left px-4 py-2 th-title-sm">घसारा दर</div>
          <div class="border-left px-4 py-2 th-title-sm">
            इ. वापरानुसार भारंक
          </div>
          <div class="border-left px-4 py-2 th-title-sm">भांडवली मूल्य</div>
          <div class="border-left px-4 py-2 th-title-sm">कराचा दर</div>
          <div class="flex flex-direction-column border-left px-4 th-title-xl th-title-height">
            <div class="text-center">वार्षिक कराची रक्कम (रुपयात)</div>
            <hr />
            <div class="flex-align-justify-center border-top gap_20">
              <p class="th-subtitle1 px-1">
                <span class="rotate-number">इमारतकर</span>
              </p>

              <% Object.entries(tax_types).forEach(([key, typeName]) => { %>
                <p class="th-subtitle1 pr-4">
                  <span class="rotate-number right">
                    <%= typeName %>
                  </span>
                </p>
              <% }); %>

              <p class="th-subtitle1">
                <span class="rotate-number">एकूण</span>
              </p>
            </div>
          </div>

          <div class="border-left px-4 py-2 th-title-md">
            नंतर वाढ किंवा घट झालेल्या बाबतीत आदेशाच्या संदर्भात शेरा
          </div>
        </div>
      </div>
      <div class="min-w-full border-collapse border grid-font">
        <div class="flex justify-center" style="height: 26px;">
          <div class="border-left px-4 py-2 th-title-sm">१</div>
          <div class="border-left px-4 py-2 th-title-sm">२</div>
          <div class="border-left px-4 py-2 th-title-sm">३</div>
          <div class="border-left px-4 py-2 th-title-sm">४</div>
          <div class="border-left px-4 py-2 th-title-sm">५</div>
          <div class="border-left px-4 py-2 th-title-md">६</div>
          <div class="border-left px-4 py-2 th-title-sm width-90">७</div>
          <div class="border-left px-4 py-2 th-title-md">
            <div class="flex flex-direction-column">
              <p class="px-4 py-2">८</p>
            </div>
          </div>
          <div class="border-left th-title-sm px-4 py-2">
            <div class="flex flex-direction-column">
              <p class="px-4 py-2">
                <span class="">९</span>
              </p>
            </div>
          </div>
          <div class="border-left px-4 py-2 th-title-md width-80">
            <div class="flex flex-direction-column">
              <p class="px-4 py-2">१०</p>
            </div>
          </div>
          <div class="flex flex-direction-column border-left px-4 th-title-md">
            <div class="flex-align-justify-center gap15">
              <p class="th-subtitle1 px-4">
                <span class="">११</span>
              </p>
              <p class="th-subtitle1 px-4">
                <span class="">१२</span>
              </p>
              <p class="th-subtitle1 px-4">
                <span class="">१३</span>
              </p>
            </div>
          </div>
          <div class="border-left px-4 py-2 th-title-sm">१४</div>
          <div class="border-left px-4 py-2 th-title-sm">१५</div>
          <div class="border-left px-4 py-2 th-title-sm">१६</div>
          <div class="border-left px-4 py-2 th-title-sm">१७</div>

          <div class="flex flex-direction-column border-left px-4 th-title-xl">
            <div class="flex-align-justify-center gap15">
              <p class="th-subtitle1 px-1">
                <span class="">१८</span>
              </p>
              <p class="th-subtitle1">
                <span class="right">१९</span>
              </p>
              <p class="th-subtitle1">
                <span class="right">२०</span>
              </p>
              <p class="th-subtitle1">
                <span class="right">२१</span>
              </p>
              <p class="th-subtitle1">
                <span class="right">२२</span>
              </p>
              <p class="th-subtitle1">
                <span class="right">२३</span>
              </p>
              <p class="th-subtitle1">
                <span class="right">२४ </span>
              </p>
              <p class="th-subtitle1">
                <span class="right">२५</span>
              </p>
              <p class="th-subtitle1">
                <span class="">२६</span>
              </p>
            </div>
          </div>

          <div class="border-left px-4 py-2 th-title-md">२७</div>
        </div>
      </div>

      <!-- This is the main change: Create a single row with multiple property types -->
      <div class="min-w-full border-collapse border grid-font">
        <% const chunk = milkatKarTax.slice(page * chunkSize, (page + 1) * chunkSize); %>
        <% chunk.forEach((row, idx) => { %>
          <div class="flex justify-center">
            <% if (page === 0 && idx === 0) { %>
              <div class="border-left px-4 py-2 th-title-sm"><%= (page * chunkSize) + idx + 1 %></div>
              <div class="border-left px-4 py-2 th-title-sm">
                <%= propertyDetails.street.street_name %>
              </div>
              <div class="border-left px-4 py-2 th-title-sm">
                <%= propertyDetails.gat_no != null && propertyDetails.gat_no !== "null" ? propertyDetails.gat_no : "" %>
              </div>
              <div class="border-left px-4 py-2 th-title-sm">
                <%= propertyDetails.old_propertyNumber %>
              </div>
              <div class="border-left px-4 py-2 th-title-sm">
                <%= propertyDetails.propertyNumber %>
              </div>
              <div class="border-left px-4 py-2 th-title-md">
                <%
                  // Get previous owners with स्वत: type and add time field
                  const previousOwners = property?.previousOwner 
                    ? property.previousOwner
                        .filter(prev => prev.owner_type.owner_type === "स्वत:")
                        .map(prev => ({
                          name: prev.name,
                          time: prev.recordCreatedTime ? new Date(prev.recordCreatedTime).getTime() : 0,
                          isPrevious: true
                        }))
                    : [];
                  
                  // Get current owners with स्वत: type and add time field
                  const currentOwners = propertyDetails.property_owner_details
                    .filter(owner => owner.owner_type.owner_type === "स्वत:")
                    .map(owner => ({
                      name: owner.name,
                      time: owner.createdAt ? new Date(owner.createdAt).getTime() : 0,
                      isPrevious: false
                    }));
                  
                  // Combine and sort all owners by time in ascending order
                  const allOwners = [...previousOwners, ...currentOwners]
                    .sort((a, b) => a.time - b.time);
                %>
                <% allOwners.forEach((owner, ownerIndex) => { %>
                  <% if (owner.isPrevious) { %>
                    [<%= owner.name %>]<% } else { %><%= owner.name %><% } %><% if (ownerIndex < allOwners.length - 1) { %>, <% } %>
                <% }); %>
              </div>
              <div class="border-left px-4 py-2 th-title-sm width-90">
                <%
                  // Get previous non-self owners and add time field
                  const previousNonSelfOwners = property?.previousOwner
                    ? property.previousOwner
                        .filter(prev => prev.owner_type.owner_type !== "स्वत:")
                        .map(prev => ({
                          name: prev.name,
                          time: prev.recordCreatedTime ? new Date(prev.recordCreatedTime).getTime() : 0,
                          isPrevious: true
                        }))
                    : [];
                  
                  // Get current non-self owners and add time field
                  const currentNonSelfOwners = propertyDetails.property_owner_details
                    .filter(owner => owner.owner_type.owner_type !== "स्वत:")
                    .map(owner => ({
                      name: owner.name,
                      time: owner.createdAt ? new Date(owner.createdAt).getTime() : 0,
                      isPrevious: false
                    }));
                  
                  // Combine and sort all non-self owners by time in ascending order
                  const allNonSelfOwners = [...previousNonSelfOwners, ...currentNonSelfOwners]
                    .sort((a, b) => a.time - b.time);
                %>
                <% if (allNonSelfOwners.length > 0) { %>
                  <% allNonSelfOwners.forEach((owner, ownerIndex) => { %>
                 
                    <% if (owner.isPrevious) { %>
                      [<%= owner.name %>]<% } else { %><%= owner.name %><% } %><% if (ownerIndex < allNonSelfOwners.length - 1) { %>, <% } %>
                  <% }); %>
                <% } else { %>
                  स्वत:
                <% } %>
              </div>
            <% } else { %>
              <div class="border-left px-4 py-2 th-title-sm"></div>
              <div class="border-left px-4 py-2 th-title-sm"></div>
              <div class="border-left px-4 py-2 th-title-sm"></div>
              <div class="border-left px-4 py-2 th-title-sm"></div>
              <div class="border-left px-4 py-2 th-title-sm"></div>
              <div class="border-left px-4 py-2 th-title-md"></div>
              <div class="border-left px-4 py-2 th-title-sm width-90"></div>
            <% } %>
            <div class="border-left  th-title-md-sub p0">
              <div class="property-types">
                <div class="property-type-item">
                  <%= row.property_usage_details.propertyType.propertyType %><br>
                  <%= row.property_usage_details.floorType ? row.property_usage_details.floorType.floor_name : "" %>
                  (लांबी<%= row.property_usage_details.length === "NaN" ? 0 : row.property_usage_details.length %> xरुंदी <%= row.property_usage_details.width === "NaN" ? 0 : row.property_usage_details.width %>)
                </div>
              </div>
            </div>
            <div class="border-left th-title-sm-sub p0">
              <div class="property-types">
                <div class="property-type-item">
                  <%= row.property_usage_details.construction_start_year %>
                </div>
              </div>
            </div>
            <div class="border-left th-title-md-sub p0 width-80-sub ">
              <div class="property-types ">
                <div class="property-type-item ">
                  <%= row.property_usage_details.are_sq_meter %> चौ.मी (<%= row.property_usage_details.are_sq_ft %> चौ. फू)
                </div>
              </div>
            </div>
            <div class="flex flex-direction-column border-left  th-title-md-sub ">
              <div class="property-types">
                <div class="property-type-item1 flex-align-justify-center gap15 ">
                  <p class="th-subtitle1-sub1 ">
                    <span class="rotate-number "><%= row.rr_rate %></span>
                  </p>
                  <p class="th-subtitle1-sub12 ">
                    <span class="rotate-number">&nbsp; &nbsp;&nbsp;&nbsp;</span>
                  </p>
                  <p class="th-subtitle1 px-4 ">
                    <span style="margin-top: 15px;" class="rotate-number"><%= row.rr_construction_rate %></span>
                  </p>
                </div>
              </div>
            </div>
            <div class="border-left  th-title-sm-sub-1">
              <div class="property-types">
                <div class="property-type-item">
                  <%= row.depreciation_rate %>
                </div>
              </div>
            </div>
            <div class="border-left  th-title-sm-sub">
              <div class="property-types">
                <div class="property-type-item">
                  <%= row.weighting %>
                </div>
              </div>
            </div>
            <div class="border-left  p0 th-title-sm-sub-2">
              <div class="property-types">
                <div class="property-type-item">
                  <span class="rotate-number" style="margin-top: 30px;">
                    <%= row.capital_value %>
                  </span>
                </div>
              </div>
            </div>
            <div class="border-left  th-title-sm-sub">
              <div class="property-types">
                <div class="property-type-item" >
                  <span class="rotate-number" style="margin-top: 30px;">
                    <%= row.tax_value %>
                  </span>
                </div>
              </div>
            </div>
            <div class="flex flex-direction-column border-left px-4 th-title-xl">
              <div class="flex-align-justify-center gap15">
                <div class="th-subtitle1 property-types px-1">
                  <div class="property-type-item-2">
                    <span class="rotate-number" style="margin-top: 20px;">
                      <%= row.tax %>
                    </span>
                  </div>
                </div>

<% if (page === 0 && idx === 0) { %>
  <% Object.entries(tax_types).forEach(([key, typeName]) => { %>
    <p class="th-subtitle1-1">
      <span class="rotate-number right">
        <% if (key === 'tax_type_5') { %>
          <%= row.shasti_fee %>
        <% } else { %>
          <%= property[key] == null || property[key] == "null" || property[key] == "undefined" ? 0 : property[key] %>
        <% } %>
      </span>
    </p>
  <% }); %>
  <p class="th-subtitle1">
    <span class="rotate-number">
      <%= property.total_tax %>
    </span>
  </p>
<% } else { %>
  <% Object.entries(tax_types).forEach(([key, typeName]) => { %>
    <p class="th-subtitle1-1">
      <span class="rotate-number right">
        <% if (key === 'tax_type_5') { %>
          <%= row.shasti_fee %>
        <% } else { %>
          <!-- Handle other keys if necessary -->
        <% } %>
      </span>
    </p>
  <% }); %>
  <p class="th-subtitle1">
    <span class="rotate-number"></span>
  </p>
<% } %>

              </div>
            </div>
       <% if (page === 0 && idx === 0) { %>
  <div class="border-left px-4 py-2 th-title-md">
    <% if (propertyDetails.property_remark && propertyDetails.property_remark !== "null") { %>
      <div><%= propertyDetails.property_remark %></div>
    <% } %>
    <% if (propertyDetails.ferfarRemark && propertyDetails.ferfarRemark !== "null") { %>
      <div><%= propertyDetails.ferfarRemark %></div>
    <% } %>
  </div>
<% } else { %>
  <div class="border-left px-4 py-2 th-title-md"></div>
<% } %>


          </div>
        <% }); %>
      </div>
    </div>
    <% } %>
    <% }); %>
    <div class="margin-top-20 grid-font">
      <p>
        सदरचा उतारा हा मालकी हक्काचा नसून करआकारणीचा आहे. (त्यावरून) सदरच्या उताऱ्यावरून खरेदी विक्रीचा व्यवहार झालेस त्यास नगरपरिषद जबाबदार राहणार नाही.
      </p>
      <p class="margin-top-20">येणे प्रमाणे लागू पुरता उतारा असे दिनांक -<%= currentDate %>
      </p>
    </div>
</body>
</html>