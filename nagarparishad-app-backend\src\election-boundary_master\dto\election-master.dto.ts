import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateElectionBoundaryMasterDto {
  @ApiProperty({ name: 'electionBoundaryName', type: String })
  @IsNotEmpty()
  @IsString()
  electionBoundaryName: string;
}

export class UpdateElectionBoundaryMasterDto extends PartialType(
  CreateElectionBoundaryMasterDto,
) {}

export class ElectionBoundaryMasterDto {
  @ApiProperty({ name: 'electionBoundary_id', type: String })
  @IsNotEmpty()
  @IsUUID()
  electionBoundary_id: string;
}
