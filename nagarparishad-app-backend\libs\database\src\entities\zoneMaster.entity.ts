import {
  BaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Ward_Master } from './ward_master.entity';

@Entity('zone_master')
export class ZoneMaster extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  zone_id: string;

  @Column({ type: String, name: 'zone_name', nullable: false })
  zoneName: string;

  @ManyToOne(() => Ward_Master, (ward) => ward.ward_id)
  @JoinColumn({ name: 'ward_id' })
  ward: Ward_Master; //wardId to ward

  /*
   * Create and Update Date Columns
   */

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
