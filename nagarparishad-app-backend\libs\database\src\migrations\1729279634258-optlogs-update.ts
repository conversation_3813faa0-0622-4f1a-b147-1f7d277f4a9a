import { MigrationInterface, QueryRunner } from "typeorm";

export class OptlogsUpdate1729279634258 implements MigrationInterface {
    name = 'OptlogsUpdate1729279634258'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_otp" ADD "status" integer`);
        await queryRunner.query(`ALTER TABLE "user_otp" ADD "mobile_number" character varying`);
        await queryRunner.query(`ALTER TABLE "user_otp" ADD "key_value" jsonb`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_otp" DROP COLUMN "key_value"`);
        await queryRunner.query(`ALTER TABLE "user_otp" DROP COLUMN "mobile_number"`);
        await queryRunner.query(`ALTER TABLE "user_otp" DROP COLUMN "status"`);
    }

}
