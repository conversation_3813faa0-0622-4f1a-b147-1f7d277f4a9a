import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeinRepo1751018710055 implements MigrationInterface {
    name = 'ChangeinRepo1751018710055'

    public async up(queryRunner: QueryRunner): Promise<void> {
     
        await queryRunner.query(`ALTER TABLE "offline_notifications" ADD "is_read" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
    
        await queryRunner.query(`ALTER TABLE "offline_notifications" DROP COLUMN "is_read"`);
           }

}
