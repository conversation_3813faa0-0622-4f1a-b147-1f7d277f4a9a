import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdatedAmountColumnToNumeric1741864096397 implements MigrationInterface {
    name = 'UpdatedAmountColumnToNumeric1741864096397'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0"`);
        // await queryRunner.query(`CREATE TABLE "deleted_property_usage_details" ("deleted_property_usage_details_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "property_id" uuid NOT NULL, "property_usage_details_id" uuid NOT NULL, CONSTRAINT "REL_3704e9416b2d21bee9eeed76e8" UNIQUE ("property_usage_details_id"), CONSTRAINT "PK_676fb1fb09519f2a427f80ec915" PRIMARY KEY ("deleted_property_usage_details_id"))`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c" UNIQUE ("billNo")`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP DEFAULT`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(50) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "backup_payment_details" DROP COLUMN "amount"`);
        await queryRunner.query(`ALTER TABLE "backup_payment_details" ADD "amount" numeric NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "FK_df738d1108e8291026728e28c55" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "deleted_property_usage_details" ADD CONSTRAINT "FK_537835d3810d522523a6f9a3e5e" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "deleted_property_usage_details" ADD CONSTRAINT "FK_3704e9416b2d21bee9eeed76e8d" FOREIGN KEY ("property_usage_details_id") REFERENCES "property_usage_details"("property_usage_details_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "deleted_property_usage_details" DROP CONSTRAINT "FK_3704e9416b2d21bee9eeed76e8d"`);
        // await queryRunner.query(`ALTER TABLE "deleted_property_usage_details" DROP CONSTRAINT "FK_537835d3810d522523a6f9a3e5e"`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "FK_df738d1108e8291026728e28c55"`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0"`);
        await queryRunner.query(`ALTER TABLE "backup_payment_details" DROP COLUMN "amount"`);
        await queryRunner.query(`ALTER TABLE "backup_payment_details" ADD "amount" integer NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(100) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET DEFAULT now()`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c"`);
        // await queryRunner.query(`DROP TABLE "deleted_property_usage_details"`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
