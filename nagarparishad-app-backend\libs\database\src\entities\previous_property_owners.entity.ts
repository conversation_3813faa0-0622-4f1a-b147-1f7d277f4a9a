import {
    Base<PERSON>ntity,
    <PERSON>umn,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    PrimaryGeneratedColumn,
    ManyToOne,
    JoinColumn,
  } from 'typeorm';
  import { PropertyEntity } from './property.entity';
  import { Owner_type_master } from './owner_type_master.entity';
  import { ReassessmentRange } from './reassesment_range.entity';

  @Entity('previous_property_owners')
  export class PreviousOwnerEntity extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    previous_owner_id: string;

    @ManyToOne(() => PropertyEntity, (property) => property.property_id, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'property_id' })
    property: PropertyEntity;

    @ManyToOne(() => Owner_type_master, (owner_type) => owner_type.owner_type_id, { onDelete: 'SET NULL' })
    @JoinColumn({ name: 'owner_type_id' })
    owner_type: Owner_type_master;

    @ManyToOne(() => ReassessmentRange, (reassessmentRange) => reassessmentRange.reassessment_range_id, { onDelete: 'SET NULL' })
    @JoinColumn({ name: 'reassessment_range_id' })
    reassessmentRange: ReassessmentRange;

    @Column({ type: 'varchar', nullable: true })
    name: string;

    @Column({ type: 'varchar', nullable: true })
    mobile_number: string;

    @Column({ type: 'varchar', nullable: true })
    email_id: string;

    @Column({ type: 'varchar', nullable: true })
    aadhar_number: string;

    @Column({ type: 'varchar', nullable: true })
    pan_card: string;

  @Column({ type: 'timestamp', name: 'record_created_time', nullable: true })
  public recordCreatedTime: Date;

    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;

    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
  }
