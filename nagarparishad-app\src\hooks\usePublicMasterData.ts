import { useState, useEffect } from 'react';
import PublicApi from '@/services/PublicApiServcies';

interface Zone {
  zone_id: string;
  zoneName: string;
  ward?: {
    ward_id: string;
  };
}

interface Ward {
  ward_id: string;
  ward_name: string;
}

export const usePublicZoneData = () => {
  const [zoneList, setZoneList] = useState<Zone[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchZones = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await PublicApi.getAllZonesPublic();
        if (response.status) {
          setZoneList(response.data.data || []);
        } else {
          setError('Failed to fetch zones');
          setZoneList([]);
        }
      } catch (err) {
        console.error('Error fetching public zones:', err);
        setError('Error fetching zones');
        setZoneList([]);
      } finally {
        setLoading(false);
      }
    };

    fetchZones();
  }, []);

  return { zoneList, loading, error };
};

export const usePublicWardData = () => {
  const [wardList, setWardList] = useState<Ward[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchWards = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await PublicApi.getAllWardsPublic();
        if (response.status) {
          setWardList(response.data.data || []);
        } else {
          setError('Failed to fetch wards');
          setWardList([]);
        }
      } catch (err) {
        console.error('Error fetching public wards:', err);
        setError('Error fetching wards');
        setWardList([]);
      } finally {
        setLoading(false);
      }
    };

    fetchWards();
  }, []);

  return { wardList, loading, error };
};

// Combined hook for both zone and ward data
export const usePublicMasterData = () => {
  const { zoneList, loading: zoneLoading, error: zoneError } = usePublicZoneData();
  const { wardList, loading: wardLoading, error: wardError } = usePublicWardData();

  return {
    zoneList,
    wardList,
    loading: zoneLoading || wardLoading,
    error: zoneError || wardError,
  };
};
