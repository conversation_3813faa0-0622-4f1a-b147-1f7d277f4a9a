import TaxListApi from "../../services/TaxServices";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// Fetch bills list
const fetchBillsList = async (searchParams: URLSearchParams, financialYear: string) => {
  const response = await TaxListApi.getBillsList(searchParams, financialYear);
  if (response.status) {
    return response.data;
  } else {
    throw new Error(response.data);
  }
};

// Generate bill mutation
const generateBill = async (financialYear: string) => {
  const response = await TaxListApi.generateBill(financialYear);
  if (response.status) {
    return response.data;
  } else {
    throw new Error(response.data);
  }
};

// Fetch Varshik Kar Akarni
const fetchVarshikKarAkarni = async (
  propertyNumber: string,
  searchOn: string,
  financialYear: string
) => {
  const response = await TaxListApi.getVarshikKar<PERSON>karni(propertyNumber, searchOn, financialYear);
  if (response.status) {
    return response.data;
  } else {
    throw new Error(response.data);
  }
};

// Process Warshik Kar Akarni
const processWarshikKarAkarni = async (financialYear: string) => {
  const response = await TaxListApi.processWarshikKarAkarni(financialYear);
  if (response.status) {
    return response.data;
  } else {
    throw new Error(response.data);
  }
};

export const useTaxCalculateController = () => {
  const queryClient = useQueryClient();

  // Generate bill mutation
  const generateBillMutation = useMutation({
    mutationFn: generateBill,
    onSuccess: () => {
      // Invalidate and refetch year data after successful bill generation
      queryClient.invalidateQueries({ queryKey: ["yearData"] });
    },
  });

  // Function to fetch bills list (used manually, not as a query)
  const getBillsList = async (searchParams: URLSearchParams, financialYear: string) => {
    return await fetchBillsList(searchParams, financialYear);
  };

  // Function to fetch Varshik Kar Akarni (used manually, not as a query)
  const getVarshikKarAkarni = async (
    propertyNumber: string,
    searchOn: string,
    financialYear: string
  ) => {
    return await fetchVarshikKarAkarni(propertyNumber, searchOn, financialYear);
  };

  return {
    generateBill: generateBillMutation.mutateAsync,
    isGeneratingBill: generateBillMutation.isPending,
    generateBillError: generateBillMutation.error,
    getBillsList,
    getVarshikKarAkarni,
  };
};
