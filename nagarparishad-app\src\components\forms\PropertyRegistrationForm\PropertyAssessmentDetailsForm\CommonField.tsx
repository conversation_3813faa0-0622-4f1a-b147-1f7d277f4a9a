import React, { useContext, useEffect, useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Form,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Input } from "@/components/ui/input";
import Collapse from "@/components/globalcomponent/collapse";
import { useTranslation } from "react-i18next";
import { PropertyContext } from "@/context/PropertyContext";

// Define the Zod schema
const schema = z.object({
  GISID: z.string().min(1, "Enter GISID").optional(),
  propertyDescription: z.string().min(1, "मिळकतीचे वर्णन प्रविष्ट करा").optional(),
  completionCertificate: z.string().min(1, "पूर्णत्वाचा दाखला प्रविष्ट करा").optional(),
  accessRoad: z.string().min(1, "पोचा मार्ग प्रविष्ट करा").optional(),
  individualToilet: z.enum(["yes", "no"], "एक पर्याय निवडा").optional(),
  toiletType: z.string().min(1, "शौचालय प्रकार प्रविष्ट करा").optional(),
  totalNumber: z.string().min(1, "एकूण संख्या प्रविष्ट करा").optional(),
  lightingFacility: z.string().min(1, "दिवाबत्ती सोय प्रविष्ट करा").optional(),
  tapConnection: z.string().min(1, "नळ कनेक्शन प्रविष्ट करा").optional(),
  totalConnections: z.string().min(1, "एकूण नळ कनेक्शन संख्या प्रविष्ट करा").optional(),
  solarProject: z.enum(["yes", "no"], "एक पर्याय निवडा").optional(),
  rainWaterHarvesting: z.enum(["yes", "no"], "एक पर्याय निवडा").optional(),
  sewageSystem: z.enum(["yes", "no"], "एक पर्याय निवडा").optional(),
  groundFloorArea: z.string().min(1, "तळ मजल्याचे बांधकाम क्षेत्रफळ प्रविष्ट करा").optional(),
  remainingGroundFloorArea: z.string().min(1, "तळ मजला उर्वरित मोकळी जागा प्रविष्ट करा").optional(),
});


interface SecondaryFormProps {
  triggerValidation: () => Promise<boolean>;
}

const CommonField: React.FC<any> = ({triggerValidation} ) => {
  const { t } = useTranslation();
  const { propertyInformation, setPropertyInformation } =
    useContext(PropertyContext);
  const [selectedUsageDetail, setselectedUsageDetail] = useState<any>(null);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      GISID: selectedUsageDetail?.GISID || "", // Should be a string
      propertyDescription: selectedUsageDetail?.propertyDescription || "", // Should be a string
      completionCertificate: selectedUsageDetail?.completionCertificate || "", // Should be a string or null
      accessRoad: selectedUsageDetail?.accessRoad || "", // Should be a string
      individualToilet: selectedUsageDetail?.individualToilet || "no", // Should be a string
      toiletType: selectedUsageDetail?.toiletType || "", // Should be a string
      totalNumber: selectedUsageDetail?.totalNumber || "", // Should be a number
      lightingFacility: selectedUsageDetail?.lightingFacility || "", // Should be a string
      tapConnection: selectedUsageDetail?.tapConnection || "", // Should be a string or null
      totalConnections: selectedUsageDetail?.totalConnections || "", // Should be a number
      solarProject: selectedUsageDetail?.solarProject || "no", // Should be a string
      rainWaterHarvesting: selectedUsageDetail?.rainWaterHarvesting || "no", // Should be a string
      sewageSystem: selectedUsageDetail?.sewageSystem || "no", // Should be a string
      groundFloorArea: selectedUsageDetail?.groundFloorArea || "", // Should be a number
      remainingGroundFloorArea:
        selectedUsageDetail?.remainingGroundFloorArea ,
    },
  });

  useEffect(() => {
    const initialData = propertyInformation?.PlotDetailsForm;
    setselectedUsageDetail(initialData);
    form.reset({
      GISID: initialData?.GISID || "",
      propertyDescription: initialData?.propertyDescription || "",
      completionCertificate: initialData?.completionCertificate || "",
      accessRoad: initialData?.accessRoad || "",
      individualToilet: initialData?.individualToilet || "no",
      toiletType: initialData?.toiletType || "",
      totalNumber: initialData?.totalNumber || "",
      lightingFacility: initialData?.lightingFacility || "",
      tapConnection: initialData?.tapConnection || "",
      totalConnections: initialData?.totalConnections || "",
      solarProject: initialData?.solarProject || "no",
      rainWaterHarvesting: initialData?.rainWaterHarvesting || "no",
      sewageSystem: initialData?.sewageSystem || "no",
      groundFloorArea: initialData?.groundFloorArea || "",
      remainingGroundFloorArea: initialData?.remainingGroundFloorArea || "",
    });
  }, [propertyInformation]);
  const {
    control,
    formState: { errors },
  } = form;


  const onSubmit = (data: z.infer<typeof schema>) => {
    console.log(data);
  };

  
  const triggerValidationWithoutErrors = async () => {
    const isValid =true;
    if (isValid) {
      // Proceed without displaying errors
      console.log("Form is valid but errors won't be shown.");
    } else {
      console.log("Form is invalid but errors won't be displayed.");
    }
    return isValid;
  };

  triggerValidation(() => triggerValidationWithoutErrors());
  


  const handleFieldChange = (fieldName: string, value: any) => {
    const updatedData = {
      ...selectedUsageDetail,
      [fieldName]: value,
    };
    console.log("updated dataa ",updatedData);

    setselectedUsageDetail(updatedData);
    setPropertyInformation((prevInfo: any) => ({
      ...prevInfo,
      PlotDetailsForm: updatedData,
    }));
  };
  return (
    <Form {...form}>
   
        <div className="grid grid-cols-1  md:grid-cols-3 gap-2 gap-x-5">
          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="GISID"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>GISID</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      className="mt-1"
                      placeholder="GISID प्रविष्ट करा"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange("GISID", e.target.value);
                      }}
                    />
                  </FormControl>
                  {errors.GISID && (
                    <FormMessage className="ml-1">Enter GISID</FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="propertyDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>मिळकतीचे वर्णन - (G.F)</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      className="mt-1"
                      placeholder="मिळकतीचे वर्णन प्रविष्ट करा"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange(
                          "propertyDescription",
                          e.target.value
                        );
                      }}
                    />
                  </FormControl>
                  {errors.propertyDescription && (
                    <FormMessage className="ml-1">
                      मिळकतीचे वर्णन प्रविष्ट करा
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="completionCertificate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t("propertyAssessment.constructionCompletionCertificate")}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      className="mt-1"
                      placeholder="पूर्णत्वाचा दाखला प्रविष्ट करा"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange(
                          "completionCertificate",
                          e.target.value
                        );
                      }}
                    />
                  </FormControl>
                  {errors.completionCertificate && (
                    <FormMessage className="ml-1">
                      पूर्णत्वाचा दाखला प्रविष्ट करा
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="accessRoad"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>पोचा मार्ग</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      className="mt-1"
                      placeholder="पोचा मार्ग प्रविष्ट करा"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange("accessRoad", e.target.value);
                      }}
                    />
                  </FormControl>
                  {errors.accessRoad && (
                    <FormMessage className="ml-1">
                      पोचा मार्ग प्रविष्ट करा
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="toiletType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>शौचालय प्रकार</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      className="mt-1"
                      placeholder="शौचालय प्रकार प्रविष्ट करा"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange("toiletType", e.target.value);
                      }}
                    />
                  </FormControl>
                  {errors.toiletType && (
                    <FormMessage className="ml-1">
                      शौचालय प्रकार प्रविष्ट करा
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="totalNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>एकूण संख्या</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      className="mt-1"
                      placeholder="एकूण संख्या प्रविष्ट करा"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange("totalNumber", e.target.value);
                      }}
                    />
                  </FormControl>
                  {errors.totalNumber && (
                    <FormMessage className="ml-1">
                      एकूण संख्या प्रविष्ट करा
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="lightingFacility"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>दिवाबत्ती सोय</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      className="mt-1"
                      placeholder="दिवाबत्ती सोय प्रविष्ट करा"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange("lightingFacility", e.target.value);
                      }}
                    />
                  </FormControl>
                  {errors.lightingFacility && (
                    <FormMessage className="ml-1">
                      दिवाबत्ती सोय प्रविष्ट करा
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="tapConnection"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>नळ कनेक्शन</FormLabel>
                  <FormControl>
                    <Input
                      className="mt-1"
                      placeholder="नळ कनेक्शन प्रविष्ट करा"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange("tapConnection", e.target.value);
                      }}
                    />
                  </FormControl>
                  {errors.tapConnection && (
                    <FormMessage className="ml-1">
                      नळ कनेक्शन प्रविष्ट करा
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="totalConnections"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>एकूण संख्या</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      className="mt-1"
                      placeholder="एकूण नळ कनेक्शन संख्या प्रविष्ट करा"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange("totalConnections", e.target.value);
                      }}
                    />
                  </FormControl>
                  {errors.totalConnections && (
                    <FormMessage className="ml-1">
                      एकूण नळ कनेक्शन संख्या प्रविष्ट करा
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="groundFloorArea"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>तळ मजल्याचे बांधकाम क्षेत्रफळ</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      className="mt-1"
                      placeholder="तळ मजल्याचे बांधकाम क्षेत्रफळ प्रविष्ट करा"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange("groundFloorArea", e.target.value);
                      }}
                    />
                  </FormControl>
                  {errors.groundFloorArea && (
                    <FormMessage className="ml-1">
                      तळ मजल्याचे बांधकाम क्षेत्रफळ प्रविष्ट करा
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="remainingGroundFloorArea"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>तळ मजल्याचे उर्वरित मोकळी जागा</FormLabel>
                  <FormControl>
                    <Input
                      type="number"

                      className="mt-1"
                      placeholder="तळ मजल्याचे उर्वरित मोकळी जागा प्रविष्ट करा"
                      
                      {...field}
                      value={field.value}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange("remainingGroundFloorArea", e.target.value);
                      }}
                    />
                  </FormControl>
                  {errors.remainingGroundFloorArea && (
                    <FormMessage className="ml-1">
                      तळ मजल्याचे उर्वरित मोकळी जागा प्रविष्ट करा
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="solarProject"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>छतावरील सौर ऊर्जा प्रकल्प</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleFieldChange("solarProject", value); // Add this line
                      }}
                      value={field.value}
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">हो </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">नाही </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  {errors.solarProject && (
                    <FormMessage className="ml-1">एक पर्याय निवडा</FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="rainWaterHarvesting"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>पावसाच्या पाण्याची साठवण प्रकल्प</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleFieldChange("rainWaterHarvesting", value); // Add this line
                      }}
                      value={field.value}
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">हो </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">नाही </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  {errors.rainWaterHarvesting && (
                    <FormMessage className="ml-1">एक पर्याय निवडा</FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="sewageSystem"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>गटाराची व्यवस्था आहे का?</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleFieldChange("sewageSystem", value); // Add this line
                      }}
                      value={field.value}
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">हो </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">नाही </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  {errors.sewageSystem && (
                    <FormMessage className="ml-1">एक पर्याय निवडा</FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="individualToilet"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("propertyAssessment.privateToilet")}</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleFieldChange("individualToilet", value); // Add this line
                      }}
                      value={field.value}
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">हो </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">नाही </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  {errors.individualToilet && (
                    <FormMessage className="ml-1">एक पर्याय निवडा</FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
        </div>
   
    </Form>
  );  
};

export default CommonField;