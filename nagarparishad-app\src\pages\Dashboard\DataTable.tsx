
import React from 'react';
import { cn } from '@/lib/utils';

interface Column {
  header: string;
  accessor: string;
  cell?: (value: any, item: any) => React.ReactNode;
}

interface DataTableProps {
  columns: Column[];
  data: any[];
  className?: string;
  emptyMessage?: string;
}

const DataTable = ({ columns, data, className, emptyMessage = "No data available" }: DataTableProps) => {
  if (!data.length) {
    return (
      <div className="w-full p-8 text-center text-gray-500">
        {emptyMessage}
      </div>
    );
  }

  return (
    <div className={cn("table-container animate-fade-up", className)}>
      <table className="data-table">
        <thead>
          <tr>
            {columns.map((column, i) => (
              <th key={i} className="bg-gray-50">
                {column.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {data.map((item, rowIndex) => (
            <tr key={rowIndex}>
              {columns.map((column, colIndex) => (
                <td key={colIndex}>
                  {column.cell 
                    ? column.cell(item[column.accessor], item)
                    : item[column.accessor]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DataTable;