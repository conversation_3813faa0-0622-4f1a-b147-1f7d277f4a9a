import {
  BaseEntity,
  Column,
  CreateDateC<PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { RoleMasterEntity } from './roleMaster.entity';
import { CollectorMaster } from './collectorMaster.entity';

@Entity('user_master')
export class UserMasterEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  user_id: string;

  @Column({ name: 'first_name', type: 'varchar', nullable: false })
  firstname: string;

  @Column({ name: 'last_name', type: 'varchar', nullable: false })
  lastname: string;

  @Column({ name: 'email', type: 'varchar', nullable: false })
  email: string;

  @Column({ name: 'password', type: 'varchar', nullable: false })
  password: string;

  @Column({ name: 'is_Active', type: 'boolean', default: false })
  isActive: boolean;

  @Column({ name: 'address', type: 'varchar', nullable: false })
  address: string;

  @ManyToOne(() => RoleMasterEntity, (role) => role.role_id)
  @JoinColumn({ name: 'role_id' })
  role: RoleMasterEntity;

  @Column({ name: 'mobile_number', type: 'varchar', nullable: false })
  mobileNumber: string;

  @Column({ name: 'profile_pic', type: 'varchar', nullable: true })
  profilePic: string;

  @Column({
    type: 'text',
    name: 'refresh_token',
    nullable: true,
  })
  refreshToken: string;

  @Column({
    type: 'text',
    name: 'reset_token',
    nullable: true,
  })
  resetToken: string;

  @OneToOne(() => CollectorMaster, (collector) => collector.user)
  collector: CollectorMaster;

  @Column({
    name: 'last_login_at',
    type: 'timestamp',
    nullable: true,
    default: null,
  })
  public lastLoginAt: Date | null;

  /*
   * Create and Update Date Columns
   */

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
