import { <PERSON><PERSON>han, Repository } from 'typeorm';
import { UserMasterEntity, UserOtpEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from '@nestjs/common';

export class UserOtpRepository extends Repository<UserOtpEntity> {
  constructor(
    @InjectRepository(UserOtpEntity)
    private readonly userOtpRepository: Repository<UserOtpEntity>,
  ) {
    super(
      userOtpRepository.target,
      userOtpRepository.manager,
      userOtpRepository.queryRunner,
    );
  }

  async saveData(input: any) {
    try {
      let data = this.userOtpRepository.create(input);
      data = await this.userOtpRepository.save(data);
      return data;
    } catch (error) {
      throw error;
    }
  }

  async findById(user: string, otp: string) {
    return await this.userOtpRepository
      .createQueryBuilder('user_otp')
      .select(['user_otp.user', 'user_otp.otp', 'user_otp.expiresAt'])
      .where('user_otp.user = :user', { user })
      .andWhere('user_otp.otp = :otp', { otp })
      .andWhere('user_otp.expiresAt > NOW()')
      .getOne();
  }

  async deleteExiperedOtp() {
    // return data;
    return await this.createQueryBuilder('user_otp')
      .delete()
      .where('expiresAt < NOW()')
      .execute();
  }


  async getOtpForVerificationId(verificationId: string, otp: string){
    const currentTime = new Date();
    return await this.userOtpRepository.createQueryBuilder('user_otp').select([
      'user_otp.otpId','user_otp.user', 'user_otp.otp', 'user_otp.expiresAt', 'user_otp.mobile_number', 'user_otp.additionalInfo'
    ]).where('user_otp.verificationId = :verificationId', {verificationId})
    .andWhere('user_otp.otp = :otp', { otp })
    .andWhere('user_otp.expiresAt > :currentTime', { currentTime })
    .getOne();
  }
}
