import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateFerFarAndFod1744698557890 implements MigrationInterface {
  name = 'UpdateFerFarAndFod1744698557890';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" DROP CONSTRAINT "FK_e217be49a6c5cdce8778f078112"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" DROP CONSTRAINT "FK_4b9cc36be3bc02368cb4b4d4096"`,
    );
    await queryRunner.query(
      `CREATE TABLE "deleted_property_usage_details" ("deleted_property_usage_details_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "property_id" uuid NOT NULL, "property_usage_details_id" uuid NOT NULL, CONSTRAINT "REL_3704e9416b2d21bee9eeed76e8" UNIQUE ("property_usage_details_id"), CONSTRAINT "PK_676fb1fb09519f2a427f80ec915" PRIMARY KEY ("deleted_property_usage_details_id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" DROP COLUMN "image_path"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" DROP COLUMN "image_path"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD "fodRemark" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" ADD "parent_propertyId" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" ADD "photo_image_paths" character varying array`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" ADD "document_image_path" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" ADD "user_email_id" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" ADD "photo_image_paths" character varying array`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" ADD "document_image_path" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" ADD "user_email_id" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" ADD CONSTRAINT "FK_e217be49a6c5cdce8778f078112" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" ADD CONSTRAINT "FK_4b9cc36be3bc02368cb4b4d4096" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "deleted_property_usage_details" DROP CONSTRAINT "FK_3704e9416b2d21bee9eeed76e8d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "deleted_property_usage_details" DROP CONSTRAINT "FK_537835d3810d522523a6f9a3e5e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" DROP CONSTRAINT "FK_4b9cc36be3bc02368cb4b4d4096"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" DROP CONSTRAINT "FK_e217be49a6c5cdce8778f078112"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" DROP COLUMN "user_email_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" DROP COLUMN "document_image_path"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" DROP COLUMN "photo_image_paths"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" DROP COLUMN "user_email_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" DROP COLUMN "document_image_path"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" DROP COLUMN "photo_image_paths"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property" DROP COLUMN "parent_propertyId"`,
    );
    await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "fodRemark"`);
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" ADD "image_path" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" ADD "image_path" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" ADD CONSTRAINT "FK_4b9cc36be3bc02368cb4b4d4096" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" ADD CONSTRAINT "FK_e217be49a6c5cdce8778f078112" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
