import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  BaseEntity,
  OneToOne,
  JoinColumn,
  OneToMany,
  ManyToOne,
  DeleteDateColumn,
} from 'typeorm';
import {
  UserMasterEntity,
  Ward_Master,
  BookNumberMasterEntity,
  RoleMasterEntity,
} from './';

@Entity('collector_master')
export class CollectorMaster extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  collectorId: string;

  @OneToOne(() => UserMasterEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: UserMasterEntity;

  @OneToOne(() => Ward_Master, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'ward_id' })
  ward: Ward_Master;

  @ManyToOne(() => RoleMasterEntity, { cascade: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'role_id' })
  role: RoleMasterEntity;

  @OneToMany(() => BookNumberMasterEntity, (book) => book.collector, {
    cascade: true,
  })
  books: BookNumberMasterEntity[];

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
