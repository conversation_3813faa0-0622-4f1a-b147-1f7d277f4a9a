import { Repository } from 'typeorm';
import { WarshilKarEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class WarshikKarRepository extends Repository<WarshilKarEntity> {
  constructor(
    @InjectRepository(WarshilKarEntity)
    private readonly warshikKarRepository: Repository<WarshilKarEntity>,
  ) {
    super(
      warshikKarRepository.target,
      warshikKarRepository.manager,
      warshikKarRepository.queryRunner,
    );
  }

  async saveData(input: any) {
    let data = this.warshikKarRepository.create(input);
    data = await this.warshikKarRepository.save(data);

    return data;
  }
  async getAllData(property_id: string) {
    return await this.warshikKarRepository
      .createQueryBuilder('warshik_kar')
      .select([
        'warshik_kar.*',
      ])
      .leftJoinAndSelect('warshik_kar.property', 'property')
      .where("warshik_kar.status = 'active'")
      .where('property.property_id = :property_id' ,{property_id})
      .orderBy('property.updated_at', 'DESC')
      .getRawMany();
  }


  async findAllData(currentYear:String) {
    return await this.warshikKarRepository
      .createQueryBuilder('warshik_kar')
      .select([
        'warshik_kar.warshik_karId',
        'warshik_kar.financial_year',
        'warshik_kar.total_tax',
        'warshik_kar.all_property_tax_sum_total',
        'warshik_kar.all_property_tax_sum',
        'warshik_kar.all_property_tax_sum_current',
        'warshik_kar.property_id',
        'warshik_kar.billNo',
        'warshik_kar.bill_generation_date',
        'property.propertyNumber',
        'property.sr_no',
        'property.property_id',
        'ward.ward_name',
        'zone.zoneName',
      ])
      .leftJoin('warshik_kar.property', 'property')
      .leftJoin('property.ward', 'ward')
      .leftJoin('property.zone', 'zone')
      .where('warshik_kar.status = :status AND warshik_kar.financial_year = :currentYear', { status: 'active', currentYear })
      .orderBy('property.updated_at', 'DESC')
      .getMany();
  }
  async findAllData_wardwise(
    ward: string,
    value: string,
    searchOn: string ="all",
    fy: string,
  ) {
    const queryBuilder = this.warshikKarRepository
      .createQueryBuilder('warshik_kar')
      .select([
        'warshik_kar.warshik_karId',
        'warshik_kar.financial_year',
        'warshik_kar.total_tax',
        'warshik_kar.all_property_tax_sum_total',
        'warshik_kar.all_property_tax_sum',
        'warshik_kar.all_property_tax_sum_current',
        'warshik_kar.property_id',
        'warshik_kar.billNo',
        'warshik_kar.bill_generation_date',
        'property.propertyNumber',
        'property.sr_no',
        'ward.ward_name',
        'street.street_name',
        'zone.zoneName',

        'ownerDetails.name',
        'ownerDetails.owner_type_id',
        'ownerDetails.property_owner_details_id',
        'owner_type.owner_type',
      ])
      .leftJoin('warshik_kar.property', 'property')
      .leftJoin('property.property_owner_details', 'ownerDetails')
      .leftJoin('ownerDetails.owner_type', 'owner_type') //
      .leftJoin('property.zone', 'zone')
      .leftJoin('property.street', 'street')
      .leftJoin('property.ward', 'ward')
      .where('warshik_kar.status = :status', { status: 'active' })
      //.andWhere('warshik_kar.financial_year = :fy', { fy: fy })

      .andWhere('warshik_kar.billNo IS NOT NULL');

    // Apply the additional filter only if searchOn is not 'all'
    if (searchOn !== 'all') {
      console.log("propertyNumberpropertyNumber",searchOn,value)
      queryBuilder.andWhere(`property.${searchOn} = :value`, { value: value });
    } else {
      queryBuilder.andWhere('ward.ward_name = :ward', { ward });
    }

    const [data , total] =await queryBuilder.orderBy('property.updated_at', 'DESC').getManyAndCount();
    return {
      data,
      total
    };
  }
  async findById(warshik_karId: string) {
    try {
      return await this.warshikKarRepository
        .createQueryBuilder('warshik_kar')
        .where('warshik_kar.warshik_karId = :warshik_karId', { warshik_karId })
        .getOne();
    } catch (error) {
      console.error('Error in findById:', error);
      throw error;
    }
  }


  async findByWarshikarId(warshik_karId: string, property_id?: string) {
    try {
      return await this.warshikKarRepository.createQueryBuilder('warshik_kar')
 
      .leftJoinAndSelect('warshik_kar.property', 'property')
      .where('warshik_kar.warshik_karId = :warshik_karId', { warshik_karId })
      .andWhere('warshik_kar.status = :status', { status: 'active' })
 
      .getOne();

      // return await this.warshikKarRepository.find({
      //   where: {
      //     warshik_karId,
      //     property: {
      //       property_id: property_id,
      //     },
      //   },
      // });
    } catch (error) {
      throw error;
    }
  }

  async getActiveWarshikKarByPropertyId(currentYear?:String) {
    return this.warshikKarRepository
      .createQueryBuilder('warshikKar')
      .where('warshikKar.status = :status AND warshikKar.financial_year = :currentYear', { status: 'active',currentYear:currentYear })
      .leftJoinAndSelect('warshikKar.property', 'property')
      .getMany();
  }

  async findByPropertyIds(propertyIds: string[]) {
    try {
      return await this.warshikKarRepository
        .createQueryBuilder('warshikKar')
        .leftJoinAndSelect('warshikKar.property', 'property')
        .where('property.property_id IN (:...propertyIds)', { propertyIds })
        .andWhere('warshikKar.status = :status', { status: 'active' })
        .orderBy('property.updated_at', 'DESC')
        .getMany();
    } catch (error) {
      console.error('Error in findByPropertyIds:', error);
      throw error;
    }
  }

  async get_data_for_bill(billNo: string) {
    try {
      return await this.warshikKarRepository
        .createQueryBuilder('warshik_kar')
        .select([
          'warshik_kar.warshik_karId',
          'warshik_kar.billNo',
          'warshik_kar.bill_generation_date',
          'ward.ward_name',
          'property.address',
          'property.house_or_apartment_name',
          'property.old_propertyNumber',
          'property.propertyNumber',
          'property.city_survey_number',
          'property.note',
          'street.street_name',
          'zone.zoneName',
          'warshik_kar.all_property_tax_sum_total', // sankalit kar (gharpatti)
          'warshik_kar.all_property_tax_sum',
          'warshik_kar.all_property_tax_sum_current',
          'warshik_kar.other_tax_sum_tax', //other //0
          'warshik_kar.other_tax_sum_tax_current',
          'warshik_kar.other_tax_sum_tax_previous',

          'warshik_kar.total_tax', //total_tax
          'warshik_kar.total_tax_current',
          'warshik_kar.total_tax_previous',

          'warshik_kar.tax_type_1',
          'warshik_kar.tax_type_2',
          'warshik_kar.tax_type_3',
          'warshik_kar.tax_type_4',
          'warshik_kar.tax_type_5',
          'warshik_kar.tax_type_6',
          'warshik_kar.tax_type_7',
          'warshik_kar.tax_type_8',
          'warshik_kar.tax_type_9',

          'warshik_kar.tax_type_1_current',
          'warshik_kar.tax_type_2_current',
          'warshik_kar.tax_type_3_current',
          'warshik_kar.tax_type_4_current',
          'warshik_kar.tax_type_5_current',
          'warshik_kar.tax_type_6_current',
          'warshik_kar.tax_type_7_current',
          'warshik_kar.tax_type_8_current',
          'warshik_kar.tax_type_9_current',

          // Previous tax types
          'warshik_kar.tax_type_1_previous',
          'warshik_kar.tax_type_2_previous',
          'warshik_kar.tax_type_3_previous',
          'warshik_kar.tax_type_4_previous',
          'warshik_kar.tax_type_5_previous',
          'warshik_kar.tax_type_6_previous',
          'warshik_kar.tax_type_7_previous',
          'warshik_kar.tax_type_8_previous',
          'warshik_kar.tax_type_9_previous',

          'ownerDetails.name',
          'ownerDetails.mobile_number',

          'ownerDetails.owner_type_id',
          'ownerDetails.property_owner_details_id',
          'owner_type.owner_type',
          'property_usage_details.property_usage_details_id',
          'property_usage_type.usage_type',
          'property_usage_type.usage_type_id',
        ])
        .leftJoin('warshik_kar.property', 'property')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.street', 'street')
        .leftJoin('property.property_usage_details', 'property_usage_details')
        .leftJoin('property_usage_details.usageType', 'property_usage_type')

        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('ownerDetails.owner_type', 'owner_type')

        .where('warshik_kar.billNo = :billNo', { billNo })
        .orderBy('warshik_kar.updated_at', 'DESC')
        .getMany();
    } catch (error) {
      throw error;
    }
  }

  async get_data_for_bill_By_PropertyNUmber(propertyNumber: string) {
    try {
      return await this.warshikKarRepository
        .createQueryBuilder('warshik_kar')
        .select([
          'warshik_kar.warshik_karId',
          'warshik_kar.billNo',
          'warshik_kar.bill_generation_date',
          'ward.ward_name',
          'property.address',
          'property.house_or_apartment_name',
          'property.old_propertyNumber',
          'property.propertyNumber',
          'property.city_survey_number',
          'property.note',
          'street.street_name',
          'zone.zoneName',
          'warshik_kar.all_property_tax_sum_total',
          'warshik_kar.all_property_tax_sum',
          'warshik_kar.all_property_tax_sum_current',
          'warshik_kar.other_tax_sum_tax',
          'warshik_kar.other_tax_sum_tax_current',
          'warshik_kar.other_tax_sum_tax_previous',

          'warshik_kar.total_tax', //total_tax
          'warshik_kar.total_tax_current',
          'warshik_kar.total_tax_previous',

          'warshik_kar.tax_type_1',
          'warshik_kar.tax_type_2',
          'warshik_kar.tax_type_3',
          'warshik_kar.tax_type_4',
          'warshik_kar.tax_type_5',
          'warshik_kar.tax_type_6',
          'warshik_kar.tax_type_7',
          'warshik_kar.tax_type_8',
          'warshik_kar.tax_type_9',
          'warshik_kar.tax_type_10',


          'warshik_kar.tax_type_1_current',
          'warshik_kar.tax_type_2_current',
          'warshik_kar.tax_type_3_current',
          'warshik_kar.tax_type_4_current',
          'warshik_kar.tax_type_5_current',
          'warshik_kar.tax_type_6_current',
          'warshik_kar.tax_type_7_current',
          'warshik_kar.tax_type_8_current',
          'warshik_kar.tax_type_9_current',
          'warshik_kar.tax_type_10_current',


          // Previous tax types
          'warshik_kar.tax_type_1_previous',
          'warshik_kar.tax_type_2_previous',
          'warshik_kar.tax_type_3_previous',
          'warshik_kar.tax_type_4_previous',
          'warshik_kar.tax_type_5_previous',
          'warshik_kar.tax_type_6_previous',
          'warshik_kar.tax_type_7_previous',
          'warshik_kar.tax_type_8_previous',
          'warshik_kar.tax_type_9_previous',
          'warshik_kar.tax_type_10_previous',


          'ownerDetails.name',
          'ownerDetails.mobile_number',
               'ownerDetails.is_payer',
                 'ownerDetails.is_owner',


          'ownerDetails.owner_type_id',
          'ownerDetails.property_owner_details_id',
          'owner_type.owner_type',
          'property_usage_details.property_usage_details_id',
          'property_usage_type.usage_type',
          'property_usage_type.usage_type_id',
          'billdata.billNo',
          'billdata.bill_generation_date',
          'billdata.property_id',
        ])
        .leftJoin('warshik_kar.property', 'property')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.street', 'street')
        .leftJoin('property.property_usage_details', 'property_usage_details')
        .leftJoin('property_usage_details.usageType', 'property_usage_type')
        .leftJoin('property.billdata', 'billdata')

        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('ownerDetails.owner_type', 'owner_type')

        .where('property."propertyNumber" = :propertyNumber', {
          propertyNumber,
        })
        .andWhere('warshik_kar.status = :status', { status: 'active' })

        .orderBy('warshik_kar.updated_at', 'DESC')
        .getMany();
    } catch (error) {
      throw error;
    }
  }

  async get_data_for_bill_by_ward(ward: string, limit?: number, page?: number) {
    try {
      // Set default values for limit and page if they are not provided
      const defaultLimit = Number.MAX_SAFE_INTEGER; // A large number to fetch all records
      const defaultPage = 1;

      const actualLimit = limit ?? defaultLimit;
      const actualPage = page ?? defaultPage;

      const offset = (actualPage - 1) * actualLimit;

      const queryBuilder = await this.warshikKarRepository
        .createQueryBuilder('warshik_kar')
        .select([
          'warshik_kar.warshik_karId',
          'warshik_kar.billNo',
          'warshik_kar.bill_generation_date',

          'ward.ward_name',
          'property.address',
          'property.house_or_apartment_name',
          'property.old_propertyNumber',
          'property.propertyNumber',
          'property.city_survey_number',
          'property.note',
          'street.street_name',
          'zone.zoneName',
          'warshik_kar.all_property_tax_sum_total',
          'warshik_kar.all_property_tax_sum',
          'warshik_kar.all_property_tax_sum_current',
          'warshik_kar.other_tax_sum_tax',
          'warshik_kar.other_tax_sum_tax_current',
          'warshik_kar.other_tax_sum_tax_previous',
          'warshik_kar.total_tax',
          'warshik_kar.total_tax_current',
          'warshik_kar.total_tax_previous',
          'warshik_kar.tax_type_1',
          'warshik_kar.tax_type_2',
          'warshik_kar.tax_type_3',
          'warshik_kar.tax_type_4',
          'warshik_kar.tax_type_5',
          'warshik_kar.tax_type_6',
          'warshik_kar.tax_type_7',
          'warshik_kar.tax_type_8',
          'warshik_kar.tax_type_9',
          'warshik_kar.tax_type_1_current',
          'warshik_kar.tax_type_2_current',
          'warshik_kar.tax_type_3_current',
          'warshik_kar.tax_type_4_current',
          'warshik_kar.tax_type_5_current',
          'warshik_kar.tax_type_6_current',
          'warshik_kar.tax_type_7_current',
          'warshik_kar.tax_type_8_current',
          'warshik_kar.tax_type_9_current',
          'warshik_kar.tax_type_1_previous',
          'warshik_kar.tax_type_2_previous',
          'warshik_kar.tax_type_3_previous',
          'warshik_kar.tax_type_4_previous',
          'warshik_kar.tax_type_5_previous',
          'warshik_kar.tax_type_6_previous',
          'warshik_kar.tax_type_7_previous',
          'warshik_kar.tax_type_8_previous',
          'warshik_kar.tax_type_9_previous',
          'ownerDetails.name',
          'ownerDetails.mobile_number',
          'ownerDetails.owner_type_id',
          'ownerDetails.property_owner_details_id',
          'owner_type.owner_type',
          'property_usage_details.usage_type_id',
          'property_usage_details.property_usage_details_id',
          'property_usage_type.usage_type',
          'property_usage_type.usage_type_id',
          'billdata.billNo',
          'billdata.bill_generation_date',
          'billdata.property_id',
        ])

        .leftJoin('warshik_kar.property', 'property')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.street', 'street')
        .leftJoin('property.property_usage_details', 'property_usage_details')
        .leftJoin('property_usage_details.usageType', 'property_usage_type')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('ownerDetails.owner_type', 'owner_type')

        .leftJoin('property.billdata', 'billdata')
        .where('ward.ward_name = :ward', { ward: ward });

      // Log the generated SQL query for debugging

      const data = await queryBuilder
        .orderBy('property.sr_no', 'ASC')
        .getMany();

      // Log the returned data for debugging

      const paginatedData = data.slice(offset, offset + actualLimit);
      return paginatedData;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async get_data_for_nanuma9(warshik_karId: string) {
    try {
      return await this.warshikKarRepository
        .createQueryBuilder('warshik_kar')
        .select([
          'warshik_kar.warshik_karId',
          'warshik_kar.billNo',
          'warshik_kar.bill_generation_date',
          'ward.ward_name',
          'property.address',
          'property.gat_no',
          'property.house_or_apartment_name',
          'property.old_propertyNumber',
          'property.propertyNumber',
          'property.city_survey_number',
          'property.note',
          'street.street_name',
          'zone.zoneName',
          'warshik_kar.all_property_tax_sum', // sankalit kar (gharpatti)
          'warshik_kar.other_tax_sum_tax', //other //0
          'warshik_kar.total_tax', //total_tax

          'warshik_kar.tax_type_1',
          'warshik_kar.tax_type_2',
          'warshik_kar.tax_type_3',
          'warshik_kar.tax_type_4',
          'warshik_kar.tax_type_5',
          'warshik_kar.tax_type_6',
          'warshik_kar.tax_type_7',
          'warshik_kar.tax_type_8',

          'warshilKarTax.sq_ft_meter',
          'warshilKarTax.rr_rate',
          'warshilKarTax.rr_construction_rate',
          'warshilKarTax.depreciation_rate',
          'warshilKarTax.weighting',
          'warshilKarTax.capital_value',
          'warshilKarTax.tax_value',
          'warshilKarTax.tax',

          'property_usage_Details.construction_end_date',
          'property_usage_Details.are_sq_ft',
          'property_usage_Details.are_sq_meter',
          'property_usage_Details.construction_area',
          'property_usage_Details.construction_start_year',

          'ownerDetails.name',
          'ownerDetails.owner_type_id',
          'ownerDetails.property_owner_details_id',
          'owner_type.owner_type',
          'propertyType.propertyType',
        ])
        .leftJoin('warshik_kar.property', 'property')
        .leftJoin('warshik_kar.warshilKarTax', 'warshilKarTax')
        .leftJoin(
          'warshilKarTax.property_usage_details',
          'property_usage_Details',
        )
        .leftJoin('property_usage_Details.propertyType', 'propertyType')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.street', 'street')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('ownerDetails.owner_type', 'owner_type')

        .where('warshik_kar.warshik_karId= :warshik_karId', { warshik_karId })
        .orderBy('warshik_kar.updated_at', 'DESC')
        .getOne();
    } catch (error) {
      throw error;
    }
  }
  async findTotalTax(year: string): Promise<number> {
    const result = await this.warshikKarRepository
        .createQueryBuilder('warshik_kar')
        .select('SUM(warshik_kar.total_tax)', 'total_tax')
        .where('warshik_kar.financial_year = :year', { year })
        .getRawOne(); // Execute the query and get the raw result

    // Extract the total tax from the result
    return result ? parseFloat(result.total_tax) : 0;
}

}
