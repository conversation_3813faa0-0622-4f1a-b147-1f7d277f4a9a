import { Repository } from 'typeorm';
import { ElectionBoundaryMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class ElectionBoundaryMasterRepository extends Repository<ElectionBoundaryMasterEntity> {
  constructor(
    @InjectRepository(ElectionBoundaryMasterEntity)
    private readonly electionBoundaryMasterRepository: Repository<ElectionBoundaryMasterEntity>,
  ) {
    super(
      electionBoundaryMasterRepository.target,
      electionBoundaryMasterRepository.manager,
      electionBoundaryMasterRepository.queryRunner,
    );
  }

  async saveData(input: {
    electionBoundaryName: string;
  }): Promise<ElectionBoundaryMasterEntity> {
    let data = this.electionBoundaryMasterRepository.create(input);
    data = await this.electionBoundaryMasterRepository.save(data);
    return data;
  }

  async findAllData(): Promise<ElectionBoundaryMasterEntity[]> {
    return await this.electionBoundaryMasterRepository
      .createQueryBuilder('election_boundary_master')
      .orderBy('election_boundary_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string): Promise<ElectionBoundaryMasterEntity> {
    return await this.electionBoundaryMasterRepository
      .createQueryBuilder('election_boundary_master')
      .where(
        'election_boundary_master.electionBoundary_id = :electionBoundary_id',
        {
          electionBoundary_id: id,
        },
      )
      .getOne();
  }

  async updateData(id: string, input: { electionBoundaryName?: string }) {
    return await this.electionBoundaryMasterRepository
      .createQueryBuilder('election_boundary_master')
      .update(ElectionBoundaryMasterEntity)
      .set(input)
      .where('electionBoundary_id = :electionBoundary_id', {
        electionBoundary_id: id,
      })
      .execute();
  }

  async deleteData(id: string) {
    return await this.electionBoundaryMasterRepository
      .createQueryBuilder('election_boundary_master')
      .softDelete()
      .where('electionBoundary_id = :electionBoundary_id', {
        electionBoundary_id: id,
      })
      .execute();
  }
}
