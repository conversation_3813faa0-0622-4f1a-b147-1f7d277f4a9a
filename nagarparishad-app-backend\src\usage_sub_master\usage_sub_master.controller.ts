import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { UsageSubMasterService } from './usage_sub_master.service';
import {
  CreateUsageSubMasterDto,
  UpdateUsageSubMasterDto,
  UsageIdDto,
  UsageSubMasterDto,
} from './dto/usage_sub__master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@ApiTags('Usage Sub Master')
@Controller('usage-sub-master')
export class UsageSubMasterController {
  constructor(private readonly usageSubMasterService: UsageSubMasterService) {}

  
  @Form('Usage Sub')
  @Permissions('can_write')
  @ApiOperation({ summary: 'Create a new Usage Sub type' })
  @ApiResponse({
    status: 201,
    description: 'The Usage Sub type has been successfully created',
  })
  @Post()
  create(@Body() createUsageSubMasterDto: CreateUsageSubMasterDto) {
    return this.usageSubMasterService.create(createUsageSubMasterDto);
  }

  
  @Form('Usage Sub')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all Usage Sub types' })
  @ApiResponse({ status: 200, description: 'Returns all Usage Sub types' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.usageSubMasterService.findAll();
  }

  
  @Form('Usage Sub')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one Usage Sub type' })
  @ApiResponse({ status: 200, description: 'Returns Single Usage Sub type' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() usageSubMasterDto: UsageSubMasterDto) {
    return this.usageSubMasterService.findOne(usageSubMasterDto);
  }

  
  @Form('Usage Sub')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Update a Usage Sub type by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Usage Sub type has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Usage Sub type not found' })
  @Patch()
  update(
    @Query() usageSubMasterDto: UsageSubMasterDto,
    @Body() updateUsageSubMasterDto: UpdateUsageSubMasterDto,
  ) {
    return this.usageSubMasterService.update(
      usageSubMasterDto,
      updateUsageSubMasterDto,
    );
  }

  
  @Form('Usage Sub')
  @Permissions('can_delete')
  @ApiOperation({ summary: 'Delete a Usage Sub type by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Usage Sub type has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Usage Sub type not found' })
  @Delete()
  remove(@Query() usageSubMasterDto: UsageSubMasterDto) {
    return this.usageSubMasterService.remove(usageSubMasterDto);
  }

  
  @Form('Usage Sub')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all Usage Sub types by Usage Types' })
  @ApiResponse({
    status: 200,
    description: 'Returns all Usage Sub types by Usage Types',
  })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getByUsage')
  getByUsage(@Query() usageIdDto: UsageIdDto) {
    return this.usageSubMasterService.getByUsage(usageIdDto);
  }
}
