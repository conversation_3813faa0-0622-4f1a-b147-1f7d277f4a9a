import WhiteContainer from "@/components/custom/WhiteContainer";
import React, { useContext, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ArrowUpDown } from "lucide-react";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { toast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { WardUpdateObject } from "@/model/ward-master";
import { ColumnDef } from "@tanstack/react-table";
import AdministrativeBoundaryForm from "../AdministrativeBoundaryMasterForm/AdministrativeBoundaryMasterForm";
import { useAdministrativeBoundaryController } from "@/controller/master/AdministrativeBoundaryController";
import { MASTER } from "@/constant/config/api.config";
import AddNewBtn from "../../globalcomponent/AddNewForm";
import { MasterObjectColumnDef } from "@/model/zone-master";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

const AdministrativeBoundaryMaster = () => {
  const { t } = useTranslation();
  const userRef = useRef(null);
  const { administrativeBoundaryList, deleteAdministrativeBoundary } =
    useAdministrativeBoundaryController();
  const { setMasterComponent, setOpen } = useContext(GlobalContext);

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Master, FormName.AdministrativeBoundaryMaster, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Master, FormName.AdministrativeBoundaryMaster, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Master, FormName.AdministrativeBoundaryMaster, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Master, FormName.AdministrativeBoundaryMaster, Action.CanDelete);

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any | null>(null);

  function handleEdit(item: any): void {
    setOpen(true);
    setMasterComponent(
      <AdministrativeBoundaryForm
        btnTitle={"administrativeboundary.updateBtn"}
        editData={item && item}
      />,
    );
    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }

  function handleDelete(item: WardUpdateObject): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  const handleConfirmDelete = () => {
    if (selectedItem) {
      deleteAdministrativeBoundary(selectedItem.adminstrativeBoundary_id, {
        onSuccess: (response) => {
          if (response?.statusCode && response?.statusCode === 200) {
            toast({
              title: response?.message,
            });
          } else {
            toast({
              title: response?.message,
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const columns: ColumnDef<MasterObjectColumnDef>[] = [
    {
      accessorKey: `${t("SrNo")}`,
      header: `${t("SrNo")}`,
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: "adminstrativeBoundary_name",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("administrativeboundary.administrativeboundaryColumn")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
     cell: ({ row }: { row: any })  => (
        <div className="">{row.original?.adminstrativeBoundary_name}</div>
      ),
    },
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: `${t("Actions")}`,
            enableHiding: true,
            cell: ({ row }: { row: any }) => (
              <>
                {canUpdate && (
                  <Button
                    variant="submit"
                    className="h-8 w-8 p-0 justify-center"
                    onClick={() => handleEdit(row?.original)}
                  >
                    {t("table.edit")}
                  </Button>
                )}
                {CanDelete && (
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0 justify-center ml-2  !border-BlueText !text-BlueText"
                    onClick={() => handleDelete(row?.original)}
                  >
                    {t("table.delete")}
                  </Button>
                )}
              </>
            ),
          },
        ]
      : []),
  ];

  const MasterType: string = MASTER.ADMINISTRATIVEBOUNDARY;

  return (
    <div className="bg-Secondary w-full h-full p-6" ref={userRef && userRef}>
      <p className="w-full flex items-center justify-between ml-2  text-[18px] font-semibold mb-2">
        {t("administrativeboundary.formTitle")}
      </p>
      {CanCreate && <WhiteContainer>
        {MasterType && <AddNewBtn masterType={MASTER.ADMINISTRATIVEBOUNDARY} />}
      </WhiteContainer>}
      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={administrativeBoundaryList}
          masterType={MASTER.ADMINISTRATIVEBOUNDARY}
          searchColumn="adminstrativeBoundary_name"
          searchKey="searchAdministrativeBoundry"
        />
      </WhiteContainer>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={selectedItem.adminstrativeBoundary_name}
          onDelete={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default AdministrativeBoundaryMaster;
