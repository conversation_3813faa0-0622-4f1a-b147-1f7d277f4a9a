import { Injectable, NotFoundException, Logger, ForbiddenException } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, getManager } from 'typeorm';
import {
  PaidDataEntity,
  PaymentInfoEntity,
  PaymentStatus,
  PropertyEntity,
  ReceiptEntity,
  BookNumberMasterEntity,
  DemandReportData,
  Property_Owner_Details_Entity,
  PenaltyFeeYearWiseEntity,
} from 'libs/database/entities';
import {
  PaymentInfoRepository,
  PaidDataRepository,
  ReceiptRepository,
  BookNumberMasterRepository,
  BackupPaymentDetailsRepository,
  Financial_yearRepository,
  PenaltyFeeYearWiseRepository,
} from 'libs/database/repositories';
import { AnnualKarAkaraniService } from 'src/annual-kar-akarani/annual-kar-akarani.service';
import { PaidDataService } from 'src/paid-data/paid-data.service';
import { PropertyGroupDto } from './dto/payment_log_master.dto';

@Injectable()
export class PaymentLogMasterService {
  private readonly logger = new Logger(PaymentLogMasterService.name);

  constructor(
    @InjectDataSource() private readonly dataSource: DataSource,
    private readonly paymentInfoRepository: PaymentInfoRepository,
    private readonly paidDataRepository: PaidDataRepository,
    private readonly annualKarAkaraniService: AnnualKarAkaraniService,
    private readonly receiptRepository: ReceiptRepository,
    private readonly bookRepo: BookNumberMasterRepository,
    private readonly backupPaymentDetailsRepo: BackupPaymentDetailsRepository,
    private readonly financialYearRepo: Financial_yearRepository,
    private readonly penaltyFeeYearWiseRepository: PenaltyFeeYearWiseRepository,
    private readonly paidDataService: PaidDataService,
  ) {}

  private subtractPaidDataFromBillData(billData: any, summedPaidData: any) {
    const updatedWarshikKar = billData.warshikKar.map((warshik: any) => {
      return {
        all_property_tax_sum_total:
          warshik.all_property_tax_sum_total -
          (summedPaidData.all_property_tax_sum || 0),
        all_property_tax_sum_prev:
          warshik.all_property_tax_sum -
          (summedPaidData.all_property_tax_sum_prev || 0),
        all_property_tax_sum_curr:
          warshik.all_property_tax_sum_current -
          (summedPaidData.all_property_tax_sum_curr || 0),

        tax_type_1: warshik.tax_type_1 - (summedPaidData.tax_type_1 || 0),
        tax_type_1_prev:
          warshik.tax_type_1_previous - (summedPaidData.tax_type_1_prev || 0),
        tax_type_1_curr:
          warshik.tax_type_1_current - (summedPaidData.tax_type_1_curr || 0),
        tax_type_2: warshik.tax_type_2 - (summedPaidData.tax_type_2 || 0),
        tax_type_2_prev:
          warshik.tax_type_2_previous - (summedPaidData.tax_type_2_prev || 0),
        tax_type_2_curr:
          warshik.tax_type_2_current - (summedPaidData.tax_type_2_curr || 0),

        tax_type_3: warshik.tax_type_3 - (summedPaidData.tax_type_3 || 0),
        tax_type_3_prev:
          warshik.tax_type_3_previous - (summedPaidData.tax_type_3_prev || 0),
        tax_type_3_curr:
          warshik.tax_type_3_current - (summedPaidData.tax_type_3_curr || 0),

        tax_type_4: warshik.tax_type_4 - (summedPaidData.tax_type_4 || 0),
        tax_type_4_prev:
          warshik.tax_type_4_previous - (summedPaidData.tax_type_4_prev || 0),
        tax_type_4_curr:
          warshik.tax_type_4_current - (summedPaidData.tax_type_4_curr || 0),

        tax_type_5: warshik.tax_type_5 - (summedPaidData.tax_type_5 || 0),
        tax_type_5_prev:
          warshik.tax_type_5_previous - (summedPaidData.tax_type_5_prev || 0),
        tax_type_5_curr:
          warshik.tax_type_5_current - (summedPaidData.tax_type_5_curr || 0),

        tax_type_6: warshik.tax_type_6 - (summedPaidData.tax_type_6 || 0),
        tax_type_6_prev:
          warshik.tax_type_6_previous - (summedPaidData.tax_type_6_prev || 0),
        tax_type_6_curr:
          warshik.tax_type_6_current - (summedPaidData.tax_type_6_curr || 0),

        tax_type_7: warshik.tax_type_7 - (summedPaidData.tax_type_7 || 0),
        tax_type_7_prev:
          warshik.tax_type_7_previous - (summedPaidData.tax_type_7_prev || 0),
        tax_type_7_curr:
          warshik.tax_type_7_current - (summedPaidData.tax_type_7_curr || 0),

        tax_type_8: warshik.tax_type_8 - (summedPaidData.tax_type_8 || 0),
        tax_type_8_prev:
          warshik.tax_type_8_previous - (summedPaidData.tax_type_8_prev || 0),
        tax_type_8_curr:
          warshik.tax_type_8_current - (summedPaidData.tax_type_8_curr || 0),

        tax_type_9: warshik?.tax_type_9 || 0,
        tax_type_9_prev:
          warshik?.tax_type_9_previous || 0 ,
        tax_type_9_curr:
          warshik.tax_type_9_current || 0,

        tax_type_10: warshik.tax_type_10 - (summedPaidData.tax_type_10 || 0),
        tax_type_10_prev:
          warshik.tax_type_10_previous - (summedPaidData.tax_type_10_prev || 0),
        tax_type_10_curr:
          warshik.tax_type_10_current - (summedPaidData.tax_type_10_curr || 0),

        other_tax_sum_tax:
          warshik.other_tax_sum_tax - (summedPaidData.other_tax_sum_tax || 0),
        other_tax_sum_tax_prev:
          warshik.other_tax_sum_tax_previous -
          (summedPaidData.other_tax_sum_tax_prev || 0),
        other_tax_sum_tax_curr:
          warshik.other_tax_sum_tax_current -
          (summedPaidData.other_tax_sum_tax_curr || 0),

        total_tax:
          warshik.total_tax -
          (summedPaidData.total_amount || 0) -
          (summedPaidData?.other_discount || 0) + (summedPaidData.tax_type_9 || 0),
        // total_tax: warshik.total_tax - (summedPaidData.total_amount || 0),
      };
    });

    return {
      ...billData,

      warshikKar: updatedWarshikKar,
      property_type_discount:
        billData.warshikKar[0].property_type_discount || 0,
    };
  }

  async findLatestBillData(
    property_number: string,
    searchOn: string,
    fy: string,
  ) {
    try {
      const latestBillData: any =
        await this.annualKarAkaraniService.getWarshikKarAkarniForBillData(
          property_number,
          searchOn,
          fy,
        );
      // Check if data is empty or if warshikKar array is empty

        console.log("latestBillDatalatestBillData-->",latestBillData.data.warshikKar)
      if (
        !latestBillData.data ||
        (Array.isArray(latestBillData.data) &&
          latestBillData.data.length === 0) ||
        (latestBillData.data.warshikKar &&
          latestBillData.data.warshikKar.length === 0)
      ) {
        throw new NotFoundException(
          `No bill data found for property ${property_number} for financial year ${fy}`,
        );
      }


      // Make sure propertyNumber exists before trying to use it
      if (!latestBillData.data.propertyNumber) {
        console.log('Warning: propertyNumber is missing in the bill data');
        throw new NotFoundException('Property number missing in bill data');
      }

      const summedPaidData =
        await this.paidDataRepository.findAndSumPaidDataByFinancialYearAndProperty(
          fy,
          latestBillData.data.propertyNumber,
        );

              console.log('latest bill data---->', latestBillData.data);
                    console.log('latest summedPaidData data---->', summedPaidData);



        console.log("summedPaidData",summedPaidData)

      const updatedBillData = this.subtractPaidDataFromBillData(
        latestBillData.data,
        summedPaidData,
      );

      return {
        message: 'Latest Bill Data Fetched and Updated Successfully',
        data: updatedBillData,
      };
    } catch (error) {
      console.log('ERROR', error);
      // Return a more user-friendly error message
      if (error instanceof NotFoundException) {
        throw error;
      } else {
        throw new NotFoundException(
          `Failed to retrieve bill data: ${error.message}`,
        );
      }
    }
  }

  // async createPayment(paymentData: any) {
  //   const queryRunner = this.dataSource.createQueryRunner();

  //   await queryRunner.connect();
  //   await queryRunner.startTransaction();

  //   try {
  //     const paidData = new PaidDataEntity();
  //     paidData.property_id = paymentData.property_id;
  //     paidData.property_number = paymentData.property_number;
  //     paidData.financial_year = paymentData.financial_year;
  //     paidData.all_property_tax_sum =
  //       paymentData.all_property_tax_sum_total ?? 0;
  //     paidData.all_property_tax_sum_prev =
  //       paymentData.all_property_tax_sum_prev ?? 0;
  //     paidData.all_property_tax_sum_curr =
  //       paymentData.all_property_tax_sum_curr ?? 0;
  //     paidData.tax_type_1 = paymentData.tax_type_1 ?? 0;
  //     paidData.tax_type_1_prev = paymentData.tax_type_1_prev ?? 0;
  //     paidData.tax_type_1_curr = paymentData.tax_type_1_curr ?? 0;
  //     paidData.tax_type_2 = paymentData.tax_type_2 ?? 0;
  //     paidData.tax_type_2_prev = paymentData.tax_type_2_prev ?? 0;
  //     paidData.tax_type_2_curr = paymentData.tax_type_2_curr ?? 0;
  //     paidData.tax_type_3 = paymentData.tax_type_3 ?? 0;
  //     paidData.tax_type_3_prev = paymentData.tax_type_3_prev ?? 0;
  //     paidData.tax_type_3_curr = paymentData.tax_type_3_curr ?? 0;
  //     paidData.tax_type_4 = paymentData.tax_type_4 ?? 0;
  //     paidData.tax_type_4_prev = paymentData.tax_type_4_prev ?? 0;
  //     paidData.tax_type_4_curr = paymentData.tax_type_4_curr ?? 0;
  //     paidData.tax_type_5 = paymentData.tax_type_5 ?? 0;
  //     paidData.tax_type_5_prev = paymentData.tax_type_5_prev ?? 0;
  //     paidData.tax_type_5_curr = paymentData.tax_type_5_curr ?? 0;
  //     paidData.tax_type_6 = paymentData.tax_type_6 ?? 0;
  //     paidData.tax_type_6_prev = paymentData.tax_type_6_prev ?? 0;
  //     paidData.tax_type_6_curr = paymentData.tax_type_6_curr ?? 0;
  //     paidData.tax_type_7 = paymentData.tax_type_7 ?? 0;
  //     paidData.tax_type_7_prev = paymentData.tax_type_7_prev ?? 0;
  //     paidData.tax_type_7_curr = paymentData.tax_type_7_curr ?? 0;
  //     paidData.tax_type_8 = paymentData.tax_type_8 ?? 0;
  //     paidData.tax_type_8_prev = paymentData.tax_type_8_prev ?? 0;
  //     paidData.tax_type_8_curr = paymentData.tax_type_8_curr ?? 0;
  //     paidData.tax_type_9 = paymentData.tax_type_9 ?? 0;
  //     paidData.tax_type_9_prev = paymentData.tax_type_9_prev ?? 0;
  //     paidData.tax_type_9_curr = paymentData.tax_type_9_curr ?? 0;
  //     paidData.tax_type_10 = paymentData.tax_type_10 ?? 0;
  //     paidData.tax_type_10_prev = paymentData.tax_type_10_prev ?? 0;
  //     paidData.tax_type_10_curr = paymentData.tax_type_10_curr ?? 0;
  //     paidData.other_tax_sum_tax = paymentData.other_tax_sum_tax ?? 0;
  //     paidData.other_tax_sum_tax_prev = paymentData.other_tax_sum_tax_prev ?? 0;
  //     paidData.other_tax_sum_tax_curr = paymentData.other_tax_sum_tax_curr ?? 0;
  //     paidData.total_amount = paymentData.total_tax ?? 0;
  //     paidData.remaining_amount = paymentData.remaining_amount ?? 0;
  //     paidData.status = 'paid';

  //     const savedPaidData = await queryRunner.manager.save(paidData);
  //       //     const paymentInfo = new PaymentInfoEntity();
  //     paymentInfo.property = {
  //       property_id: paymentData.property_id,
  //     } as PropertyEntity;
  //     paymentInfo.amount = paymentData.total_tax ?? 0;
  //     paymentInfo.payment_mode = paymentData.payment_mode;
  //     paymentInfo.payment_status = PaymentStatus.COMPLETED;
  //     paymentInfo.payment_date = paymentData.payment_date;
  //     paymentInfo.paidData = savedPaidData;
  //     const savedPaymentInfo = await queryRunner.manager.save(paymentInfo);
  //       //     // Update BookNumberMasterEntity
  //       //     const bookEntity = paymentData.book_number
  //       ? await queryRunner.manager.findOne(BookNumberMasterEntity, {
  //           where: { book_number: paymentData.book_number },
  //         })
  //       : null;

  //       //
  //     const receipt = new ReceiptEntity();
  //     receipt.property = {
  //       property_id: paymentData.property_id,
  //     } as PropertyEntity;
  //     receipt.paymentInfo = savedPaymentInfo;
  //     receipt.book_number = paymentData.book_number ?? '';
  //     receipt.book_receipt_number = paymentData.book_receipt_number ?? '';
  //     receipt.remark = paymentData.remark ?? '';
  //     receipt.financial_year = paymentData.financial_year;
  //     receipt.bookNumber = bookEntity;
  //     receipt.receipt_date = new Date();

  //     receipt.additional_notes = paymentData.additional_notes ?? '';
  //     const savedReceipt = await queryRunner.manager.save(receipt);

  //     if (bookEntity) {
  //       const receiptNumber = paymentData.book_receipt_number;
  //       bookEntity.availableReceipts = bookEntity.availableReceipts.filter(
  //         (r) => r !== receiptNumber,
  //       );
  //       bookEntity.receiptsInUse = [
  //         ...new Set([...bookEntity.receiptsInUse, receiptNumber]),
  //       ];
  //       await queryRunner.manager.save(bookEntity);
  //     }

  //     await queryRunner.commitTransaction();
  //     return {
  //       message: 'Payment successfully processed!',
  //     };
  //   } catch (error) {
  //     await queryRunner.rollbackTransaction();
  //     return {
  //       message: 'Payment processing failed!',
  //       error: error.message,
  //     };
  //   } finally {
  //     await queryRunner.release();
  //   }
  // }

  // // Uncomment the following code if you want to use the createPayment method with a different approach

  async createPayment(paymentData: any) {
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      // Create and save PaidDataEntity

      const propertyOwner = await queryRunner.manager.findOne(
        Property_Owner_Details_Entity,
        {
          where: {
            property: { property_id: paymentData.property_id },
            is_payer: true,
          },
        },
      );
      console.log('propertyOwnerpropertyOwner', propertyOwner);

      const taxPayerName = propertyOwner?.name || null;

      const paidData = new PaidDataEntity();
      paidData.property = {
        property_id: paymentData.property_id,
      } as PropertyEntity;
      paidData.property_number = paymentData.property_number;
      paidData.financial_year = paymentData.financial_year;
      paidData.all_property_tax_sum =
        paymentData.all_property_tax_sum_total ?? 0;
      paidData.all_property_tax_sum_prev =
        paymentData.all_property_tax_sum_prev ?? 0;
      paidData.all_property_tax_sum_curr =
        paymentData.all_property_tax_sum_curr ?? 0;
      paidData.tax_type_1 = paymentData.tax_type_1 ?? 0;
      paidData.tax_type_1_prev = paymentData.tax_type_1_prev ?? 0;
      paidData.tax_type_1_curr = paymentData.tax_type_1_curr ?? 0;
      paidData.tax_type_2 = paymentData.tax_type_2 ?? 0;
      paidData.tax_type_2_prev = paymentData.tax_type_2_prev ?? 0;
      paidData.tax_type_2_curr = paymentData.tax_type_2_curr ?? 0;
      paidData.tax_type_3 = paymentData.tax_type_3 ?? 0;
      paidData.tax_type_3_prev = paymentData.tax_type_3_prev ?? 0;
      paidData.tax_type_3_curr = paymentData.tax_type_3_curr ?? 0;
      paidData.tax_type_4 = paymentData.tax_type_4 ?? 0;
      paidData.tax_type_4_prev = paymentData.tax_type_4_prev ?? 0;
      paidData.tax_type_4_curr = paymentData.tax_type_4_curr ?? 0;
      paidData.tax_type_5 = paymentData.tax_type_5 ?? 0;
      paidData.tax_type_5_prev = paymentData.tax_type_5_prev ?? 0;
      paidData.tax_type_5_curr = paymentData.tax_type_5_curr ?? 0;
      paidData.tax_type_6 = paymentData.tax_type_6 ?? 0;
      paidData.tax_type_6_prev = paymentData.tax_type_6_prev ?? 0;
      paidData.tax_type_6_curr = paymentData.tax_type_6_curr ?? 0;
      paidData.tax_type_7 = paymentData.tax_type_7 ?? 0;
      paidData.tax_type_7_prev = paymentData.tax_type_7_prev ?? 0;
      paidData.tax_type_7_curr = paymentData.tax_type_7_curr ?? 0;
      paidData.tax_type_8 = paymentData.tax_type_8 ?? 0;
      paidData.tax_type_8_prev = paymentData.tax_type_8_prev ?? 0;
      paidData.tax_type_8_curr = paymentData.tax_type_8_curr ?? 0;
      // Handle tax_type_9 (penalties) specially
      paidData.tax_type_9 = paymentData.tax_type_9 ?? 0;
      paidData.tax_type_9_prev = paymentData.tax_type_9_prev ?? 0;
      paidData.tax_type_9_curr = paymentData.tax_type_9_curr ?? 0;

      // Initialize year_wise_penalty_data to track which years' penalties were paid
      const yearWisePenaltyData = {};
      paidData.tax_type_10 = paymentData.tax_type_10 ?? 0;
      paidData.tax_type_10_prev = paymentData.tax_type_10_prev ?? 0;
      paidData.tax_type_10_curr = paymentData.tax_type_10_curr ?? 0;
      paidData.other_tax_sum_tax = paymentData.other_tax_sum_tax ?? 0;
      paidData.other_tax_sum_tax_prev = paymentData.other_tax_sum_tax_prev ?? 0;
      paidData.other_tax_sum_tax_curr = paymentData.other_tax_sum_tax_curr ?? 0;
      paidData.total_amount = paymentData.total_tax ?? 0;
      paidData.other_discount = paymentData?.discount_amount ?? 0;

      paidData.remaining_amount = paymentData.remaining_amount ?? 0;
      paidData.status = 'paid';

      // Calculate the sum of all previous tax types
      const totalPreviousAmount =
        (paidData.all_property_tax_sum_prev || 0) +
        (paidData.tax_type_1_prev || 0) +
        (paidData.tax_type_2_prev || 0) +
        (paidData.tax_type_3_prev || 0) +
        (paidData.tax_type_4_prev || 0) +
        (paidData.tax_type_5_prev || 0) +
        (paidData.tax_type_6_prev || 0) +
        (paidData.tax_type_7_prev || 0) +
        (paidData.tax_type_8_prev || 0) +
        (paidData.tax_type_10_prev || 0) +
        (paidData.other_tax_sum_tax_prev || 0);

      const totalCurrentAmount =
        (paidData.all_property_tax_sum_curr || 0) +
        (paidData.tax_type_1_curr || 0) +
        (paidData.tax_type_2_curr || 0) +
        (paidData.tax_type_3_curr || 0) +
        (paidData.tax_type_4_curr || 0) +
        (paidData.tax_type_5_curr || 0) +
        (paidData.tax_type_6_curr || 0) +
        (paidData.tax_type_7_curr || 0) +
        (paidData.tax_type_8_curr || 0) +
        (paidData.tax_type_10_curr || 0) +
        (paidData.other_tax_sum_tax_curr || 0);

      // Check if any previous values exist that need penalty processing
      const hasPreviousValues = totalPreviousAmount > 0;

      // Process penalties if tax_type_9 (penalty) is being paid OR if any previous values are being paid
      if (paidData.tax_type_9_prev > 0 || hasPreviousValues) {
        console.log(
          `Processing penalties for property ${paymentData.property_id}. Tax_type_9: ${paidData.tax_type_9}, Total previous amount: ${totalPreviousAmount}`,
        );

        // Get all penalties for this property
        const propertyPenalties =
          await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
            paymentData.property_id,
          );

        // Sort penalties by year (oldest first)
        propertyPenalties.sort(
          (a: PenaltyFeeYearWiseEntity, b: PenaltyFeeYearWiseEntity) => {
            const yearA = parseInt(a.financial_year.split('-')[0]);
            const yearB = parseInt(b.financial_year.split('-')[0]);
            return yearA - yearB;
          },
        );

        // Use both explicit tax_type_9 payment and all previous tax payments for penalty processing
        let remainingPenaltyPayment =
          paidData.tax_type_9_prev + totalPreviousAmount;

        // Apply payment to penalties, starting with the oldest
        for (const penalty of propertyPenalties) {
          if (remainingPenaltyPayment <= 0) break;

          // Get the current values
          const actualValue = Number(penalty.actual_value);
          const taxPercentage = Number(penalty.tax_percentage);
          const penaltyValue = Number(penalty.penalty_value);

          // Calculate how much of this penalty can be paid
          const paymentForThisPenalty = Math.min(
            remainingPenaltyPayment,
            actualValue + penaltyValue,
          );

          if (paymentForThisPenalty > 0) {
            // Record the payment in year_wise_penalty_data
            yearWisePenaltyData[penalty.financial_year] = {
              paid: paymentForThisPenalty,
              original: {
                actual_value: actualValue,
                tax_percentage: taxPercentage,
              },
            };

            // Calculate the new actual value after payment
            const newActualValue = Math.max(
              0,
              actualValue + penaltyValue - paymentForThisPenalty,
            );

            // Update the penalty in the database with the new actual_value
            // Don't calculate penalty_value here as it should only be calculated by cron job
            // await this.penaltyFeeYearWiseRepository.updatePenaltyFee(
            //   penalty.penalty_fee_id,
            //   {
            //     actual_value: newActualValue,
            //     // penalty_value will not be recalculated during payment processing
            //   },
            //   false, // Don't calculate penalty value during payment processing
            // );
            // Replace the existing call to updatePenaltyFee with the new function
            await this.penaltyFeeYearWiseRepository.updatePenaltyWithPayment(
              penalty.penalty_fee_id,
              paymentForThisPenalty,
            );

            // Reduce the remaining payment amount
            remainingPenaltyPayment -= paymentForThisPenalty;
          }
        }

        // We've already incorporated the previous values into remainingPenaltyPayment,
        // so we don't need a separate block to handle the case when tax_type_9 is 0 but hasPreviousValues is true.
        // The penalties will be processed in the main loop above.

        // Add a log to confirm that we've processed penalties based on both tax_type_9 and previous values
        console.log(
          `Processed penalties for property ${paymentData.property_id}. Total payment applied: ${paidData.tax_type_9 + totalPreviousAmount}`,
        );

        // Store the year-wise penalty data in the paid data entity
        paidData.year_wise_penalty_data = yearWisePenaltyData;
      }
     const currentFinancialYear =
          await this.financialYearRepo.getCurrentFinancialYear();
      if (paidData.tax_type_9_curr > 0) {
        console.log(
          `Processing current year penalties for property ${paymentData.property_id}. Tax_type_9_curr: ${paidData.tax_type_9_curr}, Total current amount: ${totalCurrentAmount}`,
        );

        // Get current financial year
   
        if (!currentFinancialYear) {
          console.log(
            'No current financial year found, skipping current year penalty processing',
          );
        } else {
          // Get penalties for this property for current year only
          const propertyPenalties =
            await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
              paymentData.property_id,
              currentFinancialYear.financial_year_range,
            );

          // Since there will be only one penalty for current year, process it directly
          if (propertyPenalties.length > 0) {
            const penalty = propertyPenalties[0]; // Get the single current year penalty

            // Get the current values
            const actualValue = Number(penalty.actual_value);
            const taxPercentage = Number(penalty.tax_percentage);
            const penaltyValue = Number(penalty.penalty_value);
            const totalPenaltyAmount = actualValue + penaltyValue;
            const totalPenaltyAmountPaid =
              totalCurrentAmount + paidData.tax_type_9_curr;

            // Calculate how much of this penalty can be paid
            const paymentForThisPenalty = Math.min(
              totalPenaltyAmountPaid,
              totalPenaltyAmount,
            );

            if (paymentForThisPenalty > 0) {
              // Record the payment in year_wise_penalty_data
              yearWisePenaltyData[penalty.financial_year] = {
                paid: paymentForThisPenalty,
                original: {
                  actual_value: actualValue,
                  tax_percentage: taxPercentage,
                },
              };

              // Calculate the new actual value after payment
              const newActualValue = Math.max(
                0,
                totalPenaltyAmount - totalPenaltyAmountPaid,
              );

              // Update the penalty in the database with the new actual_value
              // Don't calculate penalty_value here as it should only be calculated by cron job
              // await this.penaltyFeeYearWiseRepository.updatePenaltyFee(
              //   penalty.penalty_fee_id,
              //   {
              //     actual_value: newActualValue,
              //     // penalty_value will not be recalculated during payment processing
              //   },
              //   false, // Don't calculate penalty value during payment processing
              // );

                   await this.penaltyFeeYearWiseRepository.updatePenaltyWithPayment(
              penalty.penalty_fee_id,
              paymentForThisPenalty,
            );

              console.log(
                `Updated current year penalty for property ${paymentData.property_id}. Payment applied: ${paymentForThisPenalty}, New actual value: ${newActualValue}`,
              );
            }
          } else {
            console.log(
              `No current year penalty found for property ${paymentData.property_id}`,
            );
          }

          // Add a log to confirm that we've processed current year penalties
          console.log(
            `Processed current year penalties for property ${paymentData.property_id}. Total payment applied: ${paidData.tax_type_9_curr}`,
          );

          // Store the year-wise penalty data in the paid data entity
          paidData.year_wise_penalty_data = yearWisePenaltyData;
        }
      }

      const savedPaidData = await queryRunner.manager.save(paidData);

      // Create and save PaymentInfoEntity
      const paymentInfo = new PaymentInfoEntity();
      paymentInfo.property = {
        property_id: paymentData.property_id,
      } as PropertyEntity;
      paymentInfo.amount = paymentData.total_tax ?? 0;
      paymentInfo.payment_mode = paymentData.payment_mode;
      paymentInfo.payment_status = PaymentStatus.COMPLETED;
      paymentInfo.payment_date = paymentData?.payment_date || null;
      paymentInfo.tax_payer_name = taxPayerName;

      paymentInfo.financial_year_range = currentFinancialYear.financial_year_range;
      paymentInfo.paidData = savedPaidData;
      const savedPaymentInfo = await queryRunner.manager.save(paymentInfo);

      // Update BookNumberMasterEntity
      const bookEntity = paymentData.book_number
        ? await queryRunner.manager.findOne(BookNumberMasterEntity, {
            where: { book_number: paymentData.book_number },
          })
        : null;

      const receipt = new ReceiptEntity();
      receipt.property = {
        property_id: paymentData.property_id,
      } as PropertyEntity;
      receipt.paymentInfo = savedPaymentInfo;
      receipt.book_number = paymentData.book_number ?? '';
      receipt.book_receipt_number = paymentData.book_receipt_number ?? '';
      receipt.remark = paymentData.remark ?? '';
      receipt.financial_year = paymentData.financial_year;
      receipt.bookNumber = bookEntity;
      receipt.receipt_date = new Date();
      receipt.additional_notes = paymentData.additional_notes ?? '';
      await queryRunner.manager.save(receipt);

      if (bookEntity) {
        const receiptNumber = paymentData.book_receipt_number;
        bookEntity.availableReceipts = bookEntity.availableReceipts.filter(
          (r) => r !== receiptNumber,
        );
        bookEntity.receiptsInUse = [
          ...new Set([...bookEntity.receiptsInUse, receiptNumber]),
        ];
        await queryRunner.manager.save(bookEntity);
      }
      console.log(
        'latestBillData--->latestdemandeReportData in book neotyt seconde',
      );

      // Update DemandeReportData
      let latestdemandeReportData = await queryRunner.manager.findOne(
        DemandReportData,
        {
          where: {
            property: { property_id: paymentData.property_id },
            financial_year: paymentData.financial_year,
          },
          relations: ['property'],
        },
      );
      // let latestdemandeReportData =[];
      const latestBillData: any =
        await this.annualKarAkaraniService.getWarshikKarAkarniForBillData(
          paymentData.property_number,
          'propertyNumber',
          paymentData.financial_year,
        );

      const latestWarshikKarData: any =
        latestBillData.data?.warshikKar[0] || [];
      console.log(
        'latestBillData--->latestdemandeReportData',
        latestBillData.data.warshikKar,
      );

      let demandeReportData = new DemandReportData();

      // If no record exists, create a new one
      demandeReportData.property = {
        property_id: paymentData.property_id,
      } as PropertyEntity;
      demandeReportData.financial_year = paymentData.financial_year;
      demandeReportData.property_number = paymentData.property_number;

      // Update remaining amounts
      if (latestdemandeReportData) {
        demandeReportData.tax_type_1_remaining =
          (latestdemandeReportData.tax_type_1_remaining ?? 0) -
          (latestdemandeReportData.tax_type_1_paid ?? 0);

        demandeReportData.tax_type_1_prev_remaining =
          (latestdemandeReportData.tax_type_1_prev_remaining ?? 0) -
          (latestdemandeReportData.tax_type_1_prev_paid ?? 0);
        demandeReportData.tax_type_1_curr_remaining =
          (latestdemandeReportData.tax_type_1_curr_remaining ?? 0) -
          (latestdemandeReportData.tax_type_1_curr_paid ?? 0);

        demandeReportData.tax_type_2_prev_remaining =
          (latestdemandeReportData.tax_type_2_prev_remaining ?? 0) -
          (latestdemandeReportData.tax_type_2_prev_paid ?? 0);
        demandeReportData.tax_type_2_curr_remaining =
          (latestdemandeReportData.tax_type_2_curr_remaining ?? 0) -
          (latestdemandeReportData.tax_type_2_curr_paid ?? 0);

        demandeReportData.tax_type_3_prev_remaining =
          (latestdemandeReportData.tax_type_3_prev_remaining ?? 0) -
          (latestdemandeReportData.tax_type_3_prev_paid ?? 0);
        demandeReportData.tax_type_3_curr_remaining =
          (latestdemandeReportData.tax_type_3_curr_remaining ?? 0) -
          (latestdemandeReportData.tax_type_3_curr_paid ?? 0);

        demandeReportData.tax_type_4_prev_remaining =
          (latestdemandeReportData.tax_type_4_prev_remaining ?? 0) -
          (latestdemandeReportData.tax_type_4_prev_paid ?? 0);
        demandeReportData.tax_type_4_curr_remaining =
          (latestdemandeReportData.tax_type_4_curr_remaining ?? 0) -
          (latestdemandeReportData.tax_type_4_curr_paid ?? 0);

        demandeReportData.tax_type_5_prev_remaining =
          (latestdemandeReportData.tax_type_5_prev_remaining ?? 0) -
          (latestdemandeReportData.tax_type_5_prev_paid ?? 0);
        demandeReportData.tax_type_5_curr_remaining =
          (latestdemandeReportData.tax_type_5_curr_remaining ?? 0) -
          (latestdemandeReportData.tax_type_5_curr_paid ?? 0);

        demandeReportData.tax_type_6_prev_remaining =
          (latestdemandeReportData.tax_type_6_prev_remaining ?? 0) -
          (latestdemandeReportData.tax_type_6_prev_paid ?? 0);
        demandeReportData.tax_type_6_curr_remaining =
          (latestdemandeReportData.tax_type_6_curr_remaining ?? 0) -
          (latestdemandeReportData.tax_type_6_curr_paid ?? 0);

        demandeReportData.tax_type_7_prev_remaining =
          (latestdemandeReportData.tax_type_7_prev_remaining ?? 0) -
          (latestdemandeReportData.tax_type_7_prev_paid ?? 0);
        demandeReportData.tax_type_7_curr_remaining =
          (latestdemandeReportData.tax_type_7_curr_remaining ?? 0) -
          (latestdemandeReportData.tax_type_7_curr_paid ?? 0);

        demandeReportData.tax_type_8_prev_remaining =
          (latestdemandeReportData.tax_type_8_prev_remaining ?? 0) -
          (latestdemandeReportData.tax_type_8_prev_paid ?? 0);
        demandeReportData.tax_type_8_curr_remaining =
          (latestdemandeReportData.tax_type_8_curr_remaining ?? 0) -
          (latestdemandeReportData.tax_type_8_curr_paid ?? 0);

        demandeReportData.tax_type_9_prev_remaining =
          (latestdemandeReportData.tax_type_9_prev_remaining ?? 0) -
          (latestdemandeReportData.tax_type_9_prev_paid ?? 0);
        demandeReportData.tax_type_9_curr_remaining =
          (latestdemandeReportData.tax_type_9_curr_remaining ?? 0) -
          (latestdemandeReportData.tax_type_9_curr_paid ?? 0);

        demandeReportData.tax_type_10_prev_remaining =
          (latestdemandeReportData.tax_type_10_prev_remaining ?? 0) -
          (latestdemandeReportData.tax_type_10_prev_paid ?? 0);
        demandeReportData.tax_type_10_curr_remaining =
          (latestdemandeReportData.tax_type_10_curr_remaining ?? 0) -
          (latestdemandeReportData.tax_type_10_curr_paid ?? 0);

        demandeReportData.other_tax_sum_tax_prev_remaining =
          (latestdemandeReportData.other_tax_sum_tax_prev_remaining ?? 0) -
          (latestdemandeReportData.other_tax_sum_tax_prev_paid ?? 0);
        demandeReportData.other_tax_sum_tax_curr_remaining =
          (latestdemandeReportData.other_tax_sum_tax_curr_remaining ?? 0) -
          (latestdemandeReportData.other_tax_sum_tax_curr_paid ?? 0);
        demandeReportData.tax_type_1_remaining =
          (latestdemandeReportData.tax_type_1_remaining ?? 0) -
          (latestdemandeReportData.tax_type_1_paid ?? 0);
        demandeReportData.tax_type_2_remaining =
          (latestdemandeReportData.tax_type_2_remaining ?? 0) -
          (latestdemandeReportData.tax_type_2_paid ?? 0);
        demandeReportData.tax_type_3_remaining =
          (latestdemandeReportData.tax_type_3_remaining ?? 0) -
          (latestdemandeReportData.tax_type_3_paid ?? 0);
        demandeReportData.tax_type_4_remaining =
          (latestdemandeReportData.tax_type_4_remaining ?? 0) -
          (latestdemandeReportData.tax_type_4_paid ?? 0);
        demandeReportData.tax_type_5_remaining =
          (latestdemandeReportData.tax_type_5_remaining ?? 0) -
          (latestdemandeReportData.tax_type_5_paid ?? 0);
        demandeReportData.tax_type_6_remaining =
          (latestdemandeReportData.tax_type_6_remaining ?? 0) -
          (latestdemandeReportData.tax_type_6_paid ?? 0);
        demandeReportData.tax_type_7_remaining =
          (latestdemandeReportData.tax_type_7_remaining ?? 0) -
          (latestdemandeReportData.tax_type_7_paid ?? 0);
        demandeReportData.tax_type_8_remaining =
          (latestdemandeReportData.tax_type_8_remaining ?? 0) -
          (latestdemandeReportData.tax_type_8_paid ?? 0);
        demandeReportData.tax_type_9_remaining =
          (latestdemandeReportData.tax_type_9_remaining ?? 0) -
          (latestdemandeReportData.tax_type_9_paid ?? 0);
        demandeReportData.tax_type_10_remaining =
          (latestdemandeReportData.tax_type_10_remaining ?? 0) -
          (latestdemandeReportData.tax_type_10_paid ?? 0);
        demandeReportData.other_tax_sum_tax_remaining =
          (latestdemandeReportData.other_tax_sum_tax_remaining ?? 0) -
          (latestdemandeReportData.other_tax_sum_tax_paid ?? 0);

        demandeReportData.all_property_tax_sum_remaining =
          (latestdemandeReportData.all_property_tax_sum_remaining ?? 0) -
          (latestdemandeReportData.all_property_tax_sum_paid ?? 0);

        demandeReportData.all_property_tax_sum_prev_remaining =
          (latestdemandeReportData.all_property_tax_sum_prev_remaining ?? 0) -
          (latestdemandeReportData.all_property_tax_sum_prev_paid ?? 0);
        demandeReportData.all_property_tax_sum_curr_remaining =
          (latestdemandeReportData.all_property_tax_sum_curr_remaining ?? 0) -
          (latestdemandeReportData.all_property_tax_sum_curr_paid ?? 0);

        demandeReportData.total_amount_remaining =
          (latestdemandeReportData.total_amount_remaining ?? 0) -
          (latestdemandeReportData.total_amount_paid ?? 0);
      } else {
        demandeReportData.tax_type_1_prev_remaining =
          latestWarshikKarData.tax_type_1_previous ?? 0;
        demandeReportData.tax_type_1_curr_remaining =
          latestWarshikKarData.tax_type_1_current ?? 0;
        demandeReportData.tax_type_1_remaining =
          latestWarshikKarData.tax_type_1 ?? 0;

        demandeReportData.tax_type_2_prev_remaining =
          latestWarshikKarData.tax_type_2_previous ?? 0;
        demandeReportData.tax_type_2_curr_remaining =
          latestWarshikKarData.tax_type_2_current ?? 0;
        demandeReportData.tax_type_2_remaining =
          latestWarshikKarData.tax_type_2 ?? 0;

        demandeReportData.tax_type_3_prev_remaining =
          latestWarshikKarData.tax_type_3_previous ?? 0;
        demandeReportData.tax_type_3_curr_remaining =
          latestWarshikKarData.tax_type_3_current ?? 0;
        demandeReportData.tax_type_3_remaining =
          latestWarshikKarData.tax_type_3 ?? 0;

        demandeReportData.tax_type_4_prev_remaining =
          latestWarshikKarData.tax_type_4_previous ?? 0;
        demandeReportData.tax_type_4_curr_remaining =
          latestWarshikKarData.tax_type_4_current ?? 0;
        demandeReportData.tax_type_4_remaining =
          latestWarshikKarData.tax_type_4 ?? 0;

        demandeReportData.tax_type_5_prev_remaining =
          latestWarshikKarData.tax_type_5_previous ?? 0;
        demandeReportData.tax_type_5_curr_remaining =
          latestWarshikKarData.tax_type_5_current ?? 0;
        demandeReportData.tax_type_5_remaining =
          latestWarshikKarData.tax_type_5 ?? 0;

        demandeReportData.tax_type_6_prev_remaining =
          latestWarshikKarData.tax_type_6_previous ?? 0;
        demandeReportData.tax_type_6_curr_remaining =
          latestWarshikKarData.tax_type_6_current ?? 0;
        demandeReportData.tax_type_6_remaining =
          latestWarshikKarData.tax_type_6 ?? 0;

        demandeReportData.tax_type_7_prev_remaining =
          latestWarshikKarData.tax_type_7_previous ?? 0;
        demandeReportData.tax_type_7_curr_remaining =
          latestWarshikKarData.tax_type_7_current ?? 0;
        demandeReportData.tax_type_7_remaining =
          latestWarshikKarData.tax_type_7 ?? 0;

        demandeReportData.tax_type_8_prev_remaining =
          latestWarshikKarData.tax_type_8_previous ?? 0;
        demandeReportData.tax_type_8_curr_remaining =
          latestWarshikKarData.tax_type_8_current ?? 0;
        demandeReportData.tax_type_8_remaining =
          latestWarshikKarData.tax_type_8 ?? 0;

        demandeReportData.tax_type_9_prev_remaining =
          latestWarshikKarData.tax_type_9_previous ?? 0;
        demandeReportData.tax_type_9_curr_remaining =
          latestWarshikKarData.tax_type_9_current ?? 0;
        demandeReportData.tax_type_9_remaining =
          latestWarshikKarData.tax_type_9 ?? 0;

        demandeReportData.tax_type_10_prev_remaining =
          latestWarshikKarData.tax_type_10_previous ?? 0;
        demandeReportData.tax_type_10_curr_remaining =
          latestWarshikKarData.tax_type_10_current ?? 0;
        demandeReportData.tax_type_10_remaining =
          latestWarshikKarData.tax_type_10 ?? 0;

        demandeReportData.other_tax_sum_tax_prev_remaining =
          latestWarshikKarData.other_tax_sum_tax_previous ?? 0;
        demandeReportData.other_tax_sum_tax_curr_remaining =
          latestWarshikKarData.other_tax_sum_tax_current ?? 0;
        demandeReportData.other_tax_sum_tax_remaining =
          latestWarshikKarData.other_tax_sum_tax ?? 0;

        demandeReportData.all_property_tax_sum_prev_remaining =
          latestWarshikKarData.all_property_tax_sum ?? 0;
        demandeReportData.all_property_tax_sum_curr_remaining =
          latestWarshikKarData.all_property_tax_sum_current ?? 0;
        demandeReportData.all_property_tax_sum_remaining =
          latestWarshikKarData.all_property_tax_sum_total ?? 0;

        demandeReportData.total_amount_remaining =
          latestWarshikKarData.total_tax ?? 0;
      }

      // Save paid amounts
      demandeReportData.tax_type_1_paid = paymentData.tax_type_1 ?? 0;
      demandeReportData.tax_type_1_prev_paid = paymentData.tax_type_1_prev ?? 0;
      demandeReportData.tax_type_1_curr_paid = paymentData.tax_type_1_curr ?? 0;

      demandeReportData.tax_type_2_paid = paymentData.tax_type_2 ?? 0;
      demandeReportData.tax_type_2_prev_paid = paymentData.tax_type_2_prev ?? 0;
      demandeReportData.tax_type_2_curr_paid = paymentData.tax_type_2_curr ?? 0;

      demandeReportData.tax_type_3_paid = paymentData.tax_type_3 ?? 0;
      demandeReportData.tax_type_3_prev_paid = paymentData.tax_type_3_prev ?? 0;
      demandeReportData.tax_type_3_curr_paid = paymentData.tax_type_3_curr ?? 0;

      demandeReportData.tax_type_4_paid = paymentData.tax_type_4 ?? 0;
      demandeReportData.tax_type_4_prev_paid = paymentData.tax_type_4_prev ?? 0;
      demandeReportData.tax_type_4_curr_paid = paymentData.tax_type_4_curr ?? 0;

      demandeReportData.tax_type_5_paid = paymentData.tax_type_5 ?? 0;
      demandeReportData.tax_type_5_prev_paid = paymentData.tax_type_5_prev ?? 0;
      demandeReportData.tax_type_5_curr_paid = paymentData.tax_type_5_curr ?? 0;

      demandeReportData.tax_type_6_paid = paymentData.tax_type_6 ?? 0;
      demandeReportData.tax_type_6_prev_paid = paymentData.tax_type_6_prev ?? 0;
      demandeReportData.tax_type_6_curr_paid = paymentData.tax_type_6_curr ?? 0;

      demandeReportData.tax_type_7_paid = paymentData.tax_type_7 ?? 0;
      demandeReportData.tax_type_7_prev_paid = paymentData.tax_type_7_prev ?? 0;
      demandeReportData.tax_type_7_curr_paid = paymentData.tax_type_7_curr ?? 0;

      demandeReportData.tax_type_8_paid = paymentData.tax_type_8 ?? 0;
      demandeReportData.tax_type_8_prev_paid = paymentData.tax_type_8_prev ?? 0;
      demandeReportData.tax_type_8_curr_paid = paymentData.tax_type_8_curr ?? 0;

      demandeReportData.tax_type_9_paid = paymentData.tax_type_9 ?? 0;
      demandeReportData.tax_type_9_prev_paid = paymentData.tax_type_9_prev ?? 0;
      demandeReportData.tax_type_9_curr_paid = paymentData.tax_type_9_curr ?? 0;

      demandeReportData.tax_type_10_paid = paymentData.tax_type_10 ?? 0;
      demandeReportData.tax_type_10_prev_paid =
        paymentData.tax_type_10_prev ?? 0;
      demandeReportData.tax_type_10_curr_paid =
        paymentData.tax_type_10_curr ?? 0;

      demandeReportData.other_tax_sum_tax_paid =
        paymentData.other_tax_sum_tax ?? 0;
      demandeReportData.other_tax_sum_tax_prev_paid =
        paymentData.other_tax_sum_tax_prev ?? 0;
      demandeReportData.other_tax_sum_tax_curr_paid =
        paymentData.other_tax_sum_tax_curr ?? 0;

      demandeReportData.all_property_tax_sum_paid =
        paymentData.all_property_tax_sum_total ?? 0;
      demandeReportData.all_property_tax_sum_prev_paid =
        paymentData.all_property_tax_sum_prev ?? 0;
      demandeReportData.all_property_tax_sum_curr_paid =
        paymentData.all_property_tax_sum_curr ?? 0;

      demandeReportData.total_amount_paid = paymentData.total_tax ?? 0;

      demandeReportData.other_discount = paymentData?.discount_amount ?? 0;
      demandeReportData.property_type_discount =
        paymentData?.property_type_discount ?? 0;

        demandeReportData.ReciptInfo=receipt;

      await queryRunner.manager.save(demandeReportData);

      await queryRunner.commitTransaction();

      // Update paid/non-paid data after successful payment creation
      try {
        await this.paidDataService.updatePropertyPaidData(
          paymentData.property_id,
          paymentData.financial_year
        );
        this.logger.log(`Updated paid/non-paid data for property ${paymentData.property_id} after payment creation`);
      } catch (updateError) {
        this.logger.error(`Failed to update paid/non-paid data for property ${paymentData.property_id}: ${updateError.message}`);
        // Don't throw error here as payment was successful, just log the issue
      }

      return {
        message: 'Payment successfully processed!',
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      return {
        message: 'Payment processing failed!',
        error: error.message,
      };
    } finally {
      await queryRunner.release();
    }
  }

  async getAllPaymentLogsData(params: any) {
    try {
      const { value, searchOn, fy, ...options } = params;
      let data = null;

      data = await this.paymentInfoRepository.getAllPaymentLogsData(
        fy,
        value,
        searchOn,
        options,
      );

      return {
        message: 'Succesfully retrieved',
        data: data,
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async editPaymentLog(id: any, bookData: any) {
    try {
      const { payment_date } = bookData;

      // const paymentData = await this.receiptRepository.getPaymentInfoById(id);
      // const dataDate =await this.paymentInfoRepository.updatePayment()
      const data = await this.receiptRepository.updateReceipt(id, bookData);
      const receipt = await this.receiptRepository.findOne({
        where: { receipt_id: data.data.receipt_id },
        relations: ['paymentInfo'], // Assuming 'payment' is the relation name in the Receipt entity
      });
      const input = { payment_date };

      await this.paymentInfoRepository.updatePayment(
        receipt.paymentInfo.payment_id,
        input,
      );

      // Update paid/non-paid data after payment edit
      try {
        await this.paidDataService.updatePropertyPaidData(
          receipt.property.property_id,
          receipt.financial_year
        );
        this.logger.log(`Updated paid/non-paid data for property ${receipt.property.property_id} after payment edit`);
      } catch (updateError) {
        this.logger.error(`Failed to update paid/non-paid data for property ${receipt.property.property_id}: ${updateError.message}`);
        // Don't throw error here as edit was successful, just log the issue
      }

      return {
        message: 'Updated Sucessfully',
      };
    } catch (e) {
      throw new Error(e);
    }
  }

  async getPaymentMode() {
    try {
      const payments = await this.paymentInfoRepository.findAllPaymentInfo();
      if (!payments || payments.length === 0) {
        return {};
      }

      // Group payments by payment_mode
      const paymentMode = payments.reduce(
        (acc, payment) => {
          acc[payment.payment_mode] = (acc[payment.payment_mode] || 0) + 1;
          acc.total += 1;
          return acc;
        },
        { total: 0 } as Record<string, number> & { total: number },
      );

      return {
        message: 'paymentMode and their respective payments',
        data: paymentMode,
      };
    } catch (error) {
      throw error;
    }
  }

  async deletePayment(id: string) {
    try {
      const receipt = await this.receiptRepository.findReceiptById(id);
      const currentFinancialYear =
        await this.financialYearRepo.getCurrentFinancialYear();
      if (!receipt) {
        throw new NotFoundException('receipt not found');
      }

      // Check if the receipt is the latest one for the property
      if (receipt.property) {
        const allReceiptsForProperty = await this.receiptRepository.find({
          where: { property: { property_id: receipt.property.property_id } },
          order: { receipt_date: 'DESC' },
        });

        if (allReceiptsForProperty && allReceiptsForProperty.length > 1) {
          const latestReceipt = allReceiptsForProperty[0];
          if (latestReceipt.receipt_id !== receipt.receipt_id) {
            throw new ForbiddenException(
              'Cannot delete older receipts. Only the latest receipt can be deleted.',
            );
          }
        }
      }

      if (
        receipt.financial_year !== currentFinancialYear.financial_year_range
      ) {
        throw new ForbiddenException('Cannot delete previous year receipts');
      }
      const paymentInfo = receipt.paymentInfo;
      const bookNumberMaster = receipt.bookNumber;
      const property = receipt.property;
console.log("paymentInfo--->",paymentInfo)
      // Get the paid data to restore penalties if needed
      const paidData = paymentInfo?.paidData;

      // Calculate the sum of all previous tax types
      const totalPreviousAmount = paidData
        ? (paidData.all_property_tax_sum_prev || 0) +
          (paidData.tax_type_1_prev || 0) +
          (paidData.tax_type_2_prev || 0) +
          (paidData.tax_type_3_prev || 0) +
          (paidData.tax_type_4_prev || 0) +
          (paidData.tax_type_5_prev || 0) +
          (paidData.tax_type_6_prev || 0) +
          (paidData.tax_type_7_prev || 0) +
          (paidData.tax_type_8_prev || 0) +
          (paidData.tax_type_9_prev || 0) +
          (paidData.tax_type_10_prev || 0) +
          (paidData.other_tax_sum_tax_prev || 0)
        : 0;

      // Calculate the sum of all current tax types
      const totalCurrentAmount = paidData
        ? (paidData.all_property_tax_sum_curr || 0) +
          (paidData.tax_type_1_curr || 0) +
          (paidData.tax_type_2_curr || 0) +
          (paidData.tax_type_3_curr || 0) +
          (paidData.tax_type_4_curr || 0) +
          (paidData.tax_type_5_curr || 0) +
          (paidData.tax_type_6_curr || 0) +
          (paidData.tax_type_7_curr || 0) +
          (paidData.tax_type_8_curr || 0) +
          (paidData.tax_type_10_curr || 0) +
          (paidData.other_tax_sum_tax_curr || 0)
        : 0;

      // Check if any previous values exist
      const hasPreviousValues = totalPreviousAmount > 0;

      // Restore penalties if this payment included penalty payments or previous values
      if (
        paidData &&
        (paidData.tax_type_9_prev > 0 || hasPreviousValues) &&
        paidData.year_wise_penalty_data
      ) {
        console.log(
          `Restoring previous year penalties for deleted payment. Property ID: ${property.property_id}, Tax_type_9_prev: ${paidData.tax_type_9_prev}, Total previous amount: ${totalPreviousAmount}`,
        );

        const yearWisePenaltyData = paidData.year_wise_penalty_data;

        // Restore each year's penalties
        for (const [year, data] of Object.entries(
          yearWisePenaltyData as Record<string, any>,
        )) {
          // Find the penalty for this year
          const penalties =
            await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
              property.property_id,
              year,
            );
            console.log("penalties",penalties)

          if (penalties && penalties.length > 0) {
            const penalty = penalties[0];
            const originalData = data.original;

            // Restore the original actual_value and tax_percentage if available
            const updateData: any = {
              actual_value: originalData.actual_value,
            };

            if (originalData.tax_percentage) {
              updateData.tax_percentage = originalData.tax_percentage;
            }

            // Restore the original values
            // Don't calculate penalty_value here as it should only be calculated by cron job
            await this.penaltyFeeYearWiseRepository.updatePenaltyFee(
              penalty.penalty_fee_id,
              updateData,
              true, // Don't calculate penalty value during payment deletion
            );

            console.log(
              `Restored penalty for year ${year}. Original actual_value: ${originalData.actual_value}`,
            );
          } else {
            // If the penalty record doesn't exist anymore, create a new one with the original values
            const taxPercentage = data.original.tax_percentage || 2; // Default to 2% if not available

            await this.penaltyFeeYearWiseRepository.savePenaltyFee({
              property: property.property_id,
              financial_year: year,
              actual_value: data.original.actual_value,
              tax_percentage: taxPercentage,
              // penalty_value will be calculated automatically in the repository
            });

            console.log(
              `Created new penalty record for year ${year}. Actual_value: ${data.original.actual_value}, Tax percentage: ${taxPercentage}`,
            );
          }
        }
      }

      // Restore current year penalties if this payment included current year penalty payments
      console.log("paidData",paidData)
      if (
        paidData &&
        paidData.tax_type_9_curr > 0 &&
        paidData.year_wise_penalty_data
      ) {
        console.log(
          `Restoring current year penalties for deleted payment. Property ID: ${property.property_id}, Tax_type_9_curr: ${paidData.tax_type_9_curr}`,
        );

        const yearWisePenaltyData = paidData.year_wise_penalty_data;
            console.log("penalties11")

        // Since there will be only one penalty for current year, process it directly
        for (const [year, data] of Object.entries(
          yearWisePenaltyData as Record<string, any>,
        )) {
          // Check if this is the current year
                      console.log("penalties2 year",currentFinancialYear,year)

          if (year === currentFinancialYear.financial_year_range) {
            // Find the penalty for current year
            const penalties =
              await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
                property.property_id,
                year,
              );

                                    console.log("penalties2 penalties",penalties);

            if (penalties && penalties.length > 0) {
              const penalty = penalties[0]; // Get the single current year penalty
              const originalData = data.original;

              // Restore the original actual_value and tax_percentage
              const updateData: any = {
                actual_value: originalData.actual_value,
              };

              if (originalData.tax_percentage) {
                updateData.tax_percentage = originalData.tax_percentage;
              }

              // Restore the original values
              // Don't calculate penalty_value here as it should only be calculated by cron job

              await this.penaltyFeeYearWiseRepository.updatePenaltyFee(
                penalty.penalty_fee_id,
                updateData,
                true, // Don't calculate penalty value during payment deletion
              );

              console.log(
                `Restored current year penalty. Original actual_value: ${originalData.actual_value}`,
              );
            } else {
              // If the penalty record doesn't exist anymore, create a new one with the original values
              const taxPercentage = data.original.tax_percentage || 2; // Default to 2% if not available

              await this.penaltyFeeYearWiseRepository.savePenaltyFee({
                property: property.property_id,
                financial_year: year,
                actual_value: data.original.actual_value,
                tax_percentage: taxPercentage,
                // penalty_value will be calculated automatically in the repository
              });

              console.log(
                `Created new current year penalty record. Actual_value: ${data.original.actual_value}, Tax percentage: ${taxPercentage}`,
              );
            }
            break; // Exit loop since we found and processed the current year penalty
          }
        }
      }

      const backupEntity = this.backupPaymentDetailsRepo.create({
        payment_details: receipt,
        property_id: property ? property.property_id : null,
        amount: paymentInfo ? paymentInfo.amount : 0,
        book_number_id: bookNumberMaster ? bookNumberMaster.id : null,
        receipt_number: receipt.book_receipt_number || null,
        payment_date: paymentInfo ? paymentInfo.payment_date : null,

      });
      await this.backupPaymentDetailsRepo.save(backupEntity);

      if (bookNumberMaster) {
        bookNumberMaster.receiptsInUse = bookNumberMaster.receiptsInUse.filter(
          (r) => r !== Number(receipt.book_receipt_number),
        );
        bookNumberMaster.availableReceipts = [
          ...new Set([
            ...bookNumberMaster.availableReceipts,
            Number(receipt.book_receipt_number),
          ]),
        ];
        await this.bookRepo.saveBookEntity(bookNumberMaster);
      }
  await this.dataSource.transaction(async (transactionalEntityManager) => {
      // Delete the ReceiptEntity  
      await transactionalEntityManager.delete(ReceiptEntity, receipt.receipt_id);

      // The cascading delete should automatically handle the deletion of the related DemandReportData
      // Delete the PaymentInfoEntity
      await transactionalEntityManager.delete(PaymentInfoEntity, paymentInfo.payment_id);
    });

      // Update paid/non-paid data after successful payment deletion
      try {
        await this.paidDataService.updatePropertyPaidData(
          property.property_id,
          receipt.financial_year
        );
        this.logger.log(`Updated paid/non-paid data for property ${property.property_id} after payment deletion`);
      } catch (updateError) {
        this.logger.error(`Failed to update paid/non-paid data for property ${property.property_id}: ${updateError.message}`);
        // Don't throw error here as deletion was successful, just log the issue
      }

      return {
        message: 'Deleted Payment',
        data: backupEntity,
      };
    } catch (e) {
      throw e;
    }
  }
  async updateTaxPayer() {
    // Step 1: Get all properties grouped with owners and payments
    const groupedProperties: PropertyGroupDto[] =
      await this.paymentInfoRepository.findAllWithoutTaxPayer();

    for (const property of groupedProperties) {
      const { owner, payments } = property;
      if (owner && owner.owner_name) {
        const ownerName = owner.owner_name;

        await Promise.all(
          payments.map((payment) =>
            this.paymentInfoRepository.update(payment.payment_id, {
              tax_payer_name: ownerName,
            }),
          ),
        );
      }
    }

    return { message: 'Tax payer names updated successfully' };
  }
}
