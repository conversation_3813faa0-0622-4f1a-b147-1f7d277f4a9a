import { MigrationInterface, QueryRunner } from "typeorm";

export class CreatedBackupPropertyUsageDetailsEntity1741009933113 implements MigrationInterface {
    name = 'CreatedBackupPropertyUsageDetailsEntity1741009933113'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_df5e676c36082c5c46778bc844c"`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0"`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_1790af28e759558cd2116e9d953"`);
        // await queryRunner.query(`ALTER TABLE "tax_property" DROP CONSTRAINT "FK_d7a73eaab514342e3364090ccba"`);
        // await queryRunner.query(`ALTER TABLE "payment_info" DROP CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9"`);
        // await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_ed2960763a10694e2d01f2f041c"`);
        // await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_63859fe9b799dc463dd2e14ad72"`);
        // await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_2b3f8b3d5bea0587920b3020eac"`);
        await queryRunner.query(`CREATE TABLE "backup_property_usage_details" ("id" SERIAL NOT NULL, "property_id" character varying NOT NULL, "property_usage_details_id" character varying NOT NULL, "property_usage_details" jsonb, CONSTRAINT "UQ_777f6b2a449cadaa51e8dc3fb12" UNIQUE ("property_usage_details_id"), CONSTRAINT "PK_d877942156b2bbfebca8e1d8ec6" PRIMARY KEY ("id"))`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c" UNIQUE ("billNo")`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP DEFAULT`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(50) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" DROP NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "property_owner_details" ADD CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_df5e676c36082c5c46778bc844c" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD CONSTRAINT "FK_48589f448b7fc0e33f3ca4cb356" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD CONSTRAINT "FK_10403a07dc38f673bf18be42e3d" FOREIGN KEY ("property_usage_details_id") REFERENCES "property_usage_details"("property_usage_details_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "milkatKar" ADD CONSTRAINT "FK_3e1f3fe6a693b75466f63a957a4" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_1790af28e759558cd2116e9d953" FOREIGN KEY ("property_usage_details_id") REFERENCES "property_usage_details"("property_usage_details_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "tax_property" ADD CONSTRAINT "FK_d7a73eaab514342e3364090ccba" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "warshik_kar" ADD CONSTRAINT "FK_dc23330aada107590eed796d7c0" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "FK_df738d1108e8291026728e28c55" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "payment_info" ADD CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_2b3f8b3d5bea0587920b3020eac" FOREIGN KEY ("street_id") REFERENCES "street_master"("street_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_63859fe9b799dc463dd2e14ad72" FOREIGN KEY ("ward_id") REFERENCES "ward_master"("ward_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_ed2960763a10694e2d01f2f041c" FOREIGN KEY ("zone_id") REFERENCES "zone_master"("zone_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "warshikKarTax" ADD CONSTRAINT "FK_782f8b60c2e42b03b9dc97ca1de" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "warshikKarTax" ADD CONSTRAINT "FK_9568ec13126620a0b3dbc231560" FOREIGN KEY ("property_usage_details_id") REFERENCES "property_usage_details"("property_usage_details_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "warshikKarTax" DROP CONSTRAINT "FK_9568ec13126620a0b3dbc231560"`);
        // await queryRunner.query(`ALTER TABLE "warshikKarTax" DROP CONSTRAINT "FK_782f8b60c2e42b03b9dc97ca1de"`);
        // await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_ed2960763a10694e2d01f2f041c"`);
        // await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_63859fe9b799dc463dd2e14ad72"`);
        // await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_2b3f8b3d5bea0587920b3020eac"`);
        // await queryRunner.query(`ALTER TABLE "payment_info" DROP CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9"`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "FK_df738d1108e8291026728e28c55"`);
        // await queryRunner.query(`ALTER TABLE "warshik_kar" DROP CONSTRAINT "FK_dc23330aada107590eed796d7c0"`);
        // await queryRunner.query(`ALTER TABLE "tax_property" DROP CONSTRAINT "FK_d7a73eaab514342e3364090ccba"`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_1790af28e759558cd2116e9d953"`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0"`);
        // await queryRunner.query(`ALTER TABLE "milkatKar" DROP CONSTRAINT "FK_3e1f3fe6a693b75466f63a957a4"`);
        // await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP CONSTRAINT "FK_10403a07dc38f673bf18be42e3d"`);
        // await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP CONSTRAINT "FK_48589f448b7fc0e33f3ca4cb356"`);
        // await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_df5e676c36082c5c46778bc844c"`);
        // await queryRunner.query(`ALTER TABLE "property_owner_details" DROP CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(100) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET DEFAULT now()`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c"`);
        await queryRunner.query(`DROP TABLE "backup_property_usage_details"`);
        // await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_2b3f8b3d5bea0587920b3020eac" FOREIGN KEY ("street_id") REFERENCES "street_master"("street_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_63859fe9b799dc463dd2e14ad72" FOREIGN KEY ("ward_id") REFERENCES "ward_master"("ward_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_ed2960763a10694e2d01f2f041c" FOREIGN KEY ("zone_id") REFERENCES "zone_master"("zone_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "payment_info" ADD CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "tax_property" ADD CONSTRAINT "FK_d7a73eaab514342e3364090ccba" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_1790af28e759558cd2116e9d953" FOREIGN KEY ("property_usage_details_id") REFERENCES "property_usage_details"("property_usage_details_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_df5e676c36082c5c46778bc844c" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
