import { Controller, Get, Post, Put, Delete, Query, Body } from '@nestjs/common';
import { Master_weightingService } from './master-weighting.service';
import { Master_weighting_rateEntity } from 'libs/database/entities';
import { CreateWeightingDto } from './dto/create-weighting.dto';
import { UpdateWeightingDto } from './dto/update-weighting.dto';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@Controller('master-weighting')
export class Master_weightingController {
  constructor(private readonly weightingService: Master_weightingService) {}

  
  @Form('Weighting Rate')
  @Permissions('can_write')
  @Post('create')
  async create(
    @Body() data: CreateWeightingDto
  ): Promise<{ message: string; data: Master_weighting_rateEntity[] }> {
    const savedWeightingRate = await this.weightingService.create(data);
    return {
      message: 'Weighting rate created successfully',
      data: savedWeightingRate.data,
    };
  }

  
  @Form('Weighting Rate')
  @Permissions('can_read')
  @Get()
  async findAll(): Promise<{ message: string; data: Master_weighting_rateEntity[] }> {
    const weightingRates = await this.weightingService.findAll();
    return {
      message: 'All weighting rates retrieved successfully',
      data: weightingRates.data,
    };
  }

  // @Get('findOne')
  // async findOne(
  //   @Query('id') id: string
  // ): Promise<{ message: string; data: Master_weighting_rateEntity }> {
  //   const weightingRate = await this.weightingService.findOne(id);
  //   return {
  //     message: 'Weighting rate retrieved successfully',
  //     data: weightingRate,
  //   };
  // }

  
  @Form('Weighting Rate')
  @Permissions('can_update')
  @Put('update')
  async update(
    @Query('id') id: string,
    @Body() data: UpdateWeightingDto
  ): Promise<{ message: string; data: Master_weighting_rateEntity }> {
    const updatedWeightingRate = await this.weightingService.update(id, data);
    return {
      message: 'Weighting rate updated successfully',
      data: updatedWeightingRate.data,
    };
  }

  
  @Form('Weighting Rate')
  @Permissions('can_delete')
  @Delete('delete')
  async delete(@Query('id') id: string): Promise<{ message: string }> {
    await this.weightingService.delete(id);
    return {
      message: 'Weighting rate deleted successfully',
    };
  }
}
