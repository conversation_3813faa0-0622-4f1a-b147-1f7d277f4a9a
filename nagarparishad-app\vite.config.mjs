import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import svgr from 'vite-plugin-svgr';
import faviconPlugin from './favicon.config';

export default defineConfig({
  plugins: [react(), svgr(), faviconPlugin()],
  resolve: {
    alias: {
      "@": "/src",
    },
  },
  esbuild: {
    // Ensure that JSX files are handled correctly
    // jsxInject: `import React from 'react'`,
  },
});
