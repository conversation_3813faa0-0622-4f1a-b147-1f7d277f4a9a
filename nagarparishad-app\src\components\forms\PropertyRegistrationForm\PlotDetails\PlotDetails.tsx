import React, { useState, useContext } from "react";
import { Input } from "@/components/ui/input";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { z, object } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  PropertyDetailsInterface,
  ReactselectInterface,
} from "@/model/global-master";
import { PropertyRegistrationInterface } from "@/model/propertyregistration-master";
import { PropertyContext } from "@/context/PropertyContext";
import { toast } from "@/components/ui/use-toast";
import { ConstructionClassObjectInterface } from "@/model/construction-master";
import { useConstructionClassController } from "@/controller/master/ConstructionController";
import AsyncSelect from "@/components/ui/react-select";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { Loader2, UploadCloudIcon } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, ChevronsLeft } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { usePropertyRegistrationController } from "@/controller/property-registration/PropertyRegistrationController";

const schema = object({
  flat_status: z.number(),
  floor: z.string(),
  flat_no: z.string(),
  occupancy_type: z.string(),
  constructionClass: z.string(),
  tax_start_date: z.date().nullable(),
  room_detail_info_flag: z.boolean(), // Assuming roomDetailsInfoFlag can be any type
  room_detail: z.string(),
  manual_area_flag: z.boolean(), // Assuming manualAreaFlag can be any type
  construction_area: z.number(),
  carpet_area: z.number(),
  exempted_area: z.number(),
  assessable_area: z.number(),
  land_cost: z.number(),
  standard_rate: z.number(),
  annual_rent: z.number(),
  capital_value: z.number(),
  remark: z.string(),
  property_photographs: z.array(z.string()).optional(),
});

const PlotDetails: React.FC<PropertyDetailsInterface> = ({
  handleNextStep,
  handlePreviousStep,
}) => {
  const { propertyInformation, setPropertyInformation, propertyNumber } =
    useContext(PropertyContext);
  const { createProperty, updateProperty } =
    usePropertyRegistrationController();
  const { t } = useTranslation();
  const [loader, setLoader] = useState(false);
  const navigate = useNavigate();
  const dynamicValues = {
    name: t("propertyText"),
  };
  const plotDetails = propertyInformation?.PlotDetailsForm;
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      flat_status: plotDetails?.flat_status || 0,
      floor: plotDetails?.floor || "",
      flat_no: plotDetails?.flat_no || "",
      occupancy_type: plotDetails?.occupancy_type || "",
      constructionClass: plotDetails?.constructionClass || "",
      tax_start_date: plotDetails?.tax_start_date
        ? new Date(plotDetails?.tax_start_date)
        : null,
      room_detail_info_flag: plotDetails?.room_detail_info_flag || false,
      room_detail: plotDetails?.room_detail || "",

      manual_area_flag: plotDetails?.manual_area_flag || false,
      construction_area: plotDetails?.construction_area || 0,
      carpet_area: plotDetails?.carpet_area || 0,

      exempted_area: plotDetails?.exempted_area || 0,

      assessable_area: plotDetails?.assessable_area || 0,
      land_cost: plotDetails?.land_cost || 0,
      standard_rate: plotDetails?.standard_rate || 0,
      annual_rent: plotDetails?.annual_rent || 0,
      capital_value: plotDetails?.capital_value || 0,
      remark: plotDetails?.remark || "",
      property_photographs: plotDetails?.property_photographs || [],
    },
  });

  const {
    formState: { errors },
  } = form;

  const onSubmit = (data: any) => {
    setPropertyInformation((prevState: PropertyRegistrationInterface) => ({
      ...prevState,
      PlotDetailsForm: data,
    }));
    const requestData = {
      ...propertyInformation.PropertyLocationDetails,
      ...propertyInformation.AssessmentDetailsForm,
      ...propertyInformation.PropertyAssessmentDetailsForm,
      ...data,
      tax_start_date: data?.tax_start_date ? data?.tax_start_date : null,
    };

    if (propertyNumber !== undefined && propertyNumber !== "") {
      setLoader(true);

      updateProperty(
        { propertyId: propertyNumber, propertyData: requestData },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            navigate("/property");
            setLoader(false);
          },
          onError: (error: any) => {
            toast({
              title: error.message,
              variant: "destructive",
            });
            setLoader(false);
          },
        },
      );
    } else {
      setLoader(true);
      createProperty(requestData, {
        onSuccess: () => {
          toast({
            title: t("api.formupdate", dynamicValues),
            variant: "success",
          });
          navigate("/property");
          setLoader(false);
        },
        onError: (error: any) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });

      // PropertyApi.AddPropertyRegistration(
      //   requestData,
      //   (response: { status: boolean; data: any }) => {
      //     if (response.status && response.data.statusCode === 201) {
      //       setLoader(false);
      //       toast({
      //         title: response.data.message,
      //         variant: "success",
      //       });
      //       navigate("/property");
      //     } else {
      //       setLoader(false);
      //       toast({
      //         title: response.data.message,
      //         variant: "destructive",
      //       });
      //     }
      //   }
      // );
    }
  };

  const { constructionClassList } = useConstructionClassController(); // Assuming it returns an array of ConstructionClassSelect

  const constructionClassOptions: ReactselectInterface[] =
    constructionClassList?.map(
      (constructionClass: ConstructionClassObjectInterface) => ({
        value: constructionClass.constructionClass_id,
        label: constructionClass.constructionClassName,
      }),
    ) || [];

  const loadConstructionOptions = (
    inputValue: string,
    callback: (options: ReactselectInterface[]) => void,
  ) => {
    setTimeout(() => {
      callback(
        constructionClassOptions.filter((option) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase()),
        ),
      );
    }, 1000);
  };

  return (
    <>
      <div className="h-fit">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 gap-x-5">
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="flat_no"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.flatNumber")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.flatNumber")}
                          {...field}
                        />
                      </FormControl>
                      {errors.flat_no && (
                        <FormMessage className="ml-1">
                          Enter Flat Number
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="floor"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.floorNumber")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="string"
                          placeholder={t("plotDetailsForm.floorNumber")}
                          {...field}
                        />
                      </FormControl>
                      {errors.floor && (
                        <FormMessage className="ml-1">Enter Floor</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="flat_status"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.flatStatus")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.flatStatus")}
                          {...field}
                          onChange={(e) => {
                            // Update the field value with a number
                            field.onChange(parseInt(e.target.value, 10));
                          }}
                        />
                      </FormControl>
                      {errors.flat_status && (
                        <FormMessage className="ml-1">
                          Enter Flat Status
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="occupancy_type"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("plotDetailsForm.occupancyType")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="string"
                          placeholder={t("plotDetailsForm.occupancyType")}
                          {...field}
                        />
                      </FormControl>
                      {errors.occupancy_type && (
                        <FormMessage className="ml-1">
                          Enter Occupancy Type
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="constructionClass"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>
                        {t("plotDetailsForm.constructionClass")}
                      </FormLabel>
                      <AsyncSelect
                        placeholder={t("plotDetailsForm.constructionClass")}
                        loadOptions={loadConstructionOptions}
                        defaultOptions={constructionClassOptions}
                        value={
                          constructionClassOptions &&
                          constructionClassOptions?.find(
                            (option: any) => option?.value === field?.value,
                          )
                        }
                        onChange={(selectedOption: any) =>
                          field.onChange(selectedOption?.value)
                        } // Use selectedOption.value
                        colourOptions={constructionClassOptions}
                      />

                      {errors.constructionClass && (
                        <FormMessage className="ml-1">
                          {t("errors.requiredField")}
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="tax_start_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="py-1">
                        {t("plotDetailsForm.taxStartDate")}
                      </FormLabel>
                      <FormControl>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full h-11 mt-1 p-3 text-left font-normal",
                                !field.value && "text-muted-foreground",
                              )}
                              onClick={() => field.onChange(null)}
                            >
                              {field.value && !isNaN(field.value.getTime()) ? (
                                format(field.value, "MM-dd-yyyy")
                              ) : (
                                <span>{t("plotDetailsForm.taxStartDate")}</span>
                              )}

                              <CalendarIcon className="ml-auto  h-4 w-4" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0" align="start">
                            <Calendar
                              className="dashboard-calender w-full "
                              mode="single"
                              selected={
                                field.value ? new Date(field.value) : null
                              }
                              onSelect={(date) => field.onChange(date)}
                              disabled={(date) =>
                                date > new Date() ||
                                date < new Date("1900-01-01")
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </FormControl>

                      {errors.tax_start_date && (
                        <FormMessage className="ml-1">
                          Enter Construction Start Date
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid flex justify-between pr-2">
                <FormField
                  control={form.control}
                  name="room_detail_info_flag"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("plotDetailsForm.roomDetailsInfo")}
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={(value) =>
                            field.onChange(value === "true")
                          }
                          defaultValue={field.value.toString()}
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="true" />
                            </FormControl>
                            <FormLabel className="font-normal">True</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="false" />
                            </FormControl>
                            <FormLabel className="font-normal">False</FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      {errors.room_detail_info_flag && (
                        <FormMessage className="ml-1">
                          Enter First Assessment
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="manual_area_flag"
                  render={({ field }) => (
                    <FormItem className="">
                      <FormLabel>
                        {t("plotDetailsForm.manualAreaFlag")}
                      </FormLabel>

                      <FormControl>
                        <RadioGroup
                          onValueChange={(value) =>
                            field.onChange(value === "true")
                          }
                          defaultValue={field.value.toString()}
                        >
                          <FormItem className="flex items-center space-x-2">
                            <FormControl>
                              <RadioGroupItem value="true" />
                            </FormControl>
                            <FormLabel className="font-normal">True</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2">
                            <FormControl>
                              <RadioGroupItem value="false" />
                            </FormControl>
                            <FormLabel className="font-normal">False</FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      {errors.manual_area_flag && (
                        <FormMessage className="ml-1">
                          Enter First Assessment
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="room_detail"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.roomDetails")}</FormLabel>
                      <FormControl>
                        <Textarea
                          className="mt-1 block w-full"
                          placeholder={t("plotDetailsForm.roomDetails")}
                          {...field}
                        />
                      </FormControl>
                      {errors.room_detail && (
                        <FormMessage className="ml-1">
                          Enter Room Details
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="standard_rate"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.standardRate")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.standardRate")}
                          value={field.value || ""}
                          onChange={(e) =>
                            field.onChange(e.target.valueAsNumber)
                          }
                        />
                      </FormControl>
                      {errors.standard_rate && (
                        <FormMessage className="ml-1">
                          Enter Standard Rate
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="construction_area"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("plotDetailsForm.constructionArea")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.constructionArea")}
                          value={field.value || ""}
                          onChange={(e) =>
                            field.onChange(e.target.valueAsNumber)
                          }
                        />
                      </FormControl>
                      {errors.construction_area && (
                        <FormMessage className="ml-1">
                          Enter Construction Area
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="carpet_area"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.carpetArea")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.carpetArea")}
                          value={field.value || ""}
                          onChange={(e) =>
                            field.onChange(e.target.valueAsNumber)
                          }
                        />
                      </FormControl>
                      {errors.carpet_area && (
                        <FormMessage className="ml-1">
                          Enter Carpet Area
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="exempted_area"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.exemptedArea")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.exemptedArea")}
                          value={field.value || ""}
                          onChange={(e) =>
                            field.onChange(e.target.valueAsNumber)
                          }
                        />
                      </FormControl>
                      {errors.exempted_area && (
                        <FormMessage className="ml-1">
                          Enter Exempted Area
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="assessable_area"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("plotDetailsForm.assessableArea")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.assessableArea")}
                          value={field.value || ""}
                          onChange={(e) =>
                            field.onChange(e.target.valueAsNumber)
                          }
                        />
                      </FormControl>
                      {errors.assessable_area && (
                        <FormMessage className="ml-1">
                          Enter Assessable Area
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="land_cost"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.landCost")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.landCost")}
                          value={field.value || ""}
                          onChange={(e) =>
                            field.onChange(e.target.valueAsNumber)
                          }
                        />
                      </FormControl>
                      {errors.land_cost && (
                        <FormMessage className="ml-1">
                          Enter Land Cost
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="annual_rent"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.annualRent")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.annualRent")}
                          value={field.value || ""}
                          onChange={(e) =>
                            field.onChange(e.target.valueAsNumber)
                          }
                        />
                      </FormControl>
                      {errors.annual_rent && (
                        <FormMessage className="ml-1">
                          Enter Annual Rent
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="remark"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.remark")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          placeholder={t("plotDetailsForm.remark")}
                          {...field}
                        />
                      </FormControl>
                      {errors.remark && (
                        <FormMessage className="ml-1">Enter Remark</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="capital_value"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.capitalValue")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.capitalValue")}
                          value={field.value || ""}
                          onChange={(e) =>
                            field.onChange(e.target.valueAsNumber)
                          }
                        />
                      </FormControl>
                      {errors.capital_value && (
                        <FormMessage className="ml-1">
                          Enter Capital Value
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormLabel>
                  {" "}
                  {t("plotDetailsForm.PropertyPhotographs")}
                </FormLabel>
                <div className="relative ">
                  <Input
                    type="file"
                    placeholder={t("plotDetailsForm.PropertyPhotographs")}
                    multiple
                    className="text-transparent mt-2"
                  />
                  <Button
                    className="h-[44px] mt-[-2px] px-4 py-5 rounded-none !absolute !top-[6%] right-0"
                    type="button"
                  >
                    <UploadCloudIcon className="w-6 h-6 mr-2" />
                    {t("propertyLocationDetailsForm.uploadFile")}
                  </Button>
                  {/* <UploadCloudIcon className="absolute top-[33%] right-5" /> */}
                  {/* <img className="absolute top-[33%] right-5" src={UploadICon} alt="" /> */}
                </div>
                {/* <FormField
                  control={form.control}
                  name="property_photographs"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("plotDetailsForm.PropertyPhotographs")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="text"
                          placeholder={t("plotDetailsForm.PropertyPhotographs")}
                          {...field}
                        />
                      </FormControl>
                      {errors.property_photographs && (
                        <FormMessage className="ml-1">
                          Property Image
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                /> */}
              </div>
            </div>

            {/* <div className="flex flex-col sm:flex-row sm:gap-6 gap-2 mb-2">
             
            </div> */}
            <div className="w-full text-end flex justify-end items-center">
              <>
                <Button
                  variant="outline"
                  className="mr-4 border-BlueText text-BlueText "
                  onClick={handlePreviousStep}
                >
                  {<ChevronsLeft className="w-5 h-5 font-bold " />}{" "}
                </Button>
                {loader ? (
                  <Button disabled>
                    <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                    {t("pleaseWait")}
                  </Button>
                ) : (
                  <Button type={"submit"}>
                    {propertyNumber !== undefined && propertyNumber !== ""
                      ? t("property.UpdateBtn")
                      : t("property.AddBtn")}
                  </Button>
                )}
              </>

              {}
            </div>
          </form>
        </Form>
      </div>
    </>
  );
};

export default PlotDetails;
