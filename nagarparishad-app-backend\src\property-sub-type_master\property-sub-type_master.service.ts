import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import {
  CreatePropertySubTypeMasterDto,
  PropertySubTypeMasterDto,
  PropertypeIdMasterDto,
  UpdatePropertySubTypeMasterDto,
} from './dto/property-sub-type_master.dto';
import {
  PropertySubTypeMasterRepository,
  PropertyTypeMasterRepository,
} from 'libs/database/repositories';

@Injectable()
export class PropertySubTypeMasterService {
  constructor(
    private readonly propertySubTypeMasterRepository: PropertySubTypeMasterRepository,
    private readonly propertyTypeMasterRepository: PropertyTypeMasterRepository,
  ) {}
  async create(createPropertySubTypeMasterDto: CreatePropertySubTypeMasterDto) {
    try {
      const { propertyType } = createPropertySubTypeMasterDto;
      //check if exists
      const checkPropertyType =
        await this.propertyTypeMasterRepository.findById(propertyType);

      if (!checkPropertyType) {
        throw new NotFoundException('Property Type Not Found');
      }
      const saveData = await this.propertySubTypeMasterRepository.saveData(
        createPropertySubTypeMasterDto,
      );

      return {
        message: 'Data Saved SuccessFully',
        data: saveData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllData =
        await this.propertySubTypeMasterRepository.findAllData();

      if (!getAllData) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(propertySubType: PropertySubTypeMasterDto) {
    try {
      const { propertySub_id } = propertySubType;
      const checkData =
        await this.propertySubTypeMasterRepository.findById(propertySub_id);

      if (!checkData) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Data Found Success',
        data: checkData,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    propertySubType: PropertySubTypeMasterDto,
    updatePropertySubTypeMasterDto: UpdatePropertySubTypeMasterDto,
  ) {
    try {
      const { propertySub_id } = propertySubType;
      const checkData =
        await this.propertySubTypeMasterRepository.findById(propertySub_id);

      if (!checkData) {
        throw new NotFoundException('Data Not Found');
      }

      const updateData = await this.propertySubTypeMasterRepository.updateData(
        propertySub_id,
        updatePropertySubTypeMasterDto,
      );

      if (updateData.affected === 0) {
        throw new NotAcceptableException('Failed to update ');
      }
      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(propertySubType: PropertySubTypeMasterDto) {
    try {
      const { propertySub_id } = propertySubType;
      const checkData =
        await this.propertySubTypeMasterRepository.findById(propertySub_id);

      if (!checkData) {
        throw new NotFoundException('Data Not Found');
      }
      const deleteData =
        await this.propertySubTypeMasterRepository.deleteData(propertySub_id);

      if (deleteData.affected === 0) {
        throw new NotAcceptableException('Failed To Delete data');
      }

      return {
        message: 'Data Deleted SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }

  async getByPropertyType(propertypeIdMaster: PropertypeIdMasterDto) {
    try {
      const { propertyType } = propertypeIdMaster;

      const checkPropertyType =
        await this.propertyTypeMasterRepository.findById(propertyType);

      if (!checkPropertyType) {
        throw new NotFoundException('Property Type Not Found');
      }

      const getData =
        await this.propertySubTypeMasterRepository.findData(propertyType);

      if (!getData || getData.length === 0) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getData,
      };
    } catch (error) {
      throw error;
    }
  }
}
