export interface WardObjectInterface {
  id: string;
  ward_name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface WardMasterApiResponse {
  statusCode?: number;
  message: string;
  data?: WardObjectInterface[] | null;
}

export interface WardObject {
  wardId: string;
  ward_name: string;
}
export interface WardCreateObject {
  ward_name: string;
}
export interface WardCreateResObject {
  statusCode: number;
  message: string;
}
export interface WardUpdateObject {
  ward_name: string;
  ward_id: string;
}
export interface WardUpdateApiResponse {
  statusCode: number;
  message: string;
}
