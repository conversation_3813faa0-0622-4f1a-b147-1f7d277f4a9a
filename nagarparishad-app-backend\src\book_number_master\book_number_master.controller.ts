import { 
    <PERSON>, 
    Post, 
    Param, 
    ParseIntPipe, 
    Get, 
    Patch, 
    Body, 
    Query,
    Delete
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BookNumberMasterService } from './book_number_master.service';
import { ReceiptStatus } from 'libs/database/repositories/book_numberMaster.repository';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@ApiTags('Book Number Master')
@Controller('book-number')
export class BookNumberMasterController {
    constructor(private readonly bookNumberMasterService: BookNumberMasterService) {}

    
    @Form('Book Master')
    @Permissions('can_read')
    @ApiOperation({ summary: 'Check if a book number exists' })
    @ApiResponse({ status: 200, description: 'Returns book existence status' })
    @Get(':bookNumber/exists')
    async checkBookExistence(@Param('bookNumber', ParseIntPipe) bookNumber: number) {
        const exists = await this.bookNumberMasterService.bookExist(bookNumber);
        return { message: exists };
    }

    
    @Form('Book Master')
    @Permissions('can_read')
    @ApiOperation({ summary: 'Get all' })
    @ApiResponse({ status: 200, description: 'Returns book existence status' })
    @Get('getAll')
    async getAll() {
        const exists = await this.bookNumberMasterService.getAll();
        return exists;
    }

    
    @Form('Book Master')
    @Permissions('can_write')
    @ApiOperation({ summary: 'Create a new book number' })
    @ApiResponse({ status: 201, description: 'Successfully created book number' })
    @Post(':bookNumber')
    async createBookNumber(@Param('bookNumber', ParseIntPipe) bookNumber: number) {
        return await this.bookNumberMasterService.createBookNumber(bookNumber);
    }

    
    @Form('Book Master')
    @Permissions('can_read')
    @ApiOperation({ summary: 'Get available receipts for a book number' })
    @ApiResponse({ status: 200, description: 'Returns available receipts' })
    @Get(':bookNumber/available-receipts')
    async getAvailableReceipts(@Param('bookNumber', ParseIntPipe) bookNumber: number) {
        return await this.bookNumberMasterService.getAvailableReceipts(bookNumber);
    }

    
    @Form('Book Master')
    @Permissions('can_read')
    @ApiOperation({ summary: 'Get receipts in use for a book number' })
    @ApiResponse({ status: 200, description: 'Returns receipts in use' })
    @Get(':bookNumber/receipts-in-use')
    async getReceiptsInUse(@Param('bookNumber', ParseIntPipe) bookNumber: number) {
        return await this.bookNumberMasterService.getReceiptsInUse(bookNumber);
    }

    
    @Form('Book Master')
    @Permissions('can_update')
    @ApiOperation({ summary: 'Update the status of a receipt' })
    @ApiResponse({ status: 200, description: 'Successfully updated receipt status' })
    @ApiResponse({ status: 400, description: 'Invalid request data' })
    @Patch('/update-receipt/')
    async updateReceiptStatus(
        @Body('receipt-number', ParseIntPipe) receiptNumber: number,
        @Body('status') status: ReceiptStatus
    ) {
        await this.bookNumberMasterService.updateReceiptStatus(receiptNumber, status);
        return { message: `Receipt ${receiptNumber} updated to ${status}` };
    }

    
    @Form('Book Master')
    @Permissions('can_delete')
    @ApiOperation({ summary: 'Delete a book number by ID' })
    @ApiResponse({ status: 200, description: 'Successfully deleted book number' })
    @ApiResponse({ status: 404, description: 'Book number not found' })
    @Delete('delete')
    async deleteBookNumber(@Query('id', ParseIntPipe) id: number) {
        return await this.bookNumberMasterService.deleteBookNumber(id);
    }
}
