import axios from "axios";

const baseURL = import.meta.env.VITE_APP_BASE_URL;

const VITE_BASE_URL = baseURL;

class PublicApi {
    static checkProperty = async (inputValue: string) => {
        // Trim the input value to remove any leading or trailing whitespace
        const trimmedInputValue = inputValue.trim();
      
        // Determine if the input value is a mobile number
        const isMobileNumber = /^\d+$/.test(trimmedInputValue);
      
        // Base URL for the API
        const url = `${VITE_BASE_URL}/v1/user-property/checkProperty`;
      
        try {
          const response = await axios.get(url, {
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
            },
            params: {
                propertyNumber: isMobileNumber ? undefined : trimmedInputValue,
                mobile_number: isMobileNumber ? trimmedInputValue : undefined,
              },
     
          });
      
          return { status: true, data: response.data };
        } catch (err: any) {
          return { status: false, data: err };
        }
      };

      static sendOtp = async (inputValue: string)=> {
        // Determine whether the input is a mobile number or property number
        const requestData = {
          mobile_number: /^\d+$/.test(inputValue) ? inputValue : undefined, // Check if input is a number
          propertyNumber: /^\d+$/.test(inputValue) ? undefined : inputValue // Assume it's a property number if not a number
        };
    
        try {
          const response = await axios.post(`${VITE_BASE_URL}/v1/user-property/search`, requestData, {
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
            },
          });
          console.log("responsee.", response.data)
    
          return { status: true, data: response.data };
        } catch (err: any) {
          console.log("responsee. err", err)

          return { status: false, data: err.response.data };
        }
      }
      
      static validateOtp = async (verificationId, otp) => {
        try {
          const response = await axios.post(
            `${VITE_BASE_URL}/v1/user-property/validate-otp`,
            { verificationId, otp },
            {
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
            }
          );
          console.log("API Response:", response.data);
          return { status: true, data: response.data };
        } catch (err) {
          console.error("Error in validateOtp:", err);
          return { status: false, data: err };
        }
      };

      // Public API methods for zone and ward data (for home page)
      static getAllZonesPublic = async () => {
        try {
          const response = await axios.get(`${VITE_BASE_URL}/v1/zone-master/public`, {
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
            },
          });
          return { status: true, data: response.data };
        } catch (err: any) {
          console.error("Error fetching public zones:", err);
          return { status: false, data: err.response?.data || err };
        }
      };

      static getAllWardsPublic = async () => {
        try {
          const response = await axios.get(`${VITE_BASE_URL}/v1/ward-master/public`, {
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
            },
          });
          return { status: true, data: response.data };
        } catch (err: any) {
          console.error("Error fetching public wards:", err);
          return { status: false, data: err.response?.data || err };
        }
      };

}
export default PublicApi;