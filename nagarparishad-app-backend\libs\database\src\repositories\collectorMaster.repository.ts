import { In, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { BookNumberMasterEntity, CollectorMaster, RoleMasterEntity, UserMasterEntity, Ward_Master } from '../entities';
import { Injectable } from '@nestjs/common';
import { UpdateCollectorDto } from 'src/collector_master/dtos/collector_master.dto';
import { WardMasterRepository } from './ward_master.repository';
import { UserRepository } from './user.repository';
import { RoleMasterRepository } from './role-master.repository';
import { BookNumberMasterRepository } from './book_numberMaster.repository';

@Injectable()
export class CollectorMasterRepository extends Repository<CollectorMaster> {
  constructor(
    @InjectRepository(CollectorMaster)  
    private readonly collectorRepo: Repository<CollectorMaster>,
    private readonly wardRepo:WardMasterRepository, 
    private readonly userRepo:UserRepository,
    private readonly roleRepo:RoleMasterRepository,
    private readonly bookRepo:BookNumberMasterRepository


  ) {
    super(collectorRepo.target, collectorRepo.manager, collectorRepo.queryRunner);
  }

  async getById(collectorId: string): Promise<CollectorMaster | null> {
    return await this.collectorRepo
      .createQueryBuilder('collector')
      .leftJoinAndSelect('collector.user', 'user')
      .leftJoinAndSelect('collector.ward', 'ward')
      .leftJoinAndSelect('collector.role', 'role') 
      .where('collector.collectorId = :collectorId', { collectorId })
      .getOne();
  }
  

async getAll(): Promise<CollectorMaster[]> {
    return await this.collectorRepo
      .createQueryBuilder('collector')
      .leftJoinAndSelect('collector.user', 'user')
      .leftJoinAndSelect('collector.ward', 'ward')
      .leftJoinAndSelect('collector.role', 'role')  
      .leftJoinAndSelect('collector.books', 'books')
      .getMany();
}


  async getByWard(wardId: string): Promise<CollectorMaster | null> {
    return await this.collectorRepo
      .createQueryBuilder('collector')
      .leftJoinAndSelect('collector.user', 'user')
      .leftJoinAndSelect('collector.ward', 'ward')
      .where('ward.ward_id = :wardId', { wardId }) 
      .getOne();
  }

  async createCollector(collectorData: Partial<CollectorMaster>): Promise<CollectorMaster> {
    const collector = this.collectorRepo.create(collectorData);
    return await this.collectorRepo.save(collector);
  }

  async createCollectorForUser(data:any): Promise<CollectorMaster> {
    // console.log("inside data",data)
    const collector = this.collectorRepo.create({
        user: { user_id: data.user_id } as UserMasterEntity, // Ensure correct type casting
        ward: data.ward_id ? { ward_id: data.ward_id } as Ward_Master : null, // Ensure correct type casting
        role: { role_id: data.role_id } as RoleMasterEntity, // Ensure correct type casting
        isActive: data.isActive,
    });
    console.log("inside data",data,collector)

    return await this.collectorRepo.save(collector);
}

async createCollectorForRole(data: { isActive: boolean; role_id: number }): Promise<CollectorMaster> {
  const collector = this.collectorRepo.create({
      role: { role_id: data.role_id },  
      isActive: data.isActive,
  });

  return await this.collectorRepo.save(collector);
}
async updateCollector(
  collectorId: string,
  updateData: UpdateCollectorDto,
  books: BookNumberMasterEntity[],
): Promise<CollectorMaster | null> {
  const { wardId, userId, roleId } = updateData;

  const collector = await this.collectorRepo.findOne({
    where: { collectorId },
    relations: ['books'],
  });

  if (!collector) return null;

  if (wardId) {
    const ward = await this.wardRepo.findOne({ where: { ward_id: wardId } }); // Assuming you have a wardRepo
    if (ward) collector.ward = ward;
  }

  if (userId) {
    const user = await this.userRepo.findOne({ where: { user_id: userId } }); // Assuming you have a userRepo
    if (user) collector.user = user;
  }

  if (roleId) {
    const role = await this.roleRepo.findOne({ where: { role_id: Number(roleId) } }); // Assuming you have a roleRepo
    if (role) collector.role = role;
  }

  // Reset collectorId of all existing books to null
  if (collector.books && collector.books.length > 0) {
    await this.bookRepo.update(
      { id: In(collector.books.map(book => book.id)) },
      { collectorId: null }
    );
  }

  // Update the collector with the new books
  collector.books = books.map(book => {
    book.collectorId = collectorId;
    return book;
  });

  console.log("saveee", collector);
  await this.collectorRepo.save(collector);

  const updatedCollector = await this.getById(collectorId);
  console.log("updatedCollector", updatedCollector);

  return updatedCollector;
}


  async deleteCollector(collectorId: string): Promise<boolean> {
    const result = await this.collectorRepo.delete({ collectorId });
    return result.affected > 0;
  }

  async findAllWithWards() {
    return await this.collectorRepo.query(`
    SELECT 
    cm."collectorId" , 
    w.ward_name, COUNT(p.*)
    FROM collector_master cm 
    FULL OUTER JOIN ward_master w ON cm.ward_id = w.ward_id
    left join property p on w.ward_id = p.ward_id 
    GROUP BY w.ward_id, cm."collectorId";
`)
  }
}
