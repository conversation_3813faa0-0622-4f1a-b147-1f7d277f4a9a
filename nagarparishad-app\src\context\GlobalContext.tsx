import React, { useState, createContext } from "react";

type GlobalContext = {
  commonDialogue: any;
  setCommonDialogue: any;
  commonCollapse: any;
  setCommonCollapse: any;
  isOpen: boolean;
  setOpen: (isOpen: boolean) => void;
  toggle: () => void;
  isCollapseOpen: boolean;
  setIsCollapseOpen: (isCollapseOpen: boolean) => void;
  toggleCollapse: () => void;
  refreshZoneList: boolean;
  setRefreshZoneList: (refreshZoneList: boolean) => void;
  refreshWardList: boolean;
  setRefreshWardList: (refreshWardList: boolean) => void;
  refreshLocationList: boolean;
  setRefreshLocationList: (T: boolean) => void;
  refreshBoundaryList: boolean;
  setRefreshBoundaryList: (refreshBoundaryList: boolean) => void;
  refreshUsageList: boolean;
  setRefreshUsageList: (refreshUsageList: boolean) => void;
  refreshUsageSubList: boolean;
  setRefreshUsageSubList: (refreshUsageSubList: boolean) => void;
  refreshConstructionClassList: boolean;
  setRefreshConstructionClassList: (
    refreshConstructionClassList: boolean,
  ) => void;
  refreshAreaList: boolean;
  setRefreshAreaList: (T: boolean) => void;
  refreshPropertyList: boolean;
  setRefreshPropertyList: (T: boolean) => void;
  refreshPropertysubtypeList: boolean;
  setRefreshPropertysubtypeList: (T: boolean) => void;
  refreshStreetList: boolean;
  setRefreshStreetList: (T: boolean) => void;
  refreshadministrativeBoundaryList: boolean;
  setRefreshadministrativeBoundaryList: (T: boolean) => void;
  refreshUserList: boolean; // Added refreshUserList
  setRefreshUserList: (T: boolean) => void; // Added setRefreshUserList,
  creatRoleName: any;
  setcreatRoleName: any;
  createRoleType: any;
  setCreateRoleType: any;
  refreshModuleList: boolean; // Added refreshModuleList
  setRefreshModuleList: (T: boolean) => void; // Added setRefreshModuleList,
  refreshRoleList: boolean; // Added refreshModuleList
  setrefreshRoleList: (T: boolean) => void; // Added setRefreshModuleList,
  refreshPermissionList: boolean; // Added refreshModuleList
  setrefreshPermissionList: (T: boolean) => void; // Added setRefreshModuleList,
  searchResults: any;
  setSearchResults: any;
  isUpdateMode: any;
  setIsUpdateMode: any;
  masterComponent: any;
  setMasterComponent: any;
  refreshPropertyTaxList: any;
  setRefreshPropertyTaxList: any;
  updateProperty:any;
  setUpdateProperty:any;
  propertyId:any;
  setpropertyId:any;
};

export const GlobalContext = createContext({} as GlobalContext);

export const GlobalContextProvider = (props: any) => {
  const { children } = props;
  const [commonDialogue, setCommonDialogue] = useState(null);
  const [commonCollapse, setCommonCollapse] = useState(null);
  const [isOpen, setOpen] = useState(false);
  const [isCollapseOpen, setIsCollapseOpen] = useState(false);
  const [creatRoleName, setcreatRoleName] = useState("");
  const [createRoleType, setCreateRoleType] = useState("Administrative");
  const [refreshModuleList, setRefreshModuleList] = useState(false);
  const [refreshRoleList, setrefreshRoleList] = useState(false);
  const [refreshPermissionList, setrefreshPermissionList] = useState(false);
  const [masterComponent, setMasterComponent] = useState(null);
  const [updateProperty, setUpdateProperty] = useState<boolean>(false);
  const [propertyId, setpropertyId] = useState<any>();



  const toggle = () => {
    setOpen((prev) => !prev);
  };
  const toggleCollapse = () => {
    setIsCollapseOpen((prevState) => !prevState);
  };
  const [refreshZoneList, setRefreshZoneList] = useState(false);
  const [refreshWardList, setRefreshWardList] = useState(false);
  const [refreshLocationList, setRefreshLocationList] = useState(false);
  const [refreshBoundaryList, setRefreshBoundaryList] = useState(false);
  const [refreshUsageList, setRefreshUsageList] = useState(false);
  const [refreshUsageSubList, setRefreshUsageSubList] = useState(false);
  const [refreshConstructionClassList, setRefreshConstructionClassList] =
    useState(false);
  const [refreshAreaList, setRefreshAreaList] = useState(false);
  const [refreshPropertyList, setRefreshPropertyList] = useState(false);
  const [refreshPropertysubtypeList, setRefreshPropertysubtypeList] =
    useState(false);
  const [refreshStreetList, setRefreshStreetList] = useState(false);
  const [refreshPropertyTaxList, setRefreshPropertyTaxList] = useState(false);

  const [
    refreshadministrativeBoundaryList,
    setRefreshadministrativeBoundaryList,
  ] = useState(false);
  const [refreshUserList, setRefreshUserList] = useState(false); // Added refreshUserList
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isUpdateMode, setIsUpdateMode] = useState(true);

  return (
    <GlobalContext.Provider
      value={{
        commonDialogue,
        setCommonDialogue,
        commonCollapse,
        setCommonCollapse,
        isOpen,
        setOpen,
        toggle,
        isCollapseOpen,
        setIsCollapseOpen,
        toggleCollapse,
        refreshZoneList,
        setRefreshZoneList,
        refreshWardList,
        setRefreshWardList,
        refreshLocationList,
        setRefreshLocationList,
        refreshBoundaryList,
        setRefreshBoundaryList,
        refreshUsageList,
        setRefreshUsageList,
        refreshUsageSubList,
        setRefreshUsageSubList,
        refreshConstructionClassList,
        setRefreshConstructionClassList,
        refreshAreaList,
        setRefreshAreaList,
        refreshPropertyList,
        setRefreshPropertyList,
        refreshPropertysubtypeList,
        setRefreshPropertysubtypeList,
        refreshStreetList,
        setRefreshStreetList,
        refreshadministrativeBoundaryList,
        setRefreshadministrativeBoundaryList,
        refreshUserList, // Added refreshUserList to the context value
        setRefreshUserList, // Added setRefreshUserList to the context value
        creatRoleName,
        setcreatRoleName,
        createRoleType,
        setCreateRoleType,
        refreshModuleList, // Added refreshModuleList to the context value
        setRefreshModuleList, // Added setRefreshModuleList to the context valu,
        refreshRoleList,
        setrefreshRoleList,
        refreshPermissionList,
        setrefreshPermissionList,
        searchResults,
        setSearchResults,
        isUpdateMode,
        setIsUpdateMode,
        masterComponent,
        setMasterComponent,
        refreshPropertyTaxList,
        setRefreshPropertyTaxList,
        updateProperty,
        setUpdateProperty,
        propertyId,
        setpropertyId
        
      }}
    >
      {children}
    </GlobalContext.Provider>
  );
};
