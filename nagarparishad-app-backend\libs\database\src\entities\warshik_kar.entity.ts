import { BaseEntity, Column, CreateDateColumn, DeleteDateColumn, Entity, JoinColumn, ManyToOne, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { PropertyEntity,WarshilKarTaxEntity } from "./index"


@Entity('warshik_kar')
export class WarshilKarEntity extends BaseEntity{

    @PrimaryGeneratedColumn('uuid')
    warshik_karId: string;
  
    @ManyToOne(() => PropertyEntity, (property) => property.property_id, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'property_id' })
    property: PropertyEntity;  
  
  
    @Column({
      type: String,
      name: 'financial_year',
      nullable: true,
    })
    
    @Column({ type: String, nullable: false })
    financial_year: string;

    @Column({ type: 'float', nullable: true , default: 0})
    all_property_tax_sum_total: number;
  
    @Column({ type: 'float', nullable: true , default: 0})
    all_property_tax_sum: number;

    @Column({ type: 'float', nullable: true , default: 0})
    all_property_tax_sum_current: number;
  
    @Column({ type: 'float', nullable: true })
    tax_type_1: number;

    @Column({ type: 'float', nullable: true })
    tax_type_1_current: number;

    @Column({ type: 'float', nullable: true })
    tax_type_1_previous: number;
  
  
    @Column({ type: 'float', nullable: true })
    tax_type_2: number;

    @Column({ type: 'float', nullable: true })
    tax_type_2_current: number;

    @Column({ type: 'float', nullable: true })
    tax_type_2_previous: number;
  
    
    @Column({ type: 'float', nullable: true })
    tax_type_3: number;

    @Column({ type: 'float', nullable: true })
    tax_type_3_current: number;

    @Column({ type: 'float', nullable: true })
    tax_type_3_previous: number;
  
    
    @Column({ type: 'float', nullable: true })
    tax_type_4: number;

    @Column({ type: 'float', nullable: true })
    tax_type_4_current: number;
  
    @Column({ type: 'float', nullable: true })
    tax_type_4_previous: number;
    
    @Column({ type: 'float', nullable: true })
    tax_type_5: number;

    @Column({ type: 'float', nullable: true })
    tax_type_5_current: number;

    @Column({ type: 'float', nullable: true })
    tax_type_5_previous: number;
  
    @Column({ type: 'float', nullable: true , default:0})
    tax_type_6: number;

    @Column({ type: 'float', nullable: true , default:0})
    tax_type_6_current: number;

    @Column({ type: 'float', nullable: true , default:0})
    tax_type_6_previous: number;

    @Column({ type: 'float', nullable: true , default:0})
    tax_type_7: number;

    @Column({ type: 'float', nullable: true , default:0})
    tax_type_7_current: number;

    @Column({ type: 'float', nullable: true , default:0})
    tax_type_7_previous: number;
  
    @Column({ type: 'float', nullable: true , default:0})
    tax_type_8: number;

    @Column({ type: 'float', nullable: true , default:0})
    tax_type_8_current: number;

    @Column({ type: 'float', nullable: true , default:0})
    tax_type_8_previous: number;
  
    @Column({ type: 'float', nullable: true , default:0})
    tax_type_9: number;

    @Column({ type: 'float', nullable: true , default:0})
    tax_type_9_current: number;

    @Column({ type: 'float', nullable: true , default:0})
    tax_type_9_previous: number;
  
    @Column({ type: 'float', nullable: true , default:0}) 
    tax_type_10: number;

    @Column({ type: 'float', nullable: true , default:0})
    tax_type_10_current: number;

    @Column({ type: 'float', nullable: true , default:0})
    tax_type_10_previous: number;

    @Column({ type: 'float', nullable: true , default:0})
    other_tax_sum_tax: number;

    @Column({ type: 'float', nullable: true , default:0})
    other_tax_sum_tax_current: number;

    @Column({ type: 'float',nullable: true , default:0})
    other_tax_sum_tax_previous: number;
    
    @Column({ type: 'float',nullable: true , default:0})
    total_tax: number;

    @Column({ type: 'float',nullable: true , default:0})
    total_tax_current: number;


    @Column({ type: 'float',nullable: true , default:0})
    total_tax_previous: number;
  
    @Column({ type: String, nullable: true })
    status: string;
  
    // @OneToMany(() => Tax_PropertyWiseEntity, (tax_propertywise) => tax_propertywise.tax_Property)
    // tax_propertywise: Tax_PropertyWiseEntity[];
  
    // @OneToMany(() => Tax_PropertyEntity_Other_Taxes, (tax_property_other_taxes) => tax_property_other_taxes.tax_Property)
    // tax_property_other_taxes: Tax_PropertyEntity_Other_Taxes[];
  
    @OneToMany(() => WarshilKarTaxEntity, (warshilKarTax) => warshilKarTax.WarShikKar)
    warshilKarTax: WarshilKarTaxEntity[];


    
    @Column({ type: 'float', nullable: true, default: 0 })
    property_type_discount: string;

    
  
    @Column({ type: String, nullable: true })
    billNo: string;

 
    @Column({ type: 'date', nullable: true })
    bill_generation_date: Date;

    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;
  
    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;
  
    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;

}