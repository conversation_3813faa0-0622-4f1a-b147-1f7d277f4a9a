import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateUsageMasterDto,
  UpdateUsageMasterDto,
  UsageMasterDto,
} from './dto/usage-master.dto';
import { UsageMasterRepository } from 'libs/database/repositories';

@Injectable()
export class UsageMasterService {
  constructor(private readonly usageMasterRepository: UsageMasterRepository) {}
  async create(createUsageMasterDto: CreateUsageMasterDto) {
    try {
      const saveData =
       // await this.usageMasterRepository.saveData(createUsageMasterDto);
        console.log("saveData");
      return {
        message: 'Data Saved SuccessFully',
        data: saveData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllData = await this.usageMasterRepository.findAllData();

      if (!getAllData) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(usageMasterDto: UsageMasterDto) {
    try {
      const { usage_id } = usageMasterDto;
      const checkData = await this.usageMasterRepository.findById(usage_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      return {
        message: 'Data found Sucess',
        data: checkData,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    usageMasterDto: UsageMasterDto,
    updateUsageMasterDto: UpdateUsageMasterDto,
  ) {
    try {
      const { usage_id } = usageMasterDto;
      const checkData = await this.usageMasterRepository.findById(usage_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      // const updateData = await this.usageMasterRepository.updateData(
      //   usage_id,
      //   updateUsageMasterDto,
      // );

      const updateData={};

      // if (updateData.affected === 0) {
      //   throw new NotAcceptableException('Failed to update data');
      // }

      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(usageMasterDto: UsageMasterDto) {
    try {
      const { usage_id } = usageMasterDto;
      const checkData = await this.usageMasterRepository.findById(usage_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      const deleteData = await this.usageMasterRepository.deleteData(usage_id);

      if (deleteData.affected === 0) {
        throw new NotAcceptableException('Failed to delete data');
      }

      return {
        message: 'Data Deleted SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }
}