export enum ModuleName {
  Setting = "Setting",
  Master = "Master",
  Property = "Property",
  Payment = "Payment",
  Register = "Register",
  RoleAndUserMangement = "Role and User Mangement",
}

// ============================================================================
// FORM DEFINITIONS (47 Total)
// ============================================================================
export enum FormName {
  // ========== SETTING MODULE FORMS (9 Forms) ==========
  DeprecationRate = "Deprecation Rate",
  RRConstructionRate = "RR Construction Rate",
  RRRate = "RR Rate",
  TaxRate = "Tax Rate",
  WeightingRate = "Weighting Rate",
  GhanKachraRate = "Ghan Kachra Rate",
  BookMaster = "Book Master",
  FinancialYearMaster = "Financial Year Master",
  ReassessmentMaster = "Reassessment Master",

  // ========== MASTER MODULE FORMS (18 Forms) ==========
  Zone = "Zone",
  Ward = "Ward",
  Road = "Road",
  PropertyType = "PropertyType",
  PropertyTypeDepartment = "PropertyType Department",
  Floor = "Floor",
  Usage = "Usage",
  UsageSub = "Usage Sub",
  LocationMaster = "Location Master",
  StreetMaster = "Street Master",
  ElectionBoundryMaster = "Election Boundry Master",
  AreaMaster = "Area Master",
  PropertySubTypeMaster = "Property Sub Type Master",
  PropertyClassMaster = "Property Class Master",
  ConstructionClassMaster = "Construction Class Master",
  CollectorMaster = "Collector Master",
  OwnerTypeMaster = "Owner Type Master",
  AdminBoundaryMaster = "Admin Boundary Master",

  // ========== PROPERTY MODULE FORMS (1 Form) ==========
  Property = "Property",

  // ========== PAYMENT MODULE FORMS (2 Forms) ==========
  PaymentBill = "Payment Bill",
  PaymentLogs = "Payment Logs",

  // ========== REGISTER MODULE FORMS (2 Forms) ==========
  AssessmentReport = "Assessment Report",
  RegisterReport = "Register Report",

  // ========== ROLE AND USER MANAGEMENT MODULE FORMS (3 Forms) ==========
  Role = "Role",
  User = "User",
  UpdateRoles = "Update Roles",

  // ========== ADDITIONAL FORMS (12 Forms) ==========
  TaxCreation = "Tax Creation",
  Namuna10 = "Namuna 10",
  Dashboard = "Dashboard",
  ImportExport = "Import Export",
  TaxCalculation = "Tax Calculation",
  BillGeneration = "Bill Generation",
  PropertyDivision = "Property Division",
  PenaltyManagement = "Penalty Management",
  CronJobs = "Cron Jobs",
  GlobalSearch = "Global Search",
  Logs = "Logs",
  BackupMigration = "Backup Migration",
}

// ============================================================================
// ACTION DEFINITIONS (4 Total)
// ============================================================================
export enum Action {
  CanRead = "can_read",
  CanCreate = "can_write",
  CanUpdate = "can_update",
  CanDelete = "can_delete",
}

// ============================================================================
// PERMISSION MAPPING BY MODULE
// ============================================================================
export const MODULE_FORM_MAPPING: Record<ModuleName, FormName[]> = {
  [ModuleName.Setting]: [
    FormName.DeprecationRate,
    FormName.RRConstructionRate,
    FormName.RRRate,
    FormName.TaxRate,
    FormName.WeightingRate,
    FormName.GhanKachraRate,
    FormName.BookMaster,
    FormName.FinancialYearMaster,
    FormName.ReassessmentMaster,
  ],
  [ModuleName.Master]: [
    FormName.Zone,
    FormName.Ward,
    FormName.Road,
    FormName.PropertyType,
    FormName.PropertyTypeDepartment,
    FormName.Floor,
    FormName.Usage,
    FormName.UsageSub,
    FormName.LocationMaster,
    FormName.StreetMaster,
    FormName.ElectionBoundryMaster,
    FormName.AreaMaster,
    FormName.PropertySubTypeMaster,
    FormName.PropertyClassMaster,
    FormName.ConstructionClassMaster,
    FormName.CollectorMaster,
    FormName.OwnerTypeMaster,
    FormName.AdminBoundaryMaster,
  ],
  [ModuleName.Property]: [
    FormName.Property,
  ],
  [ModuleName.Payment]: [
    FormName.PaymentBill,
    FormName.PaymentLogs,
  ],
  [ModuleName.Register]: [
    FormName.AssessmentReport,
    FormName.RegisterReport,
  ],
  [ModuleName.RoleAndUserMangement]: [
    FormName.Role,
    FormName.User,
    FormName.UpdateRoles,
  ],
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get all forms for a specific module
 */
export const getFormsByModule = (moduleName: ModuleName): FormName[] => {
  return MODULE_FORM_MAPPING[moduleName] || [];
};

/**
 * Get total count of forms
 */
export const getTotalFormsCount = (): number => {
  return Object.values(FormName).length;
};

/**
 * Get total count of modules
 */
export const getTotalModulesCount = (): number => {
  return Object.values(ModuleName).length;
};

/**
 * Get total count of actions
 */
export const getTotalActionsCount = (): number => {
  return Object.values(Action).length;
};

/**
 * Check if a form belongs to a specific module
 */
export const isFormInModule = (formName: FormName, moduleName: ModuleName): boolean => {
  return MODULE_FORM_MAPPING[moduleName]?.includes(formName) || false;
};
