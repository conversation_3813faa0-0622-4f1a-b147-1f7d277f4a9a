import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedPenaltyTable1746017767471 implements MigrationInterface {
    name = 'AddedPenaltyTable1746017767471'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "penalty_fee_yearWise" ("penalty_fee_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year" character varying NOT NULL, "value" numeric NOT NULL, "tax_percentage" numeric NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, CONSTRAINT "PK_66d7993d5461c638f8a050d72d0" PRIMARY KEY ("penalty_fee_id"))`);
     await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" ADD CONSTRAINT "FK_8edf6ce152eeb763c6b7a6b8028" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" DROP CONSTRAINT "FK_8edf6ce152eeb763c6b7a6b8028"`);
         await queryRunner.query(`DROP TABLE "penalty_fee_yearWise"`);
    }

}
