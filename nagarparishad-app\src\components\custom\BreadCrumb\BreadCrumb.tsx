import React, { useEffect, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";

const routeNameMap = {
  "/": "Home",
  "/login": "Login",
  "/pay-online": "Pay Online",
  "/payment": "Payment",
  "/contact-us": "Contact Us",
  "/dashboard": "Dashboard",
  "/dashboard/user-registration": "User Registration",
  "/dashboard/user-role": "User Role",
  "/dashboard/create-role": "Create Role",
  "/dashboard/search-list": "Search List",
  "/property": "Property",
  "/property/property-registration": "Property Registration",
  "/property/property-view": "Property View",
  "/master": "Master",
  "/master/zone-master": "Zone Master",
  "/master/ward-master": "Ward Master",
  "/master/location-master": "Location Master",
  "/master/street-master": "Street Master",
  "/master/election-boundry-master": "Election Boundary Master",
  "/master/usage-master": "Usage Master",
  "/master/usage-sub-master": "Usage Sub Master",
  "/master/area-master": "Area Master",
  "/master/propertytype-master": "Property Type Master",
  "/master/propertysubtype-master": "Property SubType Master",
  "/master/construction-master": "Construction Class Master",
  "/master/propertytaxrate-master": "Property Tax Rate Master",
  "/master/administrativeboundary-master": "Administrative Boundary Master",
  "/master/readyreckonerrate-master": "Ready Reckoner Rate Master",

};

const BreadCrumb = ({ className }) => {
  const location = useLocation();
  const pathnames = location.pathname.split("/").filter((x) => x);
  const [href, setHref] = useState("");

  useEffect(() => {
    const userData = localStorage.getItem("UserData");
    if (userData) {
      setHref("/dashboard");
    } else {
      setHref("/");
    }
  }, []);

  return (
    <Breadcrumb className={`  border-white ${className}  pt-3 `}>
      <BreadcrumbList className=" ml-[2rem]  justify-end pr-8  ">
        <BreadcrumbItem className="text-white">
          <BreadcrumbLink
            href={href}
            className="!text-gray-600 font-bold text-[14px] mt-[1rem]"
          >
            {href !== "/" ? "Dashboard" : "Home"}
          </BreadcrumbLink>
        </BreadcrumbItem>
        {pathnames.map((value, index) => {
          const to = `/${pathnames.slice(0, index + 1).join("/")}`;
          return (
            <React.Fragment key={to}>
              <BreadcrumbSeparator className="!text-gray-600  mt-[1rem]  " />
              <BreadcrumbItem className=" text-gray-600 font-bold  text-[14px] mt-[1rem]">
                {index === pathnames.length - 1 ? (
                  <BreadcrumbPage className="!text-black text-[14px]">
                    {routeNameMap[to]}
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink href={to}>{routeNameMap[to]}</BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadCrumb;
