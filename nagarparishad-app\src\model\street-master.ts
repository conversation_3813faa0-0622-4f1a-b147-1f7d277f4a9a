export interface StreetMasterObject {
  street_id: string;
  street_name: string;
  createdAt?: string;
  updatedAt?: string;
  deleteAt?: string | null;
}

export interface StreetSelectOject {
  street_id: string;
  street_name: string;
}

export interface StreetListAllApi {
  statusCode: number;
  message: string;
  data: StreetMasterObject[];
}

export interface StreetCreateApi {
  statusCode: number; //201
  message: string;
  data: StreetMasterObject;
}
export interface StreetUpdateApi {
  statusCode: number; //200
  message: string;
}

export interface StreetSendApiObj {
  street_name: string;
}
