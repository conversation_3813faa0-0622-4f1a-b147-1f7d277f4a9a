import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useZoneMasterController } from "@/controller/master/ZoneMasterController";
import { ZoneObject } from "../../../model/zone-master";
import { useToast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { Loader2 } from "lucide-react";

interface ZoneMasterInterface {
  btnTitle: string;
  editData?: ZoneObject;
}

const ZoneMasterForm = ({ btnTitle, editData }: ZoneMasterInterface) => {
  const { t } = useTranslation();

  // Updated schema to remove 'ward'
  const schema = z.object({
    zone: z.string().trim().min(1, t("errorsRequiredField")),
  });

  const { toast } = useToast();
  const [loader, setLoader] = useState(false);
  const { setIsCollapseOpen, refreshZoneList, setRefreshZoneList } =
    useContext(GlobalContext);

  const { createZone, updateZone } = useZoneMasterController();

  const dynamicValues = {
    name: t("zone.zoneLabel"),
  };

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      zone: editData?.zoneName || "",
    },
  });

  const {
    formState: { errors },
    reset,
  } = form;

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>
  ) => {
    event.preventDefault();
    const DataResponse = {
      zoneName: data.zone,
    };

    if (editData?.zone_id !== undefined && editData?.zone_id !== null) {
      setLoader(true);
      updateZone(
        { zoneId: editData.zone_id, zoneData: DataResponse },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            form.reset({ zone: "" });
            setRefreshZoneList(!refreshZoneList);
            setIsCollapseOpen(false);
            setLoader(false);
          },
          onError: (error: any) => {
            toast({
              title: error.message,
              variant: "destructive",
            });
            setLoader(false);
          },
        }
      );
    } else {
      setLoader(true);
      createZone(DataResponse, {
        onSuccess: () => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          form.reset({ zone: "" });
          setIsCollapseOpen(false);
          setLoader(false);
        },
        onError: (error: any) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });
    }
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        zone: editData.zoneName || "",
      });
    } else {
      form.reset({
        zone: "",
      });
    }
  }, [editData]);

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className=" ">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="zone"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("zone.zoneLabel")}</FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1 "
                        placeholder={t("zone.zoneLabel")}
                        {...field}
                      />
                    </FormControl>
                    {errors.zone && (
                      <FormMessage className="ml-1">
                        {t("errorsRequiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="grid-cols-subgrid mb-1 ml-4 max-md:flex max-md:justify-end pt-[32px] ">
              {loader ? (
                <Button disabled>
                  <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                  {t("pleaseWait")}
                </Button>
              ) : (
                <Button type="submit" variant="submit">
                  {t(btnTitle)}
                </Button>
              )}
            </div>
          </div>
        </form>
      </Form>
    </>
  );
};

export default ZoneMasterForm;
