import {
    BaseEntity,
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    DeleteDateColumn,
    <PERSON>in<PERSON><PERSON>umn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    ManyToOne,
    OneToMany,
  } from 'typeorm';
  import { PropertyTypeMasterEntity } from './property-type-master.entity';
import { Property_type_class_master } from './property_type_classMaster.entity';
import { ReassessmentRange } from './reassesment_range.entity';


  @Entity('master_rr_construction_rate')
  export class Master_rr_construction_rate extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    rr_construction_rate_id: string;

    @ManyToOne(() => Property_type_class_master, (property_type) => property_type.property_type_class_id)
    @JoinColumn({ name: 'property_type_class_id' })
    property_type_class_id: Property_type_class_master;


    @Column({ type: String, nullable: true })
    financial_year: string;

    @Column({ type: 'float', nullable: false , default: 0})
    value: number;


    @Column({ type: String, nullable: true, default: 'Active' })
    status: string;

    @ManyToOne(() => ReassessmentRange, (reassessmentRange) => reassessmentRange.reassessment_range_id, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn({ name: 'reassessment_range_id' })
    reassessmentRange: ReassessmentRange;


    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;

    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;

    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
  }

