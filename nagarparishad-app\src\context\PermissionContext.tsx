import React, { createContext, useContext, useEffect, useState } from 'react';
import axios from 'axios';
import { Action, FormName, ModuleName } from '@/constant/enums/permissionEnum';

// Define enums for module names, form names, and actions


export interface PermissionAction {
  action_id: number;
  can_read: boolean;
  can_write: boolean;
  can_update: boolean;
  can_delete: boolean;
  is_valid: boolean;
}

export interface Form {
  form_id: number;
  formName: string;
  permissions?: PermissionAction[];
}

export interface PermissionModule {
  module_id: number;
  moduleName: string;
  forms: Form[];
}

export interface PermissionContextType {
  permissions: PermissionModule[] | null;
  loading: boolean;
  canPerformAction: (moduleName: string, formName: string, action: keyof PermissionAction) => boolean;
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);
const PermissionUpdateContext = createContext<React.Dispatch<React.SetStateAction<PermissionModule[] | null>> | undefined>(undefined);

export const PermissionProvider = ({ children }) => {
  const [permissions, setPermissions] = useState<PermissionModule[] | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchPermissions = async () => {
      try {
        setLoading(true);
        console.log("calling fetchPermissions");
        const savedPermissions = localStorage.getItem('permissions');
        if (savedPermissions) {
          setPermissions(JSON.parse(savedPermissions));
        }
        // Uncomment the following lines to fetch permissions from an API
        // else {
        //   const response = await axios.get('/api/permissions'); // Adjust the endpoint
        //   const permissions = response.data;
        //   localStorage.setItem('permissions', JSON.stringify(permissions));
        //   setPermissions(permissions);
        // }
      } catch (error) {
        console.error('Error fetching permissions:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPermissions();
  }, []);

  const canPerformAction = (moduleName: ModuleName, formName: FormName, action:Action):boolean => {
    if(permissions === null) return false;

    const module = permissions?.find(mod => mod.moduleName === moduleName);

    if (!module) return false;
    // if (!module) return true;

    
    
    const form = module.forms.find(f => f.formName === formName);

    if (!form) return false;
    // if (!form) return true;



    if(!form.permissions) return true;

    const permissionAction = form.permissions.find(p => p[action]);
    return permissionAction ? Boolean(permissionAction[action]) : false;
  };

  return (
    <PermissionContext.Provider value={{ permissions, loading, canPerformAction }}>
      <PermissionUpdateContext.Provider value={setPermissions}>
        {children}
      </PermissionUpdateContext.Provider>
    </PermissionContext.Provider>
  );
};

export const usePermissions = () => {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error("usePermissions must be used within a PermissionProvider");
  }
  return context;
};

export const useUpdatePermissions = () => {
  const context = useContext(PermissionUpdateContext);
  if (!context) {
    throw new Error("useUpdatePermissions must be used within a PermissionProvider");
  }
  return context;
};
