import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedColumnFY1752231625781 implements MigrationInterface {
    name = 'AddedColumnFY1752231625781'

    public async up(queryRunner: QueryRunner): Promise<void> {
                await queryRunner.query(`ALTER TABLE "payment_info" ADD "financial_year_range" character varying NULL`);

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
                await queryRunner.query(`ALTER TABLE "payment_info" DROP COLUMN "financial_year_range"`);

    }

}
