import * as React from "react";

import { cn } from "@/lib/utils";

const Wizard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("bg-Wizard text-Wizard-foreground", className)}
    {...props}
  />
));
Wizard.displayName = "Wizard";

const WizardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
));
WizardHeader.displayName = "WizardHeader";

const WizardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn("text-2xl font-medium leading-none", className)}
    {...props}
  />
));
WizardTitle.displayName = "WizardTitle";

const WizardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
WizardDescription.displayName = "WizardDescription";

const WizardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("pt-0", className)} {...props} />
));
WizardContent.displayName = "WizardContent";

const WizardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("text-left", className)} {...props} />
));
WizardFooter.displayName = "WizardFooter";

export {
  Wizard,
  WizardHeader,
  WizardFooter,
  WizardTitle,
  WizardDescription,
  WizardContent,
};
