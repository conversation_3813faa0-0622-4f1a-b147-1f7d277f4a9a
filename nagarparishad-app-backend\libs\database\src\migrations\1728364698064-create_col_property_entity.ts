import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateColPropertyEntity1728364698064 implements MigrationInterface {
    name = 'CreateColPropertyEntity1728364698064'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_2ec55a615abf24e021be0904df5"`);
        await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "UQ_2ec55a615abf24e021be0904df5"`);
        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "common_fields_id"`);
        await queryRunner.query(`ALTER TABLE "property" ADD "plot_number" character varying`);
        await queryRunner.query(`ALTER TABLE "property" ADD "block_number" character varying`);
        await queryRunner.query(`ALTER TABLE "property" ADD "house_number" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "house_number"`);
        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "block_number"`);
        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "plot_number"`);
        await queryRunner.query(`ALTER TABLE "property" ADD "common_fields_id" uuid`);
        await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "UQ_2ec55a615abf24e021be0904df5" UNIQUE ("common_fields_id")`);
        await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_2ec55a615abf24e021be0904df5" FOREIGN KEY ("common_fields_id") REFERENCES "common_fields_of_property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
