import React, { useEffect, useState } from "react";
import { Outlet } from "react-router-dom";
import Header from "@/components/custom/DashBoardHeader";
import Sidebar from "@/components/custom/DashBoardSideBar";
import { UserData } from "@/model/global-master";
import BreadCrumb from "../BreadCrumb";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  //login user info
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  useEffect(() => {
    const handleResize = () => {

      if (window.innerWidth >= 1100) {
        setSidebarOpen(true);
      } else {
        setSidebarOpen(false);
      }
    };

    handleResize();


    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);




  return (
    <div className="flex overflow-hidden gap-2">
      {window.innerWidth >= 1100 ? (
        <Sidebar isopen={true} position="relative" />
      ) : (
        <Sidebar
          isopen={sidebarOpen}
          toggle={toggleSidebar}
          position="absolute"
        />
      )}
      <div className="flex flex-col flex-1 2xl:w-full lg:w-[60%] w-full min-h-screen overflow-auto">
        <Header toggle={toggleSidebar} />
        <div className="flex-1  overflow-y-auto bg-Secondary ">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
