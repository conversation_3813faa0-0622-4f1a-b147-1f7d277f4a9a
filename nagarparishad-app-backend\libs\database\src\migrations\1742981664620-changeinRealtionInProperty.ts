import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeinRealtionInProperty1742981664620 implements MigrationInterface {
    name = 'ChangeinRealtionInProperty1742981664620'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD "length" double precision NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD "width" double precision NOT NULL DEFAULT '0'`);
 }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP COLUMN "width"`);
        await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP COLUMN "length"`);
    }

}
