import { MigrationInterface, QueryRunner } from "typeorm";

export class Warshik11727884874459 implements MigrationInterface {
    name = 'Warshik11727884874459'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "warshik_kar" ("warshik_karId" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year" character varying NOT NULL, "all_property_tax_sum_total" double precision NOT NULL DEFAULT '0', "all_property_tax_sum" double precision NOT NULL DEFAULT '0', "all_property_tax_sum_current" double precision NOT NULL DEFAULT '0', "tax_type_1" double precision, "tax_type_1_current" double precision, "tax_type_1_previous" double precision, "tax_type_2" double precision, "tax_type_2_current" double precision, "tax_type_2_previous" double precision, "tax_type_3" double precision, "tax_type_3_current" double precision, "tax_type_3_previous" double precision, "tax_type_4" double precision, "tax_type_4_current" double precision, "tax_type_4_previous" double precision, "tax_type_5" double precision, "tax_type_5_current" double precision, "tax_type_5_previous" double precision, "tax_type_6" double precision DEFAULT '0', "tax_type_6_current" double precision DEFAULT '0', "tax_type_6_previous" double precision DEFAULT '0', "tax_type_7" double precision DEFAULT '0', "tax_type_7_current" double precision DEFAULT '0', "tax_type_7_previous" double precision DEFAULT '0', "tax_type_8" double precision DEFAULT '0', "tax_type_8_current" double precision DEFAULT '0', "tax_type_8_previous" double precision DEFAULT '0', "tax_type_9" double precision DEFAULT '0', "tax_type_9_current" double precision DEFAULT '0', "tax_type_9_previous" double precision DEFAULT '0', "tax_type_10" double precision DEFAULT '0', "tax_type_10_current" double precision DEFAULT '0', "tax_type_10_previous" double precision DEFAULT '0', "other_tax_sum_tax" double precision NOT NULL, "other_tax_sum_tax_current" double precision NOT NULL, "other_tax_sum_tax_previous" double precision NOT NULL, "total_tax" double precision NOT NULL, "total_tax_current" double precision NOT NULL, "total_tax_previous" double precision NOT NULL, "status" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, CONSTRAINT "PK_0edecb2ee9a7b40b436a8dec980" PRIMARY KEY ("warshik_karId"))`);
        await queryRunner.query(`CREATE TABLE "warshikKarTax" ("warshik_karTaxId" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year" character varying, "sq_ft_meter" double precision NOT NULL, "rr_rate" double precision NOT NULL, "rr_construction_rate" double precision NOT NULL, "depreciation_rate" double precision NOT NULL, "weighting" double precision NOT NULL, "capital_value" double precision NOT NULL, "tax_value" double precision NOT NULL, "tax" double precision NOT NULL, "bill_generation_date" date NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, "warshik_karId" uuid, "property_usage_details_id" uuid, CONSTRAINT "PK_3c28ab6cdc4a8a92fb9ffe599fb" PRIMARY KEY ("warshik_karTaxId"))`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "owner_table_id"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "total_tax"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "emarat_kar"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "usage_details_table_id"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "capital_value"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "property_table_id"`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ADD CONSTRAINT "FK_dc23330aada107590eed796d7c0" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "warshikKarTax" ADD CONSTRAINT "FK_782f8b60c2e42b03b9dc97ca1de" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "warshikKarTax" ADD CONSTRAINT "FK_cb538122b36d56ac5aa3f7c42b1" FOREIGN KEY ("warshik_karId") REFERENCES "warshik_kar"("warshik_karId") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "warshikKarTax" ADD CONSTRAINT "FK_9568ec13126620a0b3dbc231560" FOREIGN KEY ("property_usage_details_id") REFERENCES "property_usage_details"("property_usage_details_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "warshikKarTax" DROP CONSTRAINT "FK_9568ec13126620a0b3dbc231560"`);
        await queryRunner.query(`ALTER TABLE "warshikKarTax" DROP CONSTRAINT "FK_cb538122b36d56ac5aa3f7c42b1"`);
        await queryRunner.query(`ALTER TABLE "warshikKarTax" DROP CONSTRAINT "FK_782f8b60c2e42b03b9dc97ca1de"`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" DROP CONSTRAINT "FK_dc23330aada107590eed796d7c0"`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "property_table_id" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "capital_value" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "usage_details_table_id" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "emarat_kar" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "total_tax" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "owner_table_id" character varying`);
        await queryRunner.query(`DROP TABLE "warshikKarTax"`);
        await queryRunner.query(`DROP TABLE "warshik_kar"`);
    }

}
