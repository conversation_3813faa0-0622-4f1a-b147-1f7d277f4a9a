import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { CronJobsService } from './cron-jobs.service';
import {
  PenaltyFeeYearWiseRepository,
  PreviousOwnerRepository,
  UserOtpRepository,
  WarshikKarRepository,
  PaidDataRepository,
  Financial_yearRepository,
  ReassessmentRangeRepository,
  CronJobFailureRepository
} from 'libs/database/repositories';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  PenaltyFeeYearWiseEntity,
  PreviousOwnerEntity,
  UserOtpEntity,
  PaidDataEntity,
  Financial_year,
  WarshilKarEntity,
  ReassessmentRange,
  CronJobFailureEntity
} from 'libs/database/entities';
import { EmailService } from 'src/utils/email.service';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([
      PreviousOwnerEntity,
      UserOtpEntity,
      PenaltyFeeYearWiseEntity,
      PaidDataEntity,
      Financial_year,
      WarshilKarEntity,
      ReassessmentRange, // Ensure this entity is included
CronJobFailureEntity
    ]),
  ],
  providers: [
    CronJobsService,
    PreviousOwnerRepository,
    UserOtpRepository,
    PenaltyFeeYearWiseRepository,
    WarshikKarRepository,
    PaidDataRepository,
    Financial_yearRepository,
    ReassessmentRangeRepository ,// Ensure this repository is provided,,
    CronJobFailureRepository,
    EmailService

  ],
  exports: [CronJobsService],
})
export class CronJobsModule {}
