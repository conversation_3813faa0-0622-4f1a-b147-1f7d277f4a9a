import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    JoinColumn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    ManyToOne,
    OneToMany,
  } from 'typeorm';
  import { PropertyTypeMasterEntity } from './property-type-master.entity';
  import { ReassessmentRange } from './reassesment_range.entity';


  @Entity('master_tax_rate')
  export class Master_tax_rateEntity extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    rr_tax_id: string;

    @ManyToOne(() => PropertyTypeMasterEntity, (property_type) => property_type.propertyType_id)
    @JoinColumn({ name: 'propertyType_id' })
    property_type: PropertyTypeMasterEntity;

    @Column({ type: String, nullable: true })
    financial_year: string;

    @Column({ type: 'float', nullable: false , default: 0})
    value: number;


    @Column({ type: String, nullable: true, default: 'Active' })
    status: string;

    @ManyToOne(() => ReassessmentRange, (reassessmentRange) => reassessmentRange.reassessment_range_id, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn({ name: 'reassessment_range_id' })
    reassessmentRange: ReassessmentRange;


    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;

    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;

    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
  }

