{"username": "वापरकर्तानाव", "submit": "सबमिट", "formDescription": "वर्णन", "Welcome to React": "रिअ‍ॅक्टमध्ये आपले स्वागत आहे", "greeting": "शुभेच्छा!", "navbar": {"downloadApp": "मोबाइल अ‍ॅप डाउनलोड करा", "mainTopics": "मुख्य विषय", "fontSize": {"decrease": "अ-", "normal": "अ", "increase": "अ+"}}, "search": "शोधा", "reset": "रीसेट", "searchBy": "याद्वारे शोधा", "advancesearch": "ऍडव्हान्स सर्च", "searchOwnerName": "मालमत्ता धारक", "back": "मागे", "BillDetails": "बिल तपशील", "errorsRequiredField": "हि फिल्ड अनिवार्य आहे.", "errorsInvalidInput": "अवैध इनपुट.", "errorsNetworkError": "नेटवर्क त्रुटी. कृपया नंतर पुन्हा प्रयत्न करा.", "errorsInternalServerError": "आंतरनिर्हित सर्व्हर त्रुटी. कृपया नंतर पुन्हा प्रयत्न करा.", "errorsUnauthorized": "अनधिकृत प्रवेश.", "errorsInvalidCredentials": "अवैध क्रेडेंशियल्स.", "errorsEmailRequiredError": "ईमेल आवश्यक आहे.", "errorsPasswordRequiredError": "पासवर्ड आवश्यक आहे.", "errorsOtpRequiredError": "OTP आवश्यक आहे.", "errorsInvalidEmailFormat": "अवैध ईमेल स्वरूप.", "errorsPasswordStrengthError": "पासवर्ड ८ अक्षरे, मोठे, लहान, संख्या, आणि विशेष अक्षरे असावे.", "errorsMobileNumber": "फोन नंबर १० अंकांचा असणे अनिवार्य आहे", "financialYearExists": "हे आर्थिक वर्ष आधीच अस्तित्वात आहे", "HeaderSectionLogoname": "शिरोळ नगरपरिषद", "HeaderSectionSearch": "शोधा", "HeaderSectionNamelist": "नावांची यादी", "HeaderSectionLogin": "लॉगिन", "removeOwner": "मालमत्ता धारक यशस्वीरित्या हटविण्यात आला", "addOwner": "मालक यशस्वीरित्या जोडला", "homepagePayOnline": "ऑनलाइन कर भरणा", "homepageNewCitizenServices": "नवीन नागरिक सेवा", "homepageAdditionalCollectorAccess": "अतिरिक्त कलेक्टर प्रवेश", "homepageAddProperty": "मालमत्ता जोडा", "homepageSearchProperty": "मालमत्ता शोधा", "footerOrganizationName": "शिरोळ नगरपरिषद", "footerOrganizationDescription": "पारदर्शक प्रशासन सशक्त करणे आणि घरगुती खर्च कमी करणे.", "footerOrganizationAddress": "शिरोळ, महाराष्ट्र ४१६१०३", "footerLinks": "द्रुत लिंक", "footerListItem1": "महाराष्ट्र शासन", "footerListItem2": "आपले सरकार", "footerListItem3": "7/12 पहा", "footerListItem4": "कोल्हापूर पोलीस", "footerListItem5": "कोल्हापूर जिल्हा परिषद", "footerListItem6": "शासकीय जमिनीच्या नोंदी", "footerContactInfo": "संपर्क माहिती", "footerPhoneNumber": "📞 फोन नंबर", "footerTechnicalSupport": "तांत्रिक समर्थन", "footerEmail": "📧 ईमेल", "footerCopyRight": "कॉपीराइट", "footerAllRightsReserved": "सर्व हक्क राखीव.", "footerContactNumber": "+९१ ९८७६५४३२१०", "footerLandLineNumber": "१२३४५-६७८९०", "footerEmailId": "<EMAIL>", "footerAddress": "छत्रपती संभाजी चौक, एमएच एसएच १३७, शिरोळ", "footerOpening": "सोमवार - शनिवार", "footerWebPolicies": "वेबसाइट धोरणे", "footerSiteMap": "साईट मॅप", "footerContactUs": "संपर्क साधा", "footerDownloadApp": "मोबाईल ॲप  डाउनलोड करा", "footerDesignBy": "डिझाइन आणि विकसित केले", "paytax": "कर भरा ", "loginHeading": "लॉगिन", "loginSubHeading": "आपले क्रेडेंशियल्स प्रविष्ट करा", "loginEmailLabel": "ईमेल आयडी", "loginEmailPlaceholder": "ईमेल आयडी", "loginPasswordLabel": "पासवर्ड", "loginPasswordPlaceholder": "पासवर्ड", "loginOTP": "ओटीपी", "loginSubmit": "सबमिट करा", "loginOTPButton": "ओटीपी", "loginForgotPasswordLink": "पासवर्ड विसरलात?", "loginEmailRequiredError": "ईमेल आवश्यक आहे", "loginPasswordRequiredError": "पासवर्ड आवश्यक आहे", "loginClassicalContentHeading": "क्लासिकल सामग्री", "loginClassicalContentText": "कृपया लक्षात घ्या: ही प्रणाली फक्त अधिकृत वापरासाठी आहे. अनधिकृत प्रवेश करण्याचा प्रयत्न कायदेशीर कारवाईस पात्र ठरू शकतो.", "loginBtn": "लॉगिन", "pleaseWait": "कृपया प्रतीक्षा करा", "onlineFormSearchBy": "द्वारे शोधा", "onlineFormSelectOption": "पर्याय निवडा", "onlineFormEnterValue": "मूल्य प्रविष्ट करा", "onlineFormSeeDetails": "वर्णन पहा", "onlineFormAvailableProperties": "उपलब्ध मालमत्ता", "onlineFormFullName": "पूर्ण नाव", "onlineFormCardNumber": "कार्ड क्रमांक", "onlineFormcvv": "सीव्हीव्ही", "onlineFormExpiryDate": "समाप्ती तारीख", "onlineFormGetOTP": "ओटीपी प्राप्त करा", "onlineFormVerifyPassword": "पासवर्ड सत्यापित करा", "onlineFormSendMoney": "पैसे पाठवा", "onlineFormHouseType": "घराचा प्रकार", "onlineFormAddress": "पत्ता", "advancedsearch": "प्रगत शोध", "onlineFormPhoneNumber": "फोन नंबर", "onlineFormTable": {"index": "सूची"}, "propertysearchholder": "मालमत्ता क्रमांक/फोन नंबराद्वारे शोधा", "propertyLocationFormWizardStepOne": "टप्पा 1", "propertyLocationFormWizardStepTwo": "टप्पा 2", "propertyLocationFormWizardStepThree": "टप्पा 3", "propertyLocationFormWizardStepFour": "टप्पा 4", "propertyLocationFormWizardStepOneTitle": "मालमत्ता ठिकाण तपशील", "propertyLocationFormWizardStepTwoTitle": "मालमत्ता धारकाची माहिती ", "propertyLocationFormWizardStepThreeTitle": "मालमत्ता मूल्यांकनाचे तपशील", "propertyLocationFormWizardStepFourTitle": "प्लॉट तपशील", "propertyLocationFormHeading": "मालमत्ता शोधा", "propertyLocationFormSubHeading": "मालमत्ता ठिकाणाची माहिती", "propertyregisteartion": "मालमत्ता नोंदणी फॉर्म", "propertyListing": "उपलब्ध मालमत्ता", "SrNo": "अ. क्र.", "Actions": "क्रिया", "PropertyOwner": "मालमत्ता धारक", "Mobile": "मोबाइल नंबर", "enterFullName": "आपले नाव प्रविष्ट करा", "openWebcam": "वेबकॅम उघडा", "uploadedFiles": "अपलोड केलेल्या फाइल्स", "takePhoto": "फोटो काढा", "propertys": {"AddBtn": "मालमत्ता नोंदवा", "update": "मालमत्ता अद्यतनित करा", "updateSuccessFully": "मालमत्ता यशस्वीरित्या अद्यतनित केली"}, "propertyText": "मालमत्ता", "wardName": "वॉर्ड नाव", "paidTax": "भरलेला कर", "remainderTax": "<PERSON><PERSON><PERSON><PERSON> कर", "enterPropertyNumber": " मालमत्ता क्रमांक किंवा जुना मालमत्ता क्रमांक प्रविष्ट करा", "financialYear": "आर्थिक वर्ष", "noDataFound": "कोणताही डेटा उपलब्ध नाही", "selectFinancialYear": "कृपया आर्थिक वर्ष निवडा", "selectYear": "आर्थिक वर्ष निवडा", "billGenerate": "बिल यशस्वीरीत्या निर्माण झाले", "viewBill": "बिल पहा", "zone": {"zoneTitle": "झोन मास्टर", "searchPlaceholder": "झोन शोधा", "searchZone": "झोन शोधा", "zoneName": "झोनचे नाव", "wardName": "प्रभागाचे नाव", "edit": "संपादन करा", "delete": "काढा", "AddBtn": "झोन जोडा", "addZone": "झोन", "selectWard": "प्रभाग निवडा", "formTitle": "झोन मास्टर", "zoneLabel": "झोन", "wardLabel": "प्रभाग", "updateBtn": "झोन अद्यतनित करा", "selectZone": "झोन निवडा"}, "ward": {"wardTitle": "प्रभाग मास्टर", "searchPlaceholder": "प्रभाग शोधा", "searchWard": "प्रभाग शोधा", "wardName": "प्रभागाचे नाव", "edit": "संपादन करा", "delete": "काढा", "AddBtn": "प्रभाग जोडा", "selectWard": "प्रभाग निवडा", "formTitle": "प्रभाग मास्टर", "wardLabel": "प्रभाग", "updateBtn": "प्रभाग अद्यतनित करा"}, "location": {"locationTitle": "ठिकाण मास्टर", "searchPlaceholder": "ठिकाण शोधा", "searchLocation": "ठिकाण शोधा", "locationName": " ठिकाण नाव", "AddBtn": "ठिकाण ऍड करा ", "edit": "संपादन करा", "delete": "काढा", "locationLabel": "ठिकाण", "formTitle": "ठिकाण मास्टर", "updateBtn": "ठिकाण अद्यतनित करा"}, "street": {"streetTitle": "रोड मास्टर", "searchPlaceholder": "रोड शोधा", "searchLocation": "रोड शोधा", "streetName": "रोड नाव", "AddBtn": "रोड ऍड करा", "edit": "संपादन करा", "delete": "काढा", "streetLabel": "रोड", "formTitle": "रोड मास्टर", "updateBtn": "रोड अद्यतनित करा"}, "propertytype": {"propertyTypeTitle": "मालमत्ता प्रकार मास्टर", "searchPlaceholder": "मालमत्ता प्रकार शोधा", "searchLocation": "मालमत्ता प्रकार शोधा", "propertytypeName": " मालमत्ता प्रकार नाव", "AddBtn": "मालमत्ता प्रकार ऍड करा", "edit": "संपादन करा", "delete": "काढा", "propertytypeLabel": "मालमत्ता प्रकार", "formTitle": "मालमत्ता प्रकार मास्टर", "updateBtn": "मालमत्ता प्रकार अद्यतनित करा"}, "propertyclass": {"propertyTypeTitle": "मालमत्ता प्रकार मास्टर", "searchPlaceholder": "मालमत्ता प्रकार शोधा", "searchLocation": "मालमत्ता प्रकार शोधा", "propertytypeName": " मालमत्ता प्रकार नाव", "AddBtn": "मालमत्ता प्रकार ऍड करा", "edit": "संपादन करा", "delete": "काढा", "propertytypeLabel": "मालमत्ता प्रकार", "formTitle": "मालमत्ता प्रकार मास्टर", "updateBtn": "मालमत्ता प्रकार अद्यतनित करा"}, "propertyfloor": {"propertyFloorTitle": "मालमत्ता मजला मास्टर", "searchPlaceholder": "मज<PERSON>ा शोधा", "searchLocation": "मज<PERSON>ा शोधा", "propertyfloorName": " मालमत्ता मजला नाव", "AddBtn": "मालमत्ता मजला ऍड करा", "edit": "संपादन करा", "delete": "काढा", "propertytypeLabel": "मालमत्ता प्रकार", "formTitle": "मालमत्ता प्रकार मास्टर", "updateBtn": "मालमत्ता प्रकार अद्यतनित करा", "floorLabel": "मजला"}, "propertysubtype": {"propertySubTypeTitle": "मालमत्ता उप-प्रकार मास्टर", "searchPlaceholder": "मालमत्ता उप-प्रकार शोधा", "searchLocation": "मालमत्ता उप-प्रकार शोधा", "propertysubtypeName": " मालमत्ता उप-प्रकार नाव", "AddBtn": "मालमत्ता उप-प्रकार ऍड करा", "edit": "संपादन करा", "delete": "काढा", "propertysubtypeLabel": "मालमत्ता उप-प्रकार", "formTitle": "मालमत्ता उप-प्रकार मास्टर", "updateBtn": "मालमत्ता उप-प्रकार अद्यतनित करा"}, "areaorlocality": {"areaTitle": "क्षेत्र मास्टर", "searchPlaceholder": "क्षेत्र शोधा", "searchLocation": "क्षेत्र शोधा", "areaName": " क्षेत्र नाव", "AddBtn": "क्षेत्र ऍड करा", "edit": "संपादन करा", "delete": "काढा", "areaLabel": "क्षेत्र", "formTitle": "क्षेत्र मास्टर", "updateBtn": "क्षेत्र अद्यतनित करा"}, "electionboundry": {"electionBoundaryTitle": "निवडणूक सीमा मास्टर", "searchPlaceholder": "निवडणूक सीमा शोधा", "searchLocation": "निवडणूक सीमा शोधा", "AddBtn": "निवडणूक सीमा ऍड करा", "formTitle": "निवडणूक सीमा मास्टर", "updateBtn": "निवडणूक सीमा अद्यतनित करा", "electionBoundaryLabel": "निवडणूक सीमा", "boundaryLabel": " निवडणूक सीमा"}, "usage": {"usageTitle": "वापर मास्टर", "searchPlaceholder": "वापर शोधा", "searchLocation": "वापर शोधा", "AddBtn": "वापर ऍड करा", "formTitle": "वापर मास्टर", "updateBtn": "वापर अद्यतनित करा", "UsageLabel": "वापर"}, "usage_sub": {"usageSubTitle": "उपयोग सब मास्टर", "searchPlaceholder": "शोधा", "searchLocation": "शोधा", "AddBtn": "ऍड करा", "formTitle": "वापर सब मास्टर", "updateBtn": "अद्यतनित करा", "UsageLabel": "वापर सब"}, "construction": {"constructionTitle": "बांधकाम मास्टर", "searchPlaceholder": "बांध<PERSON>ाम शोधा", "searchLocation": "बांधकाम प्रकार शोधा", "constructionName": "बांध<PERSON>ाम प्रकार नाव", "AddBtn": "बांधकाम प्रकार जोडा", "edit": "संपादन करा", "delete": "काढा", "constructionLabel": "बांधकाम वर्ग", "formTitle": "बांधकाम मास्टर", "updateBtn": "बांधकाम प्रकार अद्यतनित करा", "classNameLabel": "बांधकाम वर्ग", "classNamePlaceholder": "बांधकाम वर्ग प्रविष्ट करा", "classNameMarathiLabel": "मराठीत बांधकाम वर्ग", "classNameMarathiPlaceholder": "मराठीत बांधकाम वर्ग प्रविष्ट करा", "valueLabel": "मूल्य", "valuePlaceholder": "मूल्य प्रविष्ट करा"}, "administrativeboundary": {"administrativeBoundaryTitle": "प्रशासनिक सीमा मास्टर", "searchPlaceholder": "प्रशासनिक सीमा शोधा", "searchLocation": "प्रशासनिक सीमा प्रकार शोधा", "boundaryName": "प्रशासनिक सीमा प्रकार नाव", "AddBtn": "प्रशासनिक सीमा ऍड करा ", "edit": "संपादन करा", "delete": "काढा", "boundaryLabel": "प्रशासनिक सीमा प्रकार", "formTitle": "प्रशासनिक सीमा मास्टर", "updateBtn": "प्रशासनिक अद्यतनित करा", "classNameLabel": "प्रशासनिक सीमा वर्ग नाव", "classNamePlaceholder": "प्रशासनिक सीमा वर्ग नाव प्रविष्ट करा", "classNameMarathiLabel": "मराठीत प्रशासनिक सीमा वर्ग नाव", "classNameMarathiPlaceholder": "मराठीत प्रशासनिक सीमा वर्ग नाव प्रविष्ट करा", "valueLabel": "मूल्य", "valuePlaceholder": "मूल्य प्रविष्ट करा", "administrativeboundaryColumn": "प्रशासनिक सीमा"}, "contactUs": {"addressTitle": "पत्ता", "address": "छत्रपती संभाजी चौक, एमएच एसएच १३७, शिरोळ", "emailTitle": "ईमेल आयडी", "emailId": "<EMAIL>", "openingTitle": "कार्यालयाची वेळ", "opening": "सोमवार - शनिवार", "leaveCommentTitle": "संपर्क साधा", "yourName": "नाव", "yourNamePlaceholder": "तुमचे नाव प्रविष्ट करा", "yourEmail": "ईमेल", "yourEmailPlaceholder": "तुमचा ईमेल प्रविष्ट करा", "yourSubject": "विषय", "yourSubjectPlaceholder": "तुमचा विषय प्रविष्ट करा", "yourMessage": "संदेश", "yourMessagePlaceholder": "तुमचा संदेश प्रविष्ट करा", "submitMessage": "संदेश सबमिट करा"}, "master": {"masterZone": "झोन", "masterLocation": "ठिकाण ", "masterWard": "प्रभाग ", "masterStreet": "रोड ", "masterArea": "क्षेत्र ", "masterPropertyType": "मालमत्ता प्रकार ", "masterPropertySubtype": "मालमत्ता उपप्रकार ", "masterElectionBoundary": "निवडणूक सीमा ", "masterUsage": "वापर ", "masterUsageSub": "वापर सब ", "masterConstruction": "बा<PERSON><PERSON><PERSON>ाम ", "masterAdministrativeBoundary": "प्रशासनिक सीमा ", "masterReadyReckonerRate": "बाजार मूल्य ", "masterPropertyTaxRate": "मिळकत कर दर  ", "propertyTaxRate": "मालमत्ता कर दर", "floor": "मजला", "propertyclass": "मालमत्ता प्रकार विभाग", "collector": "कलेक्टर"}, "titles": {"dashboard": "डॅशबोर्ड", "propertyRegistration": "मालमत्ता नोंदणी", "master": "मास्टर", "transaction": "व्यवहार", "report": "अहवाल", "logout": "लॉग आउट", "determinationDepartment": "निर्धारण विभाग", "userRegister": "वापरकर्ता नोंदवा", "roleRegistration": "भूमिका नोंदवा", "taxGeneration": "कर निर्मिती", "namuna8": "मालमत्ता कर आकारणी(८)", "namuna9": "वार्षिक कर मागणी(९)", "namuna10": "नमुना १०", "paymentBill": "भरणा बिल", "paymentBillHistory": "बिलांचे तपशील", "settings": "सेटिंग्ज", " paymentReport ": "देयक अहवाल"}, "Bill": "बिल", "user": {"firstName": "पहिले नाव", "lastName": "आडनाव", "email": "ईमेल", "phoneNumber": "फोन नंबर", "role": "भूमिका", "search": "शोध", "address": "पत्ता", "password": "पासवर्ड", "userTitle": "वापरकर्ता माहिती"}, "propertyLocationDetailsForm": {"propertyLocationTitle": "मालमत्ता तपशील", "propertyNumber": "मालमत्ता क्रमांक", "buildingNumber": "इमारत क्रमांक", "citySurveyNumber": "शहरी सर्वेक्षण क्रमांक", "propertyStatus": "मालमत्ता स्थिती", "detailsLocationProperty": "मालमत्ता ठिकाण", "plotNumber": "प्लॉट क्रमांक", "blockNumber": "ब्लॉक क्रमांक", "houseNumber": "घर क्रमांक", "location": "ठिकाण", "houseApartmentName": "घर किंवा अपार्टमेंटचं नाव", "streetRoad": "रोड", "landmark": "संकेतस्थळ", "country": "देश", "city": "<PERSON><PERSON><PERSON>", "latitude": "अक्षांश", "longitude": "रेखांश", "ward": "प्रभाग", "zone": "झोन", "administrativeBoundary": "प्रशासनिक सीमा", "electionBoundary": "निवडणूक सीमा", "documentDetails": "कागदपत्रांचा तपशील", "uploadFile": "फाइल अपलोड करा", "sequenceNumber": "अनुक्रम क्रमांक", "propertyDescription": "मालमत्ता वर्णन", "DetailsOfLocationOfProperty": "मालमत्ता ठिकाण", "area": "क्षेत्र", "fileUploadTitle": "मालमत्तेची कागदपत्रे", "propertyOldNumber": "मालमत्ता  जुना क्रमांक ", "gatNumber": "गट नं. ", "register": "रजिस्टर क्रमांक"}, "assetDetailsForm": {"assetDetailsTitle": "संपत्तीचे तपशील", "firstName": "पहिले नाव", "middleName": "मधले नाव", "lastName": "आडनाव", "organization": "संस्था", "mobileNumber": "मोबाइल नंबर", "email": "ईमेल आयडी", "adharNumber": "आधा<PERSON> नंबर", "panNumber": "पॅन नंबर", "gender": "लिंग", "ownerType": "मालमत्ता धारक प्रकार", "fullName": "संपूर्ण नाव", "marrageStatus": "वैवाहिक स्थिती"}, "propertyAssessmentDetailsForm": {"propertyAssessmentDetailsTitle": "मालमत्ता मूल्यांकन तपशील", "plotArea": "प्लॉट क्षेत्र", "propertyType": "मालमत्ता प्रकार", "propertySubType": "मालमत्ता उपप्रकार", "propertyUsageType": "मालमत्ता वापर प्रकार", "propertyUsageSubType": "घनकचरा संकुलन ", "billType": "<PERSON><PERSON>ल प्रकार", "deliveryType": "वितरण प्रकार", "propertyCurrentAssessment": "मालमत्ता वर्तमान मूल्यांकन", "firstAssessment": "पहिलं मूल्यांकन", "propertyConstructionStartDate": "मालमत्ता बांधकाम सुरूवात तारीख", "propertyConstructionEndDate": "मालमत्ता बांधकाम समाप्ती तारीख"}, "plotDetailsForm": {"plotDetailsTitle": "प्लॉट तपशील", "flatStatus": "फ्लॅट स्थिती", "floorNumber": "मजला ", "flatNumber": "फ्लॅट क्रमांक", "occupancyType": "वसती प्रकार", "constructionClass": "निर्माण वर्ग", "usageType": "वापर प्रकार", "usageSubType": "घनकचरा संकुलन ", "taxStartDate": "कर सुरूवात तारीख", "roomDetailsInfo": "तप<PERSON>ील माहिती", "roomDetails": "तपशील", "manualAreaFlag": "मॅन्युअल क्षेत्र निर्देशांक", "constructionArea": "बांधकाम क्षेत्र", "carpetArea": "कारपेट क्षेत्र", "exemptedArea": "मुक्त क्षेत्र", "assessableArea": "मूल्यांकन क्षेत्र", "plotArea": "प्लॉट क्षेत्र", "landCost": "जमिनीची किंमत", "standardRate": "मान<PERSON> दर", "annualRent": "वार्षिक भाडे", "capitalValue": "पूंजी मूल्य", "remark": "रिमार<PERSON>क", "PropertyPhotographs": "मालमत्ता चित्रे"}, "property": {"AddBtn": "मालमत्ता ऍड करा", "UpdateBtn": "मालमत्ता अद्यतनित करा", "nextBtn": "पुढे", "previousBtn": "मागे", "propertyNumberColumn": "मालमत्ता क्रमांक", "propertyOwnerColumn": "मालमत्ता धारक", "propertyTypeColumn": "मालमत्ता प्रकार", "propertyMobileColumn": "मोबाइल नंबर", "bhogwatadar": "भोगवटादार ", "oldPropertyNumber": "जुना मालमत्ता क्रमांक ", "usageDetails": "वापर प्रकार", "propertytype": "मालमत्ता प्रकार", "surveyAndGatNumber": "सर्वे नंबर व गट नं", "bhogwatadarName": "भोगवटा धारकाचे नाव"}, "propertyOwner": "मालमत्ता धारक", "wardcolumn": "प्रभाग", "zonecolumn": "झोन", "welcomeText": "{{name}}, तुमचं स्वागत आहे, ", "goodMorning": "शुभ प्रभात!", "goodAfternoon": "शुभ दुपार!", "goodnight": "शुभ रात्री!", "toDate": "या तारखेपर्यंत ", "fromDate": "या तारखेपासून", "totalProperty": "एकूण मालमत्ता", "propertySummary": "मालमत्ता सारांश", "taxCollected": "कर वसूल", "taxisdue": "<PERSON>र <PERSON>ा<PERSON>", "Fullfilledvsreality": "पूर्ण तुलनेत वास्तविकता", "Typeofproperty": "मालमत्तेचे प्रकार", "Recenttransactionscollectedbycollectors": "कलेक्टर्सने गोळा केलेली अलीकडील लेन-देन", "propertyDetails": "मालमत्ता तपशील", "ward_name": "प्रभाग", "noResults": "कोणतेही रेकॉर्डस नाहीत", "availableAssets": "उपलब्ध संपत्ती", "searchward": "प्रभाग शोधा", "searchZone": "झोन शोधा", "searchLocation": "ठिकाण शोधा", "searchArea": "क्षेत्र शोधा", "searchStreet": "रोड शोधा", "searchPropertyType": "मालमत्ता प्रकार शोधा", "searchPropertySubType": "मालमत्ता उपप्रकार शोधा", "searchElectionBoundry": "निवडणूक सीमा शोधा", "searchUsage": "वापर शोधा", "searchUsageSubtype": "घनकचरा संकुलन  शोधा", "searchAdministrativeBoundry": "प्रशासकीय सीमा शोधा", "searchConstructionClass": "बांधकाम वर्ग शोधा", "searchPropertyOwner": "मालमत्ता धारक शोधा", "payTaxListTitle": "वेळेवर कर भरलेले मालमत्ताधारक", "dueTaxListTitle": "वेळेवर कर न भरलेले मालमत्ता धारक ", "viewMore": "अधिक तपशील पहा", "searchReadyRecoknerRate": "बाजार मूल्य शोधा", "searchFirstName": "नाव शोधा", "deleteSuccess": "यशस्वीरित्या हटवले गेले", "propertyAssessment": {"incomeDescription": "मिळकतीचे वर्णन", "constructionCompletionCertificate": "बांधकाम पूर्णत्वाचा दाखला", "privateToilet": "वयक्तिक शौचालय", "toiletType": "शौचालय प्रकार", "totleNo": "एकूण संख्या", "lightFixture": "दिवाबत्ती सोय", "faucetConnection": "नळ कनेक्शन", "rooftopSolarPowerConnection": "छतावरील सौर ऊर्जा प्रकल्प", "rainWaterHarvestingProject": "पावसाच्या पाण्याची साठवण प्रकल्प", "drainageSystem": "गटाराची व्यवस्था आहे का?", "constructionAreaOfGroundFloor": "तळ मजल्याचे बांधकाम क्षेत्रफळ", "restGroundFloorfreeSpace": "तळ मजला उर्वरित मोकळी जागा", "yes": "हो", "no": "नाही"}, "api": {"login": "लॉगिन यशस्वी झाले ", "loginfailed": "लॉगिन अयशस्वी झाले. कृपया आपले पर्याय तपासून पहा आणि पुन्हा प्रयत्न करा", "invalidotp": "अवैध OTP", "fetch": "डेटा मिळवले", "error": "कृपया आपले पर्याय तपासून पहा आणि पुन्हा प्रयत्न करा", "update": "डेटा अद्यतनित केले", "create": "डेटा तयार केले", "delete": "डेटा यशस्वीरित्या हटविले", "formupdate": "{{name}} यशस्वीरीत्या अद्यतनित केला गेला आहे", "formcreate": "{{name}} यशस्वीरीत्या तयार केला गेला आहे", "formdelete": "{{name}} यशस्वीरीत्या हटवला गेला आहे", "financialYearGenerated": "आर्थिक वर्ष तयार झाले", "generating": "तयार करत आहे...", "generate": "तया<PERSON> करा"}, "dashboard": {"usagewiseProperty": "वापराच्या प्रकारानुसार मालमत्ता", "wardwiseProperty": "वॉर्डनुसार मालमत्ता"}, "validations": {"email": {"emailRequiredError": "ईमेल आवश्यक आहे", "emailInvalidError": "अवैध ईमेल"}, "password": {"passwordLengthError": "पासवर्ड किमान 5 अक्षरांच्या असणे आवश्यक आहे", "passwordLowercaseError": "पासवर्ड किमान एक लोअरकेस अक्षर असणे आवश्यक आहे", "passwordUppercaseError": "पासवर्ड किमान एक अपरकेस अक्षर असणे आवश्यक आहे", "passwordNumberError": "पासवर्ड किमान एक संख्या असणे आवश्यक आहे", "passwordSpecialCharError": "पासवर्ड किमान एक विशेष वर्ण असणे आवश्यक आहे"}, "otp": "अवैध ओटीपी"}, "addRole": "भूमिका ऍड करा", "searchrole": "भूमिका शोधा", "alreadyexist": "आधीच अस्तित्वात आहे", "roleName": "भूमिकेचे नाव", "permissionAccess": "परवानगी प्रवेश", "action": "कृती", "createRole": "भूमिका ऍड करा", "enterRoleName": "भूमिकेचे नाव प्रविष्ट करा", "roleDeletedSuccessfully": "भूमिका यशस्वीरित्या हटवली गेली", "failedToDeleteRole": "भूमिका हटविण्यात अयशस्वी", "invalidRoleName": "अवैध भूमिका नाव किंवा भूमिका आधीच अस्तित्वात आहे", "selectRole": "भूमिका निवडा", "administrative": "प्रशासकीय", "nonAdministrative": "अप्रशासकीय", "createRoleTitle": "भूमिका तयार करा: ", "roleNamePlaceholder": "भूमिकेचे नाव", "permissionsSavedSuccess": "परवानग्या यशस्वीरित्या जतन केल्या", "permissionsSavedFail": "परवानग्या जतन करण्यात अयशस्वी", "saveRole": "भूमिका जतन करा", "permission": "परवानगी", "view": "पहा", "add": "ज<PERSON><PERSON> करा", "create": "तया<PERSON> करा", "update": "अपडेट करा", "delete": "हटवा", "save": "ज<PERSON><PERSON> करा", "cancel": "र<PERSON><PERSON><PERSON> करा", "noResultsFound": "कोणतेही रेकॉर्ड्स नाहीत!", "readyreckonerrate": {"fromDate": "या तारखेपासून", "toDate": "या तारखेपर्यंत ", "pickaDate": "तारीख निवडा", "surveyNumber": "सर्वेक्षण क्रमांक", "readyreckoner": "बाजार मूल्य दर", "valueRate": "मूल्य दर", "readyreckonerrateTitle": "बाजार मूल्य मास्टर", "searchPlaceholder": "बाजार मूल्य शोधा", "searchLocation": "बाजार मूल्य प्रकार शोधा", "AddBtn": "बाजार मूल्य ऍड करा ", "edit": "संपादन करा", "delete": "काढा", "formTitle": "बाजार मूल्य माहिती", "updateBtn": "बाजार मूल्य अद्यतनित करा", "valueLabel": "मूल्य", "valuePlaceholder": "मूल्य प्रविष्ट करा"}, "propertytaxrate": {"fromDate": "या तारखेपासून", "toDate": "या तारखेपर्यंत", "pickaDate": "तारीख निवडा", "surveyNumber": "सर्वेक्षण क्रमांक", "propertyTaxRate": "मालमत्ता कर दर", "valueRate": "मूल्य दर", "propertyTaxRateTitle": "मालमत्ता कर दर मास्टर", "searchPlaceholder": "मालमत्ता कर शोधा", "searchLocation": "मालमत्ता कर प्रकार शोधा", "AddBtn": "मालमत्ता कर दर ऍड करा", "edit": "संपादित करा", "delete": "हटवा", "formTitle": "मालमत्ता कर दर माहिती", "updateBtn": "मालमत्ता कर दर अद्यतनित करा", "valueLabel": "मूल्य", "valuePlaceholder": "मूल्य प्रविष्ट करा", "taxMin": "मालमत्ता  कर (किमान)", "taxMax": "मालमत्ता  कर (कमा<PERSON>)", "municipalTax": "नगरपरिषदेने ठरवलेला कर"}, "deletescreen": {"title": "तुमची खात्री आहे की तुम्ही हे रेकॉर्ड कायमचे हटवू इच्छिता?", "description": "", "yes": "होय", "no": "नाही"}, "table": {"edit": "संपादित करा", "delete": "काढा", "view": "मालमत्ता पहा", "printReport": "अहवाल छापा", "back": "मागे "}, "paymentform": {"title": "तुमची देणी जाणून घ्या", "taxpayerNumberLabel": "करदाता क्रमांक", "billNo": "बिल नंबर", "taxpayerFullNameLabel": "करदाता पूर्ण नाव", "taxDetailsHeader": "कर तपशील (₹)", "amountHeader": "रक्कम (₹)", "propertyTax": "संपत्ती कर (घरपट्टी)", "streetLightTax": "दिवाबत्ती कर", "healthTax": "आरोग्य कर", "drainageTax": "पडसर कर", "treeTax": "वृक्ष कर", "educationTax": "शिक्षण कर", "employmentGuaranteeTax": "रोज<PERSON><PERSON><PERSON> हमी कर", "fireTax": "अग्निशमन कर", "waterTax": "पाणीपट्टी", "solidWasteTax": "घनकचरा शुल्क", "penaltyTax": "शास्ती कर", "penalty": "दंड", "interest": "व्याज", "advertisementTax": "जाह<PERSON>रा<PERSON> कर", "warrantFee": "वारंट फी", "other": "इतर", "totalPayment": "एकूण पेमेंट", "printButton": "प्रिंट करा", "messagePlaceholder": "ग्राहकाला संदेश पाठवा", "sendMessageButton": "संदेश पाठवा", "choosePaymentMethod": "पेमेंट पद्धत निवडा", "card": "कार्ड", "userinfo": "", "cardNumber": "कार्ड क्रमांक", "cardholderName": "कार्डधारकाचे नाव", "expiryDate": "समाप्ती तारीख", "cvv": "सीवीवी", "upi": "यूपीआय", "enterUpiId": "यूपीआय आयडी प्रविष्ट करा", "paypal": "पेपाल", "enterPaypalEmail": "पेपाल ईमेल प्रविष्ट करा", "netbanking": "नेटबँकिंग", "bankName": "बँकचे नाव", "accountHolderName": "खातेधारकाचे नाव", "accountNumber": "खाते क्रमांक", "ifscCode": "IFSC कोड", "proceedToPayButton": "पेमेंट करण्यास सुरुवात करा", "razorpay": "रेझरपे", "razorpayDescription": "तुमचे पेमेंट पूर्ण करण्यासाठी तुम्हाला रेझरपेवर पुनर्निर्देशित केले जाईल."}, "DashboardTable": {"name": "नाव", "targetReached": "लक्ष्य गाठले", "taxCollected": "कर संकलित", "amount": "रक<PERSON>कम", "pendingDays": "प्रलंबित दिवस"}, "taxDemand": {"taxDemand": "कर निर्मिती", "selectRange": "वर्ष निवडा ", "year": "वर्ष", "progress": "प्रगती", "propertyCount": "मालमत्ता संख्या", "view": "मालमत्ता पहा", "publish": "प्रका<PERSON>ित करा", "unPublish": "अप्रकाशित करा", "generateBill": "बिल तयार करा", "warshiKCarCount": "वार्षिक कर संख्या", "billsGenerated": "तयार झालेली बिलं", "recordGeneratedForProperty": "कृपया वार्षिक कर मागणी(९) वर जा आणि या मालमत्तेसाठी वार्षिक कर मागणी तयार करा"}, "publish": "आपल्याला हे प्रकाशित करायचे आहे याची खात्री आहे का?", "unpublish": "आपल्याला हे अप्रकाशित करायचे आहे याची खात्री आहे का?", "selectAnyFieldNote": "टीप : कोणतीही एक फिल्ड निवडा.", "allFieldAreRequire": "टीप : सर्व फील्ड आवश्यक आहेत", "selectAnyTwoFieldNote": "टीप : कोणत्याही दोन फिल्ड निवडा.", "recordGenerated": "रेकॉर्ड यशस्वीरित्या तयार झाले", "tryAgainLater": "कृपया काही वेळानंतर पुन्हा प्रयत्न करा", "contactAdministrator": "कृपया प्रशासकाशी संपर्क साधा.", "Generating": "निर्माण करत आहे", "namunaEight": {"milkatKarAkarni": "मालमत्ता  कर आकारणी(८)", "namunaEightCheckboxOneValue": "नवीन कर आकारणी करताना जुन्या कर आकारणीच्या ३० टक्के पेक्षा कमी कर लावणे", "namunaEightCheckboxTwoValue": "५० % कर", "namunaEightCheckboxThreeValue": "जुना कर घ्यावयाचा असल्यास", "milkatDharkachiMahiti": "मालमत्ता  धारकाची माहिती", "IncomeNumber": "मालमत्ता  क्रमांक", "oldIncomeNumber": "जुना मालमत्ता  क्रमांक", "incomeType": "मालमत्ता  प्रकार", "surveyNumber": "सर्वे नंबर", "streetName": "रस्त्याचे नाव", "waterType": "पाणी प्रकार", "incomeHolderName": "मालमत्ता धारकाचे नाव", "occupancyHolderName": "भोगवटा धारकाचे नाव", "tableHeading": "मिळकतीचे वर्णन", "tableIncomeNumber": "मि नं", "tableIncomeType": "मि प्रकार", "tableLengthHeading": "लांबी", "tableWidthHeading": "रुंदी", "tableAreaHeading": "क्षेत्रफळ", "tableTaxRateHeading": "कर दर", "tableDepreciationRateHeading": "घसारा दर", "tableWeightingHeading": "भारा<PERSON><PERSON> ", "tableTotalTaxHeading": "एकूण कर (₹)", "tableIncomeOtherInfoHeading": "मि इतर माहिती", "buildingTax": "इमारत कर", "lightTax": "दिवाबत्ती  कर", "healthTax": "आरोग्य कर", "newTaxationTotal": "नवीन कर आकारणी प्रमाणे एकूण", "commonWatershed": "सामान्य पाणीपट्टी", "capitalValue": "भांडवली मूल्य", "reducedAmount30%": "३०% प्रमाणे कमी केलेली रक्कम(-)", "solidWasteCharges": "घनकचरा शुल्क", "penaltyCharges": "शास्ती शुल्क", "totalTax": "एकूण कर", "newTaxRate": "नवीन कर आकार", "incomeNote": "मिळकतीवरील शेरा नोंद", "occupancyHolder": "भोगवटादार", "propertyNumber": "मालमत्ता क्रमांक", "propertyDescription": " मालमतेचे वर्णन", "taxesOnBuildings": "इमारतीवरील कर(₹)", "pastDue": "मा<PERSON><PERSON><PERSON> बाकी", "currentTax": "चालू कर", "total": "एकूण", "totalAmount": "एकूण रक्कम", "penaltyAmount": "दंड रक्कम", "taxDemand": "कर मागणी"}, "userGuide": {"title": "अॅप्लिकेशन वापरकर्ता मार्गदर्शक", "description": "शिरोळ नगरपरिषद अॅप्लिकेशन कसे वापरावे ते शिका", "tabs": {"general": "सामान्य", "property": "मालमत्ता व्यवस्थापन", "tax": "कर भरणा", "admin": "प्र<PERSON>ासन"}, "general": {"title": "प्रार<PERSON>भ करणे", "item1": "मालमत्ता क्रमांक किंवा मालकाच्या नावाने मालमत्ता शोधण्यासाठी शोध कार्य वापरा", "item2": "साइडबार मेनू वापरून अॅप्लिकेशनमध्ये नेव्हिगेट करा", "item3": "नेव्हबारमधील A+/A- बटणे वापरून फॉन्ट आकार समायोजित करा", "item4": "संपर्क आम्हाला पृष्ठाद्वारे समर्थनाशी संपर्क साधा"}, "property": {"title": "मालमत्ता व्यवस्थापन", "item1": "मालमत्ता नोंदणी फॉर्मद्वारे नवीन मालमत्ता नोंदवा", "item2": "मालमत्ता क्रमांक किंवा मालकाच्या तपशीलांचा वापर करून विद्यमान मालमत्ता शोधा", "item3": "मालकीच्या माहितीसह मालमत्ता तपशील पहा आणि अद्यतनित करा", "item4": "मालमत्ता हस्तांतरण विभागाद्वारे मालमत्ता हस्तांतरण प्रक्रिया करा"}, "tax": {"title": "कर व्यवस्थापन", "item1": "कर निर्मिती वैशिष्ट्याचा वापर करून मालमत्ता कर तयार करा", "item2": "नमुना 8 फॉर्ममध्ये कर गणना तपशील पहा", "item3": "नमुना 9 फॉर्मद्वारे वार्षिक कर विनंत्या प्रक्रिया करा", "item4": "पेमेंट विभागाद्वारे ऑनलाइन कर भरा", "item5": "पेमेंट इतिहास आणि पावत्या पहा"}, "admin": {"title": "प्र<PERSON>ासन", "item1": "वापरकर्ता नोंदणी विभागाद्वारे वापरकर्त्यांचे व्यवस्थापन करा", "item2": "विशिष्ट परवानग्यांसह भूमिका तयार करा आणि व्यवस्थापित करा", "item3": "झोन, वॉर्ड आणि वापर प्रकार यासारखा मास्टर डेटा कॉन्फिगर करा", "item4": "डॅशबोर्ड अहवाल आणि आकडेवारी पहा"}, "sidebar": {"title": "साइडबार मेनू वापरकर्ता मार्गदर्शिका", "description": "साइडबार मेनूमधील विविध विकल्पांचा वापर कसा करावा याबद्दल माहिती", "step1": "डॅशबोर्ड: सामान्य माहिती आणि अहवाल पहा.", "step2": "निश्चितीपत्र विभाग: निश्चितीपत्र तयार करा आणि प्रिंट करा.", "step3": "मालमत्ता नोंदणी: नवीन मालमत्ता नोंदवा.", "step4": "मालमत्ता कर आकारणी: मालमत्ता कर आकारणी तयार करा.", "step5": "वार्षिक कर विनंती: वार्षिक कर विनंती प्रक्रिया करा.", "step6": "नवीन नोंदणी: नवीन वापरकर्ता नोंदणी करा.", "step7": "मालमत्ता फेरफार: मालमत्ता फेरफार प्रक्रिया करा.", "step8": "मालमत्ता फॉर्म: मालमत्ता संबंधित फॉर्म भरा.", "step9": "कर भरणा: ऑनलाइन कर भरणा करा.", "step10": "सेटिंग्ज: अॅप्लिकेशन सेटिंग्स समायोजित करा."}}, "namunaNine": {"milkatKarAkarni": "वार्षिक कर मागणी(९)"}, "total": "एकूण", "propertyView": {"basicInformation": "मुलभूत माहिती", "SrNo": "अ. क्र.", "ward": "प्रभाग", "zone": "झोन", "street": "रस्ता", "plotArea": "प्लॉट क्षेत्र", "GisNumber": "GIS क्रमांक", "ghanKachara": "घन कचरा", "plotConstructionArea": "प्लॉट बांधकाम क्षेत्र", "plotEmptyArea": "प्लॉट रिक्त क्षेत्र", "ownerDetails": "मालमत्ता धारकाची माहिती", "propertyDetails": "मालमत्ता तपशील", "taxDetails": "कर तपशील", "documents": "कागदपत्रे", "citySurveyNumber": "शहरी सर्वेक्षण क्रमांक", "houseName": "घराचे नाव", "latitude": "अक्षांश", "longitude": "रेखांश", "namunaEight": "नमुना (८)", "namunaNine": "नमुना (९)", "waste_tax": "घनकचरा ", "ownerDetail": "मालक तपशील"}, "ownerDetails": {"SrNo": "अ. क्र.", "name": "नाव", "type": "प्र<PERSON><PERSON>र", "mobileNumber": "मोबाइल नंबर", "emailId": "ईमेल आयडी", "adharNumber": "आधा<PERSON> नंबर"}, "propertyDetail": {"SrNo": "अ. क्र.", "propertyType": "मालमत्ता प्रकार", "constructionDate": "बांधकाम तारीख", "usageType": "वापर प्रकार", "usageSubType": "घनकचरा संकुलन", "length": "लांबी", "width": "रुंदी", "areaSqFt": "क्षेत्र (चौ. फू.)", "areaSqMt": "क्षेत्र (चौ. मी.)", "propertyStatus": "मालमत्ता स्थिती"}, "taxDetails": {"date": "तारीख", "taxYear": "कर वर्ष", "amount": "रक<PERSON>कम", "status": "स्थिती"}, "documents": {"SrNo": "अ. क्र.", "documentName": "नाव", "documentType": "प्र<PERSON><PERSON>र", "action": "कृती"}, "taxView": {"back": "मागे ", "billNo": "बिल नंबर", "propertyNo": "मालमत्ता क्रमांक", "ownerName": "मालमत्ताधारक", "zone": "झोन", "street": "रस्ता", "totalAmount": "एकूण(₹)"}, "malmattaFerfar": {"SrNo": "अ. क्र.", "propertyNumber": "मालमत्ता क्रमांक", "propertyOldNumber": "मालमत्ता जुना क्रमांक ", "propertyOwner": "मालमत्ता धारक", "occupancyHolder": "भोगवटादार", "mobileNumber": "मोबाइल नंबर", "wardName": "प्रभाग", "zone": "झोन", "usageType": "वापर प्रकार", "propertyType": "मालमत्ता प्रकार", "addPropertyOwner": "मालमत्ता धारक ऍड करा"}, "Value": "दर", "Status": "स्थिति", "setting": {"constructionRateTitle": "रेडी रेकनर बांधकाम दर", "RR-Rate": "रेडी रेकनर दर", "TaxRate": "कर दर", "WeightingRate": "भा<PERSON><PERSON><PERSON><PERSON><PERSON> दर", "solidWasteRate": "घनकचरा दर", "depreciationRate": "घट दर", "bookMaster": "बुक मास्टर", "value": "दर", "status": "स्थिति", "financialYear": "आर्थिक वर्ष", "startDate": "सुरुवातीची तारीख", "startYear": "सुरुवातीचे वर्ष", "endDate": "शेवटची तारीख", "endYear": "शेवटचे वर्ष", "bookNumber": "बुक क्रमांक ", "reAssessment": "पुनर्मूल्यांकन कालावधी", "reassessmentRange": "पुनर्मूल्यांकन कालावधी", "selectReassessment": "पुनर्मूल्यांकन वर्ष निवडा", "fromAge": "वयापासून", "toAge": "वयापर्यंत", "fromAgePlaceholder": "वयापासून प्रविष्ट करा", "toAgePlaceholder": "वयापर्यंत प्रविष्ट करा"}, "bookNumber": "बुक क्रमांक प्रविष्ट करा", "editCollector": "कलेक्टर संपादन करा", "updateCollector": "अद्यतनित करा", "reportModule": {"selectFinancialYear": "कृपया आर्थिक वर्ष निवडा.", "processing": "प्रक्रिया करत आहे...", "download": "डाउनलोड करा", "financial": "आर्थिक", "downloadReportInExcel": "एक्सेलमध्ये अहवाल डाउनलोड करा", "financialYear": "आर्थिक वर्ष", "selectYear": "वर्ष निवडा", "milkatkarReportTitle": "मिळकतकर अहवाल", "milkatkarReportDescription": "निवडलेल्या आर्थिक वर्षासाठी मिळकतकर (मालमत्ता कर) चा सविस्तर अहवाल.", "varshikkarReportTitle": "वार्षिक कर अहवाल", "varshikkarReportDescription": "निवडलेल्या आर्थिक वर्षासाठी सर्वसमावेशक वार्षिक अहवाल.", "paidUserReportTitle": "कर भरलेल्या वापरकर्त्यांचा अहवाल", "paidUserReportDescription": "निवडलेल्या आर्थिक वर्षासाठी कर भरलेल्या वापरकर्त्यांचा अहवाल.", "notPaidUserReportTitle": "कर न भरलेल्या वापरकर्त्यांचा अहवाल", "notPaidUserReportDescription": "निवडलेल्या आर्थिक वर्षासाठी कर न भरलेल्या वापरकर्त्यांचा अहवाल.", "propertyReportTitle": "मालमत्ता अहवाल", "propertyReportDescription": "निवडलेल्या आर्थिक वर्षासाठी मालमत्तांचा सविस्तर अहवाल.", "milkatkarReportDownloaded": "मिळकतकर अहवाल यशस्वीरित्या डाउनलोड झाला.", "milkatkarReportFailed": "मिळकतकर अहवाल डाउनलोड करण्यात अयशस्वी. कृपया पुन्हा प्रयत्न करा.", "varshikkarReportDownloaded": "वार्षिक कर अहवाल यशस्वीरित्या डाउनलोड झाला.", "varshikkarReportFailed": "वार्षिक कर अहवाल डाउनलोड करण्यात अयशस्वी. कृपया पुन्हा प्रयत्न करा.", "paidUserReportDownloaded": "कर भरलेल्या वापरकर्त्यांचा अहवाल यशस्वीरित्या डाउनलोड झाला.", "paidUserReportFailed": "कर भरलेल्या वापरकर्त्यांचा अहवाल डाउनलोड करण्यात अयशस्वी. कृपया पुन्हा प्रयत्न करा.", "notPaidUserReportDownloaded": "कर न भरलेल्या वापरकर्त्यांचा अहवाल यशस्वीरित्या डाउनलोड झाला.", "notPaidUserReportFailed": "कर न भरलेल्या वापरकर्त्यांचा अहवाल डाउनलोड करण्यात अयशस्वी. कृपया पुन्हा प्रयत्न करा.", "propertyReportDownloaded": "मालमत्ता अहवाल यशस्वीरित्या डाउनलोड झाला.", "propertyReportFailed": "मालमत्ता अहवाल डाउनलोड करण्यात अयशस्वी. कृपया पुन्हा प्रयत्न करा."}, "forgotPassword": "पासवर्ड विसरलात?", "verifyOtp": "OTP सत्यापित करा", "resetPassword": "पासवर्ड रीसेट करा", "resetPasswordDesc": "नवीन पासवर्ड सेट करा", "email": "ईमेल", "emailPlaceholder": "आपला ईमेल  टाका", "newPassword": "नवीन पासवर्ड", "newPasswordPlaceholder": "नवीन पासवर्ड टाका", "confirmPassword": "पासवर्ड पुष्टी करा", "confirmPasswordPlaceholder": "पासवर्ड पुन्हा टाका", "sendOtp": "OTP पाठवा", "resendOtp": "O<PERSON> पुन्हा पाठवा", "resendingOtp": "OTP पाठवत आहे...", "backToLogin": "लॉगिन पेजवर परत जा", "step": "चरण", "of": "पैकी", "passwordResetOtpSent": "OTP पाठविला!", "passwordResetOtpSentDesc": "आपल्या ईमेल पत्त्यावर OTP पाठविला गेला आहे", "otpVerified": "OTP सत्यापित झाला!", "otpVerifiedDesc": "आपण आता नवीन पासवर्ड सेट करू शकता", "passwordResetSuccess": "पासवर्ड रीसेट झाला!", "passwordResetSuccessDesc": "आपला पासवर्ड यशस्वीरित्या रीसेट झाला आहे", "otpResent": "OTP पुन्हा पाठविला!", "otpResentDesc": "नवीन OTP आपल्या ईमेल पत्त्यावर पाठविला गेला आहे", "validationss": {"email": {"emailRequiredError": "ईमेल पत्ता आवश्यक आहे", "emailInvalidError": "कृपया वैध ईमेल पत्ता टाका"}, "otp": {"otpRequiredError": "6 अंकी OTP आवश्यक आहे"}, "password": {"minLength": "पासवर्ड किमान 8 अक्षरांचा असावा", "uppercase": "पासवर्डमध्ये किमान एक मोठे अक्षर असावे", "lowercase": "पासवर्डमध्ये किमान एक छोटे अक्षर असावे", "number": "पासवर्डमध्ये किमान एक अंक असावा", "special": "पासवर्डमध्ये किमान एक विशेष चिन्ह असावे", "match": "पासवर्ड जुळत नाही"}}, "newUser": "नविन वापरकर्ता जतन करा", "propertyModification": "मालमता फेरफार", "selectStartYear": "सुरुवातीचे वर्ष निवडा", "selectEndYear": "शेवटचे वर्ष निवडा", "emailIsNotRegistered": "हा ईमेल पत्ता आमच्या प्रणालीमध्ये नोंदलेला नाही."}