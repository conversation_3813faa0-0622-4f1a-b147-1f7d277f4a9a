import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportPropertyupdatecoladdded41724992102460 implements MigrationInterface {
    name = 'ImportPropertyupdatecoladdded41724992102460'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ADD "is_new_property" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "is_new_property"`);
    }

}
