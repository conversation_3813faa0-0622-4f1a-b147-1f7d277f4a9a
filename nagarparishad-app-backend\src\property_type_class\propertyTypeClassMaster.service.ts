import { Injectable, NotFoundException } from '@nestjs/common';
import { PropertyTypeClassMasterRepository } from 'libs/database/repositories/property_type_classMaster.repository';
import { Property_type_class_master } from 'libs/database/entities';
import { CreatePropertyTypeClassDto } from './dto/create-property-type-class.dto';
import { UpdatePropertyTypeClassDto } from './dto/update-property-type-class.dto';

@Injectable()
export class PropertyTypeClassMasterService {
  constructor(
    private readonly propertyTypeClassMasterRepository: PropertyTypeClassMasterRepository
  ) {}

  // Create a new property type class
  async create(createPropertyTypeClassDto: CreatePropertyTypeClassDto): Promise<Property_type_class_master> {
    try {
      const newPropertyTypeClass = await this.propertyTypeClassMasterRepository.saveData(createPropertyTypeClassDto);
      return newPropertyTypeClass;
    } catch (error) {
      throw new NotFoundException('Failed to create property type class');
    }
  }

  // Retrieve all property type classes
  async findAll(): Promise<{ message: string; data: Property_type_class_master[] }> {
    try {
      const allClasses = await this.propertyTypeClassMasterRepository.findAll();
      if (!allClasses.length) {
        throw new NotFoundException('No property type classes found');
      }
      return {
        message: 'Property type classes fetched successfully',
        data: allClasses,
      };
    } catch (error) {
      throw new NotFoundException('Failed to fetch property type classes');
    }
  }

  // Retrieve a single property type class by ID
  async findOne(id: string): Promise<Property_type_class_master> {
    try {
      const propertyTypeClass = await this.propertyTypeClassMasterRepository.findOne({ where: { property_type_class_id: id } });
      if (!propertyTypeClass) {
        throw new NotFoundException(`Property type class with ID ${id} not found`);
      }
      return propertyTypeClass;
    } catch (error) {
      throw new NotFoundException('Failed to fetch property type class');
    }
  }

  // Update a property type class by ID
  async update(id: string, updatePropertyTypeClassDto: UpdatePropertyTypeClassDto): Promise<Property_type_class_master> {
    try {
      const propertyTypeClass = await await this.propertyTypeClassMasterRepository.findOne({ where: { property_type_class_id: id } });
      const updatedClass = Object.assign(propertyTypeClass, updatePropertyTypeClassDto);
      return await this.propertyTypeClassMasterRepository.save(updatedClass);
    } catch (error) {
      throw new NotFoundException('Failed to update property type class');
    }
  }

  // Delete a property type class by ID
  async delete(id: string): Promise<{ message: string }> {
    const propertyTypeClass = await this.findOne(id);
    await this.propertyTypeClassMasterRepository.softDelete(id);
    return { message: 'Property type class deleted successfully' };
  }
}
