/**
 * FRONTEND PERMISSION CONSTANTS
 *
 * This file mirrors the backend permission constants for frontend use.
 * Keep this file synchronized with: nagarparishad-app-backend/src/constants/permission.constants.ts
 *
 * TOTAL MODULES: 6
 * TOTAL FORMS: 47
 * TOTAL ACTIONS: 4
 */

// ============================================================================
// MODULE DEFINITIONS (6 Total)
// ============================================================================
export enum ModuleName {
  Setting = "Setting",
  Master = "Master",
  Property = "Property",
  Payment = "Payment",
  Register = "Register",
  RoleAndUserMangement = "Role and User Mangement",
}

// ============================================================================
// FORM DEFINITIONS (47 Total)
// ============================================================================
export enum FormName {
  // ========== SETTING MODULE FORMS (9 Forms) ==========
  DeprecationRate = "Deprecation Rate",
  RRConstructionRate = "RR Construction Rate",
  RRRate = "RR Rate",
  TaxRate = "Tax Rate",
  WeightingRate = "Weighting Rate",
  GhanKachraRate = "Ghan Kachra Rate",
  BookMaster = "Book Master",
  FinancialYearMaster = "Financial Year Master",
  ReassessmentMaster = "Reassessment Master",

  // ========== MASTER MODULE FORMS (18 Forms) ==========
  Zone = "Zone",
  Ward = "Ward",
  Road = "Road",
  PropertyType = "PropertyType",
  PropertyTypeDepartment = "PropertyType Department",
  Floor = "Floor",
  Usage = "Usage",
  UsageSub = "Usage Sub",
  LocationMaster = "Location Master",
  StreetMaster = "Street Master",
  ElectionBoundryMaster = "Election Boundry Master",
  AreaMaster = "Area Master",
  PropertySubTypeMaster = "Property Sub Type Master",
  PropertyClassMaster = "Property Class Master",
  ConstructionClassMaster = "Construction Class Master",
  CollectorMaster = "Collector Master",
  OwnerTypeMaster = "Owner Type Master",
  AdminBoundaryMaster = "Admin Boundary Master",

  // ========== PROPERTY MODULE FORMS (1 Form) ==========
  Property = "Property",

  // ========== PAYMENT MODULE FORMS (2 Forms) ==========
  PaymentBill = "Payment Bill",
  PaymentLogs = "Payment Logs",

  // ========== REGISTER MODULE FORMS (2 Forms) ==========
  AssessmentReport = "Assessment Report",
  RegisterReport = "Register Report",

  // ========== ROLE AND USER MANAGEMENT MODULE FORMS (3 Forms) ==========
  Role = "Role",
  User = "User",
  UpdateRoles = "Update Roles",

  // ========== ADDITIONAL FORMS (12 Forms) ==========
  TaxCreation = "Tax Creation",
  Namuna10 = "Namuna 10",
  Dashboard = "Dashboard",
  ImportExport = "Import Export",
  TaxCalculation = "Tax Calculation",
  BillGeneration = "Bill Generation",
  PropertyDivision = "Property Division",
  PropertyMutation = "Property Mutation",
  PenaltyManagement = "Penalty Management",
  CronJobs = "Cron Jobs",
  GlobalSearch = "Global Search",
  Logs = "Logs",
  BackupMigration = "Backup Migration",
}

// ============================================================================
// ACTION DEFINITIONS (4 Total)
// ============================================================================
export enum Action {
  CanRead = "can_read",
  CanCreate = "can_write",
  CanUpdate = "can_update",
  CanDelete = "can_delete",
}
