import { Repository } from 'typeorm';
import { Master_depreciation_rate, Property_type_class_master, ReassessmentRange } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { UpdateDepreciationRateDto } from 'src/tax_Masters/depreciation_master/dto/update-depreciation-rate.dto';
import { PropertyTypeClassMasterRepository } from 'libs/database/repositories';
import { NotFoundException } from '@nestjs/common';

export class Master_depreciationRepository extends Repository<Master_depreciation_rate> {
  constructor(
    @InjectRepository(Master_depreciation_rate)
    private readonly master_depreciation_rate: Repository<Master_depreciation_rate>,

  ) {
    super(
      master_depreciation_rate.target,
      master_depreciation_rate.manager,
      master_depreciation_rate.queryRunner,
    );
  }
  async findWithPropertyClass(): Promise<Master_depreciation_rate[]> {
    return this.master_depreciation_rate.find({
      relations: ['property_type_class_id',"reassessmentRange"], // Include property class relation
    });
  }

  async findById(
    id: string,
  ): Promise<{ message: string; data: Master_depreciation_rate | null }> {
    // Check if ID is provided
    if (!id) {
      return { message: 'ID must be provided', data: null };
    }

    // Using QueryBuilder to find the record by ID
    const record = await this.master_depreciation_rate
      .createQueryBuilder('depreciation_rate')
      .leftJoinAndSelect(
        'depreciation_rate.property_type_class_id',
        'propertyTypeClass',
      ) // Ensure relation is selected
      .where('depreciation_rate.depreciation_rate_id = :depreciation_rate_id', {
        depreciation_rate_id: id,
      })
      .getOne();

    if (!record) {
      return { message: 'Record not found', data: null };
    }

    return { message: 'Record found', data: record };
  }

  async updateRate(
    id: string,
    data: UpdateDepreciationRateDto,
  ): Promise<{ message: string; data: Master_depreciation_rate | undefined }> {
    const existingRecord = await this.master_depreciation_rate.findOne({
      where: { depreciation_rate_id: id },
      relations: ['property_type_class_id', 'reassessmentRange'], // Ensure the relation is fetched
    });

    if (!existingRecord) {
      return {
        message: 'No record found to update',
        data: undefined,
      };
    }

    // Handle reassessment range if provided
    if (data.reassessment_range_id) {
      const reassessmentRange = await this.master_depreciation_rate.manager
        .getRepository(ReassessmentRange)
        .findOne({
          where: { reassessment_range_id: data.reassessment_range_id }
        });
      if (reassessmentRange) {
        existingRecord.reassessmentRange = reassessmentRange;
      }
    }

    // Update the properties of the existing record
    Object.assign(existingRecord, data);

    // Save the updated record back to the database
    await this.master_depreciation_rate.save(existingRecord);

    return {
      message: 'Depreciation rate updated successfully',
      data: existingRecord,
    };
  }

  async generateForNewFinancialYear(newFinancialYear: string, currentReassessmentRange: ReassessmentRange): Promise<{ message: string; data?: Master_depreciation_rate[] }> {
    // Fetch all existing records
    const existingRecords = await this.findWithPropertyClass();

    if (existingRecords.length === 0) {
      return {
        message: 'No records found to duplicate',
      };
    }

    const duplicatedRecords: Master_depreciation_rate[] = [];

    for (const record of existingRecords) {
      // Create a new record based on the existing one
      const newRecord = new Master_depreciation_rate();
      newRecord.financial_year = newFinancialYear; // Keep for backward compatibility
      newRecord.from_age = record.from_age;
      newRecord.to_age = record.to_age;
      newRecord.value = record.value;
      newRecord.status = record.status;
      newRecord.property_type_class_id = record.property_type_class_id;
      newRecord.reassessmentRange = currentReassessmentRange;

      // Save the new record to the database
      const savedRecord = await this.master_depreciation_rate.save(newRecord);
      duplicatedRecords.push(savedRecord);
    }

    return {
      message: 'Records generated successfully for the new reassessment range',
      data: duplicatedRecords,
    };
  }


}
