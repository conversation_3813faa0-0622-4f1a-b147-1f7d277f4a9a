// src/tax-value-master/dto/create-tax-value.dto.ts
import { IsString, IsNumber, IsUUID, IsNotEmpty, IsIn, IsOptional } from 'class-validator';

export class CreateTaxValueDto {
  @IsString()
  @IsOptional()
  financial_year?: string; // Optional for backward compatibility

  @IsString()
  @IsOptional()
  reassessment_range_id?: string; // Reassessment range ID

  @IsNumber()
  @IsNotEmpty()
  value: number; // Required tax value

  @IsString()
  @IsIn(['Active', 'Inactive'])
  status: string; // Must be 'active' or 'inactive'

  @IsUUID()
  @IsNotEmpty()
  propertyType_id: string; // Required property type ID
}
