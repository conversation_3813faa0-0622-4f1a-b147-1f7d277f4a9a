
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  LayoutDashboard,
  UserPlus,
  CalendarCheck,
  LandPlot,
  Settings,
  Files,
  IndianRupee,
  BarChart,
  ClipboardList,
  HelpCircle
} from "lucide-react";
import {
  <PERSON>,
  <PERSON>Title,
  <PERSON>Head<PERSON>,
  <PERSON>Conte<PERSON>,
  <PERSON>Footer
} from "@/components/ui/card";

const UserGuide: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="container mx-auto p-6 max-w-7xl">
        {/* Header Section */}
        <div className="mb-8 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 text-white rounded-2xl mb-4">
            <HelpCircle className="h-8 w-8" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
            शिरोळ नगरपरिषद अॅप्लिकेशन
          </h1>
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">वापरकर्ता मार्गदर्शक</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            प्रत्येक मॉड्यूल काय करते आणि ते कसे वापरावे याची संपूर्ण माहिती
          </p>
        </div>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-6 bg-white/80 backdrop-blur-sm border shadow-sm rounded-xl p-1 mb-8">
            <TabsTrigger value="overview" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-lg transition-all">
              सामान्य
            </TabsTrigger>
            <TabsTrigger value="dashboard" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-lg transition-all">
              डॅशबोर्ड
            </TabsTrigger>
            <TabsTrigger value="property" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-lg transition-all">
              मालमत्ता
            </TabsTrigger>
            <TabsTrigger value="tax" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-lg transition-all">
              कर व्यवस्थापन
            </TabsTrigger>
            <TabsTrigger value="master" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-lg transition-all">
              मास्टर डेटा
            </TabsTrigger>
            <TabsTrigger value="admin" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white rounded-lg transition-all">
              प्रशासन
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <Wizard className="border-0 shadow-lg bg-white/70 backdrop-blur-sm">
              <WizardHeader>
                <WizardTitle className="flex items-center gap-3 text-2xl">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <HelpCircle className="h-6 w-6 text-blue-600" />
                  </div>
                  अॅप्लिकेशन परिचय
                </WizardTitle>
                <p className="text-base text-gray-600">
                  शिरोळ नगरपरिषद अॅप्लिकेशन मालमत्ता व्यवस्थापन आणि कर संकलनासाठी डिझाइन केले आहे
                </p>
              </WizardHeader>
              <WizardContent className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="border border-blue-200 bg-blue-50/50 p-4 rounded-lg">
                    <h4 className="text-lg text-blue-800 font-semibold pb-3">मुख्य वैशिष्ट्ये</h4>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm">मालमत्ता नोंदणी आणि व्यवस्थापन</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm">कर गणना आणि संकलन</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm">वापरकर्ता आणि भूमिका व्यवस्थापन</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm">अहवाल आणि आकडेवारी</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm">मास्टर डेटा कॉन्फिगरेशन</span>
                      </li>
                    </ul>
                  </div>

                  <div className="border border-green-200 bg-green-50/50 p-4 rounded-lg">
                    <h4 className="text-lg text-green-800 font-semibold pb-3">प्रारंभ करण्यासाठी</h4>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">योग्य लॉगिन क्रेडेन्शियल्स वापरा</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">साइडबार मेनू वापरून नेव्हिगेट करा</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">आपल्या भूमिकेनुसार परवानग्या तपासा</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">मदतीसाठी या मार्गदर्शकाचा वापर करा</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </WizardContent>
            </Wizard>
          </TabsContent>

          <TabsContent value="dashboard" className="space-y-6">
            <Wizard className="border-0 shadow-lg bg-white/70 backdrop-blur-sm">
              <WizardHeader>
                <WizardTitle className="flex items-center gap-3 text-2xl">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <LayoutDashboard className="h-6 w-6 text-orange-600" />
                  </div>
                  डॅशबोर्ड मॉड्यूल
                </WizardTitle>
                <p className="text-base text-gray-600">
                  सामान्य माहिती, आकडेवारी आणि अहवाल पहा
                </p>
              </WizardHeader>
              <WizardContent className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="border border-orange-200 bg-orange-50/50 p-4 rounded-lg">
                    <h4 className="text-lg text-orange-800 flex items-center gap-2 font-semibold pb-3">
                      <BarChart className="h-5 w-5" />
                      मुख्य वैशिष्ट्ये
                    </h4>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="text-sm">एकूण मालमत्ता संख्या</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="text-sm">कर संकलन आकडेवारी</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="text-sm">बाकी कर माहिती</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="text-sm">अलीकडील व्यवहार</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="text-sm">मालमत्ता प्रकारानुसार वितरण</span>
                      </li>
                    </ul>
                  </div>

                  <div className="border border-purple-200 bg-purple-50/50 p-4 rounded-lg">
                    <h4 className="text-lg text-purple-800 font-semibold pb-3">कसे वापरावे</h4>
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <Badge variant="outline" className="mt-0.5">1</Badge>
                        <span className="text-sm">लॉगिन केल्यानंतर डॅशबोर्ड स्वयंचलितपणे दिसेल</span>
                      </div>
                      <div className="flex items-start gap-3">
                        <Badge variant="outline" className="mt-0.5">2</Badge>
                        <span className="text-sm">तारीख फिल्टर वापरून विशिष्ट कालावधीची माहिती पहा</span>
                      </div>
                      <div className="flex items-start gap-3">
                        <Badge variant="outline" className="mt-0.5">3</Badge>
                        <span className="text-sm">चार्ट आणि ग्राफवर क्लिक करून तपशीलवार माहिती मिळवा</span>
                      </div>
                      <div className="flex items-start gap-3">
                        <Badge variant="outline" className="mt-0.5">4</Badge>
                        <span className="text-sm">रिफ्रेश बटण वापरून नवीनतम डेटा लोड करा</span>
                      </div>
                    </div>
                  </div>
                </div>
              </WizardContent>
            </Wizard>
          </TabsContent>

          <TabsContent value="property" className="space-y-6">
            <Wizard className="border-0 shadow-lg bg-white/70 backdrop-blur-sm">
              <WizardHeader>
                <WizardTitle className="flex items-center gap-3 text-2xl">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <LandPlot className="h-6 w-6 text-green-600" />
                  </div>
                  मालमत्ता व्यवस्थापन मॉड्यूल
                </WizardTitle>
                <p className="text-base text-gray-600">
                  मालमत्ता नोंदणी, पहा, अपडेट आणि व्यवस्थापित करा
                </p>
              </WizardHeader>
              <WizardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="text-xl font-semibold text-gray-800 mb-4">उप-मॉड्यूल्स</h4>

                    <div className="border border-blue-200 bg-gradient-to-r from-blue-50 to-blue-100/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge className="bg-blue-600">1</Badge>
                        मालमत्ता नोंदणी
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">नवीन मालमत्ता सिस्टममध्ये नोंदवा</p>
                      <ul className="space-y-1.5">
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          मालकाची माहिती भरा
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          मालमत्तेचे तपशील द्या
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          स्थान आणि क्षेत्र माहिती
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          कागदपत्रे अपलोड करा
                        </li>
                      </ul>
                    </div>

                    <div className="border border-green-200 bg-gradient-to-r from-green-50 to-green-100/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge className="bg-green-600">2</Badge>
                        मालमत्ता पहा
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">नोंदणीकृत मालमत्तांची यादी आणि तपशील</p>
                      <ul className="space-y-1.5">
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          मालमत्ता क्रमांकाने शोधा
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          मालकाच्या नावाने शोधा
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          फिल्टर वापरून शोधा
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          तपशीलवार माहिती पहा
                        </li>
                      </ul>
                    </div>

                    <div className="border border-purple-200 bg-gradient-to-r from-purple-50 to-purple-100/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge className="bg-purple-600">3</Badge>
                        मालमत्ता मागणी
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">कर मागणी तयार करा आणि पहा</p>
                      <ul className="space-y-1.5">
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          नमुना ८ (मिळकत कर आकारणी)
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          नमुना ९ (वार्षिक कर मागणी)
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          कर गणना आणि तपशील
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          प्रिंट आणि डाउनलोड
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="text-xl font-semibold text-gray-800 mb-4">अधिक वैशिष्ट्ये</h4>

                    <div className="border border-orange-200 bg-gradient-to-r from-orange-50 to-orange-100/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge className="bg-orange-600">4</Badge>
                        मिळकत कर जनरेशन
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">मिळकत कर बॅचमध्ये तयार करा</p>
                      <ul className="space-y-1.5">
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                          पुनर्मूल्यांकन वर्ष निवडा
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                          बॅच साइझ सेट करा
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                          प्रगती ट्रॅक करा
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                          अहवाल डाउनलोड करा
                        </li>
                      </ul>
                    </div>

                    <div className="border border-red-200 bg-gradient-to-r from-red-50 to-red-100/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge className="bg-red-600">5</Badge>
                        वार्षिक कर जनरेशन
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">वार्षिक कर बॅचमध्ये तयार करा</p>
                      <ul className="space-y-1.5">
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                          आर्थिक वर्ष निवडा
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                          बॅच प्रक्रिया
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                          दंड गणना
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                          स्थिती ट्रॅकिंग
                        </li>
                      </ul>
                    </div>

                    <div className="border border-teal-200 bg-gradient-to-r from-teal-50 to-teal-100/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge className="bg-teal-600">6</Badge>
                        मालमत्ता फेरफार
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">मालमत्ता तपशील अपडेट करा</p>
                      <ul className="space-y-1.5">
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>
                          मालकी बदल
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>
                          वापर बदल
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>
                          क्षेत्र अपडेट
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>
                          कागदपत्रे अपडेट
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </WizardContent>
            </Wizard>
          </TabsContent>

          <TabsContent value="tax" className="space-y-6">
            <Wizard className="border-0 shadow-lg bg-white/70 backdrop-blur-sm">
              <WizardHeader>
                <WizardTitle className="flex items-center gap-3 text-2xl">
                  <div className="p-2 bg-emerald-100 rounded-lg">
                    <IndianRupee className="h-6 w-6 text-emerald-600" />
                  </div>
                  कर व्यवस्थापन मॉड्यूल
                </WizardTitle>
                <p className="text-base text-gray-600">
                  कर गणना, संकलन आणि भुगतान व्यवस्थापन
                </p>
              </WizardHeader>
              <WizardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="border-l-4 border-l-blue-500 bg-blue-50/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge variant="outline" className="bg-blue-100 text-blue-800">Type 1</Badge>
                        मालमत्ता कर
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">मालमत्तेवरील मुख्य कर</p>
                      <ul className="space-y-1.5">
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          मालमत्ता मूल्यावर आधारित
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          वार्षिक गणना
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          वापर प्रकारानुसार दर
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                          क्षेत्रफळ आधारित
                        </li>
                      </ul>
                    </div>

                    <div className="border-l-4 border-l-green-500 bg-green-50/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge variant="outline" className="bg-green-100 text-green-800">Type 2</Badge>
                        पाणी कर
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">पाणी पुरवठ्यावरील कर</p>
                      <ul className="space-y-1.5">
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          कनेक्शन प्रकारानुसार
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          मासिक/वार्षिक
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          मीटर रीडिंग आधारित
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          निश्चित दर
                        </li>
                      </ul>
                    </div>

                    <div className="border-l-4 border-l-purple-500 bg-purple-50/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge variant="outline" className="bg-purple-100 text-purple-800">Type 3</Badge>
                        गटार कर
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">सीवेज सिस्टमवरील कर</p>
                      <ul className="space-y-1.5">
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          कनेक्शन आधारित
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          मालमत्ता प्रकारानुसार
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          वार्षिक गणना
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          निश्चित दर संरचना
                        </li>
                      </ul>
                    </div>

                    <div className="border-l-4 border-l-orange-500 bg-orange-50/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge variant="outline" className="bg-orange-100 text-orange-800">Type 4-8</Badge>
                        इतर कर
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">विविध नगरपालिका कर</p>
                      <ul className="space-y-1.5">
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                          रस्ता कर
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                          प्रकाश कर
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                          स्वच्छता कर
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                          विकास कर
                        </li>
                      </ul>
                    </div>

                    <div className="border-l-4 border-l-red-500 bg-red-50/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge variant="outline" className="bg-red-100 text-red-800">Type 9</Badge>
                        दंड
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">विलंब शुल्क आणि दंड</p>
                      <ul className="space-y-1.5">
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                          मासिक दंड गणना
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                          जानेवारी १ला २% दंड
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                          बाकी रकमेवर आधारित
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                          स्वयंचलित गणना
                        </li>
                      </ul>
                    </div>

                    <div className="border-l-4 border-l-indigo-500 bg-indigo-50/50 p-4 rounded-lg">
                      <h5 className="text-lg font-semibold pb-2">कर गणना प्रक्रिया</h5>
                      <ol className="space-y-2">
                        <li className="flex items-center gap-2 text-sm">
                          <Badge className="bg-indigo-600 text-white w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">1</Badge>
                          मालमत्ता मूल्यांकन
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <Badge className="bg-indigo-600 text-white w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">2</Badge>
                          वापर प्रकार ओळख
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <Badge className="bg-indigo-600 text-white w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">3</Badge>
                          लागू दर निवडा
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <Badge className="bg-indigo-600 text-white w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">4</Badge>
                          क्षेत्रफळ गुणाकार
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <Badge className="bg-indigo-600 text-white w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">5</Badge>
                          एकूण कर गणना
                        </li>
                        <li className="flex items-center gap-2 text-sm">
                          <Badge className="bg-indigo-600 text-white w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">6</Badge>
                          दंड जोडा (जर लागू असेल)
                        </li>
                      </ol>
                    </div>
                  </div>
                </div>
              </WizardContent>
            </Wizard>
          </TabsContent>

          <TabsContent value="master" className="space-y-6">
            <Wizard className="border-0 shadow-lg bg-white/70 backdrop-blur-sm">
              <WizardHeader>
                <WizardTitle className="flex items-center gap-3 text-2xl">
                  <div className="p-2 bg-violet-100 rounded-lg">
                    <Settings className="h-6 w-6 text-violet-600" />
                  </div>
                  मास्टर डेटा व्यवस्थापन
                </WizardTitle>
                <p className="text-base text-gray-600">
                  सिस्टमचा मूलभूत डेटा कॉन्फिगर करा आणि व्यवस्थापित करा
                </p>
              </WizardHeader>
              <WizardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                      स्थान मास्टर
                    </h4>

                    <div className="space-y-3">
                      <div className="border border-blue-200 bg-blue-50/30 p-4 rounded-lg">
                        <h5 className="text-base font-semibold pb-2">झोन मास्टर</h5>
                        <p className="text-sm text-gray-600 mb-2">शहरातील विविध झोन व्यवस्थापित करा</p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="secondary" className="text-xs">झोन नाव आणि कोड</Badge>
                          <Badge variant="secondary" className="text-xs">झोन सीमा</Badge>
                          <Badge variant="secondary" className="text-xs">झोन प्रभारी</Badge>
                        </div>
                      </div>

                      <div className="border border-green-200 bg-green-50/30 p-4 rounded-lg">
                        <h5 className="text-base font-semibold pb-2">प्रभाग मास्टर</h5>
                        <p className="text-sm text-gray-600 mb-2">निवडणूक प्रभाग व्यवस्थापित करा</p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="secondary" className="text-xs">प्रभाग क्रमांक</Badge>
                          <Badge variant="secondary" className="text-xs">प्रभाग नाव</Badge>
                          <Badge variant="secondary" className="text-xs">झोनशी संबंध</Badge>
                        </div>
                      </div>

                      <div className="border border-purple-200 bg-purple-50/30 p-4 rounded-lg">
                        <h5 className="text-base font-semibold pb-2">स्ट्रीट मास्टर</h5>
                        <p className="text-sm text-gray-600 mb-2">रस्ते आणि गल्ल्या व्यवस्थापित करा</p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="secondary" className="text-xs">रस्त्याचे नाव</Badge>
                          <Badge variant="secondary" className="text-xs">रस्ता कोड</Badge>
                          <Badge variant="secondary" className="text-xs">प्रभागाशी संबंध</Badge>
                        </div>
                      </div>

                      <div className="border border-orange-200 bg-orange-50/30 p-4 rounded-lg">
                        <h5 className="text-base font-semibold pb-2">क्षेत्र मास्टर</h5>
                        <p className="text-sm text-gray-600 mb-2">विविध क्षेत्रे व्यवस्थापित करा</p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="secondary" className="text-xs">क्षेत्राचे नाव</Badge>
                          <Badge variant="secondary" className="text-xs">क्षेत्र कोड</Badge>
                          <Badge variant="secondary" className="text-xs">स्थान माहिती</Badge>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                      मालमत्ता मास्टर
                    </h4>

                    <div className="space-y-3">
                      <div className="border border-red-200 bg-red-50/30 p-4 rounded-lg">
                        <h5 className="text-base font-semibold pb-2">मालमत्ता प्रकार</h5>
                        <p className="text-sm text-gray-600 mb-2">मालमत्तेचे मुख्य प्रकार</p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="secondary" className="text-xs">निवासी</Badge>
                          <Badge variant="secondary" className="text-xs">व्यावसायिक</Badge>
                          <Badge variant="secondary" className="text-xs">औद्योगिक</Badge>
                          <Badge variant="secondary" className="text-xs">शैक्षणिक</Badge>
                        </div>
                      </div>

                      <div className="border border-teal-200 bg-teal-50/30 p-4 rounded-lg">
                        <h5 className="text-base font-semibold pb-2">मालमत्ता उप-प्रकार</h5>
                        <p className="text-sm text-gray-600 mb-2">मुख्य प्रकारांचे उप-विभाग</p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="secondary" className="text-xs">घर, फ्लॅट, बंगला</Badge>
                          <Badge variant="secondary" className="text-xs">दुकान, कार्यालय</Badge>
                          <Badge variant="secondary" className="text-xs">कारखाना, गोदाम</Badge>
                        </div>
                      </div>

                      <div className="border border-indigo-200 bg-indigo-50/30 p-4 rounded-lg">
                        <h5 className="text-base font-semibold pb-2">वापर मास्टर</h5>
                        <p className="text-sm text-gray-600 mb-2">मालमत्तेचा वापर प्रकार</p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="secondary" className="text-xs">स्व-निवास</Badge>
                          <Badge variant="secondary" className="text-xs">भाड्याने</Badge>
                          <Badge variant="secondary" className="text-xs">व्यावसायिक वापर</Badge>
                        </div>
                      </div>

                      <div className="border border-yellow-200 bg-yellow-50/30 p-4 rounded-lg">
                        <h5 className="text-base font-semibold pb-2">बांधकाम वर्ग</h5>
                        <p className="text-sm text-gray-600 mb-2">बांधकामाचा प्रकार आणि गुणवत्ता</p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="secondary" className="text-xs">RCC, लोखंडी</Badge>
                          <Badge variant="secondary" className="text-xs">दगडी, मातीची</Badge>
                          <Badge variant="secondary" className="text-xs">मिश्र बांधकाम</Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator className="my-6" />

                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-4 rounded-lg">
                  <h4 className="text-lg text-blue-800 font-semibold pb-2">कर दर मास्टर</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-3 bg-white/50 rounded-lg">
                      <h5 className="font-semibold text-blue-700">कर दर</h5>
                      <p className="text-sm text-blue-600">विविध मालमत्ता प्रकारांसाठी कर दर</p>
                    </div>
                    <div className="text-center p-3 bg-white/50 rounded-lg">
                      <h5 className="font-semibold text-blue-700">वेटिंग दर</h5>
                      <p className="text-sm text-blue-600">मालमत्ता मूल्यांकनासाठी वजन</p>
                    </div>
                    <div className="text-center p-3 bg-white/50 rounded-lg">
                      <h5 className="font-semibold text-blue-700">रेडी रेकनर दर</h5>
                      <p className="text-sm text-blue-600">सरकारी बाजार दर</p>
                    </div>
                  </div>
                </div>
              </WizardContent>
            </Wizard>
          </TabsContent>

          <TabsContent value="admin" className="space-y-6">
            <Wizard className="border-0 shadow-lg bg-white/70 backdrop-blur-sm">
              <WizardHeader>
                <WizardTitle className="flex items-center gap-3 text-2xl">
                  <div className="p-2 bg-pink-100 rounded-lg">
                    <UserPlus className="h-6 w-6 text-pink-600" />
                  </div>
                  प्रशासन मॉड्यूल
                </WizardTitle>
                <p className="text-base text-gray-600">
                  वापरकर्ता, भूमिका आणि परवानग्या व्यवस्थापित करा
                </p>
              </WizardHeader>
              <WizardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <div className="w-4 h-4 bg-pink-500 rounded-full"></div>
                      वापरकर्ता व्यवस्थापन
                    </h4>

                    <div className="border-l-4 border-l-blue-500 bg-gradient-to-r from-blue-50 to-blue-100/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge className="bg-blue-600">1</Badge>
                        वापरकर्ता नोंदणी
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">नवीन वापरकर्ते सिस्टममध्ये जोडा</p>
                      <div className="grid grid-cols-2 gap-2">
                        <Badge variant="outline" className="justify-center text-xs">वैयक्तिक माहिती</Badge>
                        <Badge variant="outline" className="justify-center text-xs">लॉगिन क्रेडेन्शियल्स</Badge>
                        <Badge variant="outline" className="justify-center text-xs">संपर्क तपशील</Badge>
                        <Badge variant="outline" className="justify-center text-xs">भूमिका नियुक्ती</Badge>
                      </div>
                    </div>

                    <div className="border-l-4 border-l-green-500 bg-gradient-to-r from-green-50 to-green-100/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge className="bg-green-600">2</Badge>
                        भूमिका व्यवस्थापन
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">विविध भूमिका तयार करा आणि व्यवस्थापित करा</p>
                      <div className="grid grid-cols-2 gap-2">
                        <Badge variant="outline" className="justify-center text-xs">प्रशासक</Badge>
                        <Badge variant="outline" className="justify-center text-xs">कर अधिकारी</Badge>
                        <Badge variant="outline" className="justify-center text-xs">डेटा एंट्री ऑपरेटर</Badge>
                        <Badge variant="outline" className="justify-center text-xs">कस्टम भूमिका</Badge>
                      </div>
                    </div>

                    <div className="border-l-4 border-l-purple-500 bg-gradient-to-r from-purple-50 to-purple-100/50 p-4 rounded-lg">
                      <h5 className="text-lg flex items-center gap-2 font-semibold pb-2">
                        <Badge className="bg-purple-600">3</Badge>
                        परवानग्या
                      </h5>
                      <p className="text-sm text-gray-600 mb-2">प्रत्येक भूमिकेसाठी परवानग्या सेट करा</p>
                      <div className="grid grid-cols-2 gap-2">
                        <Badge variant="outline" className="justify-center text-xs">वाचा (Read)</Badge>
                        <Badge variant="outline" className="justify-center text-xs">लिहा (Create)</Badge>
                        <Badge variant="outline" className="justify-center text-xs">अपडेट (Update)</Badge>
                        <Badge variant="outline" className="justify-center text-xs">हटवा (Delete)</Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <div className="w-4 h-4 bg-indigo-500 rounded-full"></div>
                      सिस्टम सेटिंग्स
                    </h4>

                    <div className="border-l-4 border-l-orange-500 bg-gradient-to-r from-orange-50 to-orange-100/50 p-4 rounded-lg">
                      <h5 className="text-base font-semibold pb-2">आर्थिक वर्ष</h5>
                      <p className="text-sm text-gray-600 mb-2">आर्थिक वर्ष व्यवस्थापित करा</p>
                      <div className="space-y-2">
                        <Badge variant="secondary" className="text-xs">नवीन आर्थिक वर्ष तयार करा</Badge>
                        <Badge variant="secondary" className="text-xs">सुरुवात आणि समाप्ती तारीख</Badge>
                        <Badge variant="secondary" className="text-xs">सक्रिय/निष्क्रिय स्थिती</Badge>
                      </div>
                    </div>

                    <div className="border-l-4 border-l-red-500 bg-gradient-to-r from-red-50 to-red-100/50 p-4 rounded-lg">
                      <h5 className="text-base font-semibold pb-2">पुनर्मूल्यांकन</h5>
                      <p className="text-sm text-gray-600 mb-2">मालमत्ता पुनर्मूल्यांकन व्यवस्थापित करा</p>
                      <div className="space-y-2">
                        <Badge variant="secondary" className="text-xs">पुनर्मूल्यांकन वर्ष</Badge>
                        <Badge variant="secondary" className="text-xs">दर अपडेट</Badge>
                        <Badge variant="secondary" className="text-xs">बॅच प्रक्रिया</Badge>
                      </div>
                    </div>

                    <div className="border-l-4 border-l-teal-500 bg-gradient-to-r from-teal-50 to-teal-100/50 p-4 rounded-lg">
                      <h5 className="text-base font-semibold pb-2">कलेक्टर मास्टर</h5>
                      <p className="text-sm text-gray-600 mb-2">कर संकलन अधिकारी</p>
                      <div className="space-y-2">
                        <Badge variant="secondary" className="text-xs">कलेक्टर माहिती</Badge>
                        <Badge variant="secondary" className="text-xs">क्षेत्र नियुक्ती</Badge>
                        <Badge variant="secondary" className="text-xs">संकलन लक्ष्य</Badge>
                      </div>
                    </div>

                    <div className="border-l-4 border-l-indigo-500 bg-gradient-to-r from-indigo-50 to-indigo-100/50 p-4 rounded-lg">
                      <h5 className="text-base font-semibold pb-2">बुक मास्टर</h5>
                      <p className="text-sm text-gray-600 mb-2">नोंदवही व्यवस्थापन</p>
                      <div className="space-y-2">
                        <Badge variant="secondary" className="text-xs">बुक नंबर</Badge>
                        <Badge variant="secondary" className="text-xs">बुक प्रकार</Badge>
                        <Badge variant="secondary" className="text-xs">पेज रेंज</Badge>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator className="my-6" />

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 p-4 rounded-lg">
                  <h4 className="text-lg text-green-800 font-semibold pb-2">महत्वाची माहिती</h4>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2 text-sm text-green-700">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      फक्त प्रशासकच वापरकर्ता आणि भूमिका व्यवस्थापित करू शकतात
                    </li>
                    <li className="flex items-start gap-2 text-sm text-green-700">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      परवानग्या बदलल्यानंतर वापरकर्त्याने पुन्हा लॉगिन करावे
                    </li>
                    <li className="flex items-start gap-2 text-sm text-green-700">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      मास्टर डेटा बदलण्यापूर्वी बॅकअप घ्या
                    </li>
                    <li className="flex items-start gap-2 text-sm text-green-700">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      सिस्टम सेटिंग्स सावधगिरीने बदला
                    </li>
                  </ul>
                </div>
              </WizardContent>
            </Wizard>
          </TabsContent>
        </Tabs>

        {/* General Tips Section */}
        <Wizard className="border-0 shadow-lg bg-white/70 backdrop-blur-sm mt-8">
          <WizardHeader>
            <WizardTitle className="flex items-center gap-3 text-2xl">
              <div className="p-2 bg-amber-100 rounded-lg">
                <ClipboardList className="h-6 w-6 text-amber-600" />
              </div>
              सामान्य सूचना आणि टिप्स
            </WizardTitle>
          </WizardHeader>
          <WizardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border border-blue-200 bg-blue-50/30 p-4 rounded-lg">
                <h4 className="text-lg text-blue-800 font-semibold pb-3">नेव्हिगेशन टिप्स</h4>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2 text-sm">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    साइडबार मेनू वापरून मॉड्यूल्समध्ये नेव्हिगेट करा
                  </li>
                  <li className="flex items-start gap-2 text-sm">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    ब्रेडक्रम्ब वापरून आपली स्थिती ओळखा
                  </li>
                  <li className="flex items-start gap-2 text-sm">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    श 

फंक्शन वापरून जलदी माहिती मिळवा
                  </li>
                  <li className="flex items-start gap-2 text-sm">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    फिल्टर वापरून डेटा सॉर्ट करा
                  </li>
                </ul>
              </div>

              <div className="border border-green-200 bg-green-50/30 p-4 rounded-lg">
                <h4 className="text-lg text-green-800 font-semibold pb-3">डेटा एंट्री टिप्स</h4>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    सर्व आवश्यक फील्ड भरा
                  </li>
                  <li className="flex items-start gap-2 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    योग्य फॉर्मॅटमध्ये डेटा एंटर करा
                  </li>
                  <li className="flex items-start gap-2 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    सेव्ह करण्यापूर्वी डेटा तपासा
                  </li>
                  <li className="flex items-start gap-2 text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    नियमित बॅकअप घ्या
                  </li>
                </ul>
              </div>
            </div>

            <div className="bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 p-4 rounded-lg">
              <h4 className="text-lg text-yellow-800 font-semibold pb-2">महत्वाचे लक्षात ठेवण्यासारखे</h4>
              <ul className="space-y-2">
                <li className="flex items-start gap-2 text-sm text-yellow-700">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                  आपल्या भूमिकेनुसार फक्त परवानगी असलेले मॉड्यूल दिसतील
                </li>
                <li className="flex items-start gap-2 text-sm text-yellow-700">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                  डेटा बदलण्यापूर्वी योग्य परवानगी असल्याची खात्री करा
                </li>
                <li className="flex items-start gap-2 text-sm text-yellow-700">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                  सिस्टममधून लॉग आउट करण्यापूर्वी सर्व काम सेव्ह करा
                </li>
                <li className="flex items-start gap-2 text-sm text-yellow-700">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                  तांत्रिक समस्यांसाठी सिस्टम प्रशासकाशी संपर्क साधा
                </li>
                <li className="flex items-start gap-2 text-sm text-yellow-700">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                  पासवर्ड नियमित बदला आणि सुरक्षित ठेवा
                </li>
              </ul>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-4 rounded-lg">
              <h4 className="text-lg text-blue-800 font-semibold pb-2">मदतीसाठी संपर्क</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-white/50 rounded-lg">
                  <h5 className="font-semibold text-blue-700 mb-2">तांत्रिक सहाय्य</h5>
                  <p className="text-sm text-blue-600">फोन: [तांत्रिक सहाय्य नंबर]</p>
                  <p className="text-sm text-blue-600">ईमेल: [तांत्रिक सहाय्य ईमेल]</p>
                </div>
                <div className="p-4 bg-white/50 rounded-lg">
                  <h5 className="font-semibold text-blue-700 mb-2">प्रशासकीय सहाय्य</h5>
                  <p className="text-sm text-blue-600">फोन: [प्रशासकीय सहाय्य नंबर]</p>
                  <p className="text-sm text-blue-600">ईमेल: [प्रशासकीय सहाय्य ईमेल]</p>
                </div>
              </div>
            </div>
          </WizardContent>
        </Wizard>
      </div>
    </div>
  );
};

export default UserGuide;
