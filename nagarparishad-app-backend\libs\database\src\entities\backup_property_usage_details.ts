import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, OneToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Column, Unique } from 'typeorm';
@Entity('backup_property_usage_details')
@Unique(['property_usage_details_id']) 
export class BackupPropertyUsageDetailsEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', nullable: false })
  property_id: string;

  @Column({ type: 'varchar', nullable: false })
  property_usage_details_id: string;

  @Column({ type: 'jsonb', nullable: true })
  property_usage_details: object;
}   
