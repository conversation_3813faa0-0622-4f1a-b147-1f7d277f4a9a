import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  BaseEntity,
  ManyToOne,
  JoinC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany
} from 'typeorm';



@Entity('import_property_stats')
export class Import_PropertyStatsEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  dstats_id: string;

  @Column({ name: 'blankStreetCount', type: 'integer', nullable: true })
  blankStreetCount: number;  

  @Column({ name: 'blankZoneNameCount', type: 'integer', nullable: true })
  blankZoneNameCount: number;  
  
  @Column({ name: 'blankPropertyNumberCount', type: 'integer', nullable: true })
  blankPropertyNumberCount: number;  

  @Column({ name: 'blankOldPropertyNumberCount', type: 'integer', nullable: true })
  blankOldPropertyNumberCount: number;  

  @Column({ name: 'blankOwnerNameCount', type: 'integer', nullable: true })
  blankOwnerNameCount: number;  

  @Column({ name: 'blankOwnerTypeCount', type: 'integer', nullable: true })
  blankOwnerTypeCount: number;  

  @Column({ name: 'blankUsageTypeCount', type: 'integer', nullable: true })
  blankUsageTypeCount: number;  

  @Column({ name: 'blankUsageDescCount', type: 'integer', nullable: true })
  blankUsageDescCount: number;  

  @Column({ name: 'blankConstructionYearCount', type: 'integer', nullable: true })
  blankConstructionYearCount: number;  

  @Column({ name: 'blankLengthCount', type: 'integer', nullable: true })
  blankLengthCount: number;  
  
  @Column({ name: 'blankWidthCount', type: 'integer', nullable: true })
  blankWidthCount: number;  

  @Column({ name: 'blankSqftCount', type: 'integer', nullable: true })
  blankSqftCount: number;  

  @Column({ name: 'blankSqmeterCount', type: 'integer', nullable: true })
  blankSqmeterCount: number;  

  @Column({ name: 'okCount', type: 'integer', nullable: true })
  okCount: number;  

  @Column({ name: 'totalCount', type: 'integer', nullable: true })
  totalCount: number;  

  @Column({ type: String, name: 'ward_name', nullable: true})
  ward_name!: string;

  @Column({ type: 'timestamp', nullable: true })
  import_date!: Date; 

  /*
   * Create and Update Date Columns
   */

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}

