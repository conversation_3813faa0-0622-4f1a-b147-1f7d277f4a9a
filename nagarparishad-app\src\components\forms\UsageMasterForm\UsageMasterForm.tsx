import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "@/components/ui/use-toast";
import Api from "@/services/ApiServices";
import { GlobalContext } from "@/context/GlobalContext";
import {
  UsageMasterApiResponse,
  UsageUpdateApiResponse,
} from "@/model/usage-master";
import { Loader2 } from "lucide-react";
import { useUsageMasterController } from "@/controller/master/UsageMasterController";

interface UsagePopupFormInterface {
  btnTitle: string;
  editData?: any;
}

const UsageMasterForm = ({ btnTitle, editData }: UsagePopupFormInterface) => {
  const { t } = useTranslation();
  const schema = z.object({
    usage: z.string().trim().min(1, t("errors.requiredField")),
  });

  const dynamicValues = {
    name: t("usage.UsageLabel"),
  };
  const { createUsage, updateUsage } = useUsageMasterController();

  const { setOpen, refreshUsageList, setRefreshUsageList } =
    useContext(GlobalContext);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      usage: editData?.usage || "",
    },
  });

  const {
    formState: { errors },
    reset,
  } = form;

  useEffect(() => {
    if (editData) {
      reset({
        usage: editData.usage,
      });
    }
  }, [editData, reset]);

  const onSubmit = async (data: z.infer<typeof schema>) => {
    if (isSubmitting) return; // Prevent double submission
    setIsSubmitting(true);
    console.log("submit called");

    const DataResponse: any = {
      usage: data.usage,
    };

    try {
      if (editData?.usage_id !== undefined) {
        // Update existing usage
        console.log("update called");

        await updateUsage(
          {
            usageId: editData.usage_id, // Use explicit property name for clarity
            usageData: DataResponse,
          },
          {
            onSuccess: () => {
              toast({
                title: t("api.formupdate", dynamicValues),
                variant: "success",
              });
              form.reset({ usage: "" });
              setOpen(false);
              setRefreshUsageList(!refreshUsageList);
            },
            onError: (error) => {
              toast({
                title: error.message,
              });
            },
          },
        );
      } else {
        // Create new usage
        console.log("create called");
        await createUsage(DataResponse, {
          onSuccess: () => {
            toast({
              title: t("api.formcreate", dynamicValues),
              variant: "success",
            });
            form.reset({ usage: "" });
            setOpen(false);
          },
          onError: (error) => {
            toast({
              title: error.message,
            });
          },
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        usage: editData.usage || "",
      });
    } else {
      form.reset({
        usage: "",
      });
    }
  }, [editData]);
  const [loader, setLoader] = useState(false);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full md:w-full">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="usage"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>{t("usage.UsageLabel")}</FormLabel>
                  <FormControl className="mt-1">
                    <Input placeholder={t("usage.UsageLabel")} {...field} />
                  </FormControl>
                  {errors.usage && (
                    <FormMessage className="ml-1">
                      {t("errors.requiredField")}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
            <div className="grid-cols-subgrid  mb-1 ml-4 max-md:flex max-md:justify-end  pt-[32px] ">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
        </div>
   
      </form>
    </Form>
  );
};

export default UsageMasterForm;
