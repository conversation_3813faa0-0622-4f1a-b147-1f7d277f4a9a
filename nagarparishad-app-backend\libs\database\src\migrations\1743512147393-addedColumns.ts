import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedColumns1743512147393 implements MigrationInterface {
    name = 'AddedColumns1743512147393'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "all_property_tax_sum"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "all_property_tax_sum_remaining" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "all_property_tax_sum_paid" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "all_property_tax_sum_prev_paid" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "all_property_tax_sum_curr_paid" double precision DEFAULT '0'`);
  }

    public async down(queryRunner: QueryRunner): Promise<void> {
  
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "all_property_tax_sum_curr_paid"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "all_property_tax_sum_prev_paid"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "all_property_tax_sum_paid"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "all_property_tax_sum_remaining"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "all_property_tax_sum" double precision DEFAULT '0'`);
       
    }

}
