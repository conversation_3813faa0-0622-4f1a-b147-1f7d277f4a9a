import axios from "axios";
import {
  PublishInterface,
  PublishResponseInterface,
  TaxUserListByFYResponseInterface,
  YearListByFYResponseInterface,
} from "@/model/tax/taxUserDetailsByFYInterfaces";
import {
  GET_TAX_DETAILS_USER_LIST_BY_FY,
  GET_TAX_BY_YEAR,
  CHANGE_PUBLISH_STATUS,
  GET_CONSTRUCTION_RR_RATE,
  GET_RR_RATE,
  ADD_RR_RATE,
  UPDATE_RR_RATE,
  DELETE_RR_RATE,
  GET_SOLID_WASTE_RATE,
  ADD_SOLID_WASTE_RATE,
  UPDATE_SOLID_WASTE_RATE,
  DELETE_SOLID_WASTE_RATE,
  ADD_CONSTRUCTION_RR_RATE,
  UPDATE_CONSTRUCTION_RR_RATE,
  DELETE_CONSTRUCTION_RR_RATE,
  GET_WEIGHTING_RATE,
  ADD_WEIGHTING_RATE,
  UPDATE_WEIGHTING_RATE,
  DELETE_WEIGHTING_RATE,
  GET_TAX_RATE,
  ADD_TAX_RATE,
  UPDATE_TAX_RATE,
  DELETE_TAX_RATE,
  GET_DEPRECIATION_RATE_MASTER,
  ADD_DEPRECIATION_RATE_MASTER,
  UPDATE_DEPRECIATION_RATE_MASTER,
  DELETE_DEPRECIATION_RATE_MASTER,
  GET_BOOK_MASTER,
  ADD_BOOK_MASTER,
  DELETE_BOOK_MASTER,
  GET_AVAILABLE_RECIPTS,
  GET_REASSESMENT,
  ADD_REASSESMENT,
} from "@/constant/utils/taxUtils";
import {
  ConstructionRateApi,
  ConstructionRateCreateApi,
  ConstructionRateSendApiObj,
  ConstructionRateUpdateApi,
} from "@/model/tax/constructionRate";
import {
  RR_RateApi,
  RR_RateCreateApi,
  RR_RatemasterCreateApi,
  RR_RatemasterSendApiObj,
  RR_RatemasterUpdateApi,
} from "@/model/tax/RR-Rate";
import {
  SolidWasteRateApi,
  SolidWasteRatemasterCreateApi,
  SolidWasteRatemasterSendApiObj,
  SolidWasteRatemasterUpdateApi,
} from "@/model/tax/solidWaste";
import {
  WeightingRateApi,
  WeightingRateCreateApi,
  WeightingRateSendApiObj,
  WeightingRateUpdateApi,
} from "@/model/tax/weightingRate";
import {
  TaxRateApi,
  TaxRateCreateApi,
  TaxRateSendApiObj,
  TaxRateSetting,
  TaxRateUpdateApi,
} from "@/model/tax/TaxRate";

import {
  AgeWiseRateInterface,
  DepreciationRateApi,
  DepreciationRateCreateApi,
  DepreciationRateSendApiObj,
  DepreciationRateUpdateApi,
} from "@/model/tax/depreciationRateMasterInterface";

const baseURL = import.meta.env.VITE_APP_BASE_URL;

const VITE_BASE_URL = baseURL;
const REACT_APP_GET_TAX_DETAILS_USER_LIST_BY_FY =
  VITE_BASE_URL + GET_TAX_DETAILS_USER_LIST_BY_FY;
const REACT_APP_GET_TAX_BY_YEAR = VITE_BASE_URL + GET_TAX_BY_YEAR;
const REACT_APP_CHANGE_PUBLISH_STATUS = VITE_BASE_URL + CHANGE_PUBLISH_STATUS;
const REACT_APP_GET_CONSTRUCTION_RR_RATE =
  VITE_BASE_URL + GET_CONSTRUCTION_RR_RATE;
const REACT_APP_GET_RR_RATE = VITE_BASE_URL + GET_RR_RATE;
const REACT_APP_ADD_RR_RATE = VITE_BASE_URL + ADD_RR_RATE;
const REACT_APP_UPDATE_RR_RATE = VITE_BASE_URL + UPDATE_RR_RATE;
const REACT_APP_DELETE_RR_RATE = VITE_BASE_URL + DELETE_RR_RATE;
const REACT_APP_GET_SOLID_WASTE_RATE = VITE_BASE_URL + GET_SOLID_WASTE_RATE;
const REACT_APP_ADD_SOLID_WASTE_RATE = VITE_BASE_URL + ADD_SOLID_WASTE_RATE;
const REACT_APP_UPDATE_SOLID_WASTE_RATE =
  VITE_BASE_URL + UPDATE_SOLID_WASTE_RATE;
const REACT_APP_DELETE_SOLID_WASTE_RATE =
  VITE_BASE_URL + DELETE_SOLID_WASTE_RATE;
const REACT_APP_ADD_CONSTRUCTION_RR_RATE =
  VITE_BASE_URL + ADD_CONSTRUCTION_RR_RATE;
const REACT_APP_UPDATE_CONSTRUCTION_RR_RATE =
  VITE_BASE_URL + UPDATE_CONSTRUCTION_RR_RATE;
const REACT_APP_DELETE_CONSTRUCTION_RR_RATE =
  VITE_BASE_URL + DELETE_CONSTRUCTION_RR_RATE;
const REACT_APP_GET_WEIGHTING_RATE = VITE_BASE_URL + GET_WEIGHTING_RATE;
const REACT_APP_ADD_WEIGHTING_RATE = VITE_BASE_URL + ADD_WEIGHTING_RATE;
const REACT_APP_UPDATE_WEIGHTING_RATE = VITE_BASE_URL + UPDATE_WEIGHTING_RATE;
const REACT_APP_DELETE_WEIGHTING_RATE = VITE_BASE_URL + DELETE_WEIGHTING_RATE;
const REACT_APP_GET_TAX_RATE = VITE_BASE_URL + GET_TAX_RATE;
const REACT_APP_ADD_TAX_RATE = VITE_BASE_URL + ADD_TAX_RATE;
const REACT_APP_UPDATE_TAX_RATE = VITE_BASE_URL + UPDATE_TAX_RATE;
const REACT_APP_DELETE_TAX_RATE = VITE_BASE_URL + DELETE_TAX_RATE;
const REACT_APP_GET_DEPRECIATION_MASTER =
  VITE_BASE_URL + GET_DEPRECIATION_RATE_MASTER;
const REACT_APP_ADD_DEPRECIATION_MASTER =
  VITE_BASE_URL + ADD_DEPRECIATION_RATE_MASTER;
const REACT_APP_UPDATE_DEPRECIATION_MASTER =
  VITE_BASE_URL + UPDATE_DEPRECIATION_RATE_MASTER;
const REACT_APP_DELETE_DEPRECIATION_MASTER =
  VITE_BASE_URL + DELETE_DEPRECIATION_RATE_MASTER;
const REACT_APP_GET_BOOK_MASTER = VITE_BASE_URL + GET_BOOK_MASTER;
const REACT_APP_ADD_BOOK_MASTER = VITE_BASE_URL + ADD_BOOK_MASTER;
const REACT_APP_DELETE_BOOK_MASTER = VITE_BASE_URL + DELETE_BOOK_MASTER;
const REACT_APP_GET_AVAILABLE_RECIPTS = VITE_BASE_URL + GET_AVAILABLE_RECIPTS;

const REACT_APP_GET_REASSESMENT = VITE_BASE_URL + GET_REASSESMENT;
const REACT_APP_ADD_REASSESMENT = VITE_BASE_URL + ADD_REASSESMENT;


class TaxListApi {
  static getStoredToken = () => {
    const UserToken = JSON.parse(localStorage.getItem("AccessToken") || "{}");
    return UserToken !== undefined ? UserToken : false;
  };


  static getTaxUserListByFY = async (callback: (response: any) => void) => {
    try {
      const response = await axios.get(
        REACT_APP_GET_TAX_DETAILS_USER_LIST_BY_FY,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization:`Bearer ${TaxListApi.getStoredToken()}`

          },
        }
      );
      console.log("getTaxUserListByFY called");

      if (response.status === 200) {
        const responseData: TaxUserListByFYResponseInterface = response.data;
        callback({ status: true, data: responseData });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static getTaxUserListByYear = async (callback: (response: any) => void) => {
    console.log("getTaxUserListByYear called");

    try {
      const response = await axios.get(REACT_APP_GET_TAX_BY_YEAR, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        const responseData: YearListByFYResponseInterface = response.data;
        callback({ status: true, data: responseData });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static getConstructionRate = async (callback: (response: any) => void) => {
    try {
      const response = await axios.get(REACT_APP_GET_CONSTRUCTION_RR_RATE, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        const responseData: ConstructionRateApi = response.data;
        callback({ status: true, data: responseData });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static getRR_Rate = async (callback: (response: any) => void) => {
    try {
      const response = await axios.get(REACT_APP_GET_RR_RATE, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        const responseData: RR_RateApi = response.data;
        callback({ status: true, data: responseData });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };
  // static createRR_Rate = async (RR_Rate: any) => {
  //   const response = await axios.post(REACT_APP_ADD_RR_RATE, RR_Rate, {
  //     headers: {
  //       "Content-Type": "application/json",
  //       Accept: "application/json",
  //     },
  //   });
  //   return response.data;
  // };
  static createRR_Rate = async (
    RR_RateData: RR_RatemasterSendApiObj,
    callback: (response: {
      status: boolean;
      data: RR_RatemasterCreateApi;
    }) => void
  ) => {
    const url = `${REACT_APP_ADD_RR_RATE}`;

    try {
      const response = await axios.post(url, RR_RateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error creating ward:", err);
      callback({ status: false, data: err });
    }
  };

  // static updateRR_Rate = async (data: { id: string; payload: any }) => {
  //   const response = await axios.put(
  //     `${REACT_APP_UPDATE_RR_RATE}?id=${data.id}`,
  //     data.payload,
  //     {
  //       headers: {
  //         "Content-Type": "application/json",
  //         Accept: "application/json",
  //       },
  //     }
  //   );
  //   return response.data;
  // };
  // static deleteRR_Rate = async (id: string) => {
  //   const response = await axios.delete(
  //     `${REACT_APP_DELETE_RR_RATE}?id=${id}`,
  //     {
  //       headers: {
  //         "Content-Type": "application/json",
  //         Accept: "application/json",
  //       },
  //     }
  //   );
  //   return response.data;
  // };
  static updateRR_Rate = async (
    RR_RateId: string,
    RR_RateData: RR_RatemasterUpdateApi,
    callback: (response: { status: boolean; data: RR_RateCreateApi }) => void
  ) => {
    const url = `${REACT_APP_UPDATE_RR_RATE}${RR_RateId}`;

    try {
      const response = await axios.put(url, RR_RateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error updating ward:", err);
      callback({ status: false, data: err });
    }
  };

  static deleteRR_Rate = async (
    RR_RateId: string,
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.delete(
        `${REACT_APP_DELETE_RR_RATE}${RR_RateId}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization:`Bearer ${TaxListApi.getStoredToken()}`

          },
        }
      );

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error deleting construction rate:", err);
      callback({ status: false, data: err });
    }
  };

  static getSolidWasteRate = async (callback: (response: any) => void) => {
    try {
      const response = await axios.get(REACT_APP_GET_SOLID_WASTE_RATE, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });
      if (response.status === 200) {
        const responseData: SolidWasteRateApi = response.data;
        callback({ status: true, data: responseData });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };
  // static createSolidWasteRate = async (solidWasteRate: any) => {
  //   const response = await axios.post(REACT_APP_ADD_SOLID_WASTE_RATE, solidWasteRate, {
  //     headers: {
  //       "Content-Type": "application/json",
  //       Accept: "application/json",
  //     },
  //   });
  //   return response.data;
  // };
  static createSolidWasteRate = async (
    solidWasteRateData: SolidWasteRatemasterSendApiObj,
    callback: (response: {
      status: boolean;
      data: SolidWasteRatemasterCreateApi;
    }) => void
  ) => {
    const url = `${REACT_APP_ADD_SOLID_WASTE_RATE}`;

    try {
      const response = await axios.post(url, solidWasteRateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error creating ward:", err);
      callback({ status: false, data: err });
    }
  };
  // static updateSolidWasteRate = async (data: { id: string; payload: any }) => {
  //   const response = await axios.put(
  //     `${REACT_APP_UPDATE_SOLID_WASTE_RATE}?id=${data.id}`,
  //     data.payload,
  //     {
  //       headers: {
  //         "Content-Type": "application/json",
  //         Accept: "application/json",
  //       },
  //     }
  //   );
  //   return response.data;
  // };
  static updateSolidWasteRate = async (
    solidWasteRateId: string,
    solidWasteRateData: SolidWasteRatemasterUpdateApi,
    callback: (response: {
      status: boolean;
      data: SolidWasteRatemasterCreateApi;
    }) => void
  ) => {
    const url = `${REACT_APP_UPDATE_SOLID_WASTE_RATE}${solidWasteRateId}`;

    try {
      const response = await axios.put(url, solidWasteRateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error updating ward:", err);
      callback({ status: false, data: err });
    }
  };
  // static deleteSolidWasteRate = async (id: string) => {
  //   const response = await axios.delete(
  //     `${REACT_APP_DELETE_SOLID_WASTE_RATE}?id=${id}`,
  //     {
  //       headers: {
  //         "Content-Type": "application/json",
  //         Accept: "application/json",
  //       },
  //     }
  //   );
  //   return response.data;
  // };
  static deleteSolidWasteRate = async (
    solidWasteRateId: string,
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.delete(
        `${REACT_APP_DELETE_SOLID_WASTE_RATE}${solidWasteRateId}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization:`Bearer ${TaxListApi.getStoredToken()}`

          },
        }
      );

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error deleting construction rate:", err);
      callback({ status: false, data: err });
    }
  };

  // static createConstructionRate = async (constructionRate: any) => {
  //   const response = await axios.post(REACT_APP_ADD_CONSTRUCTION_RR_RATE, constructionRate, {
  //     headers: {
  //       "Content-Type": "application/json",
  //       Accept: "application/json",
  //     },
  //   });
  //   return response.data;
  // };
  static createConstructionRate = async (
    constructionRateData: ConstructionRateSendApiObj,
    callback: (response: {
      status: boolean;
      data: ConstructionRateCreateApi;
    }) => void
  ) => {
    const url = `${REACT_APP_ADD_CONSTRUCTION_RR_RATE}`;

    try {
      const response = await axios.post(url, constructionRateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error creating ward:", err);
      callback({ status: false, data: err });
    }
  };
  // static updateConstructionRates = async (data: { id: string; payload: any }) => {
  //   const response = await axios.put(
  //     `${REACT_APP_UPDATE_CONSTRUCTION_RR_RATE}?id=${data.id}`,
  //     data.payload,
  //     {
  //       headers: {
  //         "Content-Type": "application/json",
  //         Accept: "application/json",
  //       },
  //     }
  //   );
  //   return response.data;
  // };

  static updateConstructionRate = async (
    ConstructionRateId: string,
    ConstructionRateData: ConstructionRateUpdateApi,
    callback: (response: {
      status: boolean;
      data: ConstructionRateCreateApi;
    }) => void
  ) => {
    const url = `${REACT_APP_UPDATE_CONSTRUCTION_RR_RATE}${ConstructionRateId}`;

    try {
      const response = await axios.put(url, ConstructionRateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error updating ward:", err);
      callback({ status: false, data: err });
    }
  };

  // static deleteConstructionRate = async (id: string) => {
  //   const response = await axios.delete(
  //     `${REACT_APP_DELETE_CONSTRUCTION_RR_RATE}?id=${id}`,
  //     {
  //       headers: {
  //         "Content-Type": "application/json",
  //         Accept: "application/json",
  //       },
  //     }
  //   );
  //   return response.data;
  // };
  static deleteConstructionRate = async (
    constructionRateId: string,
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.delete(
        `${REACT_APP_DELETE_CONSTRUCTION_RR_RATE}${constructionRateId}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization:`Bearer ${TaxListApi.getStoredToken()}`

          },
        }
      );

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error deleting construction rate:", err);
      callback({ status: false, data: err });
    }
  };

  static getWeightingRate = async (callback: (response: any) => void) => {
    try {
      const response = await axios.get(REACT_APP_GET_WEIGHTING_RATE, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        const responseData: WeightingRateApi = response.data;
        callback({ status: true, data: responseData });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  //  static createWeightingRate = async (weightingRate: any) => {
  //   const response = await axios.post(REACT_APP_ADD_WEIGHTING_RATE, weightingRate, {
  //     headers: {
  //       "Content-Type": "application/json",
  //       Accept: "application/json",
  //     },
  //   });
  //   return response.data;
  // };

  static createWeightingRate = async (
    weightingRateData: WeightingRateSendApiObj,
    callback: (response: {
      status: boolean;
      data: WeightingRateCreateApi;
    }) => void
  ) => {
    const url = `${REACT_APP_ADD_WEIGHTING_RATE}`;

    try {
      const response = await axios.post(url, weightingRateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error creating ward:", err);
      callback({ status: false, data: err });
    }
  };

  // static updateWeightingRate = async (data: { id: string; payload: any }) => {
  //   const response = await axios.put(
  //     `${REACT_APP_UPDATE_WEIGHTING_RATE}?id=${data.id}`,
  //     data.payload,
  //     {
  //       headers: {
  //         "Content-Type": "application/json",
  //         Accept: "application/json",
  //       },
  //     }
  //   );
  //   return response.data;
  // };

  static updateWeightingRate = async (
    weightingRateId: string,
    weightingRateData: WeightingRateUpdateApi,
    callback: (response: {
      status: boolean;
      data: WeightingRateCreateApi;
    }) => void
  ) => {
    const url = `${REACT_APP_UPDATE_WEIGHTING_RATE}${weightingRateId}`;

    try {
      const response = await axios.put(url, weightingRateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error updating ward:", err);
      callback({ status: false, data: err });
    }
  };

  // static deleteWeightingRate = async (id: string) => {
  //   const response = await axios.delete(
  //     `${REACT_APP_DELETE_WEIGHTING_RATE}?id=${id}`,
  //     {
  //       headers: {
  //         "Content-Type": "application/json",
  //         Accept: "application/json",
  //       },
  //     }
  //   );
  //   return response.data;
  // };

  static deleteWeightingRate = async (
    weightingRateId: string,
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.delete(
        `${REACT_APP_DELETE_WEIGHTING_RATE}${weightingRateId}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization:`Bearer ${TaxListApi.getStoredToken()}`

          },
        }
      );

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error deleting construction rate:", err);
      callback({ status: false, data: err });
    }
  };
  //Tax Rate

  static getTaxRate = async (callback: (response: any) => void) => {
    try {
      const response = await axios.get(REACT_APP_GET_TAX_RATE, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        const responseData: TaxRateApi = response.data;
        callback({ status: true, data: responseData });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  // static createTaxRates = async (taxRate: any) => {
  //   const response = await axios.post(REACT_APP_ADD_TAX_RATE, taxRate, {
  //     headers: {
  //       "Content-Type": "application/json",
  //       Accept: "application/json",
  //     },
  //   });
  //   return response.data;
  // };
  static createTaxRate = async (
    taxRateData: TaxRateSendApiObj,
    callback: (response: { status: boolean; data: TaxRateCreateApi }) => void
  ) => {
    const url = `${REACT_APP_ADD_TAX_RATE}`;

    try {
      const response = await axios.post(url, taxRateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error creating ward:", err);
      callback({ status: false, data: err });
    }
  };
  // static updateTaxRate = async (data: { id: string; payload: any }) => {
  //   const response = await axios.put(
  //     `${REACT_APP_UPDATE_TAX_RATE}?id=${data.id}`,
  //     data.payload,
  //     {
  //       headers: {
  //         "Content-Type": "application/json",
  //         Accept: "application/json",
  //       },
  //     }
  //   );
  //   return response.data;
  // };
  static updateTaxRate = async (
    taxRateRateId: string,
    taxRateRateData: TaxRateUpdateApi,
    callback: (response: { status: boolean; data: TaxRateCreateApi }) => void
  ) => {
    const url = `${REACT_APP_UPDATE_TAX_RATE}${taxRateRateId}`;

    try {
      const response = await axios.put(url, taxRateRateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error updating ward:", err);
      callback({ status: false, data: err });
    }
  };
  // static deleteTaxRate = async (id: string) => {
  //   const response = await axios.delete(
  //     `${REACT_APP_DELETE_TAX_RATE}?id=${id}`,
  //     {
  //       headers: {
  //         "Content-Type": "application/json",
  //         Accept: "application/json",
  //       },
  //     }
  //   );
  //   return response.data;
  // };
  static deleteTaxRate = async (
    taxRateId: string,
    callback: (response: { status: boolean; data: TaxRateSetting }) => void
  ) => {
    try {
      const response = await axios.delete(
        `${REACT_APP_DELETE_TAX_RATE}${taxRateId}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization:`Bearer ${TaxListApi.getStoredToken()}`

          },
        }
      );

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error deleting construction rate:", err);
      callback({ status: false, data: err });
    }
  };

  static changePublishStatus = async (
    publishId: string,
    publishStatus: boolean,
    // publishData: PublishInterface,
    callback: (response: {
      status: boolean;
      data: PublishResponseInterface;
    }) => void
  ) => {
    try {
      const response = await axios.put(
        // REACT_APP_CHANGE_PUBLISH_STATUS + publishStatus + publishId,
        `${REACT_APP_CHANGE_PUBLISH_STATUS}${publishId}&is_published=${publishStatus}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization:`Bearer ${TaxListApi.getStoredToken()}`

          },
        }
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static getDepreciationRateMaster = async (
    callback: (response: any) => void
  ) => {
    try {
      const response = await axios.get(REACT_APP_GET_DEPRECIATION_MASTER, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });
      console.log("getTaxUserListByFY called");

      if (response.status === 200) {
        const responseData: DepreciationRateApi = response.data;
        callback({ status: true, data: responseData });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };
  static getReAssesmentMaster = async (
    callback: (response: any) => void
  ) => {
    try {
      const response = await axios.get(REACT_APP_GET_REASSESMENT, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });
      console.log("getTaxUserListByFY called");

      if (response.status === 200) {
        const responseData: DepreciationRateApi = response.data;
        callback({ status: true, data: responseData });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static createDepreciationRate = async (
    DepreciationRateData: DepreciationRateSendApiObj,
    callback: (response: {
      status: boolean;
      data: DepreciationRateCreateApi;
    }) => void
  ) => {
    const url = `${REACT_APP_ADD_DEPRECIATION_MASTER}`;

    try {
      const response = await axios.post(url, DepreciationRateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error creating ward:", err);
      callback({ status: false, data: err });
    }
  };
  static createReAssesment = async (
    DepreciationRateData: DepreciationRateSendApiObj,
    callback: (response: {
      status: boolean;
      data: DepreciationRateCreateApi;
    }) => void
  ) => {
    const url = `${REACT_APP_ADD_REASSESMENT}`;

    try {
      const response = await axios.post(url, DepreciationRateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error creating ward:", err);
      callback({ status: false, data: err });
    }
  };
  static updateDepreciationRate = async (
    DepreciationRateId: string,
    DepreciationRateData: DepreciationRateUpdateApi,
    callback: (response: {
      status: boolean;
      data: DepreciationRateCreateApi;
    }) => void
  ) => {
    const url = `${REACT_APP_UPDATE_DEPRECIATION_MASTER}${DepreciationRateId}`;

    try {
      const response = await axios.put(url, DepreciationRateData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error updating ward:", err);
      callback({ status: false, data: err });
    }
  };

  static deleteDepreciationRate = async (
    DepreciationRateId: string,
    callback: (response: { status: boolean; data: TaxRateSetting }) => void
  ) => {
    try {
      const response = await axios.delete(
        `${REACT_APP_DELETE_DEPRECIATION_MASTER}${DepreciationRateId}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization:`Bearer ${TaxListApi.getStoredToken()}`

          },
        }
      );

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error deleting construction rate:", err);
      callback({ status: false, data: err });
    }
  };

  static getBookMaster = async (callback: (response: any) => void) => {
    try {
      const response = await axios.get(REACT_APP_GET_BOOK_MASTER, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        const responseData: ConstructionRateApi = response.data;
        callback({ status: true, data: responseData });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static createBook = async (
    bookData: any,

    callback: (response: {
      status: boolean;
      data: WeightingRateCreateApi;
    }) => void
  ) => {
    const url = `${REACT_APP_ADD_BOOK_MASTER}/${bookData.book_number}`;

    try {
      const response = await axios.post(url, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error creating ward:", err);
      callback({ status: false, data: err });
    }
  };

  static deleteBook = async (
    bookNumber: string,
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.delete(
        `${REACT_APP_DELETE_BOOK_MASTER}${bookNumber}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization:`Bearer ${TaxListApi.getStoredToken()}`

          },
        }
      );

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error deleting construction rate:", err);
      callback({ status: false, data: err });
    }
  };

  static getReceiptNumbers = async (bookNumber: string, callback: (response: any) => void) => {
    try {
      const url = `${REACT_APP_GET_AVAILABLE_RECIPTS}/${bookNumber}/available-receipts`;
      const response = await axios.get(url, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${TaxListApi.getStoredToken()}`

        },
      });

      if (response.status === 200) {
        const responseData: ConstructionRateApi = response.data;
        callback({ status: true, data: responseData });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err.message || 'An error occurred' });
    }
  };

  // Tax Calculate APIs
  static getBillsList = async (
    searchParams: URLSearchParams,
    financialYear: string
  ) => {
    try {
      const queryString = searchParams.toString();
      const response = await axios.get(
        `${VITE_BASE_URL}/v1/tax-calculate/bills-list?${queryString}&fy=${financialYear}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${TaxListApi.getStoredToken()}`,
          },
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message
      };
    }
  };

  static generateBill = async (financialYear: string) => {
    try {
      const response = await axios.post(
        `${VITE_BASE_URL}/v1/tax-calculate/generate_bill?financial_year=${financialYear}`,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${TaxListApi.getStoredToken()}`,
          },
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message
      };
    }
  };

  static getVarshikKarAkarni = async (
    propertyNumber: string,
    searchOn: string,
    financialYear: string
  ) => {
    try {
      const response = await axios.get(
        `${VITE_BASE_URL}/v1/annual-kar-akarani/getVarshikKarAkarni`,
        {
          params: {
            value: propertyNumber,
            searchOn: searchOn,
            fy: financialYear,
          },
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${TaxListApi.getStoredToken()}`,
          },
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message
      };
    }
  };

  static processWarshikKarAkarni = async (financialYear: string) => {
    try {
      const response = await axios.get(
        `${VITE_BASE_URL}/v1/annual-kar-akarani/processWarshikKarAkarni?fy=${financialYear}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${TaxListApi.getStoredToken()}`,
          },
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message
      };
    }
  };
}

export default TaxListApi;
