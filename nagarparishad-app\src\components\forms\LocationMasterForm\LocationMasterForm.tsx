import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  LocationSendApiObj,
  LocationMasterObject,
} from "../../../model/location-master";
import { useToast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { Loader2 } from "lucide-react";
import { useLocationMasterController } from "@/controller/master/LocationMasterController";

interface LocationMasterInterface {
  btnTitle: string;
  editData?: LocationMasterObject;
}

const LocationMasterForm = ({
  btnTitle,
  editData,
}: LocationMasterInterface) => {
  const { t } = useTranslation();

  const dynamicValues = {
    name: t("location.locationLabel"),
  };

  const schema = z.object({
    locationName: z.string().trim().min(1, t("errorsRequiredField")),
  });

  const { toast } = useToast();
  const { setOpen, refreshLocationList, setRefreshLocationList } =
    useContext(GlobalContext);
  const [loader, setLoader] = useState(false);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      locationName: editData?.locationName || "",
    },
  });

  const { createLocation, updateLocation } = useLocationMasterController();

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();
    setLoader(true);

    const DataResponse: LocationSendApiObj = {
      locationName: data.locationName,
    };

    if (editData?.location_id !== undefined) {
      updateLocation(
        { locationId: editData.location_id, locationData: DataResponse },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            form.reset({ locationName: "" });
            setOpen(false);
            setRefreshLocationList(!refreshLocationList);
            setLoader(false);
          },
          onError: (error) => {
            toast({
              title: error.message,
            });
            setLoader(false);
          },
        },
      );
    } else {
      createLocation(DataResponse, {
        onSuccess: () => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          form.reset({ locationName: "" });
          setOpen(false);
          setRefreshLocationList(!refreshLocationList);
          setLoader(false);
        },
        onError: (error) => {
          toast({
            title: error.message,
          });
          setLoader(false);
        },
      });
    }
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        locationName: editData.locationName || "",
      });
    } else {
      form.reset({
        locationName: "",
      });
    }
  }, [editData]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full md:w-full"
        >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="locationName"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("location.locationLabel")}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("location.locationLabel")}
                        className="mt-1"
                        {...field}
                      />
                    </FormControl>
                    {form.formState.errors.locationName && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
          
              <div className="grid-cols-subgrid  mb-1 ml-4 max-md:flex max-md:justify-end  pt-[32px] ">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
          </div>
       
        </form>
      </Form>
    </>
  );
};

export default LocationMasterForm;
