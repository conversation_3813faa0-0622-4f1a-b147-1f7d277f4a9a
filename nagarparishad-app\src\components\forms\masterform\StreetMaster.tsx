import React, { useContext, useRef, useState } from "react";
import WhiteContainer from "@/components/custom/WhiteContainer";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { MasterObjectColumnDef } from "@/model/zone-master";
import { StreetMasterObject } from "@/model/street-master";
import { MASTER } from "@/constant/config/api.config";
import { GlobalContext } from "@/context/GlobalContext";
import { useStreetMasterController } from "@/controller/master/StreetMasterController";
import StreetMasterForm from "../StreetMasterForm";
import { toast } from "@/components/ui/use-toast";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import AddNewBtn from "../../globalcomponent/AddNewForm";
import { ResponseData } from "@/model/auth/authServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

const StreetMaster = () => {
  const { setMasterComponent, setOpen } = useContext(GlobalContext);
  const { t } = useTranslation();
  const userRef = useRef(null);
  const { deleteStreet, streetLoading } = useStreetMasterController();

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Master, FormName.StreetMaster, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Master, FormName.StreetMaster, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Master, FormName.StreetMaster, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Master, FormName.StreetMaster, Action.CanDelete);
  const columns: ColumnDef<MasterObjectColumnDef>[] = [
    {
      accessorKey: `${t("SrNo")}`,
      header: `${t("SrNo")}`,
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: "streetOrRoadName",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("street.streetName")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
     cell: ({ row }: { row: any })  => (
        <div className="capitalize">{row.original.street_name}</div>
      ),
    },
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: `${t("Actions")}`,
            enableHiding: false,
            cell: ({ row }: { row: any }) => (
              <div className="flex space-x-2">
                {canUpdate && (
                  <Button
                    variant="submit"
                    className="h-8 w-8 p-0 justify-center "
                    onClick={() => handleEdit(row?.original)}
                  >
                    {t("table.edit")}
                  </Button>
                )}
                {CanDelete && (
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0 justify-center ml-2  !border-BlueText !text-BlueText"
                    onClick={() => handleDelete(row?.original)}
                  >
                    {t("table.delete")}
                  </Button>
                )}
              </div>
            ),
          },
        ]
      : []),
  ];

  function handleEdit(item: any): void {
    setOpen(true);
    setMasterComponent(
      <StreetMasterForm btnTitle={"street.updateBtn"} editData={item && item} />
    );
    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<StreetMasterObject | null>(
    null
  );
  const dynamicValues = {
    name: t("street.streetLabel"),
  };

  function handleDelete(item: any): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }
  const handleConfirmDelete = () => {
    if (selectedItem) {
      deleteStreet(selectedItem.streetOrRoadId, {
        onSuccess: (response: ResponseData) => {
          if (response?.statusCode && response?.statusCode === 200) {
            toast({
              title: t("api.formdelete", dynamicValues),
              variant: "success",
            });
          } else {
            toast({
              title: response?.message,
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }

    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  //List API for Zone
  const { streetList } = useStreetMasterController();
  console.log("streeent list",streetList);
  const MasterType: string = MASTER.STREET;

  return (
    <>
      <div className="flex h-fit ">
        <div
          className=" w-full mx-auto px-4 sm:px-6 lg:px-5 py-6  "
          ref={userRef && userRef}
        >
          <p className="w-full flex items-center justify-between ml-2  text-2xl font-semibold mb-2">
            {t("street.formTitle")}
          </p>
          {CanCreate && <WhiteContainer>
            {MasterType && <AddNewBtn masterType={MASTER.STREET} />}
          </WhiteContainer>}
          <WhiteContainer>
            <TanStackTable
              columns={columns}
              data={streetList}
              masterType={MASTER.STREET}
              searchColumn="streetOrRoadName"
              searchKey="searchStreet"
              loader={streetLoading ? true : false}
            />
          </WhiteContainer>
        </div>
      </div>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={""}
          onDelete={handleConfirmDelete}
        />
      )}
    </>
  );
};

export default StreetMaster;
