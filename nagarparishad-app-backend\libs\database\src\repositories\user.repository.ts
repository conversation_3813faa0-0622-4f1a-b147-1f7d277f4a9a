import { Repository } from 'typeorm';
import { UserMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { UserField } from '@helper/helpers';
import { Logger } from '@nestjs/common';
import { PaginationDto, PaginationOptions, paginate } from '@helper/helpers/Pagination';

export class UserRepository extends Repository<UserMasterEntity> {
  constructor(
    @InjectRepository(UserMasterEntity)
    private readonly userRepository: Repository<UserMasterEntity>,
  ) {
    super(
      userRepository.target,
      userRepository.manager,
      userRepository.queryRunner,
    );
  }

  async saveData(input: any): Promise<any> {
    let data = this.userRepository.create(input);
    data = await this.userRepository.save(data);
    return data;
  }

  async findAllData(): Promise<UserMasterEntity[]> {
    return await this.userRepository
      .createQueryBuilder('user_master')
      .orderBy('user_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string): Promise<UserMasterEntity> {
    return await this.userRepository
      .createQueryBuilder('user_master')
      .select(['user_master.user_id', 'user_master.email', 'role.role_id'])
      .leftJoin('user_master.role', 'role')
      .where('user_master.user_id = :user_id', {
        user_id: id,
      })
      .getOne();
  }
  async updateData(id: string, input: any) {
    if (input.role_id) {
      input.role = { role_id: input.role_id };
      delete input.role_id;
    }
  
    return await this.userRepository
      .createQueryBuilder('user_master')
      .update(UserMasterEntity)
      .set(input)
      .where('user_id = :user_id', { user_id: id })
      .execute();
  }
  

  async deleteData(id: string) {
    return await this.userRepository
      .createQueryBuilder('user_master')
      .softDelete()
      .where('user_id = :user_id', {
        user_id: id,
      })
      .execute();
  }

  async checkUserExists(field: UserField, value: string): Promise<boolean> {
    const user = await this.findOne({ where: { [field]: value } });
    return !!user;
  }

  async findByEmail(email: string): Promise<UserMasterEntity> {
    return await this.userRepository
      .createQueryBuilder('user_master')
      .select([
        'user_master.user_id',
        'user_master.firstname',
        'user_master.lastname',
        'user_master.email',
        'user_master.password',
        'role.role_id',
      ])
      .leftJoin('user_master.role', 'role')
      .where('user_master.email = :email', {
        email,
      })
      .getOne();
  }

  async getUsers(options: any) {
    try {
      const queryBuilder = this.userRepository
        .createQueryBuilder('user_master')
        .select([
          'user_master.user_id',
          'user_master.firstname',
          'user_master.lastname',
          'user_master.email',
          'user_master.isActive',
          'user_master.address',
          'user_master.profilePic',
          'user_master.lastLoginAt',
          'user_master.mobileNumber',
          'role_master.role_id',
          'role_master.roleName',
        ])
        .leftJoin('user_master.role', 'role_master');

      if (options.role) {
        queryBuilder.where('role_master.role_id = :role', {
          role: options.role,
        });
      }
   

      return paginate(queryBuilder, options, 'user_master');
    } catch (error) {
      throw error;
    }
  }
}
