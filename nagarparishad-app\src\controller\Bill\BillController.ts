import { ZoneObjectInterface } from "@/model/zone-master";
import Api from "../../services/ApiServices";
import {
  useQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import PaymentListApi from "@/services/PaymentLogsService";
import TaxListApi from "@/services/TaxServices";
import { Payment } from "@/model/Bill/payment";
import { useState } from "react";


// const fetchPaymentList = async ({ page = 1, limit = 10 } = {}) => {
//   return new Promise((resolve, reject) => {
//     PaymentListApi.getPaymentList(page, limit, (response) => {
//       if (response.status) {
//         resolve(response.data);
//       } else {
//         reject(response.data);
//       }
//     });
//   });
// };
  


  const updatePayment = async ({ id, paymentData }: { id: string; paymentData: Partial<Payment> }) => {
    return new Promise((resolve, reject) => {
      PaymentListApi.updatePayment(id, paymentData, (response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      });
    });
  };
  

  
const deletePaymentListRate = async (PaymentListRateId: string) => {
  return new Promise((resolve, reject) => {
    TaxListApi.deletePaymentListRate(PaymentListRateId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const usePaymentListRateController = () => {
  const queryClient = useQueryClient();

  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [searchValue, setSearchValue] = useState<string>(""); // New state for search value
  const [searchOn, setSearchOn] = useState<string>("property_number"); // Default search field

  const {
    data: paginatedPropertyData,
    isLoading: paymentLoading,
  } = useQuery({
    queryKey: ["paymentListMaster", page, limit, searchValue, searchOn],
    queryFn: () =>
      PaymentListApi.getPaymentList(limit, page, searchValue, searchOn),
    staleTime: 1000 * 60 * 2,
    onSuccess: (data) => {
      setPage(Number(data.page));
      setLimit(Number(data.limit));
    },
  });
  
  
  const PaymentListRateList = paginatedPropertyData || [];
  const totalRecords = paginatedPropertyData?.data?.total || 0;
  const totalPages = paginatedPropertyData?.data?.totalPages || 0;
  

  

  const updatePaymentMutation = useMutation({
    mutationFn: updatePayment,
  
    onMutate: async ({ id, paymentData }) => {
      await queryClient.cancelQueries({ queryKey: ["PaymentList"] });
  
      const previousPayments = queryClient.getQueryData(["PaymentList"]);
  
      queryClient.setQueryData(["PaymentList"], (old: any) => {
        const updatedPayments = old?.data?.map((payment: any) =>
          payment.payment_id === id ? { ...payment, ...paymentData } : payment
        );
        console.log("Updated query data:", updatedPayments);
        return updatedPayments;
      });
  
      return { previousPayments };
    },
  
    onError: (err, { id, paymentData }, context) => {
      queryClient.setQueryData(["PaymentList"], context.previousPayments);
      console.error("Error updating payment:", err);
    },
  
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["PaymentList"] });
    },
  });
 
  const deletePaymentListRateMutation = useMutation({
    mutationFn: deletePaymentListRate,
    onMutate: async (constructionRateId) => {
      await queryClient.cancelQueries({ queryKey: ["PaymentListmaster"] });

      const previousConstructionRate = queryClient.getQueryData(["PaymentListRatemaster"]);

      queryClient.setQueryData(["PaymentListRatemaster"], (old: any) => {
        const updatedConstructionRate = old?.data?.filter((constructionRate: any) => constructionRate.usage_type_id !== constructionRateId);
        return updatedConstructionRate;
      });

      return { previousConstructionRate };
    },
    onError: (err, ward_id, context) => {
      queryClient.setQueryData(["PaymentListRatemaster"], context.previousConstructionRate);
      console.error("Error deleting ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["PaymentListRatemaster"] });
    },
  });
  return {
    PaymentListRateList,
    paymentLoading,
    pagination: {
      page,
      setPage,
      limit,
      setLimit,
      totalPages,
      totalRecords,
    },
    searchValue,
    setSearchValue, // Expose search value setter
    searchOn,
    setSearchOn, // Expose search field setter
  };
    // updatePaymentListRate: updatePaymentListRateMutation.mutate,
    // deletePaymentListRate: deletePaymentListRateMutation.mutate,
  };

  



