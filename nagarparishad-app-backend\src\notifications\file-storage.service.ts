import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);
const mkdir = promisify(fs.mkdir);
const stat = promisify(fs.stat);

@Injectable()
export class FileStorageService {
  private readonly logger = new Logger(FileStorageService.name);
  private readonly tempDir = path.join(process.cwd(), 'temp', 'notifications');

  constructor() {
    this.ensureTempDirectoryExists();
  }

  private async ensureTempDirectoryExists(): Promise<void> {
    try {
      await stat(this.tempDir);
    } catch (error) {
      if (error.code === 'ENOENT') {
        await mkdir(this.tempDir, { recursive: true });
        this.logger.log(`Created temp directory: ${this.tempDir}`);
      } else {
        throw error;
      }
    }
  }

  async saveFile(
    fileName: string, 
    fileBuffer: Buffer, 
    userId: string
  ): Promise<{ filePath: string; fileSize: number }> {
    try {
      // Create user-specific subdirectory
      const userDir = path.join(this.tempDir, userId);
      await this.ensureDirectoryExists(userDir);

      // Generate unique filename with timestamp
      const timestamp = Date.now();
      const fileExtension = path.extname(fileName);
      const baseName = path.basename(fileName, fileExtension);
      const uniqueFileName = `${baseName}_${timestamp}${fileExtension}`;
      
      const filePath = path.join(userDir, uniqueFileName);
      
      await writeFile(filePath, fileBuffer);
      
      const fileSize = fileBuffer.length;
      
      this.logger.log(`File saved: ${filePath}, Size: ${fileSize} bytes`);
      
      return { filePath, fileSize };
    } catch (error) {
      this.logger.error(`Error saving file ${fileName}:`, error);
      throw error;
    }
  }

  async deleteFile(filePath: string): Promise<void> {
    try {
      await unlink(filePath);
      this.logger.log(`File deleted: ${filePath}`);
    } catch (error) {
      if (error.code !== 'ENOENT') {
        this.logger.error(`Error deleting file ${filePath}:`, error);
        throw error;
      }
      // File doesn't exist, which is fine
    }
  }

  async fileExists(filePath: string): Promise<boolean> {
    try {
      await stat(filePath);
      return true;
    } catch (error) {
      return false;
    }
  }

  async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await stat(filePath);
      return stats.size;
    } catch (error) {
      this.logger.error(`Error getting file size for ${filePath}:`, error);
      return 0;
    }
  }

  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await stat(dirPath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        await mkdir(dirPath, { recursive: true });
      } else {
        throw error;
      }
    }
  }

  getTempDirectory(): string {
    return this.tempDir;
  }

  generateDownloadUrl(notificationId: string): string {
    return `/api/v1/notifications/download/${notificationId}`;
  }

  async cleanupExpiredFiles(filePaths: string[]): Promise<void> {
    const deletePromises = filePaths.map(filePath => this.deleteFile(filePath));
    await Promise.allSettled(deletePromises);
    this.logger.log(`Cleaned up ${filePaths.length} expired files`);
  }

  async getUserFileCount(userId: string): Promise<number> {
    try {
      const userDir = path.join(this.tempDir, userId);
      const files = await fs.promises.readdir(userDir);
      return files.length;
    } catch (error) {
      return 0;
    }
  }

  async getTotalStorageSize(): Promise<number> {
    try {
      const calculateDirSize = async (dirPath: string): Promise<number> => {
        let totalSize = 0;
        const items = await fs.promises.readdir(dirPath);
        
        for (const item of items) {
          const itemPath = path.join(dirPath, item);
          const stats = await stat(itemPath);
          
          if (stats.isDirectory()) {
            totalSize += await calculateDirSize(itemPath);
          } else {
            totalSize += stats.size;
          }
        }
        
        return totalSize;
      };

      return await calculateDirSize(this.tempDir);
    } catch (error) {
      this.logger.error('Error calculating total storage size:', error);
      return 0;
    }
  }
}
