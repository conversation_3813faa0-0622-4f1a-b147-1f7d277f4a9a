import {
    BaseEntity,
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    DeleteDateColumn,
    <PERSON>in<PERSON><PERSON>umn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    ManyToOne,
    OneToMany,
  } from 'typeorm';
import { Property_type_class_master } from './property_type_classMaster.entity';
import { ReassessmentRange } from './reassesment_range.entity';


  @Entity('master_depreciation_rate')
  export class Master_depreciation_rate extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    depreciation_rate_id: string;


    @ManyToOne(() => Property_type_class_master, (property_type) => property_type.property_type_class_id)
    @JoinColumn({ name: 'property_type_class_id' })
    property_type_class_id: Property_type_class_master;

    @Column({ type: 'float', nullable: false , default: 0})
    from_age: number;

    @Column({ type: 'float', nullable: false , default: 0})
    to_age: number;

    @Column({ type: String, nullable: true })
    financial_year: string;

    @Column({ type: 'float', nullable: false , default: 0})
    value: number;

    @Column({ type: String, nullable: true, default: 'Active' })
    status: string;

    @ManyToOne(() => ReassessmentRange, (reassessmentRange) => reassessmentRange.reassessment_range_id, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn({ name: 'reassessment_range_id' })
    reassessmentRange: ReassessmentRange;

    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;

    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;

    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
  }

