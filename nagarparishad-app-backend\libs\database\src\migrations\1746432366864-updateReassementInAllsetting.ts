import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateReassementInAllsetting1746432366864 implements MigrationInterface {
    name = 'UpdateReassementInAllsetting1746432366864'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" DROP COLUMN "value"`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "year_wise_penalty_data" jsonb DEFAULT '{}'`);
        await queryRunner.query(`ALTER TABLE "previous_property_owners" ADD "reassessment_range_id" uuid`);
        await queryRunner.query(`ALTER TABLE "master_rr_rate" ADD "reassessment_range_id" uuid`);
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" ADD "reassessment_range_id" uuid`);
        await queryRunner.query(`ALTER TABLE "master_tax_rate" ADD "reassessment_range_id" uuid`);
        await queryRunner.query(`ALTER TABLE "master_weighting_rate" ADD "reassessment_range_id" uuid`);
        await queryRunner.query(`ALTER TABLE "master_depreciation_rate" ADD "reassessment_range_id" uuid`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" ADD "reassessment_range_id" uuid`);
        await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" ADD "actual_value" numeric NOT NULL`);
        await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" ADD "penalty_value" numeric NOT NULL`);
    await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" DROP COLUMN "tax_percentage"`);
        await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" ADD "tax_percentage" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "previous_property_owners" ADD CONSTRAINT "FK_64027564953bc2d97da2bda469d" FOREIGN KEY ("reassessment_range_id") REFERENCES "reassessment_range"("reassessment_range_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_rr_rate" ADD CONSTRAINT "FK_c49933a0aa2ed584366ed8d7d10" FOREIGN KEY ("reassessment_range_id") REFERENCES "reassessment_range"("reassessment_range_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" ADD CONSTRAINT "FK_beec6c7e0672c20daeb9a8e3120" FOREIGN KEY ("reassessment_range_id") REFERENCES "reassessment_range"("reassessment_range_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_tax_rate" ADD CONSTRAINT "FK_501f0870714ebd36acee63e93ea" FOREIGN KEY ("reassessment_range_id") REFERENCES "reassessment_range"("reassessment_range_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_weighting_rate" ADD CONSTRAINT "FK_bc575e6d642c6a5085c0b1747c2" FOREIGN KEY ("reassessment_range_id") REFERENCES "reassessment_range"("reassessment_range_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_depreciation_rate" ADD CONSTRAINT "FK_8eb9178395dc5ee0d5ab33c6d10" FOREIGN KEY ("reassessment_range_id") REFERENCES "reassessment_range"("reassessment_range_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" ADD CONSTRAINT "FK_57c2a5c332d888f9e6940cbe75c" FOREIGN KEY ("reassessment_range_id") REFERENCES "reassessment_range"("reassessment_range_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
  }

    public async down(queryRunner: QueryRunner): Promise<void> {
       await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" DROP CONSTRAINT "FK_57c2a5c332d888f9e6940cbe75c"`);
        await queryRunner.query(`ALTER TABLE "master_depreciation_rate" DROP CONSTRAINT "FK_8eb9178395dc5ee0d5ab33c6d10"`);
        await queryRunner.query(`ALTER TABLE "master_weighting_rate" DROP CONSTRAINT "FK_bc575e6d642c6a5085c0b1747c2"`);
        await queryRunner.query(`ALTER TABLE "master_tax_rate" DROP CONSTRAINT "FK_501f0870714ebd36acee63e93ea"`);
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" DROP CONSTRAINT "FK_beec6c7e0672c20daeb9a8e3120"`);
        await queryRunner.query(`ALTER TABLE "master_rr_rate" DROP CONSTRAINT "FK_c49933a0aa2ed584366ed8d7d10"`);
        await queryRunner.query(`ALTER TABLE "previous_property_owners" DROP CONSTRAINT "FK_64027564953bc2d97da2bda469d"`);
        await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "FK_df738d1108e8291026728e28c55"`);
        await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0"`);
        await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" DROP COLUMN "tax_percentage"`);
        await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" ADD "tax_percentage" numeric NOT NULL`);
   await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" DROP COLUMN "penalty_value"`);
        await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" DROP COLUMN "actual_value"`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" DROP COLUMN "reassessment_range_id"`);
        await queryRunner.query(`ALTER TABLE "master_depreciation_rate" DROP COLUMN "reassessment_range_id"`);
        await queryRunner.query(`ALTER TABLE "master_weighting_rate" DROP COLUMN "reassessment_range_id"`);
        await queryRunner.query(`ALTER TABLE "master_tax_rate" DROP COLUMN "reassessment_range_id"`);
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" DROP COLUMN "reassessment_range_id"`);
        await queryRunner.query(`ALTER TABLE "master_rr_rate" DROP COLUMN "reassessment_range_id"`);
        await queryRunner.query(`ALTER TABLE "previous_property_owners" DROP COLUMN "reassessment_range_id"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "year_wise_penalty_data"`);
        await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" ADD "value" numeric NOT NULL`);
    }

}
