{"name": "test1", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js ", "migration": "yarn run build && yarn run typeorm -d libs/database/src/datasource.ts  migration:run", "migration:generate": "yarn run typeorm migration:generate  -d ./libs/database/src/datasource.ts", "migration:create": "yarn run typeorm migration:create", "migration:revert": "yarn run typeorm -d libs/database/src/datasource.ts  migration:revert", "create-entity": "yarn run typeorm entity:create"}, "dependencies": {"@nestjs/axios": "^3.0.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.4", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.5", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.10", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^7.3.1", "@nestjs/throttler": "^5.1.2", "@nestjs/typeorm": "^10.0.2", "aws-sdk": "^2.1691.0", "axios": "^1.11.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "csv-parse": "^5.5.6", "csv-parser": "^3.0.0", "csvtojson": "^2.0.10", "date-fns": "^4.1.0", "ejs": "^3.1.10", "event-emitter": "^0.3.5", "exceljs": "^4.4.0", "form-data": "^4.0.2", "jszip": "^3.10.1", "multer": "^1.4.5-lts.1", "nest-csv-parser": "^2.0.4", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdf-lib": "^1.17.1", "pg": "^8.11.5", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "xlsx": "^0.18.5", "archiver": "^7.0.1", "rimraf": "^6.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/jszip": "^3.4.1", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.17", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src/", "<rootDir>/libs/"], "moduleNameMapper": {"^libs/database(|/.*)$": "<rootDir>/libs/database/src/$1", "^@helper/helpers(|/.*)$": "<rootDir>/libs/helpers/src/$1", "^@jwt/jwt-auth(|/.*)$": "<rootDir>/libs/jwt-auth/src/$1"}}}