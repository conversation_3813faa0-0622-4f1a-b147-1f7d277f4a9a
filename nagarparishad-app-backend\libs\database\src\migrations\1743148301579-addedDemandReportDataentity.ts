import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddedDemandReportDataentity1743148301579
  implements MigrationInterface
{
  name = 'AddedDemandReportDataentity1743148301579';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "demande_report_data" ("demande_report_data_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "all_property_tax_sum" double precision DEFAULT '0', "all_property_tax_sum_prev_remaining" double precision DEFAULT '0', "all_property_tax_sum_curr_remaining" double precision DEFAULT '0', "total_amount" double precision DEFAULT '0', "tax_type_1_remaining" double precision DEFAULT '0', "tax_type_1_prev_remaining" double precision DEFAULT '0', "tax_type_1_curr_remaining" double precision DEFAULT '0', "tax_type_1_paid" double precision DEFAULT '0', "tax_type_1_prev_paid" double precision DEFAULT '0', "tax_type_1_curr_paid" double precision DEFAULT '0', "tax_type_2_remaining" double precision DEFAULT '0', "tax_type_2_prev_remaining" double precision DEFAULT '0', "tax_type_2_curr_remaining" double precision DEFAULT '0', "tax_type_2_paid" double precision DEFAULT '0', "tax_type_2_prev_paid" double precision DEFAULT '0', "tax_type_2_curr_paid" double precision DEFAULT '0', "tax_type_3_remaining" double precision DEFAULT '0', "tax_type_3_prev_remaining" double precision DEFAULT '0', "tax_type_3_curr_remaining" double precision DEFAULT '0', "tax_type_3_paid" double precision DEFAULT '0', "tax_type_3_prev_paid" double precision DEFAULT '0', "tax_type_3_curr_paid" double precision DEFAULT '0', "tax_type_4_remaining" double precision DEFAULT '0', "tax_type_4_prev_remaining" double precision DEFAULT '0', "tax_type_4_curr_remaining" double precision DEFAULT '0', "tax_type_4_paid" double precision DEFAULT '0', "tax_type_4_prev_paid" double precision DEFAULT '0', "tax_type_4_curr_paid" double precision DEFAULT '0', "tax_type_5_remaining" double precision DEFAULT '0', "tax_type_5_prev_remaining" double precision DEFAULT '0', "tax_type_5_curr_remaining" double precision DEFAULT '0', "tax_type_5_paid" double precision DEFAULT '0', "tax_type_5_prev_paid" double precision DEFAULT '0', "tax_type_5_curr_paid" double precision DEFAULT '0', "tax_type_6_remaining" double precision DEFAULT '0', "tax_type_6_prev_remaining" double precision DEFAULT '0', "tax_type_6_curr_remaining" double precision DEFAULT '0', "tax_type_6_paid" double precision DEFAULT '0', "tax_type_6_prev_paid" double precision DEFAULT '0', "tax_type_6_curr_paid" double precision DEFAULT '0', "tax_type_7_remaining" double precision DEFAULT '0', "tax_type_7_prev_remaining" double precision DEFAULT '0', "tax_type_7_curr_remaining" double precision DEFAULT '0', "tax_type_7_paid" double precision DEFAULT '0', "tax_type_7_prev_paid" double precision DEFAULT '0', "tax_type_7_curr_paid" double precision DEFAULT '0', "tax_type_8_remaining" double precision DEFAULT '0', "tax_type_8_prev_remaining" double precision DEFAULT '0', "tax_type_8_curr_remaining" double precision DEFAULT '0', "tax_type_8_paid" double precision DEFAULT '0', "tax_type_8_prev_paid" double precision DEFAULT '0', "tax_type_8_curr_paid" double precision DEFAULT '0', "tax_type_9_remaining" double precision DEFAULT '0', "tax_type_9_prev_remaining" double precision DEFAULT '0', "tax_type_9_curr_remaining" double precision DEFAULT '0', "tax_type_9_paid" double precision DEFAULT '0', "tax_type_9_prev_paid" double precision DEFAULT '0', "tax_type_9_curr_paid" double precision DEFAULT '0', "tax_type_10_remaining" double precision DEFAULT '0', "tax_type_10_prev_remaining" double precision DEFAULT '0', "tax_type_10_curr_remaining" double precision DEFAULT '0', "tax_type_10_paid" double precision DEFAULT '0', "tax_type_10_prev_paid" double precision DEFAULT '0', "tax_type_10_curr_paid" double precision DEFAULT '0', "other_tax_sum_tax_remaining" double precision DEFAULT '0', "other_tax_sum_tax_prev_remaining" double precision DEFAULT '0', "other_tax_sum_tax_curr_remaining" double precision DEFAULT '0', "other_tax_sum_tax_paid" double precision DEFAULT '0', "other_tax_sum_tax_prev_paid" double precision DEFAULT '0', "other_tax_sum_tax_curr_paid" double precision DEFAULT '0', "status" character varying, "financial_year" character varying NOT NULL, "remaining_amount" character varying, "property_number" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "payment_info_id" uuid, "property_id" uuid, CONSTRAINT "REL_d16950c2038543253d0df2bd89" UNIQUE ("payment_info_id"), CONSTRAINT "PK_d4f5f9a3a87f055465e55b2a390" PRIMARY KEY ("demande_report_data_id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "demande_report_data" ADD CONSTRAINT "FK_d16950c2038543253d0df2bd89a" FOREIGN KEY ("payment_info_id") REFERENCES "receipt"("receipt_id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "demande_report_data" ADD CONSTRAINT "FK_a673c6f54459162ee316c0c0d1b" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "demande_report_data" DROP CONSTRAINT "FK_a673c6f54459162ee316c0c0d1b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "demande_report_data" DROP CONSTRAINT "FK_d16950c2038543253d0df2bd89a"`,
    );
    await queryRunner.query(`DROP TABLE "deleted_property_usage_details"`);
    await queryRunner.query(`DROP TABLE "demande_report_data"`);
  }
}
