export interface ApiResponse {
  status: any;
  data: any;
  statusCode: boolean | number;
  message: any;
}

export interface UserLoginData {
  email: string;
  password: string;
}

export interface OtpValidationData {
  email: string;
  otp: string;
}
export interface OtpValidationRes {
  statusCode: boolean;
  message: any;
}

export interface OtpAuthResponseData {
  statusCode: number;
  message: string;
  data: AuthResponseUserData;
}

export interface AuthResponseUserData {
  accessToken: string;
  refreshToken: string;
  user: AuthResponseUser;
}

export interface AuthResponseUser {
  firstName: string;
  lastName: string;
  email: string;
  role: number;
  Permissions: any;
}

export interface AuthUserRegister {
  firstname: string;
  lastname: string;
  email: string;
  password: string;
  isActive?: boolean;
  address: string;
  role: number;
  mobileNumber: string;
  profilePic?: string;
}

export interface ResponseData {
  statusCode: number;
  message: string;
  data: any;
}
