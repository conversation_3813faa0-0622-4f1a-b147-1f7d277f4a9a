import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportPropertyupdatecoladdded51725256117646 implements MigrationInterface {
    name = 'ImportPropertyupdatecoladdded51725256117646'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ADD "duplicate_property_no" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "duplicate_property_no"`);
    }

}
