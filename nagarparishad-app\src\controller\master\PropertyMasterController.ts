import { PropertytypeMasterObject } from "@/model/propertytype-master";
import Api from "../../services/ApiServices";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

const fetchPropertytype = async () => {
  const response = await Api.getAllPropertyType();
  return response.data;
};

const fetchPropertytypeStats = async () => {
  const response = await Api.getAllpropertytypeStats();
  return response.data;
};

const createPropertytype = async (
  propertytypeData: PropertytypeMasterObject,
) => {
  return new Promise((resolve, reject) => {
    Api.createPropertyType(propertytypeData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updatePropertytype = async ({ propertytypeId, propertytypeData }) => {
  return new Promise((resolve, reject) => {
    Api.updatePropertyType(propertytypeId, propertytypeData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deletePropertytype = async (streetId: string) => {
  return new Promise((resolve, reject) => {
    Api.deletePropertyType(streetId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const usePropertytypeMasterController = () => {
  const queryClient = useQueryClient();

  const { data: propertytypeListResponse, error, isLoading:propertyTypeLoading } = useQuery({
    queryKey: ["propertytypemaster"],
    queryFn: fetchPropertytype,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });


  const { data: propertytypeStats, error:propertytypeStatsError, isLoading:propertytypeStatsLoading } = useQuery({
    queryKey: ["propertytypeStats"],
    queryFn: fetchPropertytypeStats,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });


  if (error) {
    console.error("Error fetching streets:", error);
  }

  const createPropertytypeMutation = useMutation({
    mutationFn: createPropertytype,
    onMutate: async (newStreet) => {
      await queryClient.cancelQueries({ queryKey: ["propertytypemaster"] });
      const previousStreet = queryClient.getQueryData(["propertytypemaster"]);
      queryClient.setQueryData(
        ["propertytypemaster"],
        (old: PropertytypeMasterObject) => {
          const updatedData = [newStreet, ...old];
          // Log the updated data
          return updatedData;
        },
      );
      return { previousStreet };
    },
    onError: (err, newWard, context) => {
      queryClient.setQueryData(["propertytypemaster"], context.previousStreet);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["propertytypemaster"] });
    },
  });

  const updatePropertytypeMutation = useMutation({
    mutationFn: updatePropertytype,
    onMutate: async ({ propertytypeId, propertytypeData }) => {
      await queryClient.cancelQueries({ queryKey: ["propertytypemaster"] });

      const previouspropertytype = queryClient.getQueryData([
        "propertytypemaster",
      ]);

      queryClient.setQueryData(["propertytypemaster"], (old) => {
        const updatedpropertytype = old?.map(
          (propertytype: PropertytypeMasterObject) =>
            propertytype?.propertyType_id === propertytypeId
              ? { ...propertytype, ...propertytypeData }
              : propertytype,
        );

        return updatedpropertytype;
      });

      return { previouspropertytype };
    },
    onError: (err, context) => {
      queryClient.setQueryData(
        ["propertytypemaster"],
        context.previouspropertytype,
      );
      console.error("Error updating zone:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["propertytypemaster"] });
    },
  });

  const deletePropertytypeMutation = useMutation({
    mutationFn: deletePropertytype,
    onMutate: async (propertytypeId) => {
      await queryClient.cancelQueries({ queryKey: ["propertytypemaster"] });

      const previouspropertytype = queryClient.getQueryData([
        "propertytypemaster",
      ]);

      queryClient.setQueryData(["propertytypemaster"], (old) => {
        const updatedPropertytype = old.filter(
          (propertytype: PropertytypeMasterObject) =>
            propertytype.propertyType_id !== propertytypeId,
        );
        return updatedPropertytype;
      });
      return { previouspropertytype };
    },
    onError: (err, wardId, context) => {
      queryClient.setQueryData(
        ["propertytypemaster"],
        context.previouspropertytype,
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["propertytypemaster"] });
    },
  });

  return {
    propertytypeList: propertytypeListResponse || [],
    propertyTypeLoading,
    propertytypeStats:propertytypeStats || [],
    propertytypeStatsLoading: propertytypeStatsLoading,
    propertytypeStatsError: propertytypeStatsError,
    createPropertytype: createPropertytypeMutation.mutate,
    updatePropertytype: updatePropertytypeMutation.mutate,
    deletePropertytype: deletePropertytypeMutation.mutate,
  };
};
