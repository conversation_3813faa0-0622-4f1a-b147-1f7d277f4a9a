import { MigrationInterface, QueryRunner } from "typeorm";

export class CretedFerfarAnddoeDetails1731406647194 implements MigrationInterface {
    name = 'CretedFerfarAnddoeDetails1731406647194'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "property_fod_details" ("property_fod_details_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "new_property_numbers" character varying array NOT NULL, "reason" text, "image_path" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, CONSTRAINT "PK_f01d5622fbc695d3b7e1fa7626c" PRIMARY KEY ("property_fod_details_id"))`);
        await queryRunner.query(`CREATE TABLE "property_ferfar_details" ("property_ferfar_detail_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "previous_owner_names" character varying array NOT NULL, "reason" text, "image_path" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, CONSTRAINT "PK_c4d94416b133f279f6bd1e2fefa" PRIMARY KEY ("property_ferfar_detail_id"))`);
        await queryRunner.query(`ALTER TABLE "property" ADD "parent_propertyNumber" character varying`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" DROP CONSTRAINT "FK_89469a4ad05a7ed3f78d2df185a"`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" ADD CONSTRAINT "UQ_89469a4ad05a7ed3f78d2df185a" UNIQUE ("usage_sub_type_master_id")`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" ADD CONSTRAINT "FK_89469a4ad05a7ed3f78d2df185a" FOREIGN KEY ("usage_sub_type_master_id") REFERENCES "usage_sub_type_master"("usage_sub_type_master_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property_fod_details" ADD CONSTRAINT "FK_e217be49a6c5cdce8778f078112" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property_ferfar_details" ADD CONSTRAINT "FK_4b9cc36be3bc02368cb4b4d4096" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property_ferfar_details" DROP CONSTRAINT "FK_4b9cc36be3bc02368cb4b4d4096"`);
        await queryRunner.query(`ALTER TABLE "property_fod_details" DROP CONSTRAINT "FK_e217be49a6c5cdce8778f078112"`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" DROP CONSTRAINT "FK_89469a4ad05a7ed3f78d2df185a"`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" DROP CONSTRAINT "UQ_89469a4ad05a7ed3f78d2df185a"`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" ADD CONSTRAINT "FK_89469a4ad05a7ed3f78d2df185a" FOREIGN KEY ("usage_sub_type_master_id") REFERENCES "usage_sub_type_master"("usage_sub_type_master_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "parent_propertyNumber"`);
        await queryRunner.query(`DROP TABLE "property_ferfar_details"`);
        await queryRunner.query(`DROP TABLE "property_fod_details"`);
    }

}
