import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Patch,
  Param,
  Delete,
  Query,
  ValidationPipe,
  Res,
  Req,
} from '@nestjs/common';

import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  Form,
  Permissions,
} from '@helper/helpers/role-based-access/permission.decorator';
import { RegisterService } from './register.service';
import { Response } from 'express';
import { Public } from '@jwt/jwt-auth/decorators/public.decorator';

@ApiTags('Register Master')
@Controller('register')
export class RegisterController {
  constructor(private readonly registerService: RegisterService) {}

  @Form('Register Report')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Print Namuna Eight Report' })
  @ApiResponse({ status: 200, description: 'Returns Namuna Eight Report' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Post('print_namuna_eight')
  findAll(@Query() Params, @Res() res: Response, @Req() req: any) {
    const userId = req.user?.sub; // Get user ID from JWT token
    return this.registerService.printNamunaEight(Params, res, userId);
  }

@Public()
    @Post('print_namuna_eight_by_property_numbers')
  findAllNumbers(@Body() body: { propertyNumbers: string[] }, @Query() Params, @Res() res: Response, @Req() req: any) {
    const userId = req.user?.sub; // Get user ID from JWT token
    return this.registerService.printNamunaEightByPropertyNumbers({ ...Params, ...body }, res, userId);
  }

  @Form('Register Report')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Print Namuna Nine Report' })
  @ApiResponse({ status: 200, description: 'Returns Namuna Nine Report' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Post('print_namuna_nine')
  findAllForNamunNine(@Query() Params, @Res() res: Response, @Req() req: any) {
    const userId = req.user?.sub; // Get user ID from JWT token
    return this.registerService.printNamunaNine(Params, res, userId);
  }

  @Form('Assessment Report')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get Assessment Report' })
  @ApiResponse({ status: 200, description: 'Returns Assessment Report' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('assessment-report')
  getAssessmentReport(@Query() params: any) {
    return this.registerService.getAssessmentReport(params);
  }

  @Form('Assessment Report')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Print Assessment Report' })
  @ApiResponse({ status: 200, description: 'Returns Assessment Report PDF' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Post('print-assessment-report')
  printAssessmentReport(@Query() params: any, @Res() res: Response) {
    return this.registerService.printAssessmentReport(params, res);
  }
}
