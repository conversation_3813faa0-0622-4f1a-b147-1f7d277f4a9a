import { FirstMigration1723178247276 } from './1723178247276-first_migration';
import { ImportTable1723195284623 } from './1723195284623-import_table';
import { ImportTable21723200309685 } from './1723200309685-import_table2';
import { ImportTable31723205170422 } from './1723205170422-import_table3';
import { UpdateTable51724152316783 } from './1724152316783-update_table_5';
import { ImportPropertyUpdate1724656205503 } from './1724656205503-import_property_update';
import { ImportPropertyUpdate11724666242472 } from './1724666242472-import_property_update1';
import { ImportPropertyStatsTable1724741413292 } from './1724741413292-import_property_stats-Table';
import { ImportPropertyupdatecoladdded1724757083425 } from './1724757083425-import_propertyupdatecoladdded'
import { ImportPropertyupdatecoladdded11724825479325 } from './1724825479325-import_propertyupdatecoladdded1'
import { ImportPropertyupdatecoladdded21724829253259 } from './1724829253259-import_propertyupdatecoladdded2'
import { ImportPropertyupdatecoladdded31724932962526 } from './1724932962526-import_propertyupdatecoladdded3'
import { ImportPropertyupdatecoladdded41724992102460 } from './1724992102460-import_propertyupdatecoladdded4'
import { ImportPropertyupdatecoladdded51725256117646 } from './1725256117646-import_propertyupdatecoladdded5'
import { PropertyColUpdated1725361944271 } from './1725361944271-property_col_updated'
import { ImportPropertyColAdded61725960708731 } from './1725960708731-import_property_col_added_6'
import { LogsTableCreate1726214145581 } from './1726214145581-logs_table_create';
import { LogsTableAlter11726230078342} from  './1726230078342-logs_table_alter1';
import { MilkatKar1726683379932 } from './1726683379932-milkatKar';
import { KarakaraniUpdate1726810920684 } from './1726810920684-karakarani_update'; 
import { KarakaraniUpdate31726814938123 } from './1726814938123-karakarani_update3';
// import { MasterRrRateCreate1727007212804 } from './1727007212804-master_rr_rate_create'
import { Warshik11727884874459 } from './1727884874459-warshik1';
import { WarshikarNullable1727896106718 } from './1727896106718-warshikar-nullable';
import { MasterTablesCreated1727175716342 } from './1727175716342-master_tables_created'; 
import { ImportPropertiesUpdatedColAdded1727268386459 } from './1727268386459-import_properties_updated_col_added';
import { CreateTableCommonTableProperty1728025663372 } from './1728025663372-create_table_common_table_property';
import {PropertyOwnerAddColRemark1728139459381 } from './1728139459381-property_owner_add_col_remark'
import { PropertyOwnerAddColLastActionDone1728196195891 } from './1728196195891-property_owner_add_col_last_action_done'
import { PropertyOwnerAddColIsOwner1728197252539 } from './1728197252539-property_owner_add_col_is_owner'
import {ColAddedProperty1728237403823} from './1728237403823-col_added_property'
import { CreateColPropertyEntity1728364698064 } from './1728364698064-create_col_property_entity'
import  { WarshikKarColAddes1728386656262 } from './1728386656262-warshik_kar_col_addes'
import  { ColAddedNewPropertyNumberInProperty1728471037127 } from './1728471037127-col_added_newPropertyNumberInProperty'

import { TaxFyReocrdColUpdate1728475625695 }    from './1728475625695-tax_fy_reocrd_col_update'
import { OptlogsUpdate1729279634258 } from './1729279634258-optlogs-update';
import { VerifionId1729417050331 } from './1729417050331-verifionId';
import {CreattableClassMaster1729153065904} from './1729153065904-creattable_classMaster'

import {CreateTableWrongPropertyType1729160070398} from './1729160070398-createTable_wrongPropertyType'

import {AddedColumn1729682743836} from './1729682743836-addedColumn'
import { AddedColumn1729762845567 } from './1729762845567-addedColumn';
import { CreateFloorTable1730193933912 } from './1730193933912-createFloorTable';
import { CreateGhanKachraTable1730268509069 } from './1730268509069-createGhanKachraTable';
import { CretedFerfarAnddoeDetails1731406647194 } from './1731406647194-cretedFerfarAnddoeDetails';
import { ChangeUniquConstrint1731567696570 } from './1731567696570-changeUniquConstrint';
import { PropertyUpdateDataType1736750797134 } from './1736750797134-propertyUpdateDataType';
import { TableUpdates1737093718720 } from './1737093718720-table_updates';
import {TaxPendingDuesTable1738059807856}  from './1738059807856-tax_pending_dues_table';
import { Billdata1739094982159 } from './1739094982159-billdata'
import { PaymentInfoTable1739531535523 } from './1739531535523-paymentInfoTable';
import { CreatedTableRelatedToPayments1739961971062 } from './1739961971062-createdTableRelatedToPayments';
import { ChangeInTable1740058349321 } from './1740058349321-changeInTable';
import { AddedColumneInrecipt1740120709754 } from './1740120709754-addedColumneInrecipt';
import { ChangeInentity1740138395451 } from './1740138395451-changeInentity';
import { ChangeInentityInRecipt1740143766492 } from './1740143766492-changeInentityRecipt';
import { ChangesInPaylBiltable1740560059371 } from './1740560059371-changesInPaylBiltable';
import { ChangesInTableColumn1740564574207 } from './1740564574207-changesInTableColumn';
import { AddedParsarKarInMilkartax1740726603620 } from './1740726603620-addedParsarKarInMilkartax';
import { CreateBookNumberMasterTable1740730579067 } from './1740730579067-CreateBookNumberMasterTable';
import { CreateDeletedPropertyUsageDetails1740808131280 } from './1740808131280-CreateDeletedPropertyUsageDetails';
import { UpdatedPropertyRelations1740991638012 } from './1740991638012-UpdatedPropertyRelations';
// import { CreateDeletedProperty1741001036399 } from './1741001036399-CreateDeletedProperty';
import { UpdatedPropertiesForPropertyDeletion1741004927899 } from './1741004927899-UpdatedPropertiesForPropertyDeletion';
import { CreatedBackupPropertyUsageDetailsEntity1741009933113 } from './1741009933113-CreatedBackupPropertyUsageDetailsEntity';
import { ChangeInBookEntity1740996358443 } from './1740996358443-changeInBookEntity';
import { ChangeINRaltionOfReciptAndBookEntity1741067508283 } from './1741067508283-changeINRaltionOfReciptAndBookEntity';
import { ChangeInDeltedTable1741085993642 } from './1741085993642-changeInDeltedTable';
import { AddedCascade1741775748627 } from './1741775748627-addedCascade';
import { CreatedBackupForPayment1741862122712 } from './1741862122712-CreatedBackupForPayment';
import { UpdatedTablesOnDeleteProperties1741863328122 } from './1741863328122-UpdatedTablesOnDeleteProperties';
import { UpdatedAmountColumnToNumeric1741864096397 } from './1741864096397-UpdatedAmountColumnToNumeric';
import { UpdatedColumnsInBackupPayment1741869516955 } from './1741869516955-UpdatedColumnsInBackupPayment';
import { AddedFinancialYeartoTaxDues1742816503546 } from './1742816503546-AddedFinancialYeartoTaxDues';
import { AddingDataOfFinancialYear1742817093980 } from './1742817093980-AddingDataOfFinancialYear';
import { AddingPropertyNumberInTaxPendingDues1742898393286 } from './1742898393286-AddingPropertyNumberInTaxPendingDues';
import { AddedPreviousPropertyOwners1742967312830 } from './1742967312830-AddedPreviousPropertyOwners';
import { ChangeinRealtionInProperty1742981664620 } from './1742981664620-changeinRealtionInProperty';
import { AddedColumnInProperty1743072273982 } from './1743072273982-addedColumnInProperty';
import { AddedDemandReportDataentity1743148301579 } from './1743148301579-addedDemandReportDataentity';
import { CreatedCollectorMaster1743076160910 } from './1743076160910-CreatedCollectorMaster';
import { AddedCollectorToBookMaster1743141871580 } from './1743141871580-AddedCollectorToBookMaster';
import { UpdatedCollectorMaster1743156261351 } from './1743156261351-UpdatedCollectorMaster';
import { UpdatedTableName1743448237396 } from './1743448237396-updatedTableName';
import { UpdatedColumnName1743488763682 } from './1743488763682-updatedColumnName';
import { AddedColumnNameInInfoTable1743492919605 } from './1743492919605-addedColumnNameInInfoTable';
import { AlterPaidDataEntity1743488717490 } from './1743488717490-AlterPaidDataEntity';
import { AddedColumns1743512147393 } from './1743512147393-addedColumns';
import { ChangeinColumnName1743583814225 } from './1743583814225-changeinColumnName';
import { AddedCascadetoTable1743584244266 } from './1743584244266-addedCascadetoTable';
import { AddedColumenInOwnerDetails1743668056606 } from './1743668056606-addedColumenInOwnerDetails';
import { AddedcolumnsinReportdata1743752473235 } from './1743752473235-addedcolumnsinReportdata';
import { AlterBookNumberEntity1744022409071 } from './1744022409071-AlterBookNumberEntity';
import { UpdatedCollectoBookRelation1743772479051 } from './1743772479051-UpdatedCollectoBookRelation';
import { UpdatedCollectoMaster1744199655024 } from './1744199655024-updatedCollectoMaster';
import { UpdateFerFarAndFod1744698557890 } from './1744698557890-updateFerFarAndFod';
import { AddedDiscout1745954112373 } from './1745954112373-addedDiscout';
import { ChangeinDiscountName1745997943772 } from './1745997943772-changeinDiscountName';
import { CreateReaasementAndupdatedColumn1745841966470 } from './1745841966470-createReaasementAndupdatedColumn';
import { AddedPenaltyTable1746017767471 } from './1746017767471-addedPenaltyTable';
import { ChangeInValueType1746172703175 } from './1746172703175-changeInValueType';
import { UpdateReassementInAllsetting1746432366864 } from './1746432366864-updateReassementInAllsetting';
import { RenameTaxPendingDuesColumns1743000000000 } from './1743000000000-rename-tax-pending-dues-columns';
import { AddedCronEnitity1749646677755 } from './1749646677755-addedCronEnitity'
import { CreatePaidAndNonPaidDataTable1750000000000 } from './1750000000000-create-paid-and-non-paid-data-table';
import { AddOfflineNotificationEntity1749646677756 } from './1749646677756-addOfflineNotificationEntity';
import { ChangeinRepo1751018710055 } from './1751018710055-changeinRepo';
import { AddedShastefee1751538677004 } from './1751538677004-addedShastefee';
import { AddRequestInfoToLogs1751538677005 } from './1751538677005-AddRequestInfoToLogs';
import { ChangeEnityName1752061377404 } from './1752061377404-changeEnityName';
import { AddedColumnInMilkatKar1752131674903 } from './1752131674903-addedColumnInMilkatKar';
import { AddedColumnFY1752231625781 } from './1752231625781-addedColumnFY';
import { AddedOrderIndex1753879700473 } from './1753879700473-addedOrderIndex';
import { AddedColumneForCreatedtime1756891554369 } from './1756891554369-addedColumneForCreatedtime';
import { AddedRegisterTabel1757422008876 } from './1757422008876-addedRegisterTabel';


export const migrations = [
    FirstMigration1723178247276,
    ImportTable1723195284623,
    ImportTable21723200309685,
    ImportTable31723205170422,
    UpdateTable51724152316783,
    ImportPropertyUpdate1724656205503,
    ImportPropertyUpdate11724666242472,
    ImportPropertyStatsTable1724741413292,
    ImportPropertyupdatecoladdded1724757083425,
    ImportPropertyupdatecoladdded11724825479325,
    ImportPropertyupdatecoladdded21724829253259,
    ImportPropertyupdatecoladdded31724932962526,
    ImportPropertyupdatecoladdded41724992102460,
    ImportPropertyupdatecoladdded51725256117646,
    PropertyColUpdated1725361944271,
    ImportPropertyColAdded61725960708731,
    LogsTableCreate1726214145581,
    LogsTableAlter11726230078342,
    MilkatKar1726683379932,
    KarakaraniUpdate1726810920684,
    KarakaraniUpdate31726814938123,
    // MasterRrRateCreate1727007212804,
    Warshik11727884874459,
    WarshikarNullable1727896106718,
    MasterTablesCreated1727175716342,
    ImportPropertiesUpdatedColAdded1727268386459,
    CreateTableCommonTableProperty1728025663372,
    PropertyOwnerAddColRemark1728139459381,
    PropertyOwnerAddColLastActionDone1728196195891,
    PropertyOwnerAddColIsOwner1728197252539,
    ColAddedProperty1728237403823,
    CreateColPropertyEntity1728364698064,
    WarshikKarColAddes1728386656262,
    ColAddedNewPropertyNumberInProperty1728471037127,
    TaxFyReocrdColUpdate1728475625695,
    OptlogsUpdate1729279634258,
    VerifionId1729417050331,
    CreattableClassMaster1729153065904,
    CreateTableWrongPropertyType1729160070398,
    AddedColumn1729682743836,
    AddedColumn1729762845567,
    CreateFloorTable1730193933912,
    CreateGhanKachraTable1730268509069,
    CretedFerfarAnddoeDetails1731406647194,
    ChangeUniquConstrint1731567696570,
    PropertyUpdateDataType1736750797134,
    TableUpdates1737093718720,
    TaxPendingDuesTable1738059807856,
    Billdata1739094982159,
    PaymentInfoTable1739531535523,
    CreatedTableRelatedToPayments1739961971062,
    ChangeInTable1740058349321,
    AddedColumneInrecipt1740120709754,
    ChangeInentity1740138395451,
    ChangeInentityInRecipt1740143766492,
    ChangesInPaylBiltable1740560059371,
    ChangesInTableColumn1740564574207,
    AddedParsarKarInMilkartax1740726603620,
    CreateBookNumberMasterTable1740730579067,
    CreateDeletedPropertyUsageDetails1740808131280,
    ChangeInBookEntity1740996358443,
    ChangeINRaltionOfReciptAndBookEntity1741067508283,
    UpdatedPropertyRelations1740991638012,
    // CreateDeletedProperty1741001036399,
    UpdatedPropertiesForPropertyDeletion1741004927899,
    CreatedBackupPropertyUsageDetailsEntity1741009933113,
    ChangeInDeltedTable1741085993642,
    AddedCascade1741775748627,
    CreatedBackupForPayment1741862122712,
    UpdatedTablesOnDeleteProperties1741863328122,
    UpdatedAmountColumnToNumeric1741864096397,
    UpdatedColumnsInBackupPayment1741869516955,
    AddedFinancialYeartoTaxDues1742816503546,
    AddingDataOfFinancialYear1742817093980,
    AddingPropertyNumberInTaxPendingDues1742898393286,
    AddedPreviousPropertyOwners1742967312830,
    ChangeinRealtionInProperty1742981664620,
    AddedColumnInProperty1743072273982,
    AddedDemandReportDataentity1743148301579,
    CreatedCollectorMaster1743076160910,
    AddedCollectorToBookMaster1743141871580,
    UpdatedCollectorMaster1743156261351,
    UpdatedTableName1743448237396,
    UpdatedColumnName1743488763682,
    AddedColumnNameInInfoTable1743492919605,
    AlterPaidDataEntity1743488717490,
    AddedColumns1743512147393,
    ChangeinColumnName1743583814225,
    AddedCascadetoTable1743584244266,
    AddedColumenInOwnerDetails1743668056606,
    AddedcolumnsinReportdata1743752473235,
    AlterBookNumberEntity1744022409071,
    UpdatedCollectoBookRelation1743772479051,
    UpdatedCollectoMaster1744199655024,
    UpdateFerFarAndFod1744698557890,
    CreateReaasementAndupdatedColumn1745841966470,
    AddedDiscout1745954112373,
    ChangeinDiscountName1745997943772,
    AddedPenaltyTable1746017767471,
    ChangeInValueType1746172703175,
    UpdateReassementInAllsetting1746432366864,
    RenameTaxPendingDuesColumns1743000000000,
    AddedCronEnitity1749646677755,
    CreatePaidAndNonPaidDataTable1750000000000,
    AddOfflineNotificationEntity1749646677756,
    ChangeinRepo1751018710055,
    AddedShastefee1751538677004,
    // AddRequestInfoToLogs1751538677005,
    ChangeEnityName1752061377404,
    AddedColumnInMilkatKar1752131674903,
    AddedColumnFY1752231625781,
    AddedOrderIndex1753879700473,
    AddedColumneForCreatedtime1756891554369,
    AddedRegisterTabel1757422008876
];
export * from './1723178247276-first_migration';
export * from './1723195284623-import_table';
export * from './1723200309685-import_table2';
export * from './1723205170422-import_table3';
export * from './1724152316783-update_table_5';
export * from './1724656205503-import_property_update';
export * from './1724666242472-import_property_update1'
export * from './1724741413292-import_property_stats-Table';
export * from './1724757083425-import_propertyupdatecoladdded';
export * from './1724825479325-import_propertyupdatecoladdded1'
export * from './1724829253259-import_propertyupdatecoladdded2';
export * from './1724932962526-import_propertyupdatecoladdded3';
export * from './1724992102460-import_propertyupdatecoladdded4';
export * from './1725256117646-import_propertyupdatecoladdded5';
export * from './1725361944271-property_col_updated';
export * from './1725960708731-import_property_col_added_6';
export * from './1726214145581-logs_table_create';
export * from './1726230078342-logs_table_alter1';
export * from './1726683379932-milkatKar';
export * from './1726810920684-karakarani_update';
export * from './1726814938123-karakarani_update3';
// export * from './1727007212804-master_rr_rate_create'
export * from './1727884874459-warshik1'
export * from './1727896106718-warshikar-nullable'
export * from './1727175716342-master_tables_created';
export * from './1727268386459-import_properties_updated_col_added';
export * from './1728025663372-create_table_common_table_property';
export * from './1728139459381-property_owner_add_col_remark';
export * from './1728196195891-property_owner_add_col_last_action_done';
export * from './1728197252539-property_owner_add_col_is_owner';
export * from './1728237403823-col_added_property';
export * from './1728364698064-create_col_property_entity';
export * from './1728386656262-warshik_kar_col_addes';
export * from './1728475625695-tax_fy_reocrd_col_update';
export * from './1728471037127-col_added_newPropertyNumberInProperty';
export * from './1729279634258-optlogs-update';
export * from './1729417050331-verifionId';
export * from   './1729153065904-creattable_classMaster';
export * from './1729160070398-createTable_wrongPropertyType';
export * from './1729682743836-addedColumn';
export * from  './1729762845567-addedColumn';
export * from './1730193933912-createFloorTable';
export * from'./1730268509069-createGhanKachraTable';
export * from './1731406647194-cretedFerfarAnddoeDetails';
export * from  './1731567696570-changeUniquConstrint';
export * from './1736750797134-propertyUpdateDataType';
export * from './1737093718720-table_updates';
export * from './1738059807856-tax_pending_dues_table';
export * from './1739094982159-billdata';
export * from './1739531535523-paymentInfoTable';
export * from './1739961971062-createdTableRelatedToPayments';
export * from  './1740058349321-changeInTable';
export * from './1740120709754-addedColumneInrecipt';
export *  from './1740138395451-changeInentity';
export * from './1740143766492-changeInentityRecipt';
export * from './1740560059371-changesInPaylBiltable';
export * from  './1740564574207-changesInTableColumn';
export * from './1740726603620-addedParsarKarInMilkartax';
export * from './1740730579067-CreateBookNumberMasterTable';
export * from './1740808131280-CreateDeletedPropertyUsageDetails';
export * from  './1740996358443-changeInBookEntity';
export * from  './1741067508283-changeINRaltionOfReciptAndBookEntity';


export * from './1740991638012-UpdatedPropertyRelations';
// export * from './1741001036399-CreateDeletedProperty';
export * from './1741004927899-UpdatedPropertiesForPropertyDeletion';
export * from './1741009933113-CreatedBackupPropertyUsageDetailsEntity'


export * from './1741085993642-changeInDeltedTable';
export * from './1741775748627-addedCascade';
export * from './1741862122712-CreatedBackupForPayment';
export * from './1741863328122-UpdatedTablesOnDeleteProperties';
export * from './1741864096397-UpdatedAmountColumnToNumeric';
export * from './1741869516955-UpdatedColumnsInBackupPayment';
export * from './1742816503546-AddedFinancialYeartoTaxDues';
export * from './1742817093980-AddingDataOfFinancialYear';
export * from './1742898393286-AddingPropertyNumberInTaxPendingDues';
export * from './1742967312830-AddedPreviousPropertyOwners';
export * from './1742981664620-changeinRealtionInProperty';
export * from './1743072273982-addedColumnInProperty';

export * from './1743148301579-addedDemandReportDataentity';

export * from './1743076160910-CreatedCollectorMaster';
export * from './1743141871580-AddedCollectorToBookMaster';
export * from './1743156261351-UpdatedCollectorMaster'
export * from './1743448237396-updatedTableName';
export * from  './1743488763682-updatedColumnName';

export * from './1743492919605-addedColumnNameInInfoTable';
export * from './1743488717490-AlterPaidDataEntity';
export * from './1743512147393-addedColumns';
export * from  './1743583814225-changeinColumnName';
export * from './1743584244266-addedCascadetoTable';

export * from './1743668056606-addedColumenInOwnerDetails';
export * from './1743752473235-addedcolumnsinReportdata';
export * from './1744022409071-AlterBookNumberEntity'
export * from './1743772479051-UpdatedCollectoBookRelation'
export *  from './1744199655024-updatedCollectoMaster';
export * from './1744698557890-updateFerFarAndFod';
export * from './1745841966470-createReaasementAndupdatedColumn';
export * from './1745954112373-addedDiscout';
export * from './1745997943772-changeinDiscountName';
export * from './1746017767471-addedPenaltyTable';
export * from  './1746172703175-changeInValueType';
export * from './1746432366864-updateReassementInAllsetting';
export *  from './1743000000000-rename-tax-pending-dues-columns';
export * from './1749646677755-addedCronEnitity';
export * from './1750000000000-create-paid-and-non-paid-data-table';
export * from './1749646677756-addOfflineNotificationEntity';
export * from './1751018710055-changeinRepo';
export * from './1751538677004-addedShastefee';
export * from  './1751538677005-AddRequestInfoToLogs';
export * from './1752061377404-changeEnityName';
export * from './1752131674903-addedColumnInMilkatKar';
export * from './1752231625781-addedColumnFY';
export * from './1753879700473-addedOrderIndex';
export * from './1756891554369-addedColumneForCreatedtime';
export * from './1757422008876-addedRegisterTabel';