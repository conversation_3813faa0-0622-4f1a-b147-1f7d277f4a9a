import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { FinancialMasterService } from './financial_master.service';

import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('financialMaster')
@Controller('financialMaster')
export class FinancialMasterController {
  constructor(
    private readonly financialMasterService: FinancialMasterService,
  ) {}

  @ApiOperation({ summary: 'Get all fy list' })
  @ApiResponse({ status: 200, description: 'Returns all fy list' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.financialMasterService.findAll();
  }

  @ApiOperation({ summary: 'Create new financial year and generate Warshik Kar data' })
  @ApiResponse({
    status: 200,
    description: 'Successfully created new financial year and generated Warshik Kar data',
  })
  @Post('/create-financial-year')
  async createFinancialYear() {
    return this.financialMasterService.createFinancialYear();
  }
}
