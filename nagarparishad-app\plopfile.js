module.exports = function (plop) {
  plop.setGenerator("Component", {
    description: "Create a new component",
    prompts: [
      {
        type: "input",
        name: "name",
        message: "What is your component name?",
        validate: function (value) {
          var valid = /^[A-Z][a-zA-Z0-9]+$/.test(value);
          return (
            valid ||
            "Component name must start with a capital letter and can only contain alphanumeric characters."
          );
        },
      },
      {
        type: "confirm",
        name: "isForm",
        message: "Is this a form component?",
        default: false,
      },
    ],
    actions: function (data) {
      const basePath = data.isForm
        ? "src/components/forms"
        : "src/components/custom";
      return [
        {
          type: "add",
          path: `${basePath}/{{pascalCase name}}/index.ts`,
          templateFile: "plop/index.ts.hbs",
        },
        {
          type: "add",
          path: `${basePath}/{{pascalCase name}}/{{pascalCase name}}.tsx`,
          templateFile: "plop/Component.tsx.hbs",
        },
      ];
    },
  });

  plop.setGenerator("Page", {
    description: "Create a new page",
    prompts: [
      {
        type: "input",
        name: "name",
        message: "What is your page name?",
        validate: function (value) {
          var valid = /^[A-Z][a-zA-Z0-9]+$/.test(value);
          return (
            valid ||
            "Page name must start with a capital letter and can only contain alphanumeric characters."
          );
        },
      },
    ],
    actions: [
      {
        type: "add",
        path: "src/pages/{{pascalCase name}}/index.ts",
        templateFile: "plop/index.ts.hbs",
      },
      {
        type: "add",
        path: "src/pages/{{pascalCase name}}/{{pascalCase name}}.tsx",
        templateFile: "plop/Component.tsx.hbs",
      },
    ],
  });
};
