import axios from "axios";
import {
  GetSing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PropertyRegistrationInterface,
  PropertyList<PERSON><PERSON><PERSON><PERSON>,
  GetSingleRecord,
} from "../model/propertyregistration-master";
import {
  ADD_PROPERTY_REGISTRATION,
  UPDATE_PROPERTY_REGISTRATION,
  PROPERTY_LIST,
  GET_SINGLE_PROPERTY,
  GET_PROPERTY_DETAILS,
  GET_PROPERTY_DETAILS_BYNAME,
  DELETE_SINGLE_PROPERTY,
  GET_SINGLE_PROPERTY_BY_NUMBER,
  GET_REASSESSMENT_RANGE,
  PROCESS_MILKAT_KAR,
  PROCESS_WARSHIK_KAR,
  GET_FINANCIAL_YEARS,
  GET_WARD_WISE_MILKAT_KAR_STATUS,
  GET_WARD_WISE_WARSHIK_KAR_STATUS
} from "@/constant/utils/apiUtils";

const baseURL = import.meta.env.VITE_APP_BASE_URL;

const VITE_BASE_URL = baseURL;

// Property Registration
const REACT_APP_ALL_ADD_PROPERTY_REGISTRATION =
  VITE_BASE_URL + ADD_PROPERTY_REGISTRATION;
const REACT_APP_ALL_GET_PROPERTY_DETAILS = VITE_BASE_URL + GET_PROPERTY_DETAILS;
const REACT_APP_ALL_PROPERTY_LIST = VITE_BASE_URL + PROPERTY_LIST;
const REACT_APP_ALL_GET_SINGLE_PROPERTY = VITE_BASE_URL + GET_SINGLE_PROPERTY;
const REACT_APP_ALL_DELETE_SINGLE_PROPERTY =
  VITE_BASE_URL + DELETE_SINGLE_PROPERTY;
const REACT_APP_ALL_UPDATE_PROPERTY_REGISTRATION =
  VITE_BASE_URL + UPDATE_PROPERTY_REGISTRATION;
const REACT_APP_ALL_GET_PROPERTY_BYNAME =
  VITE_BASE_URL + GET_PROPERTY_DETAILS_BYNAME;
const REACT_APP_ALL_GET_SINGLE_PROPERTY_BY_NUMBER = VITE_BASE_URL + GET_SINGLE_PROPERTY_BY_NUMBER;

// Tax Generation APIs
const REACT_APP_GET_REASSESSMENT_RANGE = VITE_BASE_URL + GET_REASSESSMENT_RANGE;
const REACT_APP_GET_FINANCIAL_YEARS = VITE_BASE_URL + GET_FINANCIAL_YEARS;
const REACT_APP_PROCESS_MILKAT_KAR = VITE_BASE_URL + PROCESS_MILKAT_KAR;
const REACT_APP_PROCESS_WARSHIK_KAR = VITE_BASE_URL + PROCESS_WARSHIK_KAR;
const REACT_APP_GET_WARD_WISE_MILKAT_KAR_STATUS = VITE_BASE_URL + GET_WARD_WISE_MILKAT_KAR_STATUS;
const REACT_APP_GET_WARD_WISE_WARSHIK_KAR_STATUS = VITE_BASE_URL + GET_WARD_WISE_WARSHIK_KAR_STATUS;


class PropertyApi {
  static getStoredToken = () => {
    const UserToken = JSON.parse(localStorage.getItem("AccessToken") || "{}");
    return UserToken !== undefined ? UserToken : false;
  };

  // Add Property Registration
  static AddPropertyRegistration = async (
    propertyData: any,
    callback: (response: {
      status: boolean;
      data: PropertyRegistrationInterface;
    }) => void,
  ) => {
    try {
      const response = await axios.post(
        REACT_APP_ALL_ADD_PROPERTY_REGISTRATION,
        propertyData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization:`Bearer ${PropertyApi.getStoredToken()}`

          },
        },
      );
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.log(err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  static getPropertyDetailsBynumber = async (
    search: any,
    callback: (response: { status: boolean; data: any }) => void,
  ) => {
    try {
      const response = await axios.get(
        `${REACT_APP_ALL_GET_PROPERTY_DETAILS}${search}`,
        {
          headers: {
            Accept: "application/json",
            Authorization:`Bearer ${PropertyApi.getStoredToken()}`

          },
        },
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error(err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };
  static getAllProperty(
    limit: number,
    page: number,
    search?: string,
    searchOn?: string
  ): Promise<any> {
    console.log("limit, page, search, searchOn:", limit, page, search, searchOn);

    return axios
      .get(`${REACT_APP_ALL_PROPERTY_LIST}`, {
        params: {
          page,
          limit,
          ...(search && searchOn ? { value: search, searchOn } : {}), // Add search params if provided
        },
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`,
        },
      })
      .then((response) => response.data.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error("Error fetching properties:", error);
        }
        throw error; // Re-throw error for React Query to handle
      });
  }


  // static getAllProperty = async (
  //     callback: (response: { status: boolean, data: PropertyListAllApi }) => void
  // ) => {
  //     try {
  //         const response = await axios.get(REACT_APP_ALL_PROPERTY_LIST, {
  //             headers: {
  //                 'Content-Type': 'application/json',
  //                 Accept: 'application/json',
  //             },
  //         });
  //         if (response.status === 200) {
  //             callback({ status: true, data: response.data });
  //         } else {
  //             callback({ status: false, data: response.data });
  //         }
  //     } catch (err: any) {
  //         callback({ status: false, data: err.response ? err.response.data : err.message });
  //     }
  // };

  // static getSingleProperty(): Promise<any> {
  //     const source = axios.CancelToken.source();

  //     return axios.get(`${REACT_APP_ALL_PROPERTY_LIST}`, {
  //       cancelToken: source.token,
  //     })
  //     .then(response => response.data)
  //     .catch(error => {
  //       if (axios.isCancel(error)) {
  //         console.log('Request canceled', error.message);
  //       } else {
  //         console.error(error);
  //       }
  //     });
  //   }

  static getSingleProperty = async (
    propertyId: string,
    callback: (response: { status: boolean; data: GetSingleRecordApi }) => void,
    search?: 'property_id' | 'propertyNumber' | 'old_propertyNumber',
  ) => {
    try {
      // Build the query string using either the provided 'search' or default to 'property_id'
      const searchOn = search || 'property_id';

      const response = await axios.get(
        `${REACT_APP_ALL_GET_SINGLE_PROPERTY}?searchOn=${searchOn}&value=${propertyId}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${PropertyApi.getStoredToken()}`,
          },
        }
      );

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };



  static UpdatePropertyRegistration = async (
    propertyId: string,
    propertyData: GetSingleRecord,
    callback: (response: {
      status: boolean;
      data: PropertyRegistrationInterface;
    }) => void,
  ) => {
    try {
      const response = await axios.patch(
        `${REACT_APP_ALL_UPDATE_PROPERTY_REGISTRATION}${propertyId}`,
        propertyData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization:`Bearer ${PropertyApi.getStoredToken()}`

          },
        },
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  static getPropertyDetailsByName = async (
    limit: any,
    ward: any,
    zone: any,
    fullName: any,
    page: any,
    callback: (response: { status: boolean; data: any }) => void,
  ) => {
    const params = new URLSearchParams({
      limit,
      ward,
      zone,
      fullName,
      page,
    });

    try {
      const response = await axios.get(
        `${REACT_APP_ALL_GET_PROPERTY_BYNAME}${params.toString()}`,
        {
          headers: {
            Accept: "application/json",
            Authorization:`Bearer ${PropertyApi.getStoredToken()}`

          },
        },
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error(err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  static deleteSingleProperty = async (
    propertyId: string,
    callback: (response: { status: boolean; data: any }) => void,
  ) => {
    try {
      const response = await axios.delete(
        `${REACT_APP_ALL_DELETE_SINGLE_PROPERTY}${propertyId}`,
        {
          headers: {
            Accept: "application/json",
            Authorization:`Bearer ${PropertyApi.getStoredToken()}`

          },
        },
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error(err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };
  static async getPropertyDetailsByNumber(selectedPropertyNumber: string, searchOnParameter: string) {
    const url = `${REACT_APP_ALL_GET_SINGLE_PROPERTY_BY_NUMBER}`;

    try {
      const response = await axios.get(url, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization:`Bearer ${PropertyApi.getStoredToken()}`
        },
        params: {
          value: selectedPropertyNumber,
          searchOn: searchOnParameter,
        },
      });

      return { status: true, data: response.data };
    } catch (err: any) {
      return { status: false, data: err };
    }
  }

  static fetchPaymentModes = async (
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.get(`${VITE_BASE_URL}/v1/payment_log_master/payment-modes`, {
        headers: {
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`,
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  static fetchUsageTypePropertyStats = async (
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.get(`${VITE_BASE_URL}/v1/dashboard/usage-type-property-stats`, {
        headers: {
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`,
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  static fetchTotalPropertyStats = async (
    callback: (response: { status: boolean; data: any }) => void,
        year?: string,

  ) => {
    try {
      const response = await axios.get(`${VITE_BASE_URL}/v1/dashboard/total-property-stats?year=${year}`, {
        headers: {
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`,
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  static fetchWardPropertyStats = async (
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.get(`${VITE_BASE_URL}/v1/dashboard/ward-property-stats`, {
        headers: {
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`,
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  static divideProperties = async (propertyData: any) => {


    try {
      const response = await axios.post(`${VITE_BASE_URL}/v1/property-divide/create-divide-properties`,
        propertyData,
        {
        headers: {
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`,
        },
      });
      if (response.status === 200) {
        return { status: true, data: response.data.data };
      } else {
        return { status: false, data: response.data.data };
      }
    } catch (err: any) {
      return{
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static deleteOwner = async (propertyOwnerId: string, remark: string) => {
    try {
      const response = await axios.patch(
        `${VITE_BASE_URL}/v1/property-owner/delete-owner/${propertyOwnerId}`,
        { remark },
        {
          headers: {
            Accept: "application/json",
            Authorization: `Bearer ${PropertyApi.getStoredToken()}`,
          },
        }
      );

      if (response.status === 200) {
        return { status: true, data: response.data };
      } else {
        return { status: false, data: response.data };
      }
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  // Get reassessment range years
  static getReassessmentRanges = async (callback: (response: { status: boolean; data: any }) => void) => {
    try {
      const response = await axios.get(REACT_APP_GET_REASSESSMENT_RANGE, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error("Error fetching reassessment ranges:", err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message
      });
    }
  };

  // Process Milkat Kar generation
  static processMilkatKar = async (reassessmentYearId: string, callback: (response: { status: boolean; data: any }) => void) => {
    try {
      const response = await axios.get(REACT_APP_PROCESS_MILKAT_KAR, {
        params: {
          reassessmentYearId
        },
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error("Error processing Milkat Kar:", err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message
      });
    }
  };

  // Process Warshik Kar generation
  static processWarshikKar = async (financialYearId: string, callback: (response: { status: boolean; data: any }) => void) => {
    try {
      const response = await axios.get(REACT_APP_PROCESS_WARSHIK_KAR, {
        params: {
          financialYearId
        },
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error("Error processing Warshik Kar:", err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message
      });
    }
  };

  // Get financial years
  static getFinancialYears = async (callback: (response: { status: boolean; data: any }) => void) => {
    try {
      const response = await axios.get(REACT_APP_GET_FINANCIAL_YEARS, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error("Error fetching financial years:", err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message
      });
    }
  };

  // Create new property
  static createProperty = async (propertyData: any) => {
    try {
      const response = await axios.post(
        `${VITE_BASE_URL}/v1/property-master/`,
        propertyData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${PropertyApi.getStoredToken()}`
          },
        }
      );

      return { status: true, data: response.data, statusCode: response.status };
    } catch (err: any) {
      console.error("Error creating property:", err);
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
        statusCode: err.response?.status
      };
    }
  };

  // Update existing property
  static updateProperty = async (propertyId: string, propertyData: any) => {
    try {
      const response = await axios.put(
        `${VITE_BASE_URL}/v1/property-master/${propertyId}`,
        propertyData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${PropertyApi.getStoredToken()}`
          },
        }
      );

      return { status: true, data: response.data, statusCode: response.status };
    } catch (err: any) {
      console.error("Error updating property:", err);
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
        statusCode: err.response?.status
      };
    }
  };

  // Get ward-wise Milkat Kar generation status
  static getWardWiseMilkatKarStatus = async (reassessmentYearId: string, callback: (response: { status: boolean; data: any }) => void) => {
    try {
      const response = await axios.get(REACT_APP_GET_WARD_WISE_MILKAT_KAR_STATUS, {
        params: {
          reassessmentYearId
        },
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error("Error fetching ward-wise Milkat Kar status:", err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message
      });
    }
  };

  // Process Milkat Kar generation for specific ward
  static processMilkatKarByWard = async (wardNumber: string, reassessmentYearId: string, callback: (response: { status: boolean; data: any }) => void) => {
    try {
      const response = await axios.get(REACT_APP_PROCESS_MILKAT_KAR, {
        params: {
          ward_number: wardNumber,
          reassessmentYearId
        },
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error("Error processing Milkat Kar for ward:", err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message
      });
    }
  };

  // Get ward-wise Warshik Kar generation status
  static getWardWiseWarshikKarStatus = async (financialYearId: string, callback: (response: { status: boolean; data: any }) => void) => {
    try {
      const response = await axios.get(REACT_APP_GET_WARD_WISE_WARSHIK_KAR_STATUS, {
        params: {
          financialYearId
        },
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error("Error fetching ward-wise Warshik Kar status:", err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message
      });
    }
  };

  // Process Warshik Kar generation for specific ward
  static processWarshikKarByWard = async (wardNumber: string, financialYearId: string, callback: (response: { status: boolean; data: any }) => void) => {
    try {
      const response = await axios.get(REACT_APP_PROCESS_WARSHIK_KAR, {
        params: {
          ward_number: wardNumber,
          financialYearId
        },
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${PropertyApi.getStoredToken()}`
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      console.error("Error processing Warshik Kar for ward:", err);
      callback({
        status: false,
        data: err.response ? err.response.data : err.message
      });
    }
  };
}

export default PropertyApi;
