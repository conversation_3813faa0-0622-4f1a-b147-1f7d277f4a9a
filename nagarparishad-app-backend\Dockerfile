# Stage 1: Build the application
FROM node:20-alpine AS build

# Set working directory
WORKDIR /app

# Copy package.json and yarn.lock to the container
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile
# RUN yarn install 


# Copy the rest of the application code
COPY . .

# Build the application
RUN yarn build

# Copy templates to dist directory
RUN mkdir -p dist/templates && cp -r src/templates dist/templates
RUN mkdir -p dist/images && cp -r images/* dist/images/

# Stage 2: Create a lightweight production image
FROM node:20-alpine

# Install PostgreSQL client tools
RUN apk add --no-cache postgresql-client

# Set working directory
WORKDIR /app

# Copy only necessary files from the previous stage
COPY --from=build /app/dist ./dist
COPY --from=build /app/node_modules ./node_modules
COPY --from=build /app/package.json ./package.json
COPY --from=build /app/.env ./.env

# Expose the port the app runs on
EXPOSE 3000


# Command to run the application
CMD ["node", "dist/main"]
