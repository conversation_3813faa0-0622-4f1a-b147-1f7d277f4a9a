import { IsString, <PERSON>N<PERSON><PERSON>, IsOptional, IsEnum, Min } from 'class-validator';

export class CommonFiledsOfPropertyDto {
  @IsString()
  @IsOptional()
  GISID: string;

  @IsString()
  @IsOptional()
  propertyDescription: string;

  @IsString()
  @IsOptional()
  completionCertificate: string;

  @IsString()
  @IsOptional()
  accessRoad: string;

  @IsEnum(['yes', 'no'])
  @IsOptional()
  individualToilet: 'yes' | 'no';

  @IsString()
  @IsOptional()
  toiletType: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  totalNumber: number | null;

  @IsString()
  @IsOptional()
  lightingFacility: string;

  @IsString()
  @IsOptional()
  tapConnection: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  totalConnections: number | null;

  @IsEnum(['yes', 'no'])
  @IsOptional()
  solarProject: 'yes' | 'no';

  @IsEnum(['yes', 'no'])
  @IsOptional()
  rainWaterHarvesting: 'yes' | 'no';

  @IsEnum(['yes', 'no'])
  @IsOptional()
  sewageSystem: 'yes' | 'no';

  @IsNumber()
  @Min(0)
  @IsOptional()
  groundFloorArea: number | null;

  @IsNumber()
  @Min(0)
  @IsOptional()
  remainingGroundFloorArea: number | null;
}
