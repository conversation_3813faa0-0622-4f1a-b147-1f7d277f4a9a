import WhiteContainer from "@/components/custom/WhiteContainer";
import React, { useContext, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ArrowUpDown } from "lucide-react";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { toast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";

import { ColumnDef } from "@tanstack/react-table";
import { MasterObjectColumnDef } from "@/model/zone-master";
import { useUsageSubController } from "@/controller/master/UsageSubController";
import { MASTER } from "@/constant/config/api.config";
import UsageSubMasterForm from "../UsageSubMasterForm/UsageSubMasterForm";
import AddNewBtn from "../../globalcomponent/AddNewForm";
import { ResponseData } from "@/model/auth/authServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

const UsageSubMaster = () => {
  const { t } = useTranslation();
  const userRef = useRef(null);
  const dynamicValues = {
    name: t("usage_sub.UsageLabel"),
  };
  const { usageSubList, deleteUsageSub, usageSubLoading } = useUsageSubController();
  const { setMasterComponent, setOpen } = useContext(GlobalContext);

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Master, FormName.UsageSub, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Master, FormName.UsageSub, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Master, FormName.UsageSub, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Master, FormName.UsageSub, Action.CanDelete);

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any | null>(null);
  console.log("usageSubList",usageSubList);

  function handleEdit(item: any): void {
    setOpen(true);
    setMasterComponent(
      <UsageSubMasterForm
        btnTitle={"usage_sub.updateBtn"}
        editData={item && item}
      />,
    );
    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }

  function handleDelete(item: any): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  const handleConfirmDelete = () => {
    if (selectedItem) {
      deleteUsageSub(selectedItem.usageSub_id, {
        onSuccess: (response: ResponseData) => {
          if (response.statusCode && response.statusCode === 200) {
            toast({
              title: t("api.formdelete", dynamicValues),
              variant: "success",
            });
          } else {
            toast({
              title: response.message,
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const columns: ColumnDef<MasterObjectColumnDef>[] = [
    {
      accessorKey: `${t("SrNo")}`,
      header: `${t("SrNo")}`,
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: "usage?.usage",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("usage.UsageLabel")}
        </Button>
      ),
     cell: ({ row }: { row: any })  => <div className="">{row.original?.usageType?.usage_type}</div>,
    },
    {
      accessorKey: "subUsage",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("usage_sub.UsageLabel")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
     cell: ({ row }: { row: any })  => <div className="">{row.original?.usage_sub_type}</div>,
    },
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: `${t("Actions")}`,
            enableHiding: true,
            cell: ({ row }: { row: any }) => (
              <div className="flex space-x-2">
                {canUpdate && (
                  <Button
                    variant="submit"
                    className="h-8 w-8 p-0 justify-center"
                    onClick={() => handleEdit(row?.original)}
                  >
                    {t("table.edit")}
                  </Button>
                )}
                {/* {CanDelete && (
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0 justify-center ml-2  !border-BlueText !text-BlueText"
                    onClick={() => handleDelete(row?.original)}
                  >
                    {t("table.delete")}
                  </Button>
                )} */}
              </div>
            ),
          },
        ]
      : []),
  ];

  const MasterType: string = MASTER.USAGE_SUB;

  return (
    <div className=" w-full h-fit sm:p-6 p-3" ref={userRef && userRef}>
      <p className="w-full flex items-center justify-between ml-2  text-2xl font-semibold mb-2">
        {t("usage_sub.formTitle")}
      </p>
      {CanCreate && <WhiteContainer>
        {MasterType && <AddNewBtn masterType={MASTER.USAGE_SUB} />}
      </WhiteContainer>}
      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={usageSubList}
          masterType={MASTER.USAGE_SUB}
          searchColumn="subUsage"
          searchKey="searchUsage"
          loader={usageSubLoading ? true : false}

        />
      </WhiteContainer>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={selectedItem.usageSub_id} // Assuming usageSub_id is a string property of selectedItem
          onDelete={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default UsageSubMaster;
