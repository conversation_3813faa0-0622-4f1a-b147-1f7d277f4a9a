import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  PropertytypeSendApiObj,
  PropertytypeMasterObject,
  PropertytypeCreateApi,
} from "../../../model/propertytype-master";
import Api from "@/services/ApiServices";
import { useToast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { Loader2 } from "lucide-react";
import { usePropertytypeMasterController } from "@/controller/master/PropertyMasterController";

interface PropertytypeMasterInterface {
  btnTitle: string;
  editData?: PropertytypeMasterObject;
}

const PropertytypeMasterForm = ({
  btnTitle,
  editData,
}: PropertytypeMasterInterface) => {
  const { t } = useTranslation();

  const schema = z.object({
    propertyType: z.string().trim().min(1, t("errorsRequiredField")),
  });

  const dynamicValues = {
    name: t("propertytype.propertytypeLabel"),
  };

  const { updatePropertytype, createPropertytype } =
    usePropertytypeMasterController();

  const { toast } = useToast();
  const { setOpen, setRefreshPropertyList, refreshPropertyList } =
    useContext(GlobalContext);
  const [loader, setLoader] = useState(false);
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      propertyType: editData?.propertyType || "",
    },
  });
  const {
    formState: { errors },
    reset,
  } = form;

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();

    const DataResponse: PropertytypeSendApiObj = {
      propertyType: data?.propertyType,
    };
    if (
      editData?.propertyType_id !== undefined &&
      editData?.propertyType_id !== null
    ) {
      setLoader(true);
      updatePropertytype(
        {
          propertytypeId: editData.propertyType_id,
          propertytypeData: DataResponse,
        },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            setRefreshPropertyList(!refreshPropertyList);

            form.reset({
              propertyType: "",
            });
            setLoader(false);
          },
          onError: (error: any) => {
            toast({
              title: error.message,
              variant: "destructive",
            });
            setLoader(false);
          },
        },
      );
    } else {
      setLoader(true);
      createPropertytype(DataResponse, {
        onSuccess: () => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          form.reset({
            propertyType: "",
          });
          setLoader(false);
        },
        onError: (error: any) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });
    }
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        propertyType: editData.propertyType || "",
      });
    } else {
      form.reset({
        propertyType: "",
      });
    }
  }, [editData]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full md:w-full"
        >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="grid-cols-subgrid">
          <FormField
                control={form.control}
                name="propertyType"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("propertytype.propertytypeLabel")}</FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1"
                        placeholder={t("propertytype.propertytypeLabel")}
                        {...field}
                      />
                    </FormControl>
                    {errors.propertyType && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
              <div className="grid-cols-subgrid  mb-1 ml-4 max-md:flex max-md:justify-end  pt-[32px] ">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
          </div>
       
        </form>
      </Form>
    </>
  );
};

export default PropertytypeMasterForm;
