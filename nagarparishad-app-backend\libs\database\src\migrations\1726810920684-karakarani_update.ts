import { MigrationInterface, QueryRunner } from "typeorm";

export class Karakara<PERSON>Update1726810920684 implements MigrationInterface {
    name = 'KarakaraniUpdate1726810920684'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "milkatKarTax" ALTER COLUMN "financial_year" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "milkatKarTax" ALTER COLUMN "financial_year" SET NOT NULL`);
    }

}
