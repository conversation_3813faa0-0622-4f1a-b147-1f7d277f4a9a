import { Repository } from 'typeorm';
import { Ward_Master } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from '@nestjs/common';

export class WardMasterRepository extends Repository<Ward_Master> {
  constructor(
    @InjectRepository(Ward_Master)
    private readonly wardMasterRepository: Repository<Ward_Master>,
  ) {
    super(
      wardMasterRepository.target,
      wardMasterRepository.manager,
      wardMasterRepository.queryRunner,
    );
  }

  async saveWard(input: { ward_name: string }): Promise<Ward_Master> {
    let ward = await this.wardMasterRepository.create(input);
    ward = await this.wardMasterRepository.save(ward);
    return ward;
  }

  async findAllWard() {
    return await this.wardMasterRepository
      .createQueryBuilder('ward-master')
      .orderBy('ward-master.updated_at', 'DESC')
      .getMany();
  }

  async findWardById(id: string) {
    return await this.wardMasterRepository
      .createQueryBuilder('ward-master')
      .where('ward-master.ward_id = :ward_id', { ward_id: id })
      .getOne();
  }

  async updateWard(id: string, input: { ward_name?: string }) {
    return await this.wardMasterRepository
      .createQueryBuilder('ward-master')
      .update(Ward_Master)
      .set(input)
      .where('ward_id = :ward_id', { ward_id: id })
      .execute();
  }

  async deleteWard(id: string) {
    return await this.wardMasterRepository
      .createQueryBuilder('ward-master')
      .softDelete()
      .where('ward_id = :ward_id', { ward_id: id })
      .execute();
  }
}
