import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApiTags } from '@nestjs/swagger';
import { GetSingleUserDto, GetUsersDto } from './dto/get-users.dto';

@ApiTags('User')
@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  @Get()
  findAll(@Query() getUsers: GetUsersDto) {
    return this.userService.findAll(getUsers);
  }

  @Get()
  findOne(@Query() user: GetSingleUserDto) {
    return this.userService.findOne(user);
  }

  @Patch()
  update(
    @Query() user: GetSingleUserDto,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    return this.userService.update(user, updateUserDto);
  }

  @Delete()
  remove(@Query() user: GetSingleUserDto) {
    return this.userService.remove(user);
  }
}
