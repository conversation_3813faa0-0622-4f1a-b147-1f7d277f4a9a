import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateGhanKachraTable1730268509069 implements MigrationInterface {
    name = 'CreateGhanKachraTable1730268509069'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "master_ghanKachra_rate" ("ghanKachra_rate_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year" character varying NOT NULL, "value" double precision NOT NULL DEFAULT '0', "status" character varying DEFAULT 'Active', "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "usage_sub_type_master_id" uuid, CONSTRAINT "PK_7ddd82197ac4d5bfc7941367cc1" PRIMARY KEY ("ghanKachra_rate_id"))`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" ADD CONSTRAINT "FK_89469a4ad05a7ed3f78d2df185a" FOREIGN KEY ("usage_sub_type_master_id") REFERENCES "usage_sub_type_master"("usage_sub_type_master_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" DROP CONSTRAINT "FK_89469a4ad05a7ed3f78d2df185a"`);
        await queryRunner.query(`DROP TABLE "master_ghanKachra_rate"`);
    }

}
