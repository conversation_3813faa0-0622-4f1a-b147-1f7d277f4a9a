import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PublicUserDto, ValidateOtp } from './dto/user-property.dto';
import {
  Financial_yearRepository,
  Property_Owner_DetailsRepository,
  PropertyMasterRepository,
  WarshikKarRepository,
} from 'libs/database/repositories';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { EVENTS } from '@helper/helpers/events.helper';
import { OtpHelperService } from '@helper/helpers';
import { PropertyAnnualTax } from './dto/property-annual-tax.dto';
import { TAX_TYPES } from '@helper/helpers/tax-types.helper';
import { randomBytes, randomInt } from 'crypto';
import { PaymentLogMasterService } from 'src/payment_log_master/payment_log_master.service';


@Injectable()
export class UserPropertyService {
  constructor(
    private readonly propertyMasterRepository: PropertyMasterRepository,
    private readonly propertyOwnerRepository: Property_Owner_DetailsRepository,
    private event: EventEmitter2,
    private readonly opthelper: OtpHelperService,
    private readonly warshikar: WarshikKarRepository,
        private readonly PaymentMasterService: PaymentLogMasterService,
            private readonly financialYearRepository: Financial_yearRepository,
        
    
  ) {}
  async checkPropertyDetails(publicUserDto: PublicUserDto) {
    try {
      const data =
        await this.propertyMasterRepository.getPublicUserData(publicUserDto);

      if (!data) {
        throw new NotFoundException();
      }

      return {
        message: 'Data Fetched SuccessFully',
        data: data,
      };
    } catch (error) {
      throw error;
    }
  } 
  async checkPropertyDetailsReturnOtp(publicUserDto: PublicUserDto) {
    try {
      const data =
        await this.propertyMasterRepository.getPublicUserData(publicUserDto);

      if (!data) {
        throw new NotFoundException();
      }

      return {
        message: 'Data Fetched SuccessFully',
        data: data,
      };
    } catch (error) {
      throw error;
    }
  }

  async searchPropertyValidate(publicUserDto: PublicUserDto) {
    try {
      const { mobile_number, propertyNumber } = publicUserDto;
      //check if propertyNumber or mobile number exists
console.log("publicUserDto",publicUserDto)
      const contactNumber = mobile_number
        ? await this.validateMobileNumber(mobile_number)
        : await this.getMobileFromPropertyNumber(propertyNumber);

      const key_value = mobile_number ? { mobile_number } : { propertyNumber };

      const verificationId = this.generateVerificationId();

      const payload = { mobile_number: contactNumber, key_value, verificationId  };

    //   this.event.emit(EVENTS.CREATE_OTP, payload);
      const otp = await this.handleSendOtp(payload) 

      return {
        message: `Otp Sent for the Registered Mobile Number: ${contactNumber}. OTP is ${otp}`,
        data: {
          verificationId: verificationId
        }
      };
    } catch (error) {
      throw error;
    }
  }

  private async validateMobileNumber(mobile_number: string) {
    try {
      Logger.log('check mobile number')
      const checkMobileNumber =
        await this.propertyOwnerRepository.checkMobileNumber(mobile_number);

      if (!checkMobileNumber) {
        throw new NotFoundException('mobile number not found');
      }

      return checkMobileNumber.mobile_number;
    } catch (error) {
      throw error;
    }
  }

  private async getMobileFromPropertyNumber(propertyNumber: string) {
    try {
      Logger.log('check property number ')
      const checkData: any =
        await this.propertyMasterRepository.checkPropertyNumber(propertyNumber);

      if (!checkData || checkData.length === 0) {
        throw new NotFoundException('PropertyNumber Not Found');
      }

      let property_id: string = checkData[0]?.property_id;
    

      Logger.log('check mobile number for property ', property_id)
      const checkMobileNumber =
        await this.propertyOwnerRepository.checkMobileNumber('', property_id);

      Logger.log('chec', JSON.stringify(checkMobileNumber))
      if (!checkMobileNumber || checkMobileNumber.mobile_number == null) {
        throw new NotFoundException(
          'mobile number not found for the proeprty number',
        );
      }

      return checkMobileNumber.mobile_number;
    } catch (error) {
      throw error;
    }
  }

//   @OnEvent(EVENTS.CREATE_OTP)
  async handleSendOtp(data: any) {
    try {
    
      
     return await this.opthelper.generateOtpV2(data);

    } catch (error) {
      throw error.message;
    }
  }


  async validateOtp(validateOtp: ValidateOtp){
    try {
      // 
      Logger.log('validate otp')
      const validOtp = await this.opthelper.validateotpV2(validateOtp)

      const getUserPropertyDetails = await this.propertyMasterRepository.getPublicUserData(validOtp.additionalInfo);

      return {
        message: "OTP verified",
        data: getUserPropertyDetails
      }
      
      
    } catch (error) {
      throw error;
    }
  }



  async getPropertyAnnualTax(propertyAnnualTax: PropertyAnnualTax){
    try {
      const {warshik_karId, propertyNumber}= propertyAnnualTax;

      // //check propertyNumber exist in propertyMaster
      // const validPropertyNumber = await this.propertyMasterRepository.checkPropertyNumber(propertyNumber)

      // // Logger.log("validProperty", validPropertyNumber[0]?.property_id)
      // if(!validPropertyNumber || validPropertyNumber.length === 0)
      // {
      //   throw new NotFoundException('Not Valid Property')
      // }

      // const getTaxData = await this.warshikar.findByWarshikarId(warshik_karId,validPropertyNumber[0]?.property_id )
       const currentFinancialYear =
        await this.financialYearRepository.getCurrentFinancialYear();

                const getTaxData = await this.PaymentMasterService.findLatestBillData(propertyNumber,'propertyNumber',currentFinancialYear?.financial_year_range);

   console.log("Get taxdata",getTaxData)

      if(!getTaxData || getTaxData.data === 0)
      {
        throw new NotFoundException("Tax Data Not Found")
      }
   
      const taxObj = {
        taxDetails: getTaxData.data,
        taxTypes: TAX_TYPES,
      }

      return {
        message: "Property Tax Retrieved SuccessFully",
        data: taxObj
      }
    } catch (error) {
      throw error;
    }
  }


  
  generateVerificationId(): string {
    let randomNumber = '';
    while (randomNumber.length < 12) {
      const randomValue = parseInt(randomBytes(4).toString('hex'), 16); 
      randomNumber += randomValue.toString().slice(0, 12 - randomNumber.length); 
    }
    return randomNumber;
  }
}
