import axios from "axios";
import {
  RoleModuleInterface,
  RoleDetailModuleInterface,
} from "@/model/role/roleInterface";
import {
  ADD_ROLE,
  GET_ALLROLES,
  GET_ROLE,
  UPDATE_ROLE,
  DELETE_ROLE,
  EDIT_ROLE,
} from "@/constant/utils/roleUtils";

const baseURL = import.meta.env.VITE_APP_BASE_URL;

const VITE_BASE_URL = baseURL;

// Define the API endpoints
const REACT_APP_ADD_ROLE = VITE_BASE_URL + ADD_ROLE;
const REACT_APP_GET_ALLROLES = VITE_BASE_URL + GET_ALLROLES;
const REACT_APP_GET_ROLE = (roleId: number) =>
  `${VITE_BASE_URL}${GET_ROLE}${roleId}`;
const REACT_APP_UPDATE_ROLE = (roleId: number) =>
  `${VITE_BASE_URL}${UPDATE_ROLE}${roleId}`;
const REACT_APP_DELETE_ROLE = (roleId: number) =>
  `${VITE_BASE_URL}${DELETE_ROLE}${roleId}`;

class RoleApi {
  static getStoredToken = () => {
    const UserToken = JSON.parse(localStorage.getItem("AccessToken") || "{}");
    return UserToken !== undefined ? UserToken : false;
  };

  // Get All Roles
  static getAllRoles = async (
    callback: (response: { status: boolean; data: RoleModuleInterface }) => void
  ) => {
    try {
      const response = await axios.get(REACT_APP_GET_ALLROLES, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${RoleApi.getStoredToken()}`,
        },
      });
      callback({ status: true, data: response.data });
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  // Get Role by ID
  static getRoleById = async (
    roleId: number,
    callback: (response: {
      status: boolean;
      data: RoleDetailModuleInterface;
    }) => void
  ) => {
    try {
      const response = await axios.get(REACT_APP_GET_ROLE(roleId), {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${RoleApi.getStoredToken()}`,
        },
      });
      callback({ status: true, data: response.data });
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  // Add Role
  static addRole = async (
    roleData: any,
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.post(REACT_APP_ADD_ROLE, roleData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${RoleApi.getStoredToken()}`,
        },
      });
      callback({ status: true, data: response.data });
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  // Update Role
  static updateRole = async (
    roleId: number,
    roleData: any,
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.patch(
        REACT_APP_UPDATE_ROLE(roleId),
        roleData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${RoleApi.getStoredToken()}`,
          },
        }
      );
      callback({ status: true, data: response.data });
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  // Delete Role
  static deleteRole = async (
    roleId: number,
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    try {
      const response = await axios.delete(REACT_APP_DELETE_ROLE(roleId), {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${RoleApi.getStoredToken()}`,
        },
      });
      callback({ status: true, data: response.data });
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };
}

export default RoleApi;
