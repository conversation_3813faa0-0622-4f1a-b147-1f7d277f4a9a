import { Injectable, Logger } from '@nestjs/common';
import { Subscriber } from 'rxjs';
import { OfflineNotificationRepository } from '../../libs/database/src/repositories/offline-notification.repository';
import { FileStorageService } from './file-storage.service';
import { OfflineNotificationEntity } from 'libs/database/entities';

export interface SSEMessage {
  type: 'notification_update' | 'progress_update' | 'completion' | 'error';
  notificationId: string;
  data: {
    status?: 'pending' | 'processing' | 'completed' | 'failed';
    progress?: number;
    message?: string;
    downloadUrl?: string;
    metadata?: {
      reportType?: 'namuna_eight' | 'namuna_nine';
      totalRecords?: number;
      processedRecords?: number;
      fileName?: string;
      fileSize?: number;
    };
  };
}

interface ClientConnection {
  userId: string;
  subscriber: Subscriber<MessageEvent>;
  lastPing: Date;
}

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);
  private clients: Map<string, ClientConnection[]> = new Map();
  private pingInterval: NodeJS.Timeout;

  constructor(
    private readonly offlineNotificationRepository: OfflineNotificationRepository,
    private readonly fileStorageService: FileStorageService,
  ) {
    this.pingInterval = setInterval(() => {
      this.pingClients();
    }, 30000); // Ping every 30 seconds
  }

  onModuleDestroy() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }
    this.closeAllConnections();
  }

  addClient(userId: string, subscriber: Subscriber<MessageEvent>): void {
    if (!this.clients.has(userId)) {
      this.clients.set(userId, []);
    }

    const clientConnection: ClientConnection = {
      userId,
      subscriber,
      lastPing: new Date(),
    };

    this.clients.get(userId)!.push(clientConnection);
    this.logger.log(`Client connected for user: ${userId}. Total connections: ${this.getTotalConnections()}`);
  }

  removeClient(userId: string, subscriber: Subscriber<MessageEvent>): void {
    const userConnections = this.clients.get(userId);
    if (userConnections) {
      const index = userConnections.findIndex(conn => conn.subscriber === subscriber);
      if (index !== -1) {
        userConnections.splice(index, 1);
        this.logger.log(`Client disconnected for user: ${userId}. Remaining connections: ${userConnections.length}`);

        if (userConnections.length === 0) {
          this.clients.delete(userId);
        }
      }
    }
  }

  sendNotification(userId: string, message: SSEMessage): void {
    const userConnections = this.clients.get(userId);
    if (!userConnections || userConnections.length === 0) {
      this.logger.warn(`No active connections for user: ${userId}`);
      return;
    }

    const deadConnections: ClientConnection[] = [];
console.log("userrrconeetion",userConnections)
console.log("deadConnections",deadConnections)

    userConnections.forEach(connection => {
      try {
        const messageEvent: any = { data: message };
        connection.subscriber.next(messageEvent);
        connection.lastPing = new Date();
      } catch (error) {
        this.logger.error(`Failed to send notification to user ${userId}:`, error);
        deadConnections.push(connection);
      }
    });

    deadConnections.forEach(deadConn => {
      this.removeClient(userId, deadConn.subscriber);
    });
  }

  broadcastNotification(message: SSEMessage): void {
    this.clients.forEach((_, userId) => {
      this.sendNotification(userId, message);
    });
  }

  sendProgressUpdate(userId: string, notificationId: string, progress: number, processedRecords?: number, totalRecords?: number): void {
    this.sendNotification(userId, {
      type: 'progress_update',
      notificationId,
      data: {
        status: 'processing',
        progress,
        message: `Processing... ${progress}% complete`,
        metadata: {
          processedRecords,
          totalRecords
        }
      }
    });
  }

  sendCompletionNotification(userId: string, notificationId: string, downloadUrl: string, fileName?: string): void {
    this.sendNotification(userId, {
      type: 'completion',
      notificationId,
      data: {
        status: 'completed',
        progress: 100,
        message: 'Process completed successfully. Your file is ready for download.',
        downloadUrl,
        metadata: {
          fileName
        }
      }
    });
  }

  sendErrorNotification(userId: string, notificationId: string, errorMessage: string): void {
    this.sendNotification(userId, {
      type: 'error',
      notificationId,
      data: {
        status: 'failed',
        message: errorMessage
      }
    });
  }

  private pingClients(): void {
    const now = new Date();
    const staleThreshold = 5 * 60 * 1000; // 5 minutes

    this.clients.forEach((connections, userId) => {
      const staleConnections: ClientConnection[] = [];

      connections.forEach(connection => {
        const timeSinceLastPing = now.getTime() - connection.lastPing.getTime();

        if (timeSinceLastPing > staleThreshold) {
          staleConnections.push(connection);
        } else {
          try {
            const pingMessage: any = { data: { timestamp: now.toISOString() } };
            connection.subscriber.next(pingMessage);
            connection.lastPing = now;
          } catch (error) {
            this.logger.error(`Failed to ping client for user ${userId}:`, error);
            staleConnections.push(connection);
          }
        }
      });

      staleConnections.forEach(staleConn => {
        this.removeClient(userId, staleConn.subscriber);
      });
    });
  }

  private closeAllConnections(): void {
    this.clients.forEach((connections, userId) => {
      connections.forEach(connection => {
        try {
          connection.subscriber.complete();
        } catch (error) {
          this.logger.error(`Error closing connection for user ${userId}:`, error);
        }
      });
    });
    this.clients.clear();
  }

  private getTotalConnections(): number {
    let total = 0;
    this.clients.forEach(connections => {
      total += connections.length;
    });
    return total;
  }

  getConnectionStats(): { totalUsers: number; totalConnections: number; userConnections: Record<string, number> } {
    const userConnections: Record<string, number> = {};

    this.clients.forEach((connections, userId) => {
      userConnections[userId] = connections.length;
    });

    return {
      totalUsers: this.clients.size,
      totalConnections: this.getTotalConnections(),
      userConnections
    };
  }

  async createOfflineNotification(
    userId: string,
    notificationId: string,
    title: string,
    message: string,
    type: OfflineNotificationEntity['type'] = 'info',
    metadata?: OfflineNotificationEntity['metadata']
  ): Promise<OfflineNotificationEntity> {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Expire after 7 days

    return await this.offlineNotificationRepository.create({
      userId,
      notificationId,
      title,
      message,
      type,
      status: 'pending',
      metadata,
      expiresAt
    });
  }

  async handleWebhookNotification(
    notificationId: string,
    fileBuffer: Buffer,
    fileName: string,
    webhookData?: any
  ): Promise<void> {
    try {
      console.log("notificationId====",notificationId)
      const notification = await this.offlineNotificationRepository.findByNotificationId(notificationId);

      if (!notification) {
        this.logger.warn(`Notification not found for ID: ${notificationId}`);
        return;
      }

      const { filePath, fileSize } = await this.fileStorageService.saveFile(
        fileName,
        fileBuffer,
        notification.userId
      );

      const downloadUrl = this.fileStorageService.generateDownloadUrl(notificationId);

    // Update status to 'delivered' instead of 'completed'
    await this.offlineNotificationRepository.updateStatus(
      notificationId,
      'delivered',
      {
        filePath,
        fileName,
        fileSize,
        downloadUrl,
        webhookReceivedAt: new Date(),
        metadata: {
          ...notification.metadata,
          webhookData
        }
      }
    );

      const userConnections = this.clients.get(notification.userId);
      console.log("userConnections------->",userConnections,this.clients)
      if (userConnections && userConnections.length > 0) {
        this.sendCompletionNotification(notification.userId, notificationId, downloadUrl, fileName);
        await this.offlineNotificationRepository.markAsDelivered(notificationId);
      }

      this.logger.log(`Webhook notification processed for ${notificationId}`);
    } catch (error) {
      this.logger.error(`Error processing webhook notification ${notificationId}:`, error);

      await this.offlineNotificationRepository.updateStatus(
        notificationId,
        'failed',
        { message: error.message }
      );
    }
  }

  async getAllNotifications(userId: string, includeRead: boolean = false): Promise<OfflineNotificationEntity[]> {
    return await this.offlineNotificationRepository.findALLByUserId(userId, includeRead);
  }

  async deliverPendingNotifications(userId: string): Promise<void> {
    const pendingNotifications = await this.getAllNotifications(userId);

    for (const notification of pendingNotifications) {
      if (notification.status === 'completed' && notification.downloadUrl) {
        this.sendCompletionNotification(
          userId,
          notification.notificationId,
          notification.downloadUrl,
          notification.fileName
        );

        await this.offlineNotificationRepository.markAsDelivered(notification.notificationId);
      }
    }

    this.logger.log(`Delivered ${pendingNotifications.length} pending notifications to user ${userId}`);
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    await this.offlineNotificationRepository.markAsRead(notificationId);
  }

  async cleanupExpiredNotifications(): Promise<void> {
    try {
      const expiredNotifications = await this.offlineNotificationRepository.findExpiredNotifications();

      if (expiredNotifications.length > 0) {
        const filePaths = expiredNotifications
          .filter(n => n.filePath)
          .map(n => n.filePath);

        await this.fileStorageService.cleanupExpiredFiles(filePaths);

        await this.offlineNotificationRepository.deleteExpiredNotifications();

        this.logger.log(`Cleaned up ${expiredNotifications.length} expired notifications`);
      }
    } catch (error) {
      this.logger.error('Error cleaning up expired notifications:', error);
    }
  }
}
