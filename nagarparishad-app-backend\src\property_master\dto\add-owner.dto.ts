import {
  IsString,
  IsOptional,
  IsBoolean,
  IsEnum,
  IsUUID,
} from 'class-validator';

export enum Gender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER',
}

export enum MaritalStatus {
  YES = 'yes',
  NO = 'no',
  DIVORCED = 'divorced',
}

export class OwnerDto {
  @IsUUID()
  @IsOptional()
  owner_type_id: string;

  @IsString()
  @IsOptional()
  name: string;

  @IsString()
  @IsOptional()
  mobile_number: string;

  @IsString()
  @IsOptional()
  email_id: string;

  @IsString()
  @IsOptional()
  aadhar_number: string;

  @IsEnum(Gender)
  @IsOptional()
  gender: Gender;

  @IsEnum(MaritalStatus)
  @IsOptional()
  marital_status: MaritalStatus;

  @IsString()
  @IsOptional()
  pan_card: string;

  @IsString()
  @IsOptional()
  partner_name: string;
}

// For the full input structure:
export class OwnerWrapperDto {
  @IsOptional()
  owners: OwnerDto[];

  @IsString()
  @IsOptional()
  remark: string;

  @IsOptional()
  photos: any[]; // Define specific PhotoDto if needed

  @IsOptional()
  documents: any[]; // Define specific DocumentDto if needed
}
