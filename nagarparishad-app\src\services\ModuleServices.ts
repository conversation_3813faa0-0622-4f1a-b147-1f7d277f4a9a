import axios from "axios";
import { ModuleInterface } from "@/model/module/moduleInterface";
import { GET_MODULES, ADD_MODULE } from "@/constant/utils/moduleUtils";

const baseURL = import.meta.env.VITE_APP_BASE_URL;

// Create axios instance with interceptors
const apiClient = axios.create({
  baseURL: baseURL,
});

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = ModuleApi.getStoredToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle 401 and refresh token
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        }).catch((err) => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = ModuleApi.getStoredRefreshToken();
        if (!refreshToken) {
          // No refresh token, redirect to login
          ModuleApi.handleLogout();
          return Promise.reject(error);
        }

        const newTokens = await ModuleApi.refreshAccessToken(refreshToken);
        if (newTokens.status) {
          const newAccessToken = newTokens.data.data.accessToken;
          const newRefreshToken = newTokens.data.data.refreshToken;

          // Update stored tokens
          localStorage.setItem('AccessToken', JSON.stringify(newAccessToken));
          localStorage.setItem('RefreshToken', JSON.stringify(newRefreshToken));

          // Update the original request with new token
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

          // Process queued requests
          processQueue(null, newAccessToken);

          // Retry the original request
          return apiClient(originalRequest);
        } else {
          // Refresh failed, logout user
          processQueue(error, null);
          ModuleApi.handleLogout();
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Refresh failed, logout user
        processQueue(refreshError, null);
        ModuleApi.handleLogout();
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

class ModuleApi {
  static getStoredToken = () => {
    const UserToken = JSON.parse(localStorage.getItem("AccessToken") || "{}");
    return UserToken !== undefined ? UserToken : false;
  };

  static getStoredRefreshToken = () => {
    const RefreshToken = JSON.parse(localStorage.getItem("RefreshToken") || "{}");
    return RefreshToken !== undefined ? RefreshToken : false;
  };

  static refreshAccessToken = async (refreshToken: string) => {
    const url = `${baseURL}/v1/auth/refreshtoken`;
    try {
      const response = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${refreshToken}`
        },
      });

      if (response.status === 200) {
        return { status: true, data: response.data };
      } else {
        console.log("Error refreshing token: " + JSON.stringify(response.data));
        return { status: false, data: response.data };
      }
    } catch (err) {
      console.log("Error refreshing token:", err);
      return { status: false, data: err };
    }
  };

  static handleLogout = () => {
    // Clear all tokens from localStorage
    localStorage.removeItem('AccessToken');
    localStorage.removeItem('RefreshToken');
    localStorage.removeItem('UserData');

    // Redirect to login page
    window.location.href = '/login';
  };

  // Get Modules
  static getModules = async (
    callback: (response: {
      status: boolean;
      data: ModuleInterface[] | any;
    }) => void,
  ) => {
    try {
      const response = await apiClient.get(GET_MODULES, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };

  // Add Module
  static addModule = async (
    moduleData: any,
    callback: (response: { status: boolean; data: any }) => void,
  ) => {
    try {
      const response = await apiClient.post(ADD_MODULE, moduleData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({
        status: false,
        data: err.response ? err.response.data : err.message,
      });
    }
  };
}

export default ModuleApi;
