import { IsString, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { PropertyOwnerDetailsDto } from './property-owner-details.dto';
import { PropertyUsageDetailsDto } from './property-usage-details.dto';
import { CommonFiledsOfPropertyDto } from './commonFields.dto';

export class CreatePropertyMasterDto {
  @IsString()
  @IsOptional()
  propertyNumber: string;

  @IsString()
  @IsOptional()
  old_propertyNumber: string;

  @IsString()
  @IsOptional()
  city_survey_number: string;

  @IsString()
  @IsOptional()
  address: string;

  @IsString()
  @IsOptional()
  house_or_apartment_name: string;

  @IsString()
  @IsOptional()
  city: string;
  
  @IsString()
  @IsOptional()
  house_number: string;
  
  @IsString()
  @IsOptional()
  block_number: string;

  @IsString()
  @IsOptional()
  plot_number: string;

  @IsString()
  @IsOptional()
  gat_no: string;

  @IsString()
  @IsOptional()
  latitude: string;

  @IsString()
  @IsOptional()
  longitude: string;

  @IsString()
  @IsOptional()
  mobile_number: string;

  @IsString()
  @IsOptional()
  email_id: string;

  @IsNumber()
  @IsOptional()
  plot_area: number;

  @IsNumber()
  @IsOptional()
  Plot_construction_area: number;

  @IsNumber()
  @IsOptional()
  construction_area: number;

  @IsString()
  @IsOptional()
  remark: string;

    @IsString()
  @IsOptional()
  property_remark: string;



  @IsString()
  @IsOptional()
  landmark: string;

  @IsString()
  @IsOptional()
  property_desc : string;
  
  @IsString()
  @IsOptional()
  street_id: string;

  @IsString()
  @IsOptional()
  ward_id: string;

  @IsString()
  @IsOptional()
  register_id: string; // Added register_id

  @IsString()
  @IsOptional()
  zone_id: string;

 

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PropertyOwnerDetailsDto)
  property_owner_details: PropertyOwnerDetailsDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PropertyUsageDetailsDto)
  property_usage_details: PropertyUsageDetailsDto[];


  @ValidateNested()
  @Type(() => CommonFiledsOfPropertyDto)
  commonFields: CommonFiledsOfPropertyDto;
}
