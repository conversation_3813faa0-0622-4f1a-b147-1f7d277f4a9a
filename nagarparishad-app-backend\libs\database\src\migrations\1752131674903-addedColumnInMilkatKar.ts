import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedColumnInMilkatKar1752131674903 implements MigrationInterface {
    name = 'AddedColumnInMilkatKar1752131674903'

    public async up(queryRunner: QueryRunner): Promise<void> {
      
        await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD "property_type_discount" double precision NOT NULL DEFAULT '0'`);
        }

    public async down(queryRunner: QueryRunner): Promise<void> {
    
        await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP COLUMN "property_type_discount"`);
       }

}
