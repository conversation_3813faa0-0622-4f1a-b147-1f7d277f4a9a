import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import Property<PERSON><PERSON> from "@/services/PropertyServices";

// Fetch reassessment ranges for MilkatKar
const fetchReassessmentRanges = async () => {
  return new Promise((resolve, reject) => {
    PropertyApi.getReassessmentRanges((response) => {
      if (response.status && response.data.statusCode === 200) {
        // Format the data to include the range display
        const formattedData = response.data.data.map(item => ({
          ...item,
          reassessment_id: item.reassessment_range_id,
          reassessment_range: `${item.start_range} to ${item.end_range}`
        }));
        resolve(formattedData);
      } else {
        reject(response.data);
      }
    });
  });
};

// Fetch MilkatKar data
const fetchMilkatKarData = async () => {
  return new Promise((resolve, reject) => {
    // This would be replaced with an actual API call to get MilkatKar data
    // For now, we're using the tax year data as a placeholder
    PropertyApi.getReassessmentRanges((response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(response.data);
      }
    });
  });
};

// Process MilkatKar generation
const processMilkatKar = async ({ reassessmentYearId }: { reassessmentYearId: string }) => {
  return new Promise((resolve, reject) => {
    PropertyApi.processMilkatKar(reassessmentYearId, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data);
      } else {
        reject(new Error(response.data.message || "Failed to generate Milkat Kar"));
      }
    });
  });
};



export const useMilkatKarController = () => {
  const queryClient = useQueryClient();

  // Mutation for processing MilkatKar
  const processMilkatKarMutation = useMutation({
    mutationFn: processMilkatKar,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["milkatKarData"] });
    },
  });



  // Query for reassessment ranges
  const {
    data: reassessmentRanges,
    isLoading: reassessmentRangesLoading,
    refetch: refetchReassessmentRanges
  } = useQuery({
    queryKey: ["reassessmentRanges"],
    queryFn: fetchReassessmentRanges,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
  });

  // Query for MilkatKar data
  const {
    data: milkatKarData,
    isLoading: milkatKarDataLoading,
    refetch: refetchMilkatKarData
  } = useQuery({
    queryKey: ["milkatKarData"],
    queryFn: fetchMilkatKarData,
    staleTime: 0, // Always refetch
    refetchOnWindowFocus: true,
  });

  return {
    reassessmentRanges: reassessmentRanges || [],
    reassessmentRangesLoading,
    milkatKarData: milkatKarData || [],
    milkatKarDataLoading,
    processMilkatKar: processMilkatKarMutation.mutate,
    refetchReassessmentRanges,
    refetchMilkatKarData
  };
};
