<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
      .container {
        max-width: 50rem;
        margin: 0 auto;
        padding: 1rem 0;
        border: 2px solid black;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
      }
      .header img {
        width: 6rem;
        height: 6rem;
      }
      .header .text-center {
        text-align: center;
      }
      .header h1 {
        font-size: 1.25rem;
        font-weight: bold;
      }
      .header p {
        font-size: 0.875rem;
      }
      .header .bold {
        font-weight: bold;
      }
      .divider {
        height: 2px;
        background-color: black;
        margin-bottom: 1rem;
      }
      .info-section {
        margin-bottom: 1rem;
      }
      .info-section .flex {
        display: flex;
        justify-content: space-between;
      }
      .info-section p {
        margin: 0;
      }
      .info-section .font-semibold {
        font-weight: 600;
      }
      .table-container {
        overflow-x: auto;
      }
      .table {
        min-width: 100%;
        border-collapse: collapse;
        border: 1px solid #71717a;
      }
      .table th,
      .table td {
        border: 1px solid #71717a;
        padding: 0.5rem;
      }
      .table th {
        text-align: left;
      }
      .footer {
        margin-bottom: 1rem;
      }
      .flex {
        display: flex;
      }
      .border-zinc-400 {
        border-color: #dae1e7;
      }
      .h-2 {
        height: 2px;
      }

      .max-w-4xl {
        max-width: 768px;
      }
      .sign-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: end;
        margin-top: 30px;
        text-align: center;
        padding-right: 30px;
        font-size: 15px;
      }
      .mx-auto {
        margin-left: auto;
        margin-right: auto;
      }

      .p-4 {
        padding: 16px;
      }

      .border-2 {
        border-width: 2px;
      }

      .border-black {
        border-color: black;
      }

      .flex {
        display: flex;
      }

      .justify-between {
        justify-content: space-between;
      }

      .items-center {
        align-items: center;
      }

      .mb-4 {
        margin-bottom: 16px;
      }

      .w-24 {
        width: 96px;
      }

      .h-24 {
        height: 96px;
      }

      .text-center {
        text-align: center;
      }

      .text-xl {
        font-size: 1.25rem;
      }

      .font-bold {
        font-weight: bold;
      }

      .text-sm {
        font-size: 0.875rem;
      }
      .text-xs {
        font-size: 0.675rem;
      }
      .h-2 {
        height: 8px;
      }

      .overflow-x-auto {
        overflow-x: auto;
      }

      .min-w-full {
        min-width: 100%;
      }

      .border-collapse {
        border-collapse: collapse;
      }

      .border {
        border: 1px solid #525252;
      }

      .border-zinc-400 {
        border-color: #525252;
      }

      .px-4 {
        padding-left: 8px;
        padding-right: 8px;
      }
      .px-1 {
        padding-left: 5px;
        padding-right: 5px;
      }
      .py-2 {
        padding-top: 2px;
        padding-bottom: 2px;
      }
      .border-2 {
        border: 2px solid black;
      }
      p {
        margin-top: 3px;
        margin-bottom: 3px;
      }
      .pb-0{
        padding-bottom: 0;

      }
    </style>
     <script>
      function numberToWords(num) {
          const units = ['zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
          const teens = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
          const tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];
          const scales = ['hundred', 'thousand', 'million', 'billion', 'trillion'];

          if (num < 10) return capitalize(units[num]);
          if (num < 20) return capitalize(teens[num - 10]);
          if (num < 100) return capitalize(tens[Math.floor(num / 10)] + (num % 10 ? '-' + units[num % 10] : ''));

          let word = '';
          let scale = 0;
          while (num > 0) {
              let n = num % 1000;
              if (n !== 0) {
                  let str = '';
                  if (n % 100 < 20) {
                      str = (n % 100 < 10 ? units[n % 100] : teens[n % 100 - 10]);
                  } else {
                      str = tens[Math.floor(n % 100 / 10)] + (n % 10 ? '-' + units[n % 10] : '');
                  }
                  if (Math.floor(n / 100) > 0) {
                      str = units[Math.floor(n / 100)] + ' ' + scales[0] + ' ' + str;
                  }
                  word = str + ' ' + (scale > 0 ? scales[scale] + ' ' : '') + word;
              }
              scale++;
              num = Math.floor(num / 1000);
          }
          return capitalize(word.trim());
      }

      function capitalize(word) {
        //  return word.charAt(0).toUpperCase() + word.slice(1);
      }

     
  </script>
  </head>
  <body>
   
    <div class="container p-2 pb-0">
      <div class="flex justify-between items-center">
        <img src="https://placehold.co/100x100" alt="logo" class="w-24 h-24" />
        <div class="text-center px-1">
          <h1 class="text-xl font-bold">शिरोळ नगरपरिषद शिरोळ</h1>
          <p class="text-sm">वि.क्र.१०/२०२३ दि.२८/०९/२०२३ अखेरचे</p>
          <p class="text-xl font-bold">करमागणी प्रोव्हिजनल बिल</p>
          <p class="text-sm">
            (महाराष्ट्र नगरपरिषद, नगरपंचायत व औद्योगिक नगरी अधिनियम १९६५ चे नियम
            १५० अन्वये)
          </p>
        </div>
        <img src="https://placehold.co/100x100" alt="logo" class="w-24 h-24" />
      </div>
      <hr />
      <div class="mb-4 px-1">
       
      </div>
      <div class="overflow-x-auto">
   
      </div>

      <div class="mb-4">
        <p class="text-xs">
          टीप:- १) स्तंभ ४ मध्ये दर्शविणेत आलेल्या करांची रक्कम आपणाकडून सन २०२३
          २०२४या आर्थिक वर्षातील येणे असुन आपण हे बिल १५ दिवसाच्या आत कराची सर्व
          रक्कम नगरपरिषदेकडे जमा करावी.
        </p>
        <p class="text-xs">
          २) अ) या बिलात मागणी केलेली रक्कम आपणाकडून देण्यात न आलेस किंवा व)ती
          का देण्यात आली नाही यावदल मुख्याधिकारी यांची खात्री पटेल असे कोणतेही
          कारण दर्शविण्यात आले नाहीतर किंवा क) महाराष्ट्र नगरपरिषद अधिनियम १९६५
          चे कलम १६९ नुसार कोणतेही अपील दाखल करण्यात आले नसेल तर उक्त रक्कम
          देण्यासंबंधी आपल्यावर कलम १५२ नुसार जप्तीची कारवाई करण्यात येईल ड)
          अनधिकृत बांधकामास नियमानुसार शास्ती | आकारणी करण्यात येईल
        </p>
        <p class="text-xs">
          ३) बिल देण्यात आलेपासून १५ दिवसाच्या आत कराची सर्व रक्कम भरलेस कलम १५०
          (३) अन्वये संकलित कराच्या चालू मागणीच्या रक्कमेवर १ % सूट देणेत येईल.
        </p>
        <p class="text-xs">
          ४) मालमत्ता कराचे देयक व नोटीस देवूनही विहित मुदतीत कर भरणा न केलेस
          संबंधित मिळकत धारकांचे पाण्याचे नळ कनेक्शन कोणतीही पूर्व सूचना न |
          देता खंडित केले जाईल, याची नोंद घ्यावी
        </p>
        <p class="text-xs">
          ५ ) कलम १५० अ ( १ ) अन्वये दिलेल्या कराची रक्कम दिनांक ३१/१२/२०२३
          पूर्वी न भरलेस न भरलेल्या येणे असलेल्या रक्कमेवर दरमहा २% प्रमाणे
          शास्ती (दंड) आकारण्यात येईल आणि बिलाची पूर्ण रक्कम भरेपर्यंत अशी
          शास्ती (दंड) भरणे बंधनकारक राहील.
        </p>
        <p class="text-xs">
          ६) जर आपण नव्याने बांधकाम केलेस /बांधकामात बदल केलेस अथवा मालमत्तेच्या
          वापरात बदल केला असलेस सुधारित कर आकारणी करणेत येईल याची नोंद घ्यावी.
        </p>
        <p class="text-xs">
          ७) विलामधील संपूर्ण रक्कम आपण पूर्वीच भरली असलेस सदर बिल ग्राह्य धरणेत
          येऊ नये.
        </p>

        <p class="text-xs">
          ८ ) सन २०२३ २०२४ या आर्थिक वर्षात कलम १२४ (२) अन्वये शिरोळ नगरपरिषद
          शिरोळ नगरपरिषद हद्दीतील सर्व मिळकतीचे करपात्र मुल्यांमध्ये सुधारणा
          करणेचे काम सुरु असून, सदरचे प्रोव्हिजनल मागणी बिल सन २०२३ २०२४ च्या
          चतुर्थ वार्षिक कर आकारणीत होणाऱ्या बदलास अधीन राहून देणेत आले आहे.
        </p>
      </div>
      <div class="sign-box">
        <div>
          <p>मुख्याधिकारी</p>

          <p>शिरोळ नगरपरिषद शिरोळ</p>
        </div>
      </div>
    </div>
    
  </body>
</html>
