import { Controller, Get, Param, Post, Body, Put, Delete, Query } from '@nestjs/common';
import { PaymentLogMasterService } from './payment_log_master.service';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@Controller('payment_log_master')
export class PaymentLogMasterController {
  constructor(
    private readonly PaymentMasterService: PaymentLogMasterService,
  ) {}

  // API to get current bill data by property ID
  @Form('Payment Logs')
  @Permissions('can_read')
  @Get('current-bill')
  async getCurrentBillData(
    @Query() params: any
  ) {
        return await this.PaymentMasterService.findLatestBillData(params.value,params.searchOn,params.fy);
  }

  @Form('Payment Logs')
  @Permissions('can_read')
  @Get('payment_logs')
  async getPaymentLogsData(
    @Query() params: any
  ) {
        return await this.PaymentMasterService.getAllPaymentLogsData(params);
  }

  @Form('Payment Logs')
  @Permissions('can_write')
  @Post('pay_bill')

  async createPaymentLog(@Body() paymentData: any) {
        return await this.PaymentMasterService.createPayment(paymentData);
  }

  @Form('Payment Logs')
  @Permissions('can_update')
  @Put('edit_payment_log')
  async editPaymentLog(
    @Query('id') paymentId: string,
    @Body() paymentData: any,
  ) {
            return await this.PaymentMasterService.editPaymentLog(paymentId, paymentData);
  }

  // @Form('Payment Logs')
  // @Permissions('can_read')
  @Get('payment-modes')
  async getPaymentMode() {
    return await this.PaymentMasterService.getPaymentMode();
  }

  @Form('Payment Logs')
  @Permissions('can_delete')
  @Delete('payment')
  async deletePayment(
    @Query('id') receipt_id : string 
  ) {
    return await this.PaymentMasterService.deletePayment(receipt_id);
  }
  
  @Form('Payment Logs')
  @Permissions('can_update')
  @Put('tax-payer')
  async updateTaxPayer() {
    return await this.PaymentMasterService.updateTaxPayer();
  }

}


