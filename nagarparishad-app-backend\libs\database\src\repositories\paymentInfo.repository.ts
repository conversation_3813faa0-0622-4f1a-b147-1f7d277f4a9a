import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaymentInfoEntity } from '../entities';
import { paginate, PaginationOptions } from '@helper/helpers/Pagination';
import { BadRequestException } from '@nestjs/common';
import { PropertyGroupDto } from 'src/payment_log_master/dto/payment_log_master.dto';

export class PaymentInfoRepository extends Repository<PaymentInfoEntity> {
  constructor(
    @InjectRepository(PaymentInfoEntity)
    private readonly paymentInfoRepository: Repository<PaymentInfoEntity>,
  ) {
    super(
      paymentInfoRepository.target,
      paymentInfoRepository.manager,
      paymentInfoRepository.queryRunner,
    );
  }

  async savePayment(
    input: Partial<PaymentInfoEntity>,
  ): Promise<PaymentInfoEntity> {
    let payment = this.paymentInfoRepository.create(input);
    return await this.paymentInfoRepository.save(payment);
  }

  async findAllPayments() {
    return await this.paymentInfoRepository
      .createQueryBuilder('payment_info')
      .leftJoinAndSelect('payment_info.property', 'property')
      .orderBy('payment_info.created_at', 'DESC')
      .getMany();
  }

  async findAllPaymentInfo() {
    return await this.paymentInfoRepository
      .createQueryBuilder('payment_info')
      .orderBy('payment_info.payment_date', 'DESC')
      .getMany();
  }

  async findAllWithoutTaxPayer() : Promise<PropertyGroupDto[]> {
    return await this.paymentInfoRepository
      .createQueryBuilder('payment_info')
      .leftJoinAndSelect('payment_info.property', 'property')
      .leftJoinAndSelect('property.property_owner_details', 'property_owner_details')
      .leftJoinAndSelect('property_owner_details.owner_type', 'owner_type') // Include owner type details
      .where('payment_info.tax_payer_name IS NULL')
      .orderBy('payment_info.payment_date' , 'ASC')
      .getMany()
      .then(data => {
        const grouped = data.reduce((acc, payment) => {
          const { property } = payment;
  
          if (!acc[property.property_id]) {
            acc[property.property_id] = {
              property_id: property.property_id,
              property_number: property.propertyNumber,
              owner: null, // Will store only one selected owner
              payments: []
            };
          }
  
          // Select owner based on priority (स्वत: > भोगवटादार)
          if (!acc[property.property_id].owner) {
            const preferredOwner =
              property.property_owner_details.find(o => o.owner_type.owner_type === 'स्वत:') ||
              property.property_owner_details.find(o => o.owner_type.owner_type === 'भोगवटादार');
  
            if (preferredOwner) {
              acc[property.property_id].owner = {
                owner_id: preferredOwner.property_owner_details_id,
                owner_name: preferredOwner.name,
                owner_type: preferredOwner.owner_type?.owner_type || null
              };
            }
          }
  
          // Store payment details
          acc[property.property_id].payments.push({
            payment_id: payment.payment_id,
            tax_payer_name: payment.tax_payer_name,
            payment_date: payment.payment_date
          });
  
          return acc;
        }, {});
  
        return Object.values(grouped);
      });
  }
  

  async findPaymentById(id: string) {
    return await this.paymentInfoRepository
      .createQueryBuilder('payment_info')
      .where('payment_info.payment_id = :id', { id })
      .getOne();
  }

  async updatePayment(id: string, input: Partial<PaymentInfoEntity>) {
    return await this.paymentInfoRepository
      .createQueryBuilder('payment_info')
      .update(PaymentInfoEntity)
      .set(input)
      .where('payment_id = :id', { id })
      .execute();
  }

  async deletePayment(id: string) {
    return await this.paymentInfoRepository
      .createQueryBuilder('payment_info')
      .softDelete()
      .where('payment_id = :id', { id })
      .execute();
  }

  async getPaymentsByPropertyNumber(
    value: any,
    searchOn:any
  ) {
    try {
      let data = null, total = null;
        if (value && searchOn) {
        // Determine the field to search based on 'searchOn'
   const searchField =`property.${searchOn}`;
        [data, total] = await this.paymentInfoRepository
          .createQueryBuilder('payment_info')
          .select([
            'payment_info.payment_id',
            'payment_info.amount',
            'payment_info.payment_mode',
            'payment_info.payment_status',
            'payment_info.payment_date',
            'property.propertyNumber',
            'property.old_propertyNumber', // Include oldPropertyNumber in the selection
            'receipt.book_number',
            'receipt.receipt_id',
            'receipt.book_receipt_number',
            'paidData.remaining_amount',
            'paidData.total_amount',
          ])
          .leftJoin('payment_info.property', 'property')
          .leftJoin('payment_info.receipts', 'receipt')
          .leftJoin('payment_info.paidData', 'paidData')
          .where(`${searchField} = :value`, { value })
          .getManyAndCount();
      } 
  
      return {data:data , total :total};
    } catch (error) {
      // Handle error
      console.error("Error fetching payment logs:", error);
      throw error;
    }
  }
  async getAllPaymentLogsData(fy: string, value: string, searchOn: string, options: PaginationOptions) {
    try {
      const queryBuilder = await this.paymentInfoRepository
        .createQueryBuilder('payment_info')
        .select([
          'payment_info.payment_id',
          'payment_info.amount',
          'payment_info.payment_mode',
          'payment_info.payment_status',
          'payment_info.payment_date',
          'payment_info.updatedAt',          
          'property.propertyNumber',
          'property.old_propertyNumber',
          'ownerDetails.name',
          'receipt.book_number',
          'receipt.receipt_id',
          'receipt.book_receipt_number',
        ])
        .leftJoin('payment_info.property', 'property')
                .leftJoin('property.property_owner_details', 'ownerDetails')

        .leftJoin('payment_info.receipts', 'receipt');
  
      if (value && searchOn) {
        let exactMatchQuery = '';
        let partialMatchQuery = '';
  
        switch (searchOn) {
          case 'property_number':
            exactMatchQuery = 'property.propertyNumber = :value';
            partialMatchQuery = 'property.propertyNumber LIKE :partialValue';
            break;
          case 'old_property_number':
            exactMatchQuery = 'property.old_propertyNumber = :value';
            partialMatchQuery = 'property.old_propertyNumber LIKE :partialValue';
            break;
          case 'receipt_id':
            exactMatchQuery = 'receipt.receipt_id = :value';
            partialMatchQuery = 'receipt.receipt_id LIKE :partialValue';
            break;
          case 'book_number':
            exactMatchQuery = 'receipt.book_number = :value';
            partialMatchQuery = 'receipt.book_number LIKE :partialValue';
            break;
          case 'receipt_number':
            exactMatchQuery = 'receipt.book_receipt_number = :value';
            partialMatchQuery = 'receipt.book_receipt_number LIKE :partialValue';
            break;
          case 'payment_mode':
            exactMatchQuery = 'payment_info.payment_mode = :value';
            partialMatchQuery = 'payment_info.payment_mode LIKE :partialValue';
            break;
          case 'payment_status':
            exactMatchQuery = 'payment_info.payment_status = :value';
            partialMatchQuery = 'payment_info.payment_status LIKE :partialValue';
            break;
          case 'payment_date':
            exactMatchQuery = 'payment_info.payment_date = :value';
            partialMatchQuery = 'payment_info.payment_date LIKE :partialValue';
            break;
          case 'owner_details_name':   
            exactMatchQuery = 'ownerDetails.name = :value';
            partialMatchQuery = 'ownerDetails.name LIKE :partialValue';
            break;
          default:
            throw new BadRequestException('Invalid searchOn parameter');
        }
  
        queryBuilder
          .andWhere(`(${partialMatchQuery})`, {
            value: value,
            partialValue: `${value}%`,
          });
      }
options.sortBy='updatedAt';
      return await paginate(queryBuilder, options, 'payment_info');
  
    } catch (error) {
      throw error;
    }
  }
  
}
