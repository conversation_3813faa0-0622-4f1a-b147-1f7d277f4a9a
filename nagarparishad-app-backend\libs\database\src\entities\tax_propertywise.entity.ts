import {
  BaseEntity,
  ManyToOne,
  OneToMany,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PropertyEntity } from './property.entity';
import { PropertyTypeMasterEntity } from './property-type-master.entity';
import { Property_Usage_Details_Entity } from './property-usage-details.entity';
import { Tax_PropertyEntity } from './tax_property.entity';

@Entity('tax_propertywise')
export class Tax_PropertyWiseEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  tax_propertywise_id: string;

  @ManyToOne(() => PropertyEntity, (property) => property.property_id, {onDelete : 'CASCADE'})
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

 
  @ManyToOne(() => Tax_PropertyEntity, (tax_Property) => tax_Property.tax_property_id)
  @JoinColumn({ name: 'tax_property_id' })
  tax_Property: Tax_PropertyEntity;



  @ManyToOne(() => Property_Usage_Details_Entity, (property_usage_details) => property_usage_details.property_usage_details_id)
  @JoinColumn({ name: 'property_usage_details_id' })
  property_usage_details: Property_Usage_Details_Entity;

  @Column({
    type: String,
    name: 'financial_year',
    nullable: false,
  })
  financial_year: string;

  @Column({ type: String, nullable: false })
  bill_no: string;

  @Column({ type: 'float', nullable: false })
  sq_ft_meter: number;

  //ready reckner rate
  @Column({ type: 'float', nullable: false })
  rr_rate: number;

  //ready reckner construction rate
  @Column({ type: 'float', nullable: false })
  rr_construction_rate: number;

  
  @Column({ type: 'float', nullable: false })
  depreciation_rate: number;

  //bharank 
  @Column({ type: 'float', nullable: false })
  weighting: number;

  
  @Column({ type: 'float', nullable: false })
  capital_value: number;

  @Column({ type: 'float', nullable: false })
  tax_value: number;

  @Column({ type: 'float', nullable: false })
  tax: number;

  @Column({ type: 'date', nullable: false })
  bill_generation_date: Date;

  

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
