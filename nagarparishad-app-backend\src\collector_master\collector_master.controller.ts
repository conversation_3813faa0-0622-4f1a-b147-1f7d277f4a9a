import { Controller, Get, Post, Put, Delete, Param, Body, Patch } from '@nestjs/common';
import { CollectorMasterService } from './collector_master.service';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CollectorMaster } from 'libs/database/entities';
import { CreateCollectorDto, UpdateCollectorDto } from './dtos/collector_master.dto';

@ApiTags('collector-master')
@Controller('collector-master')
export class CollectorMasterController {
    constructor(private readonly collectorService: CollectorMasterService) {}

    @ApiOperation({ summary: 'Get all collectors' })
    @ApiResponse({ status: 200, description: 'Returns all collectors' })
    @Get()
    async getAllCollectors(): Promise<{ message: string; data: CollectorMaster[] }> {
        return this.collectorService.getAllCollectors();
    }

    @ApiOperation({ summary: 'Get collector by ID' })
    @ApiResponse({ status: 200, description: 'Returns the collector details' })
    @Get(':collectorId')
    async getCollectorById(@Param('collectorId') collectorId: string): Promise<{ message: string; data: CollectorMaster | null }> {
        return this.collectorService.getCollectorById(collectorId);
    }

    @ApiOperation({ summary: 'Get collector by Ward ID' })
    @ApiResponse({ status: 200, description: 'Returns the collector assigned to the ward' })
    @Get('ward/:wardId')
    async getCollectorByWard(@Param('wardId') wardId: string): Promise<{ message: string; data: CollectorMaster | null }> {
        return this.collectorService.getCollectorByWard(wardId);
    }

    @ApiOperation({ summary: 'Create a new collector' })
    @ApiResponse({ status: 201, description: 'Collector created successfully' })
    @Post()
    async createCollector(@Body() collectorData: CreateCollectorDto): Promise<{ message: string; data: CollectorMaster }> {
        return this.collectorService.createCollector(collectorData);
    }

    @ApiOperation({ summary: 'Update collector details' })
    @ApiResponse({ status: 200, description: 'Collector updated successfully' })
    @Put(':collectorId')
    async updateCollector(
        @Param('collectorId') collectorId: string,
        @Body() updateData: UpdateCollectorDto
    ): Promise<{ message: string; data: CollectorMaster | null }> {
        return this.collectorService.updateCollector(collectorId, updateData);
    }

    @ApiOperation({ summary: 'Delete a collector' })
    @ApiResponse({ status: 200, description: 'Collector deleted successfully' })
    @Delete(':collectorId')
    async deleteCollector(@Param('collectorId') collectorId: string): Promise<{ message: string; success: boolean }> {
        
        return await this.collectorService.deleteCollector(collectorId);
    }

    @Put(':collectorId/books')
    async updateBooks(
      @Param('collectorId') collectorId: string,
      @Body('bookIds') bookIds: string[],
    ) {
      const result = await this.collectorService.updateBooksForCollector(collectorId, bookIds);
      return {
        message: 'Books updated successfully for the collector.',
        data: result,
      };
    }

    @Patch(':collectorId/assign-ward/:wardId')
    async assignWardToCollector(
        @Param('collectorId') collectorId: string,
        @Param('wardId') wardId: string,
      ) {
        return this.collectorService.assignWardToCollector(collectorId, wardId);
      }
  
}
