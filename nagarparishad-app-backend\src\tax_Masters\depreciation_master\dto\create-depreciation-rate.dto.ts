// src/master-depreciation/dto/create-depreciation-rate.dto.ts
import { IsString, IsNumber, IsUUID, IsOptional, IsNotEmpty, IsIn } from 'class-validator';

export class CreateDepreciationRateDto {
  @IsNumber()
  from_age: number;

  @IsNumber()
  to_age: number;

  @IsString()
  @IsOptional()
  financial_year?: string; // Optional for backward compatibility

  @IsString()
  @IsOptional()
  reassessment_range_id?: string; // Reassessment range ID

  @IsNumber()
  value: number;

  @IsString()
  @IsIn(['Active', 'Inactive'])
  status: string; // Status must be either 'Active' or 'Inactive'

  @IsNotEmpty()
  @IsString()
  property_type_class_id: string; // Changed to string
}
