import { toast } from "@/components/ui/use-toast";
import { UserRegisterObj } from "@/model/user-register";
import Api from "@/services/AuthServices";
import User<PERSON>ist<PERSON><PERSON> from "@/services/UserListServices";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

const fetchUsers = async () => {
  const response = await UserListApi.getAllUsers();
  const data = response.data;
  return data.data;
};

const createUser = async (userData: any) => {
  return new Promise((resolve, reject) => {
    Api.userRegister(userData, (response) => {
      console.log("response",response)
      if (response.status) {
        resolve(response.data);
      } else {
        reject(new Error(response.message || "Error creating user"));
      }
    });
  });
};


const updateUser = async ({ userId, userRegisterData }) => {
  return new Promise((resolve, reject) => {
    UserListApi.updateUser(userId, userRegisterData, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteUser = async (userId: string) => {
  return new Promise((resolve, reject) => {
    UserListApi.deleteUser(userId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useUserRegisterController = () => {
  const queryClient = useQueryClient();

  const { data: userData, isLoading: userLoading } = useQuery({
    queryKey: ["userlist"],
    queryFn: fetchUsers,
    staleTime: 10 * 60 * 2, // 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
  });

  // create user

  const createUserMutation = useMutation({
    mutationFn: createUser,
    onMutate: async (newZone) => {
      // Avoid optimistically updating the UI
      await queryClient.cancelQueries({ queryKey: ["userlist"] });
      const previousWards = queryClient.getQueryData(["userlist"]);
      return { previousWards };
    },
    onError: (err, newWard, context) => {
      // Ensure the error message is correctly extracted and displayed
      toast({
        title: err.message || "An error occurred while creating the user",
        variant: "destructive",
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["userlist"] });
    },
  });
  
  

  // update user
  const updateUserMutation = useMutation({
    mutationFn: updateUser,
    onMutate: async ({ userId, userRegisterData }) => {
      await queryClient.invalidateQueries({ queryKey: ["userlist"] });
      const previousUser = queryClient.getQueryData(["userlist"]);
      queryClient.setQueryData(["userlist"], (old: { user_id: any }[]) =>
        old.map((user: { user_id: any }) =>
          user.user_id === userId ? { ...user, ...userRegisterData } : user,
        ),
      );
      return { previousUser };
    },
    onError: (err, { userId, userRegisterData }, context) => {
      queryClient.setQueryData(["userlist"], context.previousUser);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["userlist"] });
    },
  });

  //Delete User
  const deleteUserMutation = useMutation({
    mutationFn: deleteUser,
    onMutate: async (userId) => {
      await queryClient.cancelQueries({ queryKey: ["userlist"] });

      const previousUsers = queryClient.getQueryData(["userlist"]);

      queryClient.setQueryData(["userlist"], (old) => {
        const updatedUsers = old.filter((user) => user.user_id !== userId);
        return updatedUsers;
      });
      return { previousUsers };
    },
    onError: (err, wardId, context) => {
      queryClient.setQueryData(["userlist"], context.previousUsers);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["userlist"] });
    },
  });

  return {
    userList: userData || [],
    userLoading,
    updateUser: updateUserMutation.mutate,
    createUser: createUserMutation.mutate,
    deleteUser: deleteUserMutation.mutate,
  };
};
