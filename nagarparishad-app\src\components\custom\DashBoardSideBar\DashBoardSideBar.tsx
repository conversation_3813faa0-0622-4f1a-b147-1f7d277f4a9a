import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import logo from "../../../assets/img/homepage/logo.png";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  CalendarCheck,
  Fence,
  LandPlot,
  LogOut,
  UserPlus,
  X,
  LayoutDashboard,
  IndianRupee,
  NotepadText,
  Files,
  ChevronDown,
  ChevronUp,
  Settings,
  BarChart,
  ClipboardList,
  CreditCard,
  History,
  BookOpen,
  HelpCircle,
FileSpreadsheet
} from "lucide-react";
import AuthController from "@/controller/AuthController";
import { cn } from "@/lib/utils";
import {
  usePermissions,
  useUpdatePermissions,
} from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";
import { useNotifications } from "@/context/NotificationContext";

interface SidebarProps {
  isopen?: boolean;
  toggle?: () => void;
  position: string;
}

const sidebarData = [
  {
    id: "1",
    name: "titles.dashboard",
    icon: <LayoutDashboard />,
    path: "/dashboard",
    permission: {
      module: ModuleName.Setting,
      form: FormName.DeprecationRate,
      action: Action.CanRead,
    },
  },
  {
    id: "3",
    name: "titles.userRegister",
    icon: <UserPlus />,
    path: "/dashboard/user-registration",
    permission: {
      module: ModuleName.RoleAndUserMangement,
      form: FormName.User,
      action: Action.CanRead,
    },
  },
  {
    id: "7",
    name: "titles.roleRegistration",
    icon: <CalendarCheck />,
    path: "/dashboard/user-role",
    permission: {
      module: ModuleName.RoleAndUserMangement,
      form: FormName.Role,
      action: Action.CanRead,
    },
  },
  {
    id: "19",
    name: "titles.determinationDepartment",
    icon: <Fence />,
    subSections: [
      {
        id: "sub-1",
        name: "titles.master",
        items: [
          {
            name: "master.masterZone",
            path: "/master/zone-master",
            permission: {
              module: ModuleName.Master,
              form: FormName.Zone,
              action: Action.CanRead,
            },
          },
          {
            name: "master.masterWard",
            path: "/master/ward-master",
            permission: {
              module: ModuleName.Master,
              form: FormName.Ward,
              action: Action.CanRead,
            },
          },
          {
            name: "master.masterStreet",
            path: "/master/street-master",
            permission: {
              module: ModuleName.Master,
              form: FormName.Road,
              action: Action.CanRead,
            },
          },
          {
            name: "master.masterPropertyType",
            path: "/master/propertytype-master",
            permission: {
              module: ModuleName.Master,
              form: FormName.PropertyType,
              action: Action.CanRead,
            },
          },
          {
            name: "master.masterUsage",
            path: "/master/usage-master",
            permission: {
              module: ModuleName.Master,
              form: FormName.Usage,
              action: Action.CanRead,
            },
          },
          {
            name: "master.masterUsageSub",
            path: "/master/usage-sub-master",
            permission: {
              module: ModuleName.Master,
              form: FormName.UsageSub,
              action: Action.CanRead,
            },
          },
          {
            name: "master.propertyclass",
            path: "/master/propertyclass-master",
            permission: {
              module: ModuleName.Master,
              form: FormName.PropertyTypeDepartment,
              action: Action.CanRead,
            },
          },
          {
            name: "master.floor",
            path: "/master/propertyfloor-master",
            permission: {
              module: ModuleName.Master,
              form: FormName.Floor,
              action: Action.CanRead,
            },
          },
          {
            name: "master.collector",
            path: "/master/collector-master",
            permission: {
              module: ModuleName.Master,
              form: FormName.Floor,
              action: Action.CanRead,
            },
          },
        ],
      },
      // {
      //   id: "sub-2",
      //   name: "titles.transaction",
      //   items: [],
      // },
    ],
  },
  {
    id: "2",
    name: "titles.propertyRegistration",
    icon: <LandPlot />,
    path: "/property",
    permission: {
      module: ModuleName.Property,
      form: FormName.Property,
      action: Action.CanRead,
    },
  },
  {
    id: "4",
    name: "titles.namuna8",
    icon: <ClipboardList />,
    path: "/namuna-eight",
    permission: {},
  },
  {
    id: "5",
    name: "titles.namuna9",
    icon: <BarChart />,
    path: "/namuna-nine",
    permission: {},
  },
  {
    id: "11",
    name: "titles.namuna10",
    icon: <CreditCard />,
    subItems: [
      {
        name: "titles.paymentBill",
        path: "/bill",
        permission: {
          module: ModuleName.Payment,
          form: FormName.PaymentBill,
          action: Action.CanRead,
        },
      },
      {
        name: "titles.paymentBillHistory",
        path: "/BillHistory",
        permission: {
          module: ModuleName.Payment,
          form: FormName.PaymentLogs,
          action: Action.CanRead,
        },
      },
    ],
  },
  // {
  //   id: "15",
  //   name: "titles.paymentBill",
  //   icon: <IndianRupee />,
  //   path: "/bill",
  // },
  // {
  //   id: "16",
  //   name: "titles.paymentBillHistory",
  //   icon: <History />,
  //   path: "/BillHistory",
  // },
  {
    id: "10",
    name: "रजिस्टर",
    icon: <Files />,
    subItems: [
      {
        name: "नमुना 8",
        path: "/register/namuna-eight-report",
        permission: {
          module: ModuleName.Register,
          form: FormName.RegisterReport,
          action: Action.CanRead,
        },
      },
      {
        name: "नमुना 9",
        path: "/register/namuna-nine-report",
        permission: {
          module: ModuleName.Register,
          form: FormName.RegisterReport,
          action: Action.CanRead,
        },
      },
    ],
  },
  {
    id: "20", // New ID for the Reports module
    name: "रिपोर्ट ", // Placeholder for translation key
    icon: <FileSpreadsheet />, // Using Files icon, can be changed
    path: "/reports",
    permission: {
      module: ModuleName.Register, // Assuming it falls under Register module for permissions
      form: FormName.RegisterReport, // Assuming it uses the same form permission as other reports
      action: Action.CanRead,
    },
  },
  {
    id: "8",
    name: "propertyModification",
    icon: <LandPlot />,
    path: "/property/ferfar",
    permission: {
      module: ModuleName.Property,
      form: FormName.PropertyMutation,
      action: Action.CanRead,
    },
  },
  {
    id: "9",
    name: "मालमता फोड",
    icon: <LandPlot />,
    path: "/property/property-fod",
    permission: {
      module: ModuleName.Property,
      form: FormName.PropertyDivision,
      action: Action.CanRead,
    },
  },
  {
    id: "6",
    name: "titles.taxGeneration",
    icon: <IndianRupee />,
    path: "/property/property-demand",
    permission: {},
  },

  {
    id: "14",
    name: "titles.settings",
    icon: <Settings />,
    subItems: [
      {
        name: "setting.depreciationRate",
        path: "/settings/master-depreciation-rate",
        permission: {
          module: ModuleName.Setting,
          form: FormName.DeprecationRate,
          action: Action.CanRead,
        },
      },
      {
        name: "setting.constructionRateTitle",
        path: "/settings/master-rr-construction-rate",
        permission: {
          module: ModuleName.Setting,
          form: FormName.RRConstructionRate,
          action: Action.CanRead,
        },
      },
      {
        name: "setting.RR-Rate",
        path: "/settings/master-rr-rate",
        permission: {
          module: ModuleName.Setting,
          form: FormName.RRRate,
          action: Action.CanRead,
        },
      },
      {
        name: "setting.TaxRate",
        path: "/settings/master-tax-rate",
        permission: {
          module: ModuleName.Setting,
          form: FormName.TaxRate,
          action: Action.CanRead,
        },
      },
      {
        name: "setting.WeightingRate",
        path: "/settings/master-weighting-rate",
        permission: {
          module: ModuleName.Setting,
          form: FormName.WeightingRate,
          action: Action.CanRead,
        },
      },
      {
        name: "setting.solidWasteRate",
        path: "/settings/master-solid-waste",
        permission: {
          module: ModuleName.Setting,
          form: FormName.GhanKachraRate,
          action: Action.CanRead,
        },
      },
      {
        name: "setting.bookMaster",
        path: "/settings/master-book",
        permission: {
          module: ModuleName.Setting,
          form: FormName.BookMaster,
          action: Action.CanRead,
        },
      },
      {
        name: "setting.financialYear",
        path: "/settings/master-financial-year",
        permission: {
          module: ModuleName.Setting,
          form: FormName.DeprecationRate,
          action: Action.CanRead,
        },
      },
      {
        name: "setting.reAssessment",
        path: "/settings/reassesment",
        permission: {
          module: ModuleName.Setting,
          form: FormName.DeprecationRate,
          action: Action.CanRead,
        },
      },
       
    ],
  },

  {
    id: "15",
    name: "मिळकत कर ",
    icon: <CalendarCheck />,
    path: "/property/generate-milkat-kar",
    permission: {},
  },
  {
    id: "16",
    name: "वार्षिक कर ",
    icon: <CalendarCheck />,
    path: "/property/generate-warshik-kar",
    permission: {},
  },
   
  {
    id: "17",
    name: "देयक अहवाल",
    icon: <BookOpen />,
    path: "/payment-dashboard",
    permission: {
      // No specific permissions needed as this should be accessible to all users
    },
  },
  // {
  //   id: "18",
  //   name: "User Guide",
  //   icon: <HelpCircle />,
  //   path: "/dashboard/user-guide",
  //   permission: {
  //     // No specific permissions needed as this should be accessible to all users
  //   },
  // },
];

const DashBoardSideBar: React.FC<SidebarProps> = ({
  isopen,
  toggle,
  position,
}) => {
  const { t } = useTranslation();
    const {disconnectSSE}=useNotifications();
  
  const { canPerformAction } = usePermissions();
  const navigate = useNavigate();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const { pathname } = useLocation();

  const { loading, permissions } = usePermissions();

  const [openSection, setOpenSection] = useState<string>("");
  const [openSubSections, setOpenSubSections] = useState<string[]>([]);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);

  useEffect(() => {
    const storedOpenSection = sessionStorage.getItem("openSection");
    const storedOpenSubSections = sessionStorage.getItem("openSubSections");
    const storedSelectedItem = sessionStorage.getItem("selectedItem");

    if (storedOpenSection) {
      setOpenSection(storedOpenSection);
    }

    if (storedOpenSubSections) {
      setOpenSubSections(JSON.parse(storedOpenSubSections));
    }

    if (storedSelectedItem) {
      setSelectedItem(storedSelectedItem);
    }
  }, []);

  useEffect(() => {
    setOpenSection("");
    setOpenSubSections([]);
    setSelectedItem(null);

    let isMatchFound = false;

    sidebarData.forEach((section) => {
      if (
        (section.path && pathname === section.path) ||
        (section.path === "/property" &&
          (pathname.startsWith("/property/property-view") ||
            pathname.startsWith("/property/property-registration"))) ||
        (section.path === "/dashboard/user-role" &&
          pathname.startsWith("/dashboard/create-role")) ||
        (section.path === "/property/property-demand" &&
          pathname.startsWith("/property/property-demand-view")) 

      ) {
        setOpenSection(section.id);
        setSelectedItem(section.path);
        isMatchFound = true;
        return;
      }

      if (section.subSections) {
        section.subSections.forEach((subSection) => {
          subSection.items.forEach((item) => {
            if (pathname.startsWith(item.path)) {
              setOpenSection(section.id);
              setOpenSubSections([subSection.id]);
              setSelectedItem(item.path);
              isMatchFound = true;
            }
          });
        });
      }

      if (section.subItems) {
        section.subItems.forEach((item) => {
          if (pathname.startsWith(item.path)) {
            setOpenSection(section.id);
            setSelectedItem(item.path);
            isMatchFound = true;
          }
        });
      }
    });

    if (isMatchFound) return;

    if (pathname.startsWith("/master/")) {
      const masterSection = sidebarData.find((section) =>
        section.subSections?.some((sub) => sub.id === "sub-1")
      );
      if (masterSection) {
        setOpenSection(masterSection.id);
        setOpenSubSections(["sub-1"]);
        setSelectedItem(pathname);
      }
      return;
    }

    if (pathname.startsWith("/register/")) {
      const registerSection = sidebarData.find((section) =>
        section.subItems?.some((item) => pathname.startsWith(item.path))
      );
      if (registerSection) {
        setOpenSection(registerSection.id);
        setSelectedItem(pathname);
      }
    }
  }, [pathname]);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node)
      ) {
        toggle && toggle();
      }
    };

    if (isopen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isopen, toggle]);

  const handleLinkClick = (item: string) => {
    setSelectedItem(item);
    sessionStorage.setItem("selectedItem", item);
  };

  const handleLogout = () => {
    // First clear local storage
    localStorage.removeItem("UserData");
    localStorage.removeItem("permissions");
    localStorage.removeItem("AccessToken");
    localStorage.removeItem("RefreshToken");
disconnectSSE();
    // Then call the API and navigate only once after completion
    AuthController.signOut((response) => {
      if (response.statusCode === 201) {

        navigate("/", { replace: true });
      } else {
        console.error("Failed to log out:", response);
        navigate("/", { replace: true });
      }
    });
  };

  const handleParentSection = (section: {
    id: string;
    subSections?: any[];
    subItems?: any[];
  }) => {
    if (section.subSections || section.subItems) {
      setOpenSection((prevSection) =>
        prevSection === section.id ? "" : section.id
      );
    } else {
      setOpenSection(section.id);
    }
  };

  const toggleSubSection = (section: string) => {
    setOpenSubSections((prevSubSections) =>
      prevSubSections.includes(section)
        ? prevSubSections.filter((sub) => sub !== section)
        : [...prevSubSections, section]
    );
    sessionStorage.setItem("openSubSections", JSON.stringify(openSubSections));
  };

  useEffect(() => {
    sessionStorage.setItem("openSection", openSection);
    sessionStorage.setItem("openSubSections", JSON.stringify(openSubSections));
  }, [openSection, openSubSections]);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    isopen && (
      <div
        ref={sidebarRef}
        className={`w-full min-w-[270px] max-w-[270px] ${
          position === "absolute" ? "absolute bg-white z-10 " : " "
        } `}
      >
        <div className="fixed flex flex-col h-[100dvh] min-w-[283px] max-w-[283px] overflow-y-auto pb-8  px-4 bg-white">
          <div>
            <div className="relative">
              <Link
                to={"/dashboard"}
                onClick={() => handleLinkClick("/dashboard")}
              >
                <div className="flex items-center space-x-2 mb-5 mt-[5px] max-md:mt-[25px]">
                  <div className="h-16 w-16 min-h-16 min-w-16">
                    <img src={logo} alt="Logo" className="w-full h-full" />
                  </div>
                  <span className="text-xl font-bold text-zinc-700 dark:text-white">
                    {t("HeaderSectionLogoname")}
                  </span>
                </div>
              </Link>
              {position === "absolute" && (
                <div
                  onClick={toggle}
                  className="absolute -top-5 -right-2 text-gray-500 cursor-pointer"
                >
                  <X />
                </div>
              )}
            </div>
            <div className="relative space-y-3 my-3">
              {sidebarData?.map((section) => {
                // Check if the section itself has permission
                let sectionHasOwnPermission = false;

                if (section.permission) {
                  // If permission object exists but is empty, set to true if it has subItems or subSections
                  if (Object.keys(section.permission).length === 0) {
                    sectionHasOwnPermission = true;
                  } else {
                    // Check permission using canPerformAction
                    sectionHasOwnPermission = canPerformAction(
                      section.permission.module,
                      section.permission.form,
                      section.permission.action
                    );
                  }
                }

                // Check for subItems or subSections
                let hasPermission = sectionHasOwnPermission;

                // For sections with subItems or subSections, check if any child has permission
                if (section.subItems) {
                  const hasSubItemPermission = section.subItems.some(
                    (item) =>
                      !item.permission || // If no permission specified, allow access
                      (item.permission &&
                        canPerformAction(
                          item.permission.module,
                          item.permission.form,
                          item.permission.action
                        ))
                  );
                  // Section is visible if it has its own permission OR if any subitem has permission
                  console.log(
                    "sectionHasOwnPermission",
                    sectionHasOwnPermission,
                    "hasSubItemPermission",
                    hasSubItemPermission
                  );

                  hasPermission =
                    sectionHasOwnPermission || hasSubItemPermission;
                } else if (section.subSections) {
                  const hasSubSectionPermission = section.subSections.some(
                    (subSection) =>
                      subSection.items.some(
                        (item) =>
                          !item.permission || // If no permission specified, allow access
                          (item.permission &&
                            canPerformAction(
                              item.permission.module,
                              item.permission.form,
                              item.permission.action
                            ))
                      )
                  );
                  console.log(
                    "sectionHasOwnPermission",
                    sectionHasOwnPermission,
                    "hasSubSectionPermission",
                    hasSubSectionPermission
                  );

                  // Section is visible if it has its own permission OR if any subsection item has permission
                  hasPermission =
                    sectionHasOwnPermission || hasSubSectionPermission;
                } else {
                  // For sections without children, use only the section's own permission
                  hasPermission = sectionHasOwnPermission;
                }

                // Skip rendering this section if it has no permission
                if (!hasPermission) {
                  return null;
                }

                return (
                  <div key={section.id} className="w-full">
                    <Link to={section.path || "#"}>
                      <button
                        className={`w-full font-Poppins text-lg px-3 py-2 rounded-lg flex items-center justify-start space-x-2 duration-300 ${
                          openSection === section.id
                            ? "bg-[#2c93d2] text-white shadow-md"
                            : "bg-white text-black"
                        }`}
                        onClick={() => handleParentSection(section)}
                      >
                        {section.icon}
                        <span className="font-semibold text-[17px] text-nowrap">
                          {t(section.name)}
                        </span>
                        {(section.subSections || section.subItems) && (
                          <span className="!ml-auto">
                            {openSection === section.id ? (
                              <ChevronUp className="w-5 h-5 transition-transform duration-300" />
                            ) : (
                              <ChevronDown className="w-5 h-5 transition-transform duration-300" />
                            )}
                          </span>
                        )}
                      </button>
                    </Link>
                    {section.subSections &&
                      openSection === section.id &&
                      section.subSections.map((subSection) => {
                        // Check if this sub-section has any accessible items
                        const hasAccessibleItems = subSection.items.some(
                          (item) =>
                            !item.permission || // If no permission specified, allow access
                            (item.permission &&
                              canPerformAction(
                                item.permission.module,
                                item.permission.form,
                                item.permission.action
                              ))
                        );

                        // Only render sub-section if it has accessible items
                        return hasAccessibleItems ? (
                          <div key={subSection.id} className="w-full pl-3 py-2">
                            <button
                              className={`w-full flex items-center justify-between text-left text-zinc-700 dark:text-white py-2 px-4 bg-white dark:bg-zinc-700 rounded-lg shadow ${
                                openSubSections.includes(subSection.id) &&
                                "bg-gray-100"
                              }`}
                              onClick={() => toggleSubSection(subSection.id)}
                            >
                              <span className="font-semibold text-[17px]">
                                {t(subSection.name)}
                              </span>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                strokeWidth="1.5"
                                stroke="currentColor"
                                className={`w-6 h-6 transition-transform duration-300 ${
                                  openSubSections.includes(subSection.id)
                                    ? "transform rotate-90"
                                    : ""
                                }`}
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M12 16.5l6-6m0 0l-6-6m6 6H6"
                                />
                              </svg>
                            </button>
                            {openSubSections.includes(subSection.id) && (
                              <ul className="my-4 space-y-3 pl-7 text-zinc-600 dark:text-zinc-300 list-disc">
                                {subSection.items.map((item) => {
                                  const itemHasPermission =
                                    !item.permission || // If no permission specified, allow access
                                    (item.permission &&
                                      canPerformAction(
                                        item.permission.module,
                                        item.permission.form,
                                        item.permission.action
                                      ));

                                  return (
                                    itemHasPermission && (
                                      <li
                                        key={item.path}
                                        className={`font-semibold text-[17px] mt-[6px] ${
                                          selectedItem === item.path
                                            ? "text-BlueText font-extrabold"
                                            : ""
                                        }`}
                                      >
                                        <Link
                                          to={item.path}
                                          onClick={() =>
                                            handleLinkClick(item.path)
                                          }
                                        >
                                          {t(item.name)}
                                        </Link>
                                      </li>
                                    )
                                  );
                                })}
                              </ul>
                            )}
                          </div>
                        ) : null;
                      })}

                    {section.subItems &&
                      openSection === section.id &&
                      section.subItems.map((item) => {
                        const itemHasPermission =
                          !item.permission || // If no permission specified, allow access
                          (item.permission &&
                            canPerformAction(
                              item.permission.module,
                              item.permission.form,
                              item.permission.action
                            ));

                        return (
                          itemHasPermission && (
                            <div
                              key={item.path}
                              className={`font-semibold text-[17px] mt-[6px] pl-7  flex items-center ${
                                selectedItem === item.path
                                  ? "text-BlueText font-extrabold"
                                  : "text-zinc-600"
                              }`}
                            >
                              <span className="mr-2 text-black-700 text-2xl">
                                •
                              </span>
                              <Link
                                to={item.path}
                                onClick={() => handleLinkClick(item.path)}
                                className="flex items-center"
                              >
                                {t(item.name)}
                              </Link>
                            </div>
                          )
                        );
                      })}
                  </div>
                );
              })}
            </div>
          </div>
          <div className="mt-auto">
            <button
              onClick={handleLogout}
              className={cn(
                "w-full text-left text-red-600 dark:text-red-400 py-2 px-4 bg-white dark:bg-zinc-700 rounded-lg shadow border-red-200 border-2",
                "hover:bg-red-500 hover:text-white  duration-300"
              )}
            >
              <LogOut className="w-5 h-5 inline mr-2 " />
              {t("Logout")}
            </button>
          </div>
        </div>
      </div>
    )
  );
};

export default DashBoardSideBar;
