import React, { useContext, useEffect, useState } from "react";
import { But<PERSON> } from "../ui/button";
import { useTranslation } from "react-i18next";
import { MASTER, PROPERTY } from "@/constant/config/api.config";
import { GlobalContext } from "@/context/GlobalContext";
import ZoneMasterForm from "@/components/forms/ZoneMasterForm";
import LocationMasterForm from "../forms/LocationMasterForm";
import WardPopupForm from "../forms/WardPopupForm";
import StreetMasterForm from "../forms/StreetMasterForm";
import AreaMasterForm from "../forms/AreaMasterForm/AreaMasterForm";
import PropertytypeMasterForm from "../forms/PropertytypeMasterForm";
import PropertysubtypeMasterForm from "../forms/PropertysubtypeMasterForm";
import ElectionBoundaryMasterPopupForm from "../forms/ElectionBoundaryMasterPopupForm";
import UsageMasterForm from "../forms/UsageMasterForm";
import UsageSubMasterForm from "../forms/UsageSubMasterForm";
import ConstructionMasterForm from "../forms/ConstructionMasterForm";
import AdminstrativeBoundryMasterForm from "../forms/AdministrativeBoundaryMasterForm";
import { useNavigate } from "react-router-dom";
import { PropertyContext } from "@/context/PropertyContext";
import ReadyRecknerRateForm from "../forms/ReadyRecknerRateForm";
import PropertyTaxRateForm from "../forms/PropertyTaxRateForm";
import UserRegisterForm from "../forms/UserRegisterForm";
import { cn } from "@/lib/utils";
import { BtnVariant } from "./tanstacktable";
import PropertyClassMasterForm from "../forms/PropertyClassMasterForm";
import PropertyFloorMasterForm from "../forms/PropertyFloorMasterForm copy";

interface AddNewFormInterface {
  masterType: string;
  displayComponent?: string;
  btnVariant?:BtnVariant;
  handleClick?:()=>void
}
const AddNewForm = ({
  masterType,
  displayComponent,
  btnVariant,
  handleClick,
  ...props
}: AddNewFormInterface) => {
  const { t } = useTranslation();
  const {
    setOpen,
    masterComponent,
    setMasterComponent,
    refreshZoneList,
    refreshLocationList,
    refreshBoundaryList,
    refreshUsageList,
    refreshUsageSubList,
    refreshConstructionClassList,
    refreshAreaList,
    refreshPropertyList,
    refreshPropertysubtypeList,
    refreshStreetList,
    refreshadministrativeBoundaryList,
    refreshWardList,
  } = useContext<any>(GlobalContext);
  const {
    initialPropertyInformation,
    setPropertyInformation,
    setPropertyNumber,
  } = useContext(PropertyContext);
  const [btnTitle, setBtnTitle] = useState<string>("");
  const [refresh, setrefresh] = useState<boolean>(false);
  const navigate = useNavigate();

  const handleDialogIntegration = (masterType: string) => {
    setOpen(true);
    if (masterType === PROPERTY.PROPERTY) {
      setPropertyInformation(initialPropertyInformation);
      setPropertyNumber("");
      navigate("/property/property-registration");
    }
  };

  useEffect(() => {
    const varName: string = masterType.toUpperCase();
    if (masterType === MASTER[varName]) {
      setBtnTitle(`${masterType.toLowerCase()}.AddBtn`);
    } else if (masterType === PROPERTY.PROPERTY) {
      setBtnTitle(`${masterType.toLowerCase()}.AddBtn`);
    }
    if (masterType === MASTER.ZONE) {
      setMasterComponent(<ZoneMasterForm btnTitle={btnTitle} />);
    } else if (masterType === MASTER.WARD) {
      setMasterComponent(<WardPopupForm btnTitle={btnTitle} />);
    } else if (masterType === MASTER.LOCATION) {
      setMasterComponent(<LocationMasterForm btnTitle={btnTitle} />);
    } else if (masterType === MASTER.STREET) {
      setMasterComponent(<StreetMasterForm btnTitle={btnTitle} />);
    }  else if (masterType === MASTER.PROPERTYCLASS) {
      setMasterComponent(<PropertyClassMasterForm btnTitle={btnTitle} />);
    } 
      else if (masterType === MASTER.PROPERTYFLOOR) {
      setMasterComponent(<PropertyFloorMasterForm btnTitle={btnTitle} />);
    } 
    else if (masterType === MASTER.AREAORLOCALITY) {
      setMasterComponent(<AreaMasterForm btnTitle={btnTitle} />);
    } else if (masterType === MASTER.PROPERTYTYPE) {
      setMasterComponent(<PropertytypeMasterForm btnTitle={btnTitle} />);
    } else if (masterType === MASTER.PROPERTYSUBTYPE) {
      setMasterComponent(<PropertysubtypeMasterForm btnTitle={btnTitle} />);
    } else if (masterType === MASTER.ELECTIONBOUNDRY) {
      setMasterComponent(
        <ElectionBoundaryMasterPopupForm btnTitle={btnTitle} />,
      );
    } else if (masterType === MASTER.USAGE) {
      setMasterComponent(<UsageMasterForm btnTitle={btnTitle} />);
    } else if (masterType === MASTER.USAGE_SUB) {
      setMasterComponent(<UsageSubMasterForm btnTitle={btnTitle} />);
    } else if (masterType === MASTER.CONSTRUCTION) {
      setMasterComponent(<ConstructionMasterForm btnTitle={btnTitle} />);
    } else if (masterType === MASTER.ADMINISTRATIVEBOUNDARY) {
      setMasterComponent(
        <AdminstrativeBoundryMasterForm btnTitle={btnTitle} />,
      );
    } else if (masterType === MASTER.READYRECKONERRATE) {
      setMasterComponent(<ReadyRecknerRateForm btnTitle={btnTitle} />);
    } else if (masterType === MASTER.PROPERTYTAXRATE) {
      setMasterComponent(<PropertyTaxRateForm btnTitle={btnTitle} />);
    } else if (masterType === MASTER.USERLIST) {
      setMasterComponent(<UserRegisterForm btnTitle={btnTitle} />);
    }
    // Add additional conditions as needed
  }, [
    masterType,
    btnTitle,
    t,
    refresh,
    refreshZoneList,
    refreshLocationList,
    refreshBoundaryList,
    refreshUsageList,
    refreshUsageSubList,
    refreshConstructionClassList,
    refreshAreaList,
    refreshPropertyList,
    refreshPropertysubtypeList,
    refreshStreetList,
    refreshadministrativeBoundaryList,
    refreshWardList,
  ]);

  const handletoggle = () => {
    setrefresh((pre) => !pre);
  };

  return (
    <>
      {displayComponent === "table" ? (
        masterType === PROPERTY.PROPERTY ? (
          <Button
          variant={btnVariant}
          className={cn("w-8/14 ml-auto")}
          onClick={() => {
            if (typeof handleClick === 'function') {
              console.log("uodate sstatus in fucntion")
              handleClick(); // Call handleClick only if it exists
            }
            handleDialogIntegration(masterType); // Always call this
            // Check if handleClick is a function before calling it
           
          }}
        >
            {t(btnTitle)} 
          </Button>
        ) : (
          <Button
            variant="outline"
            className="w-8/14 ml-auto"
            onClick={handletoggle}
          >
            {t(btnTitle)}
          </Button>
        )
      ) : masterType === PROPERTY.PROPERTY ? (
        ""
      ) : (
        masterComponent
      )}
    </>
  );
};

export default AddNewForm;
