import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Logs } from 'libs/database/entities';
import { LogsRepository } from 'libs/database/repositories';

export const log_type = {
  SUCCESS: 'SUCCESS',
  ERROR: 'ERROR',
  INFO: 'INFO',
};

@Injectable()
export class LogsService {
  constructor(
    @InjectRepository(Logs)
    // private readonly logsRepository: Repository<Logs>,
    private readonly logsRepository: LogsRepository,
  ) {}

  // Method to create a new log entry
  async logAction(logDetails: Partial<Logs>): Promise<Logs> {
    const logEntry = this.logsRepository.create(logDetails);
    return await this.logsRepository.save(logEntry);
  }

  // // Method to list logs by filters
  // async getLogs(filters: { logType?: string; logSubType?: string }): Promise<Logs[]> {
  //   const query = this.logsRepository.createQueryBuilder('logs');

  //   // Apply logType filter if provided
  //   if (filters.logType) {
  //     query.andWhere('logs.logType = :logType', { logType: filters.logType });
  //   }

  //   // Apply logSubType filter if provided
  //   if (filters.logSubType) {
  //     query.andWhere('logs.logSubType = :logSubType', { logSubType: filters.logSubType });
  //   }

  //   // Execute and return the filtered logs
  //   return await query.getMany();
  // }

  // Fetch logs by log type
  async getLogsByType(logType: string): Promise<Logs[]> {
    // Fetch logs with optional logType filter
    if (logType) {
      return await this.logsRepository.find({ where: { logType } });
    }
    return await this.logsRepository.find();
  }

  // Method to list logs by filters with optional date range
  async getLogs(filters: { logType?: string; logSubType?: string; fromDate?: Date; toDate?: Date }): Promise<Logs[]> {
    const query = this.logsRepository.createQueryBuilder('logs');

    // Apply logType filter if provided
    if (filters.logType) {
      query.andWhere('logs.logType = :logType', { logType: filters.logType });
    }

    // Apply logSubType filter if provided
    if (filters.logSubType) {
      query.andWhere('logs.logSubType = :logSubType', { logSubType: filters.logSubType });
    }

    // Apply date range filter if both fromDate and toDate are provided
    if (filters.fromDate && filters.toDate) {
      query.andWhere('logs.createdAt BETWEEN :fromDate AND :toDate', {
        fromDate: filters.fromDate,
        toDate: filters.toDate,
      });
    } else if (filters.fromDate) {
      // If only fromDate is provided, filter logs created after fromDate
      query.andWhere('logs.createdAt >= :fromDate', { fromDate: filters.fromDate });
    } else if (filters.toDate) {
      // If only toDate is provided, filter logs created before toDate
      query.andWhere('logs.createdAt <= :toDate', { toDate: filters.toDate });
    }

    // Execute and return the filtered logs
    return await query.getMany();
  }

  
}
