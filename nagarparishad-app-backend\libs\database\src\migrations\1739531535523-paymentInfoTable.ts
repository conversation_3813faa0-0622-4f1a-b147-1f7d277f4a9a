import { MigrationInterface, QueryRunner } from "typeorm";

export class PaymentInfoTable1739531535523 implements MigrationInterface {
    name = 'PaymentInfoTable1739531535523'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."payment_info_payment_status_enum" AS ENUM('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED')`);
        await queryRunner.query(`CREATE TABLE "payment_info" ("payment_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "amount" numeric(10,2) NOT NULL, "payment_mode" character varying(50) NOT NULL, "transaction_id" character varying(50), "payment_status" "public"."payment_info_payment_status_enum" NOT NULL DEFAULT 'PENDING', "payment_date" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, CONSTRAINT "PK_1004b91420f1dbcd512ee723452" PRIMARY KEY ("payment_id"))`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c" UNIQUE ("billNo")`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP DEFAULT`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(50) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" DROP NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_90614cac26fb49598421ebfed20" FOREIGN KEY ("propertyType_id") REFERENCES "property_type_master"("propertyType_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "FK_df738d1108e8291026728e28c55" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_info" ADD CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "master_tax_rate" ADD CONSTRAINT "FK_ba03f0f1cab042ed390aa18efda" FOREIGN KEY ("propertyType_id") REFERENCES "property_type_master"("propertyType_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "master_tax_rate" DROP CONSTRAINT "FK_ba03f0f1cab042ed390aa18efda"`);
        await queryRunner.query(`ALTER TABLE "payment_info" DROP CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9"`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "FK_df738d1108e8291026728e28c55"`);
        // await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_90614cac26fb49598421ebfed20"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(100) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET DEFAULT now()`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c"`);
        await queryRunner.query(`DROP TABLE "payment_info"`);
        await queryRunner.query(`DROP TYPE "public"."payment_info_payment_status_enum"`);
    }

}
