// src/weighting-master/dto/create-weighting.dto.ts
import { IsString, IsNumber, IsUUID, IsNotEmpty, IsIn, IsOptional } from 'class-validator';

export class CreateWeightingDto {
  @IsString()
  @IsOptional()
  financial_year?: string; // Optional for backward compatibility

  @IsString()
  @IsOptional()
  reassessment_range_id?: string; // Reassessment range ID

  @IsNumber()
  @IsNotEmpty()
  value: number; // Required weighting value

  @IsString()
  @IsIn(['Active', 'Inactive'])
  status: string; // Must be 'active' or 'inactive'

  @IsUUID()
  @IsNotEmpty()
  usage_type_id: string; // Required usage type ID
}
