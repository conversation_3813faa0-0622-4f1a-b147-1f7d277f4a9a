import { Injectable, NotFoundException } from '@nestjs/common';
import { GlobalSearchDto } from './dto/gloabl_serch.dto'; // Adjust the import path as necessary
import { PropertyMasterRepository } from 'libs/database/repositories';

@Injectable()
export class GlobalSearchService {
  constructor(private readonly propertyMasterRepository: PropertyMasterRepository) {}

  async checkPropertyDetails(publicUserDto: GlobalSearchDto) {
    try {
      const data = await this.propertyMasterRepository.getGloabalSerchData(publicUserDto);

      if (!data) {
        throw new NotFoundException('No data found matching the criteria.');
      }

      return {
        message: 'Data Fetched Successfully',
        data: data,
      };
    } catch (error) {
      throw error; // You might want to handle specific errors here.
    }
  }
}
