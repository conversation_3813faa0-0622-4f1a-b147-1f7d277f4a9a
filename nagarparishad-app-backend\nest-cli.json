{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "assets": [{"include": "templates/**/*", "outDir": "dist"}, {"include": "../images/**/*", "outDir": "dist"}], "watchAssets": true}, "projects": {"database": {"type": "library", "root": "libs/database", "entryFile": "index", "sourceRoot": "libs/database/src", "compilerOptions": {"tsConfigPath": "libs/database/tsconfig.lib.json"}}, "helpers": {"type": "library", "root": "libs/helpers", "entryFile": "index", "sourceRoot": "libs/helpers/src", "compilerOptions": {"tsConfigPath": "libs/helpers/tsconfig.lib.json"}}, "jwt-auth": {"type": "library", "root": "libs/jwt-auth", "entryFile": "index", "sourceRoot": "libs/jwt-auth/src", "compilerOptions": {"tsConfigPath": "libs/jwt-auth/tsconfig.lib.json"}}}}