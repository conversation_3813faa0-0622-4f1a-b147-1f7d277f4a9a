import { Repository } from 'typeorm';
import { MilkatKarTaxEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class MilkatKarTaxeRepository extends Repository<MilkatKarTaxEntity> {
  constructor(
    @InjectRepository(MilkatKarTaxEntity)
    private readonly milkatKarTaxRepository: Repository<MilkatKarTaxEntity>,
  ) {
    super(
      milkatKarTaxRepository.target,
      milkatKarTaxRepository.manager,
      milkatKarTaxRepository.queryRunner,
    );
  }

  async getDataById(id: string) {
    // return await this.milkatKarTaxRepository.find({
    //   where: {
    //     MilkatKar: {
    //       milkatKar_id: id,
    //     }
    //   },
    //   relations: ['property', 'property_usage_details']
    // })
    return await this.milkatKarTaxRepository
      .createQueryBuilder('milkatKarTax')
      .leftJoinAndSelect('milkatKarTax.property', 'property')
      .leftJoinAndSelect(
        'milkatKarTax.property_usage_details',
        'property_usage_details',
      )
      .select([
        'milkatKarTax',
        'property.property_id',
        'property_usage_details.property_usage_details_id',
      ])
      .where('milkatKarTax.MilkatKar = :milkatKarId', { milkatKarId: id })
      .getMany();
  }

  async getDataInfoByTaxId(id: string) {
    return await this.milkatKarTaxRepository
      .createQueryBuilder('milkatKarTax')
      .leftJoinAndSelect(
        'milkatKarTax.property_usage_details',
        'property_usage_details',
      )
      .select([
        'milkatKarTax',
        'property_usage_details.property_usage_details_id',
      ])
      .where('milkatKarTax.milkatKartax_id = :milkatKartax_id', { milkatKartax_id: id })
      .getOne();
  }

  async getAll() {
    return await this.milkatKarTaxRepository
      .createQueryBuilder('milkatKarTax')
      .leftJoinAndSelect(
        'milkatKarTax.property_usage_details',
        'property_usage_details'
      )
      .select([
        'milkatKarTax',
        'property_usage_details.property_usage_details_id',
      ])
      .getMany(); 
  }  

  
}
