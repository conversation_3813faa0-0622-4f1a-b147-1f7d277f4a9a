import React, { useState } from "react";
import WhiteContainer from "../WhiteContainer";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import axios from "axios";
import { useTranslation } from "react-i18next";
import { Loader } from "@/components/globalcomponent/Loader";
import { Label } from "@/components/ui/label";

const GisCompare = () => {
  const [searchType, setSearchType] = useState("propertyNumber");
  const [propertyNumbers, setPropertyNumbers] = useState({
    property1: "",
    property2: "",
    property3: "",
  });
  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;

  const [uploadLoading, setUploadLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [images, setImages] = useState([]);
  const { t } = useTranslation();

  const handleInputChange = (e: { target: { name: any; value: any; }; }) => {
    const { name, value } = e.target;
    setPropertyNumbers((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleSearch = async () => {
    const { property1, property2, property3 } = propertyNumbers;

    const queryParams = new URLSearchParams();
    if (property1) queryParams.append("property1", property1);
    if (property2) queryParams.append("property2", property2);
    if (property3) queryParams.append("property3", property3);
    setLoading(true);

    setUploadLoading(false)
    setError("");
    try {
      const res = await axios.get(
        `${apiBaseUrl}/v1/import-module/getImagesCompare?${queryParams.toString()}`
      );

      // console.log("Response", res.data.data)

      if (res.status === 200 && res.data && res.data.data) {
        setImages(res.data.data);
        console.log(res.data.data);

      } else {
        setError("Failed to fetch images. Please try again.");
      }
    } catch (error) {
      console.error("Error fetching images:", error);
      setError("An error occurred while fetching images. Please try again.");
    } finally {
      setUploadLoading(true);
      setLoading(false);
    }
  };

  // const handleDownload = () => {
  //   const link = document.createElement('a');
  //   link.href = `/file:///D:/upload/${images}`; // Adjust to the correct server path
  //   link.download = `${images}`; // Suggest a filename
  //   document.body.appendChild(link);
  //   link.click();
  //   document.body.removeChild(link);
  // };



  // const handleDownload = () => {
  //   Object.values(images).forEach((image) => {
  //     const link = document.createElement('a');
  //     link.href = `/file:///D:/upload/${image.property_images}`; // Adjust to the correct server path
  //     link.download = `${image.property_images}`; // Suggest a filename
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //   });
  // };


  

  return (
    <div className="flex h-fit">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold font-Poppins   w-full ml-1 ">
          GIS Compare
        </h1>
        <WhiteContainer>
          <div className="mb-2">
            <div className="grid md:grid-cols-5 gap-x-3">
              <div className="grid-cols-subgrid">
                <Label>{t("namunaEight.IncomeNumber")}</Label>
                <Select onValueChange={setSearchType} value={searchType}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select search type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value="propertyNumber">
                        Property Number
                      </SelectItem>
                      <SelectItem value="oldPropertyNumber">
                        Old Property Number
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid-cols-subgrid">
                <Label>मालमत्ता 1</Label>
                <Input
                  className="mt-1 block w-full"
                  transliterate={false}
                  placeholder="Property 1"
                  name="property1"
                  value={propertyNumbers.property1}
                  onChange={handleInputChange}
                />
              </div>
              <div className="grid-cols-subgrid">
                <Label>मालमत्ता 2</Label>
                <Input
                  className="mt-1 block w-full"
                  transliterate={false}
                  placeholder="Property 2"
                  name="property2"
                  value={propertyNumbers.property2}
                  onChange={handleInputChange}
                />
              </div>
              <div className="grid-cols-subgrid">
                <Label>मालमत्ता 3</Label>
                <Input
                  className="mt-1 block w-full"
                  transliterate={false}
                  placeholder="Property 3"
                  name="property3"
                  value={propertyNumbers.property3}
                  onChange={handleInputChange}
                />
              </div>
              <div className="grid-cols-subgrid flex items-end">
                <Button
                  // className="w-1/2"
                  variant="submit"
                  disabled={!propertyNumbers.property1 && !propertyNumbers.property2 && !propertyNumbers.property3}
                  onClick={handleSearch}
                >
                  शोधा
                </Button>

              </div>
            </div>
          </div>
          <p className="text-xs italic font-semibold mb-0 mt-2 text-[#3c3c3c]"> {t('selectAnyFieldNote')} </p>
        </WhiteContainer>
        {error && (
          <div className="text-red-500 text-sm mt-3">
            {error}
          </div>
        )}
        {loading && (
          <div className="flex justify-center items-center my-4">
            <Loader />
          </div>

        )}
        {(!loading && images.length > 0) &&

          <WhiteContainer>
            <div className="mt-6 w-full py-6 px-3 grid grid-cols-3 gap-3">
              {images.map((property, index) => (
                <div
                  key={index}
                  className="pr-3 mr-3 w-full border border-blue-100 rounded-lg shadow p-4"
                >
                  <div className="w-[20px] h-[20px] rounded-full flex justify-center items-center text-[11px] bg-blue-200 text-white mb-4  ">
                    <p>
                      {index + 1}
                    </p>
                  </div>
                  <p className="font-semibold">
                    Owner Name: {property.owner_name}
                  </p>
                  <p className=" font-semibold">
                    Property Number: {property.property_number}
                  </p>
                  <p className=" font-semibold">
                    Old Property Number: {property.old_property_no}
                  </p>
                  <p className=" font-semibold">
                    Property Address: {property.property_address}
                  </p>

                  <p className="mb-4 font-semibold">
                    Image Name: {property.property_images}
                  </p>


                  <div className="h-fit overflow-auto">
                    <div className="grid gap-4">
                      {property.images.map((image, imgIndex) => (
                        <img
                          key={imgIndex}
                          src={image}
                          className="w-full h-auto border-2 border-gray-200 rounded aspect-square"
                          alt={`Property ${index + 1} - ${imgIndex + 1}`}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </WhiteContainer>
        }
      </div>
    </div>
  );
};

export default GisCompare;
