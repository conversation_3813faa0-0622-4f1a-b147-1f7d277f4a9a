import React from "react";
import { useTranslation } from "react-i18next";
import BreadCrumb from "@/components/custom/BreadCrumb";

const PrivacyPolicy: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <>
      <BreadCrumb className={"bg-blue-100"} />
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <h1 className="text-3xl font-bold mb-6 text-Primary">{t("privacyPolicy")}</h1>
        
        <div className="space-y-6 text-gray-700">
          <section>
            <h2 className="text-xl font-semibold mb-3 text-Primary">{t("privacyIntroTitle")}</h2>
            <p className="mb-2">
              {t("privacyIntroText")}
            </p>
          </section>
          
          <section>
            <h2 className="text-xl font-semibold mb-3 text-Primary">{t("privacyInfoCollectedTitle")}</h2>
            <ul className="list-disc pl-5 space-y-2">
              <li>{t("privacyInfoCollected1")}</li>
              <li>{t("privacyInfoCollected2")}</li>
              <li>{t("privacyInfoCollected3")}</li>
              <li>{t("privacyInfoCollected4")}</li>
            </ul>
          </section>
          
          <section>
            <h2 className="text-xl font-semibold mb-3 text-Primary">{t("privacyInfoUseTitle")}</h2>
            <ul className="list-disc pl-5 space-y-2">
              <li>{t("privacyInfoUse1")}</li>
              <li>{t("privacyInfoUse2")}</li>
              <li>{t("privacyInfoUse3")}</li>
            </ul>
          </section>
          
          <section>
            <h2 className="text-xl font-semibold mb-3 text-Primary">{t("privacyDataSecurityTitle")}</h2>
            <p className="mb-2">
              {t("privacyDataSecurityText")}
            </p>
          </section>
          
          <section>
            <h2 className="text-xl font-semibold mb-3 text-Primary">{t("privacyThirdPartyTitle")}</h2>
            <p className="mb-2">
              {t("privacyThirdPartyText")}
            </p>
          </section>
          
          <section>
            <h2 className="text-xl font-semibold mb-3 text-Primary">{t("privacyChangesTitle")}</h2>
            <p className="mb-2">
              {t("privacyChangesText")}
            </p>
          </section>
          
          <section>
            <h2 className="text-xl font-semibold mb-3 text-Primary">{t("privacyContactTitle")}</h2>
            <p className="mb-2">
              {t("privacyContactText")} <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
            </p>
          </section>
        </div>
      </div>
    </>
  );
};

export default PrivacyPolicy;