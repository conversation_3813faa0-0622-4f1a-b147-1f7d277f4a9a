export interface UsageSubObjectInterface {
  usageSub_id: string; // Change this to match the response field
  subUsage: string;
  usage: string | null; // Assuming this is the field corresponding to 'usage'
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string | null;
}

export interface UsageSubMasterApiResponse {
  statusCode?: number;
  message: string;
  data?: UsageSubObjectInterface[] | null;
}

export interface UsageSubObject {
  usageSub_id: string; // Update the naming convention
  subUsage: string;
  usage: string | null;
}

export interface UsageSubCreateObject {
  subUsage: string;
  usage: string;
}

export interface UsageSubCreateResObject {
  statusCode: number;
  message: string;
}

export interface UsageSubUpdateObject {
  subUsage: string;
  usage: string;
}

export interface UsageSubUpdateApiResponse {
  statusCode: number;
  message: string;
}
