import { Repository } from 'typeorm';
import { Master_rr_construction_rate, ReassessmentRange } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { PropertyTypeClassMasterRepository } from './property_type_classMaster.repository';

export class Master_rr_construction_rateRepository extends Repository<Master_rr_construction_rate> {
  constructor(
    @InjectRepository(Master_rr_construction_rate)
    private readonly master_rr_construction_rate: Repository<Master_rr_construction_rate>,
    private readonly propertyTypeClassMasterRepository: PropertyTypeClassMasterRepository,
  ) {
    super(
      master_rr_construction_rate.target,
      master_rr_construction_rate.manager,
      master_rr_construction_rate.queryRunner,
    );
  }
  async findWithPropertyClass(): Promise<Master_rr_construction_rate[]> {
    return this.master_rr_construction_rate.find({
      relations: ['property_type_class_id','reassessmentRange'], // Include property class relation
    });
  }

  async updateConstructionRate(
    id: string,
    data: any,
  ): Promise<{ message: string; data: Master_rr_construction_rate }> {
    // Check if the record exists
    const existingRecord = await this.master_rr_construction_rate.findOne({
      where: { rr_construction_rate_id: id }, // Ensure to match the correct ID field
      relations: ['property_type_class_id', 'reassessmentRange'], // Ensure the relation is fetched
    });

    if (!existingRecord) {
      return {
        message: 'No record found to update',
        data: undefined,
      };
    }

    // Check if the zone_id is provided in the data
    if (data.property_type_class_id) {
      const propertyTypeClass =
        await this.propertyTypeClassMasterRepository.findById(
          data.property_type_class_id,
        );
      if (propertyTypeClass) {
        existingRecord.property_type_class_id = propertyTypeClass; // Assign the fetched zone to the existing record
      } else {
        return {
          message: 'propertyTypeClass not found',
          data: undefined,
        };
      }
    }

    // Handle reassessment range if provided
    if (data.reassessment_range_id) {
      const reassessmentRange = await this.master_rr_construction_rate.manager
        .getRepository(ReassessmentRange)
        .findOne({
          where: { reassessment_range_id: data.reassessment_range_id }
        });
      if (reassessmentRange) {
        existingRecord.reassessmentRange = reassessmentRange;
      }
    }

    // Update the properties of the existing record
    Object.assign(existingRecord, data);

    // Save the updated record back to the database
    await this.master_rr_construction_rate.save(existingRecord);

    return {
      message: 'Construction rate updated successfully',
      data: existingRecord,
    };
  }

  async generateForNewFinancialYear(newFinancialYear: string, currentReassessmentRange: ReassessmentRange): Promise<{ message: string; data?: Master_rr_construction_rate[] }> {
    // Fetch all existing records
    const existingRecords = await this.findWithPropertyClass();

    if (existingRecords.length === 0) {
      return {
        message: 'No records found to duplicate',
      };
    }

    const duplicatedRecords: Master_rr_construction_rate[] = [];

    for (const record of existingRecords) {
      // Create a new record based on the existing one
      const newRecord = new Master_rr_construction_rate();
      newRecord.financial_year = newFinancialYear; // Keep for backward compatibility
      newRecord.value = record.value;
      newRecord.status = record.status;
      newRecord.property_type_class_id = record.property_type_class_id;
      newRecord.reassessmentRange = currentReassessmentRange;

      // Save the new record to the database
      const savedRecord = await this.master_rr_construction_rate.save(newRecord);
      duplicatedRecords.push(savedRecord);
    }

    return {
      message: 'Records generated successfully for the new reassessment range',
      data: duplicatedRecords,
    };
  }

}
