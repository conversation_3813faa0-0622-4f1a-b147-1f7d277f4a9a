import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsOptional,
  IsPositive,
  IsInt,
  IsObject,
  IsString,
  IsEnum,
  IsArray,
  IsBoolean,
} from 'class-validator';

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class PaginationDto {
  @ApiProperty({
    description: 'Number of items to retrieve',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsPositive()
  @IsInt()
  @Transform(({ value }) => parseInt(value, 10))
  limit?: number;

  @ApiProperty({
    description: 'Page number to retrieve',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsPositive()
  @IsInt()
  @Transform(({ value }) => parseInt(value, 10))
  page?: number;

  @ApiProperty({
    description: 'Filters to apply',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => JSON.parse(value))
  filters?: any;

  @ApiProperty({ name: 'sortBy', type: 'string' })
  @IsOptional()
  @IsString()
  sortBy: string;

  @ApiProperty({ name: 'sortOrder', type: 'string' })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder: SortOrder;

  @ApiProperty({
    description: 'Flag to show all records without pagination',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  showAll: boolean;
}
