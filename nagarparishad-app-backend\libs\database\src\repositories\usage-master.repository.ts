import { Repository } from 'typeorm';
import { UsageTypeMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class UsageMasterRepository extends Repository<UsageTypeMasterEntity> {
  constructor(
    @InjectRepository(UsageTypeMasterEntity)
    private readonly usageMasterRepository: Repository<UsageTypeMasterEntity>,
  ) {
    super(
      usageMasterRepository.target,
      usageMasterRepository.manager,
      usageMasterRepository.queryRunner,
    );
  }

  // async saveData(input: { usage: string }): Promise<UsageTypeMasterEntity> {
  //   let data = this.usageMasterRepository.create(input);
  //   data = await this.usageMasterRepository.save(data);
  //   return data;
  // }

  async findAllData(): Promise<UsageTypeMasterEntity[]> {
    return await this.usageMasterRepository
      .createQueryBuilder('usage_master')
      .orderBy('usage_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string): Promise<UsageTypeMasterEntity> {
    return await this.usageMasterRepository
      .createQueryBuilder('usage_master')
      .where('usage_master.usage_type_id = :usage_id', {
        usage_id: id,
      })
      .getOne();
  }

  // async updateData(id: string, input: { usage?: string }) {
  //   return await this.usageMasterRepository
  //     .createQueryBuilder('usage_master')
  //     .update(UsageTypeMasterEntity)
  //     .set(input)
  //     .where('usage_id = :usage_id', {
  //       usage_id: id,
  //     })
  //     .execute();
  // }

  async deleteData(id: string) {
    return await this.usageMasterRepository
      .createQueryBuilder('usage_master')
      .softDelete()
      .where('usage_id = :usage_id', {
        usage_id: id,
      })
      .execute();
  }
}
