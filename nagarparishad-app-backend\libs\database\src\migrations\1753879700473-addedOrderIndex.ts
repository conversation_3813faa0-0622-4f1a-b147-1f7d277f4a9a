import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedOrderIndex1753879700473 implements MigrationInterface {
    name = 'AddedOrderIndex1753879700473'

    public async up(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE "property_usage_details" ADD "orderIndex" integer`);
          }

    public async down(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE "property_usage_details" DROP COLUMN "orderIndex"`);
        }

}
