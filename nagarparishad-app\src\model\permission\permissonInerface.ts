export interface PermissionForm {
  can_read: boolean;
  can_write: boolean;
  can_update: boolean;
  can_delete: boolean;
  is_valid?: boolean;
  form: number; // Assuming this is the ID of the form
}

export interface RolePermissionCreationRequest {
  role: number;
  forms: PermissionForm[];
}
export interface RolePermissionCreationResponse {
  statusCode: number;
  message: string;
}
export interface PermissionRoleWise {
  statusCode: number;
  message: string;
  data: {
    module_id: number;
    moduleName: string;
    forms: {
      form_id: number;
      formName: string;
      permissions: {
        action_id: number;
        can_read: boolean;
        can_write: boolean;
        can_update: boolean;
        can_delete: boolean;
        is_valid: boolean;
      }[];
    }[];
  }[];
}

export interface PermissionFormWise {
  statusCode: number;
  message: string;
  data: {
    action_id: number;
    can_read: boolean;
    can_write: boolean;
    can_update: boolean;
    can_delete: boolean;
    is_valid: boolean;
    role: any;
  }[];
}

export interface PermissionActionIdWise {
  statusCode: number;
  message: string;
  data: {
    action_id: number;
    can_read: boolean;
    can_write: boolean;
    can_update: boolean;
    can_delete: boolean;
    is_valid: boolean;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  };
}
