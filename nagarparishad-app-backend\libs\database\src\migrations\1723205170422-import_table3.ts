import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportTable31723205170422 implements MigrationInterface {
    name = 'ImportTable31723205170422'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ADD "sr_no" integer NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "sr_no"`);
    }

}
