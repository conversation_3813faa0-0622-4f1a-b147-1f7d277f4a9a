import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { RoleMasterService } from './role_master.service';
import {
  CreateRoleMasterDto,
  RoleIdMasterDto,
  UpdateRoleMasterDto,
} from './dto/role-master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Role Master')
@Controller('role-master')
export class RoleMasterController {
  constructor(private readonly roleMasterService: RoleMasterService) {}

  @ApiOperation({ summary: 'Create a new Role ' })
  @ApiResponse({
    status: 201,
    description: 'The Role  has been successfully created',
  })
  @Post()
  create(@Body() createRoleMasterDto: CreateRoleMasterDto) {
    return this.roleMasterService.create(createRoleMasterDto);
  }

  @ApiOperation({ summary: 'Get all Role ' })
  @ApiResponse({ status: 200, description: 'Returns all Role ' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.roleMasterService.findAll();
  }

  @ApiOperation({ summary: 'Get one Role ' })
  @ApiResponse({ status: 200, description: 'Returns Single Role ' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('detail')
  findOne(@Query() roleIdMasterDto: RoleIdMasterDto) {
    return this.roleMasterService.findOne(roleIdMasterDto);
  }

  @ApiOperation({ summary: 'Update a Role  by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Role  has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Role  not found' })
  @Patch()
  update(
    @Query() roleIdMasterDto: RoleIdMasterDto,
    @Body() updateRoleMasterDto: UpdateRoleMasterDto,
  ) {
    return this.roleMasterService.update(roleIdMasterDto, updateRoleMasterDto);
  }

  @ApiOperation({ summary: 'Delete a Role  by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Role  has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Role  not found' })
  @Delete()
  remove(@Query() roleIdMasterDto: RoleIdMasterDto) {
    return this.roleMasterService.remove(roleIdMasterDto);
  }
}
