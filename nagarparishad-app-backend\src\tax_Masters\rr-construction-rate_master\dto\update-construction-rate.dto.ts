// src/construction-rate-master/dto/update-construction-rate.dto.ts
import { IsString, IsN<PERSON>ber, IsUUID, IsOptional, IsIn } from 'class-validator';

export class UpdateConstructionRateDto {
  @IsUUID()
  @IsOptional()
  property_type_class_id?: string; // Optional for updates

  @IsString()
  @IsOptional()
  financial_year?: string; // Optional, allowing update if needed

  @IsString()
  @IsOptional()
  reassessment_range_id?: string; // Reassessment range ID

  @IsNumber()
  @IsOptional()
  value?: number; // Optional new value for the rate

  @IsString()
  @IsOptional()
  @IsIn(['Active', 'Inactive'])
  status?: string; // Status can be updated to 'Active' or 'Inactive'
}
