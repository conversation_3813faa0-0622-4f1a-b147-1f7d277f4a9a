import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ValidationPipe,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RegisterNumberService } from './register-number.service';
import { CreateRegisterNumberDto, UpdateRegisterNumberDto } from './dto/register-number.dto';
import { Public } from '@jwt/jwt-auth/decorators/public.decorator';

@ApiTags('Register Number')
@Controller('register-number')
export class RegisterNumberController {
  constructor(private readonly registerNumberService: RegisterNumberService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new register number' })
  @ApiResponse({ status: 201, description: 'The record has been successfully created.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  createRegisterNumber(@Body(ValidationPipe) createRegisterNumberDto: CreateRegisterNumberDto) {
    return this.registerNumberService.createRegisterNumber(createRegisterNumberDto);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all register numbers' })
  @ApiResponse({ status: 200, description: 'Return all register numbers.' })
  findAllRegisterNumber() {
    return this.registerNumberService.findAllRegisterNumber();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a register number by id' })
  @ApiResponse({ status: 200, description: 'Return a register number.' })
  findRegisterNumberById(@Param('id') id: string) {
    return this.registerNumberService.findRegisterNumberById(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a register number' })
  @ApiResponse({ status: 200, description: 'The record has been successfully updated.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  updateRegisterNumber(
    @Param('id') id: string,
    @Body(ValidationPipe) updateRegisterNumberDto: UpdateRegisterNumberDto,
  ) {
    return this.registerNumberService.updateRegisterNumber(id, updateRegisterNumberDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a register number' })
  @ApiResponse({ status: 200, description: 'The record has been successfully deleted.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  removeRegisterNumber(@Param('id') id: string) {
    return this.registerNumberService.removeRegisterNumber(id);
  }
}
