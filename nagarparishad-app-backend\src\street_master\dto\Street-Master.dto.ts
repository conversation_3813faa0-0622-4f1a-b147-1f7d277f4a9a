import { ApiBody, ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class CreateStreetMasterDto {
  @ApiProperty({ name: 'streetOrRoadName', type: 'string' })
  @IsNotEmpty()
  @IsString()
  streetOrRoadName: string;
}

export class UpdateStreetMasterDto extends PartialType(CreateStreetMasterDto) {}

export class StreetMasterDto {
  @ApiProperty({ name: 'streetOrRoadId', type: 'string' })
  @IsNotEmpty()
  @IsUUID()
  streetOrRoadId: string;
}
