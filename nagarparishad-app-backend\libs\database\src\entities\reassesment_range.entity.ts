import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    PrimaryGeneratedColumn,
    Unique,
    UpdateDateColumn,
  } from 'typeorm';
  
  @Entity('reassessment_range')
  @Unique(['start_range', 'end_range'])
  export class ReassessmentRange extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    reassessment_range_id: string;
  
    @Column({ name: 'start_range', type: 'varchar', nullable: false })
    start_range: string;
  
    @Column({ name: 'end_range', type: 'varchar', nullable: false })
    end_range: string;
  
    @Column({ name: 'is_active', type: 'boolean', default: true })
    is_active: boolean;
  
    @Column({ name: 'is_current', type: 'boolean', default: false })
    is_current: boolean;
  
    @Column({ name: 'is_published', type: 'boolean', default: true })
    is_published: boolean;
  
    /*
     * Create and Update Date Columns
     */
  
    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;
  
    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;
  
    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
  }
  