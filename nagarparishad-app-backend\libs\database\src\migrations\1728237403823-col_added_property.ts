import { MigrationInterface, QueryRunner } from "typeorm";

export class ColAddedProperty1728237403823 implements MigrationInterface {
    name = 'ColAddedProperty1728237403823'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property" ADD "city" character varying`);
        await queryRunner.query(`ALTER TABLE "common_fields_of_property" ADD "property_id" uuid`);
        await queryRunner.query(`ALTER TABLE "common_fields_of_property" ADD CONSTRAINT "UQ_a4dee4e94aee71ce01ff0919c39" UNIQUE ("property_id")`);
        await queryRunner.query(`ALTER TYPE "public"."property_owner_details_gender_enum" RENAME TO "property_owner_details_gender_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."property_owner_details_gender_enum" AS ENUM('MALE', 'FEMALE', 'OTHER')`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" ALTER COLUMN "gender" TYPE "public"."property_owner_details_gender_enum" USING "gender"::"text"::"public"."property_owner_details_gender_enum"`);
        await queryRunner.query(`DROP TYPE "public"."property_owner_details_gender_enum_old"`);
        await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_2ec55a615abf24e021be0904df5"`);
        await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "UQ_2ec55a615abf24e021be0904df5" UNIQUE ("common_fields_id")`);
        await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_2ec55a615abf24e021be0904df5" FOREIGN KEY ("common_fields_id") REFERENCES "common_fields_of_property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "common_fields_of_property" ADD CONSTRAINT "FK_a4dee4e94aee71ce01ff0919c39" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "common_fields_of_property" DROP CONSTRAINT "FK_a4dee4e94aee71ce01ff0919c39"`);
        await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_2ec55a615abf24e021be0904df5"`);
        await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "UQ_2ec55a615abf24e021be0904df5"`);
        await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_2ec55a615abf24e021be0904df5" FOREIGN KEY ("common_fields_id") REFERENCES "common_fields_of_property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`CREATE TYPE "public"."property_owner_details_gender_enum_old" AS ENUM('1', '2', '3')`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" ALTER COLUMN "gender" TYPE "public"."property_owner_details_gender_enum_old" USING "gender"::"text"::"public"."property_owner_details_gender_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."property_owner_details_gender_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."property_owner_details_gender_enum_old" RENAME TO "property_owner_details_gender_enum"`);
        await queryRunner.query(`ALTER TABLE "common_fields_of_property" DROP CONSTRAINT "UQ_a4dee4e94aee71ce01ff0919c39"`);
        await queryRunner.query(`ALTER TABLE "common_fields_of_property" DROP COLUMN "property_id"`);
        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "city"`);
    }

}
