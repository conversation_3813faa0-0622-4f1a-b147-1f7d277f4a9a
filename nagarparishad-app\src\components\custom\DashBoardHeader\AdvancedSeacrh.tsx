import React, { useContext, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { object, z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { useSearchController } from "@/controller/search/SearchController";
import { useTranslation } from "react-i18next";
import { useZoneMasterController } from "@/controller/master/ZoneMasterController";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import Tippy from "@tippyjs/react";
import "tippy.js/dist/tippy.css"; // optional
import { X } from "lucide-react";
import {
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Table,
} from "@/components/ui/table";
import { useNavigate } from "react-router-dom";
import { Loader } from "@/components/globalcomponent/Loader";

const AdvancedSearch: React.FC = () => {
  const { setSearchResults } = useContext(GlobalContext);
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [showNewComponent, setShowNewComponent] = useState(false);
  const [searchResults, setSearchResultsState] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages,setTotalPages]=useState(0);
  const [searchAttempted, setSearchAttempted] = useState(false); // New state

  const resultsPerPage = 10; // Define how many results per page

  const schema = object({
    ownerName: z.string().min(1, { message: t("errorsRequiredField") }),
    zone: z.string().optional(),
    ward: z.string().optional(),
  }).refine((data) => data.zone || data.ward, {
    message: t("errorsRequiredZoneOrWard"),
    path: ["zone", "ward"],
  });
  const navigate=useNavigate();

  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      ownerName: "",
      zone: "",
      ward: "",
    },
  });

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = form;

  const { zoneList } = useZoneMasterController();
  const { wardList } = useWardMasterController();
  const { advancedPropertySearch } = useSearchController();

  const onSubmit = async (data: any) => {
    setLoading(true);
    setSearchAttempted(true);
    try {
      const formData = {
        ownerName: form.getValues("ownerName").trim(),
        zone: form.getValues("zone"),
        ward: form.getValues("ward"),
      };

      const response = await advancedPropertySearch(
        formData.ownerName,
        formData.zone,
        formData.ward,
        currentPage,
        resultsPerPage
      );

      setSearchResultsState(response.data.data);
      setTotalResults(response.data.totalItems);
      setTotalPages(response.data.totalPages);
      console.log("response data", response);
      toast({
        title: t("api.fetch"),
        variant: "success",
      });
    } catch (error) {
      console.error("Error occurred while fetching property details:", error);
      toast({
        title: t("api.error"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = () => {
    form.reset();
    setSearchResultsState(null);
    setCurrentPage(1);
    setTotalResults(0);
    setShowNewComponent((prev) => !prev);
    setSearchAttempted(false);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    handleSubmit(onSubmit)();
  };

  const handleReset = () => {
    form.reset();
    setSearchResultsState(null);
    setCurrentPage(1);
    setTotalResults(0);
    setSearchAttempted(false);
  };

  console.log("totalPages", totalPages);

  const handlNavigate = (id) => {
    navigate("/property/property-view", { state: id});
    form.reset();
    setSearchResultsState(null);
    setCurrentPage(1);
    setTotalResults(0);
    setShowNewComponent((prev) => !prev);
  };
  return (
    <>
      <div
        className="text-center ml-4 cursor-pointer underline text-black font-medium sm:block hidden"
        onClick={handleToggle}
      >
        <Tippy content={t("advancesearch")}>
          <img
            src="/src/assets/img/global/advance-search-2.svg"
            alt=""
            className="w-10 h-[36px]"
          />
        </Tippy>
      </div>


      {showNewComponent &&  (
        <>
          <div
            className="fixed inset-0 bg-black opacity-40 z-40"
            onClick={handleToggle}
          ></div>
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white p-5 rounded-20 shadow-md w-fit min-w-[50rem] max-w-full">
              <div className="flex justify-between">
                <p className="text-lg font-semibold">{t("advancesearch")}</p>
                <X className="h-5 w-5 cursor-pointer" onClick={handleToggle} />
              </div>
              <hr className="my-2" />
              <Form {...form}>
                <form onSubmit={handleSubmit(onSubmit)}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={control}
                      name="ward"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("ward.wardLabel")}</FormLabel>
                          <Select
                            value={field.value}
                            onValueChange={(value) => {
                              field.onChange(value);
                            }}
                          >
                            <SelectTrigger>
                              {(wardList.length > 0 &&
                                wardList.find(
                                  (ward) => ward.ward_id === field.value
                                )?.ward_name) ||
                                t("ward.selectWard")}
                            </SelectTrigger>
                            <SelectContent>
                              {wardList.map((ward) => (
                                <SelectItem
                                  key={ward.ward_id}
                                  value={ward.ward_id}
                                >
                                  {ward.ward_name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {errors.ward && (
                            <FormMessage className="mt-1">
                              {errors.ward.message}
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="zone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("zone.zoneLabel")}</FormLabel>
                          <Select
                            value={field.value}
                            onValueChange={(value) => {
                              field.onChange(value);
                            }}
                          >
                            <SelectTrigger>
                              {(zoneList.length > 0 &&
                                zoneList.find(
                                  (zone) => zone.zone_id === field.value
                                )?.zoneName) ||
                                t("zone.selectZone")}
                            </SelectTrigger>
                            <SelectContent>
                              {zoneList.map((zone) => (
                                <SelectItem
                                  key={zone.zone_id}
                                  value={zone.zone_id}
                                >
                                  {zone.zoneName}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {errors.zone && (
                            <FormMessage className="mt-1">
                              {errors.zone.message}
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="ownerName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("searchOwnerName")} 
                          <span className="ml-1 text-red-500">*</span>
                          </FormLabel>
                          <Input
                            className="mt-1"
                            placeholder={t("searchOwnerName")}
                            {...field}
                          />
                          {errors.ownerName && (
                            <FormMessage className="mt-1">
                              {errors.ownerName.message}
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex justify-end mt-4">
                    <Button type="submit">
                      { t("search")}
                    </Button>
                    <Button
                      variant="outline"
                      className="ml-2"
                      onClick={handleReset}
                    >
                      {t("reset")}
                    </Button>
                  </div>
                </form>
              </Form>

              {loading && (
          <div className="flex justify-center items-center my-4">
            <Loader />
          </div>
        )}
              {loading ? (
                <div className="mt-4 text-center">{t("loading")}</div>
              ) : searchResults && searchResults.length > 0 ? (
                <div className="mt-4">
                  <hr />
                  <Table className="min-w-full mt-4 border border-gray-300 w-80">
                    <TableHeader className="bg-gray-100">
                      <TableRow>
                        <TableHead className=" px-4 py-2">Index</TableHead>
                        <TableHead className=" px-4 py-2">
                          property Number
                        </TableHead>
                        <TableHead className=" px-4 py-2">Owner Name</TableHead>
                        <TableHead className=" px-4 py-2">Zone</TableHead>
                        <TableHead className=" px-4 py-2">Ward </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {searchResults.map((result: any, index) => (
                        <TableRow
                          key={result.property_id}
                          className="hover:bg-gray-50 cursor-pointer"
                          onClick={()=>handlNavigate(result.property_id)}
                        >
                          <TableCell>
                            {(currentPage - 1) * resultsPerPage + index + 1}
                          </TableCell>
                          <TableCell className=" px-4 py-2">
                            {result.propertyNumber || "No propertyNumber"}
                          </TableCell>
                          <TableCell className=" px-4 py-2">
                            {result.property_owner_details.length > 0
                              ? result.property_owner_details[0].name
                              : "No Owner"}
                          </TableCell>
                          <TableCell className=" px-4 py-2">
                            {result.zone?.zoneName || "No Zone"}
                          </TableCell>
                          <TableCell className=" px-4 py-2">
                            {result.ward?.ward_name || "No Ward"}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {/* Pagination and other logic */}
                  <div className="flex justify-between items-center mt-4">
                    <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                      Page {currentPage} of {totalPages}
                    </div>
                    <div className="space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage >= totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                !loading && searchAttempted && (
                  <div className="mt-4 text-center">{t("noResultsFound")}</div>
                )
              )}
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default AdvancedSearch;
