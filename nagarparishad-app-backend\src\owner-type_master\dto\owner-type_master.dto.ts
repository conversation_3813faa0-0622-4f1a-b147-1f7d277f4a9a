import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateOwnerTypeMasterDto {
  @ApiProperty({ name: 'ownerType', type: String })
  @IsNotEmpty()
  @IsString()
  ownerType: string;
}
export class UpdateOwnerTypeMasterDto extends PartialType(
  CreateOwnerTypeMasterDto,
) {}

export class OwnerTypeMasterIdDto {
  @ApiProperty({ name: 'owner_type_id', type: String })
  @IsNotEmpty()
  @IsUUID()
  owner_type_id: string;
}
