import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportPropertiesUpdatedColAdded1727268386459 implements MigrationInterface {
    name = 'ImportPropertiesUpdatedColAdded1727268386459'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ADD "owner_table_id" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "usage_details_table_id" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "property_table_id" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "property_table_id"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "usage_details_table_id"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "owner_table_id"`);
    }

}
