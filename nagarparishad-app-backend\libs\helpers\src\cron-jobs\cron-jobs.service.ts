import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { exec } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
// import * as nodemailer from 'nodemailer';
import * as AWS from 'aws-sdk';
import {
  PreviousOwnerRepository,
  UserOtpRepository,
  PenaltyFeeYearWiseRepository,
  WarshikKarRepository,
  PaidDataRepository,
  Financial_yearRepository,
  CronJobFailureRepository,
} from 'libs/database/repositories';
import axios from 'axios';
import FormData from 'form-data';
import { smtpConfig } from 'src/utils/smtp.config';
import { EmailService } from 'src/utils/email.service';

// Interfaces for better type safety
export interface CronJobResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
  timestamp: Date;
}

interface RetryConfig {
  maxRetries: number;
  retryDelayMs: number;
  backoffMultiplier: number;
}

interface PenaltyCalculationResult {
  propertyId: string;
  propertyNumber: string;
  success: boolean;
  remainingAmount: number;
  penaltyAmount?: number;
  action?: 'created' | 'updated' | 'skipped';
  error?: string;
}

@Injectable()
export class CronJobsService {
  private readonly logger = new Logger(CronJobsService.name);
  

  // Retry configuration
  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    retryDelayMs: 5000, // 5 seconds
    backoffMultiplier: 2, // Exponential backoff
  };

  // Track failed jobs for manual retry
  private failedJobs: Map<string, {
    jobName: string;
    lastAttempt: Date;
    attempts: number;
    error: string;
    data?: any;
  }> = new Map();

  constructor(
    private readonly previousOwnerRepository: PreviousOwnerRepository,
    private readonly otpRepository: UserOtpRepository,
    private readonly PenaltyFeeYearWiseRepository: PenaltyFeeYearWiseRepository,
    private readonly warshikKarRepository: WarshikKarRepository,
    private readonly paidDataRepository: PaidDataRepository,
    private readonly financialYearRepository: Financial_yearRepository,
    private readonly cronJobFailureRepository: CronJobFailureRepository,
    private readonly emailService: EmailService,
  ) {
    // Load failed jobs from database on startup
    this.loadFailedJobsFromDatabase();
  }

  /**
   * Load failed jobs from database into memory on startup
   */
  private async loadFailedJobsFromDatabase() {
    try {
      const dbFailedJobs = await this.cronJobFailureRepository.getFailedJobs();

      for (const dbJob of dbFailedJobs) {
        this.failedJobs.set(dbJob.job_id, {
          jobName: dbJob.job_name,
          lastAttempt: dbJob.last_attempt,
          attempts: dbJob.attempt_count,
          error: dbJob.error_message,
          data: dbJob.job_data
        });
      }

      if (dbFailedJobs.length > 0) {
        this.logger.log(`Loaded ${dbFailedJobs.length} failed jobs from database`);
      }
    } catch (error) {
      this.logger.error(`Failed to load failed jobs from database: ${error.message}`);
    }
  }

  // Runs every day at midnight

  @Cron('0 0 0 * * *') // Runs at midnight every day
  async deleteExpiredOtp() {
    try {
      await this.otpRepository
        .deleteExiperedOtp()
        .then(() => {
          Logger.log('Expired OTP deleted');
        })
        .catch(() => {
          Logger.log('Failed To delete');
        });
    } catch (error) {
      throw error;
    }
  }
  // @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async deleteOldOwners() {
    this.logger.log(
      'Running cron job to delete previous owners older than 5 years',
    );
    await this.previousOwnerRepository.deleteOldOwners();
    this.logger.log('Old previous owners deleted successfully');
  }

  // Runs every day at 11 PM
  // @Cron(CronExpression.EVERY_DAY_AT_11PM)

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async handleCron() {
    if (process.env.NODE_ENV !== 'prod') {
      this.logger.log(
        'Cron job skipped: Not running in production environment',
      );
      return;
    }

    this.logger.log('Running cron job to backup and send the database');
    await this.backupAndSend();
    this.logger.log('Database backup and send process completed');
  }

  /**
   * Enhanced penalty increment cron with retry mechanism
   */
  // @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  async handlePenlatyIncreament() {
    const jobName = 'monthly_penalty_increment';
    const jobId = `${jobName}_${new Date().toISOString().split('T')[0]}`;

    this.logger.log('Running enhanced cron job to increase penalty by 2%...');

    try {
      const result = await this.executeWithRetry(
        () => this.performPenaltyIncrement(),
        this.defaultRetryConfig,
        jobName
      );

      if (result.success) {
        this.logger.log('Monthly penalty increment completed successfully');
        // Remove from failed jobs if it was previously failed
        this.failedJobs.delete(jobId);
        await this.cronJobFailureRepository.markAsResolved(jobId, 'Successfully completed');
      } else {
        this.logger.error(`Monthly penalty increment failed: ${result.error}`);
        await this.trackFailedJob(jobId, jobName, result.error);
      }
    } catch (error) {
      this.logger.error(`Critical error in penalty increment: ${error.message}`, error.stack);
      await this.trackFailedJob(jobId, jobName, error.message);
    }
  }

  /**
   * Separate method to perform the actual penalty increment logic
   */
  private async performPenaltyIncrement(): Promise<CronJobResult> {
    try {
      // Check if today is January 1st
      const today = new Date();
      const isJanuaryFirst = today.getMonth() === 0 && today.getDate() === 1;

      // Always run the regular percentage increment
      this.logger.log('Starting regular penalty percentage increment...');
      const penaltyFees = await this.PenaltyFeeYearWiseRepository.handlePercentageIncrement();
      this.logger.log(`Updated ${penaltyFees.length} penalty records with 2% increment`);

      let januaryResult: CronJobResult | null = null;

      if (isJanuaryFirst) {
        this.logger.log('Today is January 1st. Calculating new penalties on remaining amounts...');
        januaryResult = await this.executeWithRetry(
          () => this.performJanuaryFirstPenalties(),
          { ...this.defaultRetryConfig, maxRetries: 5 }, // More retries for January 1st
          'january_first_penalties'
        );

        if (!januaryResult.success) {
          throw new Error(`January 1st penalty calculation failed: ${januaryResult.error}`);
        }
      }

      return {
        success: true,
        message: `Penalty increment completed. Regular updates: ${penaltyFees.length}${isJanuaryFirst ? `, January penalties: ${januaryResult?.data?.penaltyCount || 0}` : ''}`,
        data: {
          regularUpdates: penaltyFees.length,
          isJanuaryFirst,
          januaryPenalties: januaryResult?.data || null
        },
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        message: 'Penalty increment failed',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Enhanced January 1st penalty calculation with better error handling
   */
  private async performJanuaryFirstPenalties(): Promise<CronJobResult> {
    try {
      this.logger.log('Starting January 1st penalty calculation on remaining amounts');

      // Get current financial year
      const currentFinancialYear = await this.financialYearRepository.getCurrentFinancialYear();
      if (!currentFinancialYear) {
        const errorMsg = 'No current financial year found for January 1st penalty calculation';
        this.logger.error(errorMsg);
        return {
          success: false,
          message: errorMsg,
          error: 'Missing financial year',
          timestamp: new Date()
        };
      }

      // Find the specific property with property number SNP700064
      const specificProperty = await this.warshikKarRepository.findOne({
        where: {
          property: { propertyNumber: 'SNP700064' },
          financial_year: currentFinancialYear?.financial_year_range,
          status: 'active'
        },
        relations: ['property']
      });

      if (!specificProperty) {
        const errorMsg = 'Property with number SNP700064 not found or no active warshik kar record exists';
        this.logger.error(errorMsg);
        return {
          success: false,
          message: errorMsg,
          error: 'Property not found',
          timestamp: new Date()
        };
      }

      this.logger.log(`Found property SNP700064 with ID: ${specificProperty.property?.property_id}`);

      // Get paid data for the specific property
      const propertyPayments = await this.paidDataRepository.find({
        where: {
          property: { property_id: specificProperty.property?.property_id },
          financial_year: currentFinancialYear?.financial_year_range
        }
      });

      this.logger.log(`Found ${propertyPayments.length} payment records for property SNP700064`);

      // Calculate total paid amount for current tax types
      const totalPaid = propertyPayments.reduce((sum: number, payment: any) => {
        // Sum only the current tax values (tax_type_X_curr)
        let currentTaxSum = 0;
        for (let i = 1; i <= 10; i++) {
          const taxTypeCurrentKey = `tax_type_${i}_curr`;
          if (taxTypeCurrentKey in payment) {
            currentTaxSum += Number(payment[taxTypeCurrentKey] || 0);
          }
        }

        // Also add other_tax_sum_tax_curr if it exists
        if ('other_tax_sum_tax_curr' in payment) {
          currentTaxSum += Number(payment.other_tax_sum_tax_curr || 0);
        }

        return sum + currentTaxSum;
      }, 0);

      this.logger.log(`Total paid amount for property SNP700064: ${totalPaid}`);

      // Calculate current year's tax by summing up all current tax type values
      let calculatedTotalTaxCurrent = 0;

      // Sum up all tax_type_X_current values (1 through 10)
      for (let i = 1; i <= 10; i++) {
        const taxTypeCurrentKey = `tax_type_${i}_current`;
        if (taxTypeCurrentKey in specificProperty) {
          calculatedTotalTaxCurrent += Number(specificProperty[taxTypeCurrentKey] || 0);
        }
      }

      // Also add other_tax_sum_tax_current if it exists
      if ('other_tax_sum_tax_current' in specificProperty) {
        calculatedTotalTaxCurrent += Number(specificProperty.other_tax_sum_tax_current || 0);
      }

      this.logger.log(`Total current tax for property SNP700064: ${calculatedTotalTaxCurrent}`);

      // Calculate remaining amount using the calculated current year's tax
      const remainingAmount = Math.max(0, calculatedTotalTaxCurrent - totalPaid);
      this.logger.log(`Remaining amount for property SNP700064: ${remainingAmount}`);

      if (remainingAmount > 0) {
        // Set the actual_value to the remaining amount
        const actualValue = remainingAmount;
        const taxPercentage = 2; // 2% for January 1st

        // Check if there's an existing penalty for this property
        const existingPenalties =
          await this.PenaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
            specificProperty.property?.property_id,
            currentFinancialYear.financial_year_range,
          );

        if (existingPenalties && existingPenalties.length > 0) {
          // Update existing penalty
          const existingPenalty = existingPenalties[0];
          await this.PenaltyFeeYearWiseRepository.updatePenaltyFee(
            existingPenalty.penalty_fee_id,
            {
              actual_value: actualValue,
              tax_percentage: taxPercentage, // Update to 2%
              // penalty_value will be calculated in the repository
            },
          );
          this.logger.log(`Updated existing penalty for property SNP700064 with actual value: ${actualValue}`);
        } else {
          // Create new penalty
          await this.PenaltyFeeYearWiseRepository.savePenaltyFee({
            property: specificProperty.property?.property_id,
            financial_year: currentFinancialYear.financial_year_range,
            actual_value: actualValue,
            tax_percentage: taxPercentage, // Set to 2%
            // penalty_value will be calculated in the repository
          });
          this.logger.log(`Created new penalty for property SNP700064 with actual value: ${actualValue}`);
        }
      } else {
        this.logger.log(`No remaining amount for property SNP700064, no penalty needed`);
      }

      this.logger.log(`Completed January 1st penalty calculation for property SNP700064`);

      return {
        success: true,
        message: 'January 1st penalty calculation completed successfully',
        data: {
          propertyId: specificProperty.property?.property_id,
          propertyNumber: 'SNP700064',
          remainingAmount,
          penaltyCreated: remainingAmount > 0,
          financialYear: currentFinancialYear.financial_year_range
        },
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error(`Error calculating January 1st penalties: ${error.message}`, error.stack);
      return {
        success: false,
        message: 'January 1st penalty calculation failed',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Generic retry mechanism for cron jobs
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: RetryConfig,
    jobName: string
  ): Promise<T> {
    let lastError: Error;
    let delay = config.retryDelayMs;

    for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
      try {
        this.logger.log(`${jobName}: Attempt ${attempt}/${config.maxRetries}`);
        const result = await operation();

        if (attempt > 1) {
          this.logger.log(`${jobName}: Succeeded on attempt ${attempt}`);
        }

        return result;
      } catch (error) {
        lastError = error;
        this.logger.warn(`${jobName}: Attempt ${attempt} failed: ${error.message}`);

        if (attempt < config.maxRetries) {
          this.logger.log(`${jobName}: Retrying in ${delay}ms...`);
          await this.sleep(delay);
          delay *= config.backoffMultiplier; // Exponential backoff
        }
      }
    }

    // All retries exhausted
    this.logger.error(`${jobName}: All ${config.maxRetries} attempts failed. Last error: ${lastError.message}`);
    throw lastError;
  }

  /**
   * Track failed jobs for manual intervention (now with persistent storage)
   */
  private async trackFailedJob(jobId: string, jobName: string, error: string, data?: any) {
    try {
      // Store in database for persistence
      await this.cronJobFailureRepository.recordFailure(jobId, jobName, error, data);

      // Also keep in memory for quick access
      const existingJob = this.failedJobs.get(jobId);
      const attempts = existingJob ? existingJob.attempts + 1 : 1;

      this.failedJobs.set(jobId, {
        jobName,
        lastAttempt: new Date(),
        attempts,
        error,
        data
      });

      this.logger.error(`Failed job tracked: ${jobName} (${jobId}) - Attempt ${attempts}`);
    } catch (trackingError) {
      this.logger.error(`Failed to track job failure: ${trackingError.message}`);
      // Still keep in memory even if DB storage fails
      this.failedJobs.set(jobId, {
        jobName,
        lastAttempt: new Date(),
        attempts: 1,
        error,
        data
      });
    }
  }

  /**
   * Utility method for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Manual retry for failed jobs - can be called via API endpoint
   */
  async retryFailedJob(jobId: string): Promise<CronJobResult> {
    // First check database for failed job
    const dbFailedJob = await this.cronJobFailureRepository.getFailedJobById(jobId);
    const memoryFailedJob = this.failedJobs.get(jobId);

    const failedJob = dbFailedJob || memoryFailedJob;

    if (!failedJob) {
      return {
        success: false,
        message: `No failed job found with ID: ${jobId}`,
        error: 'Job not found',
        timestamp: new Date()
      };
    }

    const jobName = dbFailedJob ? dbFailedJob.job_name : memoryFailedJob.jobName;
    this.logger.log(`Manually retrying failed job: ${jobName} (${jobId})`);

    try {
      // Mark as retrying in database
      if (dbFailedJob) {
        await this.cronJobFailureRepository.markAsRetrying(jobId);
      }

      let result: CronJobResult;

      switch (jobName) {
        case 'monthly_penalty_increment':
          result = await this.performPenaltyIncrement();
          break;
        case 'january_first_penalties':
          result = await this.performJanuaryFirstPenalties();
          break;
        default:
          return {
            success: false,
            message: `Unknown job type: ${jobName}`,
            error: 'Unknown job type',
            timestamp: new Date()
          };
      }

      if (result.success) {
        // Remove from memory and mark as resolved in database
        this.failedJobs.delete(jobId);
        if (dbFailedJob) {
          await this.cronJobFailureRepository.markAsResolved(jobId, 'Manual retry successful');
        }
        this.logger.log(`Successfully retried job: ${jobName} (${jobId})`);
      } else {
        // Update failure record
        await this.trackFailedJob(jobId, jobName, result.error || 'Retry failed');
      }

      return result;
    } catch (error) {
      this.logger.error(`Manual retry failed for job ${jobId}: ${error.message}`);

      // Update the failed job record
      await this.trackFailedJob(jobId, jobName, error.message);

      return {
        success: false,
        message: `Manual retry failed: ${error.message}`,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Get all failed jobs for monitoring (from database and memory)
   */
  async getFailedJobs(): Promise<Array<{jobId: string, details: any}>> {
    try {
      // Get from database first
      const dbFailedJobs = await this.cronJobFailureRepository.getFailedJobs();
      const failedJobsList: Array<{jobId: string, details: any}> = [];

      // Add database records
      for (const dbJob of dbFailedJobs) {
        failedJobsList.push({
          jobId: dbJob.job_id,
          details: {
            jobName: dbJob.job_name,
            lastAttempt: dbJob.last_attempt,
            attempts: dbJob.attempt_count,
            error: dbJob.error_message,
            data: dbJob.job_data,
            status: dbJob.status,
            firstFailure: dbJob.first_failure,
            source: 'database'
          }
        });
      }

      // Add memory records that might not be in database
      for (const [jobId, details] of this.failedJobs.entries()) {
        const existsInDb = failedJobsList.some(job => job.jobId === jobId);
        if (!existsInDb) {
          failedJobsList.push({
            jobId,
            details: { ...details, source: 'memory' }
          });
        }
      }

      return failedJobsList;
    } catch (error) {
      this.logger.error(`Error getting failed jobs: ${error.message}`);
      // Fallback to memory only
      const failedJobsList: Array<{jobId: string, details: any}> = [];
      for (const [jobId, details] of this.failedJobs.entries()) {
        failedJobsList.push({ jobId, details: { ...details, source: 'memory' } });
      }
      return failedJobsList;
    }
  }

  /**
   * Clear old failed jobs (older than 30 days)
   */
  cleanupOldFailedJobs(): number {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    let cleanedCount = 0;

    for (const [jobId, details] of this.failedJobs.entries()) {
      if (details.lastAttempt < thirtyDaysAgo) {
        this.failedJobs.delete(jobId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.log(`Cleaned up ${cleanedCount} old failed job records`);
    }

    return cleanedCount;
  }

  /**
   * Force run January 1st penalty calculation (for testing or manual execution)
   */
  async forceJanuaryFirstPenalties(): Promise<CronJobResult> {
    this.logger.log('Manually forcing January 1st penalty calculation...');

    return await this.executeWithRetry(
      () => this.performJanuaryFirstPenalties(),
      { ...this.defaultRetryConfig, maxRetries: 5 },
      'manual_january_first_penalties'
    );
  }

  /**
   * Handle penalty increment for a specific property ID
   * This function takes a particular property ID and performs penalty increment for that property only
   */
  async handlePenaltyIncrementForSpecificProperty(propertyId: string): Promise<CronJobResult> {
    const jobName = 'specific_property_penalty_increment';
    const jobId = `${jobName}_${propertyId}_${new Date().toISOString().split('T')[0]}`;

    console.log('='.repeat(100));
    console.log('🧪 TESTING PENALTY INCREMENT FOR SPECIFIC PROPERTY');
    console.log(`🏠 Property ID: ${propertyId}`);
    console.log('='.repeat(100));

    this.logger.log(`Running penalty increment for specific property: ${propertyId}`);

    try {
      const result = await this.executeWithRetry(
        () => this.performPenaltyIncrementForProperty(propertyId),
        this.defaultRetryConfig,
        jobName
      );

      if (result.success) {
        console.log(`✅ Penalty increment completed successfully for property ${propertyId}`);
        this.logger.log(`Penalty increment completed successfully for property ${propertyId}`);
      } else {
        console.log(`❌ Penalty increment failed for property ${propertyId}: ${result.error}`);
        this.logger.error(`Penalty increment failed for property ${propertyId}: ${result.error}`);
      }

      return result;
    } catch (error) {
      console.log(`❌ Critical error in penalty increment for property ${propertyId}: ${error.message}`);
      this.logger.error(`Critical error in penalty increment for property ${propertyId}: ${error.message}`, error.stack);

      return {
        success: false,
        message: `Critical error in penalty increment for property ${propertyId}`,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Perform penalty increment for a specific property
   */
  private async performPenaltyIncrementForProperty(propertyId: string): Promise<CronJobResult> {
    try {
      console.log(`🔍 Starting penalty increment for property: ${propertyId}`);
      this.logger.log(`Starting penalty increment for property: ${propertyId}`);

      // Check if today is January 1st
      const today = new Date();
      const isJanuaryFirst = today.getMonth() === 0 && today.getDate() === 1;

      console.log(`📅 Today's date: ${today.toDateString()}`);
      console.log(`📅 Is January 1st: ${isJanuaryFirst}`);

      // Get current financial year
      const currentFinancialYear = await this.financialYearRepository.getCurrentFinancialYear();
      if (!currentFinancialYear) {
        const errorMsg = 'No current financial year found';
        console.log(`❌ ERROR: ${errorMsg}`);
        this.logger.error(errorMsg);
        return {
          success: false,
          message: errorMsg,
          error: 'Missing financial year',
          timestamp: new Date()
        };
      }

      console.log(`📅 Current Financial Year: ${currentFinancialYear.financial_year_range}`);

      // Find the specific property
      const specificProperty = await this.warshikKarRepository.findOne({
        where: {
          property: { property_id: propertyId },
          financial_year: currentFinancialYear?.financial_year_range,
          status: 'active'
        },
        relations: ['property']
      });

      if (!specificProperty) {
        const errorMsg = `Property with ID ${propertyId} not found or no active warshik kar record exists`;
        console.log(`❌ ERROR: ${errorMsg}`);
        this.logger.error(errorMsg);
        return {
          success: false,
          message: errorMsg,
          error: 'Property not found',
          timestamp: new Date()
        };
      }

      const propertyNumber = specificProperty.property?.propertyNumber || 'Unknown';
      const oldPropertyNumber = specificProperty.property?.old_propertyNumber || 'N/A';

      console.log(`✅ Found property:`);
      console.log(`🏠 Property Number: ${propertyNumber}`);
      console.log(`🏠 Old Property Number: ${oldPropertyNumber}`);
      console.log(`🆔 Property ID: ${propertyId}`);

      this.logger.log(`Found property ${propertyNumber} with ID: ${propertyId}`);

      // Step 1: Always run the regular percentage increment for existing penalties
      console.log('\n📊 STEP 1: REGULAR PENALTY PERCENTAGE INCREMENT');
      console.log('Checking for existing penalties to increment...');

      const existingPenalties = await this.PenaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
        propertyId,
        currentFinancialYear.financial_year_range,
      );

      console.log(`Found ${existingPenalties?.length || 0} existing penalty records`);

      let regularIncrementCount = 0;
      if (existingPenalties && existingPenalties.length > 0) {
        for (const penalty of existingPenalties) {
          // Increment the tax percentage by 2%
          const newPercentage = (penalty.tax_percentage || 0) + 2;

          const oldPenaltyValue = penalty.actual_value * (penalty.tax_percentage / 100);
          const newPenaltyValue = penalty.actual_value * (newPercentage / 100);

          console.log(`  Updating penalty ID ${penalty.penalty_fee_id}:`);
          console.log(`    Old percentage: ${penalty.tax_percentage}%`);
          console.log(`    New percentage: ${newPercentage}%`);
          console.log(`    Actual value: ${penalty.actual_value}`);
          console.log(`    Old penalty value: ${oldPenaltyValue}`);
          console.log(`    New penalty value: ${newPenaltyValue}`);

          await this.PenaltyFeeYearWiseRepository.updatePenaltyFee(
            penalty.penalty_fee_id,
            {
              tax_percentage: newPercentage,
              // actual_value will be retrieved from penalty table automatically
              // penalty_value will be recalculated in the repository
            },
          );
          regularIncrementCount++;
        }
        console.log(`✅ Updated ${regularIncrementCount} existing penalty records with 2% increment`);
      } else {
        console.log('No existing penalties found for regular increment');
      }

      let januaryResult: CronJobResult | null = null;

      // Step 2: If it's January 1st, calculate new penalties on remaining amounts
      if (isJanuaryFirst) {
        console.log('\n🚨 STEP 2: JANUARY 1ST NEW PENALTY CALCULATION');
        console.log('Today is January 1st. Calculating new penalties on remaining amounts...');

        januaryResult = await this.performJanuaryFirstPenaltiesForProperty(propertyId, currentFinancialYear);
      } else {
        console.log('\n⚠️  STEP 2: SKIPPED (Not January 1st)');
        console.log('January 1st penalty calculation skipped - not January 1st');
      }

      const finalResult = {
        success: true,
        message: `Penalty increment completed for property ${propertyNumber}. Regular updates: ${regularIncrementCount}${isJanuaryFirst ? `, January penalties: ${januaryResult?.success ? 'created/updated' : 'failed'}` : ''}`,
        data: {
          propertyId,
          propertyNumber,
          oldPropertyNumber,
          regularIncrementCount,
          isJanuaryFirst,
          januaryPenalties: januaryResult?.data || null,
          financialYear: currentFinancialYear.financial_year_range
        },
        timestamp: new Date()
      };

      console.log('\n' + '='.repeat(80));
      console.log('📋 FINAL SUMMARY');
      console.log('='.repeat(80));
      console.log(`Property ID: ${propertyId}`);
      console.log(`Property Number: ${propertyNumber}`);
      console.log(`Old Property Number: ${oldPropertyNumber}`);
      console.log(`Financial Year: ${currentFinancialYear.financial_year_range}`);
      console.log(`Regular Increment Count: ${regularIncrementCount}`);
      console.log(`Is January 1st: ${isJanuaryFirst}`);
      console.log(`January Penalties: ${januaryResult ? (januaryResult.success ? 'Success' : 'Failed') : 'Skipped'}`);
      console.log('='.repeat(80));

      return finalResult;
    } catch (error) {
      console.log(`❌ ERROR in penalty increment: ${error.message}`);
      this.logger.error(`Error in penalty increment for property ${propertyId}: ${error.message}`, error.stack);

      return {
        success: false,
        message: `Penalty increment failed for property ${propertyId}`,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Perform January 1st penalty calculation for a specific property
   */
  private async performJanuaryFirstPenaltiesForProperty(propertyId: string, currentFinancialYear: any): Promise<CronJobResult> {
    try {
      console.log(`🚨 Starting January 1st penalty calculation for property: ${propertyId}`);
      this.logger.log(`Starting January 1st penalty calculation for property: ${propertyId}`);

      // Find the specific property (we already know it exists from the calling function)
      const specificProperty = await this.warshikKarRepository.findOne({
        where: {
          property: { property_id: propertyId },
          financial_year: currentFinancialYear?.financial_year_range,
          status: 'active'
        },
        relations: ['property']
      });

      if (!specificProperty) {
        const errorMsg = `Property with ID ${propertyId} not found for January 1st calculation`;
        console.log(`❌ ERROR: ${errorMsg}`);
        return {
          success: false,
          message: errorMsg,
          error: 'Property not found',
          timestamp: new Date()
        };
      }

      const propertyNumber = specificProperty.property?.propertyNumber || 'Unknown';
      console.log(`Processing January 1st penalties for property ${propertyNumber}`);

      // Get paid data for the specific property
      const propertyPayments = await this.paidDataRepository.find({
        where: {
          property: { property_id: propertyId },
          financial_year: currentFinancialYear?.financial_year_range
        }
      });

      console.log(`💰 Found ${propertyPayments.length} payment records for property`);

      // Calculate total paid amount for current tax types
      const totalPaid = propertyPayments.reduce((sum: number, payment: any) => {
        // Sum only the current tax values (tax_type_X_curr)
        let currentTaxSum = 0;
        for (let i = 1; i <= 10; i++) {
          const taxTypeCurrentKey = `tax_type_${i}_curr`;
          if (taxTypeCurrentKey in payment) {
            const taxValue = Number(payment[taxTypeCurrentKey] || 0);
            if (taxValue > 0) {
              console.log(`    Payment Tax Type ${i} Current: ${taxValue}`);
              currentTaxSum += taxValue;
            }
          }
        }

        // Also add other_tax_sum_tax_curr if it exists
        if ('other_tax_sum_tax_curr' in payment) {
          const otherTaxValue = Number(payment.other_tax_sum_tax_curr || 0);
          if (otherTaxValue > 0) {
            console.log(`    Payment Other Tax Sum Current: ${otherTaxValue}`);
            currentTaxSum += otherTaxValue;
          }
        }

        return sum + currentTaxSum;
      }, 0);

      console.log(`💰 Total paid amount for property: ${totalPaid}`);

      // Calculate current year's tax by summing up all current tax type values
      let calculatedTotalTaxCurrent = 0;

      console.log('\n📊 TAX CALCULATION BREAKDOWN:');

      // Sum up all tax_type_X_current values (1 through 10)
      for (let i = 1; i <= 10; i++) {
        const taxTypeCurrentKey = `tax_type_${i}_current`;
        if (taxTypeCurrentKey in specificProperty) {
          const taxValue = Number(specificProperty[taxTypeCurrentKey] || 0);
          if (taxValue > 0) {
            console.log(`  Tax Type ${i} Current: ${taxValue}`);
            calculatedTotalTaxCurrent += taxValue;
          }
        }
      }

      // Also add other_tax_sum_tax_current if it exists
      if ('other_tax_sum_tax_current' in specificProperty) {
        const otherTaxValue = Number(specificProperty.other_tax_sum_tax_current || 0);
        if (otherTaxValue > 0) {
          console.log(`  Other Tax Sum Current: ${otherTaxValue}`);
          calculatedTotalTaxCurrent += otherTaxValue;
        }
      }

      console.log(`📊 Total current tax for property: ${calculatedTotalTaxCurrent}`);

      // Calculate remaining amount using the calculated current year's tax
      const remainingAmount = Math.max(0, calculatedTotalTaxCurrent - totalPaid);

      console.log('\n💰 REMAINING AMOUNT CALCULATION:');
      console.log(`  Calculated Current Tax: ${calculatedTotalTaxCurrent}`);
      console.log(`  Total Paid: ${totalPaid}`);
      console.log(`  Remaining Amount: ${remainingAmount}`);

      if (remainingAmount > 0) {
        console.log('\n🚨 JANUARY 1ST PENALTY CALCULATION:');

        // Set the actual_value to the remaining amount
        const actualValue = remainingAmount;
        const taxPercentage = 2; // 2% for January 1st
        const penaltyAmount = actualValue * (taxPercentage / 100);

        console.log(`  Actual Value (Remaining Amount): ${actualValue}`);
        console.log(`  Tax Percentage: ${taxPercentage}%`);
        console.log(`  Calculated Penalty Amount: ${penaltyAmount}`);

        // Check if there's an existing penalty for this property
        const existingPenalties =
          await this.PenaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
            propertyId,
            currentFinancialYear.financial_year_range,
          );

        console.log(`  Existing January Penalties Found: ${existingPenalties?.length || 0}`);

        if (existingPenalties && existingPenalties.length > 0) {
          // Update existing penalty
          const existingPenalty = existingPenalties[0];
          console.log(`  Updating existing penalty ID: ${existingPenalty.penalty_fee_id}`);

          await this.PenaltyFeeYearWiseRepository.updatePenaltyFee(
            existingPenalty.penalty_fee_id,
            {
              actual_value: actualValue,
              tax_percentage: taxPercentage, // Update to 2%
              // penalty_value will be calculated in the repository
            },
          );
          console.log(`  ✅ Updated existing penalty for property ${propertyNumber} with actual value: ${actualValue}`);
        } else {
          // Create new penalty
          console.log(`  Creating new penalty record`);

          await this.PenaltyFeeYearWiseRepository.savePenaltyFee({
            property: propertyId,
            financial_year: currentFinancialYear.financial_year_range,
            actual_value: actualValue,
            tax_percentage: taxPercentage, // Set to 2%
            // penalty_value will be calculated in the repository
          });
          console.log(`  ✅ Created new penalty for property ${propertyNumber} with actual value: ${actualValue}`);
        }

        return {
          success: true,
          message: 'January 1st penalty calculation completed successfully',
          data: {
            propertyId,
            propertyNumber,
            remainingAmount,
            penaltyAmount,
            penaltyCreated: true,
            action: existingPenalties && existingPenalties.length > 0 ? 'updated' : 'created',
            financialYear: currentFinancialYear.financial_year_range
          },
          timestamp: new Date()
        };
      } else {
        console.log('\n⚠️  NO JANUARY 1ST PENALTY REQUIRED:');
        console.log(`  Remaining amount is ${remainingAmount}, no penalty needed`);

        return {
          success: true,
          message: 'No penalty needed - no remaining amount',
          data: {
            propertyId,
            propertyNumber,
            remainingAmount: 0,
            penaltyAmount: 0,
            penaltyCreated: false,
            action: 'none',
            financialYear: currentFinancialYear.financial_year_range
          },
          timestamp: new Date()
        };
      }
    } catch (error) {
      console.log(`❌ ERROR in January 1st penalty calculation: ${error.message}`);
      this.logger.error(`Error calculating January 1st penalties for property ${propertyId}: ${error.message}`, error.stack);

      return {
        success: false,
        message: 'January 1st penalty calculation failed',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Health check for cron job system
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    failedJobsCount: number;
    lastSuccessfulRun?: Date;
    issues: string[];
  } {
    const failedJobsCount = this.failedJobs.size;
    const issues: string[] = [];

    // Check for critical issues
    const criticalJobs = Array.from(this.failedJobs.values()).filter(job =>
      job.jobName === 'january_first_penalties' && job.attempts >= 3
    );

    if (criticalJobs.length > 0) {
      issues.push(`${criticalJobs.length} critical January 1st penalty jobs failed multiple times`);
    }

    // Check for old failed jobs
    const oldJobs = Array.from(this.failedJobs.values()).filter(job => {
      const daysSinceFailure = (Date.now() - job.lastAttempt.getTime()) / (1000 * 60 * 60 * 24);
      return daysSinceFailure > 7;
    });

    if (oldJobs.length > 0) {
      issues.push(`${oldJobs.length} jobs have been failed for more than 7 days`);
    }

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    if (criticalJobs.length > 0) {
      status = 'critical';
    } else if (failedJobsCount > 0 || oldJobs.length > 0) {
      status = 'warning';
    }

    return {
      status,
      failedJobsCount,
      issues
    };
  }

  // Method to handle the backup and send process
  private async backupAndSend() {
    const backupDir = path.join(__dirname, 'database_backup');
    const backupFilePath = path.join(backupDir, 'full_db_backup.sql');

    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const dbHost = process.env.DB_HOST;
    const dbPort: number = parseInt(process.env.DB_PORT, 10);
    const dbName = process.env.DB_NAME;
    const dbUser = process.env.DB_USERNAME;
    const dbPassword = process.env.DB_PASSWORD;

    // Use 'pg_dump' directly (assumes it's in PATH)
    const pgDumpPath = '/usr/lib/postgresql/14/bin/pg_dump'; // or '/usr/lib/postgresql/14/bin/pg_dump'

    const dumpCommand = `pg_dump -U ${dbUser} -h ${dbHost} -p ${dbPort} -F c -b -v -f "${backupFilePath}" "${dbName}"`;

    exec(
      dumpCommand,
      { env: { ...process.env, PGPASSWORD: dbPassword } },
      (error, stdout, stderr) => {
        if (error) {
          this.logger.error(`Error during database backup: ${error.message}`);
          console.log('Error in exec:', error);
          return;
        }

        if (stderr && !stderr.includes('reading extensions')) {
          console.log('Error in stderr:', stderr);
          this.logger.error(`stderr: ${stderr}`);
          return;
        }

        console.log('Database backup completed:', stdout);
        this.logger.log(`Database backup completed: ${stdout}`);

        console.log('Sending backup file:', backupFilePath);
        this.sendBackupFileToEmail(backupFilePath);
      },
    );
  }

    private async sendBackupFileToEmail(filePath: string) {
    try {
      const stats = fs.statSync(filePath);
      const fileSizeInMB = stats.size / (1024 * 1024);
      console.log(`File size: ${fileSizeInMB.toFixed(2)} MB`);

      if (fileSizeInMB > 100) {
        console.warn('File is too large to upload.');
        return;
      }

      const fileContent = fs.readFileSync(filePath);
      console.log(" this.emailService.", this.emailService)
      await this.emailService.sendEmailWithAttachment(
      '<EMAIL>',
      'SPMS Database Backup',
      'Please find the database backup attached.',
      '<p>Please find the database backup attached.</p>',
      [
        {
          filename: path.basename(filePath),
          content: fileContent,
        },
      ],
    );
      console.log('Backup file sent successfully');
    } catch (error) {
      console.error('Error during email send:', error.message);
    }
  }

  // private async sendBackupFileToEndpoint(filePath: string) {
  //   const url = new URL(`${process.env.PROD_DB_BACKUP_URL}`);
  //   // const url='https://n8n.onpointsoft.com/webhook-test/spms-db-backup';
  //   try {
  //     const stats = fs.statSync(filePath);
  //     const fileSizeInMB = stats.size / (1024 * 1024);
  //     console.log(`File size: ${fileSizeInMB.toFixed(2)} MB`);


  //     // Optional: Prevent upload if file is too large
  //     if (fileSizeInMB > 100) {
  //       // example: 100 MB limit
  //       console.warn('File is too large to upload.');
  //       return;
  //     }

  //     const formData = new FormData();
  //     formData.append('file', fs.createReadStream(filePath));

  //     console.log('Sending backup file to endpoint...');

  //     const response = await axios.post(url.toString(), formData, {
  //       headers: {
  //         ...formData.getHeaders(),
  //       },
  //       maxContentLength: Infinity,
  //       maxBodyLength: Infinity,
  //     } as any);
  //     console.log('Response data:', response.data);
  //     this.logger.log('Backup file sent successfully');
  //   } catch (error) {
  //     console.error('Error during file upload:', error.message);
  //     this.logger.error(`Error sending backup file: ${error.message}`);
  //   }
  // }

  // private async sendBackupFile(filePath: string) {
  //   // Create a Nodemailer transporter
  //   const transporter = nodemailer.createTransport({
  //     service: 'gmail',
  //     auth: {
  //       user: '<EMAIL>',
  //       pass: 'your-email-password',
  //     },
  //   });

  //   // Send the email with the backup file attached
  //   const mailOptions = {
  //     from: '<EMAIL>',
  //     to: '<EMAIL>',
  //     subject: 'Database Backup',
  //     text: 'Please find the attached database backup file.',
  //     attachments: [
  //       {
  //         filename: 'full_db_backup.sql',
  //         path: filePath,
  //       },
  //     ],
  //   };

  //   await transporter.sendMail(mailOptions);
  // }

  // private async uploadToCloudStorage(filePath: string) {
  //   const s3 = new AWS.S3({
  //     accessKeyId: 'YOUR_ACCESS_KEY',
  //     secretAccessKey: 'YOUR_SECRET_KEY',
  //     region: 'YOUR_REGION',
  //   });

  //   const fileContent = fs.readFileSync(filePath);

  //   const params = {
  //     Bucket: 'YOUR_BUCKET_NAME',
  //     Key: 'full_db_backup.sql',
  //     Body: fileContent,
  //   };

  //   await s3.upload(params).promise();
  // }
}
