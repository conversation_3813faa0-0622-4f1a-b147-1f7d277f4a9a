import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
} from '@nestjs/common';

import {
  AdminBoundaryMasterDto,
  CreateAdminBoundaryMasterDto,
  UpdateAdminBoundaryMasterDto,
} from './dto/admin-boundary_master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AdminBoundaryMasterService } from './admin-boundary_master.service';

@ApiTags('admin-boundary-master')
@Controller('adminstrativeBoundary-master')
export class AdminBoundaryMasterController {
  private readonly logger = new Logger(AdminBoundaryMasterController.name);
  constructor(
    private readonly adminBoundaryMasterService: AdminBoundaryMasterService,
  ) {}

  @ApiOperation({ summary: 'Create a new Administrative Boundary Master' })
  @ApiResponse({
    status: 201,
    description:
      'The Administrative Boundary Master has been successfully created',
  })
  @Post()
  create(@Body() createAdminBoundaryMasterDto: CreateAdminBoundaryMasterDto) {
    return this.adminBoundaryMasterService.create(createAdminBoundaryMasterDto);
  }

  @ApiOperation({ summary: 'Get all Administrative Boundary Master' })
  @ApiResponse({
    status: 200,
    description: 'Returns all Administrative Boundary Master',
  })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.adminBoundaryMasterService.findAll();
  }

  @ApiOperation({ summary: 'Get one location' })
  @ApiResponse({
    status: 200,
    description: 'Returns Single Administrative Boundary Master',
  })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() adminMasterDto: AdminBoundaryMasterDto) {
    return this.adminBoundaryMasterService.findOne(adminMasterDto);
  }

  @ApiOperation({ summary: 'Update a Administrative Boundary Master by ID' })
  @ApiResponse({
    status: 200,
    description:
      'The Administrative Boundary Master has been successfully updated',
  })
  @ApiResponse({
    status: 404,
    description: 'Administrative Boundary Master not found',
  })
  @Patch()
  update(
    @Query() adminMasterDto: AdminBoundaryMasterDto,
    @Body() updateAdminBoundaryMasterDto: UpdateAdminBoundaryMasterDto,
  ) {
    return this.adminBoundaryMasterService.update(
      adminMasterDto,
      updateAdminBoundaryMasterDto,
    );
  }

  @ApiOperation({ summary: 'Delete a Administrative Boundary Master by ID' })
  @ApiResponse({
    status: 200,
    description:
      'The Administrative Boundary Master has been successfully deleted',
  })
  @ApiResponse({
    status: 404,
    description: 'Administrative Boundary Master not found',
  })
  @Delete()
  remove(@Query() adminMasterDto: AdminBoundaryMasterDto) {
    return this.adminBoundaryMasterService.remove(adminMasterDto);
  }
}
