import { MigrationInterface, QueryRunner } from "typeorm";

export class CreatePaidAndNonPaidDataTable1750000000000 implements MigrationInterface {
    name = 'CreatePaidAndNonPaidDataTable1750000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the "paid_and_non_paid_data" table already exists before creating it
        const tableExists = await queryRunner.hasTable("paid_and_non_paid_data");
        
        if (!tableExists) {
            await queryRunner.query(`
                CREATE TABLE "paid_and_non_paid_data" (
                    "paid_and_non_paid_data_id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                    "financial_year_range" character varying NOT NULL,
                    "name" character varying,
                    "property_number" character varying NOT NULL,
                    "old_property_number" character varying,
                    "total_tax" numeric(10,2) NOT NULL DEFAULT 0,
                    "paid" numeric(10,2) NOT NULL DEFAULT 0,
                    "remaining" numeric(10,2) NOT NULL DEFAULT 0,
                    "is_paid" boolean NOT NULL DEFAULT false,
                    "property_id" uuid,
                    "financial_year_id" uuid,
                    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                    "deleted_at" TIMESTAMP,
                    CONSTRAINT "PK_paid_and_non_paid_data" PRIMARY KEY ("paid_and_non_paid_data_id")
                )
            `);

            // Add foreign key constraints
            await queryRunner.query(`
                ALTER TABLE "paid_and_non_paid_data" 
                ADD CONSTRAINT "FK_paid_and_non_paid_data_property" 
                FOREIGN KEY ("property_id") 
                REFERENCES "property"("property_id") 
                ON DELETE CASCADE ON UPDATE NO ACTION
            `);

            await queryRunner.query(`
                ALTER TABLE "paid_and_non_paid_data" 
                ADD CONSTRAINT "FK_paid_and_non_paid_data_financial_year" 
                FOREIGN KEY ("financial_year_id") 
                REFERENCES "financial_year"("financial_year_id") 
                ON DELETE CASCADE ON UPDATE NO ACTION
            `);

            // Add indexes for better performance
            await queryRunner.query(`
                CREATE INDEX "IDX_paid_and_non_paid_data_financial_year_range" 
                ON "paid_and_non_paid_data"("financial_year_range")
            `);

            await queryRunner.query(`
                CREATE INDEX "IDX_paid_and_non_paid_data_is_paid" 
                ON "paid_and_non_paid_data"("is_paid")
            `);

            await queryRunner.query(`
                CREATE INDEX "IDX_paid_and_non_paid_data_property_number" 
                ON "paid_and_non_paid_data"("property_number")
            `);

            // Add unique constraint to prevent duplicate entries for same property and financial year
            await queryRunner.query(`
                CREATE UNIQUE INDEX "IDX_paid_and_non_paid_data_unique_property_fy" 
                ON "paid_and_non_paid_data"("property_id", "financial_year_range") 
                WHERE "deleted_at" IS NULL
            `);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes first
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_paid_and_non_paid_data_unique_property_fy"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_paid_and_non_paid_data_property_number"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_paid_and_non_paid_data_is_paid"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_paid_and_non_paid_data_financial_year_range"`);
        
        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "paid_and_non_paid_data" DROP CONSTRAINT IF EXISTS "FK_paid_and_non_paid_data_financial_year"`);
        await queryRunner.query(`ALTER TABLE "paid_and_non_paid_data" DROP CONSTRAINT IF EXISTS "FK_paid_and_non_paid_data_property"`);
        
        // Drop the table
        await queryRunner.query(`DROP TABLE IF EXISTS "paid_and_non_paid_data"`);
    }
}
