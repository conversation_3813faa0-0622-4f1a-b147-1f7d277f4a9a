import {
  BaseEntity,
  Column,
  CreateDateC<PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PaymentInfoEntity, PropertyEntity } from './index';

@Entity('paid_data')
export class PaidDataEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  paid_data_id: string;

  @OneToOne(() => PaymentInfoEntity, (paymentInfo) => paymentInfo.paidData, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'payment_info_id' })
  paymentInfo: PaymentInfoEntity;

  @ManyToOne(() => PropertyEntity, (property) => property.property_id, {
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

  @Column({ type: String, nullable: true })
  property_number: string;

  @Column({ type: 'float', nullable: true, default: 0 })
  all_property_tax_sum: number;
  @Column({ type: 'float', nullable: true, default: 0 })
  all_property_tax_sum_prev: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  all_property_tax_sum_curr: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  total_amount: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_1: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_1_prev: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_1_curr: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_2: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_2_prev: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_2_curr: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_3: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_3_prev: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_3_curr: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_4: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_4_prev: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_4_curr: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_5: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_5_prev: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_5_curr: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_6: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_6_prev: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_6_curr: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_7: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_7_prev: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_7_curr: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_8: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_8_prev: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_8_curr: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_9: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_9_prev: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_9_curr: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_10: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_10_prev: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_10_curr: number;


    @Column({ type: 'float', nullable: true, default: 0 })
    other_tax_sum_tax: number;
    @Column({ type: 'float', nullable: true, default: 0 })
    other_tax_sum_tax_prev: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    other_tax_sum_tax_curr: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    other_discount: string;


    @Column({ type: String, nullable: true })
    status: string;


    @Column({ type: String, nullable: false })
    financial_year: string;

    @Column({ type: String, nullable: true })
    remaining_amount: string;

    @Column({ type: 'jsonb', nullable: true, default: '{}' })
    year_wise_penalty_data: Record<string, any>;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
