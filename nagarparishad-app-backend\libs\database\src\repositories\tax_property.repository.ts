import { Repository } from 'typeorm';
import { PropertyEntity, Tax_PropertyEntity, Tax_PropertyWiseEntity,Tax_PropertyEntity_Other_Taxes } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import {
  PaginatedResult,
  PaginationOptions,
  paginate,
} from '@helper/helpers/Pagination';
import { Logger } from '@nestjs/common';

export class Tax_PropertyRepository extends Repository<Tax_PropertyEntity> {
  constructor(
    @InjectRepository(Tax_PropertyEntity)
    private readonly tax_PropertyRepository: Repository<Tax_PropertyEntity>,
  ) {
    super(
      tax_PropertyRepository.target,
      tax_PropertyRepository.manager,
      tax_PropertyRepository.queryRunner,
    );
  }

 

 

  async findAllData() {
    return await this.tax_PropertyRepository
      .createQueryBuilder('tax_property')
      .select([
        'tax_property.tax_property_id',
        'tax_property.bill_no',
        'property.propertyNumber',              
        'zone.zoneName',       
        'tax_property.total_tax',
        'ownerDetails.name',  
        'ownerDetails.owner_type_id',  
        'ownerDetails.property_owner_details_id',
        'owner_type.owner_type',
        'street.street_name',      
      ])
      .leftJoin('tax_property.property', 'property')
      .leftJoin('property.zone', 'zone')  
      .leftJoin('property.property_owner_details', 'ownerDetails')
      .leftJoin('ownerDetails.owner_type', 'owner_type') // 
      .leftJoin('property.street', 'street')            
      .orderBy('tax_property.updated_at', 'DESC')
      .getMany();
  }

  
  //  async get_data_for_bill(bill_no: string) {
  //   try {
  //     return await this.tax_PropertyRepository
  //       .createQueryBuilder('tax_property')
  //       .select([
  //         'tax_property.tax_property_id',
  //         'tax_property.bill_no',
  //         'tax_property.bill_generation_date',
  //         'ward.ward_name',
  //         'property_master.firstname',
  //         'property_master.lastname',
  //         'property_master.plot_number', 
  //         'property_master.block_number',          
  //         'property_master.house_number', 
  //         'property_master.house_or_apartment_name', 
  //         'property_master.landmark',  
  //         'property_master.city', 
  //         'property_master.old_propertyNumber',          
  //         'property_master.propertyNumber',
          
  //         'zone.zoneName',
  
  //         'tax_property.all_property_tax_sum', // sankalit kar (gharpatti)
  //         'tax_property.tree_tax',   // vrukh kar
  //         'tax_property.educational_tax',  //shikhan kar
  //         'tax_property.employment_tax',   //roj hami kar
  //         'tax_property.fire_ext_tax',   //agnishamak kar
  //         'tax_property.trash_tax',   // ghamnakachara kar
  //         'tax_property.all_property_tax_sum',    //other //0
  //         'tax_property.punishment_tax',    //shasti kar
  //         'tax_property.total_tax'   //total_tax
  //       ])
  //       .addSelect("CONCAT(property_master.firstname, ' ', property_master.lastname)", 'name')
  //       .addSelect("CONCAT(property_master.plot_number, ' ', property_master.block_number)", 'address')
  //       .leftJoin('tax_property.propertyMaster', 'property_master')
  //       .leftJoin('property_master.zone', 'zone') 
  //       .leftJoin('property_master.ward', 'ward')             
  //       .where('tax_property.bill_no = :bill_no', { bill_no })      
  //       .orderBy('tax_property.updated_at', 'DESC')
  //       .getOne();
  //   } catch (error) {
  //     throw error;
  //   }
  // }

 async get_data_for_bill_old(bill_no: string) {
   try {
          return await this.tax_PropertyRepository
            .createQueryBuilder('tax_property')
            .select([
              'tax_property.tax_property_id',
              'tax_property.bill_no',
              'tax_property.bill_generation_date',
              'ward.ward_name',      
              'property.address',        
              'property.house_or_apartment_name',           
              'property.old_propertyNumber',          
              'property.propertyNumber',
              'property.city_survey_number', 
              'property.note'
,             'street.street_name',         
              'zone.zoneName',  
              'tax_property.all_property_tax_sum', // sankalit kar (gharpatti)
              'tax_property.other_tax_sum_tax',    //other //0        
              'tax_property.total_tax',   //total_tax
              'tax_propertywise.tax_value',
              'tax_propertywise.tax',
              'tax_property_other_taxes.tax_type',
              'tax_property_other_taxes.amount',    
              'ownerDetails.name',  
              'ownerDetails.owner_type_id',  
              'ownerDetails.property_owner_details_id',
              'owner_type.owner_type',
              'property_Usage_Details.length',
              'property_Usage_Details.width',
              'property_Usage_Details.are_sq_meter',

              'propertyType.propertyType',
            
    
            ])      
            .leftJoin('tax_property.property', 'property')    
            .leftJoin('tax_property.tax_propertywise', 'tax_propertywise') // Join tax_propertywise  
            .leftJoin('tax_property.tax_property_other_taxes', 'tax_property_other_taxes')
            .leftJoin('property.zone', 'zone') 
            .leftJoin('property.ward', 'ward')
            .leftJoin('property.street', 'street')  
            .leftJoin('property.property_owner_details', 'ownerDetails')
            .leftJoin('ownerDetails.owner_type', 'owner_type') //
            .leftJoin('tax_propertywise.property_Usage_Details', 'property_Usage_Details')  
            .leftJoin('property_Usage_Details.propertyType', 'propertyType') 
            .where('tax_property.bill_no = :bill_no', { bill_no })      
            .orderBy('tax_property.updated_at', 'DESC')
            .getOne();
        } catch (error) {
          throw error;
        }
  }

  async get_data_for_bill_old_old(bill_no: string) {
    try {
           return await this.tax_PropertyRepository
             .createQueryBuilder('tax_property')
             .select([
               'tax_property.tax_property_id',
               'tax_property.bill_no',
               'tax_property.bill_generation_date',
               'ward.ward_name',      
               'property.address',        
               'property.house_or_apartment_name',           
               'property.old_propertyNumber',          
               'property.propertyNumber',
               'property.city_survey_number', 
               'property.note'
 ,             'street.street_name',         
               'zone.zoneName',  
               'tax_property.all_property_tax_sum', // sankalit kar (gharpatti)
               'tax_property.other_tax_sum_tax',    //other //0        
               'tax_property.total_tax',   //total_tax
               
               'tax_property_other_taxes.tax_type',
               'tax_property_other_taxes.amount',    
               'ownerDetails.name',  
               'ownerDetails.owner_type_id',  
               'ownerDetails.property_owner_details_id',
               'owner_type.owner_type',
    
 
          
             
     
             ])      
             .leftJoin('tax_property.property', 'property')              
             .leftJoin('tax_property.tax_property_other_taxes', 'tax_property_other_taxes')
             .leftJoin('property.zone', 'zone') 
             .leftJoin('property.ward', 'ward')
             .leftJoin('property.street', 'street')  
             .leftJoin('property.property_owner_details', 'ownerDetails')
             .leftJoin('ownerDetails.owner_type', 'owner_type')            
          
             .where('tax_property.bill_no = :bill_no', { bill_no })      
             .orderBy('tax_property.updated_at', 'DESC')
             .getOne();
         } catch (error) {
           throw error;
         }
   }


   
 

   
  async milkat_kar() {
    const bill_no = "1";
    try {
      return await this.tax_PropertyRepository
        .createQueryBuilder('tax_property')
        .select([
          'tax_property.tax_property_id',
          'tax_property.bill_no',
          'tax_property.bill_generation_date',
          'ward.ward_name',      
          'property.address',        
          'property.house_or_apartment_name',           
          'property.old_propertyNumber',          
          'property.propertyNumber',          
          'zone.zoneName',  
          'tax_property.all_property_tax_sum', // sankalit kar (gharpatti)
          // 'tax_property.tree_tax',   // vrukh kar
          // 'tax_property.educational_tax',  //shikhan kar
          // 'tax_property.employment_tax',   //roj hami kar
          // 'tax_property.fire_ext_tax',   //agnishamak kar
          // 'tax_property.trash_tax',   // ghamnakachara kar
          'tax_property.other_tax_sum_tax',    //other //0        
          'tax_property.total_tax',   //total_tax
          'tax_propertywise.sq_ft_meter',
          'tax_propertywise.rr_rate',
          'tax_propertywise.rr_construction_rate',
          'tax_propertywise.depreciation_rate',
          'tax_propertywise.weighting',
          'tax_propertywise.capital_value',
          'tax_propertywise.tax_value',
          'tax_propertywise.tax',

          'tax_property_other_taxes.tax_type',
          'tax_property_other_taxes.amount',
          'property_usage_Details.usage_type_id'

        

        ])      
        .leftJoin('tax_property.property', 'property')    
        .leftJoin('tax_property.tax_propertywise', 'tax_propertywise') // Join tax_propertywise  
        .leftJoin('tax_propertywise.property_usage_Details', 'property_usage_Details')  
        .leftJoin('tax_property.tax_property_other_taxes', 'tax_property_other_taxes')
        .leftJoin('property.zone', 'zone') 
        .leftJoin('property.ward', 'ward')             
        .where('tax_property.bill_no = :bill_no', { bill_no })      
        .orderBy('tax_property.updated_at', 'DESC')
        .getOne();
    } catch (error) {
      throw error;
    }
  }

  // async milkat_kar(ward_id: string, property_number:string) {
  //   try {
  //     return {
  //       a:"a"
  //     }
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  
  async get_data_for_nanuma8_old(bill_no: string) {
    try {
      return await this.tax_PropertyRepository
        .createQueryBuilder('tax_property')
        .select([
          'tax_property.tax_property_id',
          'tax_property.bill_no',
          'tax_property.bill_generation_date',
          'ward.ward_name',      
          'property.address',
          'property.gat_no',       
          'property.house_or_apartment_name',           
          'property.old_propertyNumber',          
          'property.propertyNumber',
          'property.city_survey_number', 
          'property.note'
,             'street.street_name',         
          'zone.zoneName',  
          'tax_property.all_property_tax_sum', // sankalit kar (gharpatti)
          'tax_property.other_tax_sum_tax',    //other //0        
          'tax_property.total_tax',   //total_tax
          
          'tax_property_other_taxes.tax_type',
          'tax_property_other_taxes.amount',
          
          'tax_propertywise.sq_ft_meter',
          'tax_propertywise.rr_rate',
          'tax_propertywise.rr_construction_rate',
          'tax_propertywise.depreciation_rate',
          'tax_propertywise.weighting',
          'tax_propertywise.capital_value',
          'tax_propertywise.tax_value',
          'tax_propertywise.tax',
         
          'property_usage_Details.construction_end_date',
          'property_usage_Details.are_sq_ft',
          'property_usage_Details.are_sq_meter',
          'property_usage_Details.construction_area',
          'property_usage_Details.construction_start_year',

          'ownerDetails.name',  
          'ownerDetails.owner_type_id',  
          'ownerDetails.property_owner_details_id',
          'owner_type.owner_type',
          'propertyType.propertyType'


     
        

        ])      
        .leftJoin('tax_property.property', 'property')              
        .leftJoin('tax_property.tax_property_other_taxes', 'tax_property_other_taxes')        
        .leftJoin('tax_property.tax_propertywise', 'tax_propertywise') // Join tax_propertywise  
        .leftJoin('tax_propertywise.property_usage_details', 'property_usage_Details')
        .leftJoin('property_usage_Details.propertyType', 'propertyType')
        .leftJoin('property.zone', 'zone') 
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.street', 'street')  
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('ownerDetails.owner_type', 'owner_type')            
     
        .where('tax_property.bill_no = :bill_no', { bill_no })      
        .orderBy('tax_property.updated_at', 'DESC')
        .getOne();
    } catch (error) {
      throw error;
    }
  }

 

}
