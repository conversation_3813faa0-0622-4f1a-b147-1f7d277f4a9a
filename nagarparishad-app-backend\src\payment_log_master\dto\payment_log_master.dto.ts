import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsNumber,
  IsString,
  IsOptional,
  IsNotEmpty,
  IsEnum,
} from 'class-validator';

export enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
}


export class CreatePaymentDto {
  @ApiProperty({ name: 'property_id', type: 'string' })
  @IsString()
  @IsNotEmpty()
  property_id: string;

  @ApiProperty({ name: 'property_number', type: 'string' })
  @IsString()
  @IsNotEmpty()
  property_number: string;

  @ApiProperty({ name: 'financial_year', type: 'string' })
  @IsString()
  @IsNotEmpty()
  financial_year: string;

  @ApiProperty({ name: 'all_property_tax_sum_total', type: 'number' })
  @IsNumber()
  @IsNotEmpty()
  all_property_tax_sum_total: number;

  @ApiProperty({ name: 'tax_type_1', type: 'number', required: false })
  @IsNumber()
  @IsOptional()
  tax_type_1?: number;

  @ApiProperty({ name: 'tax_type_2', type: 'number', required: false })
  @IsNumber()
  @IsOptional()
  tax_type_2?: number;

  @ApiProperty({ name: 'tax_type_3', type: 'number', required: false })
  @IsNumber()
  @IsOptional()
  tax_type_3?: number;

  @ApiProperty({ name: 'tax_type_4', type: 'number', required: false })
  @IsNumber()
  @IsOptional()
  tax_type_4?: number;

  @ApiProperty({ name: 'tax_type_5', type: 'number', required: false })
  @IsNumber()
  @IsOptional()
  tax_type_5?: number;

  @ApiProperty({ name: 'tax_type_6', type: 'number', required: false })
  @IsNumber()
  @IsOptional()
  tax_type_6?: number;

  @ApiProperty({ name: 'tax_type_7', type: 'number', required: false })
  @IsNumber()
  @IsOptional()
  tax_type_7?: number;

  @ApiProperty({ name: 'tax_type_8', type: 'number', required: false })
  @IsNumber()
  @IsOptional()
  tax_type_8?: number;

  @ApiProperty({ name: 'tax_type_9', type: 'number', required: false })
  @IsNumber()
  @IsOptional()
  tax_type_9?: number;

  @ApiProperty({ name: 'tax_type_10', type: 'number', required: false })
  @IsNumber()
  @IsOptional()
  tax_type_10?: number;

  @ApiProperty({ name: 'other_tax_sum_tax', type: 'number', required: false })
  @IsNumber()
  @IsOptional()
  other_tax_sum_tax?: number;

  @ApiProperty({ name: 'total_tax', type: 'number' })
  @IsNumber()
  @IsNotEmpty()
  total_tax: number;


  @ApiProperty({ name: 'payment_mode', enum: PaymentStatus })
  @IsEnum(PaymentStatus)
  @IsNotEmpty()
  payment_mode: PaymentStatus;

  @ApiProperty({ name: 'book_number', type: 'string', required: false })
  @IsString()
  @IsOptional()
  book_number?: string;
  @ApiProperty({ name: 'book_number', type: 'string', required: false })
  @IsString()
  @IsOptional()
  remaining_amount?: string;

  @ApiProperty({ name: 'additional_notes', type: 'string', required: false })
  @IsString()
  @IsOptional()
  additional_notes?: string;
}

export class PaymentDto {
  payment_id: string;
  tax_payer_name: string | null;
  payment_date: string;
}

export class OwnerDto {
  owner_id: string;
  owner_name: string;
  owner_type?: string;
}

export class PropertyGroupDto {
  property_id: string;
  property_number: string;
  owner?: OwnerDto;
  payments: PaymentDto[];
}
