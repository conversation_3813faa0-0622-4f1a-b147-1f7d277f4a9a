import WhiteContainer from "@/components/custom/WhiteContainer";
import React, { useContext, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ArrowUpDown } from "lucide-react";

import { ColumnDef } from "@tanstack/react-table";
import { MasterObjectColumnDef } from "@/model/zone-master";
import { MASTER } from "@/constant/config/api.config";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { GlobalContext } from "@/context/GlobalContext";
import WardPopupForm from "../WardPopupForm";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { toast } from "@/components/ui/use-toast";
import AddNewBtn from "../../globalcomponent/AddNewForm";
import { WardUpdateObject } from "@/model/ward-master";
import { ResponseData } from "@/model/auth/authServices";
import PropertyClassMasterForm from "../PropertyClassMasterForm";
import { PropertyClassMasterObject } from "@/model/PropertyClassMaster";
import { usePropertyClassMasterController } from "@/controller/master/PropertyClassController";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

const PropertyClassMaster = () => {
  const { t } = useTranslation();
  const { propertyClassList } = usePropertyClassMasterController();
  const { setMasterComponent, setOpen } = useContext(GlobalContext);

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Master, FormName.PropertyClassMaster, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Master, FormName.PropertyClassMaster, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Master, FormName.PropertyClassMaster, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Master, FormName.PropertyClassMaster, Action.CanDelete);

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<PropertyClassMasterObject | null>(
    null,
  );
  const { deletePropertyClass, propertyClassLoading} = usePropertyClassMasterController(); // Assuming you have a deleteWardMutation function from your controller
  const dynamicValues = {
    name: t("ward.wardLabel"),
  };
 const propertyClassLists= propertyClassList?.data ||[]
  function handleEdit(item: any): void {
    setOpen(true);
    setMasterComponent(
      <PropertyClassMasterForm btnTitle={"update"} editData={item && item} />,
    );
  }

  function handleDelete(item: any): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }
  const handleConfirmDelete = () => {
    if (selectedItem) {
      deletePropertyClass(selectedItem.property_type_class_id, {
        onSuccess: (response: ResponseData) => {
          if (response.statusCode && response.statusCode === 200) {
            toast({
              title: t("api.formdelete", dynamicValues),
              variant: "success",
            });
          } else {
            toast({
              title: response.message,
              variant: "destructive",
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const columns: ColumnDef<MasterObjectColumnDef>[] = [
    {
      accessorKey: `${t("SrNo")}`,
      header: `${t("SrNo")}`,
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: "property_type_class",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("मालमत्ता प्रकार विभाग")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
     cell: ({ row }: { row: any })  => <div className="">{row.original?.property_type_class}</div>,
    },
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: `${t("Actions")}`,
            enableHiding: true,
            cell: ({ row }: { row: any }) => (
              <div className="flex space-x-2">
                {canUpdate && (
                  <Button
                    variant="submit"
                    className="h-8 w-8 p-0 justify-center"
                    onClick={() => handleEdit(row?.original)}
                  >
                    {t("table.edit")}
                  </Button>
                )}
                {/* {CanDelete && (
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0 justify-center ml-2  !border-BlueText !text-BlueText"
                    onClick={() => handleDelete(row?.original)}
                  >
                    {t("table.delete")}
                  </Button>
                )} */}
              </div>
            ),
          },
        ]
      : []),
  ];

  const MasterType: string = MASTER.PROPERTYCLASS;

  return (
    <div className="w-full h-fit p-6">
      <p className="w-full flex items-center justify-between ml-2 text-2xl font-semibold mb-2">
        {t("मालमत्ता प्रकार विभाग	")}
      </p>
      <WhiteContainer>
        {MasterType && CanCreate && <AddNewBtn masterType={MASTER.PROPERTYCLASS} />}
      </WhiteContainer>
      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={propertyClassList}
          masterType={MASTER.PROPERTYCLASS}
          searchKey={"मालमत्ता प्रकार विभाग	"}
          searchColumn={"property_type_class"}
          loader={propertyClassLoading ? true : false}

        />
      </WhiteContainer>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={selectedItem.property_type_class}
          onDelete={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default PropertyClassMaster;
