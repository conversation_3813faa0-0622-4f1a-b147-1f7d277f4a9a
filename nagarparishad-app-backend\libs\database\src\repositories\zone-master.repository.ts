import { Repository } from 'typeorm';
import { Ward_Master, ZoneMaster } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class ZoneMasterRepository extends Repository<ZoneMaster> {
  constructor(
    @InjectRepository(ZoneMaster)
    private readonly zoneMasterRepository: Repository<ZoneMaster>,
  ) {
    super(
      zoneMasterRepository.target,
      zoneMasterRepository.manager,
      zoneMasterRepository.queryRunner,
    );
  }

  async saveZone(input: any) {
    let zone = this.zoneMasterRepository.create(input);
    zone = await this.zoneMasterRepository.save(zone);
    return zone;
  }

  async findAllZone() {
    return await this.zoneMasterRepository
      .createQueryBuilder('zone_master')
      .select([
        'zone_master.zone_id',
        'zone_master.zoneName',
     
      ])      
      .orderBy('zone_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string) {
    return await this.zoneMasterRepository
      .createQueryBuilder('zone_master')   
      .where('zone_master.zone_id = :zone_id', { zone_id: id })
      .getOne();
  }

  async updateZone(id: string, input: any) {
    return await this.zoneMasterRepository
      .createQueryBuilder('zone-master')
      .update(ZoneMaster)
      .set(input)
      .where('zone_id = :zone_id', { zone_id: id })
      .execute();
  }

  async deleteZone(id: string) {
    return await this.zoneMasterRepository
      .createQueryBuilder('zone-master')
      .softDelete()
      .where('zone_id = :zone_id', { zone_id: id })
      .execute();
  }

  async getData(id: string) {
    return await this.zoneMasterRepository
      .createQueryBuilder('zone_master')
      .select(['zone_master.zone_id', 'zone_master.zoneName'])    
      .getMany();
  }
}
