import {
  BaseEntity,
  Column,
  CreateDateC<PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { RoleMasterEntity } from './roleMaster.entity';
import { FormMasterEntity } from './formMaster.entity';

@Entity('rolewise_form_permission')
export class RolewiseFormPermissionEntity extends BaseEntity {
  @PrimaryGeneratedColumn()
  action_id: number;

  @ManyToOne(() => RoleMasterEntity, (role) => role.role_id)
  @JoinColumn({ name: 'role_id' })
  role: RoleMasterEntity;

  @ManyToOne(() => FormMasterEntity, (form) => form.form_id)
  @JoinColumn({ name: 'form_id' })
  form: FormMasterEntity;

  @Column({ type: 'boolean', default: false })
  can_read: boolean;

  @Column({ type: 'boolean', default: false })
  can_write: boolean;

  @Column({ type: 'boolean', default: false })
  can_update: boolean;

  @Column({ type: 'boolean', default: false })
  can_delete: boolean;

  @Column({ type: 'boolean', default: true })
  is_valid: boolean;

  /*
   * Create and Update Date Columns
   */
  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
