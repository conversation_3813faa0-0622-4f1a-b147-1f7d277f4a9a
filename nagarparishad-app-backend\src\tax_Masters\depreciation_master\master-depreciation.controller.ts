import { Controller, Get, Post, Put, Delete, Query, Body, UseGuards } from '@nestjs/common';
import { MasterDepreciationService } from './master_depreciation.service';
import { Master_depreciation_rate } from 'libs/database/entities';
import { CreateDepreciationRateDto } from './dto/create-depreciation-rate.dto';
import { UpdateDepreciationRateDto } from './dto/update-depreciation-rate.dto';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@Controller('master-depreciation')
export class MasterDepreciationController {
  constructor(private readonly depreciationService: MasterDepreciationService) {}

  
  @Form('Deprecation Rate')
  @Permissions('can_write')
  @Post('create')
  async create(
    @Body() createDto: CreateDepreciationRateDto
  ): Promise<{ message: string; data: Master_depreciation_rate }> {
    return this.depreciationService.create(createDto);
  }

  @Form('Deprecation Rate')
  @Permissions('can_read')
  @Get()
  async findAll(): Promise<{ message: string; data: Master_depreciation_rate[] }> {
    return this.depreciationService.findAll();
  }

  @Form('Deprecation Rate')
  @Permissions('can_read')
  @Get('findOne')
  async findOne(@Query('id') id: string): Promise<{ message: string; data: Master_depreciation_rate }> {
    return this.depreciationService.findOne(id);
  }

  @Form('Deprecation Rate')
  @Permissions('can_update')
  @Put('update')
  async update(
    @Query('id') id: string,
    @Body() updateDto: UpdateDepreciationRateDto
  ): Promise<{ message: string; data: Master_depreciation_rate }> {
    return this.depreciationService.update(id, updateDto);
  }

  @Form('Deprecation Rate')
  @Permissions('can_delete')
  @Delete('delete')
  async delete(@Query('id') id: string): Promise<{ message: string }> {
    return this.depreciationService.delete(id);
  }
}
