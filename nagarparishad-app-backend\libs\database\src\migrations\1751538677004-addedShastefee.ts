import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedShastefee1751538677004 implements MigrationInterface {
    name = 'AddedShastefee1751538677004'

    public async up(queryRunner: QueryRunner): Promise<void> {
  await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD "shasti_fee" double precision NOT NULL DEFAULT '0'`);
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
     
        await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP COLUMN "shasti_fee"`);
   }

}
