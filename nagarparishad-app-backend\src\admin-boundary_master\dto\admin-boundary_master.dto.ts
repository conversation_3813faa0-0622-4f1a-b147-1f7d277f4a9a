import { PaginationDto } from '@helper/helpers/Pagination';
import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateAdminBoundaryMasterDto {
  @ApiProperty({ name: 'adminstrativeBoundary_name', type: String })
  @IsNotEmpty()
  @IsString()
  adminstrativeBoundary_name: string;
}

export class UpdateAdminBoundaryMasterDto extends PartialType(
  CreateAdminBoundaryMasterDto,
) {}

export class AdminBoundaryMasterDto extends PaginationDto {
  @ApiProperty({ name: 'adminstrativeBoundary_id', type: String })
  @IsNotEmpty()
  @IsUUID()
  adminstrativeBoundary_id: string;
}
