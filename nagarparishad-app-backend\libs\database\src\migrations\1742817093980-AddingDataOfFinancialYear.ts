import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddingDataOfFinancialYear1742817093980 implements MigrationInterface {
    name = 'AddingDataOfFinancialYear1742817093980'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Reset all financial years to ensure only one has is_current = true
    await queryRunner.query(`
      UPDATE "financial_year"
      SET "is_current" = false;
    `);
    
    // Step 2: Set is_current = true for the latest financial year
    await queryRunner.query(`
      UPDATE "financial_year"
      SET "is_current" = true
      WHERE "financial_year_id" = (
          SELECT "financial_year_id"
          FROM "financial_year"
          ORDER BY 
              SPLIT_PART("financial_year_range", '-', 1)::INTEGER DESC, 
              SPLIT_PART("financial_year_range", '-', 2)::INTEGER DESC
          LIMIT 1
      );
    `);

    // Step 3: Ensure financial_year column exists in tax_pending_dues
    await queryRunner.query(`ALTER TABLE "tax_pending_dues" ADD COLUMN IF NOT EXISTS "financial_year" uuid`);

    // Step 4: Update tax_pending_dues with the latest financial year
    await queryRunner.query(`
      UPDATE "tax_pending_dues"
      SET "financial_year" = (
          SELECT "financial_year_id"
          FROM "financial_year"
          WHERE "is_current" = true
          LIMIT 1
      )
      WHERE "financial_year" IS NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Revert is_current back to false for all financial years
    await queryRunner.query(`
      UPDATE "financial_year"
      SET "is_current" = false;
    `);

    // Step 2: Optionally, reset tax_pending_dues financial_year column to NULL
    await queryRunner.query(`
      UPDATE "tax_pending_dues"
      SET "financial_year" = NULL
      WHERE "financial_year" IS NOT NULL;
    `);
  }
}
