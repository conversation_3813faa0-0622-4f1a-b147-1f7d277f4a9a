import { MigrationInterface, QueryRunner } from "typeorm";

export class AddYearWiseDataToPaidData1746017767473 implements MigrationInterface {
    name = 'AddYearWiseDataToPaidData1746017767473'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add a new column to store year-wise data as a JSON object
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "year_wise_penalty_data" jsonb DEFAULT '{}'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the column if migration is reverted
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "year_wise_penalty_data`);
    }
}
