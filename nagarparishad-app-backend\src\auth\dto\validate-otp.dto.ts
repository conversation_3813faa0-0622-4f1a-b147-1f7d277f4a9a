import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  Matches,
} from 'class-validator';

export class OtpDto {
  @IsOptional()
  @IsEmail()
  email: string;

  @IsOptional()
  @Length(10, 10, { message: 'Mobile number must be exactly 10 digits' })
  @Matches(/^[6-9]\d{9}$/, {
    message:
      'Mobile number must be a valid Indian mobile number and be exactly 10 digits',
  })
  mobileNumber: string;

  @IsNotEmpty()
  @IsString()
  @Length(6, 6, { message: 'OTP must be exactly 6 characters' })
  otp: string;
}
