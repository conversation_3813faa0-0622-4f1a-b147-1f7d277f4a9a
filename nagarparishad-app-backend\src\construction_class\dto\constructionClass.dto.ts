import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class CreateConstructionClassDto {
  @ApiProperty({
    name: 'constructionClassName',
    type: 'string',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  constructionClassName: string;

  @ApiProperty({
    name: 'constructionClassMarathi',
    type: 'string',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  constructionClassMarathi: string;

  @ApiProperty({
    name: 'values',
    type: 'string',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  values: string;
}

export class UpdateConstructionClassDto extends PartialType(
  CreateConstructionClassDto,
) {}

export class ConstructionClassDto {
  @ApiProperty({
    name: 'constructionClass_id',
    type: 'string',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  constructionClass_id: string;
}
