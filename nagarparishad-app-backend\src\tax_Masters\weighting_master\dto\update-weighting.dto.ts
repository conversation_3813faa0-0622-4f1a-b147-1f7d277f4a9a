// src/weighting-master/dto/update-weighting.dto.ts
import { IsString, IsNumber, IsUUID, IsOptional, IsIn } from 'class-validator';

export class UpdateWeightingDto {
  @IsString()
  @IsOptional()
  financial_year?: string; // Optional, allows updating the year

  @IsString()
  @IsOptional()
  reassessment_range_id?: string; // Reassessment range ID

  @IsNumber()
  @IsOptional()
  value?: number; // Optional new weighting value

  @IsString()
  @IsOptional()
  @IsIn(['Active', 'Inactive'])
  status?: string; // Status can be updated to 'active' or 'inactive'

  @IsUUID()
  @IsOptional()
  usage_type_id?: string; // Optional usage type ID for updating
}
