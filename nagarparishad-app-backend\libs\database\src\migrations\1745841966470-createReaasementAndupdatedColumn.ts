import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateReaasementAndupdatedColumn1745841966470
  implements MigrationInterface
{
  name = 'CreateReaasementAndupdatedColumn1745841966470';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "reassessment_range" ("reassessment_range_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "start_range" character varying NOT NULL, "end_range" character varying NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "is_current" boolean NOT NULL DEFAULT false, "is_published" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "UQ_4a46c5a68a415ff14653dd1599a" UNIQUE ("start_range", "end_range"), CONSTRAINT "PK_3a5b7f646423cb83800806f1993" PRIMARY KEY ("reassessment_range_id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" ADD "year" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" ADD "reassessment_range_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" ADD "year" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" ADD "reassessment_range_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" ADD CONSTRAINT "FK_9157a42a8a6d4e91f62c78dce9e" FOREIGN KEY ("reassessment_range_id") REFERENCES "reassessment_range"("reassessment_range_id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" ADD CONSTRAINT "FK_20ed65d12ef56b8c486bcc5bbc9" FOREIGN KEY ("reassessment_range_id") REFERENCES "reassessment_range"("reassessment_range_id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }
  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "deleted_property_usage_details" DROP CONSTRAINT "FK_3704e9416b2d21bee9eeed76e8d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "deleted_property_usage_details" DROP CONSTRAINT "FK_537835d3810d522523a6f9a3e5e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" DROP CONSTRAINT "FK_20ed65d12ef56b8c486bcc5bbc9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" DROP CONSTRAINT "FK_9157a42a8a6d4e91f62c78dce9e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" DROP COLUMN "reassessment_range_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_ferfar_details" DROP COLUMN "year"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" DROP COLUMN "reassessment_range_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "property_fod_details" DROP COLUMN "year"`,
    );
    await queryRunner.query(`DROP TABLE "reassessment_range"`);
  }
}
