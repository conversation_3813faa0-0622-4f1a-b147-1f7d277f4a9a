export interface LocationMasterObject {
  location_id: string;
  locationName: string;
  createdAt?: string;
  updatedAt?: string;
  deleteAt?: string | null;
}

export interface LocationSelectOject {
  location_id: string;
  locationName: string;
}

export interface LocationListAllApi {
  statusCode: number;
  message: string;
  data: LocationMasterObject[];
}

export interface LocationCreateApi {
  statusCode: number;
  message: string;
  data: LocationMasterObject;
}
export interface LocationUpdateApi {
  statusCode: number;
  message: string;
}

export interface LocationSendApiObj {
  locationName: string;
}
