import { Repository } from "typeorm";
import { BackupPaymentDetailsEntity } from "../entities";
import { InjectRepository } from "@nestjs/typeorm";

export class BackupPaymentDetailsRepository extends Repository<BackupPaymentDetailsEntity> {
    constructor(
        @InjectRepository(BackupPaymentDetailsEntity)
        private readonly backupPaymentDetailsRepository: Repository<BackupPaymentDetailsEntity>
    ) {
        super(
            backupPaymentDetailsRepository.target,
            backupPaymentDetailsRepository.manager,
            backupPaymentDetailsRepository.queryRunner,
          );
    }
}