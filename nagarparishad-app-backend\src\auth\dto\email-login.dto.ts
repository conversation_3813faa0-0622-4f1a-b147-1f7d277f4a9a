import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsString,
  Length,
  Matches,
} from 'class-validator';

export class EmailLoginDto {
  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Password for the user account',
    example: 'Password123!',
  })
  @IsNotEmpty()
  @IsString()
  password: string;
}
