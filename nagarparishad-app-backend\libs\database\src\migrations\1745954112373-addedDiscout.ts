import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedDiscout1745954112373 implements MigrationInterface {
    name = 'AddedDiscout1745954112373'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "milkatKar" ADD "discount" character varying`);
        await queryRunner.query(`ALTER TABLE "milkatKar" ADD "reassessment_range_id" uuid`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ADD "discount" character varying`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "discount" character varying`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "discount" character varying`);
        await queryRunner.query(`ALTER TABLE "milkatKar" ADD CONSTRAINT "FK_7c27e74584a56db7d58808ae96c" FOREIGN KEY ("reassessment_range_id") REFERENCES "reassessment_range"("reassessment_range_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "milkatKar" DROP CONSTRAINT "FK_7c27e74584a56db7d58808ae96c"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "discount"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "discount"`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" DROP COLUMN "discount"`);
        await queryRunner.query(`ALTER TABLE "milkatKar" DROP COLUMN "reassessment_range_id"`);
        await queryRunner.query(`ALTER TABLE "milkatKar" DROP COLUMN "discount"`);
    }

}
