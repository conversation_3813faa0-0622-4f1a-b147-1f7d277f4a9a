import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  HttpStatus,
  InternalServerErrorException,
  Res,
  Put
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { KarAkaraniService } from './karAkarani.service';

@ApiTags('Milkat Kar Akarani')
@Controller('milkatKar')
export class KarAkaraniController {
  constructor(private readonly KarAkaraniService: KarAkaraniService) {}

  @ApiOperation({ summary: 'Get all Milkat Kar Akarani records' })
  @ApiResponse({ status: 200, description: 'Returns all records' })
  @Get()
  async findAll(@Query() params) {
    return await this.KarAkaraniService.findAll(params);
  }

  @ApiOperation({ summary: 'Process Milkat Kar Akarani' })
  @ApiResponse({ status: 200, description: 'Processes Milkat Kar Akarani based on property_id or ward_number' })
  @Get('/processMilakatKarAkarni')
  async processMilakatKarAkarni(@Query() params) {
    if (params.property_id) {
      return this.KarAkaraniService.calculateTaxForProperty(params.property_id);
    } else if (params.ward_number) {
      return this.KarAkaraniService.processMilakatKarAkarni(params.ward_number);
    } else if (params.reassessmentYearId) {
      return this.KarAkaraniService.processMilakatKarAkarniByReassessmentRange(params.reassessmentYearId);
    }
  }

  @ApiOperation({ summary: 'Get Milkat Kar data by reassessment range' })
  @ApiResponse({ status: 200, description: 'Returns Milkat Kar data for the specified reassessment range' })
  @Get('/by-reassessment-range')
  async getMilkatKarDataByReassessmentRange(@Query('reassessmentYearId') reassessmentYearId: string) {
    return await this.KarAkaraniService.getMilkatKarDataByReassessmentRange(reassessmentYearId);
  }

  @ApiOperation({ summary: 'Get ward-wise Milkat Kar generation status' })
  @ApiResponse({ status: 200, description: 'Returns ward-wise generation status for the specified reassessment range' })
  @Get('/getWardWiseMilkatKarStatus')
  async getWardWiseMilkatKarStatus(@Query('reassessmentYearId') reassessmentYearId: string) {
    return await this.KarAkaraniService.getWardWiseMilkatKarStatus(reassessmentYearId);
  }

  @ApiOperation({ summary: 'Get remaining Milkat Kar count for a ward' })
  @ApiResponse({ status: 200, description: 'Returns count of properties that need Milkat Kar generation' })
  @Get('/getMilkatKarRemainingCount')
  async getMilkatKarRemainingCount(
    @Query('ward_number') wardNumber: string,
    @Query('reassessment_range_id') reassessmentRangeId: string,
  ) {
    return await this.KarAkaraniService.getMilkatKarRemainingCount(wardNumber, reassessmentRangeId);
  }



  @ApiOperation({ summary: 'Get a specific Milkat Kar Akarani record' })
  @ApiResponse({ status: 200, description: 'Returns a single record' })
  @Get('/getMilkatKarAkarni')
  async getMilkatKarAkarni(@Query() params) {
    return this.KarAkaraniService.getMilkatKarAkarni(
      params.value,
      params.searchOn,
      params.fy
    );
  }

  @ApiOperation({ summary: 'Update tax data for a specific record' })
  @ApiResponse({ status: 200, description: 'Successfully updated tax data' })
  @Put('/update-taxData')
  async updateTaxData(@Body('id') id: string) {
    return this.KarAkaraniService.updateTaxData(id);
  }

  @ApiOperation({ summary: 'Update all tax data records' })
  @ApiResponse({ status: 200, description: 'Successfully updated all tax data records' })
  @Put('/update-all-taxData')
  async updatellTaxData() {
    return this.KarAkaraniService.updateAllTaxData();
  }


}
