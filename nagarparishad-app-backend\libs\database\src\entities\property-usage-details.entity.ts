import {
  BaseEntity,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  OneToMany
} from 'typeorm';
import { PropertyEntity } from './property.entity';
import { PropertyTypeMasterEntity } from './property-type-master.entity';
import { PropertySubTypeMasterEntity } from './property-sub-type-master.entity';
import { UsageTypeMasterEntity } from './usage-type-master.entity';
import { UsageSubTypeMasterEntity } from './usage-sub-type-master.entity';
import { Tax_PropertyWiseEntity } from './tax_propertywise.entity';
import { Floor_Master } from './floorMaster.entity';

@Entity('property_usage_details')
export class Property_Usage_Details_Entity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  property_usage_details_id: string;

  @ManyToOne(() => PropertyEntity, (property) => property.property_id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;


  @ManyToOne(
    () => PropertyTypeMasterEntity,
    (propertyType) => propertyType.propertyType_id,
  )
  @JoinColumn({ name: 'propertyType_id' })
  propertyType: PropertyTypeMasterEntity;

  @ManyToOne(
    () => Floor_Master,
    (floorType) => floorType.floor_id,
  )
  @JoinColumn({ name: 'floor_id' })
  floorType: Floor_Master;

  @ManyToOne(() => PropertySubTypeMasterEntity, (propertySubType) => propertySubType.property_sub_type_id)
  @JoinColumn({ name: 'propertySub_id' })
  propertySubType: PropertySubTypeMasterEntity;

  @ManyToOne(() => UsageTypeMasterEntity, (usageType) => usageType.usage_type_id)
  @JoinColumn({ name: 'usage_type_id' })
  usageType: UsageTypeMasterEntity;

  @ManyToOne(() => UsageSubTypeMasterEntity, (usageSubType) => usageSubType.usage_sub_type_master_id)
  @JoinColumn({ name: 'usage_sub_type_master_id' })
  usageSubType: UsageSubTypeMasterEntity;

  @Column({ type: 'varchar', nullable: true })
  property_type_desc: string;

  @Column({ type: 'float', nullable: true })
  construction_area: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  length: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  width: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  are_sq_ft: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  are_sq_meter: number;

  @Column({ type: 'timestamp', nullable: true })
  construction_start_date!: Date;

  @Column({ type: 'timestamp', nullable: true })
  construction_end_date!: Date;

  @Column({ type: 'float', nullable: true })
  Building_age: number;

  @Column({ type: 'float', nullable: true })
  annual_rent: number;

  @Column({ type: 'varchar', nullable: true })
  floor: string;

  @Column({ type: 'varchar', nullable: true })
  flat_no: string;

  @Column({ type: 'boolean', default: false })
  authorized: boolean | string;


  @Column({ type: 'varchar', nullable: true })
  construction_start_year!: string;


  @Column({ type: 'varchar', nullable: true })
  remark: string;
  @Column({ type: 'varchar', nullable: true })
  tapshil: string;

  // @OneToMany(() => Tax_PropertyWiseEntity, (tax_propertywise) => tax_propertywise.property_usage_details)
  // tax_propertywise: Tax_PropertyWiseEntity[];

  @Column({ type: 'int', nullable: true })
  orderIndex: number;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
