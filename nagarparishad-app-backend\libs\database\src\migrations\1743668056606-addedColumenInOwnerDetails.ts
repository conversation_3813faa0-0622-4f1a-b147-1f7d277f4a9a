import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedColumenInOwnerDetails1743668056606 implements MigrationInterface {
    name = 'AddedColumenInOwnerDetails1743668056606'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property_owner_details" ADD "is_payer" boolean`);
 }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP COLUMN "is_payer"`);

    }

}
