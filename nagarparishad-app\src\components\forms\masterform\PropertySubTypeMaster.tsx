import React, { useContext, useRef, useState } from "react";
import WhiteContainer from "@/components/custom/WhiteContainer";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { MasterObjectColumnDef } from "@/model/zone-master";
import { PropertysubtypeMasterObject } from "@/model/propertysubtype-master";
import { MASTER } from "@/constant/config/api.config";
import { GlobalContext } from "@/context/GlobalContext";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen/DeletePopUpScreen";
import { toast } from "@/components/ui/use-toast";
import { usePropertysubtypeMasterController } from "@/controller/master/PropertySubtypeMasterController";
import PropertysubtypeMasterForm from "../PropertysubtypeMasterForm";
import AddNewBtn from "../../globalcomponent/AddNewForm";
import { ResponseData } from "@/model/auth/authServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

const PropertySubTypeMaster = () => {
  const { setMasterComponent, setOpen } = useContext(GlobalContext);
  const { t } = useTranslation();
  const userRef = useRef(null);

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Master, FormName.PropertySubTypeMaster, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Master, FormName.PropertySubTypeMaster, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Master, FormName.PropertySubTypeMaster, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Master, FormName.PropertySubTypeMaster, Action.CanDelete);

  //List API for Zone
  const { propertysubtypeList, deletePropertysubtype, propertySubTypeLoading } =
    usePropertysubtypeMasterController();

  const columns: ColumnDef<MasterObjectColumnDef>[] = [
    {
      accessorKey: `${t("SrNo")}`,
      header: `${t("SrNo")}`,
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: `${t("propertytype.propertytypeLabel")}`,
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("propertytype.propertytypeLabel")}
            {/* <ArrowUpDown className="ml-2 h-4 w-4" /> */}
          </Button>
        );
      },
     cell: ({ row }: { row: any })  => (
        <div className="capitalize">
          {row?.original?.propertyType?.propertyType}
        </div>
      ),
    },
    {
      //  accessorKey: `${t("propertysubtype.propertysubtypeLabel")}`,
      accessorKey: "propertySub_name",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("propertysubtype.propertysubtypeLabel")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
     cell: ({ row }: { row: any })  => (
        <div className="capitalize">{row.original.propertySub_name}</div>
      ),
    },
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: `${t("Actions")}`,
            enableHiding: false,
            cell: ({ row }: { row: any }) => (
              <>
                {canUpdate && (
                  <Button
                    variant="submit"
                    className="h-8 w-8 p-0 justify-center"
                    onClick={() => handleEdit(row?.original)}
                  >
                    {t("table.edit")}
                  </Button>
                )}
                {CanDelete && (
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0 justify-center ml-2  !border-BlueText !text-BlueText"
                    onClick={() => handleDelete(row?.original)}
                  >
                    {t("table.delete")}
                  </Button>
                )}
              </>
            ),
          },
        ]
      : []),
  ];

  function handleEdit(item: PropertysubtypeMasterObject): void {
    setOpen(true);
    setMasterComponent(
      <PropertysubtypeMasterForm
        btnTitle={"propertysubtype.updateBtn"}
        editData={item && item}
      />,
    );
    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }
  const dynamicValues = {
    name: t("propertysubtype.propertysubtypeLabel"),
  };

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] =
    useState<PropertysubtypeMasterObject | null>(null);

  function handleDelete(item: PropertysubtypeMasterObject): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }
  const handleConfirmDelete = () => {
    if (selectedItem) {
      deletePropertysubtype(selectedItem.propertySub_id, {
        onSuccess: (response: ResponseData) => {
          if (response?.statusCode && response?.statusCode === 200) {
            toast({
              title: t("api.formdelete", dynamicValues),
              variant: "success",
            });
          } else {
            toast({
              title: response?.message,
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }

    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };
  const MasterType: string = MASTER.PROPERTYSUBTYPE;

  return (
    <>
      <div className="flex h-fit ">
        <div
          className=" w-full mx-auto px-4 sm:px-6 lg:px-5 py-6  "
          ref={userRef && userRef}
        >
          <p className="w-full flex items-center justify-between ml-2  text-[18px] font-semibold mb-2">
            {t("propertysubtype.formTitle")}
          </p>
          {CanCreate && <WhiteContainer>
            {MasterType && <AddNewBtn masterType={MASTER.PROPERTYSUBTYPE} />}
          </WhiteContainer>}
          <WhiteContainer>
            <TanStackTable
              columns={columns}
              data={propertysubtypeList}
              masterType={MASTER.PROPERTYSUBTYPE}
              searchColumn="propertySub_name"
              searchKey="searchPropertySubType"
              loader={propertySubTypeLoading ? true : false}

            />
          </WhiteContainer>
        </div>
      </div>

      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={""}
          onDelete={handleConfirmDelete}
        />
      )}
    </>
  );
};

export default PropertySubTypeMaster;
