import axios from "axios";

const baseURL = import.meta.env.VITE_APP_BASE_URL;

class PropertyFerfarApi {
  static getStoredToken() {
    return localStorage.getItem("token");
  }

  /**
   * Process property ferfar add/update operations
   * @param {Object} payload - The payload containing property_id, owners, remark, photos, documents
   * @returns {Promise} - API response
   */
  static async processFerfarAddUpdate(payload: any) {
    try {
      const response = await axios.post(
        `${baseURL}/v1/property-owner/ferfar/add-update`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${PropertyFerfarApi.getStoredToken()}`,
          },
        }
      );

      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  }

  /**
   * Process property ferfar delete operations
   * @param {Object} payload - The payload containing property_id, owner_ids, remark, photos, documents
   * @returns {Promise} - API response
   */
  static async processFerfarDelete(payload: any) {
    try {
      const response = await axios.post(
        `${baseURL}/v1/property-owner/ferfar/delete`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${PropertyFerfarApi.getStoredToken()}`,
          },
        }
      );

      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  }

  /**
   * @deprecated Use processFerfarAddUpdate or processFerfarDelete instead
   */
  static async processFerfar(ferfarData: any) {
    try {
      const response = await axios.post(
        `${baseURL}/v1/property-owner/process-ferfar`,
        ferfarData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${PropertyFerfarApi.getStoredToken()}`,
          },
        }
      );

      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  static async addMultipleOwners(propertyId: string, payload: any) {
    try {
      const response = await axios.post(
        `${baseURL}/v1/property-owner/${propertyId}/add-multiple-owners`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${PropertyFerfarApi.getStoredToken()}`,
          },
        }
      );

      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  static async updateWithNewOwners(propertyId: string, payload: any) {
    try {
      const response = await axios.post(
        `${baseURL}/v1/property-owner/${propertyId}/update-with-new-owners`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${PropertyFerfarApi.getStoredToken()}`,
          },
        }
      );

      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  static async deleteOwner(propertyOwnerId: string, remark: string) {
    try {
      const response = await axios.patch(
        `${baseURL}/v1/property-owner/delete-owner/${propertyOwnerId}`,
        { remark },
        {
          headers: {
            Accept: "application/json",
            Authorization: `Bearer ${PropertyFerfarApi.getStoredToken()}`,
          },
        }
      );

      if (response.status === 200) {
        return { status: true, data: response.data };
      } else {
        return { status: false, data: response.data };
      }
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  }
}

export default PropertyFerfarApi;
