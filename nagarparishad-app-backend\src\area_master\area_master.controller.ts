import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { AreaMasterService } from './area_master.service';

import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';
import {
  AreaMasterDto,
  CreateAreaMasterDto,
  UpdateAreaMasterDto,
} from './dto/area-master.dto';

@ApiTags('Area Master')
@Controller('area-master')
export class AreaMasterController {
  constructor(private readonly areaMasterService: AreaMasterService) {}

  @Form('Area Master')
  @Permissions('can_write')
  @ApiOperation({ summary: 'Create a new Area' })
  @ApiResponse({
    status: 201,
    description: 'The Area has been successfully created',
  })
  @Post()
  create(@Body() createAreaMasterDto: CreateAreaMasterDto) {
    return this.areaMasterService.create(createAreaMasterDto);
  }

  @Form('Area Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all Areas' })
  @ApiResponse({ status: 200, description: 'Returns all Area' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.areaMasterService.findAll();
  }

  @Form('Area Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one Area' })
  @ApiResponse({ status: 200, description: 'Returns Single Area' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() areaMaster: AreaMasterDto) {
    return this.areaMasterService.findOne(areaMaster);
  }

  @Form('Area Master')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Update a Area by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Area has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Area not found' })
  @Patch()
  update(
    @Query() areaMaster: AreaMasterDto,
    @Body() updateAreaMasterDto: UpdateAreaMasterDto,
  ) {
    return this.areaMasterService.update(areaMaster, updateAreaMasterDto);
  }

  @Form('Area Master')
  @Permissions('can_delete')
  @ApiOperation({ summary: 'Delete a Area by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Area has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Area not found' })
  @Delete()
  async remove(@Query() areaMaster: AreaMasterDto) {
    return this.areaMasterService.remove(areaMaster);
  }
}
