import { Repository } from 'typeorm';
import { RoleMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class RoleMasterRepository extends Repository<RoleMasterEntity> {
  constructor(
    @InjectRepository(RoleMasterEntity)
    private readonly roleMasterRepository: Repository<RoleMasterEntity>,
  ) {
    super(
      roleMasterRepository.target,
      roleMasterRepository.manager,
      roleMasterRepository.queryRunner,
    );
  }

  async saveData(input: { roleName: string }): Promise<RoleMasterEntity> {
    let data = this.roleMasterRepository.create(input);
    data = await this.roleMasterRepository.save(data);
    return data;
  }

  async findAllData(): Promise<RoleMasterEntity[]> {
    return await this.roleMasterRepository
      .createQueryBuilder('role_master')
      .orderBy('role_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: number): Promise<RoleMasterEntity> {
    return await this.roleMasterRepository
      .createQueryBuilder('role_master')
      .where('role_master.role_id = :role_id', {
        role_id: id,
      })
      .getOne();
  }

  async updateData(id: number, input: { roleName?: string }) {
    return await this.roleMasterRepository
      .createQueryBuilder('role_master')
      .update(RoleMasterEntity)
      .set(input)
      .where('role_id = :role_id', {
        role_id: id,
      })
      .execute();
  }

  async deleteData(id: number) {
    return await this.roleMasterRepository
      .createQueryBuilder('role_master')
      .softDelete()
      .where('role_id = :role_id', {
        role_id: id,
      })
      .execute();
  }
}
