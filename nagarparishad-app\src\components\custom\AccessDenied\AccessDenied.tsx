import React from "react";
 import deniedLogo from "../../../assets/img/homepage/image.png";
 
export const AccessDenied = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-white">
      <div className="text-center">
        {/* Illustration Placeholder */}
        <div className="relative flex justify-center mb-6">
          <div className="w-full h-30  flex items-center justify-center">
            {/* Placeholder for the cartoon character */}
            <img src={deniedLogo} alt=""  className="h-[180px]"/>
            {/* <span className="text-gray-500 text-sm"> Image</span> */}
          </div>
          {/* "No Access" Symbol */}
          {/* <div className="absolute top-0 right-10 w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-white text-2xl">⛔</span>
          </div> */}
        </div>

        {/* Text Section */}
        <h1 className="text-2xl font-semibold text-gray-800 mb-2">
           We're sorry, but you don't have permission to access this application.
        </h1>
        <p className="text-gray-600 mb-6">Please contact your administrator.</p>

        {/* Button/Link Section */}
        {/* <div className="inline-block bg-gray-100 border-l-4 border-blue-500 px-4 py-2 rounded shadow-sm">
          <span className="text-gray-600">Why did I get this error?</span>
          <a href="#" className="text-blue-500 ml-2 hover:underline">
            Read more.
          </a>
        </div> */}
      </div>
    </div>
  );
};