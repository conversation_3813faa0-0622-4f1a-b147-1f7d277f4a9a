import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Delete,
  Query,
} from '@nestjs/common';
import { PropertySubTypeMasterService } from './property-sub-type_master.service';
import {
  CreatePropertySubTypeMasterDto,
  PropertySubTypeMasterDto,
  PropertypeIdMasterDto,
  UpdatePropertySubTypeMasterDto,
} from './dto/property-sub-type_master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@ApiTags('Property Sub Type Master')
@Controller('property-sub-type-master')
export class PropertySubTypeMasterController {
  constructor(
    private readonly propertySubTypeMasterService: PropertySubTypeMasterService,
  ) {}

  @Form('Property Sub Type Master')
  @Permissions('can_write')
  @ApiOperation({ summary: 'Create a new Property Sub Type' })
  @ApiResponse({
    status: 201,
    description: 'The Property Sub Type has been successfully created',
  })
  @Post()
  create(
    @Body() createPropertySubTypeMasterDto: CreatePropertySubTypeMasterDto,
  ) {
    return this.propertySubTypeMasterService.create(
      createPropertySubTypeMasterDto,
    );
  }

  @Form('Property Sub Type Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all Property Sub Types' })
  @ApiResponse({ status: 200, description: 'Returns all Property Sub Types' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.propertySubTypeMasterService.findAll();
  }


  @Form('Property Sub Type Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one Property Sub Type' })
  @ApiResponse({ status: 200, description: 'Returns Single Property Sub Type' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() propertySubMasterDto: PropertySubTypeMasterDto) {
    return this.propertySubTypeMasterService.findOne(propertySubMasterDto);
  }

  @Form('Property Sub Type Master')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Update a Property Sub Type by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Property Sub Type has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Property Sub Type not found' })
  @Patch()
  update(
    @Query() propertySubMasterDto: PropertySubTypeMasterDto,
    @Body() updatePropertySubTypeMasterDto: UpdatePropertySubTypeMasterDto,
  ) {
    return this.propertySubTypeMasterService.update(
      propertySubMasterDto,
      updatePropertySubTypeMasterDto,
    );
  }


  @Form('Property Sub Type Master')
  @Permissions('can_delete')
  @ApiOperation({ summary: 'Delete a Property Sub Type by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Property Sub Type has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Property Sub Type not found' })
  @Delete()
  remove(@Query() propertySubMasterDto: PropertySubTypeMasterDto) {
    return this.propertySubTypeMasterService.remove(propertySubMasterDto);
  }


  @Form('Property Sub Type Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all Property Sub Type by Property Type' })
  @ApiResponse({
    status: 200,
    description: 'Returns all Property Sub Types by Property Type',
  })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getByPropertyType')
  getByPropertyType(@Query() propertypeIdMasterDto: PropertypeIdMasterDto) {
    return this.propertySubTypeMasterService.getByPropertyType(
      propertypeIdMasterDto,
    );
  }
}
