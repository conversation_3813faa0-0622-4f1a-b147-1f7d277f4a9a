import { Controller, Get, Post } from '@nestjs/common';
import { BackupMigrationPropertyUsageDetailsService } from './backup-migration-property-usage-details.service';

@Controller('backup-migration')
export class BackupMigrationPropertyUsageDetailsController {
  constructor(
    private readonly migrationService: BackupMigrationPropertyUsageDetailsService,
  ) {}

  @Post('migrate')
  async migrateData() {
    return this.migrationService.migrateAndDelete();
  }

  @Get()
  async check() {
    return {message : "health check"}
  }
}
