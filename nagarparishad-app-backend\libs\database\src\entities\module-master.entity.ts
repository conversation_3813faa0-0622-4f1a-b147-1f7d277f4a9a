import {
  <PERSON><PERSON>ntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { FormMasterEntity } from './formMaster.entity';

@Entity('module_master')
export class ModuleMasterEntity extends BaseEntity {
  @PrimaryGeneratedColumn()
  module_id: number;

  @Column({ name: 'module_name', type: 'varchar', nullable: false })
  moduleName: string;

  @OneToMany(() => FormMasterEntity, (formMaster) => formMaster.module)
  forms: FormMasterEntity[];

  @Column({ name: 'created_by', type: 'varchar', nullable: true })
  createdBy: string;

  @Column({ name: 'created_by_1', type: 'varchar', nullable: true })
  createdBy1: string;

  /*
   * Create and Update Date Columns
   */
  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
