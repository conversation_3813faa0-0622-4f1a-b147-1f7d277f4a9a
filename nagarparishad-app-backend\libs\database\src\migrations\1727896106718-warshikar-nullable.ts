import { MigrationInterface, QueryRunner } from "typeorm";

export class WarshikarNullable1727896106718 implements MigrationInterface {
    name = 'WarshikarNullable1727896106718'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "all_property_tax_sum_total" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "all_property_tax_sum" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "all_property_tax_sum_current" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax" SET DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax_current" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax_current" SET DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax_previous" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax_previous" SET DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax" SET DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax_current" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax_current" SET DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax_previous" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax_previous" SET DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax_previous" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax_previous" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax_current" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax_current" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "total_tax" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax_previous" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax_previous" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax_current" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax_current" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "other_tax_sum_tax" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "all_property_tax_sum_current" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "all_property_tax_sum" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ALTER COLUMN "all_property_tax_sum_total" SET NOT NULL`);
    }

}
