import { cn } from "@/lib/utils";
import React from "react";
interface LoaderProps {
  className?: string; // className is now optional
}

export const Loader: React.FC<LoaderProps> = ({ className = "text-blue-600" }: { className: string }) => {
  return (
    // <svg
    // className={`animate-spin h-8 w-8  ${className}`} 
    // xmlns="http://www.w3.org/2000/svg"
    //     viewBox="0 0 24 24"
    //     fill="none"
    //     stroke="currentColor"
    //     strokeWidth="2"
    //     strokeLinecap="round"
    //     strokeLinejoin="round"
    //   >
    //     <path d="M12 2v4M12 18v4M4.22 4.22l2.83 2.83M15.95 15.95l2.83 2.83M2 12h4M18 12h4M4.22 19.78l2.83-2.83M15.95 8.05l2.83-2.83" />
    //   </svg>

    <svg viewBox="25 25 50 50" className={`w-10 h-10 my-3 loader-svg ${className}`}>
      <circle r="20" cy="50" cx="50" className="loader-circle"></circle>
    </svg>
  );
};
