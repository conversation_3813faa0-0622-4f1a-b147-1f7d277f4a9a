import * as bcrypt from 'bcrypt';
import { randomInt } from 'crypto';

export class AuthHelper {
  // Encode User Password
  async encodePassword(password: string) {
    const salt: string = bcrypt.genSaltSync(10);
    return bcrypt.hashSync(password, salt);
  }

  // Validate User's password
  async isPasswordValid(password: string, userPassword: string) {
    return bcrypt.compareSync(password, userPassword);
  }
  // async  isPasswordValid(inputPassword, storedPassword) {
  //   return inputPassword === storedPassword;
  // }
  async generateRandomNumberWithPrefix(prefix: string): Promise<any> {
    const randomNumber = randomInt(100000, 1000000).toString(); // Generates a random 6-digit number
    return `${prefix}${randomNumber}`;
  }

  async encodeOtp(otp: string)
  {
    const salt: string = bcrypt.genSaltSync(10);
    return bcrypt.hashSync(otp, salt);
  }

  async isOtpvalid(otp: string, userOtp: string) {
    return bcrypt.compareSync(otp, userOtp);
  }
}
