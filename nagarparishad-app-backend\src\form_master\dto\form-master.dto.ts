import { PartialType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreateFormMasterDto {
  @IsNotEmpty()
  @IsString()
  formName: string;
}

export class UpdateFormMasterDto extends PartialType(CreateFormMasterDto) {}

export class FormMasterIdDto {
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  form_id: number;
}
