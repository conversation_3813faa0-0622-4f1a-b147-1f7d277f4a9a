import { MigrationInterface, QueryRunner } from "typeorm";

export class MasterTablesCreated1727175716342 implements MigrationInterface {
    name = 'MasterTablesCreated1727175716342'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "master_rr_rate" ("rr_rate_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year" character varying NOT NULL, "value" double precision NOT NULL DEFAULT '0', "status" character varying DEFAULT 'Active', "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "zone_id" uuid, CONSTRAINT "PK_6b4a300476d91e31e1b0fd505af" PRIMARY KEY ("rr_rate_id"))`);
        await queryRunner.query(`CREATE TABLE "master_rr_construction_rate" ("rr_construction_rate_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year" character varying NOT NULL, "value" double precision NOT NULL DEFAULT '0', "status" character varying DEFAULT 'Active', "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "propertyType_id" uuid, CONSTRAINT "PK_3a418b887d5475b0eb9aec19ccd" PRIMARY KEY ("rr_construction_rate_id"))`);
        await queryRunner.query(`CREATE TABLE "master_tax_rate" ("rr_tax_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year" character varying NOT NULL, "value" double precision NOT NULL DEFAULT '0', "status" character varying DEFAULT 'Active', "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "propertyType_id" uuid, CONSTRAINT "PK_63d78db9aee653cb4053bfaadf0" PRIMARY KEY ("rr_tax_id"))`);
        await queryRunner.query(`CREATE TABLE "master_weighting_rate" ("weighting_rate_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year" character varying NOT NULL, "value" double precision NOT NULL DEFAULT '0', "status" character varying DEFAULT 'Active', "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "usage_type_id" uuid, CONSTRAINT "PK_9a85d694d6256191788664ab72c" PRIMARY KEY ("weighting_rate_id"))`);
        await queryRunner.query(`CREATE TABLE "master_depreciatio_rate" ("depreciation_rate_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "from_age" double precision NOT NULL DEFAULT '0', "to_age" double precision NOT NULL DEFAULT '0', "financial_year" character varying NOT NULL, "value" double precision NOT NULL DEFAULT '0', "status" character varying DEFAULT 'Active', "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "propertyType_id" uuid, CONSTRAINT "PK_422b80f52b9ee70295108c99f19" PRIMARY KEY ("depreciation_rate_id"))`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "emarat_kar" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "capital_value" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "total_tax" character varying`);
        await queryRunner.query(`ALTER TABLE "master_rr_rate" ADD CONSTRAINT "FK_845b8e344e80b9cbf81b10afe76" FOREIGN KEY ("zone_id") REFERENCES "zone_master"("zone_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" ADD CONSTRAINT "FK_909c3828ef12af861ea7fd2f8f7" FOREIGN KEY ("propertyType_id") REFERENCES "property_type_master"("propertyType_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_tax_rate" ADD CONSTRAINT "FK_ba03f0f1cab042ed390aa18efda" FOREIGN KEY ("propertyType_id") REFERENCES "property_type_master"("propertyType_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_weighting_rate" ADD CONSTRAINT "FK_005ea491e2313cff6ca1483355c" FOREIGN KEY ("usage_type_id") REFERENCES "usage_type_master"("usage_type_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_depreciatio_rate" ADD CONSTRAINT "FK_14ce67ca33ed5bcb479563376d8" FOREIGN KEY ("propertyType_id") REFERENCES "property_type_master"("propertyType_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "master_depreciatio_rate" DROP CONSTRAINT "FK_14ce67ca33ed5bcb479563376d8"`);
        await queryRunner.query(`ALTER TABLE "master_weighting_rate" DROP CONSTRAINT "FK_005ea491e2313cff6ca1483355c"`);
        await queryRunner.query(`ALTER TABLE "master_tax_rate" DROP CONSTRAINT "FK_ba03f0f1cab042ed390aa18efda"`);
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" DROP CONSTRAINT "FK_909c3828ef12af861ea7fd2f8f7"`);
        await queryRunner.query(`ALTER TABLE "master_rr_rate" DROP CONSTRAINT "FK_845b8e344e80b9cbf81b10afe76"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "total_tax"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "capital_value"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "emarat_kar"`);
        await queryRunner.query(`DROP TABLE "master_depreciatio_rate"`);
        await queryRunner.query(`DROP TABLE "master_weighting_rate"`);
        await queryRunner.query(`DROP TABLE "master_tax_rate"`);
        await queryRunner.query(`DROP TABLE "master_rr_construction_rate"`);
        await queryRunner.query(`DROP TABLE "master_rr_rate"`);
    }

}
