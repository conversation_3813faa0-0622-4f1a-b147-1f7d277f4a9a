import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import Api from "../../services/ApiServices";
import { useContext, useEffect, useState } from "react";
import { GlobalContext } from "@/context/GlobalContext";
import {
  LocationListAllApi,
  LocationMasterObject,
} from "../../model/location-master";

const fetchLocations = () => {
  return new Promise((resolve, reject) => {
    Api.getAllLocation((response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(new Error("Error fetching locations"));
      }
    });
  });
};

const createLocation = async (locationData) => {
  return new Promise((resolve, reject) => {
    Api.createLocation(locationData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateLocation = async ({ locationId, locationData }) => {
  return new Promise((resolve, reject) => {
    Api.updateLocation(locationId, locationData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteLocation = async (locationId) => {
  return new Promise((resolve, reject) => {
    Api.deleteLocation(locationId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useLocationMasterController = () => {
  const queryClient = useQueryClient();

  const { data: locationData } = useQuery({
    queryKey: ["locationmaster"],
    queryFn: fetchLocations,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  const createLocationMutation = useMutation({
    mutationFn: createLocation,
    onMutate: async (newLocation) => {
      await queryClient.cancelQueries({ queryKey: ["locationmaster"] });

      const previousLocations = queryClient.getQueryData(["locationmaster"]);
      console.log("querycalle", previousLocations);
      console.log("prevous", previousLocations);
      queryClient.setQueryData(["locationmaster"], (old) => {
        const updatedData = [newLocation, ...old];
        console.log("updatestrret", updatedData);
        return updatedData;
      });

      return { previousLocations };
    },
    onError: (err, newLocation, context) => {
      queryClient.setQueryData(["locationmaster"], context.previousLocations);
      console.error("Error creating location:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["locationmaster"] });
    },
  });

  const updateLocationMutation = useMutation({
    mutationFn: updateLocation,
    onMutate: async ({ locationId, locationData }) => {
      await queryClient.cancelQueries({ queryKey: ["locationmaster"] });

      const previousLocations = queryClient.getQueryData(["locationmaster"]);

      queryClient.setQueryData(["locationmaster"], (old) => {
        const updatedLocations = old.map((location) =>
          location.location_id === locationId
            ? { ...location, ...locationData }
            : location,
        );
        return updatedLocations;
      });

      return { previousLocations };
    },
    onError: (err, { locationId, locationData }, context) => {
      queryClient.setQueryData(["locationmaster"], context.previousLocations);
      console.error("Error updating location:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["locationmaster"] });
    },
  });

  const deleteLocationMutation = useMutation({
    mutationFn: deleteLocation,
    onMutate: async (locationId) => {
      await queryClient.cancelQueries({ queryKey: ["locationmaster"] });

      const previousLocations = queryClient.getQueryData(["locationmaster"]);
      queryClient.setQueryData(["locationmaster"], (old) => {
        const updatedLocations = old.filter(
          (location) => location.location_id !== locationId,
        );
        return updatedLocations;
      });

      return { previousLocations };
    },
    onError: (err, locationId, context) => {
      queryClient.setQueryData(["locationmaster"], context.previousLocations);
      console.error("Error deleting location:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["locationmaster"] });
    },
  });
  return {
    locationList: locationData || [],
    createLocation: createLocationMutation.mutate,
    updateLocation: updateLocationMutation.mutate,
    deleteLocation: deleteLocationMutation.mutate,
  };
};
