import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  HttpStatus,
  InternalServerErrorException,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import { Response } from 'express';
import { ImportModuleService } from './import-module.service';
@Controller('import-module')
export class ImportModuleController {
  constructor(private readonly importModuleService: ImportModuleService) {}
  @Get()
  async findAll(@Res() res: Response) {
    await this.importModuleService.findAll(res);
  }

  @Get('getCountMissingFields')
  findOne(@Query() wardNumber) {
    const syncDate = ''; //set blank in case not to save into import_property_stats table and only return stat data
    //const syncDate=new Date();  //set blank in case not to save into import_property_stats table and only return stat data
    return this.importModuleService.getStatsData_on_importeddata(
      wardNumber.ward_number,
      syncDate,
    );
  }

  @Get('getImagesCompare')
  findImagesCompare(@Query() params) {
    const syncDate = ''; //set blank in case not to save into import_property_stats table and only return stat data
    //const syncDate=new Date();  //set blank in case not to save into import_property_stats table and only return stat data
    return this.importModuleService.findImagesCompare(params);
  }

  @Get('/processData')
  processData(@Query() wardNumber) {
    return this.importModuleService.processData(wardNumber.ward_number);
  }


  @Get('/processData/duplicate_number')
  processDataForDuplicateNumber(@Query() wardNumber) {
    return this.importModuleService.processDataForDuplicateNumber(wardNumber.ward_number);
  }
  @ApiResponse({ status: 400, description: 'Invalid file format' })
  @Post('/upload-file')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(),
      limits: { files: 1, fileSize: 1024 * 1024 * 5 }, // 1 MB you can adjust size here
      fileFilter: (req, file, cb) => {
        const allowedMimeTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
          'application/vnd.ms-excel', // .xls
        ];
        if (!allowedMimeTypes.includes(file.mimetype)) {
          cb(new BadRequestException('Invalid file type'), false);
        } else if (file?.size > 1024 * 1024 * 5) {
          // 1MB
          cb(
            new BadRequestException('Max File Size Reached. Max Allowed: 1MB'),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  async uploadCsvFile(@UploadedFile() file: Express.Multer.File): Promise<any> {
    try {
      // let response: any = await this.propertyMasterService.validateCsvData(file)
      // if (!response.error) {
      let response: any = await this.importModuleService.processFile(file);
      //}
      return {
        error: false,
        statusCode: response?.status || HttpStatus.OK,
        message: response?.message || 'file uploaded successfully',
        data: response?.data || [],
        errorsArray: response?.errorsArray || [],
      };
    } catch (e) {
      throw new InternalServerErrorException(
        e?.message || 'Internal Server Error',
      );
    }
  }

  @Get('/export-csv')
  async exportCsvFile(
    @Query('ward_number') wardNumber: string,
    @Res() res: Response,
  ) {
    const csvData = await this.importModuleService.exportCsvData(wardNumber);
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=export_ward_${wardNumber}.csv`,
    );
    res.status(HttpStatus.OK).send(csvData);
  }

  @Post('/upload-and-update')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(),
      limits: { files: 1, fileSize: 1024 * 1024 * 5 }, // 5 MB limit
      fileFilter: (req, file, cb) => {
        const allowedMimeTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
          'application/vnd.ms-excel', // .xls
        ];
        if (!allowedMimeTypes.includes(file.mimetype)) {
          cb(new BadRequestException('Invalid file type'), false);
        } else if (file.size > 1024 * 1024 * 5) {
          cb(
            new BadRequestException('Max File Size Reached. Max Allowed: 5MB'),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  async uploadAndUpdateFile(@UploadedFile() file: Express.Multer.File) {
    try {
      const response: any =
        await this.importModuleService.processAndUpdateFile(file);

      // Ensure both message and errors are included in the response
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: response.message || 'File processed successfully',
        data: response.data || [], // Ensure errors are returned as an object
      };
    } catch (error) {
      throw new InternalServerErrorException(
        error.message || 'Internal Server Error',
      );
    }
  }

  @Get('/ImportPropertiesIntoSystem')
  async ImportPropertiesIntoSystem(@Query() wardNumber) {
    return this.importModuleService.ImportPropertiesIntoSystem(
      wardNumber.ward_number,
    );
  }
  @Get('/ImportInfoFromForms')
  async ImportInfoIntoSystem(@Query() wardNumber) {
    return this.importModuleService.ImportInfoIntoSystem(
      wardNumber.ward_number,
    );
  }

  @Get('/UpdateDataFromGiS')
  async UpdateDataFromGiS(@Query() wardNumber) {
    return this.importModuleService.UpdateDataFromGiS(
      wardNumber.ward_number,
    );
  }

  
  @Get('/UpdateMobildNumber_From_Form_to_OwnerDetails')
  async UpdateMobileNumber(@Query() wardNumber) {
    return this.importModuleService.UpdateMobileNumber(
      wardNumber.ward_number,
    );
  }



  // @Get('/generatepdfFromImportProperty')
  // async generatePdfFromImportProperty(@Query('ward') ward: any,    @Res() res: Response,) {

  //   if (ward < 1 || ward > 8) {
  //     throw new BadRequestException('Ward number must be between 1 and 8.');
  //   }

  //   try {
  //     const zipBuffer =
  //       await this.importModuleService.generatePdfFromImportProperty(ward);

  //     res.set({
  //       'Content-Type': 'application/zip',
  //       'Content-Disposition': `attachment; filename=pdfs_ward_${ward}.zip`, 
  //     });
  //     return res.send(zipBuffer);
  //   } catch (error) {
  //     throw new BadRequestException(error.message || 'Error processing file.');
  //   }

  
  // }



  
   
  

}
