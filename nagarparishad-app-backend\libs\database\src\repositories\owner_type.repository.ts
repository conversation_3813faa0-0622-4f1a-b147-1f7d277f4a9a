import { Repository } from 'typeorm';
import { Owner_type_master } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class OwnerTypeRepository extends Repository<Owner_type_master> {
  constructor(
    @InjectRepository(Owner_type_master)
    private readonly ownerTypeRepository: Repository<Owner_type_master>,
  ) {
    super(
      ownerTypeRepository.target,
      ownerTypeRepository.manager,
      ownerTypeRepository.queryRunner,
    );
  }
  async saveData(input: {
    owner_type: string;
  }): Promise<Owner_type_master> {
    let data = this.ownerTypeRepository.create(input);
    data = await this.ownerTypeRepository.save(data);
    return data;
  }
  async findAllLocation() {
    return await this.ownerTypeRepository
      .createQueryBuilder('owner_type_master')
      .orderBy('owner_type_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string) {
    return await this.ownerTypeRepository
      .createQueryBuilder('owner_type_master')
      .where('owner_type_master.propertyType_id = :propertyType_id', {
        owner_type_id: id,
      })
      .getOne();
  }

  // async updateData(owner_type_id: string, input: { owner_type?: string }) {
  //   return await this.ownerTypeRepository
  //     .createQueryBuilder('owner_type_master')
  //     .update(Owner_type_master)
  //     .set(input)
  //     .where('owner_type_id = :owner_type_id', { owner_type_id })
  //     .execute();
  // }

  // async deleteData(owner_type_id: string) {
  //   return await this.ownerTypeRepository
  //     .createQueryBuilder('owner_type')
  //     .softDelete()
  //     .where('owner_type_id = :owner_type_id', { owner_type_id })
  //     .execute();
  // }
}


 



