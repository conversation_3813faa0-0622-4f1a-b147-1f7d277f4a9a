import {
  Body,
  Controller,
  Get,
  Patch,
  Post,
  Query,
  ValidationPipe,
} from '@nestjs/common';
import { PermissionService } from './permission.service';
import {
  CreatePermissionDto,
  FormIdDto,
  PermissionIdDto,
  RoleIdDto,
  UpdatePermissionDto,
} from './dto/permission.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Permission')
@Controller('permission')
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) {}

  @ApiOperation({ summary: 'Create a new Permission' })
  @ApiResponse({
    status: 201,
    description: 'The Permission  has been successfully created',
  })
  @ApiResponse({
    status: 404,
    description: 'Form Does Not Exists',
  })
  @ApiResponse({
    status: 404,
    description: 'Role Does Not Exists',
  })
  @Post()
  create(@Body(new ValidationPipe()) createPermissionDto: CreatePermissionDto) {
    return this.permissionService.create(createPermissionDto);
  }

  @ApiOperation({ summary: 'Get all  Permission By Role' })
  @ApiResponse({ status: 200, description: 'Returns all  Permission By Role' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll(@Query() roleIdDto: RoleIdDto) {
    return this.permissionService.findAll(roleIdDto);
  }

  @ApiOperation({ summary: 'Get one Permission' })
  @ApiResponse({ status: 200, description: 'Returns Single Permission' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('detail')
  findOne(@Query() permissionId: PermissionIdDto) {
    return this.permissionService.findOne(permissionId);
  }

  @ApiOperation({ summary: 'Get Form Wise Permission' })
  @ApiResponse({ status: 200, description: 'Returns  Form Wise Permission' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('formpermission')
  getFormPermission(@Query() formIdDto: FormIdDto) {
    return this.permissionService.getFormPermission(formIdDto);
  }

  @Patch()
  updatePermission(@Body() update: UpdatePermissionDto) {
    return this.permissionService.updatePermission(update);
  }

  @Post('/addModules')
  async addModules() {
    return this.permissionService.createModulesWithFormsAndPermissions();
  }
}
