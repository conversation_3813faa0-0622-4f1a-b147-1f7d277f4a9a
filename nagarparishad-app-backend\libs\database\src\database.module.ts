import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AppDataSource } from './datasource';
import { repositories } from './repositories';
import { entities } from './entities';

@Module({
  imports: [
    TypeOrmModule.forRoot(AppDataSource.options),
    TypeOrmModule.forFeature([...entities]),
  ],
  providers: [...entities, ...repositories],
  exports: [...entities, ...repositories],
})
export class DatabaseModule {}
