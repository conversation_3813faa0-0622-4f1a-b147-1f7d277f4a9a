export interface ConstructionRateSetting   {
    rr_construction_rate_id: string;
    financial_year?: string; // Optional for backward compatibility
    value: number;
    status: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    property_type_class_id: {
        property_type_class_id: string;
        property_type_class: string;
        createdAt: string;
        updatedAt: string;
        deletedAt: string | null;
    },
    reassessmentRange?: {
        reassessment_range_id: string;
        start_range: string;
        end_range: string;
    }
}

export interface ConstructionRateApi {
    statusCode: number;
    message: string;
    data: ConstructionRateSetting[];
  }

  export interface ConstructionRateCreateApi {
    statusCode: number;
    message: string;
  }
  export interface ConstructionRateUpdateApi {
    financial_year?: string, // Optional for backward compatibility
    reassessment_range_id: string,
    value: number,
    status:string,
    property_type_class_id:string,
    rr_construction_rate_id:string
  }

  export interface ConstructionRateSendApiObj {
    financial_year?: string; // Optional for backward compatibility
    reassessment_range_id: string;
    value: number;
    status: string;
    property_type_class_id: string;
    }
