import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdatedColumnName1743488763682 implements MigrationInterface {
    name = 'UpdatedColumnName1743488763682'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP CONSTRAINT "FK_d14ec378ce1e5bf7c014001fc9e"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" RENAME COLUMN "payment_info_id" TO "receipt_id"`);
    await queryRunner.query(`ALTER TABLE "demand_report_data" ADD CONSTRAINT "FK_be875f0a8d9f196c13767826018" FOREIGN KEY ("receipt_id") REFERENCES "receipt"("receipt_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
   }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP CONSTRAINT "FK_be875f0a8d9f196c13767826018"`);
      
        await queryRunner.query(`ALTER TABLE "demand_report_data" RENAME COLUMN "receipt_id" TO "payment_info_id"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD CONSTRAINT "FK_d14ec378ce1e5bf7c014001fc9e" FOREIGN KEY ("payment_info_id") REFERENCES "receipt"("receipt_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
