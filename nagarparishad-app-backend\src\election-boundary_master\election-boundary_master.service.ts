import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateElectionBoundaryMasterDto,
  ElectionBoundaryMasterDto,
  UpdateElectionBoundaryMasterDto,
} from './dto/election-master.dto';
import { ElectionBoundaryMasterRepository } from 'libs/database/repositories';

@Injectable()
export class ElectionBoundaryMasterService {
  constructor(
    private readonly electionBoundaryMasterRepository: ElectionBoundaryMasterRepository,
  ) {}

  async create(
    createElectionBoundaryMasterDto: CreateElectionBoundaryMasterDto,
  ) {
    try {
      const saveData = await this.electionBoundaryMasterRepository.saveData(
        createElectionBoundaryMasterDto,
      );

      return {
        message: 'Data Saved SuccessFully',
        data: saveData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllData =
        await this.electionBoundaryMasterRepository.findAllData();

      if (!getAllData) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(electionMasterDto: ElectionBoundaryMasterDto) {
    try {
      const { electionBoundary_id } = electionMasterDto;
      const checkData =
        await this.electionBoundaryMasterRepository.findById(
          electionBoundary_id,
        );
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      return {
        message: 'Data found Sucess',
        data: checkData,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    electionMasterDto: ElectionBoundaryMasterDto,
    updateElectionBoundaryMasterDto: UpdateElectionBoundaryMasterDto,
  ) {
    try {
      const { electionBoundary_id } = electionMasterDto;
      const checkData =
        await this.electionBoundaryMasterRepository.findById(
          electionBoundary_id,
        );
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }
      const updateData = await this.electionBoundaryMasterRepository.updateData(
        electionBoundary_id,
        updateElectionBoundaryMasterDto,
      );

      if (updateData.affected === 0) {
        throw new NotAcceptableException('Failed to update data');
      }

      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(electionMasterDto: ElectionBoundaryMasterDto) {
    try {
      const { electionBoundary_id } = electionMasterDto;
      const checkData =
        await this.electionBoundaryMasterRepository.findById(
          electionBoundary_id,
        );
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }
      const deleteData =
        await this.electionBoundaryMasterRepository.deleteData(
          electionBoundary_id,
        );

      if (deleteData.affected === 0) {
        throw new NotAcceptableException('Failed to delete data');
      }

      return {
        message: 'Data Deleted SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }
}
