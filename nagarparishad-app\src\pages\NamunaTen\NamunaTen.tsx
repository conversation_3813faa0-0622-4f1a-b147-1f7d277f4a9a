import WhiteContainer from "@/components/custom/WhiteContainer";
import { Checkbox } from "@/components/ui/checkbox";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import AsyncSelect from "@/components/ui/react-select";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { ReactselectInterface } from "@/model/global-master";
import { WardObjectInterface } from "@/model/zone-master";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useStreetMasterController } from "@/controller/master/StreetMasterController";
import { StreetSelectOject } from "@/model/street-master";
import { usePropertytypeMasterController } from "@/controller/master/PropertyMasterController";
import { PropertytypeMasterObject } from "@/model/propertytype-master";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useNamunaController } from "@/controller/report/MilkatKarAkarniEight";
import { Label } from "@/components/ui/label";
import { Printer } from "lucide-react";
import { useTaxCalculateController } from "@/controller/tax/TaxCalculateController";
import axios from "axios";
import { toast } from "@/components/ui/use-toast";
import { Loader } from "@/components/globalcomponent/Loader";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Api from "@/services/ApiServices";
interface NamunaEightDetails {
  tax_property_id: string;
  bill_no: string;
  all_property_tax_sum: number;
  other_tax_sum_tax: number;
  total_tax: number;
  capital_value: number;
  bill_generation_date: string;
  property: {
    propertyNumber: string;
    old_propertyNumber: string;
    city_survey_number: string;
    address: string | null;
    house_or_apartment_name: string;
    note: string;
    zone: {
      zoneName: string;
    };
    ward: {
      ward_name: string;
    };
    street: {
      street_name: string;
    };
    water_connection_type: string;
    property_owner_details: {
      property_owner_details_id: string;
      name: string;
      owner_type: {
        owner_type: string;
      };
    }[];
  };
  tax_propertywise: {
    tax_value: number;
    tax: number;
    property_Usage_Details: {
      length: number;
      width: number;
      are_sq_meter: number;
      propertyType: {
        propertyType: string;
      };
    };
  }[];
  tax_property_other_taxes: {
    tax_type: string;
    amount: number;
  }[];
}

const NamunaTen = () => {
  const { t } = useTranslation();
  const wardList: any = useWardMasterController();
  const wardOptions: ReactselectInterface = wardList?.wardList?.map(
    (ward: WardObjectInterface) => ({
      value: ward.ward_id,
      label: ward.ward_name,
    })
  );
  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;

  // const { namunaEightDetails } = useNamunaController();
  const [printloading, setPrintLoading] = useState(false);
  const [searchOnParamter, setsearchOnParamter] = useState<string>();
  const [loading, setLoading] = useState(false);
  const [oldPropertyNumber, setPropertyOldNumber] = useState<any>();
  const [propertyNumber, setpropertyNumber] = useState<any>();
  const [namanuDetail, setnamanuDetail] = useState(null);
  const [financialYears, setFinancialYears] = useState([]);
  const [selectedFinancialYear, setSelectedFinancialYear] = useState("2024-2025");
  const { getVarshikKarAkarni } = useTaxCalculateController();


  // const handleNamunaTen = async () => {
  //   const SelectedpropertyNumber = oldPropertyNumber || propertyNumber;
  //   if (!SelectedpropertyNumber) {
  //     toast({
  //       title: "Please enter a property number.",
  //       variant: "destructive",
  //     });
  //     return;
  //   }
  //   setLoading(true);

  //   try {
  //     const response = await axios.get(
  //       `${apiBaseUrl}/v1/annual-kar-akarani/getVarshikKarAkarni?value=${SelectedpropertyNumber}&searchOn=${searchOnParamter}`
  //     );

  //     if (response.data.statusCode === 200 && response.data.data) {
  //       setnamanuDetail(response.data.data);
  //     } else {
  //       toast({
  //         title: "No data found.",
  //         variant: "destructive",
  //       });
  //     }
  //   } catch (error) {
  //     toast({
  //       title: "Failed to fetch data. Please try again.",
  //       variant: "destructive",
  //     });
  //   } finally {
  //     setLoading(false);
  //   }
  // };
  
  useEffect(() => {
    fetchFinancialYears();
  }, []);
  const fetchFinancialYears = async () => {
    try {
      const response = await Api.fyYears();
      if (response.data.statusCode === 200 && response.data.data) {
        setFinancialYears(response.data.data);
      }
    } catch (error) {
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
    }
  };
  const handleNamunaTen = async () => {
    const selectedPropertyNumber = oldPropertyNumber || propertyNumber;

    if (!selectedFinancialYear) {
      toast({
        title: `${t("selectFinancialYear")}`,
        variant: "destructive",
      });
      return;
    }
    if (!selectedPropertyNumber) {
      setnamanuDetail(null);
      toast({
        title: `${t("enterPropertyNumber")}`,
        variant: "destructive",
      });
      return;
    }
    setLoading(true);
    setnamanuDetail(null);
    try {
      const response = await getVarshikKarAkarni(
        selectedPropertyNumber,
        searchOnParamter,
        selectedFinancialYear
      );

      if (response.statusCode === 200 && response.data) {
        if (response.data.count_get__milkat_kar === 0 ) {
          toast({
            title: `${t("noDataFound")}`,
            variant: "destructive",
          });
        } else if (response.data.count_get__milkat_kar === 1) {
          toast({
            title: `${t("taxDemand.recordGeneratedForProperty")}`,
            variant: "success",
          });
        } else {
          setnamanuDetail(response.data);
        }
      } else {
        toast({
          title: `${t("noDataFound")}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching Varshik Kar Akarni:", error);
      toast({
        title: "Failed to fetch data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  

  const handleOldNumberChange = (e) => {
    setPropertyOldNumber(e.target.value.trim().toUpperCase());
    setpropertyNumber(() => ''); // Clear the other field
    setsearchOnParamter('old_propertyNumber');
  };

  const handlePropertyNumberChange = (e) => {
    setpropertyNumber(e.target.value.trim().toUpperCase());
    setPropertyOldNumber(() => ''); // Clear the other field
    setsearchOnParamter('propertyNumber');
  };


  const loadWardOptions = (
    inputValue: string,
    callback: (options: ReactselectInterface[]) => void
  ) => {
    setTimeout(() => {
      callback(
        wardOptions.filter((option: any) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      );
    }, 1000);
  };
  

  const streetorRoadList: any = useStreetMasterController();
  const streetOptions: ReactselectInterface = streetorRoadList?.streetList?.map(
    (street: StreetSelectOject) => ({
      value: street.street_id,
      label: street.street_name,
    })
  );
  const handlePrintTax = async (item) => {
    setPrintLoading(true);
    try {
      const response = await axios.get(
        `${apiBaseUrl}/api/v1/billing/get-bill-namuna-eight`,
        {
          responseType: "blob",
        }
      );

      const pdfBlob = new Blob([response.data], { type: "application/pdf" });

      const pdfUrl = URL.createObjectURL(pdfBlob);
      setPrintLoading(false);

      window.open(pdfUrl);
    } catch (error) {
      setPrintLoading(false);
      toast({
        title: "Failed to fetch the PDF. Please try again.",
        variant: "destructive",
      });
    }
  };
  const loadStreetOptions = (
    inputValue: string,
    callback: (options: ReactselectInterface[]) => void
  ) => {
    setTimeout(() => {
      callback(
        streetOptions.filter((option: any) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      );
    }, 1000);
  };

  const propertytypeList: any = usePropertytypeMasterController();
  const propertyOptions: ReactselectInterface =
    propertytypeList.propertytypeList?.map(
      (property: PropertytypeMasterObject) => ({
        value: property.propertyType_id,
        label: property.propertyType,
      })
    );

  const loadPropertyOptions = (
    inputValue: string,
    callback: (options: ReactselectInterface[]) => void
  ) => {
    setTimeout(() => {
      callback(
        propertyOptions.filter((option: any) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      );
    }, 1000);
  };
  const formatNumber = (num: number | string) => {
    const value = Number(num || 0);
    return value.toFixed(2);
  };
  const tableHeaders = [
    "अ क्र ",
    "namunaEight.tableIncomeType",
    "namunaEight.tableLengthHeading",
    "namunaEight.tableWidthHeading",
    "namunaEight.tableAreaHeading",
    "namunaEight.tableTaxRateHeading",
    "namunaEight.tableTotalTaxHeading",
  ];

  const taxesAndFees = [
    { label: "इमारत कर", key: "all_property_tax_sum" },
    { label: "आरोग्य कर दिवाबत्ती कर", key: "" },
    { label: "सामान्य पाणीपट्टी", key: "" },
    { label: "पडसर/ खुली/ इतर कर", key: "" },
    { label: "वृक्ष कर", key: "tax_type_1" },
    { label: "शिक्षण कर", key: "tax_type_2" },
    { label: "अग्निशमन कर", key: "" },
    { label: "रोजगार हमी कर", key: "tax_type_3" },
    { label: "कचरा संकलन शुल्क", key: "tax_type_4" },
    { label: "शास्ती /दंड रक्कम", key: "tax_type_5" },
    { label: "दंड रक्कम", key: "" },
    { label: "नोटीस रक्कम", key: "" },
  ];
  const warshikKar  = namanuDetail?.warshikKar[0]; 
  const total1 = [
   { label:"एकूण मागील मागणी", key: "total_tax_previous" },
   { label:"एकूण चालू मागणी", key: "total_tax_current" },
   { label:"एकूण नोटीस", key: "" },
   { label:"एकूण दंड", key: "" },
   { label:"एकूण शिल्लक", key: "" },
  ];
  const total2 = [


    { label:"एकूण मागील वसूल", key: "" },
    { label:"एकूण चालू वसूल", key: "" },
    { label:"मागील + चालू वसूल", key: "" },
    { label:"वॉरंट फी", key: "" },
    { label:"एकूण वसूल कर", key: "" },
    { label:"चालू शिल्लक", key: "" },
    { label:"एकूण शिल्लक", key: "" },
  ];
  return (
    <div className="flex h-fit">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold font-Poppins   w-full ml-3 ">
          नमुना १०
        </h1>
        <WhiteContainer>
          <div className="flex gap-x-8 flex-wrap">
            {/* <div className="flex">
              <Checkbox className="mt-1" />{" "}
              <p className="ml-2 ">
                {t("namunaEight.namunaEightCheckboxOneValue")}
              </p>
            </div>
            <div className="flex">
              <Checkbox className="mt-1" />
              <p className="ml-2 ">
                {t("namunaEight.namunaEightCheckboxTwoValue")}{" "}
              </p>
            </div> */}
            {/* <div className="flex">
              <Checkbox className="mt-1" />
              <p className="ml-2 ">
                {" "}
                {t("namunaEight.namunaEightCheckboxThreeValue")}{" "}
              </p>
            </div> */}
          </div>
          <div>
            {/* <p className="w-full flex items-center justify-between  text-[16px] font-semibold mt-4">
              {t("namunaEight.milkatDharkachiMahiti")}
            </p> */}
            <div className="mt-1 mb-3">
              <div className="grid md:grid-cols-4 gap-x-3 gap-y-3">
                {/* <div className="grid-cols-subgrid">
                  <AsyncSelect
                    placeholder={t("propertyLocationDetailsForm.ward")}
                    loadOptions={loadWardOptions}
                    defaultOptions={wardOptions}
                  />
                </div> */}
                <div className="grid-cols-subgrid">
                  <Label>{t("financialYear")}
                  <span className="ml-1 text-red-500">*</span>
                  </Label>
                  <Select onValueChange={setSelectedFinancialYear} value={selectedFinancialYear}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder={t("selectYear")} />
                    </SelectTrigger>
                    <SelectContent>
                      {financialYears.map((year) => (
                        <SelectItem key={year.financial_year_range} value={year.financial_year_range}>
                          {year.financial_year_range}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid-cols-subgrid">
                  <Label>{t("property.propertyNumberColumn")}</Label>
                  <Input
                    className="mt-1 block w-full"
                    transliterate={false}
                    placeholder={t("property.propertyNumberColumn")}
                    value={propertyNumber}
                    onChange={handlePropertyNumberChange}          
                    onKeyDown={(e) => e.key === 'Enter' && handleNamunaTen()}
                    />
                </div>
                <div className="grid-cols-subgrid">
                  <Label>{t("property.oldPropertyNumber")}</Label>
                  <Input
                    className="mt-1 block w-full"
                    transliterate={false}
                    placeholder={t("property.oldPropertyNumber")}
                    value={oldPropertyNumber}
                    onChange={handleOldNumberChange}
                    onKeyDown={(e) => e.key === 'Enter' && handleNamunaTen()}
                  />
                </div>


                <div className="grid-cols-subgrid flex items-end">
                  <Button variant="submit" onClick={handleNamunaTen}>
                    {t("search")}{" "}
                  </Button>
                </div>
              </div>
            </div>
          </div>
          {/* <p className="text-xs italic font-semibold mb-0 text-[#3c3c3c]"> {t('selectAnyTwoFieldNote')} </p> */}
        </WhiteContainer>
        {loading && (
          <div className="flex justify-center items-center my-4">
            <Loader />

          </div>
        )}
        {namanuDetail && !loading && (
          <WhiteContainer>
            <div className="grid grid-cols-12 gap-2 text-[15px] ">
              <div className="col-span-4 border-2 border-solid  p-2 rounded-md">
                <div className="flex w-full items-center mb-2">
                  <p className="w-1/2 text-nowrap"></p>
                  <div className="flex w-2/3 gap-3 font-semibold">
                    <div className="w-full ">
                      <p>मागील कर</p>
                    </div>
                    <div className="w-full">
                      <p>चालू कर</p>
                    </div>
                  </div>
                </div>

                <div  className="w-full">
                    <div className="flex w-full items-center mb-2">
                    <p className="w-1/2 text-nowrap "> इमारत कर :</p>

                      <div className="flex w-2/3 gap-3">
                        <Input
                          disabled
                          value={ "0.00"}
                          className="w-full border border-[#908d8d] border-solid "
                        />
                        <Input
                          disabled
                          value={formatNumber(warshikKar[`all_property_tax_sum_current`] || 0)}
                          className="w-full border border-[#908d8d] border-solid "
                        />
                      </div>
                    </div>
                  </div>

                {Object.entries(namanuDetail.tax_types).map(([key, value]: any) => (
                  
                  <div  className="w-full">
                    <div className="flex w-full items-center mb-2">
                      <p className="w-1/2 text-nowrap ">{value} :</p>
                      <div className="flex w-2/3 gap-3">
                        <Input
                          disabled
                          value={formatNumber(warshikKar[`${key}_previous `] || "0.00")}
                          className="w-full border border-[#908d8d] border-solid "
                        />
                        <Input
                          disabled
                          value={formatNumber(warshikKar[`${key}_current`] || "0.00")}
                          className="w-full border border-[#908d8d] border-solid "
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="col-span-3 border-2 p-2 border-solid rounded-md">
                {" "}
                <div className="w-full  mb-2">
                  <div className="grid grid-cols-3  gap-3 font-semibold">
                    <div className="w-full col-start-1">
                      <p> मागील वसूल</p>
                    </div>
                    <div className="w-full">
                      <p> चालू वसूल</p>
                    </div>
                    <div className="w-full">
                      <p>सूट रक्कम </p>
                    </div>
                  </div>
                </div>
                <div  className="w-full">
                    <div className="flex w-full items-center mb-2">
                      <div className="gap-3 grid grid-cols-3">
                        <Input
                          disabled
                          value={"0.00"}
                          className="w-full border border-[#908d8d] border-solid "
                        />
                        <Input
                          disabled
                          value={"0.00"}
                          className="w-full border border-[#908d8d] border-solid "
                        />
                        {(taxesAndFees[0].label == "इमारत कर" ||
                          taxesAndFees[0].label == "पडसर/ खुली/ इतर कर") && (
                            <Input
                              disabled
                              value={"0.00"}
                              className="w-full border border-[#908d8d] border-solid "
                            />
                          )}
                      </div>
                    </div>
                  </div>
                {Object.entries(namanuDetail.tax_types).map(([key, value]: any)  => (
                  <div  className="w-full">
                    <div className="flex w-full items-center mb-2">
                      <div className="gap-3 grid grid-cols-3">
                        <Input
                          disabled
                          value={"0.00"}
                          className="w-full border border-[#908d8d] border-solid "
                        />
                        <Input
                          disabled
                          value={"0.00"}
                          className="w-full border border-[#908d8d] border-solid "
                        />
                        {(taxesAndFees[0].label == "इमारत कर" ||
                          taxesAndFees[0].label == "पडसर/ खुली/ इतर कर") && (
                            <Input
                              disabled
                              value={"0.00"}
                              className="w-full border border-[#908d8d] border-solid "
                            />
                          )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="grid-cols-subgrid col-span-5 border-2 border-solid flex gap-4 pt-7 rounded-md p-2 h-fit">
                <div className="w-1/2">
                  {total1.map((item, index) => (
                    <div key={index} className="w-full">
                      <div className="flex w-full items-center mb-2">
                        <p className="w-2/3 text-nowrap">{item.label} :</p>
                        <div className="flex w-[50%] gap-3">
                          <Input
                            disabled
                            value={formatNumber(warshikKar[`${item.key}`]|| "0.00") }
                            className="w-full border border-[#908d8d] border-solid "
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="w-1/2">
                  {total2.map((item, index) => (
                    <div key={index} className="w-full">
                      <div className="flex w-full items-center mb-2">
                        <p className="w-2/3 text-nowrap">{item.label} :</p>
                        <div className="flex w-[50%] gap-3">
                          <Input
                            disabled
                            value={formatNumber(warshikKar[`${item.key}`] || "0.00")}
                            className="w-full border border-[#908d8d] border-solid "
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="  flex align-bottom items-end justify-end ">
              {/* <Button variant="submit" className="mt-3 mr-3">
                    {t("namunaEight.newTaxRate")}{" "}
                  </Button> */}
              <Button
                className={`bg-[#2c93d2] hover:bg-[#2c93d2] ${printloading ? "cursor-not-allowed opacity-50" : ""}`}
                onClick={handlePrintTax}
                disabled={printloading}
              >
                <span className={printloading ? "hidden" : "relative"}>
                  {t("paymentform.printButton")}
                </span>
                <Printer
                  className={`w-5 ${printloading ? "animate-bounce" : "ml-4"}`}
                />
              </Button>
            </div>
          </WhiteContainer>
        )}
      </div>
    </div>
  );
};

export default NamunaTen;
