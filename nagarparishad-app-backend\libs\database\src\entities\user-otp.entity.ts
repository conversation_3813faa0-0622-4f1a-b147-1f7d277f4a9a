import {
  BaseEntity,
  Column,
  CreateDateC<PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserMasterEntity } from './userMaster.entity';

@Entity('user_otp')
export class UserOtpEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  otpId: string;

  @ManyToOne(() => UserMasterEntity, (user) => user.user_id)
  @JoinColumn({ name: 'user_id' })
  user: UserMasterEntity;

  @Column({ name: 'otp', type: 'varchar', nullable: false })
  otp: string;

  @Column({ name: 'expires_at', type: 'timestamp', nullable: false })
  expiresAt: Date;

  @Column({name: 'status', type: 'int', nullable: true})
  status: number;

  @Column({name: 'mobile_number', type: 'varchar', nullable: true})
  mobile_number: string;

  @Column({name: 'verificationId', type: 'varchar', nullable: true})
  verificationId: string;

  @Column({ name: 'key_value', type: 'jsonb', nullable: true })
  additionalInfo: object;


  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
