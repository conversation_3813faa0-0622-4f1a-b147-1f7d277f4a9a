import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class ZoneMasterDto {
  @ApiProperty({
    name: 'zone_id',
    description: 'zone id to fetch the singe result ',
    type: 'string',
  })
  @IsNotEmpty()
  @IsString()
  zone_id: string;
}

export class GetWardMastereDto {
  @ApiProperty({
    name: 'ward',
    description: "ward id to fetch all the zone's which have ward ",
    type: 'string',
  })
  @IsNotEmpty()
  @IsUUID()
  ward: string;
}
