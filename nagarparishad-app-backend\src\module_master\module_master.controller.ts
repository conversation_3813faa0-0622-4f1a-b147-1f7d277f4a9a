import { Body, Controller, Get, Post } from '@nestjs/common';
import { ModuleMasterService } from './module_master.service';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CreateModuleMasterDto } from './dto/module-master.dto';

@ApiTags('Module Master')
@Controller('module-master')
export class ModuleMasterController {
  constructor(private readonly moduleMasterService: ModuleMasterService) {}

  @ApiOperation({ summary: 'Create a new Module' })
  @ApiResponse({
    status: 201,
    description: 'The Module  has been successfully created',
  })
  @Post()
  create(@Body() createModuleMasterDto: CreateModuleMasterDto) {
    return this.moduleMasterService.create(createModuleMasterDto);
  }

  @ApiOperation({ summary: 'Create a new Module' })
  @ApiResponse({
    status: 201,
    description: 'The Module  has been successfully created',
  })
  @ApiResponse({
    status: 404,
    description: 'The Module  does not exist',
  })
  @Get()
  getAll() {
    return this.moduleMasterService.getData();
  }
}
