import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportPropertyUpdate1724656205503 implements MigrationInterface {
    name = 'ImportPropertyUpdate1724656205503'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ADD "ward_number" character varying `);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "owner_name" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "owner_type" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "bhogawat_owner_name" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "usage_type" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "usage_desc" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "construction_year" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "length" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "width" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "sq_ft" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "sq_meter" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "sq_meter"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "sq_ft"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "width"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "length"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "construction_year"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "usage_desc"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "usage_type"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "bhogawat_owner_name"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "owner_type"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "owner_name"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "ward_number"`);
    }

}
