import { Repository } from 'typeorm';
import { Import_PropertyEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import {
  PaginatedResult,
  PaginationOptions,
  paginate,
} from '@helper/helpers/Pagination';
import { Logger } from '@nestjs/common';

export class ImportPropertyMasterRepository extends Repository<Import_PropertyEntity> {
  constructor(
    @InjectRepository(Import_PropertyEntity)
    private readonly propertyMasterRepository: Repository<Import_PropertyEntity>,
  ) {
    super(
      propertyMasterRepository.target,
      propertyMasterRepository.manager,
      propertyMasterRepository.queryRunner,
    );
  }

  async saveData(input: any) {
    let data = this.propertyMasterRepository.create(input);
    data = await this.propertyMasterRepository.save(data);
    return data;
  }
  async updateBhogwatadharName(id: any, name: string) {
    return await this.propertyMasterRepository
      .createQueryBuilder('import_property')
      .update()
      .set({ bhogawat_owner_name: name })
      .where('import_property.property_id = :id', { id })
      .execute();
  }
  


  async getBlankStreetCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.street_name IS NULL OR import_property.street_name = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.street_name IS NULL OR import_property.street_name = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }



  }

  async getBlankZoneNameCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.zone_name IS NULL OR import_property.zone_name = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.zone_name IS NULL OR import_property.zone_name = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }

  }

  async getBlankPropertyNumberCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.propertyNumber IS NULL OR import_property.propertyNumber = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.propertyNumber IS NULL OR import_property.propertyNumber = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }

  }

  async getBlankOldPropertyNumberCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.old_propertyNumber IS NULL OR import_property.old_propertyNumber = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.old_propertyNumber IS NULL OR import_property.old_propertyNumber = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }

  }

  async getBlankOwnerNameCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.owner_name IS NULL OR import_property.owner_name = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.owner_name IS NULL OR import_property.owner_name = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }

  }

  async getblankOwnerTypeCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.owner_type IS NULL OR import_property.owner_type = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.owner_type IS NULL OR import_property.owner_type = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }

  }

  async getBlankUsageTypeCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.usage_type IS NULL OR import_property.usage_type = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.usage_type IS NULL OR import_property.usage_type = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }

  }

  async getBlankUsageDescCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.usage_desc IS NULL OR import_property.usage_desc = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.usage_desc IS NULL OR import_property.usage_desc = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }

  }

  async getBlankConstructionYearCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.construction_year IS NULL OR import_property.construction_year = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.construction_year IS NULL OR import_property.construction_year = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }

  }

  async getBlankLengthCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.length IS NULL OR import_property.length = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.length IS NULL OR import_property.length = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      } 
  }



  async getBlankWidthCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.width IS NULL OR import_property.width = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.width IS NULL OR import_property.width = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }

  }

  async getBlankSqftCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.sq_ft IS NULL OR import_property.sq_ft = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.sq_ft IS NULL OR import_property.sq_ft = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }

  }

  async getBlankSqmeterCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where(
          '(import_property.sq_meter IS NULL OR import_property.sq_meter = :empty) AND import_property.ward_number = :ward_number',
          { empty: '', ward_number }
        )
        .getCount();
      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            '(import_property.sq_meter IS NULL OR import_property.sq_meter = :empty) ',
            { empty: '' }
          )
          .getCount();
        return count || 0;
      }

  }

  async getokCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where('import_property.ward_number = :ward_number AND status=:ok', { ward_number ,ok: 'ok' })
        .getCount();

      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          .where(
            'status="ok"'            
          )
          .getCount();
        return count || 0;
      }

  }

  async getotalCount(ward_number) {
    if (ward_number != 0) {
      const count: number = await this.propertyMasterRepository
        .createQueryBuilder('import_property')
        .where('import_property.ward_number = :ward_number', { ward_number })
        .getCount();

      return count || 0;
    } else
      if (ward_number == 0) {
        const count: number = await this.propertyMasterRepository
          .createQueryBuilder('import_property')
          // .where(
          //   '(import_property.street_name IS NULL OR import_property.street_name = :empty) ',
          //   { empty: '' }
          // )
          .getCount();
        return count || 0;
      }

  }







}
