import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';
import { PaidDataService } from './paid-data.service';
import { PaginationDto } from '@helper/helpers/Pagination';

@ApiTags('Paid Data')
@Controller('paid-data')
export class PaidDataController {
  constructor(private readonly paidDataService: PaidDataService) {}

 // @Form('PaidData')
//  @Permissions('can_read')
  @ApiOperation({ summary: 'Get year-wise data for a property' })
  @ApiResponse({ status: 200, description: 'Returns year-wise data for the property' })
  @Get('year-wise-data')
  async getYearWiseData(
    @Query('propertyId') propertyId: string,
    @Query('year') year?: string
  ) {
    if (!propertyId) {
      return {
        message: 'Property ID is required',
        success: false
      };
    }

    const data = await this.paidDataService.getYearWiseDataByPropertyId(propertyId, year);
    return {
      message: 'Year-wise data retrieved successfully',
      success: true,
      data
    };
  }

 // @Form('PaidData')
//  @Permissions('can_write')
  @ApiOperation({ summary: 'Add or update year-wise data for a property' })
  @ApiResponse({ status: 200, description: 'Successfully added/updated year-wise data' })
  @Post('year-wise-data/:paidDataId')
  async addYearWiseData(
    @Param('paidDataId') paidDataId: string,
    @Body() data: { year: string, data: any }
  ) {
    if (!paidDataId || !data.year || !data.data) {
      return {
        message: 'Paid data ID, year, and data are required',
        success: false
      };
    }

    await this.paidDataService.addYearWiseData(paidDataId, data.year, data.data);
    return {
      message: 'Year-wise data added/updated successfully',
      success: true
    };
  }

 // @Form('PaidData')
//  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all paid data for a property' })
  @ApiResponse({ status: 200, description: 'Returns all paid data for the property' })
  @Get('by-property/:propertyId')
  async getPaidDataByPropertyId(@Param('propertyId') propertyId: string) {
    if (!propertyId) {
      return {
        message: 'Property ID is required',
        success: false
      };
    }

    const data = await this.paidDataService.getPaidDataByPropertyId(propertyId);
    return {
      message: 'Paid data retrieved successfully',
      success: true,
      data
    };
  }

 // @Form('PaidData')
//  @Permissions('can_write')
  @ApiOperation({ summary: 'Generate paid and non-paid data for a financial year' })
  @ApiResponse({ status: 200, description: 'Successfully generated paid and non-paid data' })
  @Post('generate-paid-non-paid-data')
  async generatePaidAndNonPaidData() {
  

    try {
      const result = await this.paidDataService.generatePaidAndNonPaidData();
      return result;
    } catch (error) {
      return {
        message: `Error generating data: ${error.message}`,
        success: false
      };
    }
  }

 // @Form('PaidData')
//  @Permissions('can_read')
  @ApiOperation({ summary: 'Get paid and non-paid data with pagination' })
  @ApiResponse({ status: 200, description: 'Returns paginated paid and non-paid data' })
  @Get('paid-non-paid-data')
  async getPaidAndNonPaidData(
    @Query() paginationDto?: PaginationDto
  ) {
    try {
      // Set default pagination values
      const options = {
        page: paginationDto?.page || 1,
        limit: paginationDto?.limit || 200,
        sortBy: paginationDto?.sortBy || 'updatedAt',
        // sortOrder: paginationDto?.sortOrder || 'DESC',
        showAll: paginationDto?.showAll || false,
        filters: paginationDto?.filters || []
      };

      const data = await this.paidDataService.getPaidAndNonPaidData( options);
      return {
        message: 'Data retrieved successfully',
        success: true,
        data
      };
    } catch (error) {
      return {
        message: `Error retrieving data: ${error.message}`,
        success: false
      };
    }
  }

 // @Form('PaidData')
//  @Permissions('can_read')
  @ApiOperation({ summary: 'Get only paid users data' })
  @ApiResponse({ status: 200, description: 'Returns paginated paid users data' })
  @Get('paid-users')
  async getPaidUsersData(
    @Query('financialYearRange') financialYearRange?: string,
    @Query() paginationDto?: any
  ) {
    console.log("here in paid users")
    try {
      // Set default pagination values
      const options = {
        page: paginationDto?.page || 1,
        limit: paginationDto?.limit || 200,
        sortBy: paginationDto?.sortBy || 'updatedAt',
        // sortOrder: paginationDto?.sortOrder || 'DESC',
        showAll: paginationDto?.showAll || false,
        filters: paginationDto?.filters || []
      };

      const {value,searchOn}=paginationDto
      const data = await this.paidDataService.getPaidUsersData( options,value,searchOn);
      return {
        message: 'Paid users data retrieved successfully',
        success: true,
        data
      };
    } catch (error) {
      return {
        message: `Error retrieving paid users data: ${error.message}`,
        success: false
      };
    }
  }

 // @Form('PaidData')
//  @Permissions('can_read')
  @ApiOperation({ summary: 'Get only non-paid users data' })
  @ApiResponse({ status: 200, description: 'Returns paginated non-paid users data' })
  @Get('non-paid-users')
  async getNonPaidUsersData(
    @Query('financialYearRange') financialYearRange?: string,
    @Query() paginationDto?: any
  ) {
    try {
      // Set default pagination values
      const options = {
        page: paginationDto?.page || 1,
        limit: paginationDto?.limit || 200,
        sortBy: paginationDto?.sortBy || 'updatedAt',
        // sortOrder: paginationDto?.sortOrder || 'DESC',
        showAll: paginationDto?.showAll || false,
        filters: paginationDto?.filters || []
      };

      const {value,searchOn}=paginationDto

      const data = await this.paidDataService.getNonPaidUsersData( options,value,searchOn);
      return {
        message: 'Non-paid users data retrieved successfully',
        success: true,
        data
      };
    } catch (error) {
      return {
        message: `Error retrieving non-paid users data: ${error.message}`,
        success: false
      };
    }
  }

  // @Form('PaidData')
  // @Permissions('can_update')
  @ApiOperation({ summary: 'Update paid/non-paid data for a specific property' })
  @ApiResponse({ status: 200, description: 'Successfully updated property paid data' })
  @Post('update-property-paid-data/:propertyId')
  async updatePropertyPaidData(
    @Param('propertyId') propertyId: string,
    @Query('financialYearRange') financialYearRange?: string
  ) {
    try {
      if (!propertyId) {
        return {
          message: 'Property ID is required',
          success: false
        };
      }

      const result = await this.paidDataService.updatePropertyPaidData(propertyId, financialYearRange);

      if (!result) {
        return {
          message: 'No paid/non-paid data record found for this property and financial year',
          success: false
        };
      }

      return {
        message: 'Property paid data updated successfully',
        success: true,
        data: result
      };
    } catch (error) {
      return {
        message: `Error updating property paid data: ${error.message}`,
        success: false
      };
    }
  }

  // @Form('PaidData')
  // @Permissions('can_update')
  @ApiOperation({ summary: 'Batch update paid/non-paid data for multiple properties' })
  @ApiResponse({ status: 200, description: 'Successfully started batch update' })
  @Post('batch-update-properties-paid-data')
  async batchUpdatePropertiesPaidData(
    @Body() data: { propertyIds: string[], financialYearRange?: string }
  ) {
    try {
      if (!data.propertyIds || !Array.isArray(data.propertyIds) || data.propertyIds.length === 0) {
        return {
          message: 'Property IDs array is required and cannot be empty',
          success: false
        };
      }

      const result = await this.paidDataService.batchUpdatePropertiesPaidData(
        data.propertyIds,
        data.financialYearRange
      );

      return {
        message: result.message,
        success: true,
        data: result
      };
    } catch (error) {
      return {
        message: `Error starting batch update: ${error.message}`,
        success: false
      };
    }
  }
}
