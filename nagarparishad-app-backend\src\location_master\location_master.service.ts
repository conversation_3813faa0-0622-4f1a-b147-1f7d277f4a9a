import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import { CreateLocationMasterDto } from './dto/create-location_master.dto';
import { UpdateLocationMasterDto } from './dto/update-location_master.dto';
import { LocationRepository } from 'libs/database/repositories/location-master.repository';
import { LocationMasterDto } from './dto/location-master.dto';

@Injectable()
export class LocationMasterService {
  constructor(private readonly locationRepository: LocationRepository) {}
  async create(createLocationMasterDto: CreateLocationMasterDto) {
    try {
      const saveLocation = await this.locationRepository.saveLocation(
        createLocationMasterDto,
      );

      return {
        message: 'Location Saved SuccessFully',
        data: saveLocation,
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllLocation = await this.locationRepository.findAllLocation();

      if (!getAllLocation) {
        throw new NotFoundException('Ward Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllLocation,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(locationMasterDto: LocationMasterDto) {
    try {
      const { location_id } = locationMasterDto;
      const checkLocation = await this.locationRepository.findById(location_id);
      if (!checkLocation) {
        throw new NotFoundException('Location Not found');
      }

      return {
        message: 'location found Sucess',
        data: checkLocation,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    locationMasterDto: LocationMasterDto,
    updateLocationMasterDto: UpdateLocationMasterDto,
  ) {
    try {
      const { location_id } = locationMasterDto;
      const checkLocation = await this.locationRepository.findById(location_id);

      if (!checkLocation) {
        throw new NotFoundException('Ward Not Found');
      }

      const updateLocation = await this.locationRepository.updateLocation(
        location_id,
        updateLocationMasterDto,
      );

      if (updateLocation.affected === 0) {
        throw new NotAcceptableException('Failed to update Location');
      }

      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(locationMasterDto: LocationMasterDto) {
    try {
      const { location_id } = locationMasterDto;
      const checkLocation = await this.locationRepository.findById(location_id);
      if (!checkLocation) {
        throw new NotFoundException('Location Not Found');
      }

      const deleteLocation =
        await this.locationRepository.deleteLocation(location_id);

      if (deleteLocation.affected === 0) {
        throw new NotAcceptableException('Failed to delete Location');
      }

      return {
        message: 'Location Delete Successfully',
        data: deleteLocation,
      };
    } catch (error) {
      throw error;
    }
  }
}
