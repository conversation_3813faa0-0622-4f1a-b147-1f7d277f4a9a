export interface ZoneMasterObject {
  id: string;
  zone_id: string;
  zoneName: string;
  ward_id: number;
  wardName?: string;
}

export interface MasterObjectColumnDef {
  accessorKey: string; // Key to access data in each row (T[accessorKey])
  header: string; // Column header text
  [key: string]: any; // Allow for custom column properties (optional)
}

export interface WardObjectInterface {
  ward_id: string;
  ward_name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}
export interface WardMasterApiResponse {
  statusCode?: number;
  message: string;
  data: WardObjectInterface[] | null;
}

export interface ZoneObjectInterface {
  zoneName: string;
}

export interface ZoneMasterAddApiResp {
  statusCode: number;
  message: string;
}

export interface WardObject {
  ward_id: string;
  ward_name: string;
}
export interface ZoneObject {
  zone_id: string;
  zoneName: string;
  ward: WardObject;
}

export interface ZoneUpdateApi {
  statusCode: number;
  message: string;
}

export interface ZoneDeleteResponse {
  data: ZoneUpdateApi;
}
export interface ZoneMasterListApi {
  statusCode: number;
  message: string;
  data: ZoneObject[];
}
