import { Controller, Get, Post, Put, Delete, Query, Body } from '@nestjs/common';
import { MasterRRRateService } from './master-rr-rate.service';
import { Master_rr_rateEntity } from 'libs/database/entities';
import { CreateRrRateDto } from './dto/create-rr-rate.dto';
import { UpdateRrRateDto } from './dto/update-rr-rate.dto';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@Controller('master-rr-rate')
export class MasterRRRateController {
  constructor(private readonly rrRateService: MasterRRRateService) {}

  
  @Form('RR Rate')
  @Permissions('can_write')
  @Post('create')
  async create(
    @Body() data: CreateRrRateDto
  ): Promise<{ message: string; data: Master_rr_rateEntity[] }> {
    return this.rrRateService.create(data);
  }

  
  @Form('RR Rate')
  @Permissions('can_read')
  @Get()
  async findAll(): Promise<{ message: string; data: Master_rr_rateEntity[] }> {
    return this.rrRateService.findAll();
  }

  
  @Form('RR Rate')
  @Permissions('can_read')
  @Get('findOne')
  async findOne(
    @Query('id') id: string
  ): Promise<{ message: string; data: Master_rr_rateEntity }> {
    return this.rrRateService.findOne(id);
  }

  
  @Form('RR Rate')
  @Permissions('can_update')
  @Put('update')
  async update(
    @Query('id') id: string,
    @Body() data: UpdateRrRateDto
  ): Promise<{ message: string; data: Master_rr_rateEntity }> {
    return this.rrRateService.update(id, data);
  }

  @Form('RR Rate')
  @Permissions('can_delete')
  @Delete('delete')
  async delete(@Query('id') id: string): Promise<{ message: string }> {
    return this.rrRateService.delete(id);
  }
}
