import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdatedCollectoMaster1744199655024 implements MigrationInterface {
    name = 'UpdatedCollectoMaster1744199655024'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "collector_master" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "collector_master" DROP CONSTRAINT "FK_9dce066be06d8d85ce2f257e930"`);
        await queryRunner.query(`ALTER TABLE "collector_master" DROP CONSTRAINT "UQ_9dce066be06d8d85ce2f257e930"`);
        await queryRunner.query(`ALTER TABLE "collector_master" ADD CONSTRAINT "FK_9dce066be06d8d85ce2f257e930" FOREIGN KEY ("role_id") REFERENCES "role_master"("role_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
  }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "collector_master" DROP CONSTRAINT "FK_9dce066be06d8d85ce2f257e930"`);
        await queryRunner.query(`ALTER TABLE "collector_master" ADD CONSTRAINT "UQ_9dce066be06d8d85ce2f257e930" UNIQUE ("role_id")`);
        await queryRunner.query(`ALTER TABLE "collector_master" ADD CONSTRAINT "FK_9dce066be06d8d85ce2f257e930" FOREIGN KEY ("role_id") REFERENCES "role_master"("role_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "collector_master" DROP COLUMN "deleted_at"`);
    }

}
