import WhiteContainer from "@/components/custom/WhiteContainer";
import React from "react";
import { useLocation } from "react-router-dom";

const PropertyDetails = () => {
  const location: any = useLocation();
  const state: any = location.state;

  console.log(state);

  return (
    <>

      <div className="bg-Secondary w-full py-9">
        <WhiteContainer className="p-10 max-w-[80%] mx-auto">
          <h2 className="text-xl font-semibold mb-4 text-center">
            उपलब्ध मालमत्ता
          </h2>
          <div className="dialog-box payment-box !overflow-y-auto !no-scrollbar">
            <h1 className="text-2xl	font-semibold leading-6	capitalize mb-1">
              Property Details
            </h1>

            <div className="ticket-info border-b-[1px] border-solid border-[#ffffff7c] py-4">
              <div className="flex w-1/2 break-all	justify-center items-center">
                <p className="sm:w-3/2 w-1/2 leading-5 text-md font-bold text-left my-1 ">
                  {"FirstName"}
                </p>
                <p className="sm:w-3/2 w-1/2 leading-5 text-lg font-semibold text-left my-1 ">
                  {"Sneha"}
                </p>
              </div>
            </div>
          </div>
        </WhiteContainer>
      </div>
    </>
  );
};

export default PropertyDetails;
