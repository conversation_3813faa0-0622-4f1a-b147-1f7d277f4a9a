import React, { useState } from 'react';
import { X, Download, Trash2, <PERSON><PERSON>ircle, AlertCircle, Clock, Loader2, Bell } from 'lucide-react';
import { useNotifications } from '@/context/NotificationContext';
import { Notification } from '@/types/notification';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';

interface NotificationSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const NotificationSidebar: React.FC<NotificationSidebarProps> = ({ isOpen, onClose }) => {
  const { notifications, unreadCount, markAsRead, removeNotification, markAllAsRead, clearAll, downloadNotificationFile } = useNotifications();
  const [filter, setFilter] = useState<'pending' | 'completed' | 'failed'>('pending');

  const getNotificationIcon = (notification: Notification) => {
    // Treat 'delivered' as 'completed'
    const status = (notification.status === 'delivered') ? 'completed' : notification.status;
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'processing':
        return <Loader2 className="w-5极狐 h-5 text-blue-500 animate-spin" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getNotificationColor = (notification: Notification) => {
    // Treat 'delivered' as 'completed'
    const status = (notification.status === 'delivered') ? 'completed' : notification.status;
    switch (status) {
      case 'completed':
        return 'border-l-green-500 bg-green-50 hover:bg-green-100';
      case 'failed':
        return 'border-l-red-500 bg-red-50 hover:bg-red-100';
      case 'processing':
        return 'border-l-blue-500 bg-blue-50 hover:bg-blue-100';
      default:
        return 'border-l-gray-500 bg-gray-50 hover:bg-gray-100';
    }
  };

  const handleDownload = async (notification: Notification) => {
    if (notification.downloadUrl) {
      try {
        const result = await downloadNotificationFile(notification.id);
        if (result.success) {
          // Mark as read after successful download
          markAsRead(notification.id);
        } else {
          console.error('Download failed:', result.message);
          // You could show a toast notification here
        }
      } catch (error) {
        console.error('Download error:', error);
      }
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'pending') {
      return ['pending', 'processing'].includes(notification.status);
    } else if (filter === 'completed') {
      return ['completed', 'delivered'].includes(notification.status);
    } else if (filter === 'failed') {
      return notification.status === 'failed';
    }
    return false;
  });

  const getFilterCount = (status: string) => {
    if (status === 'pending') {
      return notifications.filter(n => ['pending', 'processing'].includes(n.status)).length;
    } else if (status === 'completed') {
      return notifications.filter(n => ['completed', 'delivered'].includes(n.status)).length;
    } else if (status === 'failed') {
      return notifications.filter(n => n.status === 'failed').length;
    }
    return 0;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex">
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      {/* Sidebar */}
      <div className="relative ml-auto w-96 bg-white shadow-xl h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Bell className="w-5 h-5" />
              <h2 className="text-lg font-semibold">Notifications</h2>
              {unreadCount > 0 && (
                <Badge variant="destructive">{unreadCount}</Badge>
              )}
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Action buttons */}
          <div className="flex space-x-2 mt-3">
            <Button
              variant="outline"
              size="sm"
              onClick={markAllAsRead}
              disabled={unreadCount === 0}
            >
              Mark all read
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearAll}
              disabled={notifications.length === 0}
            >
              <Trash2 className="w-3 h-3 mr-1" />
              Clear all
            </Button>
          </div>
        </div>

        {/* Filter tabs - Show Pending, Completed, and Failed tabs */}
        <div className="p-4 border-b">
          <div className="flex space-x-1">
            {[
              { key: 'pending', label: 'Pending' },
              { key: 'completed', label: 'Completed' },
              { key: 'failed', label: 'Failed' }
            ].map(({ key, label }) => (
              <Button
                key={key}
                variant={filter === key ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setFilter(key as any)}
                className="text-xs"
              >
                {label} ({getFilterCount(key)})
              </Button>
            ))}
          </div>
        </div>

        {/* Notifications list */}
        <ScrollArea className="flex-1">
          {filteredNotifications.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No notifications</p>
              <p className="text-sm">
                {`No ${filter} notifications`}
              </p>
            </div>
          ) : (
            <div className="p-4 space-y-3">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 rounded-lg border-l-4 transition-colors ${getNotificationColor(notification)} ${
                    notification.persistent ? 'ring-2 ring-blue-200' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      {getNotificationIcon(notification)}
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm mb-1">
                          {notification.title}
                        </h3>
                        <p className="text-sm text-gray-600 mb-2">
                          {notification.message}
                        </p>
                        
                        {/* Progress bar for processing notifications */}
                        {notification.status === 'processing' && notification.progress !== undefined && (
                          <div className="mb-3">
                            <div className="flex justify-between text-xs text-gray-500 mb-1">
                              <span>Progress</span>
                              <span>{notification.progress}%</span>
                            </div>
                            <Progress value={notification.progress} className="h-2" />
                            {notification.metadata?.processedRecords && notification.metadata?.totalRecords && (
                              <p className="text-xs text-gray-500 mt-1">
                                {notification.metadata.processedRecords} of {notification.metadata.totalRecords} records processed
                              </p>
                            )}
                          </div>
                        )}
                        
                        {/* Metadata */}
                        {notification.metadata && (
                          <div className="text-xs text-gray-500 mb-2">
                            {notification.metadata.reportType && (
                              <span className="inline-block bg-gray-200 px-2 py-1 rounded mr-2">
                                {notification.metadata.reportType.replace('_', ' ').toUpperCase()}
                              </span>
                            )}
                            {notification.metadata.fileName && (
                              <span>File: {notification.metadata.fileName}</span>
                            )}
                          </div>
                        )}
                        
                        {/* Action buttons */}
                        <div className="flex items极狐-center justify-between">
                          <span className="text-xs text-gray-400">
                            {formatTimeAgo(notification.createdAt)}
                          </span>
                          
                          <div className="flex space-x-2">
                            {(notification.status === 'completed' || notification.status === 'delivered') && notification.downloadUrl && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDownload(notification)}
                                className="h-7 text-xs"
                              >
                                <Download className="w-3 h-3 mr-1" />
                                Download
                              </Button>
                            )}
                            
                            {notification.persistent && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => markAsRead(notification.id)}
                                className="h-7 text-xs"
                              >
                                Mark read
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 ml-2"
                      onClick={() => removeNotification(notification.id)}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>
    </div>
  );
};

export default NotificationSidebar;
