import {
  BaseEntity,
  ManyToOne,
  OneToMany,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PropertyEntity } from './property.entity';
import { Tax_PropertyWiseEntity } from './tax_propertywise.entity';
import { Tax_PropertyEntity_Other_Taxes } from './tax_property_other_taxes.entity';

@Entity('tax_property')
export class Tax_PropertyEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  tax_property_id: string;


  @ManyToOne(() => PropertyEntity, (property) => property.tax_Property, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;


  @Column({
    type: String,
    name: 'financial_year',
    nullable: false,
  })

  @Column({ type: String, nullable: false })
  bill_no: string;
  
  @Column({ type: String, nullable: false })
  financial_year: string;

  @Column({ type: 'float', nullable: false , default: 0})
  all_property_tax_sum: number;

  // @Column({ type: 'float', nullable: false })
  // tree_tax: number;


  // @Column({ type: 'float', nullable: false })
  // educational_tax: number;

  
  // @Column({ type: 'float', nullable: false })
  // employment_tax: number;

  
  // @Column({ type: 'float', nullable: false })
  // fire_ext_tax: number;

  
  // @Column({ type: 'float', nullable: false })
  // trash_tax: number;

  // @Column({ type: 'float', nullable: false , default:0})
  // punishment_tax: number;
  
  @Column({ type: 'float'})
  other_tax_sum_tax: number;
  
  @Column({ type: 'float'})
  total_tax: number;

  @Column({ type: 'date' })
  bill_generation_date: Date;

  @OneToMany(() => Tax_PropertyWiseEntity, (tax_propertywise) => tax_propertywise.tax_Property)
  tax_propertywise: Tax_PropertyWiseEntity[];

  @OneToMany(() => Tax_PropertyEntity_Other_Taxes, (tax_property_other_taxes) => tax_property_other_taxes.tax_Property)
  tax_property_other_taxes: Tax_PropertyEntity_Other_Taxes[];

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
