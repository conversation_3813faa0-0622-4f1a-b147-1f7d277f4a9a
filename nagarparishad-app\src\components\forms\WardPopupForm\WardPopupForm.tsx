import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { WardUpdateObject } from "@/model/ward-master";
import { toast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { Loader2 } from "lucide-react";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { ReactTransliterate } from "react-transliterate";

interface WardPopupFormInterface {
  btnTitle: string;
  editData?: WardUpdateObject;
}

const WardPopupForm = ({ btnTitle, editData }: WardPopupFormInterface) => {
  const { t } = useTranslation();

  const schema = z.object({
    ward: z.string().trim().min(1, t("errorsRequiredField")),
  });
  const dynamicValues = {
    name: t("ward.wardLabel"),
  };

  const { setOpen, refreshWardList, setRefreshWardList } =
    useContext(GlobalContext);
  const { createWard, updateWard } = useWardMasterController();
  const [loader, setLoader] = useState(false);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      ward: editData?.ward_name || " ",
    },
  });
  const {
    formState: { errors },
    reset,
  } = form;

  const onSubmit = (data: z.infer<typeof schema>) => {
    const DataResponse = {
      ward_name: data.ward,
    };

    setLoader(true);

    if (editData?.ward_id) {
      console.log("update");
      updateWard(
        { wardId: editData.ward_id, wardData: DataResponse },
        {
          onSuccess: (response) => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            resetForm();
          },
          onError: (error) => {
            toast({
              variant: "destructive",
            });
            setLoader(false);
          },
        }
      );
    } else {
      console.log("creaete");

      createWard(DataResponse, {
        onSuccess: (response) => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          resetForm();
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });
    }
  };

  const resetForm = () => {
    reset();
    form.reset({ ward: "" });
    setOpen(false);
    setRefreshWardList(!refreshWardList);
    setLoader(false);
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        ward: editData.ward_name || "",
      });
    } else {
      form.reset({
        ward: "",
      });
    }
  }, [editData]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full md:w-full">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="ward"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>{t("ward.wardLabel")}</FormLabel>
                  <FormControl className="mt-1">
                    <Input placeholder={t("ward.wardLabel")} {...field} />
                    {/* <ReactTransliterate
                      onChangeText={function (text: string): void {
                        console.log(text);
                      }}
                      className="mt-1 block w-full"
                      placeholder={t("propertyLocationDetailsForm.blockNumber")}
                      {...field}
                      lang="mr" // Set the language code (e.g., 'hi' for Hindi)
                    /> */}
                  </FormControl>
                  {errors.ward && (
                    <FormMessage className="ml-1">
                      {t("errors.requiredField")}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
            <div className="grid-cols-subgrid  mb-1 ml-4 max-md:flex max-md:justify-end  pt-[32px] ">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
        </div>
       
      </form>
    </Form>
  );
};

export default WardPopupForm;
