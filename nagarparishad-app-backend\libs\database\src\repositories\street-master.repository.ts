import { Repository } from 'typeorm';
import { StreetMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class StreetMasterRepository extends Repository<StreetMasterEntity> {
  constructor(
    @InjectRepository(StreetMasterEntity)
    private readonly streetMasterRepository: Repository<StreetMasterEntity>,
  ) {
    super(
      streetMasterRepository.target,
      streetMasterRepository.manager,
      streetMasterRepository.queryRunner,
    );
  }

  // async saveStreet(input: {
  //   streetOrRoadName: string;
  // }): Promise<StreetMasterEntity> {
  //   let location = this.streetMasterRepository.create(input);
  //   location = await this.streetMasterRepository.save(location);
  //   return location;
  // }

  async findAllStreets() {
    return await this.streetMasterRepository
      .createQueryBuilder('street_master')
      .orderBy('street_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string) {
    return await this.streetMasterRepository
      .createQueryBuilder('street_master')
      .where('street_master.streetOrRoadId = :streetOrRoadId', {
        streetOrRoadId: id,
      })
      .getOne();
  }

  // async updateStreet(id: string, input: { streetOrRoadName?: string }) {
  //   return await this.streetMasterRepository
  //     .createQueryBuilder('street_master')
  //     .update(StreetMasterEntity)
  //     .set(input)
  //     .where('streetOrRoadId = :streetOrRoadId', {
  //       streetOrRoadId: id,
  //     })
  //     .execute();
  // }

  async deleteStreet(id: string) {
    return await this.streetMasterRepository
      .createQueryBuilder('street_master')
      .softDelete()
      .where('streetOrRoadId = :streetOrRoadId', {
        streetOrRoadId: id,
      })
      .execute();
  }
}
