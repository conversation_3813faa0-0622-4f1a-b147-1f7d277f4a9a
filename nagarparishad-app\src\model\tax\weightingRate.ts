export interface WeightingRateSetting   {
    weighting_rate_id: string;
    financial_year?: string; // Optional for backward compatibility
    value: number;
    status: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    usage_type: {
        usage_type_id: string;
        usage_type: string;
        createdAt: string;
        updatedAt: string;
        deletedAt: string | null;
    },
    reassessmentRange?: {
        reassessment_range_id: string;
        start_range: string;
        end_range: string;
    }
}

export interface WeightingRateApi {
    statusCode: number;
    message: string;
    data: WeightingRateSetting[];
  }

  export interface WeightingRateCreateApi {
    statusCode: number;
    message: string;
    data: WeightingRateSetting;
  }
  export interface WeightingRateUpdateApi {
    financial_year?: string; // Optional for backward compatibility
    reassessment_range_id: string;
    value: number;
    status: string;
    usage_type_id: string;
    weighting_rate_id: string;
  }

  export interface WeightingRateSendApiObj {
    financial_year?: string; // Optional for backward compatibility
    reassessment_range_id: string;
    value: number;
    status: string;
    usage_type_id: string;
    }
