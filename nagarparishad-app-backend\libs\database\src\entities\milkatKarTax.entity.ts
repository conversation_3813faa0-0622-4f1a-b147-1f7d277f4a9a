import { Length } from 'class-validator';
import {
  BaseEntity,
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  JoinColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { PropertyEntity } from './property.entity';
import { MilkatKarEntity } from './milkat-kar.entity';
import { Property_Usage_Details_Entity } from './property-usage-details.entity';

@Entity('milkatKarTax')
export class MilkatKarTaxEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  milkatKartax_id: string;

  @ManyToOne(() => PropertyEntity, (property) => property.property_id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

  @ManyToOne(() => MilkatKarEntity, (MilkatKar) => MilkatKar.milkatKar_id)
  @JoinColumn({ name: 'milkatKar_id' })
  MilkatKar: MilkatKarEntity;



  @ManyToOne(() => Property_Usage_Details_Entity, (property_usage_details) => property_usage_details.property_usage_details_id)
  @JoinColumn({ name: 'property_usage_details_id' })
  property_usage_details: Property_Usage_Details_Entity;

  @Column({
    type: String,
    name: 'financial_year',
    nullable: true,
  })
  financial_year: string;

  @Column({ type: 'float', nullable: false })
  sq_ft_meter: number;

  @Column({ type: 'float', nullable: false, default: 0 })
  length: number;

  @Column({ type: 'float', nullable: false, default: 0 })
  width: number;

  //ready reckner rate
  @Column({ type: 'float', nullable: false })
  rr_rate: number;

  //ready reckner construction rate
  @Column({ type: 'float', nullable: false })
  rr_construction_rate: number;

  
  @Column({ type: 'float', nullable: false })
  depreciation_rate: number;

  //bharank 
  @Column({ type: 'float', nullable: false })
  weighting: number;

  
  @Column({ type: 'float', nullable: false })
  capital_value: number;

  @Column({ type: 'float', nullable: false })
  tax_value: number;

  @Column({ type: 'float', nullable: false })
  tax: number;

    @Column({ type: 'float', nullable: false,default:0 })
  shasti_fee: number;

      @Column({ type: 'float', nullable: false,default:0 })
  property_type_discount: number;


  @Column({ type: 'float', nullable: true })
  padsar_kar_tax: number;

  @Column({ type: 'date', nullable: false })
  bill_generation_date: Date;

  @Column({ type: String, nullable: true })
  tax_data: JSON;

  

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
