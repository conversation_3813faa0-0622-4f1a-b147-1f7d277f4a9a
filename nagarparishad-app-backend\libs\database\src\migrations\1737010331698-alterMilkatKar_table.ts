// import { MigrationInterface, QueryRunner } from "typeorm";

// export class AlterMilkatKarTable1737010331698 implements MigrationInterface {

//     public async up(queryRunner: QueryRunner): Promise<void> {
//         await queryRunner.query(`ALTER TABLE "property" ADD "landmark" character varying`);
//         await queryRunner.query(`ALTER TABLE "warshikKarTax" ADD "landmark" character varying`);
//     }

//     public async down(queryRunner: QueryRunner): Promise<void> {
//         await queryRunner.query(`ALTER TABLE "property" ADD "landmark" character varying`);
//         await queryRunner.query(`ALTER TABLE "property" ADD "landmark" character varying`);
//     }

// }
