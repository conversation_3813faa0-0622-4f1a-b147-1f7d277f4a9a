import { MigrationInterface, QueryRunner } from "typeorm";

export class PropertyColUpdated1725361944271 implements MigrationInterface {
    name = 'PropertyColUpdated1725361944271'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2"`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_df5e676c36082c5c46778bc844c"`);
        await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0"`);
        await queryRunner.query(`ALTER TABLE "tax_property_other_taxes" DROP CONSTRAINT "FK_693fd31b799618c70fccf7e9752"`);
        await queryRunner.query(`ALTER TABLE "tax_property" DROP CONSTRAINT "FK_d7a73eaab514342e3364090ccba"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "duplicate_property_no"`);
        // await queryRunner.query(`ALTER TABLE "property" ADD "import_property_id" character varying`); commeting this
        await queryRunner.query(`ALTER TABLE "import_property" ADD "duplicate_property_no" character varying`);
        // await queryRunner.query(`ALTER TABLE "import_property" ADD "import_done_status" character varying`); commeting this
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "missing_desc"`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "missing_desc" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "is_merge"`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "is_merge" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "status"`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "status" character varying`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" ADD CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_df5e676c36082c5c46778bc844c" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_property_other_taxes" ADD CONSTRAINT "FK_693fd31b799618c70fccf7e9752" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_property" ADD CONSTRAINT "FK_d7a73eaab514342e3364090ccba" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_2b3f8b3d5bea0587920b3020eac" FOREIGN KEY ("street_id") REFERENCES "street_master"("street_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_63859fe9b799dc463dd2e14ad72" FOREIGN KEY ("ward_id") REFERENCES "ward_master"("ward_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_ed2960763a10694e2d01f2f041c" FOREIGN KEY ("zone_id") REFERENCES "zone_master"("zone_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_ed2960763a10694e2d01f2f041c"`);
        // await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_63859fe9b799dc463dd2e14ad72"`);
        // await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_2b3f8b3d5bea0587920b3020eac"`);
        await queryRunner.query(`ALTER TABLE "tax_property" DROP CONSTRAINT "FK_d7a73eaab514342e3364090ccba"`);
        await queryRunner.query(`ALTER TABLE "tax_property_other_taxes" DROP CONSTRAINT "FK_693fd31b799618c70fccf7e9752"`);
        await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0"`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_df5e676c36082c5c46778bc844c"`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "status"`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "status" character varying(50)`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "is_merge"`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "is_merge" character varying(50)`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "missing_desc"`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "missing_desc" character varying(250)`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "import_done_status"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "duplicate_property_no"`);
        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "import_property_id"`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "duplicate_property_no" character varying`);
        await queryRunner.query(`ALTER TABLE "tax_property" ADD CONSTRAINT "FK_d7a73eaab514342e3364090ccba" FOREIGN KEY ("property_id") REFERENCES "property_test"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_property_other_taxes" ADD CONSTRAINT "FK_693fd31b799618c70fccf7e9752" FOREIGN KEY ("property_id") REFERENCES "property_test"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0" FOREIGN KEY ("property_id") REFERENCES "property_test"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_df5e676c36082c5c46778bc844c" FOREIGN KEY ("property_id") REFERENCES "property_test"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property_owner_details" ADD CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2" FOREIGN KEY ("property_id") REFERENCES "property_test"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
