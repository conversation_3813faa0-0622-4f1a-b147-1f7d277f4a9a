import React, { useState, useContext, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import AsyncSelect from "@/components/ui/react-select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { z, object, string } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { PropertyContext } from "@/context/PropertyContext";
import { PropertyRegistrationInterface } from "@/model/propertyregistration-master";
import { useLocationMasterController } from "@/controller/master/LocationMasterController";
import { LocationSelectOject } from "@/model/location-master";
import { ReactselectInterface } from "@/model/global-master";
import { useStreetMasterController } from "@/controller/master/StreetMasterController";
import { StreetSelectOject } from "@/model/street-master";
import { WardObjectInterface, ZoneObject } from "@/model/zone-master";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { useZoneMasterController } from "@/controller/master/ZoneMasterController";
import { AdministrativeBoundaryObjectInterface } from "@/model/administrativeboundary-master";
import { useAdministrativeBoundaryController } from "@/controller/master/AdministrativeBoundaryController";
import { useElectionBoundaryController } from "@/controller/master/ElectionBoundryController";
import { ElectionBoundaryMasterObject } from "@/model/electionBoundry";
import { PropertyDetailsInterface } from "@/model/global-master";
import { useAreaMasterController } from "@/controller/master/AreaMasterController";
import { AreaMasterObject } from "@/model/area-master";
import { ChevronsRight, CircleX, Eye, UploadCloudIcon, X } from "lucide-react";

import "react-transliterate/dist/index.css"; // Import the styles

import axios from "axios";
import DocumentUpload from "@/components/globalcomponent/DocumentUpload";
// import { X } from "lucide-react"; // Import the cross icon
import pdfIcon from "../../../../assets/img/files/pdf.png"; // Adjust the import path as needed
import wordIcon from "../../../../assets/img/files/pdf.png";
import { GlobalContext } from "@/context/GlobalContext";
import { useOwnerTypeController } from "@/controller/master/OwnerTypeController";
import { useRegisterNumberController } from "@/controller/master/RegisterNumberController";

const PropertyLocationDetails: React.FC<PropertyDetailsInterface> = ({
  currentStep,
  handleNextStep,
  handlePreviousStep,
}) => {
  const { t } = useTranslation();
  const [selectedWard, setSelectedWard] = useState<any>({});
  const [selectedName, setSelectedName] = useState("");

  const requiredFieldMessage = t("errorsRequiredField");
  const schema = z.object({
    building_permission_number: z.string(),
    city_survey_number: z.string(),
    property_status: z.string(),
    plot_number: z.string(),
    block_number: z.string(),
    house_number: z.string(),
    location: z.string(),
    house_or_apartment_name: z.string().optional(),
    street: z.object({
      street_id: z.string().nonempty(t("errorsRequiredField")),
      street_name: z.string(),
    }),
    landmark: z.string().optional(),
    country: z.string(),
    city: z.string(),
    latitude: z
      .union([
        z
          .string()
          .refine(
            (val) =>
              val === "" || (parseFloat(val) >= -90 && parseFloat(val) <= 90),
            {
              message: "अक्षांश -90 ते 90 दरम्यान असावा.",
            }
          ),
        z
          .number()
          .nullable()
          .refine((val) => val === null || (val >= -90 && val <= 90), {
            message: "अक्षांश -90 ते 90 दरम्यान असावा.",
          }),
      ])
      .nullable(),

    longitude: z
      .union([
        z
          .string()
          .refine(
            (val) =>
              val === "" || (parseFloat(val) >= -180 && parseFloat(val) <= 180),
            {
              message: "रेखांश -180 ते 180 दरम्यान असावा.",
            }
          ),
        z
          .number()
          .nullable()
          .refine((val) => val === null || (val >= -180 && val <= 180), {
            message: "रेखांश -180 ते 180 दरम्यान असावा.",
          }),
      ])
      .nullable(),
    ward: z.object({
      ward_id: z.string().nonempty(t("errorsRequiredField")),
      ward_name: z.string(),
    }),
    register: z.object({
      register_id: z.string(),
      register_name: z.string(),
    }).nullable(),
    zone: z.object({
      zone_id: z.string().nonempty(t("errorsRequiredField")),
      zoneName: z.string(), // zoneName as string
    }),
    adminstrativeBoundary: z.string(),
    electionBoundary: z.string(),
    uploaded_files: z.array(z.string()),
    sr_no: z.number().nullable(),

    property_desc: z.string().optional(),
    area: z.string(),
    // toilet_flag: z.boolean(),
    property_number: z.string(),
    property_old_number: z.string(),
    gat_number: z.string(),

            property_remark: z.string().optional(),
    
  });

  const { propertyInformation, setPropertyInformation } =
    useContext(PropertyContext);
  const { updateProperty, setUpdateProperty } = useContext(GlobalContext);

  const locationData = propertyInformation?.PropertyLocationDetails;

  console.log("location data", locationData);
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      ward: locationData?.ward || { ward_id: "", ward_name: "" },
      register: locationData?.register || { register_id: "", register_name: "" },
      building_permission_number:
        locationData?.building_permission_number || "",
      city_survey_number: locationData?.city_survey_number || "",
      property_status: locationData?.property_status
        ? locationData.property_status.toString()
        : "",
      plot_number: locationData?.plot_number || "",
      block_number: locationData?.block_number || "",
      house_number: locationData?.house_number || "",
      location: locationData?.location || "",
      house_or_apartment_name: locationData?.house_or_apartment_name || "",
      street: locationData?.street || { street_id: "", street_name: "" },
      landmark: locationData?.landmark || "",
      country: locationData?.country || "India",
      city: locationData?.city || "Shirol",
      latitude: locationData?.latitude || null,
      longitude: locationData?.longitude || null,
      zone: locationData?.zone || { zone_id: "", zoneName: "" },
      adminstrativeBoundary: locationData?.adminstrativeBoundary || "",
      electionBoundary: locationData?.electionBoundary || "",
      uploaded_files: locationData?.uploaded_files || [],
      sr_no: locationData?.sr_no || null,
      property_desc: locationData?.property_desc || "",
      area: locationData?.area || "",
      // toilet_flag: locationData?.toilet_flag || false,
      property_number: locationData?.property_number || "", // Added property_number
      property_old_number: locationData?.property_old_number || "", // Added property_old_number`
      gat_number: locationData?.gat_number || "", // Added gat_number
      property_remark: locationData?.property_remark || "", // Added remark
    },
  });
  console.log(
    "locationData?.sr_no ",
    locationData?.sr_no,
    typeof locationData?.sr_no
  );

  useEffect(() => {
    if (locationData && locationData !== undefined) {
      setSelectedWard({ value: locationData?.ward });
    }
  }, [locationData]);

  const {
    formState: { errors },
    watch,
  } = form;

  const onSubmit = (data: any) => {
    console.log("first step data", data);
    if (data.register && data.register.register_id === "") {
      data.register = null;
    }
    setPropertyInformation((prevState: PropertyRegistrationInterface) => ({
      ...prevState,
      PropertyLocationDetails: {
        ...data,
        property_status: data?.property_status
          ? parseInt(data.property_status, 10)
          : 1,
  uploaded_files:uploadedFiles || []

      },
    }));
    handleNextStep();

    console.log("first step data after slide", data);
  };

  const onError = (errors) => {
    console.log("Validation errors:", errors); // To log any validation errors
  };

  const locationList: any = useLocationMasterController();
  const locationOptions: ReactselectInterface[] =
    locationList.locationList?.map((location: LocationSelectOject) => ({
      value: location.location_id,
      label: location.locationName,
    }));

  const streetorRoadList: any = useStreetMasterController();
  const streetOptions: ReactselectInterface[] =
    streetorRoadList?.streetList?.map((street: StreetSelectOject) => ({
      value: street?.street_id,
      label: street?.street_name,
    }));
  const wardList: any = useWardMasterController();
  const wardOptions: ReactselectInterface[] = wardList?.wardList?.map(
    (ward: WardObjectInterface) => ({
      value: ward?.ward_id,
      label: ward?.ward_name,
    })
  );

    const RegisterList: any = useRegisterNumberController();
    console.log("useRegisterNumberController",RegisterList)
  const RegisterListOptions: ReactselectInterface[] =
    RegisterList?.registerNumberList?.data?.map((register: any) => ({
      value: register?.register_id,
      label: register?.register_name,
    }));

  const zoneList: any = useZoneMasterController();
  console.log("zonelist", zoneList);

  const zoneOptions: ReactselectInterface[] = zoneList?.zoneList?.map(
    (zone: ZoneObject) => ({
      value: zone.zone_id,
      label: zone.zoneName,
    })
  );

  const administrativeBoundaryList: any = useAdministrativeBoundaryController();
  const adminstrativeBoundaryOptions: ReactselectInterface[] =
    administrativeBoundaryList?.administrativeBoundaryList?.map(
      (boundaryOptions: AdministrativeBoundaryObjectInterface) => ({
        value: boundaryOptions.adminstrativeBoundary_id,
        label: boundaryOptions.adminstrativeBoundary_name,
      })
    );

  const electionBoundaryList: any = useElectionBoundaryController();
  const electionOptions: ReactselectInterface[] =
    electionBoundaryList?.boundaryList?.map(
      (electionboundaryOptions: ElectionBoundaryMasterObject) => ({
        value: electionboundaryOptions?.electionBoundary_id,
        label: electionboundaryOptions?.electionBoundaryName,
      })
    );

  const areaList: any = useAreaMasterController();
  const areaOptions: ReactselectInterface[] = areaList?.areaList?.map(
    (areaOptions: AreaMasterObject) => ({
      value: areaOptions?.area_id,
      label: areaOptions?.areaName,
    })
  );

  // useEffect(() => {

  //   console.log("PropertyInformation state updated:", propertyInformation);

  // }, [propertyInformation]);

  // React.useEffect(() => {
  //   const timer = setTimeout(() => setProgress(66), 500);
  //   return () => clearTimeout(timer);
  // }, []);

  const zoneNamesMapping = {
    "SHR-A": [
      "शिंदे माने गल्ली",
      "जुना मेनरोड",
      "ब्रम्हपुरी",
      "चुडमुंगे गल्ली",
      "गंगाधर गल्ली",
      "काळेगल्ली १ व २",
      "चोचले गल्ली",
      "कोरवी गल्ली",
      "सुतार गल्ली",
      "कदम गल्ली",
      "जगदाळे गल्ली",
    ],
    "SHR-B": ["हरिजन वाडा"],
    "SHR-C": ["गोडीविहीर कोळी गल्ली"],
    "SHR-D": [
      "धनगर गल्ली",
      "कुरणे गल्ली",
      "कोळी गल्ली माने चुडमुंगे गल्ली",
      "जाधव गल्ली",
      "गावडे गल्ली",
      "माळी गल्ली",
      "पाटील गल्ली",
      "कुंभार गल्ली मधील सर्व मिळकती",
    ],
    "SHR-E": [
      "गावठाण पासून जवळच्या रहिवास क्षमता असलेल्या विकसनक्षम अंतर्गत जमिनी",
    ],
    "SHR-F": [
      "गावठाण पासून जवळच्या रहिवास क्षमता असलेल्या विकसित अंतर्गत जमिनी",
    ],
    "SHR-G": ["गावठाण पासून दूरच्या रहिवास क्षमता असलेल्या विकसनक्षम जमिनी"],
    "SHR-H": ["शिवाजीनगर"],
    "SHR-I": ["चर्मकार गल्ली", "वडार गल्ली", "संगमनगर", "बेघर वसाहत"],
    "SHR-J": ["बाजारपेठ"],
    "SHR-K": [
      "गावठाण पासून दूरच्या रहिवास क्षमता असलेल्या विकसनक्षम जमिनी (महामार्ग सन्मुख)",
    ],
    "SHR-L": [
      "गावठाण पासून जवळच्या रहिवास क्षमता असलेल्या विकसनक्षम जमिनी (महामार्ग सन्मुख)",
    ],
    "SHR-M": [
      "शिवाजी चौक ते चित्रनागरी पर्यंत रस्त्यावरील स.न. मधील मुख्य रस्तावरील भूखंड",
    ],
    "SHR-N": ["दत्तनगर"],
  };
  const selectedZone = watch("zone.zoneName");
  const selectedZoneNames = zoneNamesMapping[selectedZone] || [];
  console.log("selectedZoneNames", selectedZoneNames, selectedZone);
  const [uploadedFiles, setUploadedFiles] = useState([]);

  const handleUploadComplete = (fileDetails: React.SetStateAction<any[]>) => {
    // setUploadedFiles((prevFiles) => [...prevFiles, ...fileDetails]);
    //     form.setValue('uploaded_files', [...form.getValues('uploaded_files'), ...fileDetails]);
    

  };

  const handleRemoveFile = (file: any) => {
   const updatedFiles = uploadedFiles.filter((i) => i !== file);
    setUploadedFiles(updatedFiles);
    form.setValue('uploaded_files', updatedFiles);  };

  const getIconForFileType = (fileType: string) => {
    if (fileType === "application/pdf") {
      return pdfIcon;
    } else if (
      fileType === "application/msword" ||
      fileType ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      return wordIcon;
    }
    return null;
  };

  return (
    <>
      <div className=" h-fit">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit, onError)}>
            <h2 className="text-xl font-semibold font-Noto">
              {t("propertyLocationDetailsForm.propertyLocationTitle")}
            </h2>
            <hr className="my-2" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 gap-x-5  ">
              {locationData?.property_number && (
                <div className="grid-cols-subgrid ">
                  <FormField
                    control={form.control}
                    name="property_number"
                    render={({ field }) => (
                      <FormItem className=" ">
                        <FormLabel>
                          {t("propertyLocationDetailsForm.propertyNumber")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            className="mt-1 block w-full"
                            placeholder={t(
                              "propertyLocationDetailsForm.propertyNumber"
                            )}
                            transliterate={false}
                            {...field}
                            disabled={false}
                          />
                        </FormControl>
                        {errors.property_number && (
                          <FormMessage className="ml-1">
                            Property Number
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
              )}
                  <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="register.register_id"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("propertyLocationDetailsForm.register")}
                      </FormLabel>
                      <FormControl>
                        <Controller
                          control={form.control}
                          name="register.register_id"
                          render={({ field: controllerField }) => (
                            <AsyncSelect
                              placeholder={t(
                                "propertyLocationDetailsForm.register"
                              )}
                              options={RegisterListOptions}
                              value={RegisterListOptions?.find(
                                (option: any) =>
                                  option.value === controllerField.value
                              )}
                              onChange={(selectedOption: any) => {
                                controllerField.onChange(selectedOption.value);
                                form.setValue(
                                  "register.register_name",
                                  selectedOption.label
                                );
                              }}
                              colourOptions={RegisterListOptions}
                            />
                          )}
                        />
                      </FormControl>
                      {errors.register?.register_id?.message && (
                        <FormMessage className="ml-1">
                          {errors.register.register_id.message}
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid ">
                <FormField
                  control={form.control}
                  name="property_old_number"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("propertyLocationDetailsForm.propertyOldNumber")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          placeholder={t(
                            "propertyLocationDetailsForm.propertyOldNumber"
                          )}
                          {...field}
                        />
                      </FormControl>
                      {errors.property_old_number && (
                        <FormMessage className="ml-1">
                          Property Old Number
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid ">
                <FormField
                  control={form.control}
                  name="city_survey_number"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {" "}
                        {t("propertyLocationDetailsForm.citySurveyNumber")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          placeholder={t(
                            "propertyLocationDetailsForm.citySurveyNumber"
                          )}
                          {...field}
                        />
                      </FormControl>
                      {errors.city_survey_number && (
                        <FormMessage className="ml-1">
                          City Survey Number
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid ">
                <FormField
                  control={form.control}
                  name="gat_number"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("propertyLocationDetailsForm.gatNumber")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          placeholder={t(
                            "propertyLocationDetailsForm.gatNumber"
                          )}
                          {...field}
                        />
                      </FormControl>
                      {errors.gat_number && (
                        <FormMessage className="ml-1">Gat Number</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="plot_number"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {" "}
                        {t("propertyLocationDetailsForm.plotNumber")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          placeholder={t(
                            "propertyLocationDetailsForm.plotNumber"
                          )}
                          {...field}
                        />
                      </FormControl>
                      {errors.plot_number && (
                        <FormMessage className="ml-1">
                          Enter Plot Number
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="block_number"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {" "}
                        {t("propertyLocationDetailsForm.blockNumber")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          placeholder={t(
                            "propertyLocationDetailsForm.blockNumber"
                          )}
                          {...field}
                        />
                      </FormControl>
                      {errors.block_number && (
                        <FormMessage className="ml-1">
                          Enter Block Number
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="house_number"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {" "}
                        {t("propertyLocationDetailsForm.houseNumber")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          placeholder={t(
                            "propertyLocationDetailsForm.houseNumber"
                          )}
                          {...field}
                        />
                      </FormControl>
                      {errors.house_number && (
                        <FormMessage className="ml-1">
                          Enter House Number
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="house_or_apartment_name"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {" "}
                        {t("propertyLocationDetailsForm.houseApartmentName")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="text"
                          placeholder={t(
                            "propertyLocationDetailsForm.houseApartmentName"
                          )}
                          {...field}
                        />
                      </FormControl>
                      {errors.house_or_apartment_name && (
                        <FormMessage className="ml-1">
                          Enter House or Apartment Name
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="street.street_id" // Use the correct name for the nested field
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("propertyLocationDetailsForm.streetRoad")}
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Controller
                          control={form.control}
                          name="street.street_id" // Explicitly name the nested field
                          render={({ field: controllerField }) => (
                            <AsyncSelect
                              placeholder={t(
                                "propertyLocationDetailsForm.streetRoad"
                              )}
                              options={streetOptions}
                              value={streetOptions?.find(
                                (option: any) =>
                                  option.value === controllerField.value
                              )}
                              onChange={(selectedOption: any) => {
                                controllerField.onChange(selectedOption.value);
                                form.setValue(
                                  "street.street_name",
                                  selectedOption.label
                                );
                              }}
                              colourOptions={streetOptions}
                            />
                          )}
                        />
                      </FormControl>
                      {/* Access and display the error for street_id */}
                      {errors.street?.street_id?.message && (
                        <FormMessage className="ml-1">
                          {errors.street.street_id.message}
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="landmark"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("propertyLocationDetailsForm.landmark")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="text"
                          placeholder={t(
                            "propertyLocationDetailsForm.landmark"
                          )}
                          {...field}
                        />
                      </FormControl>
                      {errors.landmark && (
                        <FormMessage className="ml-1">
                          Enter Landmark
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="ward.ward_id" // Use the correct nested field name
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("propertyLocationDetailsForm.ward")}
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Controller
                          control={form.control}
                          name="ward.ward_id" // Explicitly name the nested field
                          render={({ field: controllerField }) => (
                            <AsyncSelect
                              placeholder={t(
                                "propertyLocationDetailsForm.ward"
                              )}
                              options={wardOptions}
                              value={wardOptions?.find(
                                (option: any) =>
                                  option.value === controllerField.value
                              )}
                              onChange={(selectedOption: any) => {
                                setSelectedWard(selectedOption);
                                controllerField.onChange(selectedOption.value); // Only update ward_id here
                                form.setValue(
                                  "ward.ward_name",
                                  selectedOption.label
                                ); // Also update zoneName
                              }}
                              colourOptions={wardOptions}
                            />
                          )}
                        />
                      </FormControl>
                      {/* Access and display the error for ward_id */}
                      {errors.ward?.ward_id?.message && (
                        <FormMessage className="ml-1">
                          {errors.ward.ward_id.message}
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              {/* <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="register.register_id"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("propertyLocationDetailsForm.register")}
                      </FormLabel>
                      <FormControl>
                        <Controller
                          control={form.control}
                          name="register.register_id"
                          render={({ field: controllerField }) => (
                            <AsyncSelect
                              placeholder={t(
                                "propertyLocationDetailsForm.register"
                              )}
                              options={RegisterListOptions}
                              value={RegisterListOptions?.find(
                                (option: any) =>
                                  option.value === controllerField.value
                              )}
                              onChange={(selectedOption: any) => {
                                controllerField.onChange(selectedOption.value);
                                form.setValue(
                                  "register.register_name",
                                  selectedOption.label
                                );
                              }}
                              colourOptions={RegisterListOptions}
                            />
                          )}
                        />
                      </FormControl>
                      {errors.register?.register_id?.message && (
                        <FormMessage className="ml-1">
                          {errors.register.register_id.message}
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div> */}
              <div className="grid-cols-subgrid">
                <div className="grid-cols-subgrid">
                  <FormField
                    control={form.control}
                    name="zone.zone_id" // This targets the nested zone object
                    render={({ field }) => (
                      <FormItem className=" ">
                        <FormLabel>
                          {t("propertyLocationDetailsForm.zone")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Controller
                            control={form.control}
                            name="zone.zone_id" // Explicitly target zone_id field
                            render={({ field: controllerField }) => (
                              <AsyncSelect
                                placeholder={t(
                                  "propertyLocationDetailsForm.zone"
                                )}
                                options={zoneOptions}
                                value={zoneOptions?.find(
                                  (option: any) =>
                                    option.value === controllerField.value
                                )}
                                onChange={(selectedOption: any) => {
                                  // Update both zone_id and zoneName fields in the form state
                                  controllerField.onChange(
                                    selectedOption.value
                                  ); // Only update ward_id here

                                  form.setValue(
                                    "zone.zoneName",
                                    selectedOption.label
                                  ); // Also update zoneName
                                }}
                                colourOptions={zoneOptions}
                              />
                            )}
                          />
                        </FormControl>
                        {/* Access and display the error for zone_id */}
                        {errors.zone?.zone_id?.message && (
                          <FormMessage className="ml-1">
                            {errors.zone.zone_id.message}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="grid-cols-subgrid">
                <FormItem className="">
                  <FormLabel>{"गल्ली"}</FormLabel>
                  <FormControl>
                    <Textarea
                      value={selectedZoneNames.join(", ")}
                      readOnly
                      className="w-full p-2 border rounded"
                    />
                  </FormControl>
                </FormItem>
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="latitude"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("propertyLocationDetailsForm.latitude")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          transliterate={false}
                          placeholder={t(
                            "propertyLocationDetailsForm.latitude"
                          )}
                          {...field}
                        />
                      </FormControl>
                      {errors.latitude && (
                        <FormMessage className="ml-1">
                          Enter Latitude
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="longitude"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("propertyLocationDetailsForm.longitude")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          placeholder={t(
                            "propertyLocationDetailsForm.longitude"
                          )}
                          transliterate={false}
                          {...field}
                        />
                      </FormControl>
                      {errors.longitude && (
                        <FormMessage className="ml-1">
                          Enter Longitude
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <h3 className="text-xl font-normal font-Noto mt-8">
              {t("propertyLocationDetailsForm.fileUploadTitle")}
            </h3>
            <hr className="my-3" />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 gap-x-5">
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="property_desc"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("propertyLocationDetailsForm.propertyDescription")}
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          className="mt-1 block w-full"
                          placeholder={t(
                            "propertyLocationDetailsForm.propertyDescription"
                          )}
                          {...field}
                          onChange={(e) => field.onChange(e.target.value)}
                        />
                      </FormControl>
                      {errors.property_desc && (
                        <FormMessage className="ml-1">
                          Enter Property Description
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="sr_no"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("propertyLocationDetailsForm.sequenceNumber")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          placeholder={t(
                            "propertyLocationDetailsForm.sequenceNumber"
                          )}
                          {...field}
                          transliterate={false}
                          disabled={false}
                          onChange={(e) => {
                            const valueAsNumber = e.target.value
                              ? Number(e.target.value)
                              : null;
                            field.onChange(valueAsNumber); // Update the form state with the number
                          }}
                        />
                      </FormControl>
                      {errors.sr_no && (
                        <FormMessage className="ml-1">
                          Enter Sequence Number
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

               <div className="grid-cols-subgrid">

                    <FormField
                  control={form.control}
                  name="property_remark"
                  render={({ field }) => (
                   <FormItem>
                      <FormLabel>शेरा</FormLabel>
                      <FormControl>
                        <Textarea
                          className="mt-1 block w-full"
                          placeholder={"शेरा"}
                          {...field}
                        />
                      </FormControl>
                 
                    </FormItem>
                  )}
                />
                 
              </div>
             
            </div>

            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-2 gap-x-5">
              <DocumentUpload
                btnText={"फाइल अपलोड करा"}
                onUploadComplete={handleUploadComplete}
              />
              {/* <div className="w-full mt-2 px-5">
                {uploadedFiles.length > 0 ? (
                  <div>
                    <h3 className="text-sm mb-2">{t("uploadedFiles")}</h3>
                    <div className="list-disc flex gap-3 max-w-full overflow-x-auto">
                      {uploadedFiles.map((file, index) => (
                        <div
                          key={index}
                          className="mb-2 min-w-[145px] w-[160px] h-full border-[1px] border-solid border-gray-200 p-2 rounded-xl shadow-sm shadow-slate-200 relative overflow-hidden"
                        >
                          {file.type.startsWith("image/") ? (
                            <img
                              src={file.url}
                              alt={file.name}
                              className="w-full h-20 object-contain mb-2"
                            />
                          ) : (
                            <img
                              src={getIconForFileType(file.type)}
                              alt={file.name}
                              className="w-full h-20 object-contain mb-2"
                            />
                          )}
                          <p
                            className="break-words text-xs one-line-ellipsis"
                            title={file.name}
                          >
                            {file.name}
                          </p>
                          <div className="bg-slate-200/60 w-full h-full absolute top-0 left-0 flex items-center justify-center gap-3 opacity-0 hover:opacity-100 transition-opacity duration-500">
                            <a
                              className="text-gray-600 bg-gray-300 p-2 rounded-full h-fit"
                              href={file.url}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <Eye />
                            </a>
                            <button
                              type="button"
                              className="text-red-400 bg-red-200 p-2 rounded-full h-fit"
                              onClick={() => handleRemoveFile(file)}
                            >
                              <CircleX />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <p className="text-center">No files uploaded.</p>
                )}
              </div> */}
            </div>

            <div className="w-full text-end">
              {currentStep > 1 ? (
                <Button
                  variant="outline"
                  className="mr-4 border-BlueText text-BlueText [max-width:500px]:mt-10 "
                  onClick={handlePreviousStep}
                >
                  {t("property.previousBtn")}{" "}
                </Button>
              ) : (
                <Button className=" mt-10" variant="submit" type={"submit"}>
                  {<ChevronsRight className="w-5 h-5 font-bold " />}
                </Button>
              )}
              {}
            </div>
          </form>
        </Form>
      </div>
    </>
  );
};

export default PropertyLocationDetails;
