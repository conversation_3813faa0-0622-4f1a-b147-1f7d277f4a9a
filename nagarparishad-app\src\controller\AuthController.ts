import Api from "@/services/AuthServices";
import {
  ApiResponse,
  UserLoginData,
  OtpValidationData,
  OtpAuthResponseData,
  AuthUserRegister,
} from "@/model/auth/authServices";

const AuthController = {
  // signUp: async (userData: UserSignUpData, callback: (response: ApiResponse) => void) => {
  //     Api.signUp(userData, callback);
  // },

  signIn: async (
    credentials: UserLoginData,
    callback: (response: ApiResponse) => void,
  ) => {
    Api.signIn(credentials, callback);
  },

  validateOtp: async (
    otpData: OtpValidationData,
    callback: (response: OtpAuthResponseData) => void,
  ) => {
    Api.validateOtp(otpData, callback);
  },

  signOut: async (callback: (response: ApiResponse) => void) => {
    Api.signOut(callback);
  },

  userRegister: async (
    userData: AuthUserRegister,
    callback: (response: ApiResponse) => void,
  ) => {
    Api.userRegister(userData, callback);
  },

  sendPasswordResetOTP: async (
    email: string,
    callback: (response: ApiResponse) => void,
  ) => {
    Api.sendPasswordResetOTP(email, callback);
  },

  verifyPasswordResetOTP: async (
    email: string,
    otp: string,
    callback: (response: ApiResponse) => void,
  ) => {
    Api.verifyPasswordResetOTP(email, otp, callback);
  },

  resetPassword: async (
    email: string,
    otp: string,
    password: string,
    callback: (response: ApiResponse) => void,
  ) => {
    Api.resetPassword(email, otp, password, callback);
  },

  // refreshToken: async (callback: (response: ApiResponse) => void) => {
  //     Api.refreshToken(callback);
  // },
};

export default AuthController;
