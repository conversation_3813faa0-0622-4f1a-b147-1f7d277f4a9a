import { Repository } from 'typeorm';
import { Financial_year } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class Financial_yearRepository extends Repository<Financial_year> {
  constructor(
    @InjectRepository(Financial_year)
    private readonly financial_yearRepository: Repository<Financial_year>,
  ) {
    super(
      financial_yearRepository.target,
      financial_yearRepository.manager,
      financial_yearRepository.queryRunner,
    );
  }

  async getAll() {
    return await this.financial_yearRepository
      .createQueryBuilder('financial_year')
      .select([
        'financial_year.financial_year_range',
        'financial_year.from_date',
        'financial_year.to_date',
        'financial_year.is_active',
        'financial_year.is_current',




      ])
      .orderBy('financial_year.updated_at', 'DESC')
      .getMany();
  }

  async getCurrentFinancialYear(): Promise<Financial_year | null> {
    return await this.financial_yearRepository.findOne({
      where: { is_current: true },
    });
  }

  async getFinancialYearById(financialYearId: string): Promise<Financial_year | null> {
    return await this.financial_yearRepository.findOne({
      where: { financial_year_id: financialYearId },
    });
  }
}
