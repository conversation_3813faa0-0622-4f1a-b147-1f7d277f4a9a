import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeinDiscountName1745997943772 implements MigrationInterface {
    name = 'ChangeinDiscountName1745997943772'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "milkatKar" RENAME COLUMN "discount" TO "property_type_discount"`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" RENAME COLUMN "discount" TO "property_type_discount"`);
        await queryRunner.query(`ALTER TABLE "paid_data" RENAME COLUMN "discount" TO "other_discount"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "discount"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "property_type_discount" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "other_discount" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "milkatKar" DROP COLUMN "property_type_discount"`);
        await queryRunner.query(`ALTER TABLE "milkatKar" ADD "property_type_discount" double precision NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" DROP COLUMN "property_type_discount"`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ADD "property_type_discount" double precision DEFAULT '0'`);
   await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "other_discount"`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "other_discount" double precision DEFAULT '0'`);
}

        public async down(queryRunner: QueryRunner): Promise<void> {
     await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "other_discount"`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "other_discount" character varying`);
   await queryRunner.query(`ALTER TABLE "warshik_kar" DROP COLUMN "property_type_discount"`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ADD "property_type_discount" character varying`);
        await queryRunner.query(`ALTER TABLE "milkatKar" DROP COLUMN "property_type_discount"`);
        await queryRunner.query(`ALTER TABLE "milkatKar" ADD "property_type_discount" character varying`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "other_discount"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "property_type_discount"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "discount" character varying`);
        await queryRunner.query(`ALTER TABLE "paid_data" RENAME COLUMN "other_discount" TO "discount"`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" RENAME COLUMN "property_type_discount" TO "discount"`);
        await queryRunner.query(`ALTER TABLE "milkatKar" RENAME COLUMN "property_type_discount" TO "discount"`);
    }

}
