import React, { useEffect, useState } from "react";
import WhiteContainer from "../custom/WhiteContainer";
import { Input } from "../ui/input";
import { t } from "i18next";
import { But<PERSON> } from "../ui/button";
import TanStackTable from "../globalcomponent/tanstacktable";
import { Printer } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import AsyncSelect from "@/components/ui/react-select";
import { toast } from "../ui/use-toast";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { Loader } from "@/components/globalcomponent/Loader";
import ReportApi from "@/services/ReportServices";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Api from "@/services/ApiServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

interface AssessmentData {
  property_id: string;
  propertyNumber: string;
  ownerName: string;
  address: string;
  year: string;
  total_tax: number;
  status: string;
}

const AssessmentReport: React.FC = () => {
  const [filteredData, setFilteredData] = useState<AssessmentData[]>([]);
  const [loading, setLoading] = useState(false);
  const [open, setopen] = useState(false);
  const [selectedFinancialYear, setSelectedFinancialYear] = useState("");
  const [financialYears, setFinancialYears] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [searchOn, setSearchOn] = useState("propertyNumber");
  const [pagination, setPagination] = useState({
    totalPages: 0,
    totalRecords: 0,
  });
  const [loadingRows, setLoadingRows] = useState<string[]>([]);

  const { canPerformAction } = usePermissions();

  // Check permissions
  const CanRead = canPerformAction(
    ModuleName.Register,
    FormName.AssessmentReport,
    Action.CanRead
  );

  const CanWrite = canPerformAction(
    ModuleName.Register,
    FormName.AssessmentReport,
    Action.CanWrite
  );

  useEffect(() => {
    fetchFinancialYears();
  }, []);

  const fetchFinancialYears = async () => {
    try {
      const response = await Api.getFinancialYears();
      if (response.status && response.data.data) {
        setFinancialYears(response.data.data);
        if (response.data.data.length > 0) {
          setSelectedFinancialYear(response.data.data[0].financial_year_range);
        }
      }
    } catch (error) {
      console.error("Error fetching financial years:", error);
    }
  };

  const columns: ColumnDef<AssessmentData>[] = [
    {
      accessorKey: "propertyNumber",
      header: "Property Number",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("propertyNumber")}</div>
      ),
    },
    {
      accessorKey: "ownerName",
      header: "Owner Name",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("ownerName")}</div>
      ),
    },
    {
      accessorKey: "address",
      header: "Address",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("address")}</div>
      ),
    },
    {
      accessorKey: "year",
      header: "Assessment Year",
      cell: ({ row }) => (
        <div className="text-center">{row.getValue("year")}</div>
      ),
    },
    {
      accessorKey: "total_tax",
      header: "Total Tax",
      cell: ({ row }) => (
        <div className="text-right">₹{row.getValue("total_tax")}</div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <div className="text-center">
          <span
            className={`px-2 py-1 rounded-full text-xs ${
              row.getValue("status") === "active"
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            }`}
          >
            {row.getValue("status")}
          </span>
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const isLoading = loadingRows.includes(row.original.property_id);
        return (
          <div className="flex space-x-2">
            {CanWrite && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePrintAssessment(row.original)}
                disabled={isLoading}
                className="flex items-center space-x-1"
              >
                {isLoading ? (
                  <Loader />
                ) : (
                  <>
                    <Printer className="h-4 w-4" />
                    <span>Print</span>
                  </>
                )}
              </Button>
            )}
          </div>
        );
      },
    },
  ];

  const handleSearch = () => {
    if (!searchValue.trim()) {
      toast({
        title: "Please enter a search value",
        variant: "destructive",
      });
      return;
    }

    if (!selectedFinancialYear) {
      toast({
        title: "Please select a financial year",
        variant: "destructive",
      });
      return;
    }

    fetchAssessmentData(1, 10);
  };

  const fetchAssessmentData = async (page: number, limit: number) => {
    setLoading(true);
    try {
      const params = {
        searchOn,
        value: searchValue,
        fy: selectedFinancialYear,
        page,
        limit,
      };

      const response = await ReportApi.getAssessmentReport(params);

      if (response.status && response.data.data) {
        setFilteredData(response.data.data);
        setPagination({
          totalPages: response.data.pagination?.totalPages || 1,
          totalRecords: response.data.pagination?.total || 0,
        });
        setopen(true);
      } else {
        setFilteredData([]);
        setopen(false);
        toast({
          title: "No assessment data found",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching assessment data:", error);
      toast({
        title: "Failed to fetch assessment data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePrintAssessment = async (item: AssessmentData) => {
    const rowId = item.property_id;
    setLoadingRows((prev) => [...prev, rowId]);

    try {
      const params = {
        searchOn: "propertyNumber",
        value: item.propertyNumber,
        fy: selectedFinancialYear,
      };

      const response = await ReportApi.printAssessmentReport(params);

      if (response.status) {
        toast({
          title: "Assessment report printed successfully.",
          variant: "success",
        });
      } else {
        toast({
          title: "Failed to print assessment report. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error printing assessment report:", error);
      toast({
        title: "Failed to print assessment report. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoadingRows((prev) => prev.filter((id) => id !== rowId));
    }
  };

  if (!CanRead) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">You don't have permission to view this page.</p>
      </div>
    );
  }

  return (
    <div className="flex h-fit">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold font-Poppins w-full ml-3">
          Assessment Report
        </h1>
        <WhiteContainer>
          <div className="grid md:grid-cols-5 gap-x-3 gap-2 items-end">
            <div className="grid-cols-subgrid">
              <Label>
                Financial Year
                <span className="ml-1 text-red-500">*</span>
              </Label>
              <Select
                onValueChange={setSelectedFinancialYear}
                value={selectedFinancialYear}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select Year" />
                </SelectTrigger>
                <SelectContent>
                  {financialYears.map((year: any) => (
                    <SelectItem
                      key={year.financial_year_range}
                      value={year.financial_year_range}
                    >
                      {year.financial_year_range}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid-cols-subgrid">
              <Label>
                Search By
                <span className="ml-1 text-red-500">*</span>
              </Label>
              <Select onValueChange={setSearchOn} value={searchOn}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select Search Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="propertyNumber">Property Number</SelectItem>
                  <SelectItem value="oldPropertyNumber">Old Property Number</SelectItem>
                  <SelectItem value="ownerName">Owner Name</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid-cols-subgrid">
              <Label>
                Search Value
                <span className="ml-1 text-red-500">*</span>
              </Label>
              <Input
                type="text"
                placeholder="Enter search value"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="mt-1"
              />
            </div>

            <div className="grid-cols-subgrid">
              <Button
                onClick={handleSearch}
                disabled={loading}
                className="mt-1 bg-blue-600 hover:bg-blue-700"
              >
                {loading ? <Loader /> : "Search"}
              </Button>
            </div>
          </div>
        </WhiteContainer>

        {open && (
          <WhiteContainer>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Assessment Report Results</h2>
              <div className="text-sm text-gray-600">
                Total Records: {pagination.totalRecords}
              </div>
            </div>
            <TanStackTable
              columns={columns}
              data={filteredData}
              pagination={pagination}
              onPageChange={(page, limit) => fetchAssessmentData(page, limit)}
            />
          </WhiteContainer>
        )}
      </div>
    </div>
  );
};

export default AssessmentReport;
