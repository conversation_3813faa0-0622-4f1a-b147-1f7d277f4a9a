import { Repository } from 'typeorm';
import { BillDataEntity } from '../entities'; // Assuming you have the entity in the `entities` folder
import { InjectRepository } from '@nestjs/typeorm';

export class BillDataRepository extends Repository<BillDataEntity> {
  constructor(
    @InjectRepository(BillDataEntity)
    private readonly billDataRepository: Repository<BillDataEntity>,
  ) {
    super(
      billDataRepository.target,
      billDataRepository.manager,
      billDataRepository.queryRunner,
    );
  }

  // Save a new bill
  async saveBillData(input: {
    billNo: string;
    bill_generation_date: Date;
    property_id: string;
    property_number: string;
    fyear: string;
  }): Promise<BillDataEntity> {
    const billData = this.billDataRepository.create(input);
    return await this.billDataRepository.save(billData);
  }

  // Find all bills
  async findAllBills() {
    return await this.billDataRepository
      .createQueryBuilder('bill_data')
      .orderBy('bill_data.bill_generation_date', 'DESC')
      .getMany();
  }

  // Find bill by property ID
  async findBillByPropertyId(property_id: string) {
    return await this.billDataRepository
      .createQueryBuilder('bill_data')
      .where('bill_data.property_id = :property_id', { property_id })
      .getOne();
  }

  // Update bill data by ID
  async updateBillData(
    id: string,
    input: {
      billNo?: string;
      bill_generation_date?: Date;
      fyear?: string;
    },
  ) {
    return await this.billDataRepository
      .createQueryBuilder('bill_data')
      .update(BillDataEntity)
      .set(input)
      .where('id = :id', { id })
      .execute();
  }

  // Delete (soft delete) a bill by ID
  async deleteBillData(id: string) {
    return await this.billDataRepository
      .createQueryBuilder('bill_data')
      .softDelete()
      .where('id = :id', { id })
      .execute();
  }
}
