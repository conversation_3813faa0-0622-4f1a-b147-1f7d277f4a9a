import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedColumnInProperty1743072273982 implements MigrationInterface {
    name = 'AddedColumnInProperty1743072273982'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property" ADD "ferfarRemark" character varying`);
  }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "ferfarRemark"`);
    }

}
