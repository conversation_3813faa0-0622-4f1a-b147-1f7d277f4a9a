import { <PERSON>, Get, Post, Param, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { CronJobsService, CronJobResult } from 'libs/helpers/src/cron-jobs/cron-jobs.service';

@ApiTags('Cron Jobs Management')
@Controller('cron-jobs')
export class CronJobsController {
  constructor(private readonly cronJobsService: CronJobsService) {}

  /**
   * Get health status of cron job system
   */
  @Get('health')
  @ApiOperation({ summary: 'Get cron job system health status' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns health status of cron job system',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['healthy', 'warning', 'critical'] },
        failedJobsCount: { type: 'number' },
        lastSuccessfulRun: { type: 'string', format: 'date-time' },
        issues: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  getHealthStatus() {
    return this.cronJobsService.getHealthStatus();
  }

  /**
   * Get all failed jobs
   */
  @Get('failed-jobs')
  @ApiOperation({ summary: 'Get list of all failed cron jobs' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns list of failed jobs',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          jobId: { type: 'string' },
          details: {
            type: 'object',
            properties: {
              jobName: { type: 'string' },
              lastAttempt: { type: 'string', format: 'date-time' },
              attempts: { type: 'number' },
              error: { type: 'string' },
              data: { type: 'object' }
            }
          }
        }
      }
    }
  })
  async getFailedJobs() {
    const failedJobs = await this.cronJobsService.getFailedJobs();
    return {
      success: true,
      data: failedJobs,
      timestamp: new Date()
    };
  }

  /**
   * Manually retry a specific failed job
   */
  @Post('retry/:jobId')
  @ApiOperation({ summary: 'Manually retry a failed cron job' })
  @ApiParam({ name: 'jobId', description: 'ID of the failed job to retry' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns result of manual retry attempt',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        data: { type: 'object' },
        error: { type: 'string' },
        timestamp: { type: 'string', format: 'date-time' }
      }
    }
  })
  async retryFailedJob(@Param('jobId') jobId: string): Promise<CronJobResult> {
    return await this.cronJobsService.retryFailedJob(jobId);
  }

  /**
   * Force run January 1st penalty calculation
   */
  @Post('force-january-penalties')
  @ApiOperation({ 
    summary: 'Force run January 1st penalty calculation',
    description: 'Manually trigger January 1st penalty calculation with retry mechanism'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns result of January 1st penalty calculation'
  })
  async forceJanuaryFirstPenalties(): Promise<CronJobResult> {
    return await this.cronJobsService.forceJanuaryFirstPenalties();
  }

  /**
   * Clean up old failed job records
   */
  @Post('cleanup-failed-jobs')
  @ApiOperation({ 
    summary: 'Clean up old failed job records',
    description: 'Remove failed job records older than 30 days'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns number of cleaned up records'
  })
  cleanupOldFailedJobs() {
    const cleanedCount = this.cronJobsService.cleanupOldFailedJobs();
    return {
      success: true,
      message: `Cleaned up ${cleanedCount} old failed job records`,
      data: { cleanedCount },
      timestamp: new Date()
    };
  }

  /**
   * Test penalty increment (for development/testing)
   */
  @Post('test-penalty-increment')
  @ApiOperation({
    summary: 'Test penalty increment functionality',
    description: 'Run penalty increment logic for testing purposes'
  })
  @ApiResponse({
    status: 200,
    description: 'Returns result of penalty increment test'
  })
  async testPenaltyIncrement(): Promise<any> {
    // This would call the penalty increment logic directly
    // Useful for testing without waiting for cron schedule
    try {
      const result = await this.cronJobsService.forceJanuaryFirstPenalties();
      return {
        success: true,
        message: 'Penalty increment test completed',
        data: result,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        message: 'Penalty increment test failed',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Test penalty increment for specific property (0722fe33-1362-4a55-829f-68a6f636bc76)
   */
  @Post('test-penalty-increment-specific-property')
  @ApiOperation({
    summary: 'Test penalty increment for specific property 0722fe33-1362-4a55-829f-68a6f636bc76',
    description: 'Run penalty increment logic for specific property with detailed console logs'
  })
  @ApiResponse({
    status: 200,
    description: 'Returns result of penalty increment test for specific property'
  })
  async testPenaltyIncrementForSpecificProperty(): Promise<any> {
    const specificPropertyId = '0722fe33-1362-4a55-829f-68a6f636bc76';

    try {
      const result = await this.cronJobsService.handlePenaltyIncrementForSpecificProperty(specificPropertyId);
      return {
        success: true,
        message: `Penalty increment test completed for property ${specificPropertyId}`,
        data: result,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        message: `Penalty increment test failed for property ${specificPropertyId}`,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Test penalty increment for any property by providing property ID
   */
  @Post('test-penalty-increment-for-property')
  @ApiOperation({
    summary: 'Test penalty increment for any property by providing property ID',
    description: 'Run penalty increment logic for any property with detailed console logs'
  })
  @ApiResponse({
    status: 200,
    description: 'Returns result of penalty increment test for the specified property'
  })
  async testPenaltyIncrementForProperty(@Body() body: { propertyId: string }): Promise<any> {
        const specificPropertyId = '0722fe33-1362-4a55-829f-68a6f636bc76';

    if (!body.propertyId) {
      return {
        success: false,
        message: 'Property ID is required',
        error: 'Missing propertyId in request body',
        timestamp: new Date()
      };
    }

    try {
      const result = await this.cronJobsService.handlePenaltyIncrementForSpecificProperty(body.propertyId);
      return {
        success: true,
        message: `Penalty increment test completed for property ${body.propertyId}`,
        data: result,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        message: `Penalty increment test failed for property ${body.propertyId}`,
        error: error.message,
        timestamp: new Date()
      };
    }
  }
}
