import React from "react";
import { Controller } from "react-hook-form";
import Select from "react-select";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";

const MultiSelect = ({ control, name, label, options, placeholder, errors }) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Controller
              control={control}
              name={name}
              render={({ field: { onChange, value, ref } }) => (
                <Select
                  inputRef={ref}
                  isMulti
                  options={options}
                  value={options ? options.filter(option => value.includes(option.value)) : []}
                  onChange={(selectedOptions) => onChange(selectedOptions.map(option => option.value))}
                  placeholder={placeholder}
                />
              )}
            />
          </FormControl>
          {errors[name] && (
            <FormMessage className="ml-1">
              {errors[name].message}
            </FormMessage>
          )}
        </FormItem>
      )}
    />
  );
};

export default MultiSelect;
