import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeInentityInRecipt1740143766492 implements MigrationInterface {
    name = 'ChangeInentityInRecipt1740143766492'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "receipt" ADD "remark" character varying(255)`);
     
        // await queryRunner.query(`ALTER TABLE "receipt" ALTER COLUMN "book_receipt_number" `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "receipt" ALTER COLUMN "book_receipt_number" `);

        await queryRunner.query(`ALTER TABLE "receipt" DROP COLUMN "remark"`);
    }

}
