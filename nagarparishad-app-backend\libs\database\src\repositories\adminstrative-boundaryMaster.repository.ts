import { Repository } from 'typeorm';
import { AdminstrativeBoundaryMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class AdminstrativeBoundaryMasterRepository extends Repository<AdminstrativeBoundaryMasterEntity> {
  constructor(
    @InjectRepository(AdminstrativeBoundaryMasterEntity)
    private readonly adminstrativeBoundaryMasterRepository: Repository<AdminstrativeBoundaryMasterEntity>,
  ) {
    super(
      adminstrativeBoundaryMasterRepository.target,
      adminstrativeBoundaryMasterRepository.manager,
      adminstrativeBoundaryMasterRepository.queryRunner,
    );
  }

  async saveData(input: {
    adminstrativeBoundary_name: string;
  }): Promise<AdminstrativeBoundaryMasterEntity> {
    let adminstrativeBoundary =
      this.adminstrativeBoundaryMasterRepository.create(input);
    adminstrativeBoundary =
      await this.adminstrativeBoundaryMasterRepository.save(
        adminstrativeBoundary,
      );
    return adminstrativeBoundary;
  }

  async findAllData(): Promise<AdminstrativeBoundaryMasterEntity[]> {
    return await this.adminstrativeBoundaryMasterRepository
      .createQueryBuilder('adminstrative_boundaryMaster')
      .orderBy('adminstrative_boundaryMaster.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string): Promise<AdminstrativeBoundaryMasterEntity> {
    return await this.adminstrativeBoundaryMasterRepository
      .createQueryBuilder('adminstrative_boundaryMaster')
      .where(
        'adminstrative_boundaryMaster.adminstrativeBoundary_id = :adminstrativeBoundary_id',
        {
          adminstrativeBoundary_id: id,
        },
      )
      .getOne();
  }

  async updateData(id: string, input: { adminstrativeBoundary_name?: string }) {
    return await this.adminstrativeBoundaryMasterRepository
      .createQueryBuilder('adminstrative_boundaryMaster')
      .update(AdminstrativeBoundaryMasterEntity)
      .set(input)
      .where('adminstrativeBoundary_id = :adminstrativeBoundary_id', {
        adminstrativeBoundary_id: id,
      })
      .execute();
  }

  async deleteData(id: string) {
    return await this.adminstrativeBoundaryMasterRepository
      .createQueryBuilder('adminstrative_boundaryMaster')
      .softDelete()
      .where('adminstrativeBoundary_id = :adminstrativeBoundary_id', {
        adminstrativeBoundary_id: id,
      })
      .execute();
  }
}
