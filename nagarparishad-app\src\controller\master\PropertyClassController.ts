import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import Api from "../../services/ApiServices";

const QUERY_KEY = ["propertyclassmaster"];

interface PropertyClass {
  id: string;
  name: string;
  property_type_class_id?: string;
  property_type_class?: string;
}

const fetchPropertyClasses = async () => {
  const response = await Api.getAllPropertyClass();
  return response.data;
};

const createPropertyClass = async (propertyClass: any) => {
  return new Promise((resolve, reject) => {
    Api.createPropertyClass(propertyClass, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updatePropertyClass = async ({
  id,
  data,
}: {
  id: string;
  data: any;
}) => {
  return new Promise((resolve, reject) => {
    Api.updatePropertyClass(id, data, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deletePropertyClass = async (id: string) => {
  return new Promise((resolve, reject) => {
    Api.deletePropertyClass(id, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const usePropertyClassMasterController = () => {
  const queryClient = useQueryClient();

  const { data: propertyClassData, isLoading:propertyClassLoading } = useQuery({
    queryKey: QUERY_KEY,
    queryFn: fetchPropertyClasses,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  const createPropertyClassMutation = useMutation({
    mutationFn: createPropertyClass,
    onSuccess: (newData) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      queryClient.setQueryData(QUERY_KEY, (old: any) => {
        const oldData = old?.data || [];
        return {
          ...old,
          data: [newData, ...oldData]
        };
      });
    },
  });

  const updatePropertyClassMutation = useMutation({
    mutationFn: updatePropertyClass,
    onSuccess: (updatedData, variables) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      queryClient.setQueryData(QUERY_KEY, (old: any) => {
        const oldData = old?.data || [];
        return {
          ...old,
          data: oldData.map((item: PropertyClass) =>
            item.property_type_class_id === variables.id ? { ...item, ...variables.data } : item
          )
        };
      });
    },
  });

  const deletePropertyClassMutation = useMutation({
    mutationFn: deletePropertyClass,
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      queryClient.setQueryData(QUERY_KEY, (old: any) => {
        const oldData = old?.data || [];
        return {
          ...old,
          data: oldData.filter((item: PropertyClass) => item.property_type_class_id !== id)
        };
      });
    },
  });

  return {
    propertyClassList: propertyClassData?.data|| [],
    propertyClassLoading,
    createPropertyClass: createPropertyClassMutation.mutate,
    updatePropertyClass: updatePropertyClassMutation.mutate,
    deletePropertyClass: deletePropertyClassMutation.mutate,
  };
};