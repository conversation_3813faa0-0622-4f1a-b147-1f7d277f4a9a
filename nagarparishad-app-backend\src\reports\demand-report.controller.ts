import { Controller, Get, Res, Query, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DemandReportService } from './demand-report.service';

@ApiTags('Reports')
@Controller('reports/')
export class DemandReportController {
  constructor(private readonly demandReportService: DemandReportService) {}

  @ApiOperation({ summary: 'Export demand report data to Excel' })
  @ApiResponse({
    status: 200,
    description: 'Excel file with demand report data',
    content: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {},
    },
  })
  @Get('demand/export-excel')
  async exportDemandReportToExcel(@Res() res: Response) {
    try {
      const excelBuffer = await this.demandReportService.exportDemandReportToExcel();
      
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        'attachment; filename=demand-report.xlsx'
      );
      res.setHeader('Content-Length', excelBuffer.length); // Add Content-Length
      
      res.status(HttpStatus.OK).send(excelBuffer);
    } catch (error) {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Failed to export demand report',
        error: error.message,
      });
    }
  }

  @ApiOperation({ summary: 'Export Milkatkar report data to Excel' })
  @ApiResponse({
    status: 200,
    description: 'Excel file with Milkatkar report data',
    content: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {},
    },
  })
  @Get('milkat-kar-akarani/export-excel')
  async exportMilkatKarAkaraniToExcel(
    @Res() res: Response,
    @Query('financialYear') financialYear: string,
  ) {
    try {
      const excelBuffer = await this.demandReportService.exportAllMilkatKarDataToExcel(financialYear);
      
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=milkat_kar_report_${financialYear}.xlsx`
      );
      res.setHeader('Content-Length', excelBuffer.length); // Add Content-Length
      
      res.status(HttpStatus.OK).send(excelBuffer);
    } catch (error) {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Failed to export Milkatkar report',
        error: error.message,
      });
    }
  }

  @Get('warshik-kar-akarani/export-excel')
  async exportWarshikKarAkaraniToExcel(
    @Res() res: Response,
    @Query('financialYear') financialYear: string,
  ) {
    try {
      const excelBuffer = await this.demandReportService.exportAllWarshikKarDataToExcel(financialYear);
      
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=warshik_kar_report_${financialYear}.xlsx`
      );
      res.setHeader('Content-Length', excelBuffer.length); // Add Content-Length
      
      res.status(HttpStatus.OK).send(excelBuffer);
    } catch (error) {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Failed to export demand report',
        error: error.message,
      });
    }
  }

  @ApiOperation({ summary: 'Export property report data to Excel' })
  @ApiResponse({
    status: 200,
    description: 'Excel file with property report data',
    content: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {},
    },
  })
  @Get('property/export-excel')
  async exportPropertyReportToExcel(
    @Res() res: Response,
    @Query('financialYear') financialYear: string,
  ) {
    try {
      const excelBuffer = await this.demandReportService.exportPropertyReportToExcel(financialYear);
      
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        'attachment; filename=property-report.xlsx'
      );
      res.setHeader('Content-Length', excelBuffer.length); // Add Content-Length
      
      res.status(HttpStatus.OK).send(excelBuffer);
    } catch (error) {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Failed to export property report',
        error: error.message,
      });
    }
  }

  @ApiOperation({ summary: 'Export paid user report data to Excel' })
  @ApiResponse({
    status: 200,
    description: 'Excel file with paid user report data',
    content: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {},
    },
  })
  @Get('paid-user/export-excel')
  async exportPaidUserReportToExcel(
    @Res() res: Response,
    @Query('financialYear') financialYear: string,
  ) {
    try {
      const excelBuffer = await this.demandReportService.exportPaidUserReportToExcel(financialYear);
      
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=paid_user_report_${financialYear}.xlsx`
      );
      res.setHeader('Content-Length', excelBuffer.length);
      
      res.status(HttpStatus.OK).send(excelBuffer);
    } catch (error) {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Failed to export paid user report',
        error: error.message,
      });
    }
  }
}
