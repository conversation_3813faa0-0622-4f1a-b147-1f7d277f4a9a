import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class JwtAuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  generateAccessToken(input: { user_id: string; email: string; role: number }) {
    return this.jwtService.signAsync(
      {
        sub: input.user_id,
        email: input.email,
        role: input.role,
      },
      {
        secret: this.configService.get<string>('JWT_ACCESS_SECRET'),
        expiresIn: '3h',
      },
    );
  }

  generateRefreshToken(input: {
    user_id: string;
    email: string;
    role: number;
  }) {
    return this.jwtService.signAsync(
      {
        sub: input.user_id,
        email: input.email,
        role: input.role,
      },
      {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
        expiresIn: '24h',
      },
    );
  }
}
