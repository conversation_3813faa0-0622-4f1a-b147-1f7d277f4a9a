import { Repository } from 'typeorm';
import { PropertyEntity, Tax_PropertyEntity, Tax_PropertyWiseEntity,Tax_FY_RecordsEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import {
  PaginatedResult,
  PaginationOptions,
  paginate,
} from '@helper/helpers/Pagination';
import { Logger } from '@nestjs/common';

export class Tax_FY_RecordsRepository extends Repository<Tax_FY_RecordsEntity> {
  constructor(
    @InjectRepository(Tax_FY_RecordsEntity)
    private readonly tax_FY_RecordsRepository: Repository<Tax_FY_RecordsEntity>,
  ) {
    super(
      tax_FY_RecordsRepository.target,
      tax_FY_RecordsRepository.manager,
      tax_FY_RecordsRepository.queryRunner,
    );
  }

 

 

  async findAllData() {
    return await this.tax_FY_RecordsRepository
      .createQueryBuilder('tax_fy_records')
      .select([
        'tax_fy_records.tax_fy_records_id', 
        'tax_fy_records.financial_year',
        'tax_fy_records.status',        
        'tax_fy_records.is_published'
       
      ])
      
      .orderBy('tax_fy_records.updated_at', 'DESC')
      .getMany();
  }


}
