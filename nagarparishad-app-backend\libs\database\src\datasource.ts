import { ConfigModule } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { entities } from './entities';
import { migrations } from './migrations';

ConfigModule.forRoot();

const DB_HOST = process.env.DB_HOST;
const DB_PORT: number = parseInt(process.env.DB_PORT, 10);
const DB_NAME = process.env.DB_NAME;
const DB_USERNAME = process.env.DB_USERNAME;
const DB_PASSWORD = process.env.DB_PASSWORD;
export const AppDataSource = new DataSource({
  type: 'postgres',
  host: DB_HOST,
  port: DB_PORT,
  database: DB_NAME,
  username: DB_USERNAME,
  password: DB_PASSWORD,
  entities: [...entities],
  migrations: [...migrations],
  migrationsTableName: 'migrations_table',
  logger: 'advanced-console',
  logging: true,
  synchronize: false, // never use TRUE in production!
  // migrationsRun: true
  extra: {
    max: 20,
    min: 5,
  },
});
