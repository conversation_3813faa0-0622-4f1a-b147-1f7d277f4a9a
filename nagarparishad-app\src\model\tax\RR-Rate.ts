export interface RR_RateSetting {
    rr_rate_id: string;
    financial_year?: string; // Optional for backward compatibility
    value: number;
    status: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    zone:{
        zone_id: string;
        zoneName: string;
        createdAt?: string;
        updatedAt?: string;
        deletedAt?: string | null;
    },
    reassessmentRange?: {
        reassessment_range_id: string;
        start_range: string;
        end_range: string;
    }
}


export interface RR_RateApi {
    statusCode: number;
    message: string;
    data: RR_RateSetting[];
  }

  export interface RR_RatemasterCreateApi {
    statusCode: number;
    message: string;
    data: RR_RateSetting;
  }
  export interface RR_RatemasterUpdateApi {
    financial_year?: string, // Optional for backward compatibility
    reassessment_range_id: string,
    value: number,
    status:string,
    zone_id:string,
    rr_rate_id:string
  }
  export interface RR_RateCreateApi {
    statusCode: number;
    message: string;
  }
  export interface RR_RatemasterSendApiObj {
    financial_year?: string; // Optional for backward compatibility
    reassessment_range_id: string;
    value: number;
    status: string;
    zone_id: string;  }
