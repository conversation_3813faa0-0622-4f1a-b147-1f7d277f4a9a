import { MigrationInterface, QueryRunner } from "typeorm";

export class MilkatKar1726683379932 implements MigrationInterface {
    name = 'MilkatKar1726683379932'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "milkatKar" ("milkatKar_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year" character varying NOT NULL, "all_property_tax_sum" double precision NOT NULL DEFAULT '0', "tax_type_1" double precision, "tax_type_2" double precision, "tax_type_3" double precision, "tax_type_4" double precision, "tax_type_5" double precision, "tax_type_6" double precision DEFAULT '0', "tax_type_7" double precision DEFAULT '0', "tax_type_8" double precision DEFAULT '0', "tax_type_9" double precision DEFAULT '0', "tax_type_10" double precision DEFAULT '0', "other_tax_sum_tax" double precision NOT NULL, "total_tax" double precision NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, CONSTRAINT "PK_037204bbe8ee16780c8965a4ecc" PRIMARY KEY ("milkatKar_id"))`);
        await queryRunner.query(`CREATE TABLE "milkatKarTax" ("milkatKartax_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year" character varying NOT NULL, "sq_ft_meter" double precision NOT NULL, "rr_rate" double precision NOT NULL, "rr_construction_rate" double precision NOT NULL, "depreciation_rate" double precision NOT NULL, "weighting" double precision NOT NULL, "capital_value" double precision NOT NULL, "tax_value" double precision NOT NULL, "tax" double precision NOT NULL, "bill_generation_date" date NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, "milkatKar_id" uuid, "property_usage_details_id" uuid, CONSTRAINT "PK_b561d692958f35b7d8372d2a424" PRIMARY KEY ("milkatKartax_id"))`);
        await queryRunner.query(`ALTER TABLE "milkatKar" ADD CONSTRAINT "FK_3e1f3fe6a693b75466f63a957a4" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD CONSTRAINT "FK_48589f448b7fc0e33f3ca4cb356" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD CONSTRAINT "FK_2be9895cd8df2ac061394a78944" FOREIGN KEY ("milkatKar_id") REFERENCES "milkatKar"("milkatKar_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD CONSTRAINT "FK_10403a07dc38f673bf18be42e3d" FOREIGN KEY ("property_usage_details_id") REFERENCES "property_usage_details"("property_usage_details_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP CONSTRAINT "FK_10403a07dc38f673bf18be42e3d"`);
        await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP CONSTRAINT "FK_2be9895cd8df2ac061394a78944"`);
        await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP CONSTRAINT "FK_48589f448b7fc0e33f3ca4cb356"`);
        await queryRunner.query(`ALTER TABLE "milkatKar" DROP CONSTRAINT "FK_3e1f3fe6a693b75466f63a957a4"`);
        await queryRunner.query(`DROP TABLE "milkatKarTax"`);
        await queryRunner.query(`DROP TABLE "milkatKar"`);
    }

}
