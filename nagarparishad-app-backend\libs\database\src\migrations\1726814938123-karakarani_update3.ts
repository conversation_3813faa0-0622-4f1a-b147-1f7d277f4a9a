import { MigrationInterface, QueryRunner } from "typeorm";

export class KarakaraniUpdate31726814938123 implements MigrationInterface {
    name = 'KarakaraniUpdate31726814938123'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "milkatKar" ADD "status" character varying`);
        await queryRunner.query(`ALTER TABLE "property" ADD "updateStatus" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "updateStatus"`);
        await queryRunner.query(`ALTER TABLE "milkatKar" DROP COLUMN "status"`);
    }

}
