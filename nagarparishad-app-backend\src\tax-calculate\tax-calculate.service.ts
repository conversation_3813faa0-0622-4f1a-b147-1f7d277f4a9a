import {
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';

import {
  Tax_PropertyWiseRepository,
  Tax_FY_RecordsRepository,
  Tax_PropertyRepository,
  WarshikKarRepository,
  PropertyMasterRepository,
  Financial_yearRepository

} from 'libs/database/repositories';
import { Tax_propertyFYDto } from './dto/tax-calculate.dto';
import { Tax_billGenerateService } from './tax-billGenarete.service';

import { fsync } from 'fs';
@Injectable()
export class TaxCalculateService {
  constructor(
    private readonly tax_PropertyRepository: Tax_PropertyRepository,
    private readonly tax_FY_RecordsRepository: Tax_FY_RecordsRepository,
    private readonly tax_billGenerateService: Tax_billGenerateService,
    private readonly warshikKarRepository: WarshikKarRepository,
    private readonly propertyMasterRepository:PropertyMasterRepository,
        private readonly financialYearRepository: Financial_yearRepository,
    

  ) { }
  // create(createTaxCalculateDto: CreateTaxCalculateDto) {
  //   return 'This action adds a new taxCalculate';
  // }

  async generate_bill(tax_propertyFYDto: Tax_propertyFYDto) {
    const { financial_year } = tax_propertyFYDto;
    // return {
    //   message: 'generate bill',
    //   data: financial_year
    // };


    //Check bill already generated for the FY
    //If exist return Bills already generated for this FY


    const allWarshikKarData: any = await this.warshikKarRepository.findAllData(financial_year);
    
    return allWarshikKarData;
  }

  async findAll(ward, value , searchOn, fy) {
    try {
      console.log("hereee-->+++",ward,value,searchOn,fy)
      const allWarshikKarData: any = await this.warshikKarRepository.findAllData_wardwise(ward, value , searchOn, fy);
      //       if (!allWarshikKarData || allWarshikKarData.length == 0) {
        if (!allWarshikKarData || allWarshikKarData.length == 0) {
          return {
        message: 'Records Not Found',
        data: [],
      };
      }
      return {
        message: 'Records Fetched Success',
        data: allWarshikKarData,
      };
    } catch (error) {
      throw error;
    }
    //return `This action returns all taxCalculate`;
  }

  async findFyList() {
    try {
      // await this.insertStatsIntoTaxFyRecords();
      // const getAllData = await this.tax_FY_RecordsRepository.findAllData();
  
      // // Check if data exists
      // if (!getAllData) {
      //   throw new NotFoundException('Data Not Found');
      // }
      const propertyCount = await this.propertyMasterRepository
            .createQueryBuilder('property')          
            .getCount();
      const getAllData =
        await this.financialYearRepository.find();
      // Add property and bill counts for each financial year
      const financialYearCounts = await Promise.all(
        getAllData.map(async (record) => {

          
          const warshiK_car_count = await this.warshikKarRepository
            .createQueryBuilder('warshik_kar')
            .where('warshik_kar.financial_year = :financialYear', { financialYear: record.financial_year_range })
            .andWhere('warshik_kar.status= :status', { status: 'active'})
            .getCount();
  
          const billGeneratedCount = await this.warshikKarRepository
            .createQueryBuilder('warshik_kar')
            .where('warshik_kar.financial_year = :financialYear', { financialYear: record.financial_year_range })
            .andWhere('warshik_kar.billNo IS NOT NULL')
            .andWhere('warshik_kar.status= :status', { status: 'active'})
            .getCount();
  
          return {
           
            financial_year:record?.financial_year_range,
           
            total_properties: propertyCount,
            warshiK_car_count:warshiK_car_count,
            bills_generated: billGeneratedCount,
          };
        })
      );
  
      return {
        message: 'Records Fetched Success',
        data: financialYearCounts,
      };
    } catch (error) {
      throw error;
    }
  }
  

  findOne(id: number) {
    return `This action returns a #${id} taxCalculate`;
  }

  // update(id: number, updateTaxCalculateDto: UpdateTaxCalculateDto) {
  //   return `This action updates a #${id} taxCalculate`;
  // }

  async updateStatus(id: string, isPublished: boolean) {
    const record = await this.tax_FY_RecordsRepository.findOne({ where: { tax_fy_records_id: id } });
    if (!record) {
      throw new NotFoundException('Record not found');
    }
    record.is_published = isPublished;
    await this.tax_FY_RecordsRepository.save(record);
    return {
      statusCode: 200,
      message: 'Record updated successfully',
      data: record,
    };
  }

  remove(id: number) {
    return `This action removes a #${id} taxCalculate`;
  }
  async insertStatsIntoTaxFyRecords() { 
    const getAllData = await this.tax_FY_RecordsRepository.findAllData();

    if (!getAllData) {
      throw new NotFoundException('Data Not Found');
    } 

    for (let i = 0; i < getAllData.length; i++) { 
      getAllData[i].is_published = false;
      await this.tax_FY_RecordsRepository.save(getAllData[i]);
    } 
  }

}
