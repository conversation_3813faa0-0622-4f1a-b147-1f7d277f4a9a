import axios from "axios";

import {
  DELETE_PAYMENT,
    PAYMENT_LIST,
    UPDATE_PAYMENT,


} from "@/constant/utils/paymentLogs";
import { Payment } from "@/model/Bill/payment";
// import { SolidWasteRateApi, SolidWasteRatemasterCreateApi, SolidWasteRatemasterSendApiObj, SolidWasteRatemasterUpdateApi } from "@/model/tax/solidWaste";

const baseURL = import.meta.env.VITE_APP_BASE_URL;

// Create axios instance with interceptors
const apiClient = axios.create({
  baseURL: baseURL,
});

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = PaymentListApi.getStoredToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle 401 and refresh token
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        }).catch((err) => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = PaymentListApi.getStoredRefreshToken();
        if (!refreshToken) {
          // No refresh token, redirect to login
          PaymentListApi.handleLogout();
          return Promise.reject(error);
        }

        const newTokens = await PaymentListApi.refreshAccessToken(refreshToken);
        if (newTokens.status) {
          const newAccessToken = newTokens.data.data.accessToken;
          const newRefreshToken = newTokens.data.data.refreshToken;

          // Update stored tokens
          localStorage.setItem('AccessToken', JSON.stringify(newAccessToken));
          localStorage.setItem('RefreshToken', JSON.stringify(newRefreshToken));

          // Update the original request with new token
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

          // Process queued requests
          processQueue(null, newAccessToken);

          // Retry the original request
          return apiClient(originalRequest);
        } else {
          // Refresh failed, logout user
          processQueue(error, null);
          PaymentListApi.handleLogout();
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Refresh failed, logout user
        processQueue(refreshError, null);
        PaymentListApi.handleLogout();
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);


class PaymentListApi {

  static getStoredToken = () => {
    const UserToken = JSON.parse(localStorage.getItem("AccessToken") || "{}");
    return UserToken !== undefined ? UserToken : false;
  };

  static getStoredRefreshToken = () => {
    const RefreshToken = JSON.parse(localStorage.getItem("RefreshToken") || "{}");
    return RefreshToken !== undefined ? RefreshToken : false;
  };

  static refreshAccessToken = async (refreshToken: string) => {
    const url = `${baseURL}/v1/auth/refreshtoken`;
    try {
      const response = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${refreshToken}`
        },
      });

      if (response.status === 200) {
        return { status: true, data: response.data };
      } else {
        console.log("Error refreshing token: " + JSON.stringify(response.data));
        return { status: false, data: response.data };
      }
    } catch (err) {
      console.log("Error refreshing token:", err);
      return { status: false, data: err };
    }
  };

  static handleLogout = () => {
    // Clear all tokens from localStorage
    localStorage.removeItem('AccessToken');
    localStorage.removeItem('RefreshToken');
    localStorage.removeItem('UserData');

    // Redirect to login page
    window.location.href = '/login';
  };


  // static getPaymentList = async ( limit: number,
  //   page: number,
  //   search?: string,
  //   searchOn?: string) => {
  //   try {
  //     const response = await axios.get(REACT_APP_GET_PAYMENT_LIST, {
  //       params: {
  //         params: {
  //           page,
  //           limit,
  //           ...(search && searchOn ? { value: search, searchOn } : {}), // Add search params if provided
  //         },
  //       },
  //       headers: {
  //         "Content-Type": "application/json",
  //         Accept: "application/json",
  //         Authorization: `Bearer ${PaymentListApi.getStoredToken()}`,
  //       },
  //     });
  //     if (response.status === 200) {
  //       const responseData: Payment = response.data;
  //       callback({ status: true, data: responseData });
  //     } else {
  //       callback({ status: false, data: response.data });
  //     }
  //   } catch (err: any) {
  //     callback({ status: false, data: err });
  //   }
  // };
  static getPaymentList(
    limit: number,
    page: number,
    search?: string,
    searchOn?: string
  ): Promise<any> {
    console.log("limit, page, search, searchOn:", limit, page, search, searchOn);
    const source = axios.CancelToken.source();

    return apiClient
      .get(PAYMENT_LIST, {
        params: {
          page,
          limit,
          ...(search && searchOn ? { value: search, searchOn } : {}), // Add search params if provided
        },
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        cancelToken: source.token,
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error("Error fetching properties:", error);
        }
        throw error; // Re-throw error for React Query to handle
      });
  }
  
  static updatePayment = async (
    paymentId: string,
    paymentData: Partial<Payment>,
    callback: (response: { status: boolean; data: Payment }) => void
  ) => {
    try {
      const response = await apiClient.put(`${UPDATE_PAYMENT}${paymentId}`, paymentData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error updating payment:", err);
      callback({ status: false, data: err });
    }
  };


  
  static getCurrentPropertyBill = async (
    obj:any
  ) => {
    console.log("obje",obj)

    try {
      const response = await apiClient.get("/v1/payment_log_master/current-bill", {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        params:obj
      });

      return response;
    } catch (err: any) {
      throw err;
    }
  };

  static paidCurrentBill = async (obj: any) => {
    console.log("obje", obj);

    try {
      const response = await apiClient.post(
        "/v1/payment_log_master/pay_bill",
        obj,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );

      return response;
    } catch (err: any) {
      throw err;
    }
  };

  static updatePaymentLog = async (
    paymentId: string,
    paymentData:any
  ) => {
    try {
      const response = await apiClient.put(`${UPDATE_PAYMENT}${paymentId}`, paymentData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        return { status: true, data: response.data };
      } else {
        return { status: false, data: response.data };
      }
    } catch (err) {
      console.error("Error updating payment:", err);
      return { status: false, data: err };
    }
  };

  static deletePayment = async (paymentId: string) => {
    try {
      const response = await apiClient.delete(`${DELETE_PAYMENT}${paymentId}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        return { status: true, data: response.data };
      } else {
        return { status: false, data: response.data };
      }
    } catch (err) {
      console.error("Error deleting payment:", err);
      return { status: false, data: err };
    }
  };
}
  
  


export default PaymentListApi ;
