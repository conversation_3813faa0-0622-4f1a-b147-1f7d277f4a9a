import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class CreateUsageSubMasterDto {
  @ApiProperty({ name: 'subUsage', type: 'string' })
  @IsNotEmpty()
  @IsString()
  subUsage: string;

  @ApiProperty({ name: 'usage', type: 'string' })
  @IsNotEmpty()
  @IsUUID()
  usage: string;
}
export class UpdateUsageSubMasterDto extends PartialType(
  CreateUsageSubMasterDto,
) {}

export class UsageSubMasterDto {
  @ApiProperty({ name: 'usageSub_id', type: 'string' })
  @IsNotEmpty()
  @IsUUID()
  usageSub_id: string;
}
export class UsageIdDto {
  @ApiProperty({ name: 'usage', type: 'string' })
  @IsNotEmpty()
  @IsUUID()
  usage: string;
}
