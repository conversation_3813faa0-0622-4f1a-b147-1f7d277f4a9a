import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  BaseEntity,
  ManyToOne,
  JoinColumn,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { PropertyEntity } from './property.entity'; // Make sure this import is correct

@Entity('common_fields_of_property')
export class CommonFiledsOfPropertyEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', nullable: true })
  GISID?: string;

  @Column({ type: 'varchar', nullable: true })
  propertyDescription?: string;

  @Column({ type: 'varchar', nullable: true })
  completionCertificate?: string;

  @Column({ type: 'varchar', nullable: true })
  accessRoad?: string;

  @Column({ type: 'enum', enum: ['yes', 'no'], nullable: true })
  individualToilet?: 'yes' | 'no';

  @Column({ type: 'varchar', nullable: true })
  toiletType?: string;

  @Column({ type: 'int', nullable: true })
  totalNumber?: number;

  @Column({ type: 'varchar', nullable: true })
  lightingFacility?: string;

  @Column({ type: 'varchar', nullable: true })
  tapConnection?: string;

  @Column({ type: 'int', nullable: true })
  totalConnections?: number;

  @Column({ type: 'enum', enum: ['yes', 'no'], nullable: true })
  solarProject?: 'yes' | 'no';

  @Column({ type: 'enum', enum: ['yes', 'no'], nullable: true })
  rainWaterHarvesting?: 'yes' | 'no';

  @Column({ type: 'enum', enum: ['yes', 'no'], nullable: true })
  sewageSystem?: 'yes' | 'no';

  @Column({ type: 'int', nullable: true })
  groundFloorArea?: number;

  @Column({ type: 'int', nullable: true })
  remainingGroundFloorArea?: number;

  // @OneToOne(() => PropertyEntity, (property) => property.commonFields)
  // @JoinColumn({ name: 'property_id' })
  // property: PropertyEntity;


  @OneToOne(() => PropertyEntity, (property) => property.property_id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

}
