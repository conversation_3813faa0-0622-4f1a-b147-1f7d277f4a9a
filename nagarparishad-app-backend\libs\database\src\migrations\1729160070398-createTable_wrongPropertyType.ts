import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableWrongPropertyType1729160070398 implements MigrationInterface {
    name = 'CreateTableWrongPropertyType1729160070398'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "wrong_property_typeMaster" ("Property_type_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "Property_type" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_e7dbab0a6386c2fe96dd90ffe0c" PRIMARY KEY ("Property_type_id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "wrong_property_typeMaster"`);
    }

}
