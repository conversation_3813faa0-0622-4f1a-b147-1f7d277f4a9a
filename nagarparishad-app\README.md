# Nagarparishad App

## Quick Guide

Use the following commands to get started with the project:

```bash
# Install the necessary dependencies
npm install


# Start the application in Browser development mode
npm run dev

# Start the application in Electron development mode
npm run start


# Build the web application
npm run build

# Build the electron application
npm run make
```

## Scripts

The following scripts are available for this project:

- `start`: Runs the Electron application using Electron Forge.
- `package`: Packages the Electron application using Electron Forge.
- `make`: Creates distributable packages for the Electron application using Electron Forge.
- `publish`: Publishes the Electron application using Electron Forge.
- `lint`: Runs ESLint to lint the TypeScript and TypeScript React files.
- `dev`: Runs Vite in development mode.
- `build`: Builds the production-ready version of the application using Vite.
- `serve`: Serves the built application using Vite.
- `commit`: Runs the commitizen CLI to create a new commit.
- `plop`: Runs the Plop CLI to generate new files.

## Instructions

To get started, run the following commands:

```bash
# Install the necessary dependencies
npm install

# Start the application in web development mode
npm run dev

# Start the application in Electron development mode
npm run start

# Build the application for production
npm run build

# Package the application
npm run package

# Create distributable packages
npm run make

# Publish the application
npm run publish

# Run lint to check for code style issues
npm run lint

# Create a new commit
npm run commit

# Generate new files using Plop
npm run plop
```

## Shadcn UI Components

This project uses components from [Shadcn UI](https://ui.shadcn.com).

## Installation

To add Shadcn UI components to your project, run the following command:

```bash
npx shadcn-ui@latest add
```

## Documentation

For detailed information on the components, refer to the [Shadcn UI Documentation](https://ui.shadcn.com/docs/components/button).

- ### Shadcn Components

  Components are added to the `src/components` directory. To use the component in your application, import it from the `components` directory. For example, to use the `Button` component, import it as follows:

  ```tsx
  import { Button } from "@/components/ui/button";

  const App = () => {
    return <Button>Click me</Button>;
  };
  ```

- ### Custom Components

  Run `npm run plop` to generate new components. Choose the `Component` generator and enter the name of the component. The component will be created in the `src/components/custom` directory.

  ```
  ? [PLOP] Please choose a generator. (Use arrow keys)
  ❯ Component - Create a new component
    Page - Create a new page
  ```

- ### Custom Pages

  Run `npm run plop` to generate new pages. Choose the `Page` generator and enter the name of the page. The page will be created in the `src/pages` directory.

  ```
  ? [PLOP] Please choose a generator. (Use arrow keys)
    Component - Create a new component
  ❯ Page - Create a new page
  ```

## How to Contribute

To contribute to this project, follow the steps below:

1. Fork the repository.
2. Clone the forked repository to your local machine.
3. Create a new branch for your changes.
4. Make your changes and commit them.
5. Push the changes to your forked repository.
6. Create a pull request to the `main` branch of the original repository.

## Technologies

The following technologies are used in this project:

- [TypeScript](https://www.typescriptlang.org/): A typed superset of JavaScript that compiles to plain JavaScript.
- [React](https://reactjs.org/): A JavaScript library for building user interfaces.
- [Vite](https://vitejs.dev/): A build tool that aims to provide a faster and leaner development experience for modern web projects.
- [Electron](https://www.electronjs.org/): A framework for building cross-platform desktop applications with web technologies.
- [Tailwind CSS](https://tailwindcss.com/): A utility-first CSS framework for rapidly building custom designs.
- [ESLint](https://eslint.org/): A pluggable and configurable linter tool for identifying and reporting on patterns in JavaScript.
- [Prettier](https://prettier.io/): An opinionated code formatter that enforces a consistent code style.
- [Commitizen](https://commitizen-tools.github.io/commitizen/): A CLI tool to generate commit messages following the Conventional Commits specification.
- [Plop](https://plopjs.com/): A micro-generator that helps you create new files.
