{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2021",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "paths": {
      "libs/database": [
        "libs/database/src"
      ],
      "libs/database/*": [
        "libs/database/src/*"
      ],
      "@helper/helpers": [
        "libs/helpers/src"
      ],
      "@helper/helpers/*": [
        "libs/helpers/src/*"
      ],
      "@jwt/jwt-auth": [
        "libs/jwt-auth/src"
      ],
      "@jwt/jwt-auth/*": [
        "libs/jwt-auth/src/*"
      ]
      
    },
    "esModuleInterop": true,
  }, "include": ["src/**/*", "templates/**/*", "libs/**/*"],
}