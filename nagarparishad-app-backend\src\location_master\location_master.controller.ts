import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { LocationMasterService } from './location_master.service';
import { CreateLocationMasterDto } from './dto/create-location_master.dto';
import { UpdateLocationMasterDto } from './dto/update-location_master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';
import { LocationMasterDto } from './dto/location-master.dto';

@ApiTags('Location Master')
@Controller('location-master')
export class LocationMasterController {
  constructor(private readonly locationMasterService: LocationMasterService) {}

  @Form('Location Master')
  @Permissions('can_write')
  @ApiOperation({ summary: 'Create a new location' })
  @ApiResponse({
    status: 201,
    description: 'The location has been successfully created',
  })
  @Post('create')
  create(@Body() createLocationMasterDto: CreateLocationMasterDto) {
    return this.locationMasterService.create(createLocationMasterDto);
  }

  @Form('Location Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all locations' })
  @ApiResponse({ status: 200, description: 'Returns all locations' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.locationMasterService.findAll();
  }

  @Form('Location Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one location' })
  @ApiResponse({ status: 200, description: 'Returns Single Location' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() locationMasterDto: LocationMasterDto) {
    return this.locationMasterService.findOne(locationMasterDto);
  }

  @Form('Location Master')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Update a location by ID' })
  @ApiResponse({
    status: 200,
    description: 'The location has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Location not found' })
  @Patch('update')
  update(
    @Query() locationMasterDto: LocationMasterDto,
    @Body() updateLocationMasterDto: UpdateLocationMasterDto,
  ) {
    return this.locationMasterService.update(
      locationMasterDto,
      updateLocationMasterDto,
    );
  }

  @Form('Location Master')
  @Permissions('can_delete')
  @ApiOperation({ summary: 'Delete a location by ID' })
  @ApiResponse({
    status: 200,
    description: 'The location has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'location not found' })
  @Delete('delete')
  remove(@Query() locationMasterDto: LocationMasterDto) {
    return this.locationMasterService.remove(locationMasterDto);
  }
}
