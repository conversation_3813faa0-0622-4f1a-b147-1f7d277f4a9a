import Api from "../../services/ApiServices";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ElectionBoundaryMasterObject } from "@/model/electionBoundry";

const fetchElectionBoundry = async () => {
  const response = await Api.getallElectionBoundryList();
  return response.data;
};

const createElectionBoundary = async (
  electionBoundryData: ElectionBoundaryMasterObject,
) => {
  return new Promise((resolve, reject) => {
    Api.createElectionBoundary(electionBoundryData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateElectionBoundary = async ({
  electionBoundaryId,
  electionBoundryData,
}) => {
  return new Promise((resolve, reject) => {
    Api.updateElectionBoundary(
      electionBoundaryId,
      electionBoundryData,
      (response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      },
    );
  });
};

const deleteElectionBoundary = async (electionBoundaryId: string) => {
  return new Promise((resolve, reject) => {
    Api.deleteElectionBoundary(electionBoundaryId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useElectionBoundaryController = () => {
  const queryClient = useQueryClient();

  const { data: boundaryListResponse, error } = useQuery({
    queryKey: ["electionboundrymaster"],
    queryFn: fetchElectionBoundry,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  if (error) {
    console.error("Error fetching streets:", error);
  }

  const createElectionBoundaryMutation = useMutation({
    mutationFn: createElectionBoundary,
    onMutate: async (newElectionboundry) => {
      await queryClient.cancelQueries({ queryKey: ["electionboundrymaster"] });
      const previousElectionBoundry = queryClient.getQueryData([
        "electionboundrymaster",
      ]);
      queryClient.setQueryData(
        ["electionboundrymaster"],
        (old: ElectionBoundaryMasterObject) => {
          const updatedData = [newElectionboundry, ...old];
          // Log the updated data
          return updatedData;
        },
      );
      return { previousElectionBoundry };
    },
    onError: (err, newWard, context) => {
      queryClient.setQueryData(
        ["electionboundrymaster"],
        context.previousElectionBoundry,
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["electionboundrymaster"] });
    },
  });

  const updateElectionBoundaryMutation = useMutation({
    mutationFn: updateElectionBoundary,
    onMutate: async ({ electionBoundaryId, electionBoundryData }) => {
      await queryClient.cancelQueries({ queryKey: ["electionboundrymaster"] });

      const previousElectionBoundry = queryClient.getQueryData([
        "electionboundrymaster",
      ]);

      queryClient.setQueryData(["electionboundrymaster"], (old) => {
        const updatedElectionBoundry = old?.map(
          (electionboundry: ElectionBoundaryMasterObject) =>
            electionboundry?.electionBoundary_id === electionBoundaryId
              ? { ...electionboundry, ...electionBoundryData }
              : electionboundry,
        );

        return updatedElectionBoundry;
      });

      return { previousElectionBoundry };
    },
    onError: (err, context) => {
      queryClient.setQueryData(
        ["electionboundrymaster"],
        context.previousElectionBoundry,
      );
      console.error("Error updating zone:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["electionboundrymaster"] });
    },
  });

  const deleteElectionBoundaryMutation = useMutation({
    mutationFn: deleteElectionBoundary,
    onMutate: async (electionBoundaryId) => {
      await queryClient.cancelQueries({ queryKey: ["electionboundrymaster"] });

      const previousElectionBoundry = queryClient.getQueryData([
        "electionboundrymaster",
      ]);

      queryClient.setQueryData(["electionboundrymaster"], (old) => {
        const updatedElectionBoundry = old.filter(
          (electionboundry: ElectionBoundaryMasterObject) =>
            electionboundry.electionBoundary_id !== electionBoundaryId,
        );
        return updatedElectionBoundry;
      });
      return { previousElectionBoundry };
    },
    onError: (err, wardId, context) => {
      queryClient.setQueryData(
        ["electionboundrymaster"],
        context.previousElectionBoundry,
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["electionboundrymaster"] });
    },
  });

  return {
    boundaryList: boundaryListResponse || [],
    createElectionBoundary: createElectionBoundaryMutation.mutate,
    updateElectionBoundary: updateElectionBoundaryMutation.mutate,
    deleteElectionBoundary: deleteElectionBoundaryMutation.mutate,
  };
};
