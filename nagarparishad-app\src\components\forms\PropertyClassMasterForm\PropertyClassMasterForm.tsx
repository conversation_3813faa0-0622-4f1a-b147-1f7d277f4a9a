import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { Loader2 } from "lucide-react";
import { usePropertyClassMasterController } from "@/controller/master/PropertyClassController";
import { PropertyClassMasterObject } from "@/model/PropertyClassMaster";

interface PropertyClassPopupFormInterface {
  btnTitle: string;
  editData?: PropertyClassMasterObject;
}

const PropertyClassMasterForm = ({ btnTitle, editData }: PropertyClassPopupFormInterface) => {
  const { t } = useTranslation();

  const schema = z.object({
    property_type_class: z.string().trim().min(1, t("errorsRequiredField")),
  });
  
  const { setOpen, refreshWardList, setRefreshWardList } = useContext(GlobalContext);
  const { createPropertyClass, updatePropertyClass } = usePropertyClassMasterController();
  const [loader, setLoader] = useState(false);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      property_type_class: editData?.property_type_class || "",
    },
  });

  const onSubmit = (data: z.infer<typeof schema>) => {
    const DataResponse = {
      property_type_class: data.property_type_class,
    };

    setLoader(true);

    if (editData?.property_type_class_id) {
      updatePropertyClass(
        { 
          id: editData.property_type_class_id, 
          data: DataResponse 
        },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate"),
              variant: "success",
            });
            resetForm();
          },
          onError: (error) => {
            toast({
              variant: "destructive",
              title: error.message,
            });
            setLoader(false);
          },
        }
      );
    } else {
      createPropertyClass(DataResponse, {
        onSuccess: () => {
          toast({
            title: t("api.formcreate"),
            variant: "success",
          });
          resetForm();
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });
    }
  };

  const resetForm = () => {
    form.reset({ property_type_class: "" });
    setOpen(false);
    setRefreshWardList(!refreshWardList);
    setLoader(false);
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        property_type_class: editData.property_type_class || "",
      });
    } else {
      form.reset({
        property_type_class: "",
      });
    }
  }, [editData, form]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full md:w-full">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="grid-cols-subgrid">
            <FormField
              control={form.control}
              name="property_type_class"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>{t("मालमत्ता प्रकार विभाग")}</FormLabel>
                  <FormControl className="mt-1">
                    <Input placeholder={t("मालमत्ता प्रकार विभाग")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="grid-cols-subgrid mb-1 ml-4 max-md:flex max-md:justify-end pt-[32px]">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin" />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
        </div>
      </form>
    </Form>
  );
};

export default PropertyClassMasterForm;