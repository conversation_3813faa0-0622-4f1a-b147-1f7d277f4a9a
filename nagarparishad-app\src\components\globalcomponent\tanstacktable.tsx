import * as React from "react";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ZoneObject, MasterObjectColumnDef } from "@/model/zone-master";
import { LocationMasterObject } from "@/model/location-master";
import { StreetMasterObject } from "@/model/street-master";
import AddNewBtn from "./AddNewForm";
import { AreaMasterObject } from "@/model/area-master";
import { PropertytypeMasterObject } from "@/model/propertytype-master";
import { PropertysubtypeMasterObject } from "@/model/propertysubtype-master";
import { Payment } from "@/model/global-master";
import { PropertyList } from "@/model/propertyregistration-master";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { UsageObject } from "@/model/usage-master";
import { UsageSubCreateObject } from "@/model/usagesub-master";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Loader } from "./Loader";
import { TaxUserByFYInterface, YearWiseRecordInterface } from "@/model/tax/taxUserDetailsByFYInterfaces";

type TableData =
  | ZoneObject
  | LocationMasterObject
  | StreetMasterObject
  | AreaMasterObject
  | PropertytypeMasterObject
  | PropertysubtypeMasterObject
  | Payment
  | PropertyList
  | UsageObject
  | UsageSubCreateObject
  | YearWiseRecordInterface
  | TaxUserByFYInterface
  | any;
// Type definition improvements
export type BtnVariant =
  | "default"
  | "destructive"
  | "outline"
  | "secondary"
  | "ghost"
  | "link"
  | "submit";
interface TableProps<T extends TableData> {
  columns: any;
  data: T[];
  masterType?: string;
  searchKey?: string;
  searchColumn?: string;
  btnVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "submit";
  searchColumnArray?: string[];
  loader?: boolean;
  showPagination?: boolean;
  serverPagination?: {
    currentPage: number;
    pageSize: number;
    totalPages: number;
    totalRecords: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
  };
  onSearch?: (value: string) => void;
  onSearchFieldChange?: (value: string) => void;
  selectedSearchField?: string;
}

const TanStackTable = <T extends TableData>(props: TableProps<T>) => {
  const {
    columns,
    data,
    masterType,
    searchKey,
    searchColumn,
    btnVariant,
    searchColumnArray,
    loader,
    showPagination = true,
    serverPagination,
    onSearch,
    onSearchFieldChange,
    selectedSearchField,
  } = props;
  
  const { t } = useTranslation();
  const isServerPagination = Boolean(serverPagination);

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [selectedAccekey, setSelectedAccekey] = React.useState(
    selectedSearchField || (searchColumnArray && searchColumnArray.length > 0 ? searchColumnArray[0] : searchColumn)
  );
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [pagination, setPagination] = React.useState({
    pageIndex: isServerPagination ? serverPagination.currentPage - 1 : 0,
    pageSize: isServerPagination ? serverPagination.pageSize : 10,
  });

  // Update pagination when server pagination changes
  React.useEffect(() => {
    if (isServerPagination) {
      setPagination({
        pageIndex: serverPagination.currentPage - 1,
        pageSize: serverPagination.pageSize,
      });
    }
  }, [isServerPagination, serverPagination?.currentPage, serverPagination?.pageSize]);

  // Configure the table
  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: isServerPagination ? undefined : getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: isServerPagination,
    pageCount: isServerPagination ? serverPagination.totalPages : undefined,
  });

  const columnNameMapping = {
    owner_details_name: "पहिले नाव",
    property_number: "मालमत्ता क्रमांक",
    old_property_number: "जुना मालमत्ता क्रमांक",
    ward_name: "वॉर्ड नाव",
    mobile_no: "मोबाइल नंबर",
    zone_name: "झोन नाव",
    floor_name: "मजला",
    book_number: "बुक क्रमांक",
    receipt_number: "पावती क्रमांक",
    streetOrRoadName: "मार्ग नाव",
    year: "वर्ष"
  };

  const handleSearchKeyChange = (value: string) => {
    setSelectedAccekey(value);
    setColumnFilters([]);
    table.getColumn(value)?.setFilterValue("");
    if (onSearchFieldChange) {
      onSearchFieldChange(value);
    }
    // Reset pagination to the first page
    if (isServerPagination) {
      serverPagination.onPageChange(1);
    } else {
      table.setPageIndex(0);
    }
  
    // Reset the search input field
    if (table.getColumn(value)) {
      table.getColumn(value)?.setFilterValue("");
    }
  };
  
  

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (isServerPagination) {
      serverPagination.onPageChange(newPage);
    } else {
      table.setPageIndex(newPage - 1);
    }
  };

  // Handle page size change
  const handlePageSizeChange = (newSize: number) => {
    if (isServerPagination) {
      serverPagination.onPageSizeChange(newSize);
    } else {
      table.setPageSize(newSize);
    }
  };

  return (
    <div className="w-full">
      {/* Search bar */}
      <div className={`flex items-center flex-wrap ${searchKey === undefined ? "gap-1 py-1" : "gap-3 py-4"}`}>
        <div className="">
          {searchKey === undefined ? null : (
            <div className="flex items-center">
              {searchColumnArray && (
                <div className="mr-3" style={{ width: "-webkit-fill-available" }}>
                  <Select value={selectedAccekey} onValueChange={handleSearchKeyChange}>
                    <SelectTrigger className="mt-0 w-full">
                      <SelectValue placeholder={t(`${searchKey}`)} />
                    </SelectTrigger>
                    <SelectContent side="top">
                      {searchColumnArray?.map((column, index) => (
                        <SelectItem key={index} value={column} className="">
                          {columnNameMapping[column] || column}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            <Input
  placeholder={t(`${columnNameMapping[selectedAccekey] ? columnNameMapping[selectedAccekey] : searchKey}`)}
  value={(table.getColumn(`${selectedAccekey}`)?.getFilterValue() as string) ?? ""}
  onChange={(event) => {
    const value = event.target.value.trimEnd();
    table.getColumn(`${selectedAccekey}`)?.setFilterValue(value);
    if (onSearch) {
      onSearch(value);
    }
    // Reset pagination to the first page
    if (isServerPagination) {
      serverPagination.onPageChange(1);
    } else {
      table.setPageIndex(0);
    }
  }}
  className="max-w-sm"
/>



            </div>
          )}
        </div>
        {masterType === "property" && (
          <AddNewBtn masterType={masterType} displayComponent={"table"} btnVariant={btnVariant} />
        )}
      </div>

      {/* Table */}
      <div className="rounded-md border relative">
        <Table>
          <TableHeader className="bg-[#f3f4f6] hover:!bg-none">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  const isSortableColumn = header.column.columnDef.accessorKey !== "sr_no" && 
                                           header.column.columnDef.accessorKey !== t("SrNo") && 
                                           header.column.columnDef.accessorKey !== t("Actions");
                  return (
                    <TableHead
                      key={header.id}
                      className={`text-base font-semibold text-left min-w-[100px] py-1 ${isSortableColumn ? "cursor-pointer" : ""}`}
                      onClick={isSortableColumn ? header.column.getToggleSortingHandler() : undefined}
                    >
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loader ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  <div className="absolute left-1/2 top-1/2">
                    <Loader />
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              <>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="text-left justify-start">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      {t("noResults")}
                    </TableCell>
                  </TableRow>
                )}
              </>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {showPagination && (
        <div className="flex flex-col max-[760px]:flex-col min-[760px]:flex-row min-[760px]:items-center justify-between py-4 gap-3">
          {/* First row/column - Records info */}
          <div className="text-sm text-muted-foreground max-[760px]:text-center min-[760px]:text-left">
            {isServerPagination
              ? `Showing ${Math.min((serverPagination.currentPage - 1) * serverPagination.pageSize + 1, serverPagination.totalRecords)} to ${Math.min(serverPagination.currentPage * serverPagination.pageSize, serverPagination.totalRecords)} of ${serverPagination.totalRecords} records`
              : `${table.getFilteredSelectedRowModel().rows.length} of ${table.getFilteredRowModel().rows.length} row(s) selected.`}
          </div>
          
          {/* Second row/column - Pagination controls */}
          <div className="flex flex-wrap max-[760px]:justify-center min-[760px]:justify-end gap-3">
            {/* Rows per page selector */}
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Rows per page</p>
              <Select
                value={`${table.getState().pagination.pageSize}`}
                onValueChange={(value) => handlePageSizeChange(Number(value))}
              >
                <SelectTrigger className="h-8 w-[80px]">
                  <SelectValue placeholder={table.getState().pagination.pageSize} />
                </SelectTrigger>
                <SelectContent side="top">
                  {[10, 20, 30, 40, 50].map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Page indicator */}
            <div className="flex items-center justify-center text-sm font-medium">
              Page {isServerPagination ? serverPagination.currentPage : table.getState().pagination.pageIndex + 1} of{" "}
              {isServerPagination ? serverPagination.totalPages : table.getPageCount()}
            </div>
            
            {/* Navigation buttons */}
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (isServerPagination) {
                    if (serverPagination.currentPage > 1 && !loader) {
                      handlePageChange(serverPagination.currentPage - 1);
                    }
                  } else {
                    table.previousPage();
                  }
                }}
                disabled={isServerPagination 
                  ? serverPagination.currentPage <= 1 || loader 
                  : !table.getCanPreviousPage()}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (isServerPagination) {
                    if (serverPagination.currentPage < serverPagination.totalPages && !loader) {
                      handlePageChange(serverPagination.currentPage + 1);
                    }
                  } else {
                    table.nextPage();
                  }
                }}
                disabled={isServerPagination 
                  ? serverPagination.currentPage >= serverPagination.totalPages || loader 
                  : !table.getCanNextPage()}
              >
                Next
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TanStackTable;