import { Controller, Get, Post, Put, Delete, Query, Body } from '@nestjs/common';
import { Master_rr_construction_rateService } from './master-rr-construction-rate.service';
import { Master_rr_construction_rate } from 'libs/database/entities';
import { CreateConstructionRateDto } from './dto/create-construction-rate.dto';
import { UpdateConstructionRateDto } from './dto/update-construction-rate.dto';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@Controller('master-rr-construction-rate')
export class Master_rr_construction_rateController {
  constructor(private readonly rrConstructionRateService: Master_rr_construction_rateService) {}

  
  @Form('RR Construction Rate')
  @Permissions('can_write')
  @Post('create')
  async create(
    @Body() data: CreateConstructionRateDto
  ): Promise<{ message: string; data: Master_rr_construction_rate[] }> {
    const savedRate = await this.rrConstructionRateService.create(data);
    return {
      message: 'Construction rate created successfully',
      data: savedRate.data,
    };
  }

  
  @Form('RR Construction Rate')
  @Permissions('can_read')
  @Get()
  async findAll(): Promise<{ message: string; data: Master_rr_construction_rate[] }> {
    const rates = await this.rrConstructionRateService.findAll();
    return {
      message: 'All construction rates retrieved successfully',
      data: rates.data,
    };
  }

  // @Get('findOne')
  // async findOne(
  //   @Query('id') id: string
  // ): Promise<{ message: string; data: Master_rr_construction_rate }> {
  //   const rate = await this.rrConstructionRateService.findOne(id);
  //   return {
  //     message: 'Construction rate retrieved successfully',
  //     data: rate,
  //   };
  // }

  
  @Form('RR Construction Rate')
  @Permissions('can_update')
  @Put('update')
  async update(
    @Query('id') id: string,
    @Body() data: UpdateConstructionRateDto
  ): Promise<{ message: string; data: Master_rr_construction_rate }> {
    const updatedRate = await this.rrConstructionRateService.update(id, data);
    return {
      message: 'Construction rate updated successfully',
      data: updatedRate.data,
    };
  }

  
  @Form('RR Construction Rate')
  @Permissions('can_delete')
  @Delete('delete')
  async delete(@Query('id') id: string): Promise<{ message: string }> {
    await this.rrConstructionRateService.delete(id);
    return {
      message: 'Construction rate deleted successfully',
    };
  }
}
