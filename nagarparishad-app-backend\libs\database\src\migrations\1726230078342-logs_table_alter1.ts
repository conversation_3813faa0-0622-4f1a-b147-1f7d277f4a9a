import { MigrationInterface, QueryRunner } from "typeorm";

export class LogsTableAlter11726230078342 implements MigrationInterface {
    name = 'LogsTableAlter11726230078342'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Dropping the "action" column
        await queryRunner.query(`ALTER TABLE "logs" DROP COLUMN IF EXISTS "action"`);

        // Adding new columns
        await queryRunner.query(`ALTER TABLE "logs" ADD COLUMN IF NOT EXISTS "logSubType" character varying(50)`);
        await queryRunner.query(`ALTER TABLE "logs" ADD COLUMN IF NOT EXISTS "file" character varying(100)`);
        await queryRunner.query(`ALTER TABLE "logs" ADD COLUMN IF NOT EXISTS "api" character varying(100)`);
        await queryRunner.query(`ALTER TABLE "logs" ADD COLUMN IF NOT EXISTS "Prev_data" json`);

        // Prevent adding "user_id" if it already exists
        await queryRunner.query(`ALTER TABLE "logs" ADD COLUMN IF NOT EXISTS "user_id" json`);

        // Adding "extra" column and altering "logType"
        await queryRunner.query(`ALTER TABLE "logs" ADD COLUMN IF NOT EXISTS "extra" json`);
        await queryRunner.query(`ALTER TABLE "logs" ALTER COLUMN "logType" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Reverse the "logType" column change
        await queryRunner.query(`ALTER TABLE "logs" ALTER COLUMN "logType" SET NOT NULL`);

        // Dropping columns in reverse order
        await queryRunner.query(`ALTER TABLE "logs" DROP COLUMN IF EXISTS "extra"`);
        await queryRunner.query(`ALTER TABLE "logs" DROP COLUMN IF EXISTS "user_id"`);
        await queryRunner.query(`ALTER TABLE "logs" DROP COLUMN IF EXISTS "Prev_data"`);
        await queryRunner.query(`ALTER TABLE "logs" DROP COLUMN IF EXISTS "api"`);
        await queryRunner.query(`ALTER TABLE "logs" DROP COLUMN IF EXISTS "file"`);
        await queryRunner.query(`ALTER TABLE "logs" DROP COLUMN IF EXISTS "logSubType"`);

        // Re-adding the "action" column
        await queryRunner.query(`ALTER TABLE "logs" ADD COLUMN "action" character varying(100) NOT NULL`);
    }
}
