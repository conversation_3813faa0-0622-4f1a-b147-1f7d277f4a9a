import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import {
  AreaMasterDto,
  CreateAreaMasterDto,
  UpdateAreaMasterDto,
} from './dto/area-master.dto';
import { AreaLocalityMasterRepository } from 'libs/database/repositories';

@Injectable()
export class AreaMasterService {
  constructor(
    private readonly areaMasterRepository: AreaLocalityMasterRepository,
  ) {}
  async create(createAreaMasterDto: CreateAreaMasterDto) {
    try {
      const saveArea =
        await this.areaMasterRepository.saveArea(createAreaMasterDto);

      if (!saveArea) {
        throw new NotAcceptableException('Failed To Save');
      }

      return {
        message: 'Area Saved SuccessFully',
        data: saveArea,
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllArea = await this.areaMasterRepository.findAllArea();

      if (!getAllArea) {
        throw new NotFoundException('Area Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllArea,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(areaMaster: AreaMasterDto) {
    try {
      const { area_id } = areaMaster;
      const checkArea = await this.areaMasterRepository.findById(area_id);
      if (!checkArea) {
        throw new NotFoundException('Area Not Found');
      }

      return {
        message: 'Area found Sucess',
        data: checkArea,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    areaMaster: AreaMasterDto,
    updateAreaMaster: UpdateAreaMasterDto,
  ) {
    try {
      const { area_id } = areaMaster;
      const checkArea = await this.areaMasterRepository.findById(area_id);
      if (!checkArea) {
        throw new NotFoundException('Area Not Found');
      }

      const updateArea = await this.areaMasterRepository.updateArea(
        area_id,
        updateAreaMaster,
      );

      if (updateArea.affected === 0) {
        throw new NotAcceptableException('Failed To Update');
      }
      return {
        message: 'Record Updated SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(areaMaster: AreaMasterDto) {
    try {
      const { area_id } = areaMaster;
      const checkArea = await this.areaMasterRepository.findById(area_id);
      if (!checkArea) {
        throw new NotFoundException('Area Not Found');
      }

      const deleteArea = await this.areaMasterRepository.deleteArea(area_id);

      if (deleteArea.affected === 0) {
        throw new NotAcceptableException('Failed to Delete');
      }

      return {
        message: 'Area Deleted Successfully',
      };
    } catch (error) {
      throw error;
    }
  }
}
