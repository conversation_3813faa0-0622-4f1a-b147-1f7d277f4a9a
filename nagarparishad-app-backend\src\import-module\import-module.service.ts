import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Response } from 'express';
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { Buffer } from 'buffer';
import { In, IsNull, <PERSON>Than, Like, Not } from 'typeorm';
import { validate as isUUID } from 'uuid'; // Import UUID validation function
import { log_type, log_sub_type, api } from 'src/utils/constants';
import * as path from 'path';
import * as fs from 'fs';
import * as ejs from 'ejs';
import JSZip from 'jszip';

import {
  ImportPropertyMasterRepository,
  ImportPropertyStatsMasterRepository,
  GIS_data_Repository,
  StreetMasterRepository,
  ZoneMasterRepository,
  WardMasterRepository,
  PropertyMasterRepository,
  OwnerTypeRepository,
  Property_Owner_DetailsRepository,
  Property_Usage_DetailsRepository,
  UsageMasterRepository,
  PropertyTypeMasterRepository,
  LogsRepository,
  FormMasterRepository,
} from 'libs/database/repositories';

import { Import_PropertyEntity } from 'libs/database/entities'; // Adjust the import path as needed
import { retry } from 'rxjs';
import { WrongPropertyTypeMasterRepository } from 'libs/database/repositories/wrong_property_type.repository';
import { FloorMasterRepository } from 'libs/database/repositories/floorMaster.repository';

@Injectable()
export class ImportModuleService {
  private floorKeywords: string[];

  constructor(
    private readonly importPropertyMasterRepository: ImportPropertyMasterRepository,
    private readonly importPropertyStatsMasterRepository: ImportPropertyStatsMasterRepository,
    private readonly gis_data_Repository: GIS_data_Repository,
    private readonly streetMasterRepository: StreetMasterRepository,
    private readonly zoneMasterRepository: ZoneMasterRepository,
    private readonly wardMasterRepository: WardMasterRepository,
    private readonly propertyMasterRepository: PropertyMasterRepository,
    private readonly ownerTypeRepository: OwnerTypeRepository,
    private readonly property_Owner_DetailsRepository: Property_Owner_DetailsRepository,
    private readonly property_Usage_DetailsRepository: Property_Usage_DetailsRepository,
    private readonly usageMasterRepository: UsageMasterRepository,
    private readonly propertyTypeMasterRepository: PropertyTypeMasterRepository,
    private readonly logsRepository: LogsRepository,
    private readonly gisDataRepository: GIS_data_Repository,
    private readonly wrong_propertyTypeRepo: WrongPropertyTypeMasterRepository,
    private readonly floorMasterRepo: FloorMasterRepository,
  ) {
    this.initialize();
  }
  private async initialize() {
    this.floorKeywords = await this.floorMasterRepo.getFloorKeywords();
  }

  private propertyTypeMappings = {
    '': ['', ' ', ''],
    'आर.सी.सी. पद्धतीची इमारत तळ मजला (ABC)': [
      'आर.सी.सी. पद्धतीची इमारत तळ मजला (ABC)',
    ],
    'आर.सी.सी. पद्धतीची इमारत पहिला मजला (ABC)': [
      'आर.सी.सी. पद्धतीची इमारत पहिला मजला (ABC)',
    ],
    'इतर पक्के  पद्धतीची इमारत (ABC)': ['इतर पक्के  पद्धतीची इमारत (ABC)'],
    'इतर पक्के  उभारलेली इमारत तळ मजला (ABC)': [
      'इतर पक्के  उभारलेली इमारत तळ मजला (ABC)',
    ],
    'इतर पक्के  पद्धतीची इमारत दुसरा  मजला (ABC)': [
      'इतर पक्के  पद्धतीची इमारत दुसरा  मजला (ABC)',
    ],
    'आर.सी.सी. पद्धतीची इमारत तळ मजला': [
      'आर.सी.सी. पद्धतीची इमारत तळ मजला',
      'आर.सी.सी. पद्धतीची इमारत तळ',
      'आर.सी.सी. पद्धतीची इमारत तळ.मजला',
      'आर.सी.सी. पद्धतीची इमारत-तळ मजला',
      'आर.सी.सी. पद्धतीची इमारत त. म.',
    ],

    'आर.सी.सी. पद्धतीची इमारत पहिला मजला': [
      'आर.सी.सी. पद्धतीची इमारत पहिला मजला',
      'आर.सी.सी. पद्धतीची इमारत पहिला',
      'आर.सी.सी. पद्धतीची इमारत पहिला.मजला',
      'आर.सी.सी. पद्धतीची इमारत-पहिला मजला',
      'आर.सी.सी. पद्धतीची- इमारत-पहिला',
      'आर.सी.सी. पद्धतीची इमारत पहिला मजला ',
      'आर.सी.सी. पद्धतीची इमारतपहिला.',
      'आर.सी.सी. पद्धतीची इमारत पहिला  मजला',
      'आर.सी.सी. पद्धतीची इमारत प. म',
      'आर.सी.सी. पद्धतीची इमारत प. म.',
      'आर.सी.सी. पद्धतीची पहिला मजला पत्रा  इमारत',
    ],

    'आर.सी.सी. पद्धतीची इमारत दुसरा मजला': [
      'आर.सी.सी. पद्धतीची इमारत दुसरा मजला',
      'आर.सी.सी. पद्धतीची इमारत दुसरा.मजला',
      'आर.सी.सी. पद्धतीची इमारत दुसरामजला',
      'आर.सी.सी. पद्धतीची इमारत-दुसरा मजला',
      'आर.सी.सी. पद्धतीची इमारत  दुसरा मजला',
      'आर.सी.सी. पद्धतीची इमारत पत्रा दुसरा मजला'
    ],
    'आर.सी.सी. पद्धतीची इमारत तिसरा मजला': [
      'आर.सी.सी. पद्धतीची इमारत तिसरा मजला',
      'आर.सी.सी. पद्धतीची इमारत तिसरा.मजला',
      'आर.सी.सी. पद्धतीची इमारत-तिसरा मजला',
    ],
    'आर.सी.सी. पद्धतीची इमारत': [
      'आर.सी.सी. पद्धतीची इमारत',
      'आर.सी.सी. पद्धतीची इमारत(F1)',
      'आर.सी.सी. पद्धतीची इमारत',
    ],
    'इतर पक्के  उभारलेली इमारत तळ मजला': ['इतर पक्के  उभारलेली इमारत तळ मजला'],
    'इतर पक्के  पद्धतीची इमारत दुसरा  मजला': [
      'इतर पक्के  पद्धतीची इमारत दुसरा  मजला',
    ],

    'इतर पक्के  पद्धतीची इमारत पहिला  मजला': [
      'इतर पक्के  उभारलेली इमारत रूम 1 पहिला मजला',
      'इतर पक्के  उभारलेली इमारत रूम 2 पहिला मजला',
      'इतर पक्के  उभारलेली इमारत रूम 1 पहिला मजला',
    ],
    'इतर पक्के  पद्धतीची इमारत': [
      'इतर पक्के  पद्धतीची इमारत',
      'इतर पक्के इमारत',
      'इतर पक्के इमारत',
    ],
    'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत': [
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत',
    ],
    'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत तळ मजला': [
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत तळ मजला',
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत तळ.मजला',
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत--तळ मजला',
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत',
    ],

    'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत पहिला मजला': [
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत पहिला मजला',
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत पहिला.मजला',
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत-पहिला मजला',
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत-पहिला पहिला',
    ],
    'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत दुसरा मजला': [
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत दुसरा मजला',
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत-दुसरा मजला',
    ],
    'दगड विटांचे मातीची इमारत': ['दगड विटांचे मातीची इमारत'],
    'दगड विटांचे मातीची इमारत तळ मजला': [
      'दगड विटांचे मातीची इमारत तळ.मजला',
      'दगड विटांचे मातीची इमारत तळ मजला',

      'दगड विटांचे मातीची इमारत तळ मजला',
      'दगड विटांचे मातीची इमारत  तळ मजल',
      'दगड विटांचे मातीची इमारत  तळ मजला',
      'दगड विटांचे मातीची इमारत तळ मजला',
    ],
    'दगड विटांचे मातीची इमारत पहिला मजला': [
      'दगड विटांचे मातीची इमारत पहिला.मजला',
      'दगड विटांचे मातीची इमारत पहिला मजला',
      'दगड विटांचे मातीची पहिला मजला'
    ],
    'दगड विटांचे मातीची इमारत दुसरा मजला': [
      'दगड विटांचे मातीची इमारत-दुसरा मजला',
      'दगड विटांचे मातीची इमारत दुसरा मजला',
      'दगड विटांचे मातीची इमारत दुसरा मजला',
    ],

    'झोपडी किंवा मातीची इमारत तळ मजला': ['झोपडी किंवा मातीची इमारत तळ मजला'],
    'झोपडी किंवा मातीची इमारत पहिला मजला': [
      'झोपडी किंवा मातीची इमारत पहिला मजला',
    ],
    'झोपडी किंवा मातीची इमारत': [
      'झोपडी किंवा मातीची इमारत',
      'झोपडी किंवा मातीची इमारत',
    ],
    'पत्र्याची इमारत तळ मजला': [
      'पत्र्याची इमारत तळ मजला',
      'पत्र्याची इमारत-तळ मजला',
      'पत्रा इमारत तळ मजला',
      'पत्रा पद्धतीची इमारत तळ मजला',
      'पत्रा पद्धतीची इमारत'
    ],
    'पत्र्याची इमारत पहिला मजला': [
      'पत्र्याची इमारत पहिला मजला',
      'पत्र्याची इमारत पहिला.मजला',
      'पत्र्याची इमारत-पहिला मजला',
      'पत्रा इमारत पहिला मजला',
      'पत्रा पद्धतीची इमारत पहिला मजला',
    ],
    'पत्र्याची इमारत दुसरा मजला': [
      'पत्र्याची इमारत दुसरा मजला',
      'पत्र्याची इमारत-दुसरा मजला',
      'पत्रा पद्धतीची इमारत दुसरा मजला',
    ],
    'पत्र्याची इमारत तिसरा मजला': [
      'पत्र्याची इमारत-तिसरा मजला',
      'पत्र्याची इमारत-तिसरा मजला',
    ],
    'पत्रा इमारत': ['पत्रा इमारत', 'पत्र्याची इमारत', 'पत्र्याची शेड'],
    'लोड बेरिंग तळ मजला': [
      'लोड बेरिंग तळ मजला',
      'लोड बेरिंग तळ.मजल',
      'लोड बेरिंग-तळ मजला',
      'लोड बेरिंग तळ.मजला',
      'लोडबेरिंग-तळ मजला',
      'लोड बेरिग तळ मजला',
      'लोड बेअरिंग तळ मजला',
    ],
    'लोड बेरिंग पहिला मजला': [
      'लोड बेरिंग पहिला मजला',
      'लोड बेरिंग पहिला.मजला',
      'लोड बेरिंग -पहिला मजला',
      'लोडबेरिंग- पहिला मजला',
      'लोड बेरिग पहिला मजला',
      'लोड बेअरिंग पहिला मजला',
      'लोड बेअरिंग पहिला मजला',
    ],
    'लोड बेरिंग दुसरा मजला': ['लोड बेरिंग दुसरा मजला', 'लोड बेरिंग-दुसरा मजला'],
    'लोड बेरिंग': [
      'लोड बेरिंग',
      'लोडबेरिंग',
      'लोड बेरिंग',
      'लोड बेरिग',
      'लोड बेअरिंग',
    ],
    'खुली जागा': ['खुली जागा', 'मोकळी जागा'],

    पडसर: ['पडसर', 'पडसर जागा'],

    मनोरा: [
      'मनोरा',
      'TOWER',
      'मनोरा इमारत',
      'मनोरा तळ मजला',
      'मनोरा पहिला मजला',
      'मनोरा तळघर',
      'TOWER',
      'मनोरा इमारत',
      'मनोरा इमारत टॉवर',
      'मनोरा इमारत टॉवर पहिला मजला',
      'मनोरा इमारत टॉवर दुसरा मजला ',
    ],
  };

  private propertyTypeWithoutFloor = {
    ' ': ['', ' '],
    'आर.सी.सी. पद्धतीची इमारत': [
      'आर.सी.सी. पद्धतीची इमारत तळ मजला',
      'आर.सी.सी. पद्धतीची इमारत पहिला मजला',
      'आर.सी.सी. पद्धतीची इमारत दुसरा मजला',
      'आर.सी.सी. पद्धतीची इमारत तिसरा मजला',
      'आर.सी.सी. पद्धतीची इमारत',
      // Add more as needed
    ],
    'आर.सी.सी. पद्धतीची इमारत (ABC)': [
      'आर.सी.सी. पद्धतीची इमारत (ABC)',
      'आर.सी.सी. पद्धतीची इमारत तळ मजला (ABC)',
      'आर.सी.सी. पद्धतीची इमारत पहिला मजला (ABC)',
    ],

    'इतर पक्के  पद्धतीची इमारत (ABC)': [
      'इतर पक्के  पद्धतीची इमारत (ABC)',
      'इतर पक्के  उभारलेली इमारत तळ मजला (ABC)',
      'इतर पक्के  पद्धतीची इमारत दुसरा  मजला (ABC)',
    ],
    'इतर पक्के  पद्धतीची इमारत': [
      'इतर पक्के  पद्धतीची इमारत',
      'इतर पक्के  उभारलेली इमारत तळ मजला',
      'इतर पक्के  पद्धतीची इमारत दुसरा  मजला',
      'इतर पक्के  पद्धतीची इमारत पहिला  मजला',
    ],

    'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत': [
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत',
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत तळ मजला',
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत पहिला मजला',
      'दगड विटांचे चुना किंवा सिमेंट वापरून उभारलेली इमारत दुसरा मजला',
      // Add more as needed
    ],
    'दगड विटांचे मातीची इमारत': [
      'दगड विटांचे मातीची इमारत',
      'दगड विटांचे मातीची इमारत तळ मजला',
      'दगड विटांचे मातीची इमारत पहिला मजला',
      'दगड विटांचे मातीची इमारत दुसरा मजला',
    ],
    'झोपडी किंवा मातीची इमारत': [
      'झोपडी किंवा मातीची इमारत तळ मजला',
      'झोपडी किंवा मातीची इमारत पहिला मजला',
      'झोपडी किंवा मातीची इमारत',
      // Add more as needed
    ],
    'पत्रा इमारत': [
      'पत्र्याची इमारत तळ मजला',
      'पत्र्याची इमारत पहिला मजला',
      'पत्र्याची इमारत दुसरा मजला',
      'पत्र्याची इमारत तिसरा मजला',
      'पत्रा इमारत',
      // Add more as needed
    ],
    'लोड बेरिंग': [
      'लोड बेरिंग तळ मजला',
      'लोड बेरिंग पहिला मजला',
      'लोड बेरिंग दुसरा मजला',
      'लोड बेरिंग',
      // Add more as needed
    ],
    'खुली जागा': ['खुली जागा तळ मजला', 'खुली जागा पहिला मजला', 'खुली जागा'],
    पडसर: ['पडसर'],

    मनोरा: ['मनोरा'],
  };

  private usageTypeMappings = {
    'वाणिज्य': ['वाणिज्य'],
    'निवासी': ['निवासी'],
    'औद्योगिक': ['औद्योगिक'],
    'धार्मिक': ['धार्मिक'],
    'शैक्षणिक': ['शैक्षणिक'],
    'शासकीय इमारत': ['शासकीय इमारत'],
    'खुली जागा-निवासी': ['खुली जागा-निवासी','खुली जागा  -निवासी','खुली जागा  -निवासी'],
    'खुली जागा-वाणिज्य': ['खुली जागा-वाणिज्य'],
    'खुली जागा-औद्योगिक': ['खुली जागा-औद्योगिक'],
    'खुली जागा-धार्मिक': ['खुली जागा-धार्मिक'],
    'खुली जागा-शैक्षणिक': ['खुली जागा-शैक्षणिक','खुली जागा  -शैक्षणिक'],
    'खुली जागा-शासकीय इमारत': ['खुली जागा-शासकीय इमारत'],
    'खुली जागा': ['खुली जागा'],
  };
  
  private getMappedUsageType(inputUsage: string): string | null {
    for (const key in this.usageTypeMappings) {
      if (this.usageTypeMappings[key].includes(inputUsage.trim())) {
        return key; 
      }
    }
    return null; 
  }
  
  private findMatchingPropertyType(usageDesc: string): {
    key: string | null;
    floor: string | null;
  } {
    // Trim and clean the incoming usage description
    const trimmedUsageDesc = usageDesc.trim();
    const cleanedString = trimmedUsageDesc
      .replace(/[-.]/g, ' ')
      .replace(/\s+/g, ' '); // Replace multiple spaces with a single space

    let detectedFloor = null;
    if (!cleanedString || cleanedString.length == 0) {
      return { key: '', floor: detectedFloor };
    }

    // Iterate through property type mappings
    for (const [key, variants] of Object.entries(this.propertyTypeMappings)) {
      for (let variant of variants) {
        // Clean each variant as well before comparing
        const cleanedVariant = variant
          .trim()
          .replace(/[-.]/g, ' ')
          .replace(/\s+/g, ' ');

        // Compare cleaned string with cleaned variants
        if (cleanedString === cleanedVariant) {
          for (const floor of this.floorKeywords) {
            if (cleanedVariant.includes(floor)) {
              detectedFloor = floor;
              break;
            }
          }
          let matchingKey = null;
          for (const [typeKey, typeValues] of Object.entries(
            this.propertyTypeWithoutFloor,
          )) {
            if (typeValues.includes(key)) {
              matchingKey = typeKey;
              break; // Exit once we find the first matching key
            }
          }

          console.log(
            'detectedFloor----------------->',
            matchingKey,
            detectedFloor,
          );

          return { key: matchingKey, floor: detectedFloor }; // Return both key and detected floor if a match is found
        }
      }
    }

    return { key: null, floor: detectedFloor };
  }

  async findAll(res: Response) {
    res.send('This action returns all import-module');
  }

  // async processFile(file) {
  //     const workbook = XLSX.read(file.buffer, { type: 'buffer' });
  //     const sheetName = workbook.SheetNames[3];
  /*************  ✨ Codeium Command ⭐  *************/
  /**
   * This action returns all import-module
   * @param res Response object
   */
  /******  04657014-c0c8-4b79-8871-b982d66a85dd  *******/
  //     const worksheet = workbook.Sheets[sheetName];
  //     const parsedData = XLSX.utils.sheet_to_json(worksheet, { defval: '' });

  //     const errors: string[] = [];
  //     if (parsedData.length <= 1) {
  //         errors.push('File does not contain enough rows.');
  //         return { error: true, message: 'File Validation Failed', errorsArray: errors };
  //     }

  //     // Skip the first four rows
  //     const currentTime=new Date().toISOString();
  //     const dataStartingFromRow5 = parsedData.slice(1);
  //     let new_row:any={};
  //     let All_rows=[];
  //     let sr_no:number=1;
  //     let property_number="";
  //     let ownerData=[];
  //     for await (const [index, rowData] of dataStartingFromRow5.entries()) {
  //       const ownerData = rowData["मालकाचे (धारण करणाऱ्याचे नाव )"]; // Assuming this is the owner data
  //        //  //       //  if((String(rowData["मालमत्ता क्र."]).trim().length==0))
  //       //  {
  //       //    console.log("Blank at ",sr_no," Property Number  ",String(rowData["मालमत्ता क्र."]).trim())
  //       //  }
  //       //  if(property_number == String(rowData["मालमत्ता क्र."]).trim()){
  //       //   console.log("Duplicate  at ",sr_no," Property Number  ",String(rowData["मालमत्ता क्र."]).trim())
  //       //  }
  //       if((String(rowData["मालमत्ता क्र."]).trim().length==0) || (property_number == String(rowData["मालमत्ता क्र."]).trim()))
  //           {
  //            console.log("Blank at ",sr_no," Property Number  ",String(rowData["मालमत्ता क्र."]).trim())
  //               if (ownerData) {
  //                 ownerData.push(ownerData);
  //               }
  //           }

  //          else
  //          {

  //             new_row={
  //              "sr_no":sr_no,
  //             "street_name":rowData["रस्त्याचे नाव/ गल्लीचे नाव"],
  //             "zoneName":rowData["झोन क्र."],
  //             "propertyNumber":rowData["मालमत्ता क्र."],
  //             "old_propertyNumber":rowData["जुना मालमत्ता क्र."],
  //             "import_date":currentTime,
  //             "ward_name":'4',
  //             "owner_name":rowData["मालकाचे (धारण करणाऱ्याचे नाव )"],
  //             "owner_type":rowData["वापरकर्ता"],
  //             "bhogawat_owner_name":rowData["भोगवटा करणाऱ्याचे नाव"],
  //             "usage_type":rowData["वापर"],
  //             "usage_desc":rowData["मालमत्तेचे वर्णन"],
  //             "construction_year":rowData["मिळकत बांधकामाचे वर्ष"],
  //             "length":rowData["लांबी"],
  //             "width":rowData["रुंदी"],
  //             "sq_ft":rowData["क्षेत्रफळ (चौ. फूट )"],
  //             "sq_meter":rowData["क्षेत्रफळ चौ. मी."],

  //             }
  //              property_number = String(rowData["मालमत्ता क्र."]).trim();
  //              //  await this.savePropertyData_(new_row);
  //               //              All_rows.push(new_row);
  //              sr_no++;
  //          }

  //         if (sr_no ==25)
  //           return;
  //     }

  //     //return { message: 'File processed successfully', data: All_rows };
  //     return { message: 'File processed successfully', data: dataStartingFromRow5 };
  //   }


  async processFile(file) {
     const All_rows: any[] = [];
     let sr_no: number = 1;
 const miising_Property_number=[];
     for (let i = 0; i <= 0; i++) {
       const workbook = XLSX.read(file.buffer, { type: 'buffer' });
       const sheetName = workbook.SheetNames[i];
       const worksheet = workbook.Sheets[sheetName];
       const parsedData:any = XLSX.utils.sheet_to_json(worksheet, { defval: '' });
 
       const errors: string[] = [];
       if (parsedData.length <= 1) {
         errors.push('File does not contain enough rows.');
         return {
           error: true,
           message: 'File Validation Failed',
           errorsArray: errors,
         };
       }
 
       // Skip the first four rows
       const currentTime = new Date().toISOString();
       const dataStartingFromRow5:any = parsedData.slice(3);
       // const dataStartingFromRow5 = parsedData.slice(1);
 
       let new_row: any = {};
       let property_number = '';
       let previousWardNumber=''
       //let sr_no: number = 1;
 
       let ownerData = [];
       for await (const [index, rowData] of dataStartingFromRow5.entries()) {
         // if (sr_no = 1) {
         //if row one
         new_row = {
           sr_no: sr_no,
           sr_no_excel: rowData['अ. क्र.'],
           street_name: rowData['रस्त्याचे नाव/ गल्लीचे नाव'],
           zoneName: rowData['झोन क्र.'],
           propertyNumber: rowData[' नवीन मालमत्ता क्र.'],
           // propertyNumber: rowData['मालमत्ता क्र.'],
 
           old_propertyNumber: rowData['जुना मालमत्ता क्र.'],
           import_date: currentTime,
           // ward_name: i + 1,
           // ward_number: 5,
           ward_name: rowData['वॉर्ड न.'] || previousWardNumber,
           // ward_name: 5,
           ward_number: rowData['वॉर्ड न.'] || previousWardNumber,
           // owner_name: rowData['मालमत्ता धरकाचे नाव'],
           owner_name: rowData['मालमत्ता धाराकाचे नाव'],
 
           // owner_name: rowData['मालमत्ता धारकाचे  नाव '],
           // owner_name: rowData['मालकाचे (धारण करणाऱ्याचे नाव )'],
 
           owner_type: rowData['वापरकर्ता '],
           // bhogawat_owner_name: rowData['भोगवटादार'],
           bhogawat_owner_name: rowData['भोगवटा करणाऱ्याचे नाव'],
 
           usage_type: rowData['वापर'],
           usage_desc: rowData['मालमत्तेचे वर्णन'],
           construction_year: rowData['मिळकत बांधकामाचे वर्ष'],
           // construction_year: '2024',
 
           length: rowData['लांबी'],
           width: rowData['रुंदी'],
           sq_ft: rowData['क्षेत्रफळ (चौ. फूट )'],
           sq_meter: rowData['क्षेत्रफळ चौ. मी.'],
           // sq_meter: rowData['क्षेत्रफळ (चौ. मी.)'],
           capital_value: rowData['भांडवली मूल्य'],
           //emarat_kar: rowData['इमारत कर_'],
           // total_tax: rowData['एकूण कर_'],
           emarat_kar: rowData[Object.keys(rowData)[24]], //change form 24 to 23
           total_tax: rowData[Object.keys(rowData)[32]], //change form 32 to 31
         };
 
 if(new_row.owner_type=='' && rowData.__EMPTY){
   new_row.owner_type=  rowData.__EMPTY.trim();
 
 }
 
         property_number = String(rowData[' नवीन मालमत्ता क्र.']).trim();
         if(new_row.sr_no_excel && !property_number){
           miising_Property_number.push(new_row.sr_no_excel);
         }
         const owner_name = rowData['मालमत्ता धाराकाचे नाव'];
 
         if (property_number && owner_name) {
           try {
             const gisData = await this.gis_data_Repository.findOne({
               where: {
                 form_number: property_number,
                 owner_name: Like(`%${owner_name}%`),
               },
             });
 
             if (gisData && gisData.mobile) {
               new_row['mobile'] = gisData.mobile;
             } else {
               new_row['mobile'] = 'Not Available in gisdata';
             }
           } catch (error) {
             console.error('Error fetching mobile number:', error);
             new_row['mobile'] = 'Error Fetching';
           }
         } else {
           new_row['mobile'] = 'Missing Property or Owner Name';
         }
         await this.savePropertyData_(new_row);
        //           All_rows.push(new_row);
         sr_no++;
         previousWardNumber = new_row.ward_number;
 
 
         //}
 
         if (sr_no == 7000) return;
       }
     }
    //  return { message: 'File processed successfully', data: All_rows };
     return { message: 'File processed successfully' };  
   }

  async getStatsData_on_importeddata(ward_number, syncDate) {
    try {
      let last_syncDate: any = syncDate;
      if (syncDate == '') {
        last_syncDate =
          await this.importPropertyStatsMasterRepository.getLastSyncDate(
            ward_number,
          );
      }
      const blankStreetCount =
        await this.importPropertyMasterRepository.getBlankStreetCount(
          ward_number,
        );
      const blankZoneNameCount: number =
        await this.importPropertyMasterRepository.getBlankZoneNameCount(
          ward_number,
        );
      const blankPropertyNumberCount: number =
        await this.importPropertyMasterRepository.getBlankPropertyNumberCount(
          ward_number,
        );
      const blankOldPropertyNumberCount: number =
        await this.importPropertyMasterRepository.getBlankOldPropertyNumberCount(
          ward_number,
        );
      const blankOwnerNameCount: number =
        await this.importPropertyMasterRepository.getBlankOwnerNameCount(
          ward_number,
        );
      const blankOwnerTypeCount: number =
        await this.importPropertyMasterRepository.getblankOwnerTypeCount(
          ward_number,
        );
      const blankUsageTypeCount: number =
        await this.importPropertyMasterRepository.getBlankUsageTypeCount(
          ward_number,
        );
      const blankUsageDescCount: number =
        await this.importPropertyMasterRepository.getBlankUsageDescCount(
          ward_number,
        );
      const blankConstructionYearCount: number =
        await this.importPropertyMasterRepository.getBlankConstructionYearCount(
          ward_number,
        );
      const blankLengthCount: number =
        await this.importPropertyMasterRepository.getBlankLengthCount(
          ward_number,
        );
      const blankWidthCount: number =
        await this.importPropertyMasterRepository.getBlankWidthCount(
          ward_number,
        );
      const blankSqftCount: number =
        await this.importPropertyMasterRepository.getBlankSqftCount(
          ward_number,
        );
      const blankSqmeterCount: number =
        await this.importPropertyMasterRepository.getBlankSqmeterCount(
          ward_number,
        );
      const totalCount: number =
        await this.importPropertyMasterRepository.getotalCount(ward_number);
      const okCount: number =
        await this.importPropertyMasterRepository.getokCount(ward_number);

      if (
        blankStreetCount === undefined ||
        blankStreetCount === null ||
        blankZoneNameCount === undefined ||
        blankZoneNameCount === null ||
        blankPropertyNumberCount === undefined ||
        blankPropertyNumberCount === null ||
        blankOldPropertyNumberCount === undefined ||
        blankOldPropertyNumberCount === null ||
        blankOwnerNameCount === undefined ||
        blankOwnerNameCount === null ||
        blankOwnerTypeCount === undefined ||
        blankOwnerTypeCount === null ||
        blankUsageTypeCount === undefined ||
        blankUsageTypeCount === null ||
        blankUsageDescCount === undefined ||
        blankUsageDescCount === null ||
        blankConstructionYearCount === undefined ||
        blankConstructionYearCount === null ||
        blankLengthCount === undefined ||
        blankLengthCount === null ||
        blankWidthCount === undefined ||
        blankWidthCount === null ||
        blankSqftCount === undefined ||
        blankSqftCount === null ||
        blankSqmeterCount === undefined ||
        blankSqmeterCount === null ||
        totalCount === undefined ||
        totalCount === null ||
        okCount === undefined ||
        okCount === null
      ) {
        throw new NotFoundException('Count can not fetched from Database');
      }

      if (syncDate != '') {
        //after only csv import syncDate is passed and save record into import_property_stats table
        let statData = {
          blankStreetCount,
          blankZoneNameCount,
          blankPropertyNumberCount,
          blankOldPropertyNumberCount,
          blankOwnerNameCount,
          blankOwnerTypeCount,
          blankUsageTypeCount,
          blankUsageDescCount,
          blankConstructionYearCount,
          blankLengthCount,
          blankWidthCount,
          blankSqftCount,
          blankSqmeterCount,
          totalCount,
          okCount,
          import_date: last_syncDate,
          ward_name: ward_number,
        };

        await this.importPropertyStatsMasterRepository.save(statData);
        return;
      }

      return {
        message: 'Statastics Of Imported Data',
        data: {
          blankStreetCount,
          blankZoneNameCount,
          blankPropertyNumberCount,
          blankOldPropertyNumberCount,
          blankOwnerNameCount,
          blankOwnerTypeCount,
          blankUsageTypeCount,
          blankUsageDescCount,
          blankConstructionYearCount,
          blankLengthCount,
          blankWidthCount,
          blankSqftCount,
          blankSqmeterCount,
          okCount,
          totalCount,
          last_syncDate,
        },
      };
    } catch (error) {
      throw error;
    }
  }
  async processDataForDuplicateNumber(wardNumber: any) {
    try {
      // Fetch the ward based on the ward number
      const ward = await this.wardMasterRepository.findOne({
        where: { ward_name: wardNumber },
      });

      if (!ward) {
        throw new Error('Ward not found');
      }

      // Fetch properties associated with the ward, ordered by propertyNumber
      const properties = await this.propertyMasterRepository.find({
        where: { ward: ward },
        order: { propertyNumber: 'ASC' },
      });

      let previousPropertyNumber = null;
      let count = 1;

      for (let property of properties) {
        if (property.propertyNumber === previousPropertyNumber) {
          property.propertyNumber = `${property.propertyNumber}/${count}`;
                    count++;
        } else {
          previousPropertyNumber = property.propertyNumber;
          count = 1;
        }

        // Save the updated property number back to the database
        await this.propertyMasterRepository.save(property);
      }
    } catch (error) {
      throw error;
    }
  }

  async savePropertyData_(row: any) {
    try {
      let { propertyNumber } = row;

      const saveData = await this.importPropertyMasterRepository.saveData(row);
      return {
        message: 'Data Saved SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }

  // async exportCsvData(wardNumber: string): Promise<Buffer> {
  //   const data: Import_PropertyEntity[] = await this.importPropertyMasterRepository.find({
  //     where: { ward_name: wardNumber },
  //   });

  //   if (!data || data.length === 0) {
  //     throw new NotFoundException(`No data found for ward number ${wardNumber}`);
  //   }

  //   // Convert the data to JSON, then to CSV
  //   const dataToExport = data.map(item => ({
  //     property_id: item.property_id,
  //     sr_no: item.sr_no,
  //     street_name: item.street_name,
  //     zoneName: item.zoneName,
  //     ward_name: item.ward_name,
  //     propertyNumber: item.propertyNumber,
  //     old_propertyNumber: item.old_propertyNumber,
  //     import_date: item.import_date?.toISOString(),
  //     ward_number: item.ward_number,
  //     owner_name: item.owner_name,
  //     owner_type: item.owner_type,
  //     bhogawat_owner_name: item.bhogawat_owner_name,
  //     usage_type: item.usage_type,
  //     usage_desc: item.usage_desc,
  //     construction_year: item.construction_year,
  //     length: item.length,
  //     width: item.width,
  //     sq_ft: item.sq_ft,
  //     sq_meter: item.sq_meter,
  //     missing_data: item.missing_desc,
  //     //createdAt: item.createdAt?.toISOString(),
  //     //updatedAt: item.updatedAt?.toISOString(),
  //   }));

  //   // const worksheet = XLSX.utils.json_to_sheet(dataToExport);
  //   // const workbook = { Sheets: { data: worksheet }, SheetNames: ['data'] };
  //   // const csv = XLSX.utils.sheet_to_csv(worksheet);

  //   // return csv;

  //  // Create a new workbook and worksheet
  // const workbook = XLSX.utils.book_new();
  // const worksheet = XLSX.utils.json_to_sheet(dataToExport);

  // // Define the range
  // const range = XLSX.utils.decode_range(worksheet['!ref']!);

  // // Apply conditional formatting to odd rows
  // for (let rowNum = range.s.r + 1; rowNum <= range.e.r; rowNum++) { // Skip the header row
  //   if (rowNum % 2 !== 0) { // Check if the row is odd
  //     for (let colNum = range.s.c; colNum <= range.e.c; colNum++) {
  //       const cellRef = XLSX.utils.encode_cell({ r: rowNum, c: colNum });
  //       if (!worksheet[cellRef]) worksheet[cellRef] = { v: '' }; // Ensure cell exists
  //       worksheet[cellRef].s = {
  //         fill: {
  //           fgColor: { rgb: "FFFF00" } // Yellow highlight for odd rows
  //         },
  //         font: {
  //           bold: true,
  //         },
  //         border: {
  //           top: { style: 'thin', color: { rgb: '000000' } },
  //           bottom: { style: 'thin', color: { rgb: '000000' } },
  //           left: { style: 'thin', color: { rgb: '000000' } },
  //           right: { style: 'thin', color: { rgb: '000000' } }
  //         }
  //       };
  //     }
  //   }
  // }

  // // Append the worksheet to the workbook
  // XLSX.utils.book_append_sheet(workbook, worksheet, 'Property Data');

  // // Generate an Excel buffer
  // const excelBuffer: Buffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer', cellStyles: true });

  // return excelBuffer;

  // }

  async exportCsvData(wardNumber: string): Promise<Buffer> {
    const data: Import_PropertyEntity[] =
      await this.importPropertyMasterRepository.find({
        where: { ward_name: wardNumber },
      });

    if (!data || data.length === 0) {
      throw new NotFoundException(
        `No data found for ward number ${wardNumber}`,
      );
    }

    const sortedData = data.sort((a, b) => a.sr_no - b.sr_no);
    //console.log("dataaa------------------------------------------------------------------------------",data)
    const dataToExport = data.map((item) => ({
      property_id: item.property_id,
      sr_no_excel: item.sr_no_excel,
      sr_no: item.sr_no,
      street_name: item.street_name,
      zoneName: item.zoneName,
      ward_name: item.ward_name,
      propertyNumber: item.propertyNumber,
      propertyNumber_merge: item.property_number_merge,
      old_propertyNumber: item.old_propertyNumber,
      import_date: item.import_date?.toISOString(),
      ward_number: item.ward_number,
      owner_name: item.owner_name,
      owner_type: item.owner_type,
      bhogawat_owner_name: item.bhogawat_owner_name,
      usage_type: item.usage_type,
      usage_desc: item.usage_desc,
      construction_year: item.construction_year,
      length: item.length,
      width: item.width,
      sq_ft: item.sq_ft,
      sq_meter: item.sq_meter,
      missing_data: item.missing_desc,
      status: item.status,
      is_new_property: item.is_new_property,
      duplicate_property_no: item.duplicate_property_no,
    }));

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Property Data');

    // Add columns
    worksheet.columns = [
      { header: 'sr_no', key: 'sr_no' },
      { header: 'अ. क्र.', key: 'sr_no_excel' },
      { header: 'Property_ID', key: 'property_id' },
      { header: 'रस्त्याचे नाव/ गल्लीचे नाव', key: 'street_name' },
      { header: 'क्षेत्राचे नाव', key: 'zoneName' },
      { header: 'Ward Name', key: 'ward_name' },
      { header: 'मालमत्ता क्रमांक', key: 'propertyNumber' },
      { header: 'मालमत्ता क्रमांक_merge', key: 'propertyNumber_merge' },
      { header: 'जुना मालमत्ता क्रमांक"', key: 'old_propertyNumber' },
      { header: 'Import Date', key: 'import_date' },
      { header: 'Ward Number', key: 'ward_number' },
      { header: 'मालकाचे नाव', key: 'owner_name' },
      { header: 'मालकाचा प्रकार', key: 'owner_type' },
      { header: 'Bhogawat Owner Name', key: 'bhogawat_owner_name' },
      { header: 'वापराचा प्रकार', key: 'usage_type' },
      { header: 'वापराचे वर्णन', key: 'usage_desc' },
      { header: 'बांधकामाचे वर्ष', key: 'construction_year' },
      { header: 'लांबी', key: 'length' },
      { header: 'रुंदी', key: 'width' },
      { header: 'क्षेत्रफळ (चौ. फूट )', key: 'sq_ft' },
      { header: 'क्षेत्रफळ (चौ. मी.)', key: 'sq_meter' },
      { header: 'Missing_Data', key: 'missing_data' },
      { header: 'status', key: 'status' },
      { header: 'is_new_property', key: 'is_new_property' },
      { header: 'Duplicate Property Number', key: 'duplicate_property_no' },
    ];

    // Add rows
    worksheet.addRows(dataToExport);

    // Apply conditional formatting to highlight rows where 'Missing_Data' is empty
    worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber === 1) {
        // Bold the font of the header row
        row.eachCell((cell) => {
          cell.font = { bold: true };
        });
      }

      const missingDataCell = row.getCell('missing_data');
      if (missingDataCell.value && rowNumber > 1) {
        // Check if Missing_Data is empty
        row.eachCell({ includeEmpty: false }, (cell) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFF00' }, // Yellow background for the entire row
          };
        });
      }

      const missingDataCell2 = row.getCell('status');
      if (missingDataCell2.value == 'incomplete-merge') {
        // Check if Missing_Data is empty
        row.eachCell({ includeEmpty: false }, (cell) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFF00' }, // Yellow background for the entire row
          };
        });
      }
      //checcking

      const missingDataCell3 = row.getCell('status');
      if (missingDataCell3.value == 'Already-merge') {
        // Check if Missing_Data is empty
        row.eachCell({ includeEmpty: false }, (cell) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '00FF00' }, // Yellow background for the entire row
          };
        });
      }

      const duplicatCell = row.getCell('duplicate_property_no');
      const duplicateValue = duplicatCell.value?.toString() || '';

      if (duplicateValue.includes('Property Number')) {
        const propertyNumberCell = row.getCell('propertyNumber');
        propertyNumberCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'f8b9b8' }, // Light pink color in ARGB format
        };
      }

      if (duplicateValue.includes('Old_prperty')) {
        const oldPropertyNumberCell = row.getCell('old_propertyNumber');
        oldPropertyNumberCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'efcc9a' }, // Light orange color in ARGB format
        };
      }
    });

    // Write Excel file to buffer
    const excelBuffer: any = await workbook.xlsx.writeBuffer();

    return excelBuffer;
  }

  async processAndUpdateFile(file: Express.Multer.File) {
    const currentFile = 'import-module.service';
    try {
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const parsedData = XLSX.utils.sheet_to_json(worksheet, { defval: '' });

      const errors: any = {}; // JSON object to capture error logs
      const updatedRows = [];

      if (parsedData.length === 0) {
        return {
          error: true,
          message: 'The file is empty or could not be read.',
          errors: {},
        };
      }

      // Extract the ward number from the first row
      const ward_number = parsedData[0]['Ward Number'];

      if (!ward_number) {
        return {
          error: true,
          message: 'Ward Number is missing in the first row.',
          errors: {},
        };
      }

      for (const row of parsedData) {
        const propertyId = row['Property_ID'];

        // Validate if propertyId exists and is a valid UUID
        if (!propertyId || !isUUID(propertyId)) {
          const errorData = {
            row,
            message: 'Invalid or missing property_id',
          };

          errors[propertyId || `Row ${parsedData.indexOf(row) + 1}`] = {
            errorType: 'InvalidUUID',
            errorMessage: 'Invalid or missing property_id in one or more rows.',
            data: row,
          };
          continue;
        }

        
        try {
          // Find existing property in the database
          const existingProperty =
            await this.importPropertyMasterRepository.findOne({
              where: { property_id: propertyId },
            });

          if (!existingProperty) {
            const errorData = {
              propertyId,
              row,
            };

            errors[propertyId] = {
              errorType: 'NotFound',
              errorMessage: `Property with ID ${propertyId} not found.`,
              data: row,
            };
            continue;
          }

          // Update the existing property with new data
          existingProperty.street_name =
            row['रस्त्याचे नाव/ गल्लीचे नाव'] || existingProperty.street_name;
          existingProperty.zoneName =
            row['क्षेत्राचे नाव'] || existingProperty.zoneName;
          existingProperty.ward_name =
            row['Ward Name'] || existingProperty.ward_name;
          existingProperty.propertyNumber =
            row['मालमत्ता क्रमांक'] || existingProperty.propertyNumber;
          existingProperty.property_number_merge =
            row['मालमत्ता क्रमांक_merge'] ||
            existingProperty.property_number_merge;
          existingProperty.old_propertyNumber =
            row['जुना मालमत्ता क्रमांक'] || existingProperty.old_propertyNumber;
          existingProperty.ward_number =
            row['Ward Number'] || existingProperty.ward_number;
          existingProperty.owner_name =
            row['मालकाचे नाव'] || existingProperty.owner_name;
          existingProperty.owner_type =
            row['मालकाचा प्रकार'] || existingProperty.owner_type;
          existingProperty.bhogawat_owner_name =
            row['Bhogawat Owner Name'] || existingProperty.bhogawat_owner_name;
          existingProperty.usage_type =
            row['वापराचा प्रकार'] || existingProperty.usage_type;
          existingProperty.usage_desc =
            row['वापराचे वर्णन'] || existingProperty.usage_desc;
          existingProperty.construction_year =
            row['बांधकामाचे वर्ष'] || existingProperty.construction_year;
          existingProperty.length = row['लांबी'] || existingProperty.length;
          existingProperty.width = row['रुंदी'] || existingProperty.width;
          existingProperty.sq_ft =
            row['क्षेत्रफळ (चौ. फूट )'] || existingProperty.sq_ft;
          existingProperty.sq_meter =
            row['क्षेत्रफळ (चौ. मी.)'] || existingProperty.sq_meter;
          existingProperty.is_new_property =
            row['is_new_property'] || existingProperty.is_new_property;

          
          // Save the updated entity
          const updatedProperty =
            await this.importPropertyMasterRepository.save(existingProperty);
          
          if (!updatedProperty) {
            throw new Error(`Failed to update property with ID ${propertyId}.`);
          }

          updatedRows.push(existingProperty);

          // Log success for each updated row
          // await this.logsRepository.logAction(
          //   'SUCCESS',
          //   'Update Property',
          //   `Property with ID ${propertyId} updated successfully`,
          //   { propertyId, row }
          // );
        } catch (error) {
                    errors.push = {
            errorType: 'SaveError',
            errorMessage: error.message,
            data: row,
          };

          //Logs
          this.logsRepository.logAction({
            logType: 'ERROR',
            logSubType: log_sub_type.CATCH_ERROR,
            file: currentFile,
            api: api.csv_upload_and_update,
            message: error.message,
            Prev_data: null,
            data: { error: error.stack },
            user_id: null,
          });

          continue; // Continue to the next row
        }
      }

      const syncDate = new Date();
      this.getStatsData_on_importeddata(ward_number, syncDate);
      this.processData(ward_number);

      // Return errors if any
      if (Object.keys(errors).length > 0) {
        //Logs
        this.logsRepository.logAction({
          logType: 'ERROR',
          file: currentFile,
          api: api.csv_upload_and_update,
          message: 'Some rows could not be processed',
          Prev_data: null,
          data: { error: errors },
          user_id: null,
        });
        return {
          message: 'Some rows could not be processed',
          errors: errors,
        };
      }

      //Logs
      this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.IMPORT,
        file: currentFile,
        api: api.csv_upload_and_update,
        message: 'File processed and data updated successfully',
        Prev_data: null,
        data: null,
        user_id: null,
      });

      return {
        message: 'File processed and data updated successfully',
        errors: {},
      };
    } catch (error) {
      this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: api.csv_upload_and_update,
        message: 'function catch ' + error.message,
        Prev_data: null,
        data: null,
        user_id: null,
      });
      throw error;
    }
  }

  // async processData(wardNumber: string) {

  //   await this.importPropertyMasterRepository.update(
  //     { ward_number: wardNumber },
  //     { missing_desc: "" }
  //   );

  //   const properties = await this.importPropertyMasterRepository.find({
  //     where: { ward_number: wardNumber
  //     },
  //   });

  //   for (const property of properties) {
  //     //  //     const missingColumns: string[] = [];
  //     let error_count: number = 0;
  //     // Check each column for blank values

  //     if (!property.street_name || property.street_name.trim().length==0) {
  //       missingColumns.push('रस्त्याचे नाव'); error_count++; }
  //    if (property.zoneName.trim().length==0) { missingColumns.push('क्षेत्राचे नाव'); error_count++; }
  //    if (property.propertyNumber.trim().length==0) { missingColumns.push('मालमत्ता क्रमांक'); error_count++; }
  //    if (property.old_propertyNumber.trim().length==0) { missingColumns.push('जुना मालमत्ता क्रमांक'); }
  //    if (property.owner_name.trim().length==0) { missingColumns.push('मालकाचे नाव'); error_count++; }
  //    if (property.owner_type.trim().length==0) { missingColumns.push('मालकाचा प्रकार"'); error_count++; }
  //    if (property.usage_type.trim().length==0) { missingColumns.push('मालमत्तेचे प्रकार'); error_count++; }
  //    if (property.usage_desc.trim().length==0) { missingColumns.push('मालमत्तेचे वर्णन'); error_count++; }
  //    if (property.construction_year.trim().length==0) { missingColumns.push('बांधकामाचे वर्ष'); error_count++; }
  //    if (property.length.trim().length==0) { missingColumns.push('लांबी'); error_count++; }
  //    if (property.width.trim().length==0) { missingColumns.push('रुंदी'); error_count++; }
  //    if (property.sq_meter.trim().length==0) { missingColumns.push('क्षेत्रफळ चौ. मी.'); error_count++; }

  //     if (error_count > 0) {
  //       property.status = 'incomplete';  // Set status to 'ok' if no missing columns
  //     } else {
  //       property.status = 'ok';  // You can set it to 'incomplete' or any other value
  //     }
  //     // Update the missing_desc column
  //     property.missing_desc = missingColumns.join(', ');

  //     if (property.propertyNumber.trim().length==0 && property.old_propertyNumber.trim().length==0 && property.owner_name.trim().length==0 ) {
  //       property.missing_desc = "";
  //       property.status = 'incomplete-merge';
  //     }

  //     // Save the updated property
  //     await this.importPropertyMasterRepository.save(property);
  //   }

  //   return { message: 'File processed and data updated successfully' };
  // }

  // this is cpu intensive function
  async processData_old(wardNumber: string) {
    await this.importPropertyMasterRepository.update(
      { ward_number: wardNumber },
      {
        missing_desc: '',
        property_number_merge: '',
        duplicate_property_no: '',
      },
    );

    const properties = await this.importPropertyMasterRepository.find({
      where: { ward_number: wardNumber },
      order: { sr_no: 'ASC' }, // Assuming sr_no defines the row order
    });
    const propertyNumberCountMap: { [propertyNumber: string]: number } = {};

    for (const property of properties) {
      const propertyNumber = property.propertyNumber.trim();
      if (propertyNumber) {
        if (propertyNumberCountMap[propertyNumber]) {
          propertyNumberCountMap[propertyNumber]++;
        } else {
          propertyNumberCountMap[propertyNumber] = 1;
        }
      }
    }

    const OldPropertyNumberCountMap: { [propertyNumber: string]: number } = {};

    for (const property of properties) {
      const propertyNumber = property.old_propertyNumber.trim();
      if (propertyNumber) {
        if (OldPropertyNumberCountMap[propertyNumber]) {
          OldPropertyNumberCountMap[propertyNumber]++;
        } else {
          OldPropertyNumberCountMap[propertyNumber] = 1;
        }
      }
    }

    let lastPropertyNumber = '';

    for (const property of properties) {
      const missingColumns: string[] = [];
      let error_count: number = 0;

      // Check each column for blank values
      if (!property.street_name || property.street_name.trim().length == 0) {
        missingColumns.push('रस्त्याचे नाव');
        error_count++;
      }
      if (!property.zoneName || property.zoneName.trim().length == 0) {
        missingColumns.push('क्षेत्राचे नाव');
        error_count++;
      }
      if (
        !property.propertyNumber ||
        property.propertyNumber.trim().length == 0
      ) {
        missingColumns.push('मालमत्ता क्रमांक');
        error_count++;
      }
      if (
        !property.old_propertyNumber ||
        property.old_propertyNumber.trim().length == 0
      ) {
        missingColumns.push('जुना मालमत्ता क्रमांक');
      }
      if (!property.owner_name || property.owner_name.trim().length == 0) {
        missingColumns.push('मालकाचे नाव');
        error_count++;
      }
      if (!property.owner_type || property.owner_type.trim().length == 0) {
        missingColumns.push('मालकाचा प्रकार');
        error_count++;
      }
      if (!property.usage_type || property.usage_type.trim().length == 0) {
        missingColumns.push('मालमत्तेचे प्रकार');
        error_count++;
      }
      if (!property.usage_desc || property.usage_desc.trim().length == 0) {
        missingColumns.push('मालमत्तेचे वर्णन');
        error_count++;
      }
      if (
        !property.construction_year ||
        property.construction_year.trim().length == 0
      ) {
        missingColumns.push('बांधकामाचे वर्ष');
        error_count++;
      }
      // if (!property.length || property.length.trim().length == 0) {
      //   missingColumns.push('लांबी');
      //   error_count++;
      // }
      // if (!property.width || property.width.trim().length == 0) {
      //   missingColumns.push('रुंदी');
      //   error_count++;
      // }
      if (!property.sq_meter || property.sq_meter.trim().length == 0) {
        missingColumns.push('क्षेत्रफळ चौ. मी.');
        error_count++;
      }

      if (error_count > 0) {
        property.status = 'incomplete';
      } else {
        property.status = 'ok';
      }

      //check for merge rows depend on अ. क्र. is blank in this case
      if (property.sr_no_excel.trim().length == 0) {
        //validation for only length,width and sq_meter is blank or not
        property.missing_desc = '';
        error_count = 0;
        // if (!property.length || property.length.trim().length == 0) {
        //   missingColumns.push('लांबी');
        //   error_count++;
        // }
        // if (!property.width || property.width.trim().length == 0) {
        //   missingColumns.push('रुंदी');
        //   error_count++;
        // }
        if (!property.sq_meter || property.sq_meter.trim().length == 0) {
          missingColumns.push('क्षेत्रफळ चौ. मी.');
          error_count++;
        }

        //if any length,width and width is blank insert
        if (error_count > 0) {
          property.status = 'incomplete-merge';
        } else {
          property.status = 'Already-merge';
        }
        property.missing_desc = missingColumns.join(', ');
      } else {
        property.missing_desc = missingColumns.join(', ');
      }

      // Update the property_number_merge column
      if (!property.sr_no_excel || property.sr_no_excel.trim().length == 0) {
        property.property_number_merge = lastPropertyNumber;
      } else {
        lastPropertyNumber = property.propertyNumber;
        // property.property_number_merge = lastPropertyNumber;
      }

      // if propety found duplicate insert into duplicate_property_no
      // duplicate checking on both column :propertyNumber and OldPropertyNumber
      // this coumn value is used in export csv to highlight dupliacte property number
      const propertyNumber = property.propertyNumber.trim();
      if (propertyNumber && propertyNumberCountMap[propertyNumber] > 1) {
        property.duplicate_property_no = 'Property Number';
      }
      const OldPropertyNumber = property.old_propertyNumber.trim();
      if (
        OldPropertyNumber &&
        OldPropertyNumberCountMap[OldPropertyNumber] > 1
      ) {
        property.duplicate_property_no = property.duplicate_property_no
          ? property.duplicate_property_no.concat(', Old_prperty')
          : 'Old_prperty';
      }

      // Save the updated property
      await this.importPropertyMasterRepository.save(property);
    }

    return { message: 'File processed and data updated successfully' };
  }

  async processData(wardNumber: string) {
    // Step 1: Batch update instead of individual update queries
    await this.importPropertyMasterRepository.update(
      { ward_number: wardNumber },
      {
        missing_desc: '',
        property_number_merge: '',
        duplicate_property_no: '',
      },
    );

    // Step 2: Fetch all properties at once to minimize database interactions
    const properties = await this.importPropertyMasterRepository.find({
      where: { ward_number: wardNumber },
      order: { sr_no: 'ASC' }, // Assuming sr_no defines the row order
    });

    // Step 3: Pre-compute property counts for property_number and old_property_number
    const propertyNumberCountMap: { [propertyNumber: string]: number } = {};
    const oldPropertyNumberCountMap: { [propertyNumber: string]: number } = {};

    for (const property of properties) {
      const propertyNumber = property.propertyNumber?.trim();
      if (propertyNumber) {
        propertyNumberCountMap[propertyNumber] =
          (propertyNumberCountMap[propertyNumber] || 0) + 1;
      }

      const oldPropertyNumber = property.old_propertyNumber?.trim();
      if (oldPropertyNumber) {
        oldPropertyNumberCountMap[oldPropertyNumber] =
          (oldPropertyNumberCountMap[oldPropertyNumber] || 0) + 1;
      }
    }

    let lastPropertyNumber = '';
    const updatedProperties: any[] = [];

    // Step 4: Process all properties in-memory, batch save at the end
    for (const property of properties) {
      const missingColumns: string[] = [];
      let errorCount = 0;
      const excludedUsageDescs = ['पडसर जागा', 'पडसर', 'पडसर ', 'पडसर'].map(
        (desc) => desc.trim(),
      );

      // Step 5: Validate property fields and build missing columns
      //commeting out below cause in padsar there are not street name so

      if (!property.zoneName?.trim())
        missingColumns.push('क्षेत्राचे नाव'), errorCount++;
      if (!property.propertyNumber?.trim())
        missingColumns.push('मालमत्ता क्रमांक'), errorCount++;
      if (!property.old_propertyNumber?.trim())
        missingColumns.push('जुना मालमत्ता क्रमांक');
      if (!property.owner_name?.trim())
        missingColumns.push('मालकाचे नाव'), errorCount++;
      if (!property.owner_type?.trim())
        missingColumns.push('मालकाचा प्रकार'), errorCount++;
      if (!property.usage_type?.trim())
        missingColumns.push('मालमत्तेचे प्रकार'), errorCount++;
      if (!property.usage_desc?.trim() || property.usage_desc?.trim() == '')
        missingColumns.push('मालमत्तेचे वर्णन'), errorCount++;
      if (!excludedUsageDescs.includes(property.usage_desc.trim())) {
        if (!property.construction_year?.trim()) {
          missingColumns.push('बांधकामाचे वर्ष'), errorCount++;
          errorCount++;
        }
      }
      if (!excludedUsageDescs.includes(property.usage_desc.trim())) {
        if (!property.street_name?.trim())
          missingColumns.push('रस्त्याचे नाव'), errorCount++;
      }
      // if (!property.length?.trim()) missingColumns.push('लांबी'), errorCount++;
      // if (!property.width?.trim()) missingColumns.push('रुंदी'), errorCount++;
      if (!property.sq_meter?.trim()) missingColumns.push('क्षेत्रफळ चौ. मी.');

      // Step 6: Check status and merge rows based on conditions
      if (errorCount > 0) {
        property.status = 'incomplete';
      } else {
        property.status = 'ok';
      }

      // Merge row checks when sr_no_excel is blank
      if (property.sr_no_excel?.trim().length == 0) {
                missingColumns.length = 0; // emtying the missing columens
        errorCount = 0;
        //2025 change
        // if (!property.length?.trim())
        //   missingColumns.push('लांबी'), errorCount++;
        // if (!property.width?.trim()) missingColumns.push('रुंदी'), errorCount++;
        if (!property.sq_meter?.trim())
          missingColumns.push('क्षेत्रफळ चौ. मी.'), errorCount++;

        // Check if property.usage_desc is not in the array

        if (!excludedUsageDescs.includes(property.usage_desc.trim())) {
          if (!property.construction_year?.trim()) {
            missingColumns.push('बांधकामाचे वर्ष'), errorCount++;
            errorCount++;
          }
        }
        if (errorCount > 0) {
          property.status = 'incomplete-merge';
        } else {
          property.status = 'Already-merge';
        }
              }

      // Store last property number
      if (property.sr_no_excel?.trim()) {
        lastPropertyNumber = property.propertyNumber;
      } else {
        property.property_number_merge = lastPropertyNumber;
      }

      // Step 7: Duplicate property number checks
      const propertyNumber = property.propertyNumber?.trim();
      if (propertyNumber && propertyNumberCountMap[propertyNumber] > 1) {
        property.duplicate_property_no = 'Property Number';
      }

      const oldPropertyNumber = property.old_propertyNumber?.trim();
      if (
        oldPropertyNumber &&
        oldPropertyNumberCountMap[oldPropertyNumber] > 1
      ) {
        property.duplicate_property_no = property.duplicate_property_no
          ? property.duplicate_property_no.concat(', Old_property')
          : 'Old_property';
      }

      // Append missing description and add property to batch
      property.missing_desc = missingColumns.join(', ');
      updatedProperties.push(property);
    }

    // Step 8: Batch save updated properties (reduces number of save operations)
    await this.importPropertyMasterRepository.save(updatedProperties);

    return { message: 'File processed and data updated successfully' };
  }

  async findImagesCompare(params: any) {
    const imagesData: any[] = [];

    // Check if property1 is provided
    if (params.property1) {
      const property1Data = await this.gis_data_Repository.findOne({
        where: { form_number: params.property1 },
        select: [
          'form_number',
          'new_property_no',
          'old_property_no',
          'property_images',
          'plan_attachment',
          'owner_name',
          'property_address',
        ],
      });

      if (property1Data) {
        const property1Images = property1Data.property_images
          ? property1Data.property_images
              .split(',')
              .map(
                (image) =>
                  `${process.env.S3_PUBLIC_URL}/property/${image.trim()}`,
              )
          : [];

        imagesData.push({
          property_number: property1Data.form_number,
          images: property1Images,
          owner_name: property1Data.owner_name,
          form_number: property1Data.form_number,
          new_property_no: property1Data.new_property_no,
          old_property_no: property1Data.old_property_no,
          property_images: property1Data.property_images,
          plan_attachment: property1Data.plan_attachment,
          property_address: property1Data.property_address,
        });
      }
    }

    // Check if property2 is provided
    if (params.property2) {
      const property2Data = await this.gis_data_Repository.findOne({
        where: { new_property_no: Like(`%${params.property2}%`) }, // LIKE filter for partial match
        select: [
          'form_number',
          'new_property_no',
          'old_property_no',
          'property_images',
          'plan_attachment',
          'owner_name',
          'property_address',
        ],
      });

      if (property2Data) {
        const property2Images = property2Data.property_images
          ? property2Data.property_images
              .split(',')
              .map(
                (image) =>
                  `${process.env.S3_PUBLIC_URL}/property/${image.trim()}`,
              )
          : [];

        imagesData.push({
          property_number: property2Data.form_number,
          images: property2Images,
          owner_name: property2Data.owner_name,
          form_number: property2Data.form_number,
          new_property_no: property2Data.new_property_no,
          old_property_no: property2Data.old_property_no,
          property_images: property2Data.property_images,
          plan_attachment: property2Data.plan_attachment,
          property_address: property2Data.property_address,
        });
      }
    }

    if (params.property3) {
      const property3Data = await this.gis_data_Repository.findOne({
        where: { form_number: params.property3 },
        select: ['form_number', 'property_images', 'owner_name'],
      });

      if (property3Data) {
        const property3Images = property3Data.property_images
          ? property3Data.property_images
              .split(',')
              .map(
                (image) =>
                  `${process.env.S3_PUBLIC_URL}/property/${image.trim()}`,
              )
          : [];

        imagesData.push({
          property_number: property3Data.form_number,
          images: property3Images,
          owner_name: property3Data.owner_name,
        });
      }
    }
    return {
      message: 'Compare images data with property',
      data: imagesData,
    };
  }

  //import from dumb property (import_property) into main system table
  async ImportPropertiesIntoSystem(wardNumber: string) {
    try {
      // Fetch properties with status 'ok' and blank duplicate_property_no
      const importProperties = await this.importPropertyMasterRepository.find({
        where: {
          status: In(['ok', 'Already-merge']),
          ward_name: wardNumber,
        },
        order: {
          sr_no: 'ASC',
        },
        //take: 100, // Limit the results to 50 records
      });

      const trimmedImportProperties = importProperties.map((property) => ({
        ...property,
        usage_type: property.usage_type?.trim(),
        street_name: property.street_name?.trim(),
        zoneName: property.zoneName?.trim(),
        usage_desc: property.usage_desc?.trim(),
        owner_type: property.owner_type?.trim(),
      }));

      let importPropertyPreviousId = null;
      let previousUsagetype = null;
      let previousconstruction_year = null;
      let street = null;
      let zone = null;

      let sr_nubmer = 1; // Initialize the serial number

      for (const element of trimmedImportProperties) {
        const importProperty = element;
        // console.log("no Float value_____________________________________________",importProperty.sq_meter)
        // console.log("Float value_____________________________________________",parseFloat(importProperty.sq_meter))
        // return
        if (importProperty.status === 'ok') {
          importPropertyPreviousId = importProperty.property_id;
          if (importProperty.usage_type && importProperty.usage_type.trim()) {
            const mappedUsageType =await this.getMappedUsageType(importProperty.usage_type);
          
            if (mappedUsageType) {
              previousUsagetype = await this.usageMasterRepository.findOne({
                where: { usage_type: mappedUsageType },
              });
          
              if (!previousUsagetype) {
                previousUsagetype = this.usageMasterRepository.create({
                  usage_type: mappedUsageType, 
                });
                await this.usageMasterRepository.save(previousUsagetype);
              }
            }
          }
          
          previousconstruction_year = importProperty.construction_year;

          street = await this.streetMasterRepository.findOne({
            where: { street_name: importProperty.street_name },
          });

          if (!street) {
            street = this.streetMasterRepository.create({
              street_name: importProperty.street_name,
            });
            await this.streetMasterRepository.save(street);
          }

          zone = await this.zoneMasterRepository.findOne({
            where: { zoneName: importProperty.zoneName },
          });

          if (!zone) {
            zone = this.zoneMasterRepository.create({
              zoneName: importProperty.zoneName,
            });

            await this.zoneMasterRepository.save(zone);
          }
        }

        let ward = await this.wardMasterRepository.findOne({
          where: { ward_name: importProperty.ward_number },
        });

        if (!ward) {
          ward = this.wardMasterRepository.create({
            ward_name: importProperty.ward_number,
          });
          await this.wardMasterRepository.save(ward);
        }

        let propertyNumberToUse = importProperty.propertyNumber.trim();
        let oldPropertyNumberToUse = importProperty.old_propertyNumber.trim();

        if (importProperty.status === 'Already-merge') {
          propertyNumberToUse = importProperty.property_number_merge;
          const previousProperty = importProperties.find(
            (prop) => prop.property_id === importPropertyPreviousId,
          );

          if (previousProperty) {
            oldPropertyNumberToUse = previousProperty.old_propertyNumber.trim();
          }

          if (importProperty.usage_type && importProperty.usage_type.trim()) {
            const mappedUsageType =await this.getMappedUsageType(importProperty.usage_type);
          
          
            previousUsagetype = await this.usageMasterRepository.findOne({
              where: { usage_type: mappedUsageType },
            });
          }

          if (importProperty.construction_year) {
            previousconstruction_year = importProperty.construction_year;
          }
        }

        let existingProperty = await this.propertyMasterRepository.findOne({
          where: [{ import_property_id: importPropertyPreviousId }],
        });

        let propertyId;
        let savedProperty;
        let newPropertyNumber;

        const getNextNewPropertyNumber = async (ward_number: string) => {
          const ward = await this.wardMasterRepository.findOne({
            where: {
              ward_name: ward_number,
            },
          });

          const lastProperty = await this.propertyMasterRepository.findOne({
            where: { ward: { ward_id: ward.ward_id } }, // Ensure correct foreign key is being referenced

            order: { newPropertyNumber: 'DESC' }, // Order by newPropertyNumber in descending order
          });

          let nextNumber = 1; // Default to 1 if no properties found

          if (lastProperty && lastProperty.newPropertyNumber) {
            const lastNumber = parseInt(
              lastProperty.newPropertyNumber.slice(-5),
            );

            // Increment the number for the next property
            nextNumber = lastNumber + 1;
          }
          // Pad the new number with leading zeros if necessary
          const paddedNumber = nextNumber.toString().padStart(5, '0');
          return `SNP${ward_number}${paddedNumber}`;
        };

        // Generate `newPropertyNumber` for each property inside the loop
        if (importProperty.status === 'ok') {
          newPropertyNumber = await getNextNewPropertyNumber(
            importProperty.ward_number.trim(),
          );
        }
                if (!existingProperty) {
          const newProperty = this.propertyMasterRepository.create({
            street: street,
            zone: zone,
            ward: ward,
            propertyNumber: propertyNumberToUse.trim(),
            old_propertyNumber: oldPropertyNumberToUse.trim(),
            import_property_id: importPropertyPreviousId,
            newPropertyNumber: newPropertyNumber,
            sr_no: sr_nubmer, // Generate a new property number for each iteration
          });
          sr_nubmer++;

          savedProperty = await this.propertyMasterRepository.save(newProperty);
          propertyId = savedProperty.property_id;
        } else {
          propertyId = existingProperty.property_id;
          savedProperty = existingProperty;

          await this.propertyMasterRepository.update(propertyId, {
            street: street,
            zone: zone,
            ward: ward,
            propertyNumber: propertyNumberToUse,
            old_propertyNumber: oldPropertyNumberToUse,
          });
        }

        let ownerDetails;

        if (importProperty.owner_table_id) {
          let ownerType = await this.ownerTypeRepository.findOne({
            where: { owner_type: importProperty.owner_type },
          });

          if (!ownerType) {
            ownerType = this.ownerTypeRepository.create({
              owner_type: importProperty.owner_type,
            });
            await this.ownerTypeRepository.save(ownerType);
          }

          if (importProperty.owner_name) {
            const ownerUpdateResult =
              await this.property_Owner_DetailsRepository.update(
                { property_owner_details_id: importProperty.owner_table_id },
                {
                  property: savedProperty,
                  owner_type: ownerType,
                  name: importProperty.owner_name,
                },
              );

            if (ownerUpdateResult.affected === 0) {
              console.warn(
                `Owner details with ID ${importProperty.owner_table_id} not found.`,
              );
            }
          }
          ownerDetails = await this.property_Owner_DetailsRepository.findOne({
            where: { property_owner_details_id: importProperty.owner_table_id },
          });
        } else {
          let existingOwnerDetails =
            await this.property_Owner_DetailsRepository.findOne({
              where: {
                property: {
                  property_id: propertyId,
                },
              },
            });
          if (!existingOwnerDetails) {
            let ownerType = await this.ownerTypeRepository.findOne({
              where: { owner_type: importProperty.owner_type },
            });

            if (!ownerType) {
              ownerType = this.ownerTypeRepository.create({
                owner_type: importProperty.owner_type,
              });
              await this.ownerTypeRepository.save(ownerType);
            }

            ownerDetails = this.property_Owner_DetailsRepository.create({
              property: savedProperty,
              owner_type: ownerType,
              name: importProperty.owner_name,
            });
            await this.property_Owner_DetailsRepository.save(ownerDetails);
          } else {
            ownerDetails = existingOwnerDetails;
          }
        }

        let usageDetails;

        if (importProperty.usage_details_table_id) {
          const { key: matchingPropertyTypeKey, floor: detectedFloor } =
            await this.findMatchingPropertyType(importProperty.usage_desc);

          let propertyType = null; // Initialize propertyType as null
          let floor_type = null;
          if (matchingPropertyTypeKey) {
            propertyType = await this.propertyTypeMasterRepository.findOne({
              where: { propertyType: matchingPropertyTypeKey },
            });
          }
          if (detectedFloor) {
            floor_type = await this.floorMasterRepo.findOne({
              where: { floor_name: detectedFloor },
            });
          }

          // If no property type found, save the wrong property type in another variable
          let wrongPropertyType = null;

          if (!propertyType) {
            wrongPropertyType = this.wrong_propertyTypeRepo.create({
              Property_type:
                matchingPropertyTypeKey || importProperty.usage_desc,
            });
            await this.wrong_propertyTypeRepo.save(wrongPropertyType); // Save in the wrong property type repository
          }
          if (!propertyType) {
            propertyType = this.propertyTypeMasterRepository.create({
              propertyType:
                matchingPropertyTypeKey || importProperty.usage_desc,
            });
            await this.propertyTypeMasterRepository.save(propertyType);
          }

          const usageUpdateResult =
            await this.property_Usage_DetailsRepository.update(
              {
                property_usage_details_id:
                  importProperty.usage_details_table_id,
              },
              {
                property: savedProperty,
                usageType: previousUsagetype,
                propertyType: propertyType, // This will be null if no matching property type was found
                construction_start_year: previousconstruction_year,
                length: parseFloat(importProperty.length),
                width: parseFloat(importProperty.width),
                are_sq_ft: parseFloat(importProperty.sq_ft),
                are_sq_meter: parseFloat(importProperty.sq_meter),
                floorType: floor_type,
              },
            );

          if (usageUpdateResult.affected === 0) {
            console.warn(
              `Usage details with ID ${importProperty.usage_details_table_id} not found.`,
            );
          }

          usageDetails = await this.property_Usage_DetailsRepository.findOne({
            where: {
              property_usage_details_id: importProperty.usage_details_table_id,
            },
          });
        } else {
          // Find matching property type
                    const { key: matchingPropertyTypeKey, floor: detectedFloor } =
            await this.findMatchingPropertyType(importProperty.usage_desc);

          let propertyType = null; // Initialize propertyType as null
          let floor_type = null;
          if (detectedFloor) {
            floor_type = await this.floorMasterRepo.findOne({
              where: { floor_name: detectedFloor },
            });
          }
          if (matchingPropertyTypeKey) {
            propertyType = await this.propertyTypeMasterRepository.findOne({
              where: { propertyType: matchingPropertyTypeKey },
            });
          }

          // If no property type found, save the wrong property type in another variable
          let wrongPropertyType = null;

          if (!propertyType) {
            wrongPropertyType = this.wrong_propertyTypeRepo.create({
              Property_type:
                matchingPropertyTypeKey || importProperty.usage_desc,
            });
            await this.wrong_propertyTypeRepo.save(wrongPropertyType); // Save in the wrong property type repository
          }
          // If the property type exists, save usage details normally
          if (!propertyType) {
            propertyType = this.propertyTypeMasterRepository.create({
              propertyType:
                matchingPropertyTypeKey || importProperty.usage_desc,
            });
            await this.propertyTypeMasterRepository.save(propertyType);
          }
          usageDetails = this.property_Usage_DetailsRepository.create({
            property: savedProperty,
            usageType: previousUsagetype,
            propertyType: propertyType, // Will be valid if found
            construction_start_year: previousconstruction_year,
            length: parseFloat(importProperty.length),
            width: parseFloat(importProperty.width),
            are_sq_ft: parseFloat(importProperty.sq_ft),
            are_sq_meter: parseFloat(importProperty.sq_meter),
            floorType: floor_type,
          });

          await this.property_Usage_DetailsRepository.save(usageDetails);
        }

        await this.importPropertyMasterRepository.update(
          { property_id: importProperty.property_id },
          {
            property_table_id: propertyId,
            owner_table_id: ownerDetails.property_owner_details_id,
            usage_details_table_id: usageDetails.property_usage_details_id,
          },
        );
      }

      return {
        message: 'Properties and related details inserted successfully.',
      };
    } catch (error) {
      throw error;
    }
  }

  async ImportInfoIntoSystem(wardNumber: string) {
    try {
      const importProperties = await this.property_Owner_DetailsRepository
        .createQueryBuilder('owner')
        .leftJoinAndSelect('owner.property', 'property') // Join on the property relation
        .select([
          'owner',
          'property.propertyNumber',
          'property.old_propertyNumber',
        ])
        .getMany();

      const propetyNumber = importProperties[1].property.propertyNumber;
      const OldpropetyNumber = importProperties[1].property.old_propertyNumber;

      return {
        message: 'File processed successfully;;',
        data: {
          propetyNumber: propetyNumber,
          OldpropetyNumber: OldpropetyNumber,
        },
      };
    } catch (error) {
      throw error;
    }
  }

  async UpdateDataFromGiS(wardNumber: string) {
    try {
    } catch (error) {
      throw error;
    }
  }

  async UpdateMobileNumber(wardNumber: string) {
    try {
      // Step 1: Fetch the ward data from the GIS_data_Repository
      const gisDataList = await this.gis_data_Repository.find({
        where: { ward: Number(wardNumber) },
      });

      if (!gisDataList || gisDataList.length === 0) {
        throw new Error('Ward data not found');
      }

      // Iterate over each item in gisDataList
      for (const gisData of gisDataList) {
        // Step 2: Match the new_property_no from the GIS data with the property_number in the PropertyMasterRepository
        const propertyMasterList = await this.propertyMasterRepository.find({
          where: { propertyNumber: gisData.new_property_no },
        });

        if (propertyMasterList && propertyMasterList.length > 0) {
          // Step 3: Retrieve the property ID
          const propertyId = propertyMasterList[0].property_id;

          // Step 4: Update the mobile number in the owner details with the mobile number from the GIS data
                    await this.property_Owner_DetailsRepository.update(
            {
              property: { property_id: propertyId },
            },
            { mobile_number: gisData.mobile },
          );
        } else {
          console.log(
            `No matching property found for new_property_no: ${gisData.new_property_no}`,
          );
        }
      }

      return {
        message: 'Mobile numbers updated successfully where matches were found',
      };
    } catch (error) {
      throw error;
    }
  }

  //  oldd
  // async generatePdfFromImportProperty(wardNumber: string): Promise<Buffer> {
  //   try {
  //     const importProperties = await this.importPropertyMasterRepository.find({
  //       where: {
  //         ward_name: wardNumber,

  //       },
  //       order: {
  //         sr_no: 'ASC',
  //       },
  //     });
  //     const results = [];

  //     for (let i = 0; i < 100; i++) {
  //       const currentRow = importProperties[i];

  //       if (!currentRow.sr_no_excel) {
  //         continue;
  //       } else {
  //         let nextRow = importProperties[i + 1];
  //         if (nextRow && !nextRow.sr_no_excel) {
  //           let j = i + 1;
  //           let accumulatedCapitalValue = 0;
  //           let accumulatedAnnualTax = 0;

  //           while (j < importProperties.length && !importProperties[j].sr_no_excel) {
  //             // Accumulate the "capital_value" and round to nearest integer
  //             accumulatedCapitalValue += importProperties[j].capital_value
  //               ? Math.round(parseFloat(importProperties[j].capital_value))
  //               : 0;

  //             accumulatedAnnualTax += importProperties[j].emarat_kar
  //               ? Math.round(parseFloat(importProperties[j].emarat_kar))
  //               : 0;

  //             j++; // Move to the next row
  //           }

  //           currentRow.capital_value = currentRow.capital_value
  //           ? (Math.round(
  //               parseFloat(currentRow.capital_value) + accumulatedCapitalValue
  //             )).toString()  // Convert back to string
  //           : accumulatedCapitalValue.toString();  // Convert back to string

  //         currentRow.emarat_kar = currentRow.emarat_kar
  //           ? (Math.round(
  //               parseFloat(currentRow.emarat_kar) + accumulatedAnnualTax
  //             )).toString()  // Convert back to string
  //           : accumulatedAnnualTax.toString();

  //           i = j - 1; // Move the outer loop index to skip the empty "sr_no_excel" rows
  //         }

  //         results.push(currentRow);
  //       }
  //     }

  //     const batchSize = 100; // Process 100 rows at a time
  //   const totalRows = results.length;
  //   const zip = new JSZip();
  //   const page = await this.browser.newPage(); // Create only one page instance

  //     for (let batchStart = 0; batchStart < totalRows; batchStart += batchSize) {
  //       const batch = results.slice(batchStart, batchStart + batchSize);

  //       for (const [index, property] of batch.entries()) {
  //         const pdfBuffer = await this.generatePdfForProperty(property, page, batchStart + index + 1, wardNumber);
  //         zip.file(`pdf_${batchStart + index + 1}.pdf`, pdfBuffer);
  //       }
  //     }

  //     await page.close(); // Close the page once processing is done
  //     return zip.generateAsync({ type: 'nodebuffer' });
  //   } catch (error) {
  //     console.error('Error in generating PDF:', error);
  //     throw new InternalServerErrorException(
  //       error.message || 'Internal Server Error',
  //     );
  //   }
  // }
  // async generatePdfForProperty(
  //   importProperties: any, // Data fetched from your repository
  //   page: Page,  // Reuse the page instance
  //   index: number,
  //   ward: string,
  // ): Promise<Buffer> {
  //   const templatePath = path.join(__dirname, 'templates', 'generate_pdf.ejs');
  //   const templateContent = await fs.promises.readFile(templatePath, 'utf8');

  //   const templateData = {
  //     zone_name: importProperties.zoneName,
  //     ward_name: ward,
  //     property_holder_name: importProperties.owner_name ,
  //     tenant_name: importProperties.bhogawat_owner_name ,
  //     property_tax: Math.round(parseFloat(importProperties.emarat_kar )),
  //     new_property_number: importProperties.propertyNumber ,
  //     old_property_number: importProperties.old_propertyNumber ,
  //     capital_value: Math.round(parseFloat(importProperties.capital_value )),
  //     address: importProperties.street_name ,
  //     mobileNumber: importProperties.mobile && /^\d{10}$/.test(importProperties.mobile)
  //       ? importProperties.mobile
  //       : null,  // Validate mobile number, if not valid return `null`
  //     currentDate: new Date().toLocaleDateString('mr-IN', {
  //       year: 'numeric',
  //       month: 'long',
  //       day: 'numeric',
  //     }),  // Format the date for the template
  //     formattedNoticeNumber: `${ward}/${importProperties.sr_no_excel || 'N/A'}`,  // Format the notice number
  //   };

  //   const renderedHtml = ejs.render(templateContent, templateData);

  //   await page.setContent(renderedHtml, { waitUntil: 'networkidle0' });

  //   const pdfBuffer = await page.pdf({
  //     format: 'A4',
  //     printBackground: true,  // Ensures that background colors/images are included
  //   });

  //   return pdfBuffer;
  // }
}
