import { Injectable, NotFoundException } from '@nestjs/common';
import {
  BookNumberMasterRepository,
  CollectorMasterRepository,
  LogsRepository,
  WardMasterRepository,
} from 'libs/database/repositories';
import { api, log_sub_type, log_type } from 'src/utils/constants';
import { CollectorMaster } from 'libs/database/entities';
import {
  CreateCollectorDto,
  UpdateCollectorDto,
} from './dtos/collector_master.dto';
import { In } from 'typeorm';

@Injectable()
export class CollectorMasterService {
  constructor(
    private readonly collectorRepo: CollectorMasterRepository,
    private readonly bookRepo: BookNumberMasterRepository,
    private readonly wardRepo: WardMasterRepository,
    private readonly logsRepository: LogsRepository,
  ) {}

  async getAllCollectors() {
    return {
      message: 'successfully fetched all collectors',
      data: await this.collectorRepo.getAll(),
    };
  }

  async getCollectorById(collectorId: string) {
    return {
      message: 'successfully fetched collector',
      data: await this.collectorRepo.getById(collectorId),
    };
  }

  async getCollectorByWard(wardId: string) {
    return {
      message: 'successfully fetched collectors alongisde its wards',
      data: await this.collectorRepo.getByWard(wardId),
    };
  }

  async createCollector(collectorData: CreateCollectorDto) {
    const currentFile = 'collector_master.service.ts';
    const currentApi = '/api/v1/collector-master';
    try {
      const data = await this.collectorRepo.createCollector(collectorData);
      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.SAVE,
        file: currentFile,
        api: currentApi,
        message: 'Collector created successfully',
        data: { collectorId: data.collectorId },
        user_id: null, // User ID should be passed from the controller if available
      });
      return {
        message: 'created collector',
        data,
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error creating collector',
        data: { error: error.message, stack: error.stack },
        user_id: null,
      });
      throw error;
    }
  }

  async updateCollector(collectorId: string, updateData: UpdateCollectorDto) {
    const currentFile = 'collector_master.service.ts';
    const currentApi = '/api/v1/collector-master/:collectorId';
    try {
      const books = await this.bookRepo.findMany(updateData.bookIds);

      const collector = await this.collectorRepo.updateCollector(
        collectorId,
        updateData,
        books,
      );

      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.UPDATE,
        file: currentFile,
        api: currentApi,
        message: 'Collector updated successfully',
        data: { collectorId, changes: updateData },
        user_id: null, // User ID should be passed from the controller if available
      });

      return {
        message: 'Collector update successfully',
        data: collector,
      };
    } catch (e) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error updating collector',
        data: { error: e.message, stack: e.stack },
        user_id: null,
      });
      throw e;
    }
  }

  async deleteCollector(
    collectorId: string,
  ): Promise<{ message: string; success: boolean }> {
    return {
      message: 'Deleted Collector }',
      success: await this.collectorRepo.deleteCollector(collectorId),
    };
  }

  async updateBooksForCollector(collectorId: string, bookIds: string[]) {
    const collector = await this.collectorRepo.findOne({
      where: { collectorId },
      relations: ['books'],
    });

    if (!collector) {
      throw new NotFoundException(`Collector with ID ${collectorId} not found`);
    }

    const books = await this.bookRepo.findByIds(bookIds);

    if (books.length !== bookIds.length) {
      throw new NotFoundException(`Some book IDs were not found`);
    }

    collector.books = books;
    return await this.collectorRepo.save(collector);
  }

  async assignWardToCollector(collectorId: string, wardId: string) {
    const currentFile = 'collector_master.service.ts';
    const currentApi = '/api/v1/collector-master/:collectorId/assign-ward/:wardId';
    try {
      const collector = await this.collectorRepo.findOne({
        where: { collectorId },
        relations: ['ward'],
      });

      if (!collector) {
        throw new NotFoundException('Collector not found');
      }

      const ward = await this.wardRepo.findOne({ where: { ward_id: wardId } });

      if (!ward) {
        throw new NotFoundException('Ward not found');
      }

      collector.ward = ward;
      await this.collectorRepo.save(collector);

      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.UPDATE,
        file: currentFile,
        api: currentApi,
        message: 'Ward assigned to collector successfully',
        data: { collectorId, wardId },
        user_id: null, // User ID should be passed from the controller if available
      });

      return {
        message: 'assigned ward to collector',
        data: collector,
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error assigning ward to collector',
        data: { error: error.message, stack: error.stack },
        user_id: null,
      });
      throw error;
    }
  }
}
