import { Injectable } from '@nestjs/common';
import { BookNumberMasterEntity } from 'libs/database/entities';
import { BookNumberMasterRepository, ReceiptStatus } from 'libs/database/repositories';

@Injectable()
export class BookNumberMasterService {

    constructor(
        private readonly bookNumberMasterRepo: BookNumberMasterRepository
    ) {}

    async bookExist(bookNumber: number): Promise<boolean> {
        try {
            const book = await this.bookNumberMasterRepo.findOneBook(bookNumber);
            return !!book;
        } catch (error) {
            throw error;
        }
    }


    async getAll(): Promise<any> {
        try {
            const book = await this.bookNumberMasterRepo.findAll();
            return { message: "All book number Number fetched Successfully", data: book };
        } catch (error) {
            throw error;
        }
    }

    async createBookNumber(bookNumber: number): Promise<Partial<{ message: string; data?: BookNumberMasterEntity }>> {
        try {
            if (await this.bookExist(bookNumber)) {
                return { message: "Book Number Already taken" };
            }
let ChangedbookNumber=bookNumber-1;
            const availableReceipts = new Set<number>();
            for (let i = ChangedbookNumber * 100 + 1; i <= ChangedbookNumber * 100 + 100; i++) {
                availableReceipts.add(i);
            }

            const book = await this.bookNumberMasterRepo.createBookNumber(bookNumber, availableReceipts);
            return { message: "Book Number Created Successfully", data: book };
        } catch (error) {
            throw error;
        }
    }

    async getAvailableReceipts(bookNumber: number):Promise<Partial<{ message: string; data?: number[] }>> {
        try {
            const ar = await this.bookNumberMasterRepo.getAvailableReceipts(bookNumber);
                        return {
                message : "available receipts",
                data: Array.from(ar)
            }
        } catch (error) {
            throw error;
        }
    }

    async getReceiptsInUse(bookNumber: number): Promise<Partial<{ message: string; data?: number[] }>> {
        try {
            const ar = await this.bookNumberMasterRepo.getReceiptsInUse(bookNumber);
                        return {
                message : "available receipts",
                data: Array.from(ar)
            }        
        } catch (error) {
            throw error;
        }
    }

    async updateReceiptStatus(receiptNumber: number, status: ReceiptStatus): Promise<{ message: string }> {
        try {
            const bookNumber =  Math.floor(receiptNumber/1000) ;
            await this.bookNumberMasterRepo.updateReceipts(bookNumber, receiptNumber, status);
            return { message: `Receipt ${receiptNumber} has been marked as ${status}` };
        } catch (error) {
            throw error;
        }
    }
    async deleteBookNumber(bookNumber: number): Promise<any> {

        try{
        // Logic to delete the book number from the database
        await this.bookNumberMasterRepo.deleteByNumber(bookNumber);
        return { message: `Deleted successfully` };


    }
    catch(err){
        return { message: `Already in use` };

    }
}
}
