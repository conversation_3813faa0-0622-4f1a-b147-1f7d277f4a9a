import * as React from "react";
import { ReactTransliterate } from "react-transliterate";
import "react-transliterate/dist/index.css";
import { cn } from "@/lib/utils";

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  transliterate?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, transliterate = true, disabled, ...props }, ref) => {
    const [inputValue, setInputValue] = React.useState<string>(
      (props.value !== undefined && props.value !== null) ? props.value.toString() : ""
    );
    const [lastCharWasSpace, setLastCharWasSpace] = React.useState<boolean>(false);

    const inputType = typeof props.value === "number" ? "number" : type;

    const disabledClasses = "font-semibold cursor-not-allowed opacity-50 border-gray-300 bg-gray-100 focus-visible:ring-0 focus-visible:ring-offset-0 focus:none bg-[#f6f7f8]";
    const defaultClass = disabled ? disabledClasses : "";
    const [isEnabled, setIsEnabled] = React.useState(true);

    const handleChangeText = (text: string) => {
      if (disabled) return;
      if (!lastCharWasSpace && text.endsWith(" ")) {
        text = text.slice(0, -1);
      }
      setInputValue(text);

      if (props.onChange) {
        props.onChange({
          ...props,
          target: {
            ...props,
            value: text,
          },
        } as any);
      }
    };

    React.useEffect(() => {
      const trimmedValue =
        props.value !== undefined ? String(props.value).trim() : "";
      if (trimmedValue !== inputValue) {
        setInputValue(trimmedValue);
      }
    }, [props.value]);

    React.useEffect(() => {
      setIsEnabled(inputValue.length > 0);
    }, [inputValue]);

    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === " ") {
        setLastCharWasSpace(true);
      } else if (event.key === "Backspace") {
        // Handle backspace if needed
      } else if (props.onKeyDown) {
        props.onKeyDown(event);
      } else {
        setLastCharWasSpace(false);
      }
    };

    // Debugging logs
    React.useEffect(() => {
      // console.log("Input Value:", inputValue);
      // console.log("Props Value:", props.value);
    }, [inputValue, props.value]);

    return transliterate ? (
      <ReactTransliterate
        type={inputType}
        enabled={isEnabled}
        containerClassName="w-full"
        className={cn(
          "flex h-10 w-full rounded-md border-[1.5px] border-[#0000006b] bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-semibold placeholder:text-[#94A3B7] focus-visible:ring-1 focus-visible:ring-GraphiteSec focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-20 devanagari-text",
          defaultClass,
          className,
        )}
        ref={ref}
        {...props}
        onChangeText={handleChangeText}
        value={inputValue}
        lang="mr"
        onKeyDown={handleKeyDown}
        insertCurrentSelectionOnBlur={true}
        // suggestions prop removed as it is not supported by ReactTransliterate
      />
    ) : (
      <input
        type={inputType}
        className={cn(
          "flex h-10 w-full rounded-md border-[1.5px] border-[#0000006b] bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-[#94A3B7] focus-visible:ring-1 focus-visible:ring-GraphiteSec focus-visible:ring-offset-1 devanagari-text",
          defaultClass,
          className,
          "react-transliterate-input"
        )}
        ref={ref}
        {...props}
      />
    );
  }
);

Input.displayName = "Input";

export { Input };
