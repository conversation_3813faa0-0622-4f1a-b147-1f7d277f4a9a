import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ElectionBoundarySendApiObj,
  ElectionBoundaryMasterObject,
  ElectionBoundaryCreateApi,
} from "@/model/electionBoundry";
import Api from "@/services/ApiServices";
import { useToast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Loader2 } from "lucide-react";
import { useElectionBoundaryController } from "@/controller/master/ElectionBoundryController";

interface ElectionBoundaryMasterInterface {
  btnTitle: string;
  editData?: ElectionBoundaryMasterObject;
}

const ElectionBoundaryMasterPopupForm = ({
  btnTitle,
  editData,
}: ElectionBoundaryMasterInterface) => {
  const { t } = useTranslation();
  const schema = z.object({
    boundaryName: z.string().trim().min(1, t("errorsRequiredField")),
  });

  const dynamicValues = {
    name: t("electionboundry.boundaryLabel"),
  };
  const { updateElectionBoundary, createElectionBoundary } =
    useElectionBoundaryController();

  const { toast } = useToast();
  const { setOpen, refreshBoundaryList, setRefreshBoundaryList } =
    useContext(GlobalContext);
  const [loader, setLoader] = useState(false);
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      boundaryName: editData?.electionBoundaryName || "",
    },
  });
  const {
    formState: { errors },
    reset,
  } = form;

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();

    const DataResponse: any = {
      electionBoundaryName: data?.boundaryName,
    };
    if (
      editData?.electionBoundary_id !== undefined &&
      editData?.electionBoundary_id !== null
    ) {
      setLoader(true);
      updateElectionBoundary(
        {
          electionBoundaryId: editData.electionBoundary_id,
          electionBoundryData: DataResponse,
        },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            form.reset({
              boundaryName: "",
            });
            setRefreshBoundaryList(!refreshBoundaryList);
            setLoader(false);
          },
          onError: (error: any) => {
            toast({
              title: error.message,
              variant: "destructive",
            });
            setLoader(false);
          },
        },
      );
    } else {
      setLoader(true);
      createElectionBoundary(DataResponse, {
        onSuccess: () => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          form.reset({
            boundaryName: "",
          });
          setLoader(false);
        },
        onError: (error: any) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });
    }
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        boundaryName: editData.electionBoundaryName || "",
      });
    } else {
      form.reset({
        boundaryName: "",
      });
    }
  }, [editData]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full md:w-full"
        >
          <div className="form-flex">
            <div className="form-element ">
              <FormField
                control={form.control}
                name="boundaryName"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("electionboundry.boundaryLabel")}</FormLabel>
                    <FormControl>
                      <Input
                        className=" mt-1"
                        placeholder={t("electionboundry.boundaryLabel")}
                        {...field}
                      />
                    </FormControl>
                    {errors.boundaryName && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="w-full flex justify-end mt-4">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
        </form>
      </Form>
    </>
  );
};

export default ElectionBoundaryMasterPopupForm;
