export interface UserRegisterObj {
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  role: string;
}

export interface UserRegisterListObj {
  data(data: any): unknown;
  statusCode: number;
  user_id?: number;
  firstname?: string;
  lastname?: string;
  email?: string;
  isActive?: boolean;
  address?: string;
  profilePic?: string;
  lastLoginAt?: string;
  role?: any;
}

export interface UserUpdateObj {
  // phone_number: string;
  address?: string;
  email?: string;
  firstname?: string;
  lastname?: string;
  mobileNumber?: number;
  role?: string;
  userId?: number;
}

export interface UserRegisterUpdateApiResponse {
  statusCode: number;
  message: string;
  data: UserRegisterObj[];
}
