export interface Property {
    propertyNumber: string;
}

export interface Receipts {
    receipt_id: string;
    book_number: string;
}

export interface Payment {
  
    payment_id: string;
    amount: string;
    payment_mode: string;
    payment_status: string;
    payment_date: string;
    property: Property;
    receipts: Receipts;
}
export interface PaymentInterface {
    book_number: string;
  }
export interface paymentList{
    statusCode: number;
    message: string;
    data: Payment[]
}
