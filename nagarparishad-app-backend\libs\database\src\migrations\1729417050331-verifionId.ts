import { MigrationInterface, QueryRunner } from "typeorm";

export class VerifionId1729417050331 implements MigrationInterface {
    name = 'VerifionId1729417050331'

    public async up(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE "user_otp" ADD "verificationId" character varying`);
 }

    public async down(queryRunner: QueryRunner): Promise<void> {
      await queryRunner.query(`ALTER TABLE "user_otp" DROP COLUMN "verificationId"`);
       }

}
