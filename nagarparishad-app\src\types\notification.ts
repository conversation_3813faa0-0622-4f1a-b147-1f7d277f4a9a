export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'progress';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'delivered';
  progress?: number;
  downloadUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  persistent: boolean;
  isRead?: boolean; // Added isRead property
  metadata?: {
    reportType?: 'namuna_eight' | 'namuna_nine';
    totalRecords?: number;
    processedRecords?: number;
    fileName?: string;
    fileSize?: number;
  };
}

export interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'updatedAt'>) => string;
  updateNotification: (id: string, updates: Partial<Notification>) => void;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  downloadNotificationFile: (notificationId: string) => Promise<{ success: boolean; message: string }>;
  connectToSSE: (token:string) => void;
  disconnectSSE: () => void;
}

export interface SSEMessage {
  type: 'notification_update' | 'progress_update' | 'completion' | 'error';
  notificationId: string;
  data: {
    status?: Notification['status'];
    progress?: number;
    message?: string;
    downloadUrl?: string;
    metadata?: Notification['metadata'];
  };
}
