import { Repository } from 'typeorm';
import { PropertyEntity, Tax_PropertyWiseEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

import { Logger } from '@nestjs/common';

export class Tax_PropertyWiseRepository extends Repository<Tax_PropertyWiseEntity> {
  constructor(
    @InjectRepository(Tax_PropertyWiseEntity)
    private readonly tax_PropertyWiseRepository: Repository<Tax_PropertyWiseEntity>,
  ) {
    super(
      tax_PropertyWiseRepository.target,
      tax_PropertyWiseRepository.manager,
      tax_PropertyWiseRepository.queryRunner,
    );
  }

 

 

  async findAllData() {
    return await this.tax_PropertyWiseRepository
      .createQueryBuilder('tax_propertywise')
      .select([
        'tax_propertywise.tax_propertywise_id',
        'property_master.old_propertyNumber',
        'property_master.firstname',
        'property_master.lastname',
        // 'property_master.mobile_number',
        // 'property_master.propertyNumber',
       
         'zone.zoneName',
        // 'tax_propertywise.financial_year',
        // 'tax_propertywise.tax',
       
      ])
      .leftJoin('tax_propertywise.propertyMaster', 'property_master')
      .leftJoin('property_master.zone', 'zone')      
      .orderBy('tax_propertywise.updated_at', 'DESC')
      .getMany();
  }


  // async get_data_for_nanuma8(bill_no: string) {
  //   try {
  //     return await this.tax_PropertyWiseRepository
  //       .createQueryBuilder('tax_propertywise')
  //       .select([
  //         'tax_propertywise.tax_propertywise_id',
  //         'tax_propertywise.rr_rate',
  //         'tax_propertywise.sq_ft_meter',
  //         'tax_propertywise.rr_rate',
  //         'tax_property.bill_no',
  //         'property_type_master.propertyType',        
  //         'property_Type.are_sq_ft',    
  //         'property_master.firstname',
  //         'property_master.lastname'
  //       ])
  //       .addSelect("CONCAT(property_master.firstname, ' ', property_master.lastname)", 'name')
  //       .leftJoin('tax_propertywise.tax_Property', 'tax_property')
  //       .leftJoin('tax_property.propertyMaster', 'property_master') 
  //       .leftJoin('tax_propertywise.property_Type', 'property_Type')
  //       .leftJoin('property_Type.propertyTypeMasterEntity', 'property_type_master')
  //       .where('tax_propertywise.bill_no = :bill_no', { bill_no })      
  //       .orderBy('tax_propertywise.updated_at', 'DESC')
  //       .getMany();
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  

}
