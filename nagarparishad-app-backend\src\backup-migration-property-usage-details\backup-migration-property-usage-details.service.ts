import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BackupPropertyUsageDetailsEntity, DeletedPropertyUsageEntity } from 'libs/database/entities';
import { DeletedPropertyUsageRepository, BackupPropertyUsageDetailsRepository } from 'libs/database/repositories';

@Injectable()
export class BackupMigrationPropertyUsageDetailsService {
  constructor(
    private readonly backupPropertyUsageRepo: BackupPropertyUsageDetailsRepository,
    private readonly deletedPropertyUsageRepo: DeletedPropertyUsageRepository,
  ) {}

  async migrateAndDelete(): Promise<BackupPropertyUsageDetailsEntity[]> {
    // Fetch all deleted property usage records
    const deletedRecords = await this.deletedPropertyUsageRepo.findAll();

    if (!deletedRecords.length) {
      throw new Error('No deleted property usage records found to migrate.');
    }

    // Map deleted records to backup entities
    const backupRecords = deletedRecords.map(deletedRecord => ({
      property_id: deletedRecord.property.property_id,
      property_usage_details_id: deletedRecord.property_usage.property_usage_details_id,
      property_usage_details: deletedRecord.property_usage, // Storing as JSONB
    }));

    // Save all records in the backup table
    const savedRecords = await this.backupPropertyUsageRepo.save(backupRecords);

    // Delete records after successful backup
    await this.deletedPropertyUsageRepo.deleteAll();

    return savedRecords;
  }
}