import { Repository } from 'typeorm';
import { ConstructionClassEnitity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class ConstructionClassRepository extends Repository<ConstructionClassEnitity> {
  constructor(
    @InjectRepository(ConstructionClassEnitity)
    private readonly constructionClassRepository: Repository<ConstructionClassEnitity>,
  ) {
    super(
      constructionClassRepository.target,
      constructionClassRepository.manager,
      constructionClassRepository.queryRunner,
    );
  }

  async saveData(input: {
    constructionClassName: string;
    constructionClassMarathi: string;
    values: string;
  }): Promise<ConstructionClassEnitity> {
    let data = this.constructionClassRepository.create(input);
    data = await this.constructionClassRepository.save(data);
    return data;
  }

  async findAllLocation() {
    return await this.constructionClassRepository
      .createQueryBuilder('construction_class')
      .orderBy('construction_class.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string) {
    return await this.constructionClassRepository
      .createQueryBuilder('construction_class')
      .where(
        'construction_class.constructionClass_id = :constructionClass_id',
        {
          constructionClass_id: id,
        },
      )
      .getOne();
  }

  async updateData(
    id: string,
    input: {
      constructionClassName?: string;
      constructionClassMarathi?: string;
      values?: string;
    },
  ) {
    return await this.constructionClassRepository
      .createQueryBuilder('construction_class')
      .update(ConstructionClassEnitity)
      .set(input)
      .where('constructionClass_id = :constructionClass_id', {
        constructionClass_id: id,
      })
      .execute();
  }

  async deleteData(id: string) {
    return await this.constructionClassRepository
      .createQueryBuilder('construction_class')
      .softDelete()
      .where('constructionClass_id = :constructionClass_id', {
        constructionClass_id: id,
      })
      .execute();
  }
}
