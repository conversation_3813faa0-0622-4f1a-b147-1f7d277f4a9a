import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  FormMasterRepository,
  ModuleMasterRepository,
  RoleMasterRepository,
  RolewiseFormPermissionRepository,
} from 'libs/database/repositories';
import {
  CreatePermissionDto,
  FormIdDto,
  PermissionIdDto,
  RoleIdDto,
  UpdatePermissionDto,
} from './dto/permission.dto';
import { PermissionHelper } from './permission.helper';

@Injectable()
export class PermissionService {
  constructor(
    private readonly permissionRepository: RolewiseFormPermissionRepository,
    private readonly roleRepository: RoleMasterRepository,
    private readonly formRepository: FormMasterRepository,
    private readonly permissionHelper: PermissionHelper,
    private readonly moduleMasterRepository: ModuleMasterRepository,

  ) {}
  async create(input: CreatePermissionDto) {
    try {
      const { role, forms } = input;

      //Check Form Exists
      const checkForm = await this.roleRepository.findById(role);
      if (!checkForm) {
        throw new NotFoundException('Form does not exist');
      }

      //Map the role Ids
      const formIds = forms.map((form) => form.form);

      //Check Roles Exists
      const formExists = await Promise.all(
        formIds.map(async (formId) => {
          const form = await this.formRepository.findById(formId);
          return !!form;
        }),
      );

      //Throw error if one or more roles do not exist
      if (formExists.includes(false)) {
        throw new BadRequestException('One or more roles do not exist');
      }

      const data = forms.map((form) => ({
        ...form,
        role,
      }));

      //Save the datas
      await Promise.all(
        data.map(async (permission) => {
          await this.permissionRepository.saveData(permission);
        }),
      );

      return {
        message: 'Permission created successfully',
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll(roleIdDto: RoleIdDto) {
    try {
      const { role } = roleIdDto;

      const moduleData = await this.moduleMasterRepository.getAll();

      if (!moduleData || moduleData.length === 0) {
        throw new NotFoundException('Data Not Found');
      }
      const permissionsData = await this.permissionRepository.findAllData(role);

      if (!permissionsData || permissionsData.length == 0) {
        throw new NotFoundException('Data Not Found');
      }

      const formattedData = await this.permissionHelper.formatData(
        permissionsData,
        moduleData,
      );

      return {
        message: 'Records Fetched Success',
        data: formattedData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(permissionId: PermissionIdDto) {
    try {
      const { action_id } = permissionId;
      const getData = await this.permissionRepository.findById(action_id);

      if (!getData) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getData,
      };
    } catch (error) {
      throw error;
    }
  }

  async getFormPermission(formIdDto: FormIdDto) {
    try {
      const { form } = formIdDto;
      const getPermissions =
        await this.permissionRepository.getPermissions(form);

      if (!getPermissions || getPermissions.length === 0) {
        throw new NotFoundException('No Permissions found');
      }

      return {
        message: 'Records Fetched Success',
        data: getPermissions,
      };
    } catch (error) {
      throw error;
    }
  }

  async updatePermission(update: UpdatePermissionDto) {
    try {
      const updatePromises = update.forms.map((form) =>
        this.permissionRepository.updateData(form.action_id, {
          can_read: form.can_read,
          can_write: form.can_write,
          can_update: form.can_update,
          can_delete: form.can_delete,
          is_valid: form.is_valid,
        }),
      );

      await Promise.all(updatePromises);

      return {
        message: 'Permission Updated SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }
  async createModulesWithFormsAndPermissions() {
    const modulesData = [
      {
        moduleName: 'Setting',
        forms: [
          'Deprecation Rate',
          'RR Construction Rate',
          'RR Rate',
          'Tax Rate',
          'Weighting Rate',
          'Ghan Kachra Rate',
          'Book Master',
          'Financial Year Master',
          'Reassessment Master',
        ],
      },
      {
        moduleName: 'Master',
        forms: [
          'Zone',
          'Ward',
          'Road',
          'PropertyType',
          'PropertyType Department',
          'Floor',
          'Usage',
          'Usage Sub',
          'Location Master',
          'Street Master',
          'Election Boundry Master',
          'Area Master',
          'Property Sub Type Master',
          'Property Class Master',
          'Construction Class Master',
          'Collector Master',
        ],
      },
      {
        moduleName: 'Property',
        forms: ['Property'],
      },
      {
        moduleName: 'Payment',
        forms: ['Payment Bill', 'Payment Logs'],
      },
      {
        moduleName: 'Register',
        forms: ['Assessment Report', 'Register Report'],
      },
      {
        moduleName: 'Role and User Mangement',
        forms: ['Role', 'User','Collector Module' ],
      },
    ];

    const rolesData = [
      { roleName: 'Admin', permissions: { can_read: true, can_write: true, can_update: true, can_delete: true } },
      { roleName: 'Supervisor', permissions: { can_read: true, can_write: true, can_update: true, can_delete: false } },
    ];

    for (const moduleData of modulesData) {
      let module = await this.moduleMasterRepository.findOne({ where: { moduleName: moduleData.moduleName } });

      if (!module) {
        module = this.moduleMasterRepository.create({ moduleName: moduleData.moduleName, createdBy: 'System' });
        module = await this.moduleMasterRepository.save(module);
      }

      for (const formName of moduleData.forms) {
        let form = await this.formRepository.findOne({ where: { formName, module } });

        if (!form) {
          form = this.formRepository.create({ formName, module });
          form = await this.formRepository.save(form);
        }

        for (const roleData of rolesData) {
          let role = await this.roleRepository.findOne({ where: { roleName: roleData.roleName } });

          if (!role) {
            role = this.roleRepository.create({ roleName: roleData.roleName, createdBy: 'System' });
            role = await this.roleRepository.save(role);
          }

          let permission = await this.permissionRepository.findOne({
            where: { role, form },
          });

          if (!permission) {
            permission = this.permissionRepository.create({
              role,
              form,
              ...roleData.permissions,
              is_valid: true,
            });

            await this.permissionRepository.save(permission);
          }
        }
      }
    }

    return { message: 'Modules, Forms, and Role-wise Permissions created successfully!' };
  }
}

