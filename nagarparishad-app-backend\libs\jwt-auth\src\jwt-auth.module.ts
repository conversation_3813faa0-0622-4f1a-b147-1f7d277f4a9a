import { Module } from '@nestjs/common';
import { JwtAuthService } from './jwt-auth.service';
import { DatabaseModule } from 'libs/database';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { AccessTokenStrategy, RefreshTokenStrategy } from './strategies';
import { AccessTokenGuard, RefreshTokenGuard } from './guards';

@Module({
  imports: [JwtModule.register({}), DatabaseModule],
  providers: [
    JwtService,
    JwtAuthService,
    AccessTokenStrategy,
    RefreshTokenStrategy,
    AccessTokenGuard,
    RefreshTokenGuard,
  ],
  exports: [
    JwtService,
    JwtAuthService,
    AccessTokenStrategy,
    RefreshTokenStrategy,
  ],
})
export class JwtAuthModule {}
