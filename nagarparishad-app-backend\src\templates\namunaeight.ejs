<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
      .container {
        max-width: 100rem;
        margin: 40 auto;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
      }
      .header img {
        width: 6rem;
        height: 6rem;
      }
      .header .text-center {
        text-align: center;
      }
      .header h1 {
        font-size: 1.25rem;
        font-weight: bold;
      }
      .header p {
        font-size: 0.875rem;
      }
      .header .bold {
        font-weight: bold;
      }
      .divider {
        height: 2px;
        background-color: black;
        margin-bottom: 1rem;
      }
      .info-section {
        margin-bottom: 1rem;
      }
      .info-section .flex {
        display: flex;
        justify-content: space-between;
      }
      .info-section p {
        margin: 0;
      }
      .info-section .font-semibold {
        font-weight: 600;
      }
      .table-container {
        overflow-x: auto;
      }
      .table {
        min-width: 100%;
        border-collapse: collapse;
        border: 1px solid #71717a;
      }
      .table th,
      .table td {
        border: 1px solid #71717a;
        padding: 0.5rem;
      }

      thead,
      tbody {
        vertical-align: baseline;
      }
      .table th {
        text-align: left;
      }
      .footer {
        margin-bottom: 1rem;
      }
      .flex {
        display: flex;
      }
      .border-zinc-400 {
        border-color: #dae1e7;
      }
      .h-2 {
        height: 2px;
      }

      .max-w-4xl {
        max-width: 768px;
      }
      .sign-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: end;
        margin-top: 30px;
        text-align: center;
        padding-right: 30px;
        font-size: 15px;
      }
      .mx-auto {
        margin-left: auto;
        margin-right: auto;
      }

      .p-4 {
        padding: 16px;
      }

      .border-2 {
        border-width: 2px;
      }

      .border-black {
        border-color: black;
      }

      .flex {
        display: flex;
      }

      .justify-between {
        justify-content: space-between;
      }

      .items-center {
        align-items: center;
      }

      .flex-direction-column {
        flex-direction: column;
      }

      .mb-4 {
        margin-bottom: 16px;
      }

      .w-24 {
        width: 96px;
      }

      .h-24 {
        height: 96px;
      }

      .text-center {
        text-align: center;
      }

      .text-xl {
        font-size: 1.25rem;
      }

      .font-bold {
        font-weight: bold;
      }

      .text-sm {
        font-size: 0.875rem;
      }
      .text-xs {
        font-size: 0.675rem;
      }
      .h-2 {
        height: 8px;
      }

      .overflow-x-auto {
        overflow-x: auto;
      }

      .min-w-full {
        min-width: fit-content;
      }

      .border-collapse {
        border-collapse: collapse;
      }

      .border {
        border: 1px solid #525252;
      }

      .border-zinc-400 {
        border-color: #525252;
      }

      .px-4 {
        padding-left: 8px;
        padding-right: 8px;
      }
      .px-1 {
        padding-left: 5px;
        padding-right: 5px;
      }
      .py-2 {
        padding-top: 2px;
        padding-bottom: 2px;
      }

      p {
        margin-top: 3px;
        margin-bottom: 3px;
      }
      .pb-0 {
        padding-bottom: 0;
      }
      .border-red {
        border: 1px solid red;
      }
      .gap15 {
        gap: 10px;
      }
      . {
        border-right: 1px solid black;
        margin-left: 1px;
        height: fit-content;
      }
      .th-subtitle {
        width: 20%;
        word-wrap: break-word;
        margin-right: 1px;
      }
      .th-subtitle1 {
        width: 4%;
        word-wrap: break-word;
        margin-right: 1px;
      }
      .th-title-sm {
        width: 35px;
        min-width: 35px;
        max-width: 35px;
        word-wrap: break-word;
        margin-right: 1px;
      }
      .th-title-md {
        width: 99px;
        min-width: 99px;
        max-width: 99px;

        word-wrap: break-word;
        margin-right: 1px;
      }
      .th-title-lg {
        width: 12%;
        word-wrap: break-word;
        margin-right: 1px;
      }
      .th-title-xl {
        width: 230px;
        min-width: 230px;
        max-width: 230px;
        word-wrap: break-word;
        margin-right: 1px;
      }

      .margin-top-20 {
        margin-top: 20px;
      }
      .rotate-number {
        display: flex;
        gap: 10px;
        transform: rotate(90deg);
        word-wrap: break-word;
      }
      .border-left {
        border: 1px solid #525252;
      }
      .border-top {
        border-top: 1px solid #525252;
      }
      .grid-font {
        font-size: 12px;
      }
      div {
        margin-left: 0;
        margin-right: 0 !important;
      }
      .flex-align-justify-center {
        display: flex;
        /* justify-content: center; */
         justify-content: space-around; 
        align-items: center;
      }
      .grid-font-numbers {
        font-size: 12px;
      }
      .th-title-height {
        min-height: 120px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="flex justify-center items-center flex-direction-column">
        <h1 class="text-xl font-bold">नमुना ८ नियम ३२ (१)</h1>
        <p class="text-md font-bold">
          सन. २०१५-२०१६ ते २०२३-२०२४ साठी कर आकाराणी नोंदवही
        </p>
      </div>
      <div class="mb-4 px-1 margin-top-20">
        <div class="flex justify-between font-bold">
          <div>
            <p><span class="font-semibold">नगरपरिषद</span>: नगरपरिषद शिरोळ</p>
          </div>
          <div>
            <p><span class="font-semibold">तालुका</span>: शिरोळ</p>
          </div>
          <div>
            <p>
              <span class="font-semibold">जिल्हा: कोल्हापूर </span>
            </p>
          </div>
          <div>
            <p><span class="font-semibold">वॉर्ड क्रमांक:</span>४</p>
          </div>
        </div>
      </div>
      <div class="min-w-full border-collapse border grid-font">
        <div class="flex justify-center">
          <div class="border-left px-4 py-2 th-title-sm">अ. क्र.</div>
          <div class="border-left px-4 py-2 th-title-sm">
            रस्त्याचे नाव/गल्लीचे नाव
          </div>
          <div class="border-left px-4 py-2 th-title-sm">
            गट क्र. / भूमापन क्र. / न भूमापन क्र.
          </div>
          <div class="border-left px-4 py-2 th-title-sm">मालमत्ता क्रमांक</div>
          <div class="border-left px-4 py-2 th-title-md">
            मालकाचे ( धारण करणाऱ्याचे) नाव
          </div>
          <div class="border-left px-4 py-2 th-title-sm">
            भोगवटा करणाऱ्याचे नाव
          </div>
          <div class="border-left px-4 py-2 th-title-md">मालमत्तेचे वर्णन</div>
          <div class="border-left px-4 py-2 th-title-sm">
            मिळकत बांधकामचे वर्ष
          </div>
          <div class="border-left px-4 py-2 th-title-md">
            क्षेत्रफळ चौ मी / (चौ. फू )
          </div>
          <div
            class="flex flex-direction-column border-left px-4 py-2 th-title-md"
          >
            <div>रेडीरेकणर दर प्रती चौ मी</div>
            <hr />
            <div class="flex-align-justify-center border-top gap15">
              <p class="th-subtitle1 px-4">
                <span class="rotate-number">जमीन </span>
              </p>
              <p class="th-subtitle1 px-4">
                <span class="rotate-number">इमारत</span>
              </p>
              <p class="th-subtitle1 px-4">
                <span class="rotate-number">बांधकाम</span>
              </p>
            </div>
          </div>
          <div class="border-left px-4 py-2 th-title-sm">घसारा दर</div>
          <div class="border-left px-4 py-2 th-title-sm">
            इ. वापरानुसार भारंक
          </div>
          <div class="border-left px-4 py-2 th-title-sm">भांडवली मूल्य</div>
          <div class="border-left px-4 py-2 th-title-sm">कराचा दर</div>
          <div
            class="flex flex-direction-column border-left px-4 py-2 th-title-xl th-title-height"
          >
            <div>वार्षिक कराची रक्कम (रुपयात)</div>
            <hr />
            <div class="flex-align-justify-center border-top gap15">
              <p class="th-subtitle1">
                <span class="rotate-number">इमारतकर </span>
              </p>
             
              <% Bill.tax_property_other_taxes.forEach(function(tax) { %>
                <p class="th-subtitle1">
                  <span class="rotate-number"><%= tax.tax_type %> </span>
                </p>
                <% }); %>
              <p class="th-subtitle1">
                <span class="rotate-number">शास्तीफी </span>
              </p>
              <p class="th-subtitle1">
                <span class="rotate-number">एकूण</span>
              </p>
           
            </div>
          </div>
          <div
            class="flex flex-direction-column border-left px-4 py-2 th-title-xl th-title-height"
          >
            अपीलाचे निकाल व त्यावर केलेले फेरफार

            <hr />
            <div class="flex-align-justify-center border-top gap15">
              <p class="th-subtitle1">
                <span class="rotate-number">इमारतकर </span>
              </p>
              <% Bill.tax_property_other_taxes.forEach(function(tax) { %>
              <p class="th-subtitle1">
                <span class="rotate-number"><%= tax.tax_type %> </span>
              </p>
              <% }); %>
              <p class="th-subtitle1">
                <span class="rotate-number">शास्तीफी </span>
              </p>
              <p class="th-subtitle1">
                <span class="rotate-number">एकूण</span>
              </p>
            </div>
          </div>
          <div class="border-left px-4 py-2 th-title-md">
            नंतर वाढ किंवा घट झालेल्या बाबतीत आदेशाच्या संदर्भात शेरा
          </div>
        </div>
      </div>
    <!--   <div class="min-w-full border-collapse border grid-font-numbers">
        <div class="flex justify-center">
          <div
            class="border-left px-4 py-2 th-title-sm flex-align-justify-center"
          >
            1
          </div>
          <div
            class="border-left px-4 py-2 th-title-sm flex-align-justify-center"
          >
            2
          </div>
          <div
            class="border-left px-4 py-2 th-title-sm flex-align-justify-center"
          >
            3
          </div>
          <div
            class="border-left px-4 py-2 th-title-sm flex-align-justify-center"
          >
            4
          </div>
          <div
            class="border-left px-4 py-2 th-title-md flex-align-justify-center"
          >
            5
          </div>
          <div
            class="border-left px-4 py-2 th-title-sm flex-align-justify-center"
          >
            6
          </div>
          <div
            class="border-left px-4 py-2 th-title-md flex-align-justify-center"
          >
            7
          </div>

          <div
            class="border-left px-4 py-2 th-title-sm flex-align-justify-center"
          >
            8
          </div>
          <div
            class="border-left px-4 py-2 th-title-md flex-align-justify-center"
          >
            9
          </div>
          <div
            class="flex flex-direction-column border-left px-4 py-2 th-title-md"
          >
            <div class="flex-align-justify-center">
              <p class="px-1">10</p>
              <p class="px-1">11</p>
              <p class="px-1">12</p>
            </div>
          </div>
          <div
            class="border-left px-4 py-2 th-title-sm flex-align-justify-center"
          >
            13
          </div>
          <div
            class="border-left px-4 py-2 th-title-sm flex-align-justify-center"
          >
            14
          </div>
          <div
            class="border-left px-4 py-2 th-title-sm flex-align-justify-center"
          >
            15
          </div>
          <div
            class="border-left px-4 py-2 th-title-sm flex-align-justify-center"
          >
            16
          </div>
          <div
            class="flex flex-direction-column border-left px-4 py-2 th-title-xl"
          >
            <div class="flex-align-justify-center">
              <p class="px-1">17</p>
              <p class="px-1">18</p>
              <p class="px-1">19</p>
              <p class="px-1">20</p>
              <p class="px-1">21</p>
              <p class="px-1">22</p>
              <p class="px-1">23</p>
              <p class="px-1">24</p>
              <p class="px-1">25</p>
              <p class="px-1">26</p>
              <p class="px-1">27</p>
            </div>
          </div>
          <div
            class="flex flex-direction-column border-left px-4 py-2 th-title-xl"
          >
            <div class="flex-align-justify-center">
              <p class="px-1">28</p>
              <p class="px-1">29</p>
              <p class="px-1">30</p>
              <p class="px-1">31</p>
              <p class="px-1">32</p>
              <p class="px-1">33</p>
              <p class="px-1">34</p>
              <p class="px-1">35</p>
              <p class="px-1">36</p>
            </div>
          </div>
          <div
            class="border-left px-4 py-2 th-title-md flex-align-justify-center"
          >
            37
          </div> 
        </div>
      </div>-->
      <% Bill.tax_propertywise.forEach((row, index) => { %>
        <div class="min-w-full border-collapse border grid-font">
          <div class="flex justify-center">
            <div class="border-left px-4 py-2 th-title-sm"><%= index + 1 %></div>
            <div class="border-left px-4 py-2 th-title-sm">
              <% if (index === 0) { %>
              <%= Bill.property.street.street_name %>
              <% } %></div>
            <div class="border-left px-4 py-2 th-title-sm">
                <% if (index === 0) { %>
                <%= Bill.property.gat_no %>
                <% } %></div>
            <div class="border-left px-4 py-2 th-title-sm">
              <% if (index === 0) { %>
                <%= Bill.property.propertyNumber %>
                <% } %></div>
            <div class="border-left px-4 py-2 th-title-md">
              <% Bill.property.property_owner_details.forEach((row, index) => { %>
                <%= Bill.property.property_owner_details[index].name %> ,
                <% }); %></div>
            <div class="border-left px-4 py-2 th-title-sm">
              <% if (index === 0) { %>
                <%= Bill.property.property_owner_details[0].owner_type.owner_type %> 
                <% } %></div>
            <div class="border-left px-4 py-2 th-title-md">
              <div class="flex flex-direction-column">
                <p class="px-4 py-2">
                  <%= Bill.tax_propertywise[index].property_usage_details.propertyType.propertyType %> 
                </p>
                <!-- <p class="px-4 py-2">
                  इतर पक्के घर (दगड विटांचे चुना किंवा सिमेंटचे घर ) पहिला मजला
                  (लांबी '३६' × रुंदी '३९')
                </p>
                <p class="px-4 py-2">पडसर/खुली जागा (लांबी '३६' × रुंदी '३९')</p> -->
              </div>
            </div>
            <div class="border-left th-title-sm px-4 py-2">
              <div class="flex flex-direction-column">
                <p class="px-4 py-2">
                  <span class="rotate-number">  <%= Bill.tax_propertywise[index].property_usage_details.construction_start_year%>   </span>
                </p>
                <!-- <p class="px-4 py-2" style="margin-top: 120px">
                  <span class="rotate-number">1975</span>
                </p>
                <p class="px-4 py-2" style="margin-top: 120px">
                  <span class="rotate-number">-</span>
                </p> -->
              </div>
            </div>
            <div class="border-left px-4 py-2 th-title-md">
              <div class="flex flex-direction-column">
                <p class="px-4 py-2"><%= Bill.tax_propertywise[index].property_usage_details.are_sq_meter%> (<%= Bill.tax_propertywise[index].property_usage_details.are_sq_ft %>  चौ. फू)</p>
                <!-- <p class="px-4 py-2" style="margin-top: 105px">
                  130.44(1404 चौ. फू)877dsf8789777
                </p>
                <p class="px-4 py-2" style="margin-top: 60px">
                  59.46(640 चौ. फू)
                </p> -->
              </div>
            </div>
            <div
              class="flex flex-direction-column border-left px-4 py-2 th-title-md"
            >
              <div class="flex-align-justify-center gap15">
                <p class="th-subtitle1 px-4">
                  <span class="rotate-number"><%= Bill.tax_propertywise[index].rr_rate%> </span>
                </p>
                <p class="th-subtitle1 px-4">
                  <span class="rotate-number"></span>
                </p>
                <p class="th-subtitle1 px-4">
                  <span class="rotate-number"><%= Bill.tax_propertywise[index].rr_construction_rate%></span>
                </p>
              </div>
            </div>
            <div class="border-left px-4 py-2 th-title-sm"><%= Bill.tax_propertywise[index].depreciation_rate%></div>
            <div class="border-left px-4 py-2 th-title-sm"><%= Bill.tax_propertywise[index].weighting%></div>
            <div class="border-left px-4 py-2 th-title-sm">
              <p class="th-subtitle1">
                <span class="rotate-number"><%= Bill.tax_propertywise[index].capital_value%> </span>
              </p>
            </div>
            <div class="border-left px-4 py-2 th-title-sm"><%= Bill.tax_propertywise[index].tax_value%></div>
            <div
              class="flex flex-direction-column border-left px-4 py-2 th-title-xl"
            >
              <div class="flex-align-justify-center gap15">
                <p class="th-subtitle1">
                  <span class="rotate-number"><%= Bill.all_property_tax_sum%> </span>
                </p>
                <% Bill.tax_property_other_taxes.forEach(function(tax) { %>
                <p class="th-subtitle1">
                  <span class="rotate-number"><%= tax.amount %> </span>
                </p>
                <% }); %>
                <p class="th-subtitle1">
                  <span class="rotate-number">0 </span>
                </p>
                <p class="th-subtitle1">
                  <span class="rotate-number"><%= Bill.total_tax%></span>
                </p>
                
              </div>
            </div>
            <div
              class="flex flex-direction-column border-left px-4 py-2 th-title-xl"
            >
              <div class="flex-align-justify-center gap15">
                <p class="th-subtitle1">
                  <span class="rotate-number">0</span>
                </p>
                <% Bill.tax_property_other_taxes.forEach(function(tax) { %>
                  <p class="th-subtitle1">
                    <span class="rotate-number">0</span>
                  </p>
                  <% }); %>
                <p class="th-subtitle1">
                  <span class="rotate-number">0</span>
                </p>
                <p class="th-subtitle1">
                  <span class="rotate-number">0</span>
                </p>
              </div>
            </div>
            <div class="border-left px-4 py-2 th-title-md"><%= Bill.property.note%></div>
           
          </div>
        </div>
        <% }); %>
     
     
    </div>
    <div class="margin-top-20 grid-font">
      <p>
        सदरचा उतारा हा मालकी हक्काचा नसून करआकारणीचा आहे. (त्यावरून) सदरच्या
        उताऱ्यावरून खरेदी विक्रीचा व्यवहार झालेस त्यास नगरपरिषद जबाबदार राहणार
        नाही.
      </p>
      <p class="margin-top-20">येणे प्रमाणे लागू पुरता उतारा असे दिनांक -</p>
    </div>
  </body>
</html>
