import React, { useContext, useRef, useState } from "react";
import WhiteContainer from "@/components/custom/WhiteContainer";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { MasterObjectColumnDef } from "@/model/zone-master";
import { MASTER } from "@/constant/config/api.config";
import { useZoneMasterController } from "@/controller/master/ZoneMasterController";
import { GlobalContext } from "@/context/GlobalContext";
import { ZoneObject } from "../../../model/zone-master";
import { toast } from "@/components/ui/use-toast";
import Api from "@/services/ApiServices";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import ReadyRecknerRateForm from "../../forms/ReadyRecknerRateForm";
import AddNewBtn from "../../globalcomponent/AddNewForm";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

const Readyreckonerratemaster = () => {
  const {
    setOpen,
    setRefreshZoneList,
    refreshZoneList,
    toggleCollapse,
    setMasterComponent,
  } = useContext(GlobalContext);
  const { t } = useTranslation();
  const userRef = useRef(null);

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Master, FormName.RRRate, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Master, FormName.RRRate, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Master, FormName.RRRate, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Master, FormName.RRRate, Action.CanDelete);

  const columns: ColumnDef<MasterObjectColumnDef>[] = [
    {
      accessorKey: "zone",
      header: `${t("SrNo")}`,
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: "zoneName",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {/* {t("zone.zoneName")} */}
            {t("readyreckonerrate.readyreckoner")}{" "}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
     cell: ({ row }: { row: any })  => <div className="capitalize">{"ABC XYZ"}</div>,
    },
    {
      accessorKey: "wardName",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("propertyLocationDetailsForm.ward")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
     cell: ({ row }: { row: any })  => <div className="capitalize">{"C Ward"}</div>,
    },
    {
      accessorKey: "wardName",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("propertyLocationDetailsForm.zone")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
     cell: ({ row }: { row: any })  => <div className="capitalize">{"D Zone"}</div>,
    },
    {
      accessorKey: "wardName",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("readyreckonerrate.surveyNumber")}{" "}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
     cell: ({ row }: { row: any })  => <div className="capitalize">{1111}</div>,
    },
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: `${t("Actions")}`,
            enableHiding: false,
            cell: ({ row }: { row: any }) => (
              <>
                {canUpdate && (
                  <Button
                    variant="submit"
                    className="h-8 w-8 p-0 justify-center"
                    onClick={() => handleEdit(row?.original)}
                  >
                    {t("table.edit")}
                  </Button>
                )}
                {CanDelete && (
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0 justify-center ml-2  !border-BlueText !text-BlueText"
                    onClick={() => handleDelete(row?.original)}
                  >
                    {t("table.delete")}
                  </Button>
                )}
              </>
            ),
          },
        ]
      : []),
  ];

  function handleEdit(item: ZoneObject): void {
    setOpen(true);
    setMasterComponent(
      <ReadyRecknerRateForm
        btnTitle={"readyreckonerrate.updateBtn"}
        editData={item && item}
      />,
    );
    toggleCollapse();
    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<ZoneObject | null>(null);

  function handleDelete(item: ZoneObject): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }
  const handleConfirmDelete = () => {
    if (selectedItem) {
      Api.deleteZone(
        selectedItem.zone_id,
        (response: { status: boolean; data: any }) => {
          if (response.status && response.data.statusCode === 200) {
            toast({
              title: response.data.message,
            });
            setRefreshZoneList(!refreshZoneList);
          } else {
            toast({
              title: response.data.message,
            });
          }
        },
      );
    }

    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  //List API for Zone
  const { zoneList } = useZoneMasterController();
  const MasterType: string = MASTER.ZONE;

  return (
    <>
      <div className="flex h-fit  ">
        <div
          className=" w-full mx-auto px-4 sm:px-6 lg:px-5 py-6  "
          ref={userRef && userRef}
        >
          <p className="w-full flex items-center justify-between ml-2  text-[18px] font-semibold mb-2">
            {t("readyreckonerrate.formTitle")}
          </p>
          {CanCreate && <WhiteContainer>
            {MasterType && <AddNewBtn masterType={MASTER.READYRECKONERRATE} />}
          </WhiteContainer>}
          <WhiteContainer>
            <TanStackTable
              columns={columns}
              data={zoneList}
              masterType={MASTER.READYRECKONERRATE}
              searchKey={"searchReadyRecoknerRate"}
              searchColumn={"zoneName"}
            />
          </WhiteContainer>
        </div>
      </div>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={""}
          onDelete={handleConfirmDelete}
        />
      )}
    </>
  );
};

export default Readyreckonerratemaster;
