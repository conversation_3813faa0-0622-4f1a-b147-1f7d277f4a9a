import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangesInTableColumn1740564574207 implements MigrationInterface {
    name = 'ChangesInTableColumn1740564574207'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "paid_data" RENAME COLUMN "tax_type_10_tax_type_9_" TO "tax_type_10_curr"`);
    //     await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c" UNIQUE ("billNo")`);
    //     await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP NOT NULL`);
    //     await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP DEFAULT`);
    //     await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
    //     await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(50) NOT NULL`);
    //     await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" DROP NOT NULL`);
    //     await queryRunner.query(`ALTER TABLE "receipt" ALTER COLUMN "book_receipt_number" SET NOT NULL`);
    //     await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "FK_df738d1108e8291026728e28c55" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    // }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "FK_df738d1108e8291026728e28c55"`);
        // await queryRunner.query(`ALTER TABLE "receipt" ALTER COLUMN "book_receipt_number" DROP NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(100) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET DEFAULT now()`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c"`);
        await queryRunner.query(`ALTER TABLE "paid_data" RENAME COLUMN "tax_type_10_curr" TO "tax_type_10_tax_type_9_"`);
    }

}
