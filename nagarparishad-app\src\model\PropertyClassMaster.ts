export interface PropertyClassMasterObject {
    property_type_class_id: string;
    property_type_class: string;
    createdAt?: string;
    updatedAt?: string;
    deletedAt?: string | null;
  }
  
  export interface PropertyClassMasterListApiOject {
    property_type_class_id: string;
    property_type_class: string;
  }
  
  export interface PropertyClassMasterListAllApi {
    statusCode: number;
    message: string;
    data: PropertyClassMasterObject[];
  }
  
  export interface PropertyClassCreateApi {
    statusCode: number; //201
    message: string;
    data: PropertyClassMasterObject;
  }
  export interface PropertyClassUpdateApi {
    statusCode: number; //200
    message: string;
  }
  
  export interface PropertyClassSendApiObj {
    property_type_class: string;
  }
  