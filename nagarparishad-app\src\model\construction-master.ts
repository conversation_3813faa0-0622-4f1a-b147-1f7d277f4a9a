export interface ConstructionClassObjectInterface {
  constructionClass_id: string;
  constructionClassName: string;
  constructionClassMarathi: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string | null;
}

export interface ConstructionClassApiResponse {
  statusCode?: number;
  message: string;
  data?: ConstructionClassObjectInterface[] | null;
}

export interface ConstructionClassCreateObject {
  constructionClassName: string;
  constructionClassMarathi: string;
  values: string;
  description?: string | null;
}

export interface ConstructionClassCreateResObject {
  statusCode: number;
  message: string;
}

export interface ConstructionClassUpdateObject {
  constructionClassName?: string;
  constructionClassMarathi?: string;
  values?: string;
  description?: string | null;
}

export interface ConstructionClassUpdateApiResponse {
  statusCode: number;
  message: string;
}

export interface ConstructionClassSelectObjectInterface {
  constructionClass_id: string;
  constructionClassName: string;
}
