import { AdministrativeBoundaryObjectInterface } from "@/model/administrativeboundary-master";
import Api from "../../services/ApiServices";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
//import { ElectionBoundaryMasterObject } from '@/model/electionBoundry';

const fetchAdministrativeBoundry = async () => {
  const response = await Api.getAllAdministrativeBoundaryList();
  return response.data;
};

const createAdministrativeBoundry = async (
  administrativeBoundryData: AdministrativeBoundaryObjectInterface,
) => {
  return new Promise((resolve, reject) => {
    Api.createAdministrativeBoundary(administrativeBoundryData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateAdministrativeBoundry = async ({
  administrativeBoundaryId,
  administrativeBoundaryData,
}: {
  administrativeBoundaryId: any;
  administrativeBoundaryData: any;
}) => {
  return new Promise((resolve, reject) => {
    Api.updateAdministrativeBoundary(
      administrativeBoundaryId,
      administrativeBoundaryData,
      (response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      },
    );
  });
};

const deleteAdministrativeBoundry = async (administrativeBoundaryId: string) => {
  return new Promise((resolve, reject) => {
    Api.deleteAdministrativeBoundary(administrativeBoundaryId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useAdministrativeBoundaryController = () => {
  const queryClient = useQueryClient();

  const { data: administrativeListResponse, error } = useQuery({
    queryKey: ["administrativemaster"],
    queryFn: fetchAdministrativeBoundry,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  if (error) {
    console.error("Error fetching streets:", error);
  }

  const createAdministrativeMutation = useMutation({
    mutationFn: createAdministrativeBoundry,
    onMutate: async (newAdministrativeboundry) => {
      await queryClient.cancelQueries({ queryKey: ["administrativemaster"] });
      const previousAdministrativeBoundry = queryClient.getQueryData([
        "administrativemaster",
      ]);
      queryClient.setQueryData(
        ["administrativemaster"],
        (old: AdministrativeBoundaryObjectInterface) => {
          const updatedData = [newAdministrativeboundry, ...old];
          // Log the updated data
          return updatedData;
        },
      );
      return { previousAdministrativeBoundry };
    },
    onError: (err, newWard, context) => {
      queryClient.setQueryData(
        ["administrativemaster"],
        context.previousAdministrativeBoundry,
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["administrativemaster"] });
    },
  });

  const updateAdminstrativeBoundaryMutation = useMutation({
    mutationFn: updateAdministrativeBoundry,
    onMutate: async ({
      administrativeBoundaryId,
      administrativeBoundaryData,
    }) => {
      await queryClient.cancelQueries({ queryKey: ["administrativemaster"] });

      const previousAdministrativeBoundry = queryClient.getQueryData([
        "administrativemaster",
      ]);

      queryClient.setQueryData(["administrativemaster"], (old) => {
        const updatedAdminstrativeBoundry = old?.map(
          (administrativeboundry: AdministrativeBoundaryObjectInterface) =>
            administrativeboundry?.administrativeBoundary_id ===
            administrativeBoundaryId
              ? { ...administrativeboundry, ...administrativeBoundaryData }
              : administrativeboundry,
        );
        return updatedAdminstrativeBoundry;
      });

      return { previousAdministrativeBoundry };
    },
    onError: (err, context) => {
      queryClient.setQueryData(
        ["administrativemaster"],
        context.previousAdministrativeBoundry,
      );
      console.error("Error updating zone:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["administrativemaster"] });
    },
  });

  const deleteAdministrativeBoundryMutation = useMutation({
    mutationFn: deleteAdministrativeBoundry,
    onMutate: async (administrativeboundryId) => {
      await queryClient.cancelQueries({ queryKey: ["administrativemaster"] });

      const previousElectionBoundry = queryClient.getQueryData([
        "administrativemaster",
      ]);

      queryClient.setQueryData(["administrativemaster"], (old) => {
        const updatedElectionBoundry = old.filter(
          (administrativeboundry: AdministrativeBoundaryObjectInterface) =>
            administrativeboundry.administrativeBoundary_id !==
            administrativeboundryId,
        );
        return updatedElectionBoundry;
      });
      return { previousElectionBoundry };
    },
    onError: (err, wardId, context) => {
      queryClient.setQueryData(
        ["administrativemaster"],
        context.previousElectionBoundry,
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["administrativemaster"] });
    },
  });

  return {
    administrativeBoundaryList: administrativeListResponse || [],
    createAdministrativeBoundary: createAdministrativeMutation.mutate,
    updateAdministrativeBoundary: updateAdminstrativeBoundaryMutation.mutate,
    deleteAdministrativeBoundary: deleteAdministrativeBoundryMutation.mutate,
  };
};
