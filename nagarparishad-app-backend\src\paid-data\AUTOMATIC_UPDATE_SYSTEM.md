# Automatic Paid/Non-Paid Data Update System

## Overview
This system automatically updates the `paid_and_non_paid_data` table whenever payments are created, updated, or deleted. This ensures that the paid/non-paid status and amounts are always accurate and up-to-date.

## How It Works

### 1. Payment Creation
When a new payment is created via `PaymentLogMasterService.createPayment()`:
- The payment is processed and saved to the database
- After successful transaction commit, the system automatically calls `PaidDataService.updatePropertyPaidData()`
- This recalculates the total paid amount, remaining amount, and paid status for the property
- The `paid_and_non_paid_data` table is updated with the new values

### 2. Payment Deletion
When a payment is deleted via `PaymentLogMasterService.deletePayment()`:
- The payment is removed from the database
- Penalties are restored if applicable
- After successful deletion, the system automatically calls `PaidDataService.updatePropertyPaidData()`
- This recalculates the amounts without the deleted payment
- The `paid_and_non_paid_data` table is updated accordingly

### 3. Payment Editing
When a payment is edited via `PaymentLogMasterService.editPaymentLog()`:
- The payment details are updated
- After successful update, the system automatically calls `PaidDataService.updatePropertyPaidData()`
- This ensures the paid/non-paid data reflects any changes in payment amounts or dates

## Key Features

### Automatic Calculation
The system automatically calculates:
- **Total Tax**: Retrieved from the WarshikKar entity for the current financial year
- **Paid Amount**: Sum of all payments for the property
- **Remaining Amount**: Total tax minus paid amount
- **Paid Status**: Boolean indicating if any payment has been made

### Owner Name Resolution
The system prioritizes owner names in this order:
1. Owner with type "स्वत:" (self-owned)
2. Any available owner if no "स्वत:" owner exists
3. "Unknown" if no owner details are found

### Error Handling
- Payment operations are not affected if the paid data update fails
- Errors are logged but don't prevent successful payment processing
- This ensures payment functionality remains robust

### Financial Year Support
- Updates work with the current financial year by default
- Can specify a different financial year if needed
- Handles financial year transitions gracefully

## API Endpoints

### Manual Update Single Property
```
POST /paid-data/update-property-paid-data/:propertyId?financialYearRange=2023-2024
```

### Batch Update Multiple Properties
```
POST /paid-data/batch-update-properties-paid-data
Body: {
  "propertyIds": ["prop1", "prop2", "prop3"],
  "financialYearRange": "2023-2024" // optional
}
```

## Database Schema

### PaidAndNonPaidDataEntity Fields
- `property_id`: Reference to the property
- `financial_year_id`: Reference to the financial year
- `financial_year_range`: String representation of the financial year
- `name`: Property owner name
- `property_number`: Property number
- `old_property_number`: Old property number (if any)
- `total_tax`: Total tax amount for the property
- `paid`: Total amount paid
- `remaining`: Remaining amount to be paid
- `is_paid`: Boolean indicating payment status

## Integration Points

### PaymentLogMasterService
- `createPayment()`: Calls update after successful payment creation
- `deletePayment()`: Calls update after successful payment deletion
- `editPaymentLog()`: Calls update after successful payment edit

### PaidDataService
- `updatePropertyPaidData()`: Updates single property
- `updateMultiplePropertiesPaidData()`: Updates multiple properties synchronously
- `batchUpdatePropertiesPaidData()`: Updates multiple properties asynchronously

## Performance Considerations

### Asynchronous Processing
- Batch updates run in the background using `setImmediate()`
- Large batch operations don't block the main thread
- Progress is logged for monitoring

### Efficient Queries
- Uses specific property and financial year filters
- Includes necessary relations in single queries
- Minimizes database round trips

## Monitoring and Logging

### Success Logging
- Logs successful updates with property ID and amounts
- Tracks batch processing progress and completion

### Error Logging
- Logs update failures without affecting payment operations
- Includes detailed error messages for debugging
- Separates payment errors from update errors

## Usage Examples

### Automatic Updates (No Code Required)
```typescript
// When creating a payment, the update happens automatically
const result = await paymentLogMasterService.createPayment(paymentData);
// Paid data is automatically updated after successful payment creation
```

### Manual Updates
```typescript
// Update single property
await paidDataService.updatePropertyPaidData('property-id-123');

// Update multiple properties
await paidDataService.updateMultiplePropertiesPaidData([
  'property-id-1', 
  'property-id-2', 
  'property-id-3'
]);

// Batch update (async)
await paidDataService.batchUpdatePropertiesPaidData([
  'property-id-1', 
  'property-id-2', 
  'property-id-3'
]);
```

## Benefits

1. **Data Consistency**: Ensures paid/non-paid data is always accurate
2. **Real-time Updates**: Updates happen immediately after payment operations
3. **Automatic Operation**: No manual intervention required
4. **Error Resilience**: Payment operations continue even if updates fail
5. **Flexible**: Supports both automatic and manual update scenarios
6. **Scalable**: Handles both single property and batch updates efficiently

## Future Enhancements

1. **Webhook Support**: Notify external systems of payment status changes
2. **Audit Trail**: Track all changes to paid/non-paid data
3. **Scheduled Reconciliation**: Periodic verification of data accuracy
4. **Performance Metrics**: Monitor update performance and success rates
