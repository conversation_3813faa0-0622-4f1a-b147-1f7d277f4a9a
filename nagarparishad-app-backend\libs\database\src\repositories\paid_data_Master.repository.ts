import { Repository } from 'typeorm';
import { PaidDataEntity } from '../entities'; // Assuming the entity is in the `entities` folder
import { InjectRepository } from '@nestjs/typeorm';

export class PaidDataRepository extends Repository<PaidDataEntity> {
  constructor(
    @InjectRepository(PaidDataEntity)
    private readonly paidDataRepository: Repository<PaidDataEntity>,
  ) {
    super(
      paidDataRepository.target,
      paidDataRepository.manager,
      paidDataRepository.queryRunner,
    );
  }

  // Save a new paid data
  async savePaidData(input: {
    previous_amount: number;
    current_amount: number;
    total_amount: number;
    tax_type_1: number;
    tax_type_2: number;
    tax_type_3: number;
    tax_type_4: number;
    tax_type_5: number;
    tax_type_6: number;
    tax_type_7: number;
    tax_type_8: number;
    tax_type_9: number;
    tax_type_10: number;
    status: string;
    payment_date: Date;
    fyyear: string;
    property_id: string;
    property_number: string;
    year_wise_penalty_data?: Record<string, any>;
  }): Promise<PaidDataEntity> {
    // Create a year-wise data object if not provided
    if (!input.year_wise_penalty_data && input.fyyear) {
      // Create a default year-wise data structure
      const yearWiseData = {};

      // Add the current year's data
      yearWiseData[input.fyyear] = {
        tax_type_1: input.tax_type_1,
        tax_type_2: input.tax_type_2,
        tax_type_3: input.tax_type_3,
        tax_type_4: input.tax_type_4,
        tax_type_5: input.tax_type_5,
        tax_type_6: input.tax_type_6,
        tax_type_7: input.tax_type_7,
        tax_type_8: input.tax_type_8,
        tax_type_9: input.tax_type_9,
        tax_type_10: input.tax_type_10,
        total_amount: input.total_amount,
        previous_amount: input.previous_amount,
        current_amount: input.current_amount,
        payment_date: input.payment_date,
      };

      input.year_wise_penalty_data = yearWiseData;
    }

    const paidData = this.paidDataRepository.create(input);
    return await this.paidDataRepository.save(paidData);
  }

  // Find all paid data
  async findAllPaidData() {
    return await this.paidDataRepository
      .createQueryBuilder('paid_data')
      .leftJoinAndSelect('paid_data.property', 'property')
      .orderBy('paid_data.created_at', 'DESC')
      .getMany();
  }

  async getAllPaidData(currentYear: String) {
    return await this.paidDataRepository
      .createQueryBuilder('paid_data')
      .leftJoinAndSelect('paid_data.property', 'property')
            .leftJoinAndSelect('paid_data.paymentInfo', 'paymentInfo')
            .leftJoinAndSelect('paymentInfo.receipts', 'receipts')

      .where('paid_data.financial_year = :currentYear', { currentYear })
      .andWhere('paid_data.deleted_at IS NULL')
      .orderBy('paid_data.created_at', 'DESC')
      .getMany();
  }

  async findAllPaidDataByYear(year: string) {
    return await this.paidDataRepository
      .createQueryBuilder('paid_data')
      .select(['paid_data.total_amount', 'property.property_id'])
      .leftJoin('paid_data.property', 'property')
      .where('paid_data.financial_year = :year', { year })
      .getMany();
  }

  // Find paid data by property ID
  async findPaidDataByPropertyId(property_id: string) {
    return await this.paidDataRepository
      .createQueryBuilder('paid_data')
      .where('paid_data.property_id = :property_id', { property_id })
      .getOne();
  }

  // Update paid data by ID
  async updatePaidData(
    id: string,
    input: {
      previous_amount?: number;
      current_amount?: number;
      total_amount?: number;
      tax_type_1?: number;
      tax_type_2?: number;
      tax_type_3?: number;
      tax_type_4?: number;
      tax_type_5?: number;
      tax_type_6?: number;
      tax_type_7?: number;
      tax_type_8?: number;
      tax_type_9?: number;
      tax_type_10?: number;
      status?: string;
      payment_date?: Date;
      fyyear?: string;
      property_number?: string;
      year_wise_penalty_data?: Record<string, any>;
    },
  ) {
    // If year-wise data is not provided but other fields are updated and fyyear is provided,
    // update the year-wise data for that year
    if (!input.year_wise_penalty_data && input.fyyear) {
      // Get the current paid data record
      const paidData = await this.paidDataRepository.findOne({
        where: { paid_data_id: id },
      });

      if (paidData) {
        // Get the current year-wise data or initialize it
        const yearWiseData = paidData.year_wise_penalty_data || {};

        // Create or update the data for the specified year
        const yearData = yearWiseData[input.fyyear] || {};

        // Update the year data with the new values
        if (input.tax_type_1 !== undefined)
          yearData.tax_type_1 = input.tax_type_1;
        if (input.tax_type_2 !== undefined)
          yearData.tax_type_2 = input.tax_type_2;
        if (input.tax_type_3 !== undefined)
          yearData.tax_type_3 = input.tax_type_3;
        if (input.tax_type_4 !== undefined)
          yearData.tax_type_4 = input.tax_type_4;
        if (input.tax_type_5 !== undefined)
          yearData.tax_type_5 = input.tax_type_5;
        if (input.tax_type_6 !== undefined)
          yearData.tax_type_6 = input.tax_type_6;
        if (input.tax_type_7 !== undefined)
          yearData.tax_type_7 = input.tax_type_7;
        if (input.tax_type_8 !== undefined)
          yearData.tax_type_8 = input.tax_type_8;
        if (input.tax_type_9 !== undefined)
          yearData.tax_type_9 = input.tax_type_9;
        if (input.tax_type_10 !== undefined)
          yearData.tax_type_10 = input.tax_type_10;
        if (input.total_amount !== undefined)
          yearData.total_amount = input.total_amount;
        if (input.previous_amount !== undefined)
          yearData.previous_amount = input.previous_amount;
        if (input.current_amount !== undefined)
          yearData.current_amount = input.current_amount;
        if (input.payment_date !== undefined)
          yearData.payment_date = input.payment_date;

        // Update the year-wise data
        yearWiseData[input.fyyear] = yearData;

        // Add the year-wise data to the input
        input.year_wise_penalty_data = yearWiseData;
      }
    }

    return await this.paidDataRepository
      .createQueryBuilder('paid_data')
      .update(PaidDataEntity)
      .set(input)
      .where('paid_data_id = :id', { id })
      .execute();
  }
  async findAndSumPaidDataByFinancialYearAndProperty(
    financial_year: string,
    property_number: string,
  ) {
    return await this.paidDataRepository
      .createQueryBuilder('paid_data')
      .select('SUM(paid_data.total_amount)', 'total_amount')
      .addSelect('SUM(paid_data.all_property_tax_sum)', 'all_property_tax_sum')
      .addSelect(
        'SUM(paid_data.all_property_tax_sum_prev)',
        'all_property_tax_sum_prev',
      )
      .addSelect(
        'SUM(paid_data.all_property_tax_sum_curr)',
        'all_property_tax_sum_curr',
      )
      .addSelect('SUM(paid_data.tax_type_1)', 'tax_type_1')
      .addSelect('SUM(paid_data.tax_type_1_prev)', 'tax_type_1_prev')
      .addSelect('SUM(paid_data.tax_type_1_curr)', 'tax_type_1_curr')
      .addSelect('SUM(paid_data.tax_type_2)', 'tax_type_2')
      .addSelect('SUM(paid_data.tax_type_2_prev)', 'tax_type_2_prev')
      .addSelect('SUM(paid_data.tax_type_2_curr)', 'tax_type_2_curr')
      .addSelect('SUM(paid_data.tax_type_3)', 'tax_type_3')
      .addSelect('SUM(paid_data.tax_type_3_prev)', 'tax_type_3_prev')
      .addSelect('SUM(paid_data.tax_type_3_curr)', 'tax_type_3_curr')
      .addSelect('SUM(paid_data.tax_type_4)', 'tax_type_4')
      .addSelect('SUM(paid_data.tax_type_4_prev)', 'tax_type_4_prev')
      .addSelect('SUM(paid_data.tax_type_4_curr)', 'tax_type_4_curr')
      .addSelect('SUM(paid_data.tax_type_5)', 'tax_type_5')
      .addSelect('SUM(paid_data.tax_type_5_prev)', 'tax_type_5_prev')
      .addSelect('SUM(paid_data.tax_type_5_curr)', 'tax_type_5_curr')
      .addSelect('SUM(paid_data.tax_type_6)', 'tax_type_6')
      .addSelect('SUM(paid_data.tax_type_6_prev)', 'tax_type_6_prev')
      .addSelect('SUM(paid_data.tax_type_6_curr)', 'tax_type_6_curr')
      .addSelect('SUM(paid_data.tax_type_7)', 'tax_type_7')
      .addSelect('SUM(paid_data.tax_type_7_prev)', 'tax_type_7_prev')
      .addSelect('SUM(paid_data.tax_type_7_curr)', 'tax_type_7_curr')
      .addSelect('SUM(paid_data.tax_type_8)', 'tax_type_8')
      .addSelect('SUM(paid_data.tax_type_8_prev)', 'tax_type_8_prev')
      .addSelect('SUM(paid_data.tax_type_8_curr)', 'tax_type_8_curr')
      .addSelect('SUM(paid_data.tax_type_9)', 'tax_type_9')
      .addSelect('SUM(paid_data.tax_type_9_prev)', 'tax_type_9_prev')
      .addSelect('SUM(paid_data.tax_type_9_curr)', 'tax_type_9_curr')
      .addSelect('SUM(paid_data.tax_type_10)', 'tax_type_10')
      .addSelect('SUM(paid_data.tax_type_10_prev)', 'tax_type_10_prev')
      .addSelect('SUM(paid_data.tax_type_10_curr)', 'tax_type_10_curr')
      .addSelect('SUM(paid_data.other_tax_sum_tax)', 'other_tax_sum_tax')
      .addSelect(
        'SUM(paid_data.other_tax_sum_tax_prev)',
        'other_tax_sum_tax_prev',
      )
      .addSelect(
        'SUM(paid_data.other_tax_sum_tax_curr)',
        'other_tax_sum_tax_curr',
      )
      .addSelect('SUM(paid_data.other_discount)', 'other_discount')

      .where('paid_data.financial_year = :financial_year', { financial_year })
      .andWhere('paid_data.property_number = :property_number', {
        property_number,
      })
      .getRawOne();
  }

  async findAndSumPaidDataByFinancialYearAndPropertyTotalSum(
    financial_year: string,
    property_number: string,
  ) {
    return await this.paidDataRepository
      .createQueryBuilder('paid_data')
      .select('SUM(paid_data.total_amount)', 'total_amount')

      .where('paid_data.financial_year = :financial_year', { financial_year })
      .andWhere('paid_data.property_number = :property_number', {
        property_number,
      })
      .getRawOne();
  }

  // Delete (soft delete) a paid data by ID
  async deletePaidData(id: string) {
    return await this.paidDataRepository
      .createQueryBuilder('paid_data')
      .softDelete()
      .where('paid_data_id = :id', { id })
      .execute();
  }

  async findTotalTaxPaid(year: string): Promise<number> {
    const result = await this.paidDataRepository
      .createQueryBuilder('paid_data')
      .select('SUM(paid_data.total_amount)', 'total_amount')
      .where('paid_data.financial_year = :year', { year })
      .getRawOne(); // Execute the query and get the raw result

    // Extract the total amount from the result
    return result ? parseFloat(result.total_amount) : 0;
  }

  /**
   * Add or update year-wise data for a specific property
   * @param paidDataId The ID of the paid data record
   * @param year The year for which to store data
   * @param data The data to store for the year
   */
  async addYearWiseData(
    paidDataId: string,
    year: string,
    data: any,
  ): Promise<void> {
    // First, get the current year_wise_penalty_data
    const paidData = await this.paidDataRepository.findOne({
      where: { paid_data_id: paidDataId },
    });

    if (!paidData) {
      throw new Error(`Paid data with ID ${paidDataId} not found`);
    }

    // Initialize year_wise_penalty_data if it doesn't exist
    const yearWiseData = paidData.year_wise_penalty_data || {};

    // Add or update the data for the specified year
    yearWiseData[year] = data;

    // Update the record
    await this.paidDataRepository.update(
      { paid_data_id: paidDataId },
      { year_wise_penalty_data: yearWiseData },
    );
  }

  /**
   * Get year-wise data for a specific property and year
   * @param paidDataId The ID of the paid data record
   * @param year Optional: The specific year to retrieve data for
   * @returns The year-wise data for the specified property and year, or all years if no year is specified
   */
  async getYearWiseData(paidDataId: string, year?: string): Promise<any> {
    const paidData = await this.paidDataRepository.findOne({
      where: { paid_data_id: paidDataId },
    });

    if (!paidData) {
      throw new Error(`Paid data with ID ${paidDataId} not found`);
    }

    const yearWiseData = paidData.year_wise_penalty_data || {};

    // If a specific year is requested, return only that year's data
    if (year) {
      return yearWiseData[year] || null;
    }

    // Otherwise, return all year-wise data
    return yearWiseData;
  }

  /**
   * Get year-wise data for a specific property by property ID
   * @param propertyId The ID of the property
   * @param year Optional: The specific year to retrieve data for
   * @returns The year-wise data for the specified property and year, or all years if no year is specified
   */
  async getYearWiseDataByPropertyId(
    propertyId: string,
    year?: string,
  ): Promise<any> {
    const paidData = await this.paidDataRepository.find({
      where: { property: { property_id: propertyId } },
      order: { createdAt: 'DESC' },
    });

    if (!paidData || paidData.length === 0) {
      return null;
    }

    // Combine year-wise data from all records for this property
    const combinedYearWiseData = {};

    for (const record of paidData) {
      const yearWiseData = record.year_wise_penalty_data || {};

      // Merge the year-wise data
      Object.keys(yearWiseData).forEach((yearKey) => {
        combinedYearWiseData[yearKey] = yearWiseData[yearKey];
      });
    }

    // If a specific year is requested, return only that year's data
    if (year) {
      return combinedYearWiseData[year] || null;
    }

    // Otherwise, return all year-wise data
    return combinedYearWiseData;
  }
}
