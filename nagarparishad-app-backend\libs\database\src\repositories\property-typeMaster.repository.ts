import { Repository } from 'typeorm';
import { PropertyTypeMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class PropertyTypeMasterRepository extends Repository<PropertyTypeMasterEntity> {
  constructor(
    @InjectRepository(PropertyTypeMasterEntity)
    private readonly propertyTypeMasterRepository: Repository<PropertyTypeMasterEntity>,
  ) {
    super(
      propertyTypeMasterRepository.target,
      propertyTypeMasterRepository.manager,
      propertyTypeMasterRepository.queryRunner,
    );
  }

  async saveData(input: {
    propertyType: string;
  }): Promise<PropertyTypeMasterEntity> {
    let data = this.propertyTypeMasterRepository.create(input);
    data = await this.propertyTypeMasterRepository.save(data);
    return data;
  }
  async findAllLocation() {
    return await this.propertyTypeMasterRepository
      .createQueryBuilder('property_type_master')
      .orderBy('property_type_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string) {
    return await this.propertyTypeMasterRepository
      .createQueryBuilder('property_type_master')
      .where('property_type_master.propertyType_id = :propertyType_id', {
        propertyType_id: id,
      })
      .getOne();
  }

  async updateData(propertyType_id: string, input: { propertyType?: string }) {
    return await this.propertyTypeMasterRepository
      .createQueryBuilder('property_type_master')
      .update(PropertyTypeMasterEntity)
      .set(input)
      .where('propertyType_id = :propertyType_id', { propertyType_id })
      .execute();
  }

  async deleteData(propertyType_id: string) {
    return await this.propertyTypeMasterRepository
      .createQueryBuilder('property_type_master')
      .softDelete()
      .where('propertyType_id = :propertyType_id', { propertyType_id })
      .execute();
  }

  async findOne_class(propertyType_id: string) {
    return await this.propertyTypeMasterRepository

      .createQueryBuilder('property_type_master')

      .select([
        'property_type_master.propertyType_id',
        'property_type_class.property_type_class_id',
        'property_type_class.property_type_class'
  
      ])  
      .leftJoin('property_type_master.property_type_class', 'property_type_class')
      .where('property_type_master.propertyType_id = :propertyType_id', {
        propertyType_id: propertyType_id,
      })
      .getOne();
  }
}
