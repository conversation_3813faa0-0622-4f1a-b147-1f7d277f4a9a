import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportPropertyColAdded61725960708731 implements MigrationInterface {
    name = 'ImportPropertyColAdded61725960708731'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ADD "systemPropertyId" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "systemPropertyId"`);
    }

}
