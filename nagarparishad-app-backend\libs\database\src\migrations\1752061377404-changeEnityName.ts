import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeEnityName1752061377404 implements MigrationInterface {
    name = 'ChangeEnityName1752061377404'

    public async up(queryRunner: QueryRunner): Promise<void> {
                await queryRunner.query(`ALTER TABLE "property" RENAME COLUMN "remark" TO "property_remark"`);

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
          await queryRunner.query(`ALTER TABLE "property" RENAME COLUMN "property_remark" TO "remark"`);
  }

}
