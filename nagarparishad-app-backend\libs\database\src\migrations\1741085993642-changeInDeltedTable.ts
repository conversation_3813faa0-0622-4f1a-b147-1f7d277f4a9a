import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeInDeltedTable1741085993642 implements MigrationInterface {
  name = 'ChangeInDeltedTable1741085993642';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "deleted_property" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "deleted_property" DROP COLUMN "createdAt"`,
    );
  }
}
