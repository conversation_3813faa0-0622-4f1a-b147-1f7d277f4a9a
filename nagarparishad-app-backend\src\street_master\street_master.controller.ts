import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { StreetMasterService } from './street_master.service';
import {
  CreateStreetMasterDto,
  StreetMasterDto,
  UpdateStreetMasterDto,
} from './dto/Street-Master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@ApiTags('Street Master')
@Controller('street-master')
export class StreetMasterController {
  constructor(private readonly streetMasterService: StreetMasterService) {}

  @Form('Street Master')
  @Permissions('can_write')
  @ApiOperation({ summary: 'Create a new street' })
  @ApiResponse({
    status: 201,
    description: 'The street has been successfully created',
  })
  @Post()
  create(@Body() createStreetMasterDto: CreateStreetMasterDto) {
    return this.streetMasterService.create(createStreetMasterDto);
  }

  @Form('Street Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all streets' })
  @ApiResponse({ status: 200, description: 'Returns all streets' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.streetMasterService.findAll();
  }

  @Form('Street Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one street' })
  @ApiResponse({ status: 200, description: 'Returns Single street' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() streetMasterDto: StreetMasterDto) {
    return this.streetMasterService.findOne(streetMasterDto);
  }

  @Form('Street Master')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Update a street by ID' })
  @ApiResponse({
    status: 200,
    description: 'The street has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'street not found' })
  @Patch()
  update(
    @Query() streetMasterDto: StreetMasterDto,
    @Body() updateStreetMasterDto: UpdateStreetMasterDto,
  ) {
    return this.streetMasterService.update(
      streetMasterDto,
      updateStreetMasterDto,
    );
  }

  @Form('Street Master')
  @Permissions('can_delete')
  @ApiOperation({ summary: 'Delete a street by ID' })
  @ApiResponse({
    status: 200,
    description: 'The street has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'street not found' })
  @Delete()
  remove(@Query() streetMasterDto: StreetMasterDto) {
    return this.streetMasterService.remove(streetMasterDto);
  }
}
