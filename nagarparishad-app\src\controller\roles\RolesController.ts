import { useContext, useEffect } from "react";
import { RoleModuleInterface } from "@/model/role/roleInterface";
import { GlobalContext } from "@/context/GlobalContext";
import Role<PERSON><PERSON> from "@/services/RolesServices"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

// Fetch function for roles
const fetchRoles = async () => {
  return new Promise((resolve, reject) => {
    RoleApi.getAllRoles((response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(new Error("Error fetching roles"));
      }
    });
  });
};

// Update function for roles
const updateRole = ({ roleId, roleData }) => {
  return new Promise((resolve, reject) => {
    RoleApi.updateRole(roleId, roleData, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(new Error("Error updating role"));
      }
    });
  });
};

// Add function for roles
const addRole = (roleData) => {
  return new Promise((resolve, reject) => {
    RoleApi.addRole(roleData, (response) => {
      if (response.status && response.data.statusCode === 201) {
        resolve(response.data.data);
      } else {
        reject(new Error("Error adding role"));
      }
    });
  });
};

// Delete function for roles
const deleteRole = (roleId) => {
  return new Promise((resolve, reject) => {
    RoleApi.deleteRole(roleId, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(new Error("Error deleting role"));
      }
    });
  });
};

export const useRoleController = () => {
  const queryClient = useQueryClient();
  const { refreshRoleList } = useContext(GlobalContext);

  // Fetch roles using useQuery
  const { data: roleData, refetch, isLoading: userRoleList } = useQuery({
    queryKey: ["rolelist"],
    queryFn: fetchRoles,
    staleTime: 10 * 60 * 2,
    refetchOnWindowFocus: true,
  });

  useEffect(() => {
    if (refreshRoleList) {
      refetch();
    }
  }, [refreshRoleList, refetch]);

  const updateRoleMutation = useMutation({
    mutationFn: updateRole,
    onMutate: async ({ roleId, roleData }) => {
      await queryClient.cancelQueries({ queryKey: ["rolelist"] });

      const previousRoles = queryClient.getQueryData(["rolelist"]);
      queryClient.setQueryData(["rolelist"], (old) =>
        old.map((role) =>
          role.role_id === roleId ? { ...role, ...roleData } : role,
        ),
      );

      return { previousRoles };
    },
    onError: (err, { roleId, roleData }, context) => {
      queryClient.setQueryData(["rolelist"], context.previousRoles);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["rolelist"] });
    },
  });

  const addRoleMutation = useMutation({
    mutationFn: addRole,
    onMutate: async (roleData) => {
      await queryClient.cancelQueries({ queryKey: ["rolelist"] });

      const previousRoles = queryClient.getQueryData(["rolelist"]);
      queryClient.setQueryData(["rolelist"], (old) => [...old, roleData]);

      return { previousRoles };
    },
    onError: (err, roleData, context) => {
      queryClient.setQueryData(["rolelist"], context.previousRoles);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["rolelist"] });
    },
  });

  const deleteRoleMutation = useMutation({
    mutationFn: deleteRole,
    onMutate: async (roleId) => {
      await queryClient.cancelQueries({ queryKey: ["rolelist"] });

      const previousRoles = queryClient.getQueryData(["rolelist"]);
      queryClient.setQueryData(["rolelist"], (old) =>
        old.filter((role) => role.role_id !== roleId),
      );

      return { previousRoles };
    },
    onError: (err, roleId, context) => {
      queryClient.setQueryData(["rolelist"], context.previousRoles);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["rolelist"] });
    },
  });

  return {
    roleList: roleData || [],
    userRoleList,
    addRole: addRoleMutation.mutate,
    updateRole: updateRoleMutation.mutate,
    deleteRole: deleteRoleMutation.mutate,
  };
};
