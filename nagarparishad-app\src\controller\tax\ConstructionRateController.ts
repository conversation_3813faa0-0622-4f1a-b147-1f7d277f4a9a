import { ZoneObjectInterface } from "@/model/zone-master";
import Api from "../../services/ApiServices";
import {
  useQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import TaxListApi from "@/services/TaxServices";

interface ZoneData {
  zoneName: string; // Only zoneName now
}

const fetchConstructionRate = async () => {
    return new Promise((resolve, reject) => {
      TaxListApi.getConstructionRate((response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      });
    });
  };
  

const createConstructionRate = async (constructionRateData: any) => {
  return new Promise((resolve, reject) => {
    TaxListApi.createConstructionRate(constructionRateData, (response) => {
      console.log("in api take the chaneg",response)
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateConstructionRate = async ({ id, payload }: { id : string; payload: any }) => {
  return new Promise((resolve, reject) => {
    TaxListApi.updateConstructionRate(id, payload, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteConstructionRate = async (constructionRateId: string) => {
  return new Promise((resolve, reject) => {
    TaxListApi.deleteConstructionRate(constructionRateId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useConstructionRateController = () => {
  const queryClient = useQueryClient();

  const { data: constructionRateData, isLoading: propertyLoading } = useQuery({
    queryKey: ["constructionRatemaster"],
    queryFn: fetchConstructionRate,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
  });

  // const createConstructionRateMutation = useMutation({
  //   mutationFn: TaxListApi.createConstructionRate,
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["constructionRatemaster"] });
  //   },
  // });
  const createConstructionRateMutation = useMutation({
    mutationFn: createConstructionRate,
    onMutate: async (newconstructionRates) => {
      await queryClient.cancelQueries({ queryKey: ["constructionRatemaster"] });
      const previousconstructionRates = queryClient.getQueryData(["constructionRatemaster"]);

      queryClient.setQueryData(["constructionRatemaster"], (old: any) => {
             console.log(" construtionold",old)
        const updatedData = [newconstructionRates, ...old.data];
        console.log("updatedData",updatedData)
        return updatedData;
      });

      return { previousconstructionRates };
    },
    onError: (err, newconstructionRates, context) => {
      queryClient.setQueryData(["constructionRatemaster"], context.previousconstructionRates);
      console.error("Error creating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["constructionRatemaster"] });
    },
  });

  const updateConstructionRateMutationa = useMutation({
    mutationFn: TaxListApi.updateConstructionRate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["constructionRatemaster"] });
    },
  });
  const updateConstructionRateMutation = useMutation({
    mutationFn: updateConstructionRate,
    onMutate: async ({ id, payload }) => {
      await queryClient.cancelQueries({ queryKey: ["constructionRatemaster"] });

      const previousWards = queryClient.getQueryData(["constructionRatemaster"]);
      queryClient.setQueryData(["constructionRatemaster"], (old: any) => {
        const updatedWards = old?.data?.map((constructionRate: any) =>
          constructionRate.rr_construction_rate_id === id ? { ...constructionRate, ...payload } : constructionRate,
        );
        console.log("Updated query data:", updatedWards);
        return updatedWards;
      });

      return { previousWards };
    },
    onError: (err, { id, payload }, context) => {
      queryClient.setQueryData(["constructionRatemaster"], context.previousWards);
      console.error("Error updating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["constructionRatemaster"] });
    },
  });
  // const deleteConstructionRateMutation = useMutation({
  //   mutationFn: TaxListApi.deleteConstructionRate,
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["constructionRatemaster"] });
  //   },
  // });
  const deleteConstructionRateMutation = useMutation({
    mutationFn: deleteConstructionRate,
    onMutate: async (constructionRateId) => {
      await queryClient.cancelQueries({ queryKey: ["constructionRatemaster"] });

      const previousConstructionRate = queryClient.getQueryData(["constructionRatemaster"]);

      queryClient.setQueryData(["constructionRatemaster"], (old: any) => {
        const updatedConstructionRate = old?.data?.filter((constructionRate: any) => constructionRate.rr_construction_rate_id !== constructionRateId);
        return updatedConstructionRate;
      });

      return { previousConstructionRate };
    },
    onError: (err, ward_id, context) => {
      queryClient.setQueryData(["constructionRatemaster"], context.previousConstructionRate);
      console.error("Error deleting ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["constructionRatemaster"] });
    },
  });
  return {
    constructionRateList: constructionRateData?.data || [],
    propertyLoading,
    createConstructionRate: createConstructionRateMutation.mutate,
    updateConstructionRate: updateConstructionRateMutation.mutate,
    deleteConstructionRate: deleteConstructionRateMutation.mutate,
  };

  
};


