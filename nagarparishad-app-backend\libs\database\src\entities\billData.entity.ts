import {
  Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne,
  JoinColumn,
} from 'typeorm';
import { PropertyEntity } from './property.entity';
@Entity({ name: 'billdata' })
export class BillDataEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50, unique: true })
  billNo: string;

  // @CreateDateColumn({ type: 'timestamp' })
  // bill_generation_date: Date;
  @Column({ type: 'date', nullable: true })
    bill_generation_date: Date;

  @ManyToOne(() => PropertyEntity, (property) => property.property_id, { cascade: true , onDelete: 'CASCADE' })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

  // @Column({ type: 'uuid' })
  // property_id: string;

  @Column({ type: 'varchar', length: 50 })
  property_number: string;

  @Column({ type: 'varchar', length: 10 })
  fyear: string;
}
