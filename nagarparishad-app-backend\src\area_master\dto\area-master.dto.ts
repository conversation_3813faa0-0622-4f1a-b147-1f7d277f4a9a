import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateAreaMasterDto {
  @ApiProperty({ name: 'areaName', type: String })
  @IsNotEmpty()
  @IsString()
  areaName: string;
}
export class UpdateAreaMasterDto extends PartialType(CreateAreaMasterDto) {}

export class AreaMasterDto {
  @ApiProperty({ name: 'areaId', type: String, description: 'STring of uuid' })
  @IsNotEmpty()
  @IsUUID()
  area_id: string;
}
