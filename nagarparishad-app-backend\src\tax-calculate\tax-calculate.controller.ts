import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { TaxCalculateService } from './tax-calculate.service';
import { Tax_billGenerateService } from './tax-billGenarete.service';
import { Tax_propertyFYDto } from './dto/tax-calculate.dto';

import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@Controller('tax-calculate')
export class TaxCalculateController {
  constructor(
    private readonly taxCalculateService: TaxCalculateService,
    private readonly tax_billGenerateService: Tax_billGenerateService
  ) {}

  @Post('generate_bill')
  generate_bill(@Query() tax_propertyFYDto: Tax_propertyFYDto) {
    // const { financial_year } = tax_propertyFYDto;
    // return {
    //   message: 'generate bill',
    //   data: financial_year
    // };
    // return  this.taxCalculateService.generate_bill(tax_propertyFYDto);
    return  this.tax_billGenerateService.generate_bills(tax_propertyFYDto);
  }

  @ApiOperation({ summary: 'Get all  Tax details of all Property ' })
  @ApiResponse({ status: 200, description: 'Returns all  Tax details of all Property ' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('bills-list')
  findAll(@Query() params) {
    const { ward, value , searchOn, fy } = params;
    return this.taxCalculateService.findAll(ward, value , searchOn, fy);
  }

  @ApiOperation({ summary: 'Get all  Tax details of all Property ' })
  @ApiResponse({ status: 200, description: 'Returns all  Tax details of all Property ' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('fy-list')
  findFyList() { 
   
   return this.taxCalculateService.findFyList();
  }

  @ApiOperation({ summary: 'Update is_published status of a record' })
  @ApiResponse({ status: 200, description: 'Record updated successfully' })
  @ApiResponse({ status: 404, description: 'Record not found' })
  @Put('fy-update-status')
  updateStatus(@Query('id') id: string, @Query('is_published') isPublished: boolean) {
    return this.taxCalculateService.updateStatus(id, isPublished);
  }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.taxCalculateService.findOne(+id);
  // }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateTaxCalculateDto: UpdateTaxCalculateDto) {
  //   return this.taxCalculateService.update(+id, updateTaxCalculateDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.taxCalculateService.remove(+id);
  // }
}
