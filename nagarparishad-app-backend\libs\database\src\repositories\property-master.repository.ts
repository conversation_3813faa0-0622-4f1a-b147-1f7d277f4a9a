import { Brackets, Repository } from 'typeorm';
import { PropertyEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import {
  PaginatedResult,
  PaginationOptions,
  paginate,
} from '@helper/helpers/Pagination';
import { BadRequestException, Logger } from '@nestjs/common';
import { GlobalSearchDto } from 'src/gloabl_serch/dto/gloabl_serch.dto';

export class PropertyMasterRepository extends Repository<PropertyEntity> {
  constructor(
    @InjectRepository(PropertyEntity)
  private readonly propertyMasterRepository: Repository<PropertyEntity>,
  ) {
    super(
      propertyMasterRepository.target,
      propertyMasterRepository.manager,
      propertyMasterRepository.queryRunner,
    );
  }

  async saveData(input: any) {
    let data = this.propertyMasterRepository.create(input);
    data = await this.propertyMasterRepository.save(data);
    return data;
  }

  // async findAllData() {
  //   return await this.propertyMasterRepository
  //     .createQueryBuilder('property')
  //     .select([
  //       'property.property_id',
  //       'property.property',
  //       'property."old_propertyNumber"',
  //       'property.mobile_number',
  //       'property.propertyNumber',
  //       'property.old_propertyNumber',
  //       'zone.zoneName',
  //       'ward.ward_name',
  //     ])
  //     .leftJoin('property_master.ward', 'ward')
  //     .leftJoin('property_master.zone', 'zone')
  //     .orderBy('property_master.updated_at', 'DESC')
  //     .getMany();
  // }

  // property_number, old property number, mobile no, ward name, zone name , owner details name, default limit of 200, Order by latest_updated.
  async findAllData(
    value: string,
    searchOn: string,
    options: PaginationOptions,
  ) {
    try {
      const queryBuilder = this.propertyMasterRepository
        .createQueryBuilder('property')
        .select([
          'property.property_id',
          'property.propertyNumber',
          'property.old_propertyNumber',
          'property.sr_no',
          'property.updatedAt',
          'zone.zoneName',
          'ward.ward_name',
          'register_number.register_name', // Changed to register_number_name
          'street.street_name',
          'ownerDetails.name',
          'ownerDetails.owner_type_id',
          'ownerDetails.property_owner_details_id',
          'usageDetails.are_sq_meter',
          'usageDetails.property_usage_details_id',
          'property_type.propertyType',
          'usageType.usage_type',
          'property.mobile_number',
          'owner_type.owner_type',
        ])
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.street', 'street')
        .leftJoin('property.register_number', 'register_number') // Changed to register_number join
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('property.property_usage_details', 'usageDetails')
        .leftJoin('usageDetails.propertyType', 'property_type')
        .leftJoin('usageDetails.usageType', 'usageType')
        .leftJoin('ownerDetails.owner_type', 'owner_type');

      if (value && searchOn) {
        let exactMatchQuery = '';
        let partialMatchQuery = '';

        switch (searchOn) {
          case 'property_number':
            exactMatchQuery = 'property.propertyNumber = :value';
            partialMatchQuery = 'property.propertyNumber LIKE :partialValue';
            break;
          case 'old_property_number':
            exactMatchQuery = 'property.old_propertyNumber = :value';
            partialMatchQuery =
              'property.old_propertyNumber LIKE :partialValue';
            break;
          case 'mobile_no':
            exactMatchQuery = 'property.mobile_number = :value';
            partialMatchQuery = 'property.mobile_number LIKE :partialValue';
            break;
          case 'ward_name':
            exactMatchQuery = 'ward.ward_name = :value';
            partialMatchQuery = 'ward.ward_name LIKE :partialValue';
            break;
          case 'zone_name':
            exactMatchQuery = 'zone.zoneName = :value';
            partialMatchQuery = 'zone.zoneName LIKE :partialValue';
            break;
          case 'owner_details_name':
            exactMatchQuery = 'ownerDetails.name = :value';
            partialMatchQuery = 'ownerDetails.name LIKE :partialValue';
            break;
          default:
            throw new BadRequestException('Invalid searchOn parameter');
        }

        queryBuilder.andWhere(`(${partialMatchQuery})`, {
          value: value,
          partialValue: `${value}%`,
        });
      }
      options.sortBy = 'updatedAt';
      return await paginate(queryBuilder, options, 'property');
    } catch (error) {
      throw error;
    }
  }

 async getData(value: string, searchOn: string) {
  try {
    const searchField = 'property.' + searchOn;

    const result = await this.propertyMasterRepository
      .createQueryBuilder('property')
      .select([
        'property.property_id',
        'property.sr_no',
        'property.propertyNumber',
        'property.old_propertyNumber',
        'property.city_survey_number',
        'property.address',
        'property.house_or_apartment_name',
        'property.latitude',
        'property.longitude',
        'property.mobile_number',
        'property.email_id',
        'property.property_desc',
        'property.plot_area',
        'property.Plot_construction_area',
        'property.Plot_empty_area',
        'property.carpet_area',
        'property.exempted_area',
        'property.assessable_area',
        'property.land_cost',
        'property.standard_rate',
        'property.annual_rent',
        'property.capital_value',
        'property.property_remark',
        'property.landmark',
        'property.snp_ward',
        'property.zone_code',
        'property.gat_no',
        'property.gis_number',
        'property.note',
        'property.survey_person_code',
        'property.survey_date',
        'property.city',
        'property.plot_number',
        'property.block_number',
        'property.house_number',
        'property.ferfarRemark',

        'zone.zoneName',
        'zone.zone_id',
        'ward.ward_name',
        'ward.ward_id',
        'register_number.register_name', // Changed to register_number_name
        'register_number.register_id',   // Changed to register_number_id
        'street.street_name',
        'street.street_id',
        'ownerDetails.property_owner_details_id',
        'ownerDetails.name',
        'ownerDetails.owner_type_id',
        'ownerDetails.mobile_number',
        'ownerDetails.email_id',
        'ownerDetails.aadhar_number',
        'ownerDetails.pan_card',
        'ownerDetails.gender',
        'ownerDetails.marital_status',
        'ownerDetails.partner_name',
        'owner_type.owner_type',
        'owner_type.owner_type_id',
         'ownerDetails.created_at',

        'usageDetails.property_usage_details_id',
        'usageDetails.are_sq_meter',
        'usageDetails.construction_area',
        'usageDetails.orderIndex',
        'usageDetails.length',
        'usageDetails.width',
        'usageDetails.are_sq_ft',
        'usageDetails.Building_age',
        'usageDetails.floor',
        'usageDetails.flat_no',
        'usageDetails.authorized',
        'usageDetails.construction_end_date',
        'usageDetails.construction_start_date',
        'usageDetails.construction_start_year',
        'usageDetails.tapshil',
        'usageDetails.remark',
        'usageDetails.annual_rent',

        'property_type.propertyType',
        'property_type.propertyType_id',
        'usageType.usage_type',
        'usageType.usage_type_id',
        'usageSubType.usage_sub_type',
        'usageSubType.usage_sub_type_master_id',

        'floorType.floor_id',
        'floorType.floor_name',

        'common_fields_of_property.id',
        'common_fields_of_property.GISID',
        'common_fields_of_property.propertyDescription',
        'common_fields_of_property.completionCertificate',
        'common_fields_of_property.accessRoad',
        'common_fields_of_property.individualToilet',
        'common_fields_of_property.toiletType',
        'common_fields_of_property.totalNumber',
        'common_fields_of_property.lightingFacility',
        'common_fields_of_property.tapConnection',
        'common_fields_of_property.totalConnections',
        'common_fields_of_property.solarProject',
        'common_fields_of_property.rainWaterHarvesting',
        'common_fields_of_property.sewageSystem',
        'common_fields_of_property.groundFloorArea',
        'common_fields_of_property.remainingGroundFloorArea',
      ])
      .leftJoin('property.ward', 'ward')
      .leftJoin('property.zone', 'zone')
      .leftJoin('property.street', 'street')
      .leftJoin('property.register_number', 'register_number') // Changed to register_number join
      .leftJoin('property.commonFields', 'common_fields_of_property')
      .leftJoin('property.property_owner_details', 'ownerDetails')
      .leftJoin('property.property_usage_details', 'usageDetails')
      .leftJoin('usageDetails.propertyType', 'property_type')
      .leftJoin('usageDetails.usageType', 'usageType')
      .leftJoin('usageDetails.usageSubType', 'usageSubType')
      .leftJoin('usageDetails.floorType', 'floorType')
      .leftJoin('ownerDetails.owner_type', 'owner_type')
      .where(`${searchField} = :value`, { value })
      .orderBy('ownerDetails.created_at', 'ASC')
      .getOne()
      ;

    // ✅ Sort usageDetails by `orderIndex` ascending
    if (result?.property_usage_details && Array.isArray(result.property_usage_details)) {
      result.property_usage_details.sort((a, b) => (a.orderIndex ?? 0) - (b.orderIndex ?? 0));
    }

    return result;
  } catch (error) {
    throw error;
  }
}

  async findById(id: string) {
    try {
      return await this.propertyMasterRepository
        .createQueryBuilder('property')
        .leftJoinAndSelect('property.ward', 'ward')
        .leftJoinAndSelect('property.zone', 'zone')
        .leftJoinAndSelect('property.street', 'street')
        .leftJoinAndSelect('property.commonFields', 'commonFields')
        .leftJoinAndSelect('property.property_owner_details', 'ownerDetails')
        .leftJoinAndSelect('property.property_usage_details', 'usageDetails')
        .leftJoinAndSelect('usageDetails.propertyType', 'propertyType')
        .leftJoinAndSelect('usageDetails.usageType', 'usageType')
        .leftJoinAndSelect('usageDetails.usageSubType', 'usageSubType')
        .leftJoinAndSelect('usageDetails.floorType', 'floorType')
        .leftJoinAndSelect('property.milkatKar', 'milkatKar')
        .leftJoinAndSelect('property.milkatKarTax', 'milkatKarTax')
        .leftJoinAndSelect('property.tax_Property', 'taxProperty')
        .leftJoinAndSelect('property.billdata', 'billData')
        .leftJoinAndSelect('property.payments', 'payments')
        .leftJoinAndSelect('property.receipt', 'receipt')
        .leftJoinAndSelect('property.warshikKar', 'warshikKar')
        .where('property.property_id = :property_id', { property_id: id })
        .getOne();
    } catch (error) {
      throw new Error(
        `Error fetching property with ID ${id}: ${error.message}`,
      );
    }
  }

  async findByIdMini(id: string) {
    try {
      return await this.propertyMasterRepository
        .createQueryBuilder('property')
        .leftJoinAndSelect('property.property_usage_details', 'usageDetails')
        .leftJoinAndSelect('usageDetails.propertyType', 'propertyType')
        .leftJoinAndSelect('property.ward', 'ward')
        .where('property.property_id = :property_id', { property_id: id })
        .getOne();
    } catch (error) {
      throw new Error(
        `Error fetching property with ID ${id}: ${error.message}`,
      );
    }
  }

  async getDataFromID(id: string) {
    try {
      return await this.propertyMasterRepository
        .createQueryBuilder('property')
        .leftJoinAndSelect('property.ward', 'ward')
        .leftJoinAndSelect('property.zone', 'zone')
        .leftJoinAndSelect('property.street', 'street')
        .leftJoinAndSelect('property.property_owner_details', 'ownerDetails')
        .leftJoinAndSelect('ownerDetails.owner_type', 'ownerType')
        .where('property.property_id = :property_id', { property_id: id })
        .orderBy('ownerDetails.updated_at', 'ASC')
        .getOne();
    } catch (error) {
      throw new Error(
        `Error fetching property with ID ${id}: ${error.message}`,
      );
    }
  }

  async updateData(id: string, input: any) {
    return await this.propertyMasterRepository
      .createQueryBuilder('property_master')
      .update(PropertyEntity)
      .set(input)
      .where('property_master.property_master_id = :property_master_id', {
        property_master_id: id,
      })
      .execute();
  }

  async deleteData(id: string) {
    return await this.propertyMasterRepository
      .createQueryBuilder('property_master')
      .softDelete()
      .where('property_master.property_master_id = :property_master_id', {
        property_master_id: id,
      })
      .execute();
  }

  async getSearch(options: any): Promise<PaginatedResult<PropertyEntity>> {
    const { search } = options;

    const queryBuilder =
      this.propertyMasterRepository.createQueryBuilder('property_master');

    queryBuilder
      .select([
        'property_master.property_master_id',
        'property_master.firstname',
        'property_master.lastname',
        'property_master.mobile_number',
        'property_master.propertyNumber',
        'property_master.old_propertyNumber',
        'zone.zoneName',
        'ward.ward_name',
        'propertyType.propertyType',
      ])
      .leftJoin('property_master.ward', 'ward')
      .leftJoin('property_master.zone', 'zone')
      .leftJoin('property_master.propertyType', 'propertyType');

    if (search) {
      queryBuilder.orWhere('property_master.propertyNumber LIKE :search', {
        search: `%${search}%`,
      });
      queryBuilder.orWhere('property_master.old_propertyNumber LIKE :search', {
        search: `%${search}%`,
      });
      queryBuilder.orWhere('property_master.mobile_number LIKE :search', {
        search: `%${search}%`,
      });
    }

    return await paginate(queryBuilder, options, 'property_master');
  }
  async findByName(input: any, options: PaginationOptions) {
    const { ward, zone, firstName, lastName } = input;
    const queryBuilder =
      this.propertyMasterRepository.createQueryBuilder('property');

    // Combine firstName and lastName, handling cases where either is missing
    let name = '';
    if (firstName && lastName) {
      name = `${firstName} ${lastName}`;
    } else if (firstName) {
      name = firstName;
    } else if (lastName) {
      name = lastName;
    }

    queryBuilder
      .select([
        'property.property_id',
        'property.propertyNumber',
        'zone.zoneName',
        'ward.ward_name',
        'ownerDetails.name',
        'ownerDetails.mobile_number',
        'warshikKar.warshik_karId',
        'warshikKar.financial_year',
        'warshikKar.total_tax',
        'warshikKar.total_tax_previous',
      ])
      .leftJoin('property.ward', 'ward')
      .leftJoin('property.zone', 'zone')
      .leftJoin('property.property_owner_details', 'ownerDetails')
      .leftJoin('property.property_usage_details', 'usageDetails')
      .leftJoin('property.warshikKar', 'warshikKar');

    // Conditionally add `ward`, `zone`, and `name` to the query if provided
    if (ward) {
      queryBuilder.andWhere('ward.ward_name = :ward', { ward });
    }

    if (zone) {
      queryBuilder.andWhere('zone.zoneName = :zone', { zone });
    }

    if (name) {
      queryBuilder.andWhere('ownerDetails.name ILIKE :name', {
        name: `%${name}%`,
      });
    }

    return await paginate(queryBuilder, options, 'property');
  }

  async getMilkatKar(
    value: string,
    searchOn: string,
    financial_year: string,
    reassessment_range_id: string,
  ) {
    try {
      // Determine the field to search on based on the searchOn parameter
      // const searchField = searchOn === 'old_propertyNumber' ? 'property.old_propertyNumber' : 'property.propertyNumber';
      const searchField = 'property.' + searchOn;
      const queryBuilder = this.propertyMasterRepository
        .createQueryBuilder('property')
        .select([
          'property.property_id',
          'property.sr_no',
          'property.propertyNumber',
          'property.city_survey_number',
          'property.address',
          'property.house_or_apartment_name',
          'property.latitude',
          'property.longitude',
          'property.mobile_number',
          'property.email_id',
          'property.property_desc',
          'property.plot_area',
          'property.Plot_construction_area',
          'property.Plot_empty_area',
          'property.carpet_area',
          'property.exempted_area',
          'property.assessable_area',
          'property.land_cost',
          'property.standard_rate',
          'property.annual_rent',
          'property.capital_value',
          'property.property_remark',
          'property.snp_ward',
          'property.zone_code',
          'property.gat_no',
          'property.gis_number',
          'property.note',
          'property.survey_person_code',
          'property.survey_date',
          'property.updateStatus',
          'zone.zoneName',
          'ward.ward_name',
          'street.street_name',

          'ownerDetails.property_owner_details_id',
          'ownerDetails.name',
          'ownerDetails.owner_type_id',
          'ownerDetails.mobile_number',
          'ownerDetails.email_id',
          'ownerDetails.aadhar_number',
          'owner_type.owner_type',

          'usageDetails.property_usage_details_id',
          'usageDetails.are_sq_meter',
          'usageDetails.construction_area',
          'usageDetails.length',
          'usageDetails.width',
          'usageDetails.are_sq_ft',
          'usageDetails.Building_age',
          'usageDetails.floor',
          'usageDetails.flat_no',
          'usageDetails.authorized',
          'usageDetails.construction_end_date',
          'property_type.propertyType',
          'usageType.usage_type',
          'usageSubType.usage_sub_type',
          'floor_Type.floor_name',

          'milkatKar.milkatKar_id',
          'milkatKar.financial_year',
          'milkatKar.all_property_tax_sum',
          'milkatKar.tax_type_1',
          'milkatKar.tax_type_2',
          'milkatKar.tax_type_3',
          'milkatKar.tax_type_4',
          'milkatKar.tax_type_5',
          'milkatKar.tax_type_6',
          'milkatKar.tax_type_7',
          'milkatKar.tax_type_8',
          'milkatKar.tax_type_9',
          'milkatKar.other_tax_sum_tax',
          'milkatKar.total_tax',

          'milkatKarTax.tax_value',
          'milkatKarTax.tax',
          'milkatKarTax.capital_value',
          'milkatKarTax.weighting',
          'milkatKarTax.depreciation_rate',
          'milkatKarTax.rr_construction_rate',
          'milkatKarTax.rr_rate',
          'milkatKarTax.sq_ft_meter',
          'milkatKarTax.capital_value',
          'milkatKarTax.tax_value',
          'milkatKarTax.tax',
          'milkatKarTax.shasti_fee',

          'reassessmentRange.reassessment_range_id',
          'reassessmentRange.start_range',
          'reassessmentRange.end_range',

          // Add a sum of capital_value from milkatKarTax
          // 'SUM(milkatKarTax.capital_value) AS totalCapitalValue',
        ])
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.street', 'street')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        // .leftJoin('property.property_usage_details', 'usageDetails')
        .leftJoin('property.milkatKar', 'milkatKar') // Correct table name here
        .leftJoin('milkatKar.milkatKarTax', 'milkatKarTax')
        .leftJoin('milkatKarTax.property_usage_details', 'usageDetails')
        .leftJoin('usageDetails.propertyType', 'property_type')
        .leftJoin('usageDetails.usageType', 'usageType')
                .leftJoin('usageDetails.floorType', 'floor_Type')

        .leftJoin('usageDetails.usageSubType', 'usageSubType')
        .leftJoin('ownerDetails.owner_type', 'owner_type')
        .leftJoin('milkatKar.reassessmentRange', 'reassessmentRange')
        .where(`${searchField} = :value AND milkatKar.status = 'active'`, {
          value,
        });

      // Use the reassessment_range_id for filtering
      queryBuilder.andWhere(
        'reassessmentRange.reassessment_range_id = :reassessmentRangeId',
        {
          reassessmentRangeId: reassessment_range_id,
        },
      );

      // Assuming the relationship is correctly defined
      // .where('property.propertyNumber = :propertyNumber', {
      //   propertyNumber: propertyNumber,
      // })
      //.groupBy('property.property_id') // Group by property to get the sum
      return await queryBuilder.getOne();
    } catch (error) {
      throw error;
    }
  }

  async getMilkatKarAll(
    ward: string,
    value: string,
    searchOn: string,
    options: PaginationOptions,
  ) {
    try {
      const queryBuilder = this.propertyMasterRepository
        .createQueryBuilder('property')
        .select([
          'property.property_id',
          'property.sr_no',
          'property.propertyNumber',
          'property.old_propertyNumber',

          'property.city_survey_number',
          'property.address',
          'property.house_or_apartment_name',

          'zone.zoneName',
          'ward.ward_name',
          'street.street_name',

          'ownerDetails.property_owner_details_id',
          'ownerDetails.name',
          'ownerDetails.owner_type_id',
          'ownerDetails.mobile_number',
          'ownerDetails.email_id',
          'ownerDetails.aadhar_number',
          'owner_type.owner_type',

          'usageDetails.property_usage_details_id',
          'usageDetails.are_sq_meter',
          'usageDetails.construction_area',
          'usageDetails.length',
          'usageDetails.width',
          'usageDetails.are_sq_ft',
          'usageDetails.Building_age',
          'usageDetails.floor',
          'usageDetails.flat_no',
          'usageDetails.authorized',
          'usageDetails.construction_end_date',
          'property_type.propertyType',
          'usageType.usage_type',
          'usageSubType.usage_sub_type',

          'milkatKar.milkatKar_id',
          'milkatKar.financial_year',
          'milkatKar.all_property_tax_sum',

          'milkatKar.other_tax_sum_tax',
          'milkatKar.total_tax',

          // Add a sum of capital_value from milkatKarTax
          // 'SUM(milkatKarTax.capital_value) AS totalCapitalValue',
        ])
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.street', 'street')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        // .leftJoin('property.property_usage_details', 'usageDetails')
        .leftJoin('property.milkatKar', 'milkatKar') // Correct table name here
        .leftJoin('milkatKar.milkatKarTax', 'milkatKarTax')
        .leftJoin('milkatKarTax.property_usage_details', 'usageDetails')
        .leftJoin('usageDetails.propertyType', 'property_type')
        .leftJoin('usageDetails.usageType', 'usageType')
        .leftJoin('usageDetails.usageSubType', 'usageSubType')
        .leftJoin('ownerDetails.owner_type', 'owner_type')
        .where(` milkatKar.status = 'active'`, {
          value,
        });

      // Apply the additional filter only if searchOn is not 'all'
      if (searchOn == 'name') {
        queryBuilder.andWhere(`ownerDetails.name LIKE :value`, {
          value: `%${value}%`,
        });
      } else if (searchOn !== 'all') {
        queryBuilder.andWhere(`property.${searchOn} = :value`, {
          value: value,
        });
      } else {
        queryBuilder.andWhere('ward.ward_name = :ward', { ward: ward });
      }
      return await paginate(queryBuilder, options, 'property');
    } catch (error) {
      throw error;
    }
  }

  async getWarshikKar(value: string, searchOn: string, financial_year: string) {
    try {
      // Determine the field to search on based on the searchOn parameter
      // const searchField = searchOn === 'old_propertyNumber' ? 'property.old_propertyNumber' : 'property.propertyNumber';
      console.log(
        'values',
        value,
        'searchOn',
        searchOn,
        'financial_year',
        financial_year,
      );
      const searchField = 'property.' + searchOn;
      return await this.propertyMasterRepository
        .createQueryBuilder('property')
        .select([
          'property.property_id',
          'property.sr_no',
          'property.propertyNumber',
          'property.old_propertyNumber',

          'property.city_survey_number',
          'property.address',
          'property.house_or_apartment_name',
          'property.latitude',
          'property.longitude',
          'property.mobile_number',
          'property.email_id',
          'property.property_desc',
          'property.plot_area',
          'property.Plot_construction_area',
          'property.Plot_empty_area',
          'property.carpet_area',
          'property.exempted_area',
          'property.assessable_area',
          'property.land_cost',
          'property.standard_rate',
          'property.annual_rent',
          'property.capital_value',
          'property.property_remark',
          'property.snp_ward',
          'property.zone_code',
          'property.gat_no',
          'property.gis_number',
          'property.note',
          'property.survey_person_code',
          'property.survey_date',
          'property.updateStatus',
          'register_number.register_name',
          'register_number.register_id',
          'zone.zoneName',
          'ward.ward_name',
          'street.street_name',

          'ownerDetails.property_owner_details_id',
          'ownerDetails.name',
          'ownerDetails.is_payer',

          'ownerDetails.owner_type_id',
          'ownerDetails.mobile_number',
          'ownerDetails.email_id',
          'ownerDetails.aadhar_number',
          'owner_type.owner_type',

          // 'usageDetails.property_usage_details_id',
          // 'usageDetails.are_sq_meter',
          // 'usageDetails.construction_area',
          // 'usageDetails.length',
          // 'usageDetails.width',
          // 'usageDetails.are_sq_ft',
          // 'usageDetails.Building_age',
          // 'usageDetails.floor',
          // 'usageDetails.flat_no',
          // 'usageDetails.authorized',
          // 'usageDetails.construction_end_date',
          // 'property_type.propertyType',
          // 'usageType.usage_type',
          // 'usageSubType.usage_sub_type',

          //Warhsik Kar Details

          'warshikKar.warshik_karId',
          'warshikKar.financial_year',
          'warshikKar.all_property_tax_sum_total',
          'warshikKar.all_property_tax_sum',
          'warshikKar.all_property_tax_sum_current',
          'warshikKar.tax_type_1',
          'warshikKar.tax_type_1_current',
          'warshikKar.tax_type_1_previous',
          'warshikKar.tax_type_2',
          'warshikKar.tax_type_2_current',
          'warshikKar.tax_type_2_previous',
          'warshikKar.tax_type_3',
          'warshikKar.tax_type_3_current',
          'warshikKar.tax_type_3_previous',
          'warshikKar.tax_type_4',
          'warshikKar.tax_type_4_current',
          'warshikKar.tax_type_4_previous',
          'warshikKar.tax_type_5',
          'warshikKar.tax_type_5_current',
          'warshikKar.tax_type_5_previous',
          'warshikKar.tax_type_6',
          'warshikKar.tax_type_6_current',
          'warshikKar.tax_type_6_previous',
          'warshikKar.tax_type_7',
          'warshikKar.tax_type_7_current',
          'warshikKar.tax_type_7_previous',
          'warshikKar.tax_type_8',
          'warshikKar.tax_type_8_current',
          'warshikKar.tax_type_8_previous',
          'warshikKar.tax_type_9',
          'warshikKar.tax_type_9_current',
          'warshikKar.tax_type_9_previous',
          'warshikKar.tax_type_10',
          'warshikKar.tax_type_10_current',
          'warshikKar.tax_type_10_previous',
          'warshikKar.other_tax_sum_tax',
          'warshikKar.other_tax_sum_tax_current',
          'warshikKar.other_tax_sum_tax_previous',
          'warshikKar.total_tax',
          'warshikKar.total_tax_previous',
          'warshikKar.total_tax_current',
          'warshikKar.property_type_discount',

          // Add a sum of capital_value from milkatKarTax
          // 'SUM(milkatKarTax.capital_value) AS totalCapitalValue',
        ])
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.street', 'street')
                .leftJoin('property.register_number', 'register_number')

        .leftJoin('property.property_owner_details', 'ownerDetails')
        // .leftJoin('property.property_usage_details', 'usageDetails')
        .leftJoin('property.warshikKar', 'warshikKar')
        // .leftJoin('milkatKar.milkatKarTax', 'milkatKarTax')
        // .leftJoin('milkatKarTax.property_usage_details', 'usageDetails')
        // .leftJoin('usageDetails.propertyType', 'property_type')
        // .leftJoin('usageDetails.usageType', 'usageType')
        // .leftJoin('usageDetails.usageSubType', 'usageSubType')
        .leftJoin('ownerDetails.owner_type', 'owner_type')
        .where(`${searchField} = :value AND warshikKar.status = 'active'`, {
          value,
        })
        .andWhere('warshikKar.financial_year = :financial_year', {
          financial_year: financial_year,
        })
        // Assuming the relationship is correctly defined
        // .where('property.propertyNumber = :propertyNumber', {
        //   propertyNumber: propertyNumber,
        // })
        //.groupBy('property.property_id') // Group by property to get the sum
        .getOne();
    } catch (error) {
      throw error;
    }
  }

  async getWarshikKarAll(
    ward: string,
    value: string,
    searchOn: string,
    financial_year: string,
    options: PaginationOptions,
  ) {
    try {
      const queryBuilder = this.propertyMasterRepository
        .createQueryBuilder('property')
        .select([
          'property.property_id',
          'property.sr_no',
          'property.propertyNumber',
          'property.old_propertyNumber',
          'property.city_survey_number',
          'property.address',
          'property.house_or_apartment_name',
          'zone.zoneName',
          'ward.ward_name',
          'street.street_name',
          'ownerDetails.property_owner_details_id',
          'ownerDetails.name',
          'ownerDetails.owner_type_id',
          'ownerDetails.mobile_number',
          'ownerDetails.email_id',
          'ownerDetails.aadhar_number',
          'owner_type.owner_type',
          'warshikKar.warshik_karId',
          'warshikKar.financial_year',
          'warshikKar.all_property_tax_sum_total',
          'warshikKar.all_property_tax_sum',
          'warshikKar.all_property_tax_sum_current',
          'warshikKar.tax_type_1',
          'warshikKar.tax_type_1_current',
          'warshikKar.tax_type_1_previous',
          'warshikKar.tax_type_2',
          'warshikKar.tax_type_2_current',
          'warshikKar.tax_type_2_previous',
          'warshikKar.tax_type_3',
          'warshikKar.tax_type_3_current',
          'warshikKar.tax_type_3_previous',
          'warshikKar.tax_type_4',
          'warshikKar.tax_type_4_current',
          'warshikKar.tax_type_4_previous',
          'warshikKar.tax_type_5',
          'warshikKar.tax_type_5_current',
          'warshikKar.tax_type_5_previous',
          'warshikKar.tax_type_6',
          'warshikKar.tax_type_6_current',
          'warshikKar.tax_type_6_previous',
          'warshikKar.tax_type_7',
          'warshikKar.tax_type_7_current',
          'warshikKar.tax_type_7_previous',
          'warshikKar.tax_type_8',
          'warshikKar.tax_type_8_current',
          'warshikKar.tax_type_8_previous',
          'warshikKar.tax_type_9',
          'warshikKar.tax_type_9_current',
          'warshikKar.tax_type_9_previous',
          'warshikKar.tax_type_10',
          'warshikKar.tax_type_10_current',
          'warshikKar.tax_type_10_previous',
          'warshikKar.other_tax_sum_tax',
          'warshikKar.other_tax_sum_tax_current',
          'warshikKar.other_tax_sum_tax_previous',
          'warshikKar.total_tax',
          'warshikKar.total_tax_previous',
          'warshikKar.total_tax_current',
        ])
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.street', 'street')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('property.warshikKar', 'warshikKar')
        .leftJoin('ownerDetails.owner_type', 'owner_type')
        .where('warshikKar.status = :status', { status: 'active' })
        .andWhere('warshikKar.financial_year = :financial_year', {
          financial_year,
        });

      // Apply the additional filter only if searchOn is not 'all'
      if (searchOn === 'name') {
        queryBuilder.andWhere('ownerDetails.name LIKE :value', {
          value: `%${value}%`,
        });
      } else if (searchOn !== 'all' && searchOn !=='ward') {
        queryBuilder.andWhere(`property.${searchOn} = :value`, { value });
      } else {
        queryBuilder.andWhere('ward.ward_name = :ward', { ward });
      }


      const result = await paginate(queryBuilder, options, 'property');

      // Log the result to debug

      return result;
    } catch (error) {
      console.error('Error in getWarshikKarAll:', error);
      throw error;
    }
  }

  async getWarshikKarAllcount(financial_year: string) {
    try {
      // Determine the field to search on based on the searchOn parameter
      // const searchField = searchOn === 'old_propertyNumber' ? 'property.old_propertyNumber' : 'property.propertyNumber';

      const queryBuilder = this.propertyMasterRepository
        .createQueryBuilder('property')
        .select([
          'property.property_id',
          'property.sr_no',
          'property.propertyNumber',
          'property.city_survey_number',
          'property.address',
          'property.house_or_apartment_name',

          'zone.zoneName',
          'ward.ward_name',
          'street.street_name',

          'ownerDetails.property_owner_details_id',
          'ownerDetails.name',
          'ownerDetails.owner_type_id',
          'ownerDetails.mobile_number',
          'ownerDetails.email_id',
          'ownerDetails.aadhar_number',
          'owner_type.owner_type',

          'warshikKar.warshik_karId',
          'warshikKar.financial_year',

          'warshikKar.total_tax',
          'warshikKar.total_tax_previous',
          'warshikKar.total_tax_current',

          // Add a sum of capital_value from milkatKarTax
          // 'SUM(milkatKarTax.capital_value) AS totalCapitalValue',
        ])
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.street', 'street')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('property.warshikKar', 'warshikKar') // Correct table name here
        .leftJoin('ownerDetails.owner_type', 'owner_type')
        .where(` warshikKar.status = 'active'`, {
          active: 'active',
        })

        .andWhere('warshikKar.financial_year = :financial_year', {
          financial_year: financial_year,
        });
      // Apply the additional filter only if searchOn is not 'all'

      return await queryBuilder
        .orderBy('property.updated_at', 'DESC')
        .getCount();
    } catch (error) {
      throw error;
    }
  }

  async getGloabalSerchData(options: GlobalSearchDto) {
    try {
      const {
        propertyNumber,
        value,
        ward,
        zone,
        name,
        page = 1,
        limit = 10,
      } = options;

      const queryBuilder = this.propertyMasterRepository
        .createQueryBuilder('property')
        .select([
          'property.property_id',
          'property.propertyNumber',
          'zone.zoneName',
          'ward.ward_name',
          'ownerDetails.name',
          'ownerDetails.mobile_number',
          'warshikKar.warshik_karId',
          'warshikKar.financial_year',
          'warshikKar.total_tax',
          'warshikKar.total_tax_previous',
        ])
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('property.property_usage_details', 'usageDetails')
        .leftJoin('property.warshikKar', 'warshikKar');

      // Filter by mobile_number (partial match)

      // Filter by propertyNumber (partial match)
      if (propertyNumber) {
        let property_Number = propertyNumber.trim();

        queryBuilder.andWhere('property.propertyNumber LIKE :propertyNumber', {
          propertyNumber: `${property_Number}%`,
        });
      }

      if (value) {
        const trimmedValue = value.trim();

        // Prioritize old_propertyNumber
        queryBuilder.andWhere(
          new Brackets((qb) => {
            const oldPropertyCondition =
              'property.old_propertyNumber LIKE :old_propertyNumber';
            const mobileCondition =
              'ownerDetails.mobile_number LIKE :mobile_number';

            qb.where(oldPropertyCondition, {
              old_propertyNumber: `${trimmedValue}%`,
            }).orWhere(mobileCondition, { mobile_number: `${trimmedValue}%` });
          }),
        );
      }

      // Filter by ward, zone, and name together
      if (ward || zone || name) {
        if (ward) {
          queryBuilder.andWhere('ward.ward_id = :ward', { ward });
        }
        if (zone) {
          queryBuilder.andWhere('zone.zone_id = :zone', { zone });
        }
        if (name) {
          let fullNmae = name.trim();
          queryBuilder.andWhere('ownerDetails.name ILIKE :fullName', {
            fullName: `%${fullNmae}%`,
          });
        }
      }

      // Add pagination (limit and offset)
      const skip = (page - 1) * limit;
      queryBuilder.skip(skip).take(limit);

      // Execute the query and get the results
      const [data, totalItems] = await queryBuilder.getManyAndCount();

      // Return paginated response
      return {
        data,
        currentPage: page,
        totalPages: Math.ceil(totalItems / limit),
        totalItems,
      };
    } catch (error) {
      throw error;
    }
  }

  async getPublicUserData(options: any) {
    try {
      const {
        mobile_number,
        propertyNumber,
        ward,
        zone,
        name,
        page = 1,
        limit = 10,
      } = options;
      const queryBuilder = this.propertyMasterRepository
        .createQueryBuilder('property')
        .select([
          'property.property_id',
          'property.propertyNumber',
          'zone.zoneName',
          'ward.ward_name',
          'ownerDetails.name',
          'ownerDetails.mobile_number',
          'warshikKar.warshik_karId',
          'warshikKar.financial_year',
          'warshikKar.total_tax',
          'warshikKar.total_tax_previous',
        ])
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('property.property_usage_details', 'usageDetails')
        .leftJoin('property.warshikKar', 'warshikKar');

      // Filter by mobile_number (partial match)
      if (mobile_number) {
        queryBuilder.where('ownerDetails.mobile_number LIKE :mobile_number', {
          mobile_number: `${mobile_number}%`,
        });
      }

      // Filter by propertyNumber (partial match)
      if (propertyNumber) {
        queryBuilder.where('property.propertyNumber LIKE :propertyNumber', {
          propertyNumber: `${propertyNumber}%`,
        });
      }

      // Filter by ward, zone, and fullName together
      if (ward || zone || name) {
        if (ward) {
          queryBuilder.andWhere('ward.ward_id = :ward', { ward });
        }
        if (zone) {
          queryBuilder.andWhere('zone.zone_id = :zone', { zone });
        }
        if (name) {
          queryBuilder.andWhere('ownerDetails.name ILIKE :fullName', {
            fullName: `%${name}%`,
          });
        }
      }

      // Add pagination (limit and offset)
      const skip = (page - 1) * limit;
      queryBuilder.skip(skip).take(limit);

      // Execute the query and get the results
      const [data, totalItems] = await queryBuilder.getManyAndCount();

      // Return paginated response
      return {
        data,
        currentPage: page,
        totalPages: Math.ceil(totalItems / limit),
        totalItems,
      };
    } catch (error) {
      throw error;
    }
  }
  async getPropertyCountByType() {
    const propertyCounts = await this.propertyMasterRepository
      .createQueryBuilder('property')
      .select('ptm.property_type', 'propertyType')
      .addSelect('COUNT(DISTINCT property.property_id)', 'propertyCount')
      .leftJoin('property.property_usage_details', 'pud')
      .leftJoin('pud.propertyType', 'ptm') // Assuming 'propertyType' is the relation in PropertyUsageDetails
      .groupBy('ptm.property_type')
      .getRawMany();

    const totalCount = await this.propertyMasterRepository
      .createQueryBuilder('property')
      .select('COUNT(DISTINCT property.property_id)', 'totalPropertyCount')
      .getRawOne();

    return [
      ...propertyCounts,
      { propertyType: 'Total', propertyCount: totalCount.totalPropertyCount },
    ];
  }

  async getCountOfProperty(value: string, searchOn: string) {
    try {
      // Determine the field to search on based on the searchOn parameter
      // const searchField = searchOn === 'old_propertyNumber' ? 'property.old_propertyNumber' : 'property.propertyNumber';
      const searchField = 'property.' + searchOn;
      return await this.propertyMasterRepository
        .createQueryBuilder('property')
        .select(['property.property_id'])
        .where(`${searchField} = :value `, {
          value,
        })

        .getOne();
    } catch (error) {
      throw error;
    }
  }

  //check property number
  async checkPropertyNumber(propertyNumber: string) {
    try {
      return await this.propertyMasterRepository.find({
        select: ['property_id'],
        where: {
          propertyNumber: propertyNumber,
        },
      });
    } catch (error) {
      throw error;
    }
  }

  async findCount() {
    const totalProperty = await this.propertyMasterRepository.count();
    return totalProperty;
  }

  async findTotalForWard(ward: string): Promise<number> {
    const result = await this.createQueryBuilder('property_master')
      .select('COUNT(property_master.property_id)', 'total')
      .leftJoin('property_master.ward', 'ward')
      .where('ward.ward_name = :ward', { ward })
      .andWhere('property_master.deleted_at IS NULL') // Ensure only non-deleted records are counted
      .groupBy('ward.ward_id') // Group by ward_id to satisfy SQL requirements
      .getRawOne();

    return result ? parseInt(result.total, 10) : 0;
  }

  async findTotalWardWise(): Promise<
    {
      ward_id(ward_id: any): { collectorId: string; userName: string };
      ward_name: string;
      total: number;
    }[]
  > {
    return await this.createQueryBuilder('property_master')
      .select('ward.ward_name', 'ward_name')
      .addSelect('COUNT(property_master.property_id)', 'total')
      .leftJoin('property_master.ward', 'ward') // Join the ward table
      .where('property_master.deleted_at IS NULL') // Ensure only non-deleted records are counted
      .groupBy('ward.ward_name') // Group by ward name
      .getRawMany();
  }
  async getRegisterNineData(
    value: string,
    searchOn: string,
    fy: string,
    options: PaginationOptions,
  ) {
    try {
      options.showAll = true;
      const queryBuilder = this.propertyMasterRepository
        .createQueryBuilder('property')
        .select([
          'property.property_id',
          'property.propertyNumber',
          'property.old_propertyNumber',
          'property.sr_no',

          'ward.ward_name',
          'street.street_name',
          'ownerDetails.name',
          'ownerDetails.owner_type_id',
          'ownerDetails.property_owner_details_id',
          'ownerDetails.createdAt',

          'paymentInfo.payment_date',
          'paymentInfo.tax_payer_name',

          'receipt.book_number',
          'receipt.financial_year',
          'receipt.book_receipt_number',
          'receipt.financial_year',

          // 'property_type.propertyType',
          // 'usageType.usage_type',
        ])
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.street', 'street')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('property.payments', 'paymentInfo')
        .leftJoin('paymentInfo.receipts', 'receipt')

        .leftJoinAndSelect('property.demandReportData', 'demandReportData')
        // .leftJoin('usageDetails.propertyType', 'property_type')
        // .leftJoin('usageDetails.usageType', 'usageType')
        .leftJoin('ownerDetails.owner_type', 'owner_type')
        .orderBy('demandReportData.createdAt', 'ASC'); // Order by createdAt in ascending order


      if (value && searchOn) {
        let query = '';
        let parameters = {};

        switch (searchOn) {
          case 'propertyNumber':
          case 'old_propertyNumber':
            query = `property.${searchOn} = :value`;
            parameters = { value };
            break;

          case 'ward_name':
            query = 'ward.ward_name = :value';
            parameters = { value };
            break;
          case 'owner_details_name':
            query = 'ownerDetails.name LIKE :partialValue';
            parameters = { partialValue: `${value}%` };
            break;

          default:
            throw new BadRequestException('Invalid searchOn parameter');
        }

        queryBuilder.andWhere(query, parameters);
      }

      return await paginate(queryBuilder, options, 'property');
    } catch (error) {
      throw error;
    }
  }
  async findByPropertyNumber(
    propertyNumber: string,
  ): Promise<PropertyEntity | null> {
    return this.findOne({ where: { propertyNumber } });
  }

  async findOneWithRelations(
    propertyId: string,
  ): Promise<PropertyEntity | null> {
    return this.findOne({
      where: { property_id: propertyId },
      relations: ['property_owner_details', 'property_usage_details'],
    });
  }

  async getReassessmentRangeById(reassessmentYearId: string) {
    try {
      return await this.propertyMasterRepository.manager
        .createQueryBuilder()
        .select([
          'reassessment_range.reassessment_range_id',
          'reassessment_range.start_range',
          'reassessment_range.end_range',
          'reassessment_range.is_active',
          'reassessment_range.is_current',
          'reassessment_range.is_published',
        ])
        .from('reassessment_range', 'reassessment_range')
        .where(
          'reassessment_range.reassessment_range_id = :reassessmentYearId',
          { reassessmentYearId },
        )
        .getOne();
    } catch (error) {
      throw error;
    }
  }

  async getPropertyDataForExport(financial_year?: string) {
    try {
      const queryBuilder = this.propertyMasterRepository
        .createQueryBuilder('property')
        .select([
          'property.property_id',
          'property.propertyNumber',
          'property.old_propertyNumber',
          'property.sr_no',
          'zone.zoneName',
          'ward.ward_name',
          'ownerDetails.name',
         
          'usageDetails.length',
          'usageDetails.width',
          'usageDetails.are_sq_ft',
          'usageDetails.are_sq_meter',
          'usageDetails.construction_area',
          'usageDetails.floor',
          'propertyType.propertyType',
          'usageType.usage_type',
        ])
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('property.property_usage_details', 'usageDetails')
        .leftJoin('usageDetails.propertyType', 'propertyType')
        .leftJoin('usageDetails.floorType', 'floor')

        .leftJoin('usageDetails.usageType', 'usageType')
        .leftJoinAndSelect('property.warshikKar', 'warshikKar')
        .where('warshikKar.status = :status', { status: 'active' });

      if (financial_year) {
        queryBuilder.andWhere('warshikKar.financial_year = :financial_year', {
          financial_year,
        });
      }

      queryBuilder.orderBy('property.sr_no', 'ASC');

      return await queryBuilder.getMany();
    } catch (error) {
      throw error;
    }
  }


   async getPropertiesWithDemand(): Promise<any[]> {
    return this.createQueryBuilder('property')
      .leftJoinAndSelect('property.ownerDetails', 'owner')
      .leftJoinAndSelect('property.ward', 'ward')
      .leftJoinAndSelect('property.warshikKar', 'warshikKar')
      .select([
        'property.propertyNumber',
        'property.old_propertyNumber',
        'owner.name as ownerName',
        'owner.mobile_number as mobileNumber',
        'ward.ward_name as wardName',
        'warshikKar.total_tax_previous as total_prev',
        'warshikKar.total_tax as total',
        'warshikKar.all_property_tax_sum_prev',
        'warshikKar.all_property_tax_sum_cur',
        'warshikKar.all_property_tax_sum',
        'warshikKar.tax_type_1_previous as vrksha_upkar_magil',
        'warshikKar.tax_type_1_current as vrksha_upkar_chalu',
        'warshikKar.tax_type_1 as vrksha_upkar',
        'warshikKar.tax_type_2_previous as shikshan_upkar_magil',
        'warshikKar.tax_type_2_current as shikshan_upkar_chalu',
        'warshikKar.tax_type_2 as shikshan_upkar',
        'warshikKar.tax_type_3_previous as rojgar_hami_magil',
        'warshikKar.tax_type_3_current as rojgar_hami_chalu',
        'warshikKar.tax_type_3 as rojgar_hami',
        'warshikKar.tax_type_4_previous as ghanakachra_shulk_magil',
        'warshikKar.tax_type_4_current as ghanakachra_shulk_chalu',
        'warshikKar.tax_type_4 as ghanakachra_shulk',
        'warshikKar.tax_type_5_previous as anadhikrut_shasti_magil',
        'warshikKar.tax_type_5_current as anadhikrut_shasti_chalu',
        'warshikKar.tax_type_5 as anadhikrut_shasti',
        'warshikKar.tax_type_6_previous as divabatti_magil',
        'warshikKar.tax_type_6_current as divabatti_chalu',
        'warshikKar.tax_type_6 as divabatti',
        'warshikKar.tax_type_7_previous as arogya_magil',
        'warshikKar.tax_type_7_current as arogya_chalu',
        'warshikKar.tax_type_7 as arogya',
        'warshikKar.tax_type_8_previous as padsar_magil',
        'warshikKar.tax_type_8_current as padsar_chalu',
        'warshikKar.tax_type_8 as padsar',
        'warshikKar.tax_type_9_previous as dand_magil',
        'warshikKar.tax_type_9_current as dand_chalu',
        'warshikKar.tax_type_9 as dand',
        'warshikKar.tax_type_10_previous as agnishaman_magil',
        'warshikKar.tax_type_10_current as agnishaman_chalu',
        'warshikKar.tax_type_10 as agnishaman'
      ])
      .where('warshikKar.status = :status', { status: 'active' })
      .getRawMany();
  }
}
