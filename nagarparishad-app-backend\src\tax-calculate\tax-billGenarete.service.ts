import {
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';

import {
  Tax_PropertyWiseRepository,
  Tax_FY_RecordsRepository,
  Tax_PropertyRepository,
  PropertyMasterRepository,
  WarshikKarRepository,
  BillDataRepository
 
} from 'libs/database/repositories';
import { Tax_propertyFYDto } from './dto/tax-calculate.dto';
// import { format } from 'date-fns'; // Make sure to install date-fns for date formatting

@Injectable()
export class Tax_billGenerateService {
  constructor(
    private readonly tax_PropertyRepository: Tax_PropertyRepository,
    private readonly tax_FY_RecordsRepository: Tax_FY_RecordsRepository,
    private readonly propertyMasterRepository: PropertyMasterRepository,
    private readonly warshikKarRepository: WarshikKarRepository,
    private readonly billDataRepository:BillDataRepository
  ) {}
  // create(createTaxCalculateDto: CreateTaxCalculateDto) {
  //   return 'This action adds a new taxCalculate';
  // }

  // async generate_bills(tax_propertyFYDto: Tax_propertyFYDto) {
  //   const { financial_year } = tax_propertyFYDto;
  
  //   // Get data of all property master
  //   const allWarshikKarData: any = await this.warshikKarRepository.findAllData(); 
  
  //   if (allWarshikKarData.length > 0) {
  //     const updatedWarshikKarData = [];
  
  //     for (const warshikData of allWarshikKarData) {
  //       // If billNo is null, generate a new one
  //       if (!warshikData.billNo) {
  //         const newBillNo = await this.generateBillNo(financial_year, warshikData.property.sr_no);
  //         warshikData.billNo = newBillNo;
  
  //         // Update the bill generation date to current date
  //         warshikData.bill_generation_date = new Date();
  
  //         // Save the updated data back to the repository
  //         await this.warshikKarRepository.save(warshikData);
  //         updatedWarshikKarData.push(warshikData);
  //       }
  //     }
  
  //     return {
  //       message: 'Bills generated and updated successfully',
  //       data: updatedWarshikKarData,
  //     };
  //   } else {
  //     return {
  //       message: 'Properties exist. No need to generate bills.',
  //       data: financial_year,
  //     };
  //   }
  // }

  // async generate_bills(tax_propertyFYDto: Tax_propertyFYDto) {
  //   const { financial_year } = tax_propertyFYDto;
  
  //   // Get data of all property master
  //   const allWarshikKarData: any = await this.warshikKarRepository.findAllData();
  
  //   if (allWarshikKarData.length > 0) {
  //     const insertedBillData = [];
  
  //     for (const warshikData of allWarshikKarData) {
  //       //  //       // Check if the property exists in the property table
  //       const property_data = await this.propertyMasterRepository.findOne({
  //         where: { property_id: warshikData.property.property_id }, // Assuming warshikData has the property ID
  //         select: ['property_id','propertyNumber'], // Only fetch required fields
  //       });
        
  //       //  
  //       if (property_data) {
  //         // Check if a billNo already exists for this property_id in the billdata table
          
  //         const existingBill = await this.billDataRepository.findOne({
  //           where: { property: { property_id: property_data.property_id } },
  //           select: ['billNo'], // Only fetch billNo
  //         });
          
  
  //         if (!existingBill) {
  //           // Generate a new bill number if none exists
  //           const newBillNo = await this.generateBillNo(financial_year, warshikData.property.sr_no);
  
  //           // Prepare the bill data for insertion
  //           const billData = {
  //             billNo: newBillNo,
  //             bill_generation_date: new Date(),
  //             property_id: property_data.property_id,
  //             property_number: property_data.propertyNumber,
  //             fyear: financial_year,
  //           };
  
  //           // Insert the bill data into the billdata table
  //           await this.billDataRepository.save(billData);
  //           insertedBillData.push(billData);
  //         } else {
  //             //         }
  //       } else {
  //         console.error(`Property not found for warshikData with ID: ${warshikData.id}`);
  //       }
  //     }
  
  //     return {
  //       message: 'Bills generated and inserted successfully',
  //       data: insertedBillData,
  //     };
  //   } else {
  //     return {
  //       message: 'Properties exist. No need to generate bills.',
  //       data: financial_year,
  //     };
  //   }
  // }

  async generate_bills(tax_propertyFYDto: Tax_propertyFYDto) {
    const { financial_year } = tax_propertyFYDto;
  
    // Fetch all WarshikKar data
    const allWarshikKarData: any = await this.warshikKarRepository.findAllData(financial_year);
  
    if (allWarshikKarData.length === 0) {
      return {
        message: 'No properties found. No bills generated.',
        data: [],
      };
    }
  
    const insertedBillData = [];
  
    for (const warshikData of allWarshikKarData) {
      const property_id = warshikData.property.property_id;
  
      // Fetch property details
      const property_data = await this.propertyMasterRepository.findOne({
        where: { property_id },
        select: ['property_id', 'propertyNumber'],
      });
  
      if (!property_data) {
        console.error(`Property not found for ID: ${property_id}`);
        continue;
      }
  
      // Check if a bill already exists
      const existingBill = await this.billDataRepository.findOne({
        where: { property: { property_id: property_data.property_id }, fyear: financial_year },
        select: ['id', 'billNo'],
      });
  
      if (existingBill) {
                continue;
      }
  
      // Generate a new bill number
      const newBillNo = await this.generateBillNo(financial_year, warshikData.property.sr_no);
  
      // Insert new bill data
      const billData = this.billDataRepository.create({
        billNo: newBillNo,
        bill_generation_date: new Date(),
        property: property_data,
        property_number: property_data.propertyNumber,
        fyear: financial_year,
      });
  
      await this.billDataRepository.save(billData);
      insertedBillData.push(billData);
  
      await this.warshikKarRepository.update(
        { warshik_karId: warshikData.warshik_karId },
        { billNo: newBillNo, bill_generation_date: new Date() } 
      );
    }
  
    return {
      message: 'Bills generated and inserted successfully',
      data: insertedBillData,
    };
  }
  

  async generate_billsForPropertyNumber(tax_propertyFYDto: any) {
    const { financial_year, propertyNumber } = tax_propertyFYDto;
  
    // Fetch property details for the given property number
    const property_data = await this.propertyMasterRepository.findOne({
      where: { propertyNumber },
      select: ['property_id', 'propertyNumber','sr_no'],
    });
  
    if (!property_data) {
      return {
        message: `Property not found for property number: ${propertyNumber}`,
        data: [],
      };
    }
  
    // Check if a bill already exists for the given property number
    const existingBill = await this.billDataRepository.findOne({
      where: { property: { property_id: property_data.property_id }, fyear: financial_year },
      select: ['id', 'billNo'],
    });
  
    if (existingBill) {
      return {
        message: `Bill already exists for property number: ${propertyNumber}`,
        data: [],
      };
    }
  
    // Generate a new bill number
    const newBillNo = await this.generateBillNo(financial_year, property_data.sr_no);
  
    // Insert new bill data
    const billData = this.billDataRepository.create({
      billNo: newBillNo,
      bill_generation_date: new Date(),
      property: property_data,
      property_number: property_data.propertyNumber,
      fyear: financial_year,
    });
  
    await this.billDataRepository.save(billData);
  
    return {
      message: 'Bill generated and inserted successfully',
      data: [billData],
    };
  }
  
  
  
  async generateBillNo(financialYear: string, srNo: number): Promise<string> {
    // Extract year from financial year (e.g., "2023-24")
    const yearPart = financialYear.replace('/', '-');
  
    // Get the current timestamp in milliseconds
    const timestamp = Date.now();
  
    // Extract a unique part from the timestamp (e.g., last 5 digits)
    const uniquePart = timestamp.toString().slice(-5);
  
    // Generate a random 2-digit number for additional randomness
    const randomPart = Math.floor(10 + Math.random() * 90);
  
    // Combine the unique part and the random part to form a 7-digit number
    const uniqueRandomNumber = parseInt(uniquePart + randomPart.toString().padStart(2, '0'), 10);
  
    // Generate the bill number in the required format: "SNP/<year>/<uniqueRandomNumber>"
    const billNo = `SNP/${yearPart}/${srNo}/${uniqueRandomNumber.toString().padStart(7, '0')}`;
  
    return billNo;
  }
  
  

  async generateTax(property_master_id: string) {
    // Implement your tax generation logic here
    Logger.log(`Generating tax for property id: ${property_master_id}`);

    // Example logic
    // const taxData = await this.calculateTax(propertyNumber);
    // await this.tax_PropertyRepository.save(taxData);
  }
  async getAreaInSqm(property_master_id)
  {
    return 1200;
    //formula logic here
  }
  // async getAreaInSqm(property_master_id)
  // {
  //   return 1200;
  //   //formula logic here
  // }
  async getReadyRecknerRate()
  {
    return 1000;
     //formula logic here
  }
  async getReadyRecknerConstructionRate()
  {
    return 1000;
     //formula logic here
  }
  async getDepreciationRate()
  {
    return 1000;
     //formula logic here
  }
  async getWeighting_rate()
  {
    return 1000;
     //formula logic here
  }
  async getCapitalValue()
  {
    return 1000;
     //formula logic here
  }
  async getTaxrRate()
  {
    return 1000;
     //formula logic here
  }
  async getTax()
  {
    return 1000;
     //formula logic here
  }
  async getOtherTax()
  {
    return 1000;
     //formula logic here
  }
  // async getReadyRecknerRate()
  // {
  //   return 1000;
  //    //formula logic here
  // }
  // async getReadyRecknerRate()
  // {
  //   return 1000;
  //    //formula logic here
  // }
  // async getReadyRecknerRate()
  // {
  //   return 1000;
  //    //formula logic here
  // }
  // async getReadyRecknerRate()
  // {
  //   return 1000;
  //    //formula logic here
  // }
  // async getReadyRecknerRate()
  // {
  //   return 1000;
  //    //formula logic here
  // }
  // async getReadyRecknerRate()
  // {
  //   return 1000;
  //    //formula logic here
  // }
  // async getReadyRecknerRate()
  // {
  //   return 1000;
  //    //formula logic here
  // }
  // async getReadyRecknerRate()
  // {
  //   return 1000;
  //    //formula logic here
  // }

  async getDataOfALLProperty()
  {

  }

 
}
