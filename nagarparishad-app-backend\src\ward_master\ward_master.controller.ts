import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { WardMasterService } from './ward_master.service';
import { CreateWardMasterDto } from './dto/create-ward_master.dto';
import { UpdateWardMasterDto } from './dto/update-ward_master.dto';
import { WardIdDto } from './dto/ward-id.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';
import { Public } from '@jwt/jwt-auth/decorators/public.decorator';

@ApiTags('Ward Master')
@Controller('ward-master')
export class WardMasterController {
  constructor(private readonly wardMasterService: WardMasterService) {}

  @Form('Ward')
  @Permissions('can_write')
  @ApiOperation({ summary: 'Create a new ward' })
  @ApiResponse({
    status: 201,
    description: 'The ward has been successfully created',
  })
  @Post('create')
  create(@Body() createWardMasterDto: CreateWardMasterDto) {
    return this.wardMasterService.create(createWardMasterDto);
  }
  @Form('Ward')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all wards' })
  @ApiResponse({ status: 200, description: 'Returns all wards' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.wardMasterService.findAllWard();
  }

  @Form('Ward')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one ward' })
  @ApiResponse({ status: 200, description: 'Returns Single ward' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() wardIdDto: WardIdDto) {
    return this.wardMasterService.findOne(wardIdDto);
  }

  @Form('Ward')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Update a ward by ID' })
  @ApiResponse({
    status: 200,
    description: 'The ward has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Ward not found' })
  @Patch('update')
  update(
    @Query() wardIdDto: WardIdDto,
    @Body() updateWardMasterDto: UpdateWardMasterDto,
  ) {
    return this.wardMasterService.update(wardIdDto, updateWardMasterDto);
  }

  @Form('Ward')
  @Permissions('can_delete')
  @ApiOperation({ summary: 'Delete a ward by ID' })
  @ApiResponse({
    status: 200,
    description: 'The ward has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Ward not found' })
  @Delete('delete')
  remove(@Query() wardIdDto: WardIdDto) {
    return this.wardMasterService.remove(wardIdDto);
  }

  @Public()
  @ApiOperation({ summary: 'Get all wards (Public API for home page)' })
  @ApiResponse({ status: 200, description: 'Returns all wards without authentication' })
  @Get('public')
  findAllPublic() {
    return this.wardMasterService.findAllWard();
  }
}
