import React, { createContext, useContext, useState, ReactNode } from "react";

interface StepContextType {
  currentStep: number;
  handleNextStep: () => void;
  handlePreviousStep: () => void;
}

const WizardContext = createContext<StepContextType | undefined>(undefined);

export const StepProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [currentStep, setCurrentStep] = useState<number>(1);

  const handleNextStep = () => {
    setCurrentStep((prevStep) => prevStep + 1);
  };

  const handlePreviousStep = () => {
    setCurrentStep((prevStep) => prevStep - 1);
  };

  const value: StepContextType = {
    currentStep,
    handleNextStep,
    handlePreviousStep,
  };

  return <WizardContext.Provider value={value}>{children}</WizardContext.Provider>;
};

export const useStep = (): StepContextType => {
  const context = useContext(WizardContext);

  if (!context) {
    throw new Error("useStep must be used within a StepProvider");
  }

  return context;
};

export default WizardContext;
