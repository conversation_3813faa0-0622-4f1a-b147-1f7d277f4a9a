import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  BaseEntity,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
  OneToOne,
} from 'typeorm';

import { StreetMasterEntity } from './streetMaster.entity';
import { Ward_Master } from './ward_master.entity';
import { ZoneMaster } from './zoneMaster.entity';
import { Property_Owner_Details_Entity } from './property-owner-details.entity';
import { Property_Usage_Details_Entity } from './property-usage-details.entity';
import { MilkatKarEntity } from './milkat-kar.entity';
import { Tax_PropertyEntity } from './tax_property.entity';
import { MilkatKarTaxEntity } from './milkatKarTax.entity';
import { CommonFiledsOfPropertyEntity } from './commonFiledsOfProperty.entity';
import { WarshilKarEntity } from './warshik_kar.entity';
import { BillDataEntity} from './billData.entity'
import { PaymentInfoEntity } from './payment_info.entity';
import { PreviousOwnerEntity } from './previous_property_owners.entity';
import { DemandReportData } from './demandeReportData.entity';
import { ReceiptEntity } from './receipt.entity';
import { RegisterNumberEntity } from './register_number.entity';

@Entity('property')
export class PropertyEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  property_id: string;

  @Column({ type: 'varchar', nullable: true })
  newPropertyNumber: string;

  @Column({ type: 'varchar', nullable: true })
  propertyNumber: string;

  @Column({ type: 'varchar', nullable: true })
  old_propertyNumber: string;


  @Column({ type: 'varchar', nullable: true })
  parent_propertyNumber: string;


  @Column({ type: 'varchar', nullable: true })
  city_survey_number: string;

  @Column({ type: 'varchar', nullable: true })
  address: string;

  @Column({ type: 'varchar', nullable: true })
  house_or_apartment_name: string;

  @ManyToOne(() => StreetMasterEntity, (street) => street.street_id, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'street_id' })
  street: StreetMasterEntity;

  @Column({ type: 'varchar', nullable: true })
  latitude: string;

  @Column({ type: 'varchar', nullable: true })
  longitude: string;

  @ManyToOne(() => Ward_Master, (ward) => ward.ward_id, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'ward_id' })
  ward: Ward_Master;


  
  @ManyToOne(() => RegisterNumberEntity, (ward) => ward.register_id, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'register_number' })
  register_number: RegisterNumberEntity;

  @ManyToOne(() => ZoneMaster, (zone) => zone.zone_id, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'zone_id' })
  zone: ZoneMaster;

  @Column({ type: 'varchar', nullable: true })
  mobile_number: string;

  @Column({ type: 'varchar', nullable: true })
  email_id: string;

  @Column({ type: 'json', nullable: true })
  uploaded_files: string[];

  @Column({ type: 'varchar', nullable: true })
  property_desc: string;

  @Column({ type: 'float', nullable: true })
  plot_area: number;

  @Column({ type: 'float', nullable: true })
  Plot_construction_area: number;

  @Column({ type: 'float', nullable: true })
  Plot_empty_area: number;

  @Column({ type: 'float', nullable: true })
  construction_area: number;

  @Column({ type: 'float', nullable: true })
  carpet_area: number;

  @Column({ type: 'float', nullable: true })
  exempted_area: number;

  @Column({ type: 'float', nullable: true })
  assessable_area: number;

  @Column({ type: 'float', nullable: true })
  land_cost: number;

  @Column({ type: 'float', nullable: true })
  standard_rate: number;

  @Column({ type: 'float', nullable: true })
  annual_rent: number;

  @Column({ type: 'float', nullable: true })
  capital_value: number;

  @Column({ type: 'varchar', nullable: true })
  property_remark: string;

  @Column({ type: 'varchar', nullable: true })
  landmark: string;

  @Column({ type: 'varchar', nullable: true })
  snp_ward: string;

  @Column({ type: 'varchar', nullable: true })
  zone_code: string;

  @Column({ type: 'varchar', nullable: true })
  gat_no: string;

  @Column({ type: 'varchar', nullable: true })
  gis_number: string;

  @Column({ type: 'varchar', nullable: true })
  note: string;

  @Column({ type: 'varchar', nullable: true })
  ferfarRemark: string;


  @Column({ type: 'varchar', nullable: true })
  fodRemark: string;

  @Column({ type: 'varchar', nullable: true })
  parent_propertyId: string;


  @Column({ type: 'varchar', nullable: true })
  survey_person_code: string[];

  @Column({ type: 'varchar', nullable: true })
  survey_date: string[];

  @Column({ type: 'simple-array', nullable: true })
  property_photographs: string[];

  @Column({ type: 'integer', nullable: true })
  sr_no: number;

  @Column({ type: 'varchar', nullable: true })
  import_property_id: string;

  @OneToMany(
    () => Property_Owner_Details_Entity,
    (ownerDetails) => ownerDetails.property
  )
  property_owner_details: Property_Owner_Details_Entity[];

  
  @OneToMany(
    () => BillDataEntity,
    (billDataEntity) => billDataEntity.property
  )
  billdata: BillDataEntity[];

  @OneToMany(
    () => Property_Usage_Details_Entity,
    (usageDetails) => usageDetails.property 
  )
  property_usage_details: Property_Usage_Details_Entity[];

  @OneToMany(() => MilkatKarEntity, (milkat_Kar) => milkat_Kar.property)
  milkatKar: MilkatKarEntity[];

  @OneToMany(() => MilkatKarTaxEntity, (milkatKarTax) => milkatKarTax.property)
  milkatKarTax: MilkatKarTaxEntity[];

  @OneToMany(() => WarshilKarEntity, (warshik_kar) => warshik_kar.property)
  warshikKar: WarshilKarEntity[];

  @OneToMany(() => Tax_PropertyEntity, (tax_Property) => tax_Property.property)
  tax_Property: Tax_PropertyEntity[];

  @OneToMany(() => PaymentInfoEntity, (payment) => payment.property)
  payments: PaymentInfoEntity[];

  
  @OneToMany(() => PreviousOwnerEntity, (previousOwner) => previousOwner.property)
  previousOwners: PreviousOwnerEntity[];
  
  @OneToMany(() => ReceiptEntity, (receipt) => receipt.property)
  receipt: ReceiptEntity[];

  @OneToMany(() => DemandReportData, (demand) => demand.property)
  demandReportData: DemandReportData[];

  @OneToOne(
    () => CommonFiledsOfPropertyEntity,
    (commonFields) => commonFields.property,
  )
  commonFields: CommonFiledsOfPropertyEntity;

  @Column({ type: String, nullable: true })
  updateStatus: string;

  @Column({ type: 'varchar', nullable: true })
  city: string;

  @Column({ type: 'varchar', nullable: true })
  plot_number: string;

  @Column({ type: 'varchar', nullable: true })
  block_number: string;
  @Column({ type: 'varchar', nullable: true })
  house_number: string;


   

  // @Column({ type: 'boolean', default: false })
  // is_imarat_kar_full_discount: boolean;
  /*
   * Create and Update Date Columns
   */


  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
