import {
  Ward<PERSON><PERSON>Object,
  Ward<PERSON>reateResObject,
  Ward<PERSON><PERSON>dateObject,
} from "@/model/ward-master";
import {
  WARD_LIST,
  ADD_ZONE,
  ZONE_LIST,
  UPDATE_ZONE,
  ADD_WARD,
  UPDATE_WARD,
  DELETE_WARD,
  DELETE_ZONE,
  Location_List,
  ADD_LOCATION,
  UPDATE_LOCATION,
  STREET_List,
  ADD_STREET,
  UPDATE_STREET,
  ELECTION_BOUNDRY_LIST,
  UPDATE_ELECTION_BOUNDRY_LIST,
  ADD_ELECTION_BOUNDRY_LIST,
  DELETE_ELECTION_BOUNDRY_LIST,
  ADD_USAGE,
  UPDATE_USAGE,
  USAGE_LIST,
  DELETE_USAGE,
  AREA_List,
  ADD_AREA,
  UPDATE_AREA,
  DELETE_AREA,
  PROPERTYTYPE_List,
  ADD_PROPERTYTYPE,
  UPDATE_PROPERTYTYPE,
  DELETE_PROPERTYTYPE,
  PROPERTYSUBTYPE_List,
  ADD_PROPERTYSUBTYPE,
  UPDATE_PROPERTYSUBTYPE,
  <PERSON><PERSON><PERSON>_PROPERTYSUBTYPE,
  DELE<PERSON>_STREET,
  <PERSON>LETE_LOCATION,
  USAGE_SUB_LIST,
  UPDATE_USAGE_SUB,
  DELETE_USAGE_SUB,
  ADD_USAGE_SUB,
  CONSTRUCTION_CLASS_LIST,
  UPDATE_CONSTRUCTION_CLASS,
  DELETE_CONSTRUCTION_CLASS,
  ADD_CONSTRUCTION_CLASS,
  ADMINISTRATIVE_BOUNDARY_LIST,
  UPDATE_ADMINISTRATIVE_BOUNDARY,
  ADD_ADMINISTRATIVE_BOUNDARY,
  DELETE_ADMINISTRATIVE_BOUNDARY,
  OWNER_TYPE_LIST,
  PROPERTY_CLASS_LIST,
  PROPERTY_FLOOR_LIST,
  ADD_PROPERTY_CLASS,
  UPDATE_PROPERTY_CLASS,
  DELETE_PROPERTY_CLASS,
  ADD_PROPERTY_FLOOR,
  UPDATE_PROPERTY_FLOOR,
  DELETE_PROPERTY_FLOOR,
  PROPERTYSTATS,
  GET_ALL_COLLECTOR,
  GET_MILKAT_KAR_AKARNI,
  PROCESS_MILKAT_KAR_AKARNI,
  GET_NAMUNA_EIGHT_BILL,
  GET_NAMUNA_NINE,
  PROCESS_WARSHIK_KAR_AKARNI,
  UPDATE_WARSHIK_KAR_AKARNI,
  GET_ALL_REGISTERNUMBER_LIST
} from "@/constant/utils/apiUtils";
import {
  WardMasterApiResponse,
  ZoneObjectInterface,
  ZoneMasterAddApiResp,
} from "@/model/zone-master";
import {
  LocationListAllApi,
  LocationCreateApi,
  LocationUpdateApi,
  LocationSendApiObj,
} from "@/model/location-master";
import {
  StreetCreateApi,
  StreetUpdateApi,
  StreetSendApiObj,
} from "@/model/street-master";
import { ElectionBoundaryListAllApi } from "@/model/electionBoundry";
import { UsageMasterApiResponse } from "@/model/usage-master";
import {
  AreaListAllApi,
  AreaCreateApi,
  AreaUpdateApi,
  AreaSendApiObj,
} from "@/model/area-master";
import {
  PropertytypeCreateApi,
  PropertytypeUpdateApi,
  PropertytypeSendApiObj,
} from "@/model/propertytype-master";
import {
  PropertysubtypeCreateApi,
  PropertysubtypeUpdateApi,
  PropertysubtypeSendApiObj,
} from "@/model/propertysubtype-master";
import {
  UsageSubCreateObject,
  UsageSubMasterApiResponse,
} from "@/model/usagesub-master";
import { ConstructionClassApiResponse } from "@/model/construction-master";
import { AdministrativeBoundaryApiResponse } from "@/model/administrativeboundary-master";
import axios from "axios";
import { UploadProgressEvent } from "@/model/document-upload";
import { PropertyClassCreateApi, PropertyClassMasterListAllApi, PropertyClassSendApiObj, PropertyClassUpdateApi } from "@/model/PropertyClassMaster";
import { PropertyFloorCreateApi, PropertyFloorSendApiObj, PropertyFloorUpdateApi } from "@/model/PropertyFloorMaster";

const baseURL = import.meta.env.VITE_APP_BASE_URL;

// Create axios instance with interceptors
const apiClient = axios.create({
  baseURL: baseURL,
});

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = Api.getStoredToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle 401 and refresh token
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        }).catch((err) => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = Api.getStoredRefreshToken();
        if (!refreshToken) {
          Api.handleLogout();
          return Promise.reject(error);
        }

        const newTokens = await Api.refreshAccessToken(refreshToken);
        if (newTokens.status) {
          const newAccessToken = newTokens.data.data.accessToken;
          const newRefreshToken = newTokens.data.data.refreshToken;

          localStorage.setItem('AccessToken', JSON.stringify(newAccessToken));
          localStorage.setItem('RefreshToken', JSON.stringify(newRefreshToken));

          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

          processQueue(null, newAccessToken);

          return apiClient(originalRequest);
        } else {
          processQueue(error, null);
          Api.handleLogout();
          return Promise.reject(error);
        }
      } catch (refreshError) {
        processQueue(refreshError, null);
        Api.handleLogout();
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

const VITE_BASE_URL = baseURL;
const REACT_APP_ALL_WARD_LIST = VITE_BASE_URL + WARD_LIST;
const REACT_APP_ADD_ZONE = VITE_BASE_URL + ADD_ZONE;
const REACT_APP_ADD_WARD = VITE_BASE_URL + ADD_WARD;
const REACT_APP_ALL_ZONE_LIST = VITE_BASE_URL + ZONE_LIST;
const REACT_APP_DELETE_ZONE = VITE_BASE_URL + DELETE_ZONE;
const REACT_APP_UPDATE_ZONE = VITE_BASE_URL + UPDATE_ZONE;
const REACT_APP_ALL_Location_List = VITE_BASE_URL + Location_List;
const REACT_APP_ADD_LOCATION = VITE_BASE_URL + ADD_LOCATION;
const REACT_APP_UPDATE_LOCATION = VITE_BASE_URL + UPDATE_LOCATION;
const REACT_APP_DELETE_LOCATION = VITE_BASE_URL + DELETE_LOCATION;
const REACT_APP_STREET_List = VITE_BASE_URL + STREET_List;
const REACT_APP_ADD_STREET = VITE_BASE_URL + ADD_STREET;
const REACT_APP_UPDATE_STREET = VITE_BASE_URL + UPDATE_STREET;
const REACT_APP_DELETE_STREET = VITE_BASE_URL + DELETE_STREET;
const REACT_APP_UPDATE_WARD = VITE_BASE_URL + UPDATE_WARD;
const REACT_APP_DELETE_WARD = VITE_BASE_URL + DELETE_WARD;
const REACT_APP_ALL_AREA_List = VITE_BASE_URL + AREA_List;
const REACT_APP_ADD_AREA = VITE_BASE_URL + ADD_AREA;
const REACT_APP_UPDATE_AREA = VITE_BASE_URL + UPDATE_AREA;
const REACT_APP_DELETE_AREA = VITE_BASE_URL + DELETE_AREA;
const REACT_APP_ALL_PROPERTYTYPE_List = VITE_BASE_URL + PROPERTYTYPE_List;
const REACT_APP_ADD_PROPERTYTYPE = VITE_BASE_URL + ADD_PROPERTYTYPE;
const REACT_APP_UPDATE_PROPERTYTYPE = VITE_BASE_URL + UPDATE_PROPERTYTYPE;
const REACT_APP_DELETE_PROPERTYTYPE = VITE_BASE_URL + DELETE_PROPERTYTYPE;
const REACT_APP_ALL_PROPERTYSUBTYPE_List = VITE_BASE_URL + PROPERTYSUBTYPE_List;
const REACT_APP_ADD_PROPERTYSUBTYPE = VITE_BASE_URL + ADD_PROPERTYSUBTYPE;
const REACT_APP_UPDATE_PROPERTYSUBTYPE = VITE_BASE_URL + UPDATE_PROPERTYSUBTYPE;
const REACT_APP_DELETE_PROPERTYSUBTYPE = VITE_BASE_URL + DELETE_PROPERTYSUBTYPE;
const REACT_APP_OWNER_TYPE_LIST = VITE_BASE_URL + OWNER_TYPE_LIST;
const REACT_APP_ALL_PROPERTYTYPESTATS = VITE_BASE_URL + PROPERTYSTATS;
const REACT_APP_ALL_ELECTION_BOUNDRY_LIST = VITE_BASE_URL + ELECTION_BOUNDRY_LIST;
const REACT_APP_UPDATE_ELECTION_BOUNDRY_LIST = VITE_BASE_URL + UPDATE_ELECTION_BOUNDRY_LIST;
const REACT_APP_ADD_ELECTION_BOUNDRY_LIST = VITE_BASE_URL + ADD_ELECTION_BOUNDRY_LIST;
const REACT_APP_DELETE_ELECTION_BOUNDRY_LIST = VITE_BASE_URL + DELETE_ELECTION_BOUNDRY_LIST;
const REACT_APP_ALL_PROPERTY_CLASS_LIST = VITE_BASE_URL + PROPERTY_CLASS_LIST;
const REACT_APP_UPDATE_PROPERTY_CLASS = VITE_BASE_URL + UPDATE_PROPERTY_CLASS;
const REACT_APP_ADD_PROPERTY_CLASS = VITE_BASE_URL + ADD_PROPERTY_CLASS;
const REACT_APP_DELETE_PROPERTY_CLASS = VITE_BASE_URL + DELETE_PROPERTY_CLASS;
const REACT_APP_ALL_PROPERTY_FLOOR_LIST = VITE_BASE_URL + PROPERTY_FLOOR_LIST;
const REACT_APP_UPDATE_PROPERTY_FLOOR = VITE_BASE_URL + UPDATE_PROPERTY_FLOOR;
const REACT_APP_ADD_PROPERTY_FLOOR = VITE_BASE_URL + ADD_PROPERTY_FLOOR;
const REACT_APP_DELETE_PROPERTY_FLOOR = VITE_BASE_URL + DELETE_PROPERTY_FLOOR;
const REACT_APP_ALL_USAGE_LIST = VITE_BASE_URL + USAGE_LIST;
const REACT_APP_UPDATE_USAGE_LIST = VITE_BASE_URL + UPDATE_USAGE;
const REACT_APP_ADD_USAGE_LIST = VITE_BASE_URL + ADD_USAGE;
const REACT_APP_DELETE_USAGE_LIST = VITE_BASE_URL + DELETE_USAGE;
const REACT_APP_ALL_USAGE_SUB_LIST = VITE_BASE_URL + USAGE_SUB_LIST;
const REACT_APP_UPDATE_USAGE_SUB = VITE_BASE_URL + UPDATE_USAGE_SUB;
const REACT_APP_ADD_USAGE_SUB = VITE_BASE_URL + ADD_USAGE_SUB;
const REACT_APP_DELETE_USAGE_SUB = VITE_BASE_URL + DELETE_USAGE_SUB;
const REACT_APP_ALL_CONSTRUCTION_CLASS_LIST = VITE_BASE_URL + CONSTRUCTION_CLASS_LIST;
const REACT_APP_UPDATE_CONSTRUCTION_CLASS = VITE_BASE_URL + UPDATE_CONSTRUCTION_CLASS;
const REACT_APP_ADD_CONSTRUCTION_CLASS = VITE_BASE_URL + ADD_CONSTRUCTION_CLASS;
const REACT_APP_DELETE_CONSTRUCTION_CLASS = VITE_BASE_URL + DELETE_CONSTRUCTION_CLASS;
const REACT_APP_ALL_ADMINISTRATIVE_BOUNDARY_LIST = VITE_BASE_URL + ADMINISTRATIVE_BOUNDARY_LIST;
const REACT_APP_UPDATE_ADMINISTRATIVE_BOUNDARY = VITE_BASE_URL + UPDATE_ADMINISTRATIVE_BOUNDARY;
const REACT_APP_ADD_ADMINISTRATIVE_BOUNDARY = VITE_BASE_URL + ADD_ADMINISTRATIVE_BOUNDARY;
const REACT_APP_DELETE_ADMINISTRATIVE_BOUNDARY = VITE_BASE_URL + DELETE_ADMINISTRATIVE_BOUNDARY;
const REACT_APP_ALL_COLLECTOR = VITE_BASE_URL + GET_ALL_COLLECTOR;
const REACT_APP_GET_MILKAT_KAR_AKARNI = VITE_BASE_URL + GET_MILKAT_KAR_AKARNI;
const REACT_APP_PROCESS_MILKAT_KAR_AKARNI = VITE_BASE_URL + PROCESS_MILKAT_KAR_AKARNI;
const REACT_APP_GET_NAMUNA_EIGHT_BILL = VITE_BASE_URL + GET_NAMUNA_EIGHT_BILL;
const REACT_APP_GET_NAMUNA_NINE = VITE_BASE_URL + GET_NAMUNA_NINE;
const REACT_APP_PROCESS_WARSHIK_KAR_AKARNI = VITE_BASE_URL + PROCESS_WARSHIK_KAR_AKARNI;
const REACT_APP_UPDATE_WARSHIK_KAR_AKARNI = VITE_BASE_URL + UPDATE_WARSHIK_KAR_AKARNI;
const REACT_APP_REGISTERNUMBER_LIST = VITE_BASE_URL + GET_ALL_REGISTERNUMBER_LIST;


class Api {

  static getStoredToken = () => {
    const UserToken = JSON.parse(localStorage.getItem("AccessToken") || "{}");
    return UserToken !== undefined ? UserToken : false;
  };

  static getStoredRefreshToken = () => {
    const RefreshToken = JSON.parse(localStorage.getItem("RefreshToken") || "{}");
    return RefreshToken !== undefined ? RefreshToken : false;
  };

  static refreshAccessToken = async (refreshToken: string) => {
    const url = `${baseURL}/v1/auth/refreshtoken`;
    try {
      const response = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${refreshToken}`
        },
      });

      if (response.status === 200) {
        return { status: true, data: response.data };
      } else {
        console.log("Error refreshing token: " + JSON.stringify(response.data));
        return { status: false, data: response.data };
      }
    } catch (err) {
      console.log("Error refreshing token:", err);
      return { status: false, data: err };
    }
  };

  static handleLogout = () => {
    localStorage.removeItem('AccessToken');
    localStorage.removeItem('RefreshToken');
    localStorage.removeItem('UserData');
    window.location.href = '/login';
  };

  static createZone = async (
    zoneData: ZoneObjectInterface,
    callback: (response: {
      status: boolean;
      data: ZoneMasterAddApiResp;
    }) => void,
  ) => {
    try {
      const response = await apiClient.post(ADD_ZONE, zoneData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static getAllWard(): Promise<any> {
    const source = axios.CancelToken.source();

    return apiClient
      .get(WARD_LIST, {
        cancelToken: source.token,
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error(error);
        }
      });
  }

  static getAllZone(): Promise<any> {
    const source = axios.CancelToken.source();

    return apiClient
      .get(ZONE_LIST, {
        cancelToken: source.token,
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error(error);
        }
      });
  }

  static updateZone = async (
    zoneId: string,
    zoneData: ZoneObjectInterface,
    callback: (response: {
      status: boolean;
      data: ZoneMasterAddApiResp;
    }) => void,
  ) => {
    try {
      const response = await apiClient.patch(
        UPDATE_ZONE + zoneId,
        zoneData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static getAllLocation = async (
    callback: (response: { status: boolean; data: LocationListAllApi }) => void,
  ) => {
    try {
      const response = await apiClient.get(Location_List, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };



  static getAllPropertyClass(): Promise<any> {
    const source = axios.CancelToken.source();

    return apiClient
      .get(PROPERTY_CLASS_LIST, {
        cancelToken: source.token,
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error(error);
        }
      });
  }

  static createPropertyClass = async (
    PropertyClassData: PropertyClassCreateApi,
    callback: (response: {
      status: boolean;
      data: PropertyClassCreateApi;
    }) => void,
  ) => {
    try {
      const response = await apiClient.post(ADD_PROPERTY_CLASS, PropertyClassData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error creating ward:", err);
      callback({ status: false, data: err });
    }
  };

  static updatePropertyClass = async (
    ClassId: string,
    classData: PropertyClassSendApiObj,
    callback: (response: {
      status: boolean;
      data: PropertyClassUpdateApi;
    }) => void,
  ) => {
    try {
      const response = await apiClient.put(
        UPDATE_PROPERTY_CLASS + ClassId,
        classData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static deletePropertyClass = async (
    propertyclassId: string,
    callback: (response: { status: boolean; data: PropertyFloorUpdateApi }) => void,
  ) => {
    try {
      const response = await apiClient.delete(`${DELETE_PROPERTY_CLASS}${propertyclassId}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error(err);
      callback({ status: false, data: err });
    }
  };

  static getAllPropertyFloor(): Promise<any> {
    const source = axios.CancelToken.source();

    return apiClient
      .get(PROPERTY_FLOOR_LIST, {
        cancelToken: source.token,
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error(error);
        }
      });
  }

  static createPropertFloor = async (
    floorData: PropertyFloorSendApiObj,
    callback: (response: { status: boolean; data: PropertyFloorCreateApi }) => void,
  ) => {
    try {
      const response = await apiClient.post(ADD_PROPERTY_FLOOR, floorData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static updateFloor = async (
    FloorId: string,
    floorData: PropertyFloorSendApiObj,
    callback: (response: {
      status: boolean;
      data: PropertyFloorUpdateApi;
    }) => void,
  ) => {
    try {
      const response = await apiClient.put(
        UPDATE_PROPERTY_FLOOR + FloorId,
        floorData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static deleteFloor = async (
    floorId: string,
    callback: (response: { status: boolean; data: PropertyFloorUpdateApi }) => void,
  ) => {
    try {
      const response = await apiClient.delete(`${DELETE_PROPERTY_FLOOR}${floorId}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error(err);
      callback({ status: false, data: err });
    }
  };

  static getOwnerType = async (
    callback: (response: { status: boolean; data: LocationListAllApi }) => void,
  ) => {
    try {
      const response = await apiClient.get(OWNER_TYPE_LIST, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static createLocation = async (
    locationData: LocationSendApiObj,
    callback: (response: { status: boolean; data: LocationCreateApi }) => void,
  ) => {
    try {
      const response = await apiClient.post(ADD_LOCATION, locationData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static updateLocation = async (
    locationId: string,
    locationData: LocationSendApiObj,
    callback: (response: { status: boolean; data: LocationUpdateApi }) => void,
  ) => {
    try {
      const response = await apiClient.patch(`${UPDATE_LOCATION}${locationId}`, locationData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static deleteLocation = async (
    locationId: string,
    callback: (response: { status: boolean; data: StreetUpdateApi }) => void,
  ) => {
    try {
      const response = await apiClient.delete(`${DELETE_LOCATION}${locationId}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error(err);
      callback({ status: false, data: err });
    }
  };

  static getAllStreet(): Promise<any> {
    const source = axios.CancelToken.source();

    return apiClient
      .get(STREET_List, {
        cancelToken: source.token,
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error(error);
        }
      });
  }

  static createStreet = async (
    streetData: StreetSendApiObj,
    callback: (response: { status: boolean; data: StreetCreateApi }) => void,
  ) => {
    try {
      const response = await apiClient.post(ADD_STREET, streetData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static deleteZone = async (
    zone_id: string,
    callback: (response: {
      status: boolean;
      data: WardMasterApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.delete(`${DELETE_ZONE}${zone_id}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static createWard = async (
    wardData: WardCreateObject,
    callback: (response: {
      status: boolean;
      data: WardCreateResObject;
    }) => void,
  ) => {
    try {
      const response = await apiClient.post(ADD_WARD, wardData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error creating ward:", err);
      callback({ status: false, data: err });
    }
  };

  static updateWard = async (
    wardId: string,
    wardData: WardUpdateObject,
    callback: (response: {
      status: boolean;
      data: WardMasterApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.patch(`${UPDATE_WARD}${wardId}`, wardData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error updating ward:", err);
      callback({ status: false, data: err });
    }
  };

  static deleteWard = async (
    ward_id: string,
    callback: (response: {
      status: boolean;
      data: WardMasterApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.delete(`${DELETE_WARD}${ward_id}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error("Error deleting ward:", err);
      callback({ status: false, data: err });
    }
  };

  static updateStreet = async (
    streetId: string,
    streetData: StreetSendApiObj,
    callback: (response: { status: boolean; data: StreetUpdateApi }) => void,
  ) => {
    try {
      const response = await apiClient.patch(`${UPDATE_STREET}${streetId}`, streetData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static deleteStreet = async (
    streetId: string,
    callback: (response: { status: boolean; data: StreetUpdateApi }) => void,
  ) => {
    try {
      const response = await apiClient.delete(`${DELETE_STREET}${streetId}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static getallElectionBoundryList = async () => {
    const source = axios.CancelToken.source();

    return apiClient
      .get(ELECTION_BOUNDRY_LIST, {
        cancelToken: source.token,
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error(error);
        }
      });
  };

  static createElectionBoundary = async (
    electionBoundaryName: string,
    callback: (response: { status: boolean; data: any }) => void,
  ) => {
    try {
      const response = await apiClient.post(ADD_ELECTION_BOUNDRY_LIST, electionBoundaryName, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static updateElectionBoundary = async (
    boundaryId: string,
    boundaryName: string,
    callback: (response: {
      status: boolean;
      data: ElectionBoundaryListAllApi;
    }) => void,
  ) => {
    try {
      const response = await apiClient.patch(`${UPDATE_ELECTION_BOUNDRY_LIST}${boundaryId}`, boundaryName, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static deleteElectionBoundary = async (
    boundaryId: string,
    callback: (response: {
      status: boolean;
      data: ElectionBoundaryListAllApi;
    }) => void,
  ) => {
    try {
      const response = await apiClient.delete(`${DELETE_ELECTION_BOUNDRY_LIST}${boundaryId}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static getAllUsageList = async (
    callback: (response: {
      status: boolean;
      data: UsageMasterApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.get(USAGE_LIST, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static createUsage = async (
    usage: string,
    callback: (response: {
      status: boolean;
      data: UsageMasterApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.post(ADD_USAGE, usage, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static updateUsage = async (
    usage_id: string,
    usage: string,
    callback: (response: {
      status: boolean;
      data: UsageMasterApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.patch(`${UPDATE_USAGE}${usage_id}`, usage, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static deleteUsage = async (
    usage_id: string,
    callback: (response: {
      status: boolean;
      data: UsageMasterApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.delete(`${DELETE_USAGE}${usage_id}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static getAllArea = async (
    callback: (response: { status: boolean; data: AreaListAllApi }) => void,
  ) => {
    try {
      const response = await apiClient.get(AREA_List, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static createArea = async (
    areaData: AreaSendApiObj,
    callback: (response: { status: boolean; data: AreaCreateApi }) => void,
  ) => {
    try {
      const response = await apiClient.post(ADD_AREA, areaData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static updateArea = async (
    areaId: string,
    areaData: AreaSendApiObj,
    callback: (response: { status: boolean; data: AreaUpdateApi }) => void,
  ) => {
    try {
      const response = await apiClient.patch(`${UPDATE_AREA}${areaId}`, areaData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static deleteArea = async (
    areaId: string,
    callback: (response: { status: boolean; data: AreaUpdateApi }) => void,
  ) => {
    try {
      const response = await apiClient.delete(`${DELETE_AREA}${areaId}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static getAllPropertyType = async () => {
    const source = axios.CancelToken.source();

    return apiClient
      .get(PROPERTYTYPE_List, {
        cancelToken: source.token,
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error(error);
        }
      });
  };

  static getAllpropertytypeStats = async () => {
    const source = axios.CancelToken.source();

    return apiClient
      .get(PROPERTYSTATS, {
        cancelToken: source.token,
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error(error);
        }
      });
  };

  static createPropertyType = async (
    propertytypeData: PropertytypeSendApiObj,
    callback: (response: {
      status: boolean;
      data: PropertytypeCreateApi;
    }) => void,
  ) => {
    try {
      const response = await apiClient.post(
        ADD_PROPERTYTYPE,
        propertytypeData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static updatePropertyType = async (
    propertyTypeId: string,
    propertyTypeData: PropertytypeSendApiObj,
    callback: (response: {
      status: boolean;
      data: PropertytypeUpdateApi;
    }) => void,
  ) => {
    try {
      const response = await apiClient.patch(
        `${UPDATE_PROPERTYTYPE}${propertyTypeId}`,
        propertyTypeData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static deletePropertyType = async (
    propertyTypeId: string,
    callback: (response: {
      status: boolean;
      data: PropertytypeUpdateApi;
    }) => void,
  ) => {
    try {
      const response = await apiClient.delete(
        `${DELETE_PROPERTYTYPE}${propertyTypeId}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static getAllPropertySubType = async () => {
    const source = axios.CancelToken.source();

    return apiClient
      .get(PROPERTYSUBTYPE_List, {
        cancelToken: source.token,
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error(error);
        }
      });
  };

  static createPropertySubType = async (
    propertysubtypeData: PropertysubtypeSendApiObj,
    callback: (response: {
      status: boolean;
      data: PropertysubtypeCreateApi;
    }) => void,
  ) => {
    try {
      const response = await apiClient.post(
        ADD_PROPERTYSUBTYPE,
        propertysubtypeData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static updatePropertySubType = async (
    propertysubTypeId: string,
    propertySubTypeData: PropertysubtypeSendApiObj,
    callback: (response: {
      status: boolean;
      data: PropertysubtypeUpdateApi;
    }) => void,
  ) => {
    try {
      const response = await apiClient.patch(
        `${UPDATE_PROPERTYSUBTYPE}${propertysubTypeId}`,
        propertySubTypeData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static deletePropertySubType = async (
    propertysubTypeId: string,
    callback: (response: {
      status: boolean;
      data: PropertysubtypeUpdateApi;
    }) => void,
  ) => {
    try {
      const response = await apiClient.delete(
        `${DELETE_PROPERTYSUBTYPE}${propertysubTypeId}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static getAllUsageSubList = async (
    callback: (response: {
      status: boolean;
      data: UsageSubMasterApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.get(USAGE_SUB_LIST, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static createUsageSub = async (
    subUsage: any,
    callback: (response: {
      status: boolean;
      data: UsageSubMasterApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.post(ADD_USAGE_SUB, subUsage, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      callback({ status: response.status === 201, data: response.data });
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static updateUsageSub = async (
    subUsage_id: string,
    subUsage: UsageSubCreateObject,
    callback: (response: {
      status: boolean;
      data: UsageSubMasterApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.patch(
        `${UPDATE_USAGE_SUB}${subUsage_id}`,
        subUsage,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      callback({ status: response.status === 200, data: response.data });
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static deleteUsageSub = async (
    subUsage_id: string,
    callback: (response: {
      status: boolean;
      data: UsageSubMasterApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.delete(
        `${DELETE_USAGE_SUB}${subUsage_id}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      callback({ status: response.status === 200, data: response.data });
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static getAllConstructionClassList = async (
    callback: (response: {
      status: boolean;
      data: ConstructionClassApiResponse | String;
    }) => void,
  ) => {
    try {
      const response = await apiClient.get(REACT_APP_ALL_CONSTRUCTION_CLASS_LIST, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        const errorMessage =
          response.statusText || "Error fetching construction class list";
        callback({ status: false, data: errorMessage });
      }
    } catch (error) {
      console.error("Error fetching construction class list:", error);
      callback({ status: false, data: "Network error" });
    }
  };

  static createConstructionClass = async (
    constructionClass: any,
    callback: (response: {
      status: boolean;
      data: any;
    }) => void,
  ) => {
    try {
      const response = await apiClient.post(REACT_APP_ADD_CONSTRUCTION_CLASS, constructionClass, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        const errorMessage =
          response.statusText || "Error creating construction class";
        callback({ status: false, data: errorMessage });
      }
    } catch (error) {
      console.error("Error creating construction class:", error);
      callback({ status: false, data: "Network error" });
    }
  };

  static updateConstructionClass = async (
    constructionClass_id: string,
    constructionClass: any,
    callback: (response: {
      status: boolean;
      data: any;
    }) => void,
  ) => {
    try {
      const response = await apiClient.patch(`${REACT_APP_UPDATE_CONSTRUCTION_CLASS}${constructionClass_id}`, constructionClass, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        const errorMessage =
          response.statusText || "Error updating construction class";
        callback({ status: false, data: errorMessage });
      }
    } catch (error) {
      console.error("Error updating construction class:", error);
      callback({ status: false, data: "Network error" });
    }
  };

  static deleteConstructionClass = async (
    constructionClass_id: string,
    callback: (response: {
      status: boolean;
      data: any;
    }) => void,
  ) => {
    try {
      const response = await apiClient.delete(`${REACT_APP_DELETE_CONSTRUCTION_CLASS}${constructionClass_id}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        const errorMessage =
          response.statusText || "Error deleting construction class";
        callback({ status: false, data: errorMessage });
      }
    } catch (error) {
      console.error("Error deleting construction class:", error);
      callback({ status: false, data: "Network error" });
    }
  };

  static getAllAdministrativeBoundaryList = async () => {
    const source = axios.CancelToken.source();

    return apiClient
      .get(REACT_APP_ALL_ADMINISTRATIVE_BOUNDARY_LIST, {
        cancelToken: source.token,
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error(error);
        }
      });
  };

  static createAdministrativeBoundary = async (
    administrativeBoundary: any,
    callback: (response: {
      status: boolean;
      data: AdministrativeBoundaryApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.post(REACT_APP_ADD_ADMINISTRATIVE_BOUNDARY, administrativeBoundary, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static updateAdministrativeBoundary = async (
    administrativeBoundary_id: string,
    adminstrativeBoundaryData: any,
    callback: (response: {
      status: boolean;
      data: AdministrativeBoundaryApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.patch(`${REACT_APP_UPDATE_ADMINISTRATIVE_BOUNDARY}${administrativeBoundary_id}`, adminstrativeBoundaryData, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static deleteAdministrativeBoundary = async (
    administrativeBoundary_id: string,
    callback: (response: {
      status: boolean;
      data: AdministrativeBoundaryApiResponse;
    }) => void,
  ) => {
    try {
      const response = await apiClient.delete(`${REACT_APP_DELETE_ADMINISTRATIVE_BOUNDARY}${administrativeBoundary_id}`, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  static globalSearch = async (
    queryParam: string,
    page?: number,
    limit?: number
  ) => {
    const url = `${VITE_BASE_URL}/v1/global-search`;

    try {
      const response = await apiClient.get(url.toString(), {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        params: {
          ...parseQueryParams(queryParam),
          page: page,
          limit: limit,
        },
        responseType: "json",
      });

      return response;
    } catch (err: any) {
      throw err;
    }
  };

  static fyYears = async () => {
    const url = `${VITE_BASE_URL}/v1/financialMaster`;

    try {
      const response = await apiClient.get(url.toString(), {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      return response;
    } catch (err: any) {
      throw err;
    }
  };

  static genrateFyYear = async () => {
    const url = `${VITE_BASE_URL}/v1/financialMaster/create-financial-year`;

    try {
      const response = await apiClient.post(url.toString(), {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      return response;
    } catch (err: any) {
      throw err;
    }
  };

  static divideAndGenerateNumber = async (number: number, propertyNumber: string) => {
    const url = `${VITE_BASE_URL}/v1/property-divide/divideAndGenrateNumber`;

    try {
      const response = await apiClient.get(
        url,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params: {
            number: number,
            propertyNumber: propertyNumber
          }
        }
      );

      return { status: true, data: response.data };
    } catch (err: any) {
      return { status: false, data: err };
    }
  };

  static checkNumberAreUnique = async (propertyNumbers: string[]) => {
    const url = `${VITE_BASE_URL}/v1/property-divide/check-numbers`;

    try {
      const response = await apiClient.post(
        url,
        {
          propertyNumbers: propertyNumbers
        },
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );

      return { status: true, data: response.data };
    } catch (err: any) {
      return { status: false, data: err };
    }
  };

  static advancedPropertySearch = async (
    name: string,
    zone: string,
    ward: string,
    page: number,
    limit: number
  ) => {
    try {
      const params = new URLSearchParams({
        name,
        zone,
        ward,
        page: page.toString(),
        limit: limit.toString(),
      });

      const response = await apiClient.get(
        `${VITE_BASE_URL}/v1/user-property/checkProperty?${params.toString()}`,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );

      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message
      };
    }
  };

  static getAllCollector = async (
    callback: (response: { status: boolean; data: any }) => void,
  ) => {
    try {
      const response = await apiClient.get(REACT_APP_ALL_COLLECTOR, {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      callback({ status: false, data: err });
    }
  };

  static getMilkatKarAkarni = async (
    searchValue: string,
    searchOn: string,
    financialYear: string
  ) => {
    try {
      const response = await apiClient.get(
        REACT_APP_GET_MILKAT_KAR_AKARNI,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params: {
            value: searchValue,
            searchOn: searchOn,
            fy: financialYear
          }
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static processMilkatKarAkarni = async (propertyId: string, wardNumber?: string) => {
    try {
      const params: any = { property_id: propertyId };
      if (wardNumber) {
        params.ward_number = wardNumber;
      }

      const response = await apiClient.get(
        REACT_APP_PROCESS_MILKAT_KAR_AKARNI,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static getNamunaEightBill = async () => {
    try {
      const response = await apiClient.get(
        REACT_APP_GET_NAMUNA_EIGHT_BILL,
        {
          headers: {
            Accept: "application/json",
          },
          responseType: "blob"
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static getNamunaNine = async (
    searchValue: string,
    searchOn: string,
    financialYear: string
  ) => {
    try {
      const response = await apiClient.get(
        REACT_APP_GET_NAMUNA_NINE,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params: {
            value: searchValue,
            searchOn: searchOn,
            fy: financialYear
          }
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static processWarshikKarAkarni = async ({
    financialYear,
    milkatKarId=null,
    ward=null,
    propertyNumber=null,
    oldPropertyNumber=null,
    ownerName=null
  })  => {
    try {
      const response = await apiClient.get(
        REACT_APP_PROCESS_WARSHIK_KAR_AKARNI,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params: {
            fy: financialYear,
            milkatKar_id: milkatKarId ,
            ward: ward,
            propertyNumber: propertyNumber,
            oldPropertyNumber: oldPropertyNumber,
            ownerName: ownerName
          }
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static getWarshikKarRemainingCount = async (wardNumber: string, financialYear: string) => {
    try {
      const response = await apiClient.get(
        "/v1/annual-kar-akarani/getWarshikKarRemainingCount",
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params: {
            ward_number: wardNumber,
            financial_year: financialYear
          }
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static getRecentTransactions = async () => {
    try {
      const response = await apiClient.get("/v1/dashboard/recent-transactions");
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static getTaxCollectionTrend = async (financialYear: string) => {
    try {
      const response = await apiClient.get("/v1/dashboard/tax-collection-trend", {
        params: {
          financialYear: financialYear
        }
      });
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static getWardPerformance = async () => {
    try {
      const response = await apiClient.get("/v1/dashboard/ward-performance");
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static getDefaulterAnalysis = async () => {
    try {
      const response = await apiClient.get("/v1/dashboard/defaulter-analysis");
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static getMilkatKarRemainingCount = async (wardNumber: string, reassessmentRangeId: string) => {
    try {
      const response = await apiClient.get(
        "/v1/milkatKar/getMilkatKarRemainingCount",
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params: {
            ward_number: wardNumber,
            reassessment_range_id: reassessmentRangeId
          }
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static updateWarshikKarAkarni = async (
    data: any,
    warshikKarId: string,
    propertyId: string
  ) => {
    try {
      const response = await apiClient.put(
        REACT_APP_UPDATE_WARSHIK_KAR_AKARNI,
        data,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params: {
            warshikKar_id: warshikKarId,
            property_id: propertyId
          }
        }
      );
      return { status: true, data: response.data };
    } catch (err: any) {
      return {
        status: false,
        data: err.response ? err.response.data : err.message,
      };
    }
  };

  static getAllRegisterNumbers = async (
  callback: (response: { status: boolean; data: any }) => void,
) => {
  try {
    const response = await apiClient.get(REACT_APP_REGISTERNUMBER_LIST, {
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    });

    if (response.status === 200) {
      callback({ status: true, data: response.data });
    } else {
      callback({ status: false, data: response.data });
    }
  } catch (err) {
    callback({ status: false, data: err });
  }
};

}

function parseQueryParams(queryParam: string): Record<string, any> {
  const params = new URLSearchParams(queryParam);
  const obj: Record<string, any> = {};
  for (const [key, value] of params.entries()) {
    obj[key] = value;
  }

  return obj;
}

export default Api;
