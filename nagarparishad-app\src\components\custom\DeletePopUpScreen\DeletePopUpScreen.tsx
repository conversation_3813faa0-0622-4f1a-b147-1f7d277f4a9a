import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useTranslation } from "react-i18next";
import Trash from "@/assets/img/trash.png";

export interface DeleteScreenProps {
  isOpen: boolean;
  toggle: () => void;
  itemName: string;
  onDelete: () => void;
}

const DeletePopUpScreen: React.FC<DeleteScreenProps> = ({
  isOpen,
  toggle,
  itemName,
  onDelete,
}) => {
  const { t } = useTranslation();

  const handleClose = () => {
    toggle();
  };

  const handleDelete = () => {
    onDelete();
    handleClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[90%] sm:max-w-[450px] bg-white">
        <DialogHeader>
          <DialogTitle>
            {t("deletescreen.title")}
          </DialogTitle>
          <DialogDescription className="flex justify-center">
            {t("deletescreen.description")}{" "}
            {/* <img src={Trash} className="w-[50px] h-[50px]" /> */}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className=" !justify-center max-sm:flex-row max-sm:flex-row-reverse">
          <Button onClick={handleClose}>{t("deletescreen.no")}</Button>
          <Button className="bg-red-500 hover:bg-bg-red-500" onClick={handleDelete}>{t("deletescreen.yes")}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeletePopUpScreen;
