import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import { toast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { Loader2 } from "lucide-react";
import { useConstructionClassController } from "@/controller/master/ConstructionController";

interface ConstructionMasterFormInterface {
  btnTitle: string;
  editData?: any;
}

const ConstructionMasterForm = ({
  btnTitle,
  editData,
}: ConstructionMasterFormInterface) => {
  const { t } = useTranslation();
  const schema = z.object({
    Construction_className: z.string().trim().min(1, t("errorsRequiredField")),
    Construction_className_marathi: z.string().min(1, t("errorsRequiredField")),
    value: z.string().min(1, t("errorsRequiredField")),
  });
  const dynamicValues = {
    name: t("construction.classNameLabel"),
  };

  const [loader, setLoader] = useState(false);
  const {
    setOpen,
    refreshConstructionClassList,
    setRefreshConstructionClassList,
  } = useContext(GlobalContext);
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      Construction_className: editData?.constructionClassName || "",
      Construction_className_marathi: editData?.constructionClassMarathi || "",
      value: editData?.values || "",
    },
  });
  const {
    formState: { errors },
    reset,
  } = form;
  const { createConstructionClass, updateConstructionClass } =
    useConstructionClassController();

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();
    const dataResponse = {
      constructionClassName: data.Construction_className,
      constructionClassMarathi: data.Construction_className_marathi || null,
      values: data.value || null,
    };
    if (editData?.constructionClass_id !== undefined) {
      setLoader(true);
      updateConstructionClass(
        { classId: editData.constructionClass_id, classData: dataResponse },
        {
          onSuccess: (response) => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });

            reset();
            setOpen(false);
            setRefreshConstructionClassList(!refreshConstructionClassList);
            form.reset({
              Construction_className: "",
              Construction_className_marathi: "",
              value: "",
            });
            setLoader(false);
          },
          onError: (error) => {
            toast({
              title: error.message,
              variant: "destructive",
            });
            setLoader(false);
          },
        },
      );
    } else {
      setLoader(true);
      createConstructionClass(dataResponse, {
        onSuccess: (response) => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });

          reset();
          setOpen(false);
          form.reset({
            Construction_className: "",
            Construction_className_marathi: "",
            value: "",
          });
          setLoader(false);
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });
    }
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        Construction_className: editData?.constructionClassName || "",
        Construction_className_marathi:
          editData?.constructionClassMarathi || "",
        value: editData?.values || "",
      });
    } else {
      form.reset({
        Construction_className: "",
        Construction_className_marathi: "",
        value: "",
      });
    }
  }, [editData]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full md:w-full"
        >
          <div className="form-flex">
            <div className="form-element">
              <FormField
                control={form.control}
                name="Construction_className"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("construction.classNameLabel")}</FormLabel>
                    <FormControl className="mt-1">
                      <Input
                        type="text"
                        placeholder={t("construction.classNamePlaceholder")}
                        {...field}
                      />
                    </FormControl>
                    {errors.Construction_className && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="form-element">
              <FormField
                control={form.control}
                name="Construction_className_marathi"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t("construction.classNameMarathiLabel")}
                    </FormLabel>
                    <FormControl className="mt-1">
                      <Input
                        type="text"
                        placeholder={t(
                          "construction.classNameMarathiPlaceholder",
                        )}
                        {...field}
                      />
                    </FormControl>
                    {errors.Construction_className_marathi && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="form-flex">
            <div className="form-element">
              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("construction.valueLabel")}</FormLabel>
                    <FormControl className="mt-1">
                      <Input
                        type="text"
                        placeholder={t("construction.valuePlaceholder")}
                        {...field}
                      />
                    </FormControl>
                    {errors.value && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="form-element"></div>
          </div>
          <div className="w-full flex justify-end mt-4">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
        </form>
      </Form>
    </>
  );
};

export default ConstructionMasterForm;
