import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Put,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  HttpStatus,
  InternalServerErrorException,
  NotFoundException,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { PropertyMasterService } from './property_master.service';
import { CreatePropertyMasterDto } from './dto/create-property_master.dto';
import { UpdatePropertyMasterDto } from './dto/update-property_master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PropertyMasterIdDto } from './dto/property_master.dto';
import { SearchPropertyDto } from './dto/search-property_master.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { UpdatePropertyDto } from './dto/update-property_master.dto';
import { diskStorage } from 'multer';
import { extname } from 'path';
import csv from 'csv-parser';
import fs from 'fs';
import path from 'path';
import multer from 'multer';
import { CsvModule } from 'nest-csv-parser';
import { memoryStorage } from 'multer';
import { CSVDTO } from './dto/csv.validator.dto';
import {
  Form,
  Permissions,
} from '@helper/helpers/role-based-access/permission.decorator';
import { Public } from '@jwt/jwt-auth/decorators/public.decorator';

@ApiTags('Property Master')
@Controller('property-master')
export class PropertyMasterController {
  constructor(private readonly propertyMasterService: PropertyMasterService) {}

  @Post()
  async create(@Body() createPropertyDto: CreatePropertyMasterDto) {
    return await this.propertyMasterService.createProperty(createPropertyDto);
  }

  @ApiOperation({ summary: 'Get all  Property Master' })
  @ApiResponse({ status: 200, description: 'Returns all  Property Master' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getAll')
  findAll(@Query() params) {
    return this.propertyMasterService.findAll(
      params.value,
      params.searchOn,
      params.page,
      params.limit,
    );
  }
  // criteria based searching, searchOn and value, pageLimit Optional.

   @Public()
  @ApiOperation({ summary: 'Get one Property Master' })
  @ApiResponse({ status: 200, description: 'Returns Single Property Master' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() params) {
    return this.propertyMasterService.findOne(params.value, params.searchOn);
  }

  // @ApiOperation({ summary: 'Update a Property Master by ID' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'The Property Master has been successfully updated',
  // })
  // @ApiResponse({ status: 404, description: 'Property Master not found' })
  // @Patch()
  // update(
  //   @Query() propertyMasterId: PropertyMasterIdDto,
  //   @Body() updatePropertyMasterDto: UpdatePropertyMasterDto,
  // ) {
  //   return this.propertyMasterService.update(
  //     propertyMasterId,
  //     updatePropertyMasterDto,
  //   );
  // }
  

  @ApiOperation({ summary: 'Delete a Property Master by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Property Master has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Property Master not found' })
  @Delete()
  remove(@Query() propertyMasterId: PropertyMasterIdDto) {
    return this.propertyMasterService.remove(propertyMasterId);
  }

  @ApiOperation({
    summary: 'Search a Property Master by Phone Number/ Name / Property Number',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all  Property Master by Condition',
  })
  @ApiResponse({ status: 404, description: 'Property Master not found' })
  @Get('search')
  search(@Query() searchPropertyDto: SearchPropertyDto) {
    return this.propertyMasterService.searchProperty(searchPropertyDto);
  }

  // @ApiResponse({ status: 400, description: 'Invalid file format' })
  // @Post('/upload-file')
  // @UseInterceptors(
  //   FileInterceptor('file', { storage: memoryStorage(),
  //     limits: { files: 1, fileSize: 1024 * 1024 * 5 }, // 1 MB you can adjust size here
  //     fileFilter: (req, file, cb) => {
  //       const allowedMimeTypes = ['text/csv'];
  //       if (!allowedMimeTypes.includes(file.mimetype)) {
  //         cb(new BadRequestException('Invalid file type'), false);
  //       } else if (file?.size > 1024 * 1024 * 5) { // 1MB
  //         cb(
  //           new BadRequestException('Max File Size Reached. Max Allowed: 1MB'),
  //           false,
  //         );
  //       }
  //       cb(null, true);
  //     },
  //   }),
  // )
  // async uploadCsvFile(
  //   @UploadedFile() file: Express.Multer.File
  // ): Promise<any> {
  //   try {
  //     let response: any = await this.propertyMasterService.validateCsvData(file)
  //     if (!response.error) {
  //       response = await this.propertyMasterService.processFile(file);
  //     }
  //     return {
  //       error: false,
  //       statusCode: response?.status || HttpStatus.OK,
  //       message: response?.message || "file uploaded successfully",
  //       data: response?.data || [],
  //       errorsArray: response?.errorsArray || []
  //     };
  //   } catch (e) {
  //     throw new InternalServerErrorException(e?.message || "Internal Server Error")
  //   }
  // }

  @ApiResponse({ status: 400, description: 'Invalid file format' })
  @Post('/upload-file')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(),
      limits: { files: 1, fileSize: 1024 * 1024 * 5 }, // 1 MB you can adjust size here
      fileFilter: (req, file, cb) => {
        const allowedMimeTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
          'application/vnd.ms-excel', // .xls
        ];
        if (!allowedMimeTypes.includes(file.mimetype)) {
          cb(new BadRequestException('Invalid file type'), false);
        } else if (file?.size > 1024 * 1024 * 5) {
          // 1MB
          cb(
            new BadRequestException('Max File Size Reached. Max Allowed: 1MB'),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  async uploadCsvFile(@UploadedFile() file: Express.Multer.File): Promise<any> {
    try {
      // let response: any = await this.propertyMasterService.validateCsvData(file)
      // if (!response.error) {
      let response: any = await this.propertyMasterService.processFile(file);
      //}
      return {
        error: false,
        statusCode: response?.status || HttpStatus.OK,
        message: response?.message || 'file uploaded successfully',
        data: response?.data || [],
        errorsArray: response?.errorsArray || [],
      };
    } catch (e) {
      throw new InternalServerErrorException(
        e?.message || 'Internal Server Error',
      );
    }
  }

  // @Post('test-deletion')
  // async testingDeletion(
  //   @Body('propertyId') propertyId: string,
  //   @Body('deleted_property_usage_details_id') deletedPropertyUsageDetailsIds: UpdatePropertyDto['deleted_property_usage_details_id'],
  // ) {
  //   return this.propertyMasterService.testingDeletion(propertyId, deletedPropertyUsageDetailsIds);
  // }

  @Delete('delete-property')
  testing(@Query() params) {
    return this.propertyMasterService.deleteProperty(params.property_Id);
  }
  @Get('deleteDuplicates')
  deleteDuplicates() {
    return this.propertyMasterService.deleteDuplicates();
  }

  @ApiOperation({ summary: 'Export property data to Excel' })
  @ApiResponse({
    status: 200,
    description: 'Excel file with property data exported successfully',
    content: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'No property data found for export',
  })
  @Get('export-excel')
  async exportPropertyDataToExcel(
    @Res() res: Response,
    @Query('financial_year') financial_year?: string,
  ) {
    try {
      const excelBuffer =
        await this.propertyMasterService.exportPropertyDataToExcel(
          financial_year,
        );

      const currentDate = new Date();
      const formattedDate = `${currentDate.getFullYear()}-${(
        currentDate.getMonth() + 1
      )
        .toString()
        .padStart(
          2,
          '0',
        )}-${currentDate.getDate().toString().padStart(2, '0')}`;
      const formattedTime = `${currentDate.getHours().toString().padStart(2, '0')}-${currentDate
        .getMinutes()
        .toString()
        .padStart(
          2,
          '0',
        )}-${currentDate.getSeconds().toString().padStart(2, '0')}`;

      const filename = financial_year
        ? `property_data_${financial_year}_${formattedDate}_${formattedTime}.xlsx`
        : `property_data_all_${formattedDate}_${formattedTime}.xlsx`;

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      res.status(HttpStatus.OK).send(excelBuffer);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to export property data to Excel',
      );
    }
  }

  /**
   * Update length and width in all MilkatKar tax records from property usage details
  //  */
 
  @Put('/update-milkatkar-length-width')
  @ApiOperation({
    summary: 'Update length and width in all MilkatKar tax records',
    description:
      'Iterates over all MilkatKar tax records and updates length and width values from corresponding property usage details',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Length and width updated successfully',
  })
  async updateMilkatKarLengthWidth() {
    try {
      const result =
        await this.propertyMasterService.updateAllMilkatKarLengthWidth();
      return {
        status: HttpStatus.OK,
        message: 'Length and width updated successfully',
        data: result,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        error.message || 'Failed to update length and width',
      );
    }
  }
s
  /**
   * Update tax payer name in payment_info from property owner details
   */
  // @Form('Property')
  // @Permissions('can_update')
  @Put('update-payment-info-taxpayer-name')
  @ApiOperation({
    summary:
      'Update tax payer name in payment_info from property owner details',
    description:
      'Iterates over all payment_info records and updates tax_payer_name from property owner details (priority: owner_type_id 98e191a7-4254-49bc-86d9-c746b61ddc7a, then first available)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tax payer names updated successfully',
  })
  async updatePaymentInfoTaxPayerName() {
    return await this.propertyMasterService.updateAllPaymentInfoTaxPayerName();
  }

  @Put('update-ispayername')
  @ApiOperation({
    summary:
      'Update tax payer name in payment_info from property owner details',
    description:
      'Iterates over all payment_info records and updates tax_payer_name from property owner details (priority: owner_type_id 98e191a7-4254-49bc-86d9-c746b61ddc7a, then first available)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tax payer names updated successfully',
  })
  async updateIsPayementPayerName() {
    return await this.propertyMasterService.updateIsPaymentPayerName();
  }


  @Patch(':id/imarat-kar-discount')
  async updateImaratKarDiscount(
    @Param('id') propertyId: string,
    @Body('discountPercentage') discountPercentage: number,
  ) {
    return this.propertyMasterService.updateImaratKarDiscount(propertyId, discountPercentage);
  }

  @Put(':id')
  async updateProperty(
    @Param('id') propertyId: string,
    @Body() updatePropertyDto: UpdatePropertyDto,
  ) {
    const updatedProperty = await this.propertyMasterService.updateProperty(
      propertyId,
      updatePropertyDto,
    );
    if (!updatedProperty) {
      throw new NotFoundException('Property not found');
    }
    return updatedProperty;
  }
}
