import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  StreetSendApiObj,
  StreetMasterObject,
  StreetCreateApi,
} from "../../../model/street-master";
import Api from "@/services/ApiServices";
import { useToast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { Loader2 } from "lucide-react";
import { useStreetMasterController } from "@/controller/master/StreetMasterController";

interface LocationMasterInterface {
  btnTitle: string;
  editData?: StreetMasterObject;
}

const StreetMasterForm = ({ btnTitle, editData }: LocationMasterInterface) => {
  const { t } = useTranslation();

  const schema = z.object({
    street_name: z.string().trim().min(1, t("errorsRequiredField")),
  });
  const dynamicValues = {
    name: t("street.streetLabel"),
  };
  const { createStreet, updateStreet } = useStreetMasterController();

  const { toast } = useToast();
  const { setOpen, refreshStreetList, setRefreshStreetList } =
    useContext(GlobalContext);
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      street_name: editData?.street_name || "",
    },
  });
  const [loader, setLoader] = useState(false);

  const {
    formState: { errors },
    reset,
  } = form;

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();

    const DataResponse: any = {
      streetOrRoadName: data?.street_name,
    };
    if (
      editData?.street_id !== undefined &&
      editData?.street_id !== null
    ) {
      setLoader(true);
      updateStreet(
        { streetId: editData.street_id, streetData: DataResponse },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            setRefreshStreetList(!refreshStreetList);
            form.reset({
              street_name: "",
            });
            setLoader(false);
          },
          onError: (error: any) => {
            toast({
              title: error.message,
              variant: "destructive",
            });
            setLoader(false);
          },
        },
      );
    } else {
      setLoader(true);
      console.log(JSON.stringify(DataResponse));
      createStreet(DataResponse, {
        onSuccess: () => {
          toast({
            title: t("api.formcreate", dynamicValues),
            
            variant: "success",
          });
          form.reset({
            street_name: "",
          });
          setLoader(false);
        },
        onError: (error: any) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });
    }
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        street_name: editData.street_name || "",
      });
    } else {
      form.reset({
        street_name: "",
      });
    }
  }, [editData]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full md:w-full"
        >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="street_name"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("street.streetLabel")}</FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1"
                        placeholder={t("street.streetLabel")}
                        {...field}
                      />
                    </FormControl>
                    {errors.street_name && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
              <div className="grid-cols-subgrid  mb-1 ml-4 max-md:flex max-md:justify-end  pt-[32px] ">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
          </div>
          
        </form>
      </Form>
    </>
  );
};

export default StreetMasterForm;
