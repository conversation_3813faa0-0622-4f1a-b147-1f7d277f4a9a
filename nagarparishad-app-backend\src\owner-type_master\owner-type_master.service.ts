import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateOwnerTypeMasterDto,
  OwnerTypeMasterIdDto,
  UpdateOwnerTypeMasterDto,
} from './dto/owner-type_master.dto';
import { OwnerTypeRepository } from 'libs/database/repositories';

@Injectable()
export class OwnerTypeMasterService {
  constructor(
    private readonly ownerTypeRepository: OwnerTypeRepository,
  ) {}

  // async create(createOwnerTypeMasterDto: CreateOwnerTypeMasterDto) {
  //   try {
  //     const saveData = await this.ownerTypeRepository.saveData(
  //       createOwnerTypeMasterDto,
  //     );

  //     return {
  //       message: 'Record Saved SuccessFully',
  //     };
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  async findAll() {
    try {
      const getAllData =
        await this.ownerTypeRepository.findAllLocation();

      if (!getAllData) {
        throw new NotFoundException('Record Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(ownerTypeMaster: OwnerTypeMasterIdDto) {
    try {
      const { owner_type_id } = ownerTypeMaster;
      const checkData =
        await this.ownerTypeRepository.findById(owner_type_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      return {
        message: 'Data found Sucess',
        data: checkData,
      };
    } catch (error) {
      throw error;
    }
  }

  // async update(
  //   ownerTypeMaster: OwnerTypeMasterIdDto,
  //   updateOwnerTypeMasterDto: UpdateOwnerTypeMasterDto,
  // ) {
  //   try {
  //     const { owner_type_id } = ownerTypeMaster;
  //     const checkData =
  //       await this.ownerTypeRepository.findById(owner_type_id);
  //     if (!checkData) {
  //       throw new NotFoundException('Data Not found');
  //     }

  //     const updateData = await this.ownerTypeRepository.updateData(
  //       owner_type_id,
  //       updateOwnerTypeMasterDto,
  //     );

  //     if (updateData.affected === 0) {
  //       throw new NotAcceptableException('Failed to update Data');
  //     }

  //     return {
  //       message: 'Update Success',
  //     };
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  // async remove(ownerTypeMaster: OwnerTypeMasterIdDto) {
  //   try {
  //     const { owner_type_id } = ownerTypeMaster;
  //     const checkData =
  //       await this.ownerTypeRepository.findById(owner_type_id);
  //     if (!checkData) {
  //       throw new NotFoundException('Data Not found');
  //     }

  //     const deleteData =
  //       await this.ownerTypeRepository.deleteData(owner_type_id);

  //     if (deleteData.affected === 0) {
  //       throw new NotAcceptableException('Failed to delete data');
  //     }

  //     return {
  //       message: 'data Delete Successfully',
  //       data: deleteData,
  //     };
  //   } catch (error) {
  //     throw error;
  //   }
  // }
}
