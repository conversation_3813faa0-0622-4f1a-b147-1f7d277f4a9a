import { Modu<PERSON> } from '@nestjs/common';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { RequestInterceptor, ResponseInterceptor } from './interceptors';
import { AuthHelper } from './auth.helper';
import { DatabaseModule } from 'libs/database';
import { OtpHelperService } from './otp.helper';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { CustomThrottlerGuard } from './exceptions';
import { CronJobsModule } from './cron-jobs/cron-jobs.module';

@Module({
  imports: [
    DatabaseModule,
    ScheduleModule.forRoot(),
    ThrottlerModule.forRoot([
      {
        ttl: 60000,
        limit: 50,
      },
    ]),
    CronJobsModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: RequestInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: CustomThrottlerGuard,
    },
    AuthHelper,
    OtpHelperService,
  ],
  exports: [AuthHelper, OtpHelperService],
})
export class HelpersModule {}
