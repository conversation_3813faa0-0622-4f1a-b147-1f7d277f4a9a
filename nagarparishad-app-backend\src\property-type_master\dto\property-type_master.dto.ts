import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreatePropertyTypeMasterDto {
  @ApiProperty({ name: 'propertyType', type: String })
  @IsNotEmpty()
  @IsString()
  propertyType: string;
}
export class UpdatePropertyTypeMasterDto extends PartialType(
  CreatePropertyTypeMasterDto,
) {}

export class PropertyTypeMasterIdDto {
  @ApiProperty({ name: 'propertyType_id', type: String })
  @IsNotEmpty()
  @IsUUID()
  propertyType_id: string;
}
