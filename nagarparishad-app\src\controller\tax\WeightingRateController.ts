import { ZoneObjectInterface } from "@/model/zone-master";
import Api from "../../services/ApiServices";
import {
  useQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import TaxListApi from "@/services/TaxServices";

interface ZoneData {
  zoneName: string; // Only zoneName now
}

const fetchConstructionRate = async () => {
    return new Promise((resolve, reject) => {
      TaxListApi.getWeightingRate((response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      });
    });
  };
  

const createWeightingRate = async (weightingRateData: any) => {
  return new Promise((resolve, reject) => {
    TaxListApi.createWeightingRate(weightingRateData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateWeightingRate = async ({ weightingRateData, weightingRateId }: { weightingRateId: string; weightingRateData: any }) => {
  return new Promise((resolve, reject) => {
    TaxListApi.updateWeightingRate(weightingRateId, weightingRateData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteWeightingRate = async (weightingRateId: string) => {
  return new Promise((resolve, reject) => {
    TaxListApi.deleteWeightingRate(weightingRateId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useWeightingRateController = () => {
  const queryClient = useQueryClient();

  const { data: constructionRateData,isLoading: propertyLoading } = useQuery({
    queryKey: ["weightingRatemaster"],
    queryFn: fetchConstructionRate,
    staleTime: 10 * 60 * 2, // 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
  });

  // const createWeightingRateMutation = useMutation({
  //   mutationFn: TaxListApi.createWeightingRate,

  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["weightingRatemaster"] });
  //   },
  // });

  const createWeightingRateMutation = useMutation({
    mutationFn: createWeightingRate,
    onMutate: async (newconstructionRates) => {
      await queryClient.cancelQueries({ queryKey: ["weightingRatemaster"] });
      const previousconstructionRates = queryClient.getQueryData(["weightingRatemaster"]);

      queryClient.setQueryData(["weightingRatemaster"], (old: any) => {
             console.log(" construtionold",old)
        const updatedData = [newconstructionRates, ...old.data];
        console.log("updatedData",updatedData)
        return updatedData;
      });

      return { previousconstructionRates };
    },
    onError: (err, newconstructionRates, context) => {
      queryClient.setQueryData(["weightingRatemaster"], context.previousconstructionRates);
      console.error("Error creating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["weightingRatemaster"] });
    },
  });
  // const updateWeightingRateMutation = useMutation({
  //   mutationFn: TaxListApi.updateWeightingRate,
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["weightingRatemaster"] });
  //   },
  // });


  const updateWeightingRateMutation = useMutation({
    mutationFn: updateWeightingRate,
    onMutate: async ({ weightingRateId, weightingRateData }) => {
      await queryClient.cancelQueries({ queryKey: ["weightingRatemaster"] });

      const previousWards = queryClient.getQueryData(["weightingRatemaster"]);
      queryClient.setQueryData(["weightingRatemaster"], (old: any) => {
        const updatedWards = old?.data?.map((constructionRate: any) =>
          constructionRate.weighting_rate_id === weightingRateId ? { ...constructionRate, ...weightingRateData } : constructionRate,
        );
        console.log("Updated query data:", updatedWards);
        return updatedWards;
      });

      return { previousWards };
    },
    onError: (err, { weightingRateId, weightingRateData }, context) => {
      queryClient.setQueryData(["weightingRatemaster"], context.previousWards);
      console.error("Error updating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["weightingRatemaster"] });
    },
  });
  // const deleteWeightingRateMutation = useMutation({
  //   mutationFn: TaxListApi.deleteWeightingRate,
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["weightingRatemaster"] });
  //   },
  // });
  const deleteWeightingRateMutation = useMutation({
    mutationFn: deleteWeightingRate,
    onMutate: async (constructionRateId) => {
      await queryClient.cancelQueries({ queryKey: ["weightingRatemaster"] });

      const previousConstructionRate = queryClient.getQueryData(["weightingRatemaster"]);

      queryClient.setQueryData(["weightingRatemaster"], (old: any) => {
        const updatedConstructionRate = old?.data?.filter((constructionRate: any) => constructionRate.usage_type_id !== constructionRateId);
        return updatedConstructionRate;
      });

      return { previousConstructionRate };
    },
    onError: (err, ward_id, context) => {
      queryClient.setQueryData(["weightingRatemaster"], context.previousConstructionRate);
      console.error("Error deleting ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["weightingRatemaster"] });
    },
  });
  return {
    weightingRateList: constructionRateData || [],
    propertyLoading,
    createWeightingRate: createWeightingRateMutation.mutate,
    updateWeightingRate: updateWeightingRateMutation.mutate,
    deleteWeightingRate: deleteWeightingRateMutation.mutate,
  };

  
};


