import { EntityRepository, Repository } from 'typeorm';
import { Logs } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
@EntityRepository(Logs)
export class LogsRepository extends Repository<Logs> {
    constructor(
        @InjectRepository(Logs)
        private readonly logs: Repository<Logs>,
    ) {
        super(
            logs.target,
            logs.manager,
            logs.queryRunner,
        );

    }
    async logAction(logData: Partial<Logs>) {
        const log = this.create({
          logType: logData.logType || 'INFO',  // Default logType to 'INFO' if not provided
          file: logData.file || '',  // Default file to an empty string
          api: logData.api || '',    // Default api to an empty string
          message: logData.message || '',  // Default message to an empty string
          Prev_data: logData.Prev_data || {},  // Default Prev_data to an empty object
          data: logData.data || {},  // Default data to an empty object
          user_id: logData.user_id || null,  // Default user_id to null if not provided
          extra: logData.extra || null,  // Default extra to null if not provided
          createdAt: new Date(),  // Automatically set current date/time
        });
    
        await this.save(log);
    }
}