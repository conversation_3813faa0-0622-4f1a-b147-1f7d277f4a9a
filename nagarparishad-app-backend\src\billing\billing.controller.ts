import { Controller, Get, Post, Body, Patch, Param, Delete, Res, Query } from '@nestjs/common';
import { BillingService } from './billing.service';
import { Response } from 'express';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';
import { Public } from '@jwt/jwt-auth/decorators/public.decorator';

@Controller('billing')
export class BillingController {
  constructor(private readonly billingService: BillingService) {}

  // @Post()
  // create(@Body() createBillingDto: CreateBillingDto) {
  //   return this.billingService.create(createBillingDto);
  // }

  
  @Form('Payment Bill')
  @Permissions('can_read')
  @Get('get-bill')
  async get_bill_pdf(@Query() params,@Res() res: Response) {
    await this.billingService.get_bill_pdf(params,res);
  }
  
  @Form('Payment Bill')
  @Permissions('can_read')
  @Post('get-bill_by_arrayofPropertyNumber')
  async get_bill_pdf_ByNumber(@Body() body: { propertyNumbers: string[] }, @Res() res: Response) {
    await this.billingService.get_bill_pdf_ByNumber(body.propertyNumbers, res);
  }

  @Form('Payment Bill')
  @Permissions('can_read')
  @Post('get-bill_by_PropertyNumber')
  async get_bill_pdf_ByPropertyNumber(@Query() Params,@Res() res: Response) {
    await this.billingService.get_bill_pdf_ByPropertyNumber(Params,res);
  }

  
  @Public()
  @Post('get-bill_by_property_numbers')
  async get_bill_pdf_ByPropertyNumbers(@Body() body: { propertyNumbers: string[] }, @Res() res: Response) {
    await this.billingService.getBillPdfByPropertyNumbers(body.propertyNumbers, res);
  }
  
  
  @Form('Payment Bill')
  @Permissions('can_read')
  @Get('get-bill-namuna-eight')
  async get_namuna8_pdf(@Query() params,@Res() res: Response) {
    
    await this.billingService.get_namuna8_pdf(params,res);
  }

  
  @Form('Payment Bill')
  @Permissions('can_read')
  @Get('get-bill-namuna-nine')
  async get_namuna9_pdf(@Query() params,@Res() res: Response) {
    
    await this.billingService.get_namuna9_pdf(params,res);
  }

  
  @Form('Payment Bill')
  @Permissions('can_read')
  @Get('get-form8-data')
  async get_form8_data(@Res() res: Response) {
         const dt= {
      "tax_property_id": "a78cef72-829b-4445-95d7-9bc51435a962",
      "bill_no": "1",
      "all_property_tax_sum": 50,
      "other_tax_sum_tax": 30,
      "total_tax": 80,
      "capital_value": 100050, //manually added
      "bill_generation_date": "2024-01-01",
      "property": {
          "propertyNumber": "SNP100021",
          "old_propertyNumber": "6145",
          "city_survey_number": "123",
          "address": null,
          "house_or_apartment_name": "बापूंची कृपा",
          "note": "test note",
          "zone": {
              "zoneName": "zone1"
          },
          "ward": {
              "ward_name": "ward1"
          },
          "street": {
              "street_name": "दत्त नगर"
          },
          "water_connection_type":"अवैध", //maually added
          "property_owner_details": [
            {
                "property_owner_details_id": "252cec7a-0501-4311-aedc-9601c6c13445",
                "name": "Vinayak Patil",
                "owner_type": {
                    "owner_type": "स्वतः"
                }
            },
            {
                "property_owner_details_id": "3e376ff5-a916-45f4-9b76-f85eb2daedb3",
                "name": "Vin Patil",
                "owner_type": {
                    "owner_type": "भोगवटादार"
                }
            }
        ]
    },
      "tax_propertywise": [
          {
              "tax_value": 1,
              "tax": 2,
              "property_Usage_Details": {
                  "length": 17,
                  "width": 10,
                  "are_sq_meter": 44,
                  "propertyType": {
                      "propertyType": "दगड विटांचे चुना किंवा सिमेंट वापरून उभाररेली इमारत,निवासी"
                  }
              }
          }
      ],
      "tax_property_other_taxes": [
        {
            "tax_type": "दिवाबत्ती कर ",           
            "amount": 10,           
        },
        {
            "tax_type": "आरोग्य कर",          
            "amount": 5,           
        },
        {
            "tax_type": "सार्वजनिक पाणीपट्टी",           
            "amount": 5,         
        },
        {
          "tax_type": "पडसर कर",    
          "amount": 10,         
        }
      ]
  };
      res.send(dt)
    
    // return await this.billingService.getForm8Data();
  }


  
  @Form('Payment Bill')
  @Permissions('can_read')
  @Get('get-form9-data')
  async get_form9_data(@Res() res: Response) {
         const dt= {
      "tax_property_id": "a78cef72-829b-4445-95d7-9bc51435a962",
      "bill_no": "1",
      "all_property_tax_sum_prev": 0, 
      "all_property_tax_sum": 50,   
      "all_property_tax_sum_total": 50,        
      "total_tax_prev": 0,
      "total_tax": 80,
      "total_tax_total": 80,
      "punishment_tax_prev": 0,
      "punishment_tax": 0,
      "punishment_tax_total": 0,
      "property_owner_details": [
            {
                "property_owner_details_id": "252cec7a-0501-4311-aedc-9601c6c13445",
                "name": "Vinayak Patil",
                "owner_type": {
                    "owner_type": "स्वतः"
                }
            },
            {
                "property_owner_details_id": "3e376ff5-a916-45f4-9b76-f85eb2daedb3",
                "name": "Vin Patil",
                "owner_type": {
                    "owner_type": "भोगवटादार"
                }
            }
        ],
        "property": {
          "propertyNumber": "SNP100021",
          "old_propertyNumber": "6145"},

   
   
     
      "tax_property_other_taxes": [
          {
              "tax_type": "दिवाबत्ती कर ",
              "amount_prev": 10,
              "amount": 10,
              "amount_total": 10
          },
          {
              "tax_type": "आरोग्य कर",
              "amount_prev": 5,
              "amount": 5,
              "amount_total": 5
          },
          {
              "tax_type": "सार्वजनिक पाणीपट्टी",
              "amount_prev": 5,
              "amount": 5,
              "amount_total": 5
          },
          {
            "tax_type": "पडसर कर",
            "amount_prev": 10,
            "amount": 10,
            "amount_total": 10
          }
      ]
  };
      res.send(dt)
    
    // return await this.billingService.getForm8Data();
  }

  
  @Form('Payment Bill')
  @Permissions('can_read')
  @Get('generate-invoices')
  generateInvoices() {
    return {
      message: 'Invoice data',
      data: 'Invoice'
    };
    
  }

  
  @Form('Payment Bill')
  @Permissions('can_read')
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.billingService.findOne(+id);
  }

 


}
