import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportTable1723195284623 implements MigrationInterface {
    name = 'ImportTable1723195284623'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "import_property" ("property_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "street_name" character varying NOT NULL, "zone_name" character varying NOT NULL, "propertyNumber" character varying NOT NULL, "old_propertyNumber" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_0cdf1f815d51bd8a85a8177f922" PRIMARY KEY ("property_id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "import_property"`);
    }

}
