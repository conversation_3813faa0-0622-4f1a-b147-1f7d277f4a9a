import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Master_ghanKachra_rateEntity } from 'libs/database/entities';
import {
  MasterGhanKachraRateRepository,
  UsageSubMasterRepository,
} from 'libs/database/repositories';
import { CreateUsageSubTypeDto } from './dto/create-usage-sub-type.dto';
import { UpdateUsageSubTypeDto } from './dto/update-usage-sub-type.dto';

@Injectable()
export class MasterGhanKachraRateService {
  constructor(
    @InjectRepository(MasterGhanKachraRateRepository)
    private readonly ghanKachraRateRepository: MasterGhanKachraRateRepository,
    private readonly usageSubMasterRepository: UsageSubMasterRepository,
  ) {}

  async create(
    data: CreateUsageSubTypeDto,
  ): Promise<{ message: string; data: Master_ghan<PERSON><PERSON>ra_rateEntity }> {

    const usagesubType = await this.usageSubMasterRepository.findById(
      data.usage_sub_type_master_id,
    );

    if (!usagesubType) {
      throw new NotFoundException('UsagesubType not found');
    }

    // Reassessment range ID is optional, but recommended
    if (!data.reassessment_range_id) {
      console.warn('Creating a record without a reassessment range ID is not recommended');
    }

    const newRecord = this.ghanKachraRateRepository.create({
      ...data,
      UsageSubType: usagesubType, // Set the actual entity here
    });

    const savedRecord = await this.ghanKachraRateRepository.save(newRecord);
    return {
      message: 'Ghan Kachra rate created successfully',
      data: savedRecord,
    };
  }

  async findAll(): Promise<{
    message: string;
    data: Master_ghanKachra_rateEntity[];
  }> {
    const allRecords =
      await this.ghanKachraRateRepository.getAllWithUsageSubType();
    return {
      message: 'Ghan Kachra rates fetched successfully',
      data: allRecords,
    };
  }

  async findOne(
    id: string,
  ): Promise<{
    message: string;
    data: Master_ghanKachra_rateEntity | undefined;
  }> {
    const record = await this.ghanKachraRateRepository.findOne({
      where: { ghanKachra_rate_id: id },
      relations: ['UsageSubType', 'reassessmentRange'],
    });
    if (!record) {
      return {
        message: 'Ghan Kachra rate not found',
        data: undefined,
      };
    }
    return {
      message: 'Ghan Kachra rate fetched successfully',
      data: record,
    };
  }

  async update(
    id: string,
    data: any,
  ): Promise<{ message: string; data: Master_ghanKachra_rateEntity }> {
    const updatedRecord = await this.ghanKachraRateRepository.updateGhanKachra(
      id,
      data,
    );
    return {
      message: 'Ghan Kachra rate updated successfully',
      data: updatedRecord.data,
    };
  }

  async delete(id: string): Promise<{ message: string }> {
    await this.ghanKachraRateRepository.softDelete(id);
    return {
      message: 'Ghan Kachra rate deleted successfully',
    };
  }
}
