import React, { createContext, useContext, useState, ReactNode } from "react";

interface FontSizeContextType {
  increaseFontSize: () => void;
  decreaseFontSize: () => void;
  resetFontSize: () => void;
}

const FontSizeContext = createContext<FontSizeContextType | undefined>(
  undefined,
);

export const useFontSize = (): FontSizeContextType => {
  const context = useContext(FontSizeContext);
  if (!context) {
    throw new Error("useFontSize must be used within a FontSizeProvider");
  }
  return context;
};

interface FontSizeProviderProps {
  children: ReactNode;
}

export const FontSizeProvider: React.FC<FontSizeProviderProps> = ({
  children,
}) => {
  const [fontSizeMultiplier, setFontSizeMultiplier] = useState<number>(1);
  const minMultiplier = 0.7;
  const maxMultiplier = 1.2;

  const updateFontSize = (multiplier: number) => {
    const sizes = {
      sm: 0.875,
      base: 1,
      lg: 1.125,
      xl: 1.25,
      "2xl": 1.5,
    };

    Object.keys(sizes).forEach((size) => {
      document.documentElement.style.setProperty(
        `--font-size-${size}`,
        `${sizes[size as keyof typeof sizes] * multiplier}rem`,
      );
    });

    setFontSizeMultiplier(multiplier);
  };

  const increaseFontSize = () => {
    if (fontSizeMultiplier < maxMultiplier) {
      updateFontSize(Math.min(maxMultiplier, fontSizeMultiplier + 0.1));
    }
  };

  const decreaseFontSize = () => {
    if (fontSizeMultiplier > minMultiplier) {
      updateFontSize(Math.max(minMultiplier, fontSizeMultiplier - 0.1));
    }
  };

  const resetFontSize = () => {
    updateFontSize(1);
  };

  return (
    <FontSizeContext.Provider
      value={{ increaseFontSize, decreaseFontSize, resetFontSize }}
    >
      {children}
    </FontSizeContext.Provider>
  );
};
