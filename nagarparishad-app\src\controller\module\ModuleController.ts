import { useContext, useEffect, useState } from "react";
import {
  
  ModuleDataInterface,
} from "@/model/module/moduleInterface"; // Import the interfaces for modules

import { GlobalContext } from "@/context/GlobalContext";
import ModuleApi from "../../services/ModuleServices";

export const useModuleController = () => {
  const [moduleListResponse, setModuleListResponse] = useState<
    ModuleDataInterface[]
  >([]);
  const { refreshModuleList } = useContext(GlobalContext);

  useEffect(() => {
    ModuleApi.getModules((response: { status: boolean; data: any }) => {
      if (response.status && response.data.statusCode === 200) {
        setModuleListResponse(response?.data?.data);
      }
    });
  }, [refreshModuleList]);

  return {
    moduleList: moduleListResponse,
  };
};
