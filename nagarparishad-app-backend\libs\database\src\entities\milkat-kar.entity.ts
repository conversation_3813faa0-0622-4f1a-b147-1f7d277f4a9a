import {
  BaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  JoinColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { PropertyEntity } from './property.entity';
import { MilkatKarTaxEntity } from './milkatKarTax.entity';
import { ReassessmentRange } from './reassesment_range.entity';

@Entity('milkatKar')
export class MilkatKarEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  milkatKar_id: string;

  @ManyToOne(() => PropertyEntity, (property) => property.property_id, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

  @ManyToOne(
    () => ReassessmentRange,
    (reassessmentRange) => reassessmentRange.reassessment_range_id,
    { onDelete: 'SET NULL' },
  )
  @JoinColumn({ name: 'reassessment_range_id' })
  reassessmentRange: ReassessmentRange;

  @Column({
    type: String,
    name: 'financial_year',
    nullable: true,
  })
  @Column({ type: String, nullable: false })
  financial_year: string;

  @Column({ type: 'float', nullable: false, default: 0 })
  all_property_tax_sum: number;

  @Column({ type: 'float', nullable: true })
  tax_type_1: number;

  @Column({ type: 'float', nullable: true })
  tax_type_2: number;

  @Column({ type: 'float', nullable: true })
  tax_type_3: number;

  @Column({ type: 'float', nullable: true })
  tax_type_4: number;

  @Column({ type: 'float', nullable: true })
  tax_type_5: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_6: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_7: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_8: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_9: number;

  @Column({ type: 'float', nullable: true, default: 0 })
  tax_type_10: number;

  @Column({ type: 'float', nullable: false, default: 0 })
  property_type_discount: number;


  @Column({ type: 'float' })
  other_tax_sum_tax: number;

  @Column({ type: 'float' })
  total_tax: number;

  @Column({ type: String, nullable: true })
  status: string;

  // @OneToMany(() => Tax_PropertyWiseEntity, (tax_propertywise) => tax_propertywise.tax_Property)
  // tax_propertywise: Tax_PropertyWiseEntity[];

  // @OneToMany(() => Tax_PropertyEntity_Other_Taxes, (tax_property_other_taxes) => tax_property_other_taxes.tax_Property)
  // tax_property_other_taxes: Tax_PropertyEntity_Other_Taxes[];

  @OneToMany(() => MilkatKarTaxEntity, (milkatKarTax) => milkatKarTax.MilkatKar)
  milkatKarTax: MilkatKarTaxEntity[];

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
