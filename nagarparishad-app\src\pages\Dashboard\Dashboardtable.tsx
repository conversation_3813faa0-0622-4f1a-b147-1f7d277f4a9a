import React from "react";
import { useTranslation } from "react-i18next";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Filter } from "lucide-react";
import formatToINR from "indian-currency-formatter";

interface Dashboardtable {
  title: string;
  data: any;
  viewMore: boolean;
  handleViewMore?: () => void;
  setActivePayTime: (T: boolean) => void;
  activePayTime: boolean | string;
}

const Dashboardtable = ({
  title,
  data,
  viewMore,
  handleViewMore,
  setActivePayTime,
  activePayTime,
}: Dashboardtable) => {
  const { t } = useTranslation();

  const handleFormat = (number: any) => {
    const formattedNumber = formatToINR(number);
    return formattedNumber;
  };

  return (
    <div className="bg-white dark:bg-zinc-700 shadow rounded-2xl p-4 mt-7 overflow-x-auto ">
      <div className="flex justify-between items-center pb-3">
        <div>
          <h2 className="text-lg font-semibold">{title}</h2>
        </div>
        <div className="flex justify-between items-center ">
          <div>
            {viewMore ? (
              ""
            ) : (
              <p className="text-center mr-6 cursor-pointer underline text-blue-800 font-medium"
                onClick={handleViewMore}
              >
                {t("viewMore")}
              </p>
            )}
          </div>
          {viewMore ? (
            ""
          ) : (
            <>
              <div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Filter className="w-6 h-6 cursor-pointer" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-40">
                    <DropdownMenuSeparator />
                    <DropdownMenuCheckboxItem
                      checked={activePayTime ? true : false}
                      onCheckedChange={() => {
                        setActivePayTime(true);
                      }}
                    >
                      Pay tax on time
                    </DropdownMenuCheckboxItem>
                    <DropdownMenuCheckboxItem
                      checked={activePayTime ? false : true}
                      onCheckedChange={() => {
                        setActivePayTime(false);
                      }}
                    >
                      Due tax
                    </DropdownMenuCheckboxItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </>
          )}
        </div>
      </div>
      <table className="w-full text-sm text-left text-zinc-500 dashboard-table">
        <thead className="text-xs text-zinc-700 uppercase bg-zinc-50">
          <tr>
            <th scope="col" className="py-3 sm:px-6 px-3">
              {t("SrNo")}{" "}
            </th>
            <th scope="col" className="py-3 sm:px-6 px-3">
              {t("DashboardTable.name")}
            </th>
            <th scope="col" className="py-3 sm:px-6 px-3">
              {t("DashboardTable.pendingDays")}{" "}
            </th>
            <th scope="col" className="py-3 sm:px-6 px-3">
              {t("DashboardTable.amount")}(₹){" "}
            </th>
          </tr>
        </thead>
        <tbody>
          {data &&
            data !== undefined &&
            data?.map((employee: any, index: any) => (
              <tr
                key={employee.id}
                className={
                  index % 2 === 0 ? "bg-white border-b" : "bg-zinc-50 border-b"
                }
              >
                <td className="py-4 sm:px-6 px-3">{employee.id}</td>
                <td className="py-4 sm:px-6 px-3">{employee.name}</td>
                <td className="py-4 sm:px-6 px-3">{employee.rating}</td>
                <td className="py-4 sm:px-6 px-3">{handleFormat(employee.total)}</td>
              </tr>
            ))}
        </tbody>
      </table>
    </div>
  );
};

export default Dashboardtable;
