# Property Tax Calculation Business Logic

This document outlines the business logic for property tax calculation in the Shirol Nagarparishad Property Tax Management System.

## Overview

The property tax calculation is divided into two main parts:
1.  **`get_property_tax_values`**: Calculates tax values for a single usage component of a property.
2.  **`calculateTaxForProperty`**: Orchestrates the tax calculation for an entire property, which may have multiple usage components, and aggregates the final tax amount.

The final tax is composed of the main property tax plus several cesses and fees.

---

## Detailed Logic

### `get_property_tax_values`

This function calculates tax values for a specific part of a property (a specific floor or usage type).

**Key Inputs:**
*   Zone ID
*   Property Type
*   Construction Year
*   Usage Type
*   Area (in sq. meters)
*   Usage Sub-Type
*   Floor Type

**Core Calculation Steps:**

1.  **Data Retrieval**: The function starts by fetching several rates from the database based on the property's attributes:
    *   **Ready Reckoner (RR) Rate**: Based on the property's Zone. This is the base land value.
    *   **RR Construction Rate**: Based on the class of property construction (e.g., RCC, Patra Shed).
    *   **Depreciation Rate**: Calculated based on the age of the building. The older the building, the higher the depreciation.
    *   **Weighting Rate**: A multiplier based on the Usage Type (e.g., residential, commercial, industrial).
    *   **Tax Rate**: A percentage applied to the capital value to determine the base tax.
    *   **Solid Waste Fee (GhanKachra)**: A fixed amount based on the usage sub-type.

2.  **Capital Value Calculation**: This is the most crucial step. The formula for `capital_value` changes based on the property and floor type:
    *   **For Open Land (पडसर)**:
        ```
        capital_value = (Area * RR Rate) * Weighting Rate
        ```
    *   **For Ground Floor ("तळ मजला")**: It includes both land value and construction cost.
        ```
        capital_value = ((Area * RR Rate) + (Area * RR Construction Rate * Depreciation Rate)) * Weighting Rate
        ```
    *   **For Upper Floors (e.g., "पहिला मजला")**: It only considers the construction cost, as the land value is already accounted for in the ground floor.
        ```
        capital_value = (Area * RR Construction Rate * Depreciation Rate) * Weighting Rate
        ```

3.  **Tax Calculation**:
    *   **`tax_value`**: The capital value is multiplied by the tax rate.
        ```
        tax_value = capital_value * Tax Rate
        ```
    *   **`tax` (Final Property Tax for the usage)**: Depreciation is applied to the `tax_value`.
        ```
        tax = tax_value * (1 - Depreciation Rate)
        ```

4.  **Cess Calculations**: Several cesses are calculated as a percentage of the `capital_value`.
    *   **Vruksh Kar (Tree Cess) (`tax_type_1`)**: `capital_value * 0.0001`
    *   **Shikhan Kar (Education Cess) (`tax_type_2`)**: `capital_value * 0.0001`
    *   **Rojagar Hami Kar (Employment Guarantee Cess) (`tax_type_3`)**: `capital_value * 0.0001` (not applicable for residential properties).
    *   These cesses are disabled for certain usage types like "Open Space," "Religious," and "Padsar."

**Output:**
The function returns an object with all the calculated values, including rates, capital value, tax, and the various cesses.

---

### `calculateTaxForProperty`

This function aggregates the taxes for an entire property.

**Logic:**

1.  **Iterate and Calculate**: It fetches all usage components of a property and calls `get_property_tax_values` for each one.

2.  **Aggregate Taxes**: It sums up the `tax` and all the cesses (`vruskh_kar`, `shikhan_kar`, etc.) from all usage components.

3.  **Apply Discounts**:
    *   **Religious Usage (धार्मिक)**: Gets a 100% discount on the `tax`.
    *   **Educational Usage (शैक्षणिक)**: Gets a 50% discount on the `tax`.

4.  **Calculate Shasti Fee**: A fee is applied for unauthorized constructions.
    *   The total unauthorized area of the property is calculated.
    *   **For Commercial/Industrial Usage**: Shasti Fee is `2 * tax`.
    *   **For Residential Usage**:
        *   Area <= 600 sq. ft: No Shasti Fee.
        *   600 < Area <= 1000 sq. ft: Shasti Fee is `0.5 * tax`.
        *   Area > 1000 sq. ft: Shasti Fee is `2 * tax`.

5.  **Calculate Final Total Tax**:
    The `total_tax` is the sum of:
    *   Total Property Tax (after discounts)
    *   Total Tree Cess (`tax_type_1`)
    *   Total Education Cess (`tax_type_2`)
    *   Total Employment Guarantee Cess (`tax_type_3`)
    *   The highest Solid Waste Fee (GhanKachra) (`tax_type_4`)
    *   Total Shasti Fee (`tax_type_5`)

6.  **Save to Database**:
    *   The previously calculated tax record for the property is marked as 'inactive'.
    *   A new `MilkatKar` record is created with the new `total_tax` and breakdown for the current financial year.
    *   A detailed snapshot of each usage calculation (`MilkatKarTax`) is also saved for auditing purposes.

---
### Tax Types Helper
The following are the different tax types used in the system, as defined in `@nagarparishad-app-backend\libs\helpers\src\tax-types.helper.ts`:
*   `tax_type_1`: 'वृक्ष उपकर' (Tree Cess)
*   `tax_type_2`: 'शिक्षण उपकर' (Education Cess)
*   `tax_type_3`: 'रोजगार हमी कर' (Employment Guarantee Cess)
*   `tax_type_4`: 'घनकचरा शुल्क' (Solid Waste Fee)
*   `tax_type_5`: 'अनधिकृत शास्ती कर' (Unauthorized Shasti Tax)
*   `tax_type_6`: 'दिवाबत्ती कर' (Light Tax)
*   `tax_type_7`: 'आरोग्य कर' (Health Tax)
*   `tax_type_8`: 'पडसर कर' (Padsar Tax)
*   `tax_type_9`: 'दंड' (Penalty)
*   `tax_type_10`: 'अग्निशमन फी' (Fire Fighting Fee)
