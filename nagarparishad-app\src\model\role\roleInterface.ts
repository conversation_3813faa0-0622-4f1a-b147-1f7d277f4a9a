export interface CreatRoleInterface {
  roleName: string;
  createdBy?: string;
}

export interface RoleInterface {
  role_id: number;
  roleName: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface RoleModuleInterface {
  statusCode: number;
  message: string;
  data: RoleInterface[];
}

export interface RoleDetailModuleInterface {
  statusCode: number;
  message: string;
  data: RoleInterface;
}
