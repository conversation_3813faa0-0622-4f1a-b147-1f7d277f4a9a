export interface PropertysubtypeMasterObject {
  propertyType: string;
  propertySub_name: string;
  createdAt?: string;
  updatedAt?: string;
  deleteAt?: string | null;
}

export interface PropertysubtypeListAllApi {
  statusCode: number;
  message: string;
  data: PropertysubtypeMasterObject[];
}

export interface PropertysubtypeCreateApi {
  statusCode: number;
  message: string;
  data: PropertysubtypeMasterObject;
}
export interface PropertysubtypeUpdateApi {
  statusCode: number;
  message: string;
}

export interface PropertysubtypeSendApiObj {
  propertySub_name: string;
  propertyType: string;
}

export interface PropertysubtypeListObject {
  propertySub_id: string;
  propertySub_name: string;
}
