import {
  BaseEntity,
  <PERSON>umn,
  CreateDateC<PERSON>umn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToOne,
  Unique,
  OneToMany,
} from 'typeorm';
import { ZoneMaster } from './zoneMaster.entity';
import { UsageSubTypeMasterEntity } from './usage-sub-type-master.entity';
import { UsageTypeMasterEntity } from './usage-type-master.entity';
import { ReassessmentRange } from './reassesment_range.entity';

@Entity('master_ghanKachra_rate')

export class Master_ghanKachra_rateEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  ghanKachra_rate_id: string;

  @ManyToOne(() => UsageSubTypeMasterEntity, (usageSubType) => usageSubType.usage_sub_type_master_id)
  @JoinColumn({ name: 'usage_sub_type_master_id' })
  UsageSubType: UsageSubTypeMasterEntity;

  @ManyToOne(() => UsageTypeMasterEntity, (usage_type) => usage_type.usage_type_id)
  @JoinColumn({ name: 'usage_type_id' })
  usage_type: UsageTypeMasterEntity;

  @Column({ type: String, nullable: true })
  financial_year: string;

  @Column({ type: 'float', nullable: false, default: 0 })
  value: number;

  @Column({ type: String, nullable: true, default: 'Active' })
  status: string;

  @ManyToOne(() => ReassessmentRange, (reassessmentRange) => reassessmentRange.reassessment_range_id, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'reassessment_range_id' })
  reassessmentRange: ReassessmentRange;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
