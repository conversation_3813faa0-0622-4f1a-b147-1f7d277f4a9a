// src/rr-rate-master/dto/create-rr-rate.dto.ts
import { IsString, IsNumber, IsUUID, IsNotEmpty, IsIn, IsOptional } from 'class-validator';

export class CreateRrRateDto {
  @IsString()
  @IsOptional()
  financial_year?: string; // Optional for backward compatibility

  @IsString()
  @IsOptional()
  reassessment_range_id?: string; // Reassessment range ID

  @IsNumber()
  @IsNotEmpty()
  value: number; // Value for the RR rate

  @IsString()
  @IsIn(['Active', 'Inactive'])
  status: string; // Must be 'Active' or 'Inactive'

  @IsUUID()
  @IsNotEmpty()
  zone_id: string; // Required for RR rate creation
}
