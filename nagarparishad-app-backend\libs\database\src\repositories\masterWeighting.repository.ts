import { Repository } from 'typeorm';
import { Master_weighting_rateEntity, ReassessmentRange } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { UsageMasterRepository } from './usage-master.repository';

export class Master_WeightingRepository extends Repository<Master_weighting_rateEntity> {
  constructor(
    @InjectRepository(Master_weighting_rateEntity)
    private readonly master_weighting_rateEntity: Repository<Master_weighting_rateEntity>,
    private readonly UsageMasterRepository: UsageMasterRepository,
  ) {
    super(
      master_weighting_rateEntity.target,
      master_weighting_rateEntity.manager,
      master_weighting_rateEntity.queryRunner,
    );
  }
  async getAllWithUsageType(): Promise<Master_weighting_rateEntity[]> {
    return this.master_weighting_rateEntity.find({
      relations: ['usage_type','reassessmentRange'], // Include property class relation
    });
  }
  async updateWeightingRate(
    id: string,
    data: any,
  ): Promise<{ message: string; data?: Master_weighting_rateEntity }> {
    // Check if the record exists
    const existingRecord = await this.master_weighting_rateEntity.findOne({
      where: { weighting_rate_id: id }, // Adjust this to your actual ID field
      relations: ['usage_type', 'reassessmentRange'], // Include related usage type if necessary
    });

    if (!existingRecord) {
      return {
        message: 'No record found to update',
      };
    }

    // Check if the usage_type_id is provided in the data
    if (data.usage_type_id) {
      const usageType = await this.UsageMasterRepository.findById(
        data.usage_type_id,
      );
      if (usageType) {
        existingRecord.usage_type = usageType; // Assign the fetched usage type to the existing record
      } else {
        return {
          message: 'Usage type not found',
        };
      }
    }

    // Handle reassessment range if provided
    if (data.reassessment_range_id) {
      const reassessmentRange = await this.master_weighting_rateEntity.manager
        .getRepository(ReassessmentRange)
        .findOne({
          where: { reassessment_range_id: data.reassessment_range_id }
        });
      if (reassessmentRange) {
        existingRecord.reassessmentRange = reassessmentRange;
      }
    }

    // Update the properties of the existing record
    Object.assign(existingRecord, data);

    // Save the updated record back to the database
    await this.master_weighting_rateEntity.save(existingRecord);

    return {
      message: 'Weighting rate updated successfully',
      data: existingRecord,
    };
  }


  async genrateForNewFinancialYear(newFinancialYear: string, currentReassessmentRange: ReassessmentRange): Promise<{ message: string; data?: Master_weighting_rateEntity[] }> {
    // Fetch all existing records
    const existingRecords = await this.getAllWithUsageType();

    if (existingRecords.length === 0) {
      return {
        message: 'No records found to duplicate',
      };
    }

    const duplicatedRecords: Master_weighting_rateEntity[] = [];

    for (const record of existingRecords) {
      // Create a new record based on the existing one
      const newRecord = new Master_weighting_rateEntity();
      newRecord.financial_year = newFinancialYear; // Keep for backward compatibility
      newRecord.value = record.value;
      newRecord.status = record.status;
      newRecord.usage_type = record.usage_type;
      newRecord.reassessmentRange = currentReassessmentRange;

      // Save the new record to the database
      const savedRecord = await this.master_weighting_rateEntity.save(newRecord);
      duplicatedRecords.push(savedRecord);
    }

    return {
      message: 'Records generated successfully for the new reassessment range',
      data: duplicatedRecords,
    };
  }

}
