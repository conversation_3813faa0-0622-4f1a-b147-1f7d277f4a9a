import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
  } from 'typeorm';
  
  @Entity('property_type_class')
  export class Property_type_class_master extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    property_type_class_id: string;
  
    @Column({
      type: String,
      name: 'property_type_class',
      nullable: false,
    })
    property_type_class: string;
  

   
    /*
     * Create and Update Date Columns
     */
  
    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;
  
    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;
  
    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
  }
  