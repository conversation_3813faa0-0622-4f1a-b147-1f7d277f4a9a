import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Api from "../../services/ApiServices";


const fetchConstruction = () => {
  return new Promise((resolve, reject) => {
    Api.getAllConstructionClassList((response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(new Error("Error fetching construction class list"));
      }
    });
  });
};


const createConstructionClass = (newClass:any) => {
  return new Promise((resolve, reject) => {
    Api.createConstructionClass(newClass, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(new Error("Error creating construction class"));
      }
    });
  });
};


const updateConstructionClass = ({
  classId,
  classData,
}: {
  classId: any;
  classData: any;
}) => {
  return new Promise((resolve, reject) => {
    Api.updateConstructionClass(classId, classData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(new Error("Error updating construction class"));
      }
    });
  });
};


const deleteConstructionClass = (classId: any) => {
  return new Promise((resolve, reject) => {
    Api.deleteConstructionClass(classId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(new Error("Error deleting construction class"));
      }
    });
  });
};


export const useConstructionClassController = () => {
  const queryClient = useQueryClient();


  const {
    data: constructionClassList,
    error,
    isLoading,
  } = useQuery({
    queryKey: ["constructionmaster"],
    queryFn: fetchConstruction,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

 
  const createClassMutation = useMutation({
    mutationFn: createConstructionClass,
    onMutate: async (newClass) => {
      await queryClient.cancelQueries({ queryKey: ["constructionmaster"] });

      const previousClasses = queryClient.getQueryData(["constructionmaster"]);

      queryClient.setQueryData(["constructionmaster"], (old: any) => [
        newClass,
        ...(old as any),
      ]);

      return { previousClasses };
    },
    onError: (err, newClass, context) => {
      queryClient.setQueryData(["constructionmaster"], context.previousClasses);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["constructionmaster"] });
    },
  });

  // Mutation for updating an existing construction class
  const updateClassMutation = useMutation({
    mutationFn: updateConstructionClass,
    onMutate: async ({ classId, classData }) => {
      await queryClient.cancelQueries({ queryKey: ["constructionmaster"] });

      const previousClasses = queryClient.getQueryData(["constructionmaster"]);

      queryClient.setQueryData(["constructionmaster"], (old: any) =>
        old.map((cls: any) =>
          cls.constructionClass_id === classId ? { ...cls, ...classData } : cls,
        ),
      );

      return { previousClasses };
    },
    onError: (err, { classId, classData }, context) => {
      queryClient.setQueryData(["constructionmaster"], context.previousClasses);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["constructionmaster"] });
    },
  });

  // Mutation for deleting a construction class
  const deleteClassMutation = useMutation({
    mutationFn: deleteConstructionClass,
    onMutate: async (classId) => {
      await queryClient.cancelQueries({ queryKey: ["constructionmaster"] });

      const previousClasses = queryClient.getQueryData(["constructionmaster"]);

      queryClient.setQueryData(["constructionmaster"], (old: any) =>
        old.filter((cls: any) => cls.constructionClass_id !== classId),
      );

      return { previousClasses };
    },
    onError: (err, classId, context) => {
      queryClient.setQueryData(["constructionmaster"], context.previousClasses);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["constructionmaster"] });
    },
  });
  console.log("contrustion", constructionClassList);
  return {
    constructionClassList: constructionClassList || [],
    createConstructionClass: createClassMutation.mutate,
    updateConstructionClass: updateClassMutation.mutate,
    deleteConstructionClass: deleteClassMutation.mutate,
    error,
    isLoading,
  };
};
