import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedColumneForCreatedtime1756891554369 implements MigrationInterface {
    name = 'AddedColumneForCreatedtime1756891554369'

    public async up(queryRunner: QueryRunner): Promise<void> {
                await queryRunner.query(`ALTER TABLE "previous_property_owners" ADD "record_created_time" TIMESTAMP`);

         }

    public async down(queryRunner: QueryRunner): Promise<void> {
                await queryRunner.query(`ALTER TABLE "previous_property_owners" ADD "record_created_time" TIMESTAMP`);

      }

}
