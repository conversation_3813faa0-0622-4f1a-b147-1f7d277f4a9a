import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { useTranslation } from "react-i18next";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useAdministrativeBoundaryController } from "@/controller/master/AdministrativeBoundaryController";

interface AdministrativeBoundaryFormInterface {
  btnTitle: string;
  editData?: any;
}

const AdministrativeBoundaryForm = ({
  btnTitle,
  editData,
}: AdministrativeBoundaryFormInterface) => {
  const { t } = useTranslation();
  const schema = z.object({
    boundaryName: z.string().trim().min(1, t("errorsRequiredField")),
  });
  const dynamicValues = {
    name: t("administrativeboundary.boundaryName"),
  };
  const { createAdministrativeBoundary, updateAdministrativeBoundary } =
    useAdministrativeBoundaryController();
  const {
    refreshadministrativeBoundaryList,
    setRefreshadministrativeBoundaryList,
  } = useContext(GlobalContext);
  const [loader, setLoader] = useState(false);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      boundaryName: editData?.adminstrativeBoundary_name || "",
    },
  });

  const {
    formState: { errors },
  
  } = form;

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();

    const dataResponse = {
      adminstrativeBoundary_name: data.boundaryName,
    };

    if (
      editData?.adminstrativeBoundary_id !== undefined &&
      editData?.adminstrativeBoundary_id !== null
    ) {
      setLoader(true);
      updateAdministrativeBoundary(
        {
          administrativeBoundaryId: editData.adminstrativeBoundary_id,
          administrativeBoundaryData: dataResponse,
        },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            form.reset({
              boundaryName: "",
            });
            setRefreshadministrativeBoundaryList(
              !refreshadministrativeBoundaryList,
            );
            setLoader(false);
          },
          onError: (error: any) => {
            toast({
              title: error.message,
              variant: "destructive",
            });
            setLoader(false);
          },
        },
      );
    } else {
      setLoader(true);
      createAdministrativeBoundary(dataResponse, {
        onSuccess: () => {
          toast({
            title: t("api.formupdate", dynamicValues),
            variant: "success",
          });
          form.reset({
            boundaryName: "",
          });
          setRefreshadministrativeBoundaryList(
            !refreshadministrativeBoundaryList,
          );
          setLoader(false);
        },
        onError: (error: any) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });
    }
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        boundaryName: editData?.adminstrativeBoundary_name || "",
      });
    } else {
      form.reset({
        boundaryName: "",
      });
    }
  }, [editData]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full md:w-full"
        >
          <div className="form-flex">
            <div className="form-element">
              <FormField
                control={form.control}
                name="boundaryName"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t("administrativeboundary.boundaryName")}
                    </FormLabel>
                    <FormControl className="mt-1">
                      <Input
                        type="text"
                        placeholder={t(
                          "administrativeboundary.classNamePlaceholder",
                        )}
                        {...field}
                      />
                    </FormControl>
                    {errors.boundaryName && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="w-full flex justify-end mt-4">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
        </form>
      </Form>
    </>
  );
};

export default AdministrativeBoundaryForm;
