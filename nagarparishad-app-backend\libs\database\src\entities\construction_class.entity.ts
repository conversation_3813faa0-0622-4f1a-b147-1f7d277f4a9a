import {
  BaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('construction_class')
export class ConstructionClassEnitity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  constructionClass_id: string;

  @Column({ type: String, name: 'construction_class_name', nullable: false })
  constructionClassName: string;

  @Column({ type: String, name: 'construction_class_marathi', nullable: false })
  constructionClassMarathi: string;

  @Column({ type: String, name: 'values', nullable: false })
  values: string;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
