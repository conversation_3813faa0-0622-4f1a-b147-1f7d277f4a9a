import { Body, Controller, Get, Param, Post, Put, Query } from '@nestjs/common';
import { AnnualKarAkaraniService } from './annual-kar-akarani.service';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UpdateTaxDetailsDto } from './dto/update-tax-types.dto';
import { Public } from '@jwt/jwt-auth/decorators/public.decorator';

@ApiTags('Annual Kar Akarani')
@Controller('annual-kar-akarani')
export class AnnualKarAkaraniController {
  constructor(private readonly annualKarAkaraniService: AnnualKarAkaraniService) {}

  @ApiOperation({ summary: 'Get all Warshik Kar Akarni records' })
  @ApiResponse({ status: 200, description: 'Returns all Warshik Kar Akarni records' })
  @Get()
  async getWarshikKarAkarniAll(@Query() params) {
    return await this.annualKarAkaraniService.getWarshikKarAkarniAll(params);
  }

  @ApiOperation({ summary: 'Process Warshik Kar Akarni' })
  @ApiResponse({ status: 200, description: 'Successfully processed Warshik Kar Akarni' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @Get('processWarshikKarAkarni')
  async processWarshikKarAkarani(@Query() params: any) {
    if (params.financialYearId) {
      return await this.annualKarAkaraniService.processWarshikKarAkaraniByFinancialYear(params.financialYearId);
    } else if (params.ward_number && params.financialYearId) {
      return await this.annualKarAkaraniService.processWarshikKarAkaraniByWard(params.ward_number, params.financialYearId);
    } else {
      return await this.annualKarAkaraniService.processWarshikKarAkarani(params);
    }
  }

  @Public()
  @ApiOperation({ summary: 'Process Warshik Kar Akarni in batches' })
  @ApiResponse({ status: 200, description: 'Successfully processed Warshik Kar Akarni in batches' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @Get('processWarshikKarAkarniBatch')
  async processWarshikKarAkaraniBatch(
    @Query('financialYearId') financialYearId: string,
    @Query('batchSize') batchSize: number = 50
  ) {
    if (!financialYearId) {
      throw new Error('Financial year ID is required');
    }
    return await this.annualKarAkaraniService.processWarshikKarAkaraniByFinancialYearBatch(financialYearId, batchSize);
  }

  @ApiOperation({ summary: 'Process Warshik Kar Akarni for a single property' })
  @ApiResponse({ status: 200, description: 'Successfully processed Warshik Kar Akarni for the property' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @Get('processWarshikKarForSingleProperty')
  async processWarshikKarForSingleProperty(
    @Query('financialYearId') financialYearId: string,
    @Query('propertyId') propertyId: string
  ) {
    if (!financialYearId) {
      throw new Error('Financial year ID is required');
    }
    if (!propertyId) {
      throw new Error('Property ID is required');
    }
    return await this.annualKarAkaraniService.processWarshikKarForSingleProperty(financialYearId, propertyId);
  }

  @ApiOperation({ summary: 'Get Warshik Kar data by reassessment range' })
  @ApiResponse({ status: 200, description: 'Returns Warshik Kar data for the specified reassessment range' })
  @Get('by-reassessment-range')
  async getWarshikKarDataByReassessmentRange(@Query('reassessmentYearId') reassessmentYearId: string) {
    return await this.annualKarAkaraniService.getWarshikKarDataByReassessmentRange(reassessmentYearId);
  }

  @ApiOperation({ summary: 'Get ward-wise Warshik Kar generation status' })
  @ApiResponse({ status: 200, description: 'Returns ward-wise generation status for the specified financial year' })
  @Get('/getWardWiseWarshikKarStatus')
  async getWardWiseWarshikKarStatus(@Query('financialYearId') financialYearId: string) {
    return await this.annualKarAkaraniService.getWardWiseWarshikKarStatus(financialYearId);
  }

  @ApiOperation({ summary: 'Get remaining Warshik Kar count for a ward' })
  @ApiResponse({ status: 200, description: 'Returns count of properties that need Warshik Kar generation' })
  @Get('/getWarshikKarRemainingCount')
  async getWarshikKarRemainingCount(
    @Query('ward_number') wardNumber: string,
    @Query('financial_year') financialYear: string,
  ) {
    return await this.annualKarAkaraniService.getWarshikKarRemainingCount(wardNumber, financialYear);
  }



  @ApiOperation({ summary: 'Get a specific Warshik Kar Akarni record' })
  @ApiResponse({ status: 200, description: 'Returns the requested Warshik Kar Akarni record' })
  @ApiResponse({ status: 404, description: 'Record not found' })
  @Get('getVarshikKarAkarni')
  async getWarshikKarAkarni(@Query() params: any) {
    return await this.annualKarAkaraniService.getWarshikKarAkarni(params.value, params.searchOn, params.fy);
  }

  @ApiOperation({ summary: 'Update tax data for a specific record' })
  @ApiResponse({ status: 200, description: 'Successfully updated tax data' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 404, description: 'Record not found' })
  @Put('/update-taxData')
  async updateTaxData(@Body('id') id: string) {
    return this.annualKarAkaraniService.updateTaxData(id);
  }

  @ApiOperation({ summary: 'Update all tax data records' })
  @ApiResponse({ status: 200, description: 'Successfully updated all tax data records' })
  @Put('/update-all-taxData')
  async updatellTaxData() {
    return this.annualKarAkaraniService.updateAllTaxData();
  }


  @ApiOperation({ summary: 'Update tax types' })
  @ApiResponse({ status: 200, description: 'Successfully updated all tax types' })
  @Put('/updateWarshikKarAkarni')
  async updateTaxTypes(
    @Query('warshikKar_id') warshikKar_id: string,
    @Query('property_id') property_id: string,
    @Body() updateTaxTypes: UpdateTaxDetailsDto
  ) {
    return this.annualKarAkaraniService.updatetaxtypes({ warshikKar_id, property_id, updateTaxTypes });
  }

  @ApiOperation({ summary: 'update demand report' })
  @ApiResponse({ status: 200, description: 'Successfully updated financial year' })
  @Put('/restore-demand-report')
  async () {
    return this.annualKarAkaraniService.updateDemandReport();
  }

}
