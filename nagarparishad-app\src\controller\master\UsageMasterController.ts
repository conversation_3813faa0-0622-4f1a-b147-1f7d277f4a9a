import { useContext, useEffect, useState } from "react";
import Api from "../../services/ApiServices";
import {
  UsageMasterApiResponse,
  UsageObjectInterface,
} from "../../model/usage-master";
import { GlobalContext } from "@/context/GlobalContext";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

const fetchUsages = () => {
  return new Promise((resolve, reject) => {
    Api.getAllUsageList((response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(new Error("Error fetching locations"));
      }
    });
  });
};

const createUsage = async (newUsage) => {
  return new Promise((resolve, reject) => {
    Api.createUsage(newUsage, (response) => {
      if (response.status && response.data.statusCode === 201) {
        resolve(response.data.data); // Return the created usage data
      } else {
        reject(new Error("Error creating usage"));
      }
    });
  });
};
const updateUsage = async ({ usageId, usageData }) => {
  return new Promise((resolve, reject) => {
    Api.updateUsage(usageId, usageData, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data); // Return the updated usage data
      } else {
        reject(new Error("Error updating usage"));
      }
    });
  });
};

const deleteUsage = async (usageId) => {
  return new Promise((resolve, reject) => {
    Api.deleteUsage(usageId, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data);
      } else {
        reject(new Error("Error deleting usage"));
      }
    });
  });
};
export const useUsageMasterController = () => {
  const queryClient = useQueryClient();

  const { data: usagedata, isLoading:usageLoading } = useQuery({
    queryKey: ["usagemaster"],
    queryFn: fetchUsages,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });
  const createUsageMutation = useMutation({
    mutationFn: createUsage,
    onMutate: async (newUsage) => {
      await queryClient.cancelQueries({ queryKey: ["usagemaster"] });

      const previousUsages = queryClient.getQueryData(["usagemaster"]);
      console.log("previousUsages", previousUsages);

      queryClient.setQueryData(["usagemaster"], (old) => [newUsage, ...old]);

      return { previousUsages };
    },
    onError: (err, newUsage, context) => {
      queryClient.setQueryData(["usagemaster"], context.previousUsages);
      console.error("Error creating usage:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["usagemaster"] });
    },
  });

  const updateUsageMutation = useMutation({
    mutationFn: updateUsage,
    onMutate: async ({ usageId, usageData }) => {
      await queryClient.cancelQueries({ queryKey: ["usagemaster"] });

      const previousUsages = queryClient.getQueryData(["usagemaster"]);
      console.log("usagedata", usagedata);
      console.log("updadte usage", { usageId, usageData });

      queryClient.setQueryData(["usagemaster"], (old) =>
        old.map((usage) =>
          usage.usage_id === usageId ? { ...usage, ...usageData } : usage,
        ),
      );

      return { previousUsages };
    },
    onError: (err, { usageId, usageData }, context) => {
      queryClient.setQueryData(["usagemaster"], context.previousUsages);
      console.error("Error updating usage:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["usagemaster"] });
    },
  });

  const deleteUsageMutation = useMutation({
    mutationFn: deleteUsage,
    onMutate: async (usageId) => {
      await queryClient.cancelQueries({ queryKey: ["usagemaster"] });

      const previousUsages = queryClient.getQueryData(["usagemaster"]);

      queryClient.setQueryData(["usagemaster"], (old) =>
        old.filter((usage) => usage.usage_id !== usageId),
      );

      return { previousUsages };
    },
    onError: (err, usageId, context) => {
      queryClient.setQueryData(["usagemaster"], context.previousUsages);
      console.error("Error deleting usage:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["usagemaster"] });
    },
  });

  return {
    usageList: usagedata || [],
  usageLoading,
    createUsage: createUsageMutation.mutate,
    updateUsage: updateUsageMutation.mutate,
    deleteUsage: deleteUsageMutation.mutate,
  };
};
