import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Master_weighting_rateEntity } from 'libs/database/entities';
import { Master_WeightingRepository,UsageMasterRepository } from 'libs/database/repositories';

@Injectable()
export class Master_weightingService {
  constructor(
    @InjectRepository(Master_WeightingRepository)
    private readonly weightingRepository: Master_WeightingRepository,
    private readonly UsageMasterRepository: UsageMasterRepository,

  ) {}

  async create(data: any): Promise<{ message: string; data: Master_weighting_rateEntity[] }> {
    const usageType =
    await this.UsageMasterRepository.findById(
      data.usage_type_id,
    ); // Replace with actual logic to find zone

  if (!usageType) {
    throw new NotFoundException('propertyTypeClass not found');
  }

  // Handle reassessment range if provided
  let reassessmentRange = null;
  if (data.reassessment_range_id) {
    const { ReassessmentRange } = await import('libs/database/entities');
    reassessmentRange = await this.weightingRepository.manager
      .getRepository(ReassessmentRange)
      .findOne({
        where: { reassessment_range_id: data.reassessment_range_id }
      });
    if (!reassessmentRange) {
      throw new NotFoundException('Reassessment range not found');
    }
  }

  // Create a new instance of Master_rr_construction_rate
  const newRate = this.weightingRepository.create({
    ...data,
    usage_type: usageType, // Set the actual zone entity here
    reassessmentRange: reassessmentRange, // Set the reassessment range entity
  });


    const savedWeighting = await this.weightingRepository.save(newRate);
    return {
      message: 'Weighting rate created successfully',
      data: savedWeighting,
    };
  }

  async findAll(): Promise<{ message: string; data: Master_weighting_rateEntity[] }> {
    const allWeightings = await this.weightingRepository.getAllWithUsageType();
    return {
      message: 'Weighting rates fetched successfully',
      data: allWeightings,
    };
  }

  // async findOne(id: string): Promise<{ message: string; data: Master_weighting_rateEntity | undefined }> {
  //   const weighting = await this.weightingRepository.findOne(id);
  //   if (!weighting) {
  //     return {
  //       message: 'Weighting rate not found',
  //       data: undefined,
  //     };
  //   }
  //   return {
  //     message: 'Weighting rate fetched successfully',
  //     data: weighting,
  //   };
  // }

  async update(id: string, data: any): Promise<{ message: string; data: Master_weighting_rateEntity | undefined }> {
    const updatedWeighting = await this.weightingRepository.updateWeightingRate(id, data);
    return {
      message: 'Weighting rate updated successfully',
      data: updatedWeighting.data,
    };
  }

  async delete(id: string): Promise<{ message: string }> {
    await this.weightingRepository.softDelete(id);
    return {
      message: 'Weighting rate deleted successfully',
    };
  }
}
