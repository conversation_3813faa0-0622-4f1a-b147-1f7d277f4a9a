import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString, IsUUID } from 'class-validator';

export class PropertyFerfarDeleteDto {
  @ApiProperty({ description: 'Property ID', example: 'uuid' })
  @IsUUID()
  property_id: string;

  @ApiProperty({ description: 'Remark for the deletion operation', example: 'Owner removed due to property sale' })
  @IsString()
  @IsOptional()
  remark?: string;

  @ApiProperty({ description: 'Array of owner IDs to delete', type: [String] })
  @IsArray()
  owner_ids: string[];

  @ApiProperty({ description: 'Array of photo image paths', type: [String] })
  @IsArray()
  @IsOptional()
  photos?: string[];

  @ApiProperty({ description: 'Array of document image paths', type: [String] })
  @IsArray()
  @IsOptional()
  documents?: string[];
}
