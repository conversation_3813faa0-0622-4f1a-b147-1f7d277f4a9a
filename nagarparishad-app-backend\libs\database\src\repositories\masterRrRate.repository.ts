import { Repository } from 'typeorm';
import { Master_rr_rateEntity, ReassessmentRange } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { ZoneMasterRepository } from './zone-master.repository';

export class Master_rr_rateRepository extends Repository<Master_rr_rateEntity> {
  constructor(
    @InjectRepository(Master_rr_rateEntity)
    private readonly master_rr_rateRepository: Repository<Master_rr_rateEntity>,
    private readonly zoneRepository: ZoneMasterRepository,

  ) {
    super(
      master_rr_rateRepository.target,
      master_rr_rateRepository.manager,
      master_rr_rateRepository.queryRunner,
    );
  }
  async getAllWithZone(): Promise<Master_rr_rateEntity[]> {
    return this.master_rr_rateRepository.find({
      relations: ['zone','reassessmentRange'],  // Include property class relation
    });
}

async updateRR_rate(
  id: string,
  data: any,
): Promise<{ message: string; data: Master_rr_rateEntity }> {
  // Check if the record exists
  const existingRecord = await this.master_rr_rateRepository.findOne({
    where: { rr_rate_id: id }, // Ensure to match the correct ID field
    relations: ['zone', 'reassessmentRange'], // Ensure the relation is fetched
  });

  if (!existingRecord) {
    return {
      message: 'No record found to update',
      data: undefined,
    };
  }
  if (data.zone_id) {

    const zone = await this.zoneRepository.findById(data.zone_id); // Adjust to your zone entity structure
    if (zone) {
      existingRecord.zone = zone; // Assign the fetched zone to the existing record
    } else {
      return {
        message: 'Zone not found',
        data: undefined,
      };
    }
  }

  // Handle reassessment range if provided
  if (data.reassessment_range_id) {
    const reassessmentRange = await this.master_rr_rateRepository.manager
      .getRepository(ReassessmentRange)
      .findOne({
        where: { reassessment_range_id: data.reassessment_range_id }
      });
    if (reassessmentRange) {
      existingRecord.reassessmentRange = reassessmentRange;
    }
  }

  // Update the properties of the existing record
  Object.assign(existingRecord, data);

  // Save the updated record back to the database
  await this.master_rr_rateRepository.save(existingRecord);

  return {
    message: 'RR rate updated successfully',
    data: existingRecord,
  };
}

async generateForNewFinancialYear(newFinancialYear: string, currentReassessmentRange: ReassessmentRange): Promise<{ message: string; data?: Master_rr_rateEntity[] }> {
  const existingRecords = await this.getAllWithZone();

  if (existingRecords.length === 0) {
    return {
      message: 'No records found to duplicate',
    };
  }

  const duplicatedRecords: Master_rr_rateEntity[] = [];

  for (const record of existingRecords) {
    const newRecord = new Master_rr_rateEntity();
    newRecord.financial_year = newFinancialYear; // Keep for backward compatibility
    newRecord.value = record.value;
    newRecord.status = record.status;
    newRecord.zone = record.zone;
    newRecord.reassessmentRange = currentReassessmentRange;

    const savedRecord = await this.master_rr_rateRepository.save(newRecord);
    duplicatedRecords.push(savedRecord);
  }

  return {
    message: 'Records generated successfully for the new reassessment range',
    data: duplicatedRecords,
  };
}


}
