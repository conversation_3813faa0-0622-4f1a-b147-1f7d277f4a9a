import { Injectable, Logger } from '@nestjs/common';
import {
  PenaltyFeeYearWiseRepository,
  WarshikKarRepository,
  PaidDataRepository,
  Financial_yearRepository,
} from 'libs/database/repositories';

@Injectable()
export class PenaltyFeeYearWiseService {
  private readonly logger = new Logger(PenaltyFeeYearWiseService.name);

  constructor(
    private readonly penaltyFeeYearWiseRepository: PenaltyFeeYearWiseRepository,
    private readonly warshikKarRepository: WarshikKarRepository,
    private readonly paidDataRepository: PaidDataRepository,
    private readonly financialYearRepository: Financial_yearRepository,
  ) {}

  async createPenaltyFee(input: {
    property: string;
    financial_year: string;
    actual_value: number;
    tax_percentage: number;
  }) {
    return this.penaltyFeeYearWiseRepository.savePenaltyFee(input);
  }

  async getAllPenaltyFees() {
    return this.penaltyFeeYearWiseRepository.findAllPenaltyFees();
  }

  async getPenaltyFeeById(id: string) {
    return this.penaltyFeeYearWiseRepository.findPenaltyFeeById(id);
  }

  async updatePenaltyFee(
    id: string,
    input: {
      property?: string;
      financial_year?: string;
      actual_value?: number;
      penalty_value?: number;
      tax_percentage?: number;
    },
    calculatePenaltyValue: boolean = true,
  ) {
    return this.penaltyFeeYearWiseRepository.updatePenaltyFee(id, input, calculatePenaltyValue);
  }

  async removePenaltyFee(id: string) {
    return this.penaltyFeeYearWiseRepository.deletePenaltyFee(id);
  }

  async getPenaltyFeesByPropertyId(propertyId: string, financialYear?: string) {
    return this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
      propertyId,
      financialYear,
    );
  }

  /**
   * Manually trigger the monthly 2% penalty increment
   * This is the same logic used by the cron job on the first day of each month
   */
  async triggerMonthlyIncrement() {
    try {
      this.logger.log('Manually triggering monthly 2% penalty increment');

      const penaltyFees =
        await this.penaltyFeeYearWiseRepository.handlePercentageIncrement();

      return {
        message: `Successfully incremented penalties for ${penaltyFees.length} records`,
        success: true,
        data: {
          count: penaltyFees.length,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error incrementing penalties: ${error.message}`,
        error.stack,
      );
      return {
        message: `Error incrementing penalties: ${error.message}`,
        success: false,
      };
    }
  }

  /**
   * Calculate penalties on remaining amounts
   * This is the same logic used on January 1st, but can be triggered manually
   */
  async calculatePenaltiesOnRemainingAmounts() {
    try {
      this.logger.log('Starting penalty calculation on remaining amounts');

      // Get current financial year
      const currentFinancialYear =
        await this.financialYearRepository.getCurrentFinancialYear();
      if (!currentFinancialYear) {
        this.logger.error('No current financial year found');
        return {
          message: 'No current financial year found',
          success: false,
        };
      }

      // Get all active warshik kar records
      const warshikKarRecords =
        await this.warshikKarRepository.getActiveWarshikKarByPropertyId(
          currentFinancialYear?.financial_year_range,
        );
      this.logger.log(
        `Found ${warshikKarRecords.length} active warshik kar records`,
      );

      // Get all paid data
      const paidData = await this.paidDataRepository.getAllPaidData(
        currentFinancialYear?.financial_year_range,
      );

      // Group paid data by property ID
      const paidDataByProperty = new Map();
      for (const payment of paidData) {
        const propertyId = payment.property?.property_id;
        if (!propertyId) continue;

        if (!paidDataByProperty.has(propertyId)) {
          paidDataByProperty.set(propertyId, []);
        }

        paidDataByProperty.get(propertyId).push(payment);
      }

      // Process each property
      let processedCount = 0;
      let penaltyCount = 0;

      for (const warshikKar of warshikKarRecords) {
        const propertyId = warshikKar.property?.property_id;
        if (!propertyId) continue;

        // Calculate remaining amount after payment
        const propertyPayments = paidDataByProperty.get(propertyId) || [];
        const totalPaid = propertyPayments
          .filter(
            (payment: any) =>
              payment.financial_year ===
              currentFinancialYear.financial_year_range,
          )
          .reduce(
            (sum: number, payment: any) =>
              sum + Number(payment.total_amount || 0),
            0,
          );

        // Calculate current year's tax by summing up all current tax type values
        let calculatedTotalTaxCurrent = 0;

        // Sum up all tax_type_X_current values (1 through 10)
        for (let i = 1; i <= 10; i++) {
          const taxTypeCurrentKey = `tax_type_${i}_current`;
          if (taxTypeCurrentKey in warshikKar) {
            calculatedTotalTaxCurrent += Number(
              warshikKar[taxTypeCurrentKey] || 0,
            );
          }
        }

        // Also add other_tax_sum_tax_current if it exists
        if ('other_tax_sum_tax_current' in warshikKar) {
          calculatedTotalTaxCurrent += Number(
            warshikKar.other_tax_sum_tax_current || 0,
          );
        }

        // Calculate remaining amount using the calculated current year's tax
        const remainingAmount = Math.max(
          0,
          calculatedTotalTaxCurrent - totalPaid,
        );

        if (remainingAmount > 0) {
          // Set the actual_value to the remaining amount
          const actualValue = remainingAmount;
          const taxPercentage = 2; // 2% for January 1st (consistent with cron job)

          // Check if there's an existing penalty for this property
          const existingPenalties =
            await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
              propertyId,
              currentFinancialYear.financial_year_range,
            );

          if (existingPenalties && existingPenalties.length > 0) {
            // Update existing penalty
            const existingPenalty = existingPenalties[0];
            await this.penaltyFeeYearWiseRepository.updatePenaltyFee(
              existingPenalty.penalty_fee_id,
              {
                actual_value: actualValue,
                tax_percentage: taxPercentage, // Update to 2%
                // penalty_value will be calculated in the repository
              },
            );
          } else {
            // Create new penalty
            await this.penaltyFeeYearWiseRepository.savePenaltyFee({
              property: propertyId,
              financial_year: currentFinancialYear.financial_year_range,
              actual_value: actualValue,
              tax_percentage: taxPercentage, // Set to 2%
              // penalty_value will be calculated in the repository
            });
          }

          penaltyCount++;
        }

        processedCount++;
      }

      this.logger.log(
        `Processed ${processedCount} properties, added/updated penalties for ${penaltyCount} properties`,
      );

      return {
        message: `Processed ${processedCount} properties, added/updated penalties for ${penaltyCount} properties`,
        success: true,
        data: {
          processedCount,
          penaltyCount,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error calculating penalties: ${error.message}`,
        error.stack,
      );
      return {
        message: `Error calculating penalties: ${error.message}`,
        success: false,
      };
    }
  }

  /**
   * Test API for January 1st penalty calculation
   * This method simulates what would happen on January 1st with detailed logging:
   * 1. Takes the current year's remaining total tax
   * 2. Calculates 2% of it
   * 3. Adds that to the penalty table organized by financial year
   */
  async testJanuaryFirstPenalty() {
    try {
      this.logger.log('Starting test for January 1st penalty calculation');

      // Get current financial year
      const currentFinancialYear =
        await this.financialYearRepository.getCurrentFinancialYear();
      if (!currentFinancialYear) {
        this.logger.error('No current financial year found');
        return {
          message: 'No current financial year found',
          success: false,
        };
      }
      this.logger.log(
        `Current financial year: ${currentFinancialYear.financial_year_range}`,
      );

      // Get all active warshik kar records
      const warshikKarRecords =
        await this.warshikKarRepository.getActiveWarshikKarByPropertyId(
          currentFinancialYear?.financial_year_range,
        );
      this.logger.log(
        `Found ${warshikKarRecords.length} active warshik kar records`,
      );

      // Get all paid data
      const paidData = await this.paidDataRepository.getAllPaidData(
        currentFinancialYear?.financial_year_range,
      );
      this.logger.log(`Found ${paidData.length} payment records`);

      // Group paid data by property ID
      const paidDataByProperty = new Map();
      for (const payment of paidData) {
        const propertyId = payment.property?.property_id;
        if (!propertyId) continue;

        if (!paidDataByProperty.has(propertyId)) {
          paidDataByProperty.set(propertyId, []);
        }

        paidDataByProperty.get(propertyId).push(payment);
      }
      this.logger.log(
        `Grouped payments for ${paidDataByProperty.size} properties`,
      );

      // Process each property
      let processedCount = 0;
      let penaltyCount = 0;
      const detailedResults = [];

      for (const warshikKar of warshikKarRecords) {
        const propertyId = warshikKar.property?.property_id;
        if (!propertyId) {
          this.logger.warn('Skipping warshik kar record with no property ID');
          continue;
        }

        const propertyNumber = warshikKar.property?.propertyNumber || 'Unknown';

        // Calculate remaining amount after payment
        const propertyPayments = paidDataByProperty.get(propertyId) || [];
        const relevantPayments = propertyPayments.filter(
          (payment: any) =>
            payment.financial_year ===
            currentFinancialYear.financial_year_range,
        );

        const totalPaid = relevantPayments.reduce(
          (sum: number, payment: any) =>
            sum + Number(payment.total_amount || 0),
          0,
        );

        // Calculate current year's tax by summing up all current tax type values
        let calculatedTotalTaxCurrent = 0;

        // Sum up all tax_type_X_current values (1 through 10)
        for (let i = 1; i <= 10; i++) {
          const taxTypeCurrentKey = `tax_type_${i}_current`;
          if (taxTypeCurrentKey in warshikKar) {
            calculatedTotalTaxCurrent += Number(
              warshikKar[taxTypeCurrentKey] || 0,
            );
          }
        }

        // Also add other_tax_sum_tax_current if it exists
        if ('other_tax_sum_tax_current' in warshikKar) {
          calculatedTotalTaxCurrent += Number(
            warshikKar.other_tax_sum_tax_current || 0,
          );
        }

        // For comparison, get the stored values
        const storedTotalTaxCurrent = Number(warshikKar.total_tax_current || 0);
        const totalTaxPrevious = Number(warshikKar.total_tax_previous || 0);
        const totalTaxFull = Number(warshikKar.total_tax || 0);

        // Log the tax values for debugging
        this.logger.log(`Property ${propertyNumber} tax values:
          Calculated total_tax_current: ${calculatedTotalTaxCurrent}
          Stored total_tax_current: ${storedTotalTaxCurrent}
          total_tax_previous: ${totalTaxPrevious}
          total_tax (full): ${totalTaxFull}`);

        // Use the calculated current year's tax for remaining amount
        const remainingAmount = Math.max(
          0,
          storedTotalTaxCurrent - totalPaid,
        );

        this.logger.log(`Property ${propertyNumber} calculation:
          calculated_total_tax_current: ${calculatedTotalTaxCurrent}
          stored_total_tax_current: ${storedTotalTaxCurrent}
          totalPaid: ${totalPaid}
          remainingAmount: ${remainingAmount}`);

        const propertyResult = {
          propertyId,
          propertyNumber,
          calculatedTotalTaxCurrent,
          storedTotalTaxCurrent,
          totalTaxPrevious,
          totalTaxFull,
          totalPaid,
          remainingAmount,
          penaltyAmount: 0,
          action: 'none',
        };

        if (remainingAmount > 0) {
          // Set the actual_value to the remaining amount
          const actualValue = remainingAmount;
          const taxPercentage = 2; // 2% for January 1st
          const penaltyAmount = actualValue * (taxPercentage / 100);

          propertyResult.penaltyAmount = penaltyAmount;

          // Check if there's an existing penalty for this property
          const existingPenalties =
            await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
              propertyId,
              currentFinancialYear.financial_year_range,
            );

          if (existingPenalties && existingPenalties.length > 0) {
            // Update existing penalty
            const existingPenalty = existingPenalties[0];
            await this.penaltyFeeYearWiseRepository.updatePenaltyFee(
              existingPenalty.penalty_fee_id,
              {
                actual_value: actualValue,
                tax_percentage: taxPercentage,
                // penalty_value will be calculated in the repository
              },
            );
            propertyResult.action = 'updated';
            this.logger.log(
              `Updated penalty for property ${propertyNumber}: ${penaltyAmount} (${taxPercentage}% of ${actualValue})`,
            );
          } else {
            // Create new penalty
            await this.penaltyFeeYearWiseRepository.savePenaltyFee({
              property: propertyId,
              financial_year: currentFinancialYear.financial_year_range,
              actual_value: actualValue,
              tax_percentage: taxPercentage,
              // penalty_value will be calculated in the repository
            });
            propertyResult.action = 'created';
            this.logger.log(
              `Created new penalty for property ${propertyNumber}: ${penaltyAmount} (${taxPercentage}% of ${actualValue})`,
            );
          }

          penaltyCount++;
        } else {
          this.logger.log(
            `No remaining amount for property ${propertyNumber}, skipping penalty calculation`,
          );
        }

        detailedResults.push(propertyResult);
        processedCount++;
      }

      this.logger.log(
        `Test completed: Processed ${processedCount} properties, added/updated penalties for ${penaltyCount} properties`,
      );

      // Calculate summary statistics
      let totalCalculatedCurrentTax = 0;
      let totalStoredCurrentTax = 0;
      let totalPreviousTax = 0;
      let totalFullTax = 0;
      let totalPaid = 0;
      let totalRemainingAmount = 0;
      let totalPenaltyAmount = 0;

      detailedResults.forEach((result) => {
        totalCalculatedCurrentTax += result.calculatedTotalTaxCurrent || 0;
        totalStoredCurrentTax += result.storedTotalTaxCurrent || 0;
        totalPreviousTax += result.totalTaxPrevious || 0;
        totalFullTax += result.totalTaxFull || 0;
        totalPaid += result.totalPaid || 0;
        totalRemainingAmount += result.remainingAmount || 0;
        totalPenaltyAmount += result.penaltyAmount || 0;
      });

      return {
        message: `Test completed: Processed ${processedCount} properties, added/updated penalties for ${penaltyCount} properties`,
        success: true,
        data: {
          processedCount,
          penaltyCount,
          currentFinancialYear: currentFinancialYear.financial_year_range,
          summary: {
            totalCalculatedCurrentTax,
            totalStoredCurrentTax,
            totalPreviousTax,
            totalFullTax,
            totalPaid,
            totalRemainingAmount,
            totalPenaltyAmount,
            penaltyPercentage: 2,
          },
        },
      };
    } catch (error) {
      this.logger.error(
        `Error in test January 1st penalty calculation: ${error.message}`,
        error.stack,
      );
      return {
        message: `Error in test: ${error.message}`,
        success: false,
        error: error.stack,
      };
    }
  }

  /**
   * Test January 1st penalty calculation for specific property 0722fe33-1362-4a55-829f-68a6f636bc76
   * This function tests the penalty calculation with detailed console logs
   */
  async testJanuaryFirstPenaltyForSpecificProperty() {
    const specificPropertyId = '0722fe33-1362-4a55-829f-68a6f636bc76';

    try {
      console.log('='.repeat(100));
      console.log('🧪 TESTING JANUARY FIRST PENALTY FOR SPECIFIC PROPERTY');
      console.log(`🏠 Property ID: ${specificPropertyId}`);
      console.log('='.repeat(100));

      this.logger.log(`Starting test for January 1st penalty calculation for property: ${specificPropertyId}`);

      // Get current financial year
      const currentFinancialYear =
        await this.financialYearRepository.getCurrentFinancialYear();
      if (!currentFinancialYear) {
        console.log('❌ ERROR: No current financial year found');
        this.logger.error('No current financial year found');
        return {
          message: 'No current financial year found',
          success: false,
        };
      }

      console.log(`📅 Current Financial Year: ${currentFinancialYear.financial_year_range}`);
      this.logger.log(
        `Current financial year: ${currentFinancialYear.financial_year_range}`,
      );

      // Get all warshik kar records and filter for specific property
      const allWarshikKarRecords =
        await this.warshikKarRepository.getActiveWarshikKarByPropertyId(
          currentFinancialYear?.financial_year_range,
        );

      // Filter for specific property
      const warshikKarRecords = allWarshikKarRecords.filter(
        record => record.property?.property_id === specificPropertyId
      );

      console.log(`📊 Total Warshik Kar Records in System: ${allWarshikKarRecords.length}`);
      console.log(`📊 Warshik Kar Records Found for Property: ${warshikKarRecords.length}`);
      this.logger.log(
        `Found ${warshikKarRecords.length} warshik kar records for property ${specificPropertyId}`,
      );

      if (warshikKarRecords.length === 0) {
        console.log(`❌ ERROR: No Warshik Kar record found for property ${specificPropertyId}`);
        return {
          message: `No Warshik Kar record found for property ${specificPropertyId}`,
          success: false,
        };
      }

      const warshikKar = warshikKarRecords[0]; // Take the first record
      const propertyNumber = warshikKar.property?.propertyNumber || 'Unknown';
      const oldPropertyNumber = warshikKar.property?.old_propertyNumber || 'N/A';

      console.log(`✅ Found Warshik Kar record for property`);
      console.log(`🏠 Property Number: ${propertyNumber}`);
      console.log(`🏠 Old Property Number: ${oldPropertyNumber}`);

      // Get paid data for specific property
      const paidData = await this.paidDataRepository.getAllPaidData(
        currentFinancialYear?.financial_year_range,
      );

      // Filter paid data for test property only
      const testPropertyPaidData = paidData.filter(
        payment => payment.property?.property_id === specificPropertyId
      );

      console.log(`💰 Total Payment Records in System: ${paidData.length}`);
      console.log(`💰 Payment Records for Test Property: ${testPropertyPaidData.length}`);
      this.logger.log(`Found ${testPropertyPaidData.length} payment records for property ${specificPropertyId}`);

      // Log payment details for the test property
      if (testPropertyPaidData.length > 0) {
        console.log('\n📋 PAYMENT DETAILS FOR TEST PROPERTY:');
        testPropertyPaidData.forEach((payment, index) => {
          console.log(`  Payment ${index + 1}:`);
          console.log(`    - Amount: ${payment.total_amount || 0}`);
          console.log(`    - Financial Year: ${payment.financial_year}`);
          console.log(`    - Date: ${payment.createdAt || 'N/A'}`);
        });
      } else {
        console.log('💰 No payments found for this property');
      }

      // Process the test property
      console.log('\n' + '='.repeat(60));
      console.log('🔍 PROCESSING TEST PROPERTY CALCULATION');
      console.log('='.repeat(60));

      // Calculate remaining amount after payment
      const relevantPayments = testPropertyPaidData.filter(
        (payment: any) =>
          payment.financial_year === currentFinancialYear.financial_year_range,
      );

      const totalPaid = relevantPayments.reduce(
        (sum: number, payment: any) =>
          sum + Number(payment.total_amount || 0),
        0,
      );

      console.log(`💰 Relevant Payments for Current Year: ${relevantPayments.length}`);
      console.log(`💰 Total Paid Amount: ${totalPaid}`);

      // Calculate current year's tax by summing up all current tax type values
      let calculatedTotalTaxCurrent = 0;

      console.log('\n📊 TAX CALCULATION BREAKDOWN:');

      // Sum up all tax_type_X_current values (1 through 10)
      for (let i = 1; i <= 10; i++) {
        const taxTypeCurrentKey = `tax_type_${i}_current`;
        if (taxTypeCurrentKey in warshikKar) {
          const taxValue = Number(warshikKar[taxTypeCurrentKey] || 0);
          if (taxValue > 0) {
            console.log(`  Tax Type ${i} Current: ${taxValue}`);
            calculatedTotalTaxCurrent += taxValue;
          }
        }
      }

      // Also add other_tax_sum_tax_current if it exists
      if ('other_tax_sum_tax_current' in warshikKar) {
        const otherTaxValue = Number(warshikKar.other_tax_sum_tax_current || 0);
        if (otherTaxValue > 0) {
          console.log(`  Other Tax Sum Current: ${otherTaxValue}`);
          calculatedTotalTaxCurrent += otherTaxValue;
        }
      }

      console.log(`📊 Total Calculated Current Tax: ${calculatedTotalTaxCurrent}`);

      // For comparison, get the stored values
      const storedTotalTaxCurrent = Number(warshikKar.total_tax_current || 0);
      const totalTaxPrevious = Number(warshikKar.total_tax_previous || 0);
      const totalTaxFull = Number(warshikKar.total_tax || 0);

      console.log('\n📋 TAX COMPARISON:');
      console.log(`  Calculated Total Tax Current: ${calculatedTotalTaxCurrent}`);
      console.log(`  Stored Total Tax Current: ${storedTotalTaxCurrent}`);
      console.log(`  Total Tax Previous: ${totalTaxPrevious}`);
      console.log(`  Total Tax (Full): ${totalTaxFull}`);

      // Use the calculated current year's tax for remaining amount
      const remainingAmount = Math.max(
        0,
        storedTotalTaxCurrent - totalPaid,
      );

      console.log('\n💰 REMAINING AMOUNT CALCULATION:');
      console.log(`  Calculated Current Tax: ${calculatedTotalTaxCurrent}`);
      console.log(`  Total Paid: ${totalPaid}`);
      console.log(`  Remaining Amount: ${remainingAmount}`);

      const propertyResult = {
        propertyId: specificPropertyId,
        propertyNumber,
        oldPropertyNumber,
        calculatedTotalTaxCurrent,
        storedTotalTaxCurrent,
        totalTaxPrevious,
        totalTaxFull,
        totalPaid,
        remainingAmount,
        penaltyAmount: 0,
        action: 'none',
      };

      if (remainingAmount > 0) {
        console.log('\n🚨 PENALTY CALCULATION:');

        // Set the actual_value to the remaining amount
        const actualValue = remainingAmount;
        const taxPercentage = 2; // 2% for January 1st
        const penaltyAmount = actualValue * (taxPercentage / 100);

        console.log(`  Actual Value (Remaining Amount): ${actualValue}`);
        console.log(`  Tax Percentage: ${taxPercentage}%`);
        console.log(`  Calculated Penalty Amount: ${penaltyAmount}`);

        propertyResult.penaltyAmount = penaltyAmount;

        // Check if there's an existing penalty for this property
        const existingPenalties =
          await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
            specificPropertyId,
            currentFinancialYear.financial_year_range,
          );

        console.log(`  Existing Penalties Found: ${existingPenalties?.length || 0}`);

        if (existingPenalties && existingPenalties.length > 0) {
          // Update existing penalty
          const existingPenalty = existingPenalties[0];
          console.log(`  Updating existing penalty ID: ${existingPenalty.penalty_fee_id}`);

          await this.penaltyFeeYearWiseRepository.updatePenaltyFee(
            existingPenalty.penalty_fee_id,
            {
              actual_value: actualValue,
              tax_percentage: taxPercentage,
              // penalty_value will be calculated in the repository
            },
          );
          propertyResult.action = 'updated';
          console.log(`  ✅ Updated penalty for property ${propertyNumber}: ${penaltyAmount} (${taxPercentage}% of ${actualValue})`);
        } else {
          // Create new penalty
          console.log(`  Creating new penalty record`);

          await this.penaltyFeeYearWiseRepository.savePenaltyFee({
            property: specificPropertyId,
            financial_year: currentFinancialYear.financial_year_range,
            actual_value: actualValue,
            tax_percentage: taxPercentage,
            // penalty_value will be calculated in the repository
          });
          propertyResult.action = 'created';
          console.log(`  ✅ Created new penalty for property ${propertyNumber}: ${penaltyAmount} (${taxPercentage}% of ${actualValue})`);
        }
      } else {
        console.log('\n⚠️  NO PENALTY REQUIRED:');
        console.log(`  Remaining amount is ${remainingAmount}, no penalty calculation needed`);
      }

      console.log('\n' + '='.repeat(80));
      console.log('📋 FINAL SUMMARY');
      console.log('='.repeat(80));
      console.log(`Property ID: ${specificPropertyId}`);
      console.log(`Property Number: ${propertyNumber}`);
      console.log(`Old Property Number: ${oldPropertyNumber}`);
      console.log(`Financial Year: ${currentFinancialYear.financial_year_range}`);
      console.log(`Calculated Total Tax Current: ${propertyResult.calculatedTotalTaxCurrent}`);
      console.log(`Stored Total Tax Current: ${propertyResult.storedTotalTaxCurrent}`);
      console.log(`Total Tax Previous: ${propertyResult.totalTaxPrevious}`);
      console.log(`Total Tax Full: ${propertyResult.totalTaxFull}`);
      console.log(`Total Paid: ${propertyResult.totalPaid}`);
      console.log(`Remaining Amount: ${propertyResult.remainingAmount}`);
      console.log(`Penalty Amount: ${propertyResult.penaltyAmount}`);
      console.log(`Action Taken: ${propertyResult.action}`);
      console.log('='.repeat(80));

      this.logger.log(
        `Test completed for property ${propertyNumber}: Action = ${propertyResult.action}, Penalty = ${propertyResult.penaltyAmount}`,
      );

      return {
        message: `Test completed for property ${propertyNumber}`,
        success: true,
        data: {
          propertyId: specificPropertyId,
          propertyNumber,
          oldPropertyNumber,
          currentFinancialYear: currentFinancialYear.financial_year_range,
          calculatedTotalTaxCurrent: propertyResult.calculatedTotalTaxCurrent,
          storedTotalTaxCurrent: propertyResult.storedTotalTaxCurrent,
          totalTaxPrevious: propertyResult.totalTaxPrevious,
          totalTaxFull: propertyResult.totalTaxFull,
          totalPaid: propertyResult.totalPaid,
          remainingAmount: propertyResult.remainingAmount,
          penaltyAmount: propertyResult.penaltyAmount,
          penaltyPercentage: 2,
          action: propertyResult.action,
        },
      };
    } catch (error) {
      console.log(`❌ ERROR: ${error.message}`);
      this.logger.error(
        `Error in test January 1st penalty calculation for property: ${error.message}`,
        error.stack,
      );
      return {
        message: `Error in test: ${error.message}`,
        success: false,
        error: error.stack,
      };
    }
  }
}
