import {
  BadRequestException,
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';

import {
  CreateReassessmentRangeDto,
  ReassessmentRangeIdDto,
  UpdateReassessmentRangeDto,
} from './dto/reassessment-range.dto';
import { ReassessmentRangeRepository } from 'libs/database/repositories/reassesment.repository';
import { AnnualKarAkaraniService } from 'src/annual-kar-akarani/annual-kar-akarani.service';
import { FinancialMasterService } from 'src/financial-master/financial_master.service';
import {
  Financial_yearRepository,
  Master_depreciationRepository,
  Master_rr_construction_rateRepository,
  Master_rr_rateRepository,
  Master_TaxValueRepository,
  Master_WeightingRepository,
  MasterGhanKachraRateRepository,
} from 'libs/database/repositories';

@Injectable()
export class ReassessmentRangeService {
  constructor(
    private readonly financialYearRepository: Financial_yearRepository,

    private readonly reassessmentRangeRepository: ReassessmentRangeRepository,
    private readonly annualKarAkarniService: AnnualKarAkaraniService,
    private readonly weightingRateRepo: Master_WeightingRepository,
    private readonly depreciationRepo: Master_depreciationRepository,
    private readonly rrConstrutionRepo: Master_rr_construction_rateRepository,
    private readonly rrRateRepo: Master_rr_rateRepository,
    private readonly masterTaxValueRepo: Master_TaxValueRepository,
    private readonly masterGhanKachraRepo: MasterGhanKachraRateRepository,
    private readonly reassessmentRangeRepo: ReassessmentRangeRepository,
    private readonly FinancialMasterService: FinancialMasterService,
  ) {}

  async create(createReassessmentRangeDto: CreateReassessmentRangeDto) {
    try {
   
      const currentReassementYear = await this.reassessmentRangeRepository.findOne({
        where: { is_current: true },
      });

      let newFinancialYear = null;

      newFinancialYear = await this.financialYearRepository.findOne({
        where: {
          financial_year_range: createReassessmentRangeDto?.start_range,
        },
      });

      if (!newFinancialYear) {
        await this.FinancialMasterService.createFinancialYear();
      }
      newFinancialYear =
        await this.financialYearRepository.getCurrentFinancialYear();

      const newFinancialYearRange = newFinancialYear?.financial_year_range;
      
      const currentReassessmentRange =
      await this.reassessmentRangeRepository.saveData(
        createReassessmentRangeDto,
      );

      // Get the current reassessment range

      if (!currentReassessmentRange) {
        throw new BadRequestException(
          'No active reassessment range found. Please check the database.',
        );
      }

      await Promise.all([
        this.weightingRateRepo.genrateForNewFinancialYear(
          newFinancialYearRange,
          currentReassessmentRange,
        ),
        this.depreciationRepo.generateForNewFinancialYear(
          newFinancialYearRange,
          currentReassessmentRange,
        ),
        this.rrConstrutionRepo.generateForNewFinancialYear(
          newFinancialYearRange,
          currentReassessmentRange,
        ),
        this.rrRateRepo.generateForNewFinancialYear(
          newFinancialYearRange,
          currentReassessmentRange,
        ),
        this.masterTaxValueRepo.generateForNewFinancialYear(
          newFinancialYearRange,
          currentReassessmentRange,
        ),
        this.masterGhanKachraRepo.generateForNewFinancialYear(
          newFinancialYearRange,
          currentReassessmentRange,
        ),
      ]);
      await this.reassessmentRangeRepository.update(
        currentReassementYear.reassessment_range_id,
        { is_current: false },
      );
      return {
        message: 'Record Saved SuccessFully',
      };
    } catch (error) {
      console.log('error', error);
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllData =
        await this.reassessmentRangeRepository.findAllReassessmentRanges();

      if (!getAllData) {
        throw new NotFoundException('Record Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(reassessmentRange: ReassessmentRangeIdDto) {
    try {
      const { reassessmentRange_id } = reassessmentRange;
      const checkData =
        await this.reassessmentRangeRepository.findById(reassessmentRange_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      return {
        message: 'Data found Sucess',
        data: checkData,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    reassessmentRange: ReassessmentRangeIdDto,
    updateReassessmentRangeDto: UpdateReassessmentRangeDto,
  ) {
    try {
      const { reassessmentRange_id } = reassessmentRange;
      const checkData =
        await this.reassessmentRangeRepository.findById(reassessmentRange_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      const updateData =
        await this.reassessmentRangeRepository.updateReassessmentRange(
          reassessmentRange_id,
          updateReassessmentRangeDto,
        );

      if (updateData.affected === 0) {
        throw new NotAcceptableException('Failed to update Data');
      }

      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(reassessmentRange: ReassessmentRangeIdDto) {
    try {
      const { reassessmentRange_id } = reassessmentRange;
      const checkData =
        await this.reassessmentRangeRepository.findById(reassessmentRange_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      const deleteData =
        await this.reassessmentRangeRepository.deleteReassessmentRange(
          reassessmentRange_id,
        );

      if (deleteData.affected === 0) {
        throw new NotAcceptableException('Failed to delete data');
      }

      return {
        message: 'data Delete Successfully',
        data: deleteData,
      };
    } catch (error) {
      throw error;
    }
  }
}
