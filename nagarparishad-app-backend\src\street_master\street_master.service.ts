import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';

import { StreetMasterRepository } from 'libs/database/repositories';
import {
  CreateStreetMasterDto,
  StreetMasterDto,
  UpdateStreetMasterDto,
} from './dto/Street-Master.dto';

@Injectable()
export class StreetMasterService {
  constructor(
    private readonly streetMasterRepository: StreetMasterRepository,
  ) {}
  async create(createStreetMasterDto: CreateStreetMasterDto) {
    try {
      // const saveStreet = await this.streetMasterRepository.saveStreet(
      //   createStreetMasterDto,
      // );

      const saveStreet ={};
      return {
        message: 'Street Saved SuccessFully',
        data: saveStreet,
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllStreet = await this.streetMasterRepository.findAllStreets();

      if (!getAllStreet) {
        throw new NotFoundException('Street Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllStreet,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(streetMasterDto: StreetMasterDto) {
    try {
      const { streetOrRoadId } = streetMasterDto;
      const checkStreet =
        await this.streetMasterRepository.findById(streetOrRoadId);

      if (!checkStreet) {
        throw new NotFoundException('Street Not Found');
      }

      return {
        message: 'Street found Sucess',
        data: checkStreet,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    streetMasterDto: StreetMasterDto,
    updateStreetMasterDto: UpdateStreetMasterDto,
  ) {
    try {
      const { streetOrRoadId } = streetMasterDto;
      const checkStreet =
        await this.streetMasterRepository.findById(streetOrRoadId);

      if (!checkStreet) {
        throw new NotFoundException('Street Not Found');
      }

      // const updateStreet = await this.streetMasterRepository.updateStreet(
      //   streetOrRoadId,
      //   updateStreetMasterDto,
      // );
      const updateStreet ={};

      // if (updateStreet.affected === 0) {
      //   throw new NotAcceptableException('Failed To Update');
      // }

      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(streetMasterDto: StreetMasterDto) {
    try {
      const { streetOrRoadId } = streetMasterDto;
      const checkStreet =
        await this.streetMasterRepository.findById(streetOrRoadId);

      if (!checkStreet) {
        throw new NotFoundException('Street Not Found');
      }

      const deleteStreet =
        await this.streetMasterRepository.deleteStreet(streetOrRoadId);

      if (deleteStreet.affected === 0) {
        throw new NotAcceptableException('Failed To deleted');
      }

      return {
        message: 'Deleted Successfully',
      };
    } catch (error) {
      throw error;
    }
  }
}
