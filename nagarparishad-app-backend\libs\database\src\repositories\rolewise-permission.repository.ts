import { Repository } from 'typeorm';
import { FormMasterEntity, RolewiseFormPermissionEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class RolewiseFormPermissionRepository extends Repository<RolewiseFormPermissionEntity> {
  constructor(
    @InjectRepository(RolewiseFormPermissionEntity)
    private readonly rolewiseFormPermissionRepository: Repository<RolewiseFormPermissionEntity>,
  ) {
    super(
      rolewiseFormPermissionRepository.target,
      rolewiseFormPermissionRepository.manager,
      rolewiseFormPermissionRepository.queryRunner,
    );
  }

  async saveData(input: any) {
    let data = this.rolewiseFormPermissionRepository.create(input);
    data = await this.rolewiseFormPermissionRepository.save(data);
    return data;
  }

  async findAllData(id: number) {
    return await this.rolewiseFormPermissionRepository
      .createQueryBuilder('rolewise_form_permission')
      .select([
        'rolewise_form_permission.action_id',
        'rolewise_form_permission.can_read',
        'rolewise_form_permission.can_write',
        'rolewise_form_permission.can_update',
        'rolewise_form_permission.can_delete',
        'rolewise_form_permission.is_valid',
        'form_master.form_id',
        'form_master.formName',
      ])
      .leftJoin('rolewise_form_permission.role', 'role_master')
      .leftJoin('rolewise_form_permission.form', 'form_master')
      .where('role_master.role_id = :role_id', {
        role_id: id,
      })
      .orderBy('rolewise_form_permission.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: number) {
    return await this.rolewiseFormPermissionRepository
      .createQueryBuilder('rolewise_form_permission')
      .where('rolewise_form_permission.action_id = :action_id', {
        action_id: id,
      })
      .getOne();
  }

  async getPermissions(id: number) {
    return await this.rolewiseFormPermissionRepository
      .createQueryBuilder('rolewise_form_permission')
      .select([
        'rolewise_form_permission.action_id',
        'rolewise_form_permission.can_read',
        'rolewise_form_permission.can_write',
        'rolewise_form_permission.can_update',
        'rolewise_form_permission.can_delete',
        'rolewise_form_permission.is_valid',
        'role_master.role_id',
        'role_master.roleName',
      ])
      .leftJoin('rolewise_form_permission.role', 'role_master')
      .where('rolewise_form_permission.form = :form', { form: id })
      .getMany();
  }

  async updateData(
    id: number,
    input: {
      can_read?: boolean;
      can_write?: boolean;
      can_update?: boolean;
      can_delete?: boolean;
      is_valid?: boolean;
    },
  ) {
    return await this.rolewiseFormPermissionRepository
      .createQueryBuilder('rolewise_form_permission')
      .update(RolewiseFormPermissionEntity)
      .set(input)
      .where('action_id = :action_id', {
        action_id: id,
      })
      .execute();
  }
}
