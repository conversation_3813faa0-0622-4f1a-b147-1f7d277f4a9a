import { Repository } from 'typeorm';
import { LocationEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class LocationRepository extends Repository<LocationEntity> {
  constructor(
    @InjectRepository(LocationEntity)
    private readonly locationRepository: Repository<LocationEntity>,
  ) {
    super(
      locationRepository.target,
      locationRepository.manager,
      locationRepository.queryRunner,
    );
  }

  async saveLocation(input: { locationName: string }): Promise<LocationEntity> {
    let location = this.locationRepository.create(input);
    location = await this.locationRepository.save(location);
    return location;
  }

  async findAllLocation() {
    return await this.locationRepository
      .createQueryBuilder('location_master')
      .orderBy('location_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(location_id: string) {
    return await this.locationRepository
      .createQueryBuilder('location_master')
      .where('location_master.location_id = :location_id', { location_id })
      .getOne();
  }

  async updateLocation(location_id: string, input: { locationName?: string }) {
    return await this.locationRepository
      .createQueryBuilder('location_master')
      .update(LocationEntity)
      .set(input)
      .where('location_id = :location_id', { location_id })
      .execute();
  }

  async deleteLocation(location_id: string) {
    return await this.locationRepository
      .createQueryBuilder('location_master')
      .softDelete()
      .where('location_id = :location_id', { location_id })
      .execute();
  }
}
