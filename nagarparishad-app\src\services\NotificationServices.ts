import { Notification } from '@/types/notification';
import axios from "axios";

const baseURL = import.meta.env.VITE_APP_BASE_URL;

// Create axios instance with interceptors
const apiClient = axios.create({
  baseURL: baseURL,
});

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = NotificationApi.getStoredToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle 401 and refresh token
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        }).catch((err) => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = NotificationApi.getStoredRefreshToken();
        if (!refreshToken) {
              

          NotificationApi.handleLogout();
          return Promise.reject(error);
        }

        const newTokens = await NotificationApi.refreshAccessToken(refreshToken);
        if (newTokens.status) {
          const newAccessToken = newTokens.data.data.accessToken;
          const newRefreshToken = newTokens.data.data.refreshToken;

          localStorage.setItem('AccessToken', JSON.stringify(newAccessToken));
          localStorage.setItem('RefreshToken', JSON.stringify(newRefreshToken));

          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

          processQueue(null, newAccessToken);

          return apiClient(originalRequest);
        } else {
          processQueue(error, null);
         

          NotificationApi.handleLogout();
          return Promise.reject(error);
        }
      } catch (refreshError) {
        processQueue(refreshError, null);

        NotificationApi.handleLogout();
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

export class NotificationApi {
  // Get stored token from localStorage
  static getStoredToken = () => {
    const UserToken = JSON.parse(localStorage.getItem("AccessToken") || "{}");
    return UserToken !== undefined ? UserToken : false;
  };

  static getStoredRefreshToken = () => {
    const RefreshToken = JSON.parse(localStorage.getItem("RefreshToken") || "{}");
    return RefreshToken !== undefined ? RefreshToken : false;
  };
  static handleLogout = () => {
    localStorage.removeItem('AccessToken');
    localStorage.removeItem('RefreshToken');
    localStorage.removeItem('UserData');
    window.location.href = '/login';
  };
  static refreshAccessToken = async (refreshToken: string) => {
    const url = `${baseURL}/v1/auth/refreshtoken`;
    try {
      const response = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${refreshToken}`
        },
      });

      if (response.status === 200) {
        return { status: true, data: response.data };
      } else {
        console.log("Error refreshing token: " + JSON.stringify(response.data));
        return { status: false, data: response.data };
      }
    } catch (err) {
      console.log("Error refreshing token:", err);
      return { status: false, data: err };
    }
  };

  // Test notification endpoint
  static testNotification = async () => {
    try {
      const response = await apiClient.get('/v1/notifications/test');
      return { status: true, data: response.data };
    } catch (err: any) {
      return { status: false, data: err.response?.data || err.message };
    }
  };

  // Get notifications for the current user
  static getAllNotifications = async (includeRead: boolean = false) => {
    try {
      const response = await apiClient.get('/v1/notifications/all', {
        params: { includeRead: includeRead ? 'true' : 'false' }
      });
      return { status: true, data: response.data };
    } catch (err: any) {
      return { status: false, data: err.response?.data || err.message };
    }
  };

  // Download file for a notification
  static deleteNotification = async (notificationId: string) => {
    try {
      const response = await apiClient.delete(`/v1/notifications/${notificationId}`);
      return { status: true, data: response.data };
    } catch (err: any) {
      return { status: false, data: err.response?.data || err.message };
    }
  };

  static markNotificationAsRead = async (notificationId: string) => {
    try {
      const response = await apiClient.patch(`/v1/notifications/${notificationId}/mark-as-read`);
      return { status: true, data: response.data };
    } catch (err: any) {
      return { status: false, data: err.response?.data || err.message };
    }
  };

  static downloadNotificationFile = async (notificationId: string) => {
    try {
      const response = await apiClient.get(`/v1/notifications/download/${notificationId}`, {
        responseType: 'blob'
      });

      // Create blob URL and trigger download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);

      // Get filename from response headers
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'download.zip';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create temporary link and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up blob URL
      window.URL.revokeObjectURL(url);

      return { status: true, data: { message: 'File downloaded successfully' } };
    } catch (err: any) {
      return { status: false, data: err.response?.data || err.message };
    }
  };

  // Get notification system statistics
  static getNotificationStats = async () => {
    try {
      const response = await apiClient.get('/v1/notifications/stats');
      return { status: true, data: response.data };
    } catch (err: any) {
      return { status: false, data: err.response?.data || err.message };
    }
  };

  // Create a notification for report generation
  static createReportNotification = async (reportType: 'namuna_eight' | 'namuna_nine', totalRecords: number) => {
    try {
      const notificationData = {
        title: `${reportType === 'namuna_eight' ? 'Namuna Eight' : 'Namuna Nine'} Report Generation`,
        message: 'Your ZIP file is being generated...',
        type: 'progress' as const,
        status: 'pending' as const,
        persistent: true,
        metadata: {
          reportType,
          totalRecords,
          processedRecords: 0
        }
      };

      // This would typically be sent to a backend endpoint to create the notification
      // For now, we'll return a mock notification ID
      const notificationId = `${reportType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      return { 
        status: true, 
        data: { 
          notificationId,
          notification: notificationData
        } 
      };
    } catch (err: any) {
      return { status: false, data: err.response?.data || err.message };
    }
  };

  // Update notification progress
  static updateNotificationProgress = async (notificationId: string, progress: number, processedRecords?: number) => {
    try {
      // This would typically be handled by SSE from the backend
      // For demo purposes, we'll return success
      return { 
        status: true, 
        data: { 
          notificationId, 
          progress, 
          processedRecords 
        } 
      };
    } catch (err: any) {
      return { status: false, data: err.response?.data || err.message };
    }
  };

  // Mark notification as completed
  static completeNotification = async (notificationId: string, downloadUrl: string, fileName?: string) => {
    try {
      // This would typically be handled by SSE from the backend
      return { 
        status: true, 
        data: { 
          notificationId, 
          downloadUrl, 
          fileName 
        } 
      };
    } catch (err: any) {
      return { status: false, data: err.response?.data || err.message };
    }
  };

  // Mark notification as failed
  static failNotification = async (notificationId: string, errorMessage: string) => {
    try {
      // This would typically be handled by SSE from the backend
      return { 
        status: true, 
        data: { 
          notificationId, 
          errorMessage 
        } 
      };
    } catch (err: any) {
      return { status: false, data: err.response?.data || err.message };
    }
  };

  // Get notification connection stats (for debugging)
  static getConnectionStats = async () => {
    try {
      const response = await apiClient.get('/v1/notifications/stats');
      return { status: true, data: response.data };
    } catch (err: any) {
      return { status: false, data: err.response?.data || err.message };
    }
  };
}

export default NotificationApi;
