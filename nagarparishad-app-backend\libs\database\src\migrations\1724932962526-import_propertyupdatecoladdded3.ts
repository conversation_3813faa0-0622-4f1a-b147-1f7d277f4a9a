import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportPropertyupdatecoladdded31724932962526 implements MigrationInterface {
    name = 'ImportPropertyupdatecoladdded31724932962526'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ADD "property_number_merge" character varying`);
       // await queryRunner.query(`ALTER TABLE "forms" ADD CONSTRAINT "PK_9c769ba2d5400edeac5711cc352" PRIMARY KEY ("gid")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
       // await queryRunner.query(`ALTER TABLE "forms" DROP CONSTRAINT "PK_9c769ba2d5400edeac5711cc352"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "property_number_merge"`);
    }

}
