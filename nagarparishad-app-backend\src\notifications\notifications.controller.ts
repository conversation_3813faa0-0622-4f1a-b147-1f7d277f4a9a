import {
  <PERSON>,
  Get,
  Post,
  Req,
  Res,
  Query,
  UseGuards,
  Param,
  Body,
  HttpStatus,
  HttpException,
  NotFoundException,
  Sse,
  Delete,
  Patch
} from '@nestjs/common';
import { Response, Request } from 'express';
import * as jwt from 'jsonwebtoken';

import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { NotificationsService } from './notifications.service';
import { OfflineNotificationRepository } from '../../libs/database/src/repositories/offline-notification.repository';
import { FileStorageService } from './file-storage.service';
import { AccessTokenGuard } from '@jwt/jwt-auth/guards/access-token.guard';
import * as fs from 'fs';
import { Public } from '@jwt/jwt-auth/decorators/public.decorator';
import { Observable } from 'rxjs/internal/Observable';
import { RawResponse } from '@helper/helpers/decorators/raw-response.decorator';

@ApiTags('Notifications')
@Controller('notifications')
export class NotificationsController {
  constructor(
    private readonly notificationsService: NotificationsService,
    private readonly offlineNotificationRepository: OfflineNotificationRepository,
    private readonly fileStorageService: FileStorageService,
  ) {}


  @Public()
  @Sse('stream')
  streamNotifications(@Query('token') token: string): Observable<MessageEvent> {
    // Validate token from query parameter
    let userId: string;
    try {
      if (!token) {
        throw new Error('Token required');
      }

      // Validate the JWT token
      const decoded = jwt.verify(token, process.env.JWT_ACCESS_SECRET) as any;
      userId = decoded.sub;

      if (!userId) {
        throw new Error('Invalid token');
      }
    } catch (error) {
      // Handle token validation errors by returning an observable with an error event
      return new Observable<MessageEvent>(subscriber => {
        subscriber.next({ data: { error: error.message } } as MessageEvent);
        subscriber.complete();
      });
    }

    // Return an observable that emits events
    return new Observable<MessageEvent>(subscriber => {
      // Send initial connection message
      subscriber.next({ data: { message: 'Connected to notification stream' } } as MessageEvent);

      // Register client for notifications
      this.notificationsService.addClient(userId, subscriber);

      // Deliver any pending notifications
      this.notificationsService.deliverPendingNotifications(userId).then(() => {
        // Emit periodic messages or handle other logic
        const intervalId = setInterval(() => {
          subscriber.next({ data: { message: 'Periodic update' } } as MessageEvent);
        }, 5000);

        // Handle cleanup on unsubscribe
        return () => {
          clearInterval(intervalId);
          this.notificationsService.removeClient(userId, subscriber);
        };
      });
    });
  }


  @Get('test')
  @UseGuards(AccessTokenGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Test notification endpoint' })
  @ApiResponse({ status: 200, description: 'Test notification sent' })
  async testNotification(@Req() req: Request) {
    const userId = (req as any).user?.sub;
    
    if (!userId) {
      return { error: 'Unauthorized' };
    }

    // Send test notification
    this.notificationsService.sendNotification(userId, {
      type: 'notification_update',
      notificationId: `test_${Date.now()}`,
      data: {
        status: 'processing',
        progress: 50,
        message: 'Test notification - Processing...',
        metadata: {
          reportType: 'namuna_eight',
          totalRecords: 100,
          processedRecords: 50
        }
      }
    });

    return { message: 'Test notification sent' };
  }
 
  @Public()
  @Post('webhook')
  @ApiOperation({ summary: 'Webhook endpoint for PDF microservice' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  async handleWebhook(@Body() webhookData: {
    notificationId: string;
    fileName: string;
    fileData: string; // Base64 encoded file data
    status: 'completed' | 'failed';
    error?: string;
  }) {
    try {
      console.log("=".repeat(20),webhookData)

      // return;
      if (webhookData.status === 'failed') {
        // Handle failed notification
        const notification = await this.offlineNotificationRepository.findByNotificationId(webhookData.notificationId);
        if (notification) {
          await this.offlineNotificationRepository.updateStatus(
            webhookData.notificationId,
            'failed',
            { message: webhookData.error || 'Processing failed' }
          );
        }
        return { success: true, message: 'Failed notification processed' };
      }

      // Decode base64 file data
      const fileBuffer = Buffer.from(webhookData.fileData, 'base64');

      await this.notificationsService.handleWebhookNotification(
        webhookData.notificationId,
        fileBuffer,
        webhookData.fileName,
        webhookData
      );

      return { success: true, message: 'Webhook processed successfully' };
    } catch (error) {
      throw new HttpException(
        `Webhook processing failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
 @Get('download/:notificationId')
  @RawResponse()
  @UseGuards(AccessTokenGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Download file for notification' })
  @ApiResponse({ status: 200, description: 'File downloaded successfully' })
  async downloadFile(
    @Param('notificationId') notificationId: string,
    @Req() req: Request,
    @Res() res: Response
  ) {
    console.log('Entering downloadFile method for notificationId:', notificationId);
    try {
      const userId = (req as any).user?.sub;

      if (!userId) {
        throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
      }

      const notification = await this.offlineNotificationRepository.findByNotificationId(notificationId);
      console.log("lalalalallala  1")

      if (!notification) {
        throw new NotFoundException('Notification not found');
      }

      if (notification.userId !== userId) {
        throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
      }
      if (!notification.filePath || !await this.fileStorageService.fileExists(notification.filePath)) {
        throw new NotFoundException('File not found');
      }
      console.log("lalalalallala++++",notification.filePath)
      // Increment download count
      await this.offlineNotificationRepository.incrementDownloadCount(notificationId);

      // Set response headers
      res.setHeader('Content-Disposition', `attachment; filename="${notification.fileName}"`);
      res.setHeader('Content-Type', 'application/zip');

      // Stream the file
      const fileStream = fs.createReadStream(notification.filePath);
            console.log("lalalalallala++++ 3")

      fileStream.pipe(res);
            console.log("lalalalallala++++ 4----")

      fileStream.on('end', () => {
                    console.log("lalalalallala++++ 5")

        // Optionally delete file after first download
        // this.fileStorageService.deleteFile(notification.filePath);
      });

      fileStream.on('error', (error) => {
                            console.log("lalalalallala++++ 6")

        console.error('File streaming error:', error);
        if (!res.headersSent) {
          // If headers haven't been sent, throw an HttpException to be caught by the outer try-catch
          throw new HttpException(
            `File streaming error: ${error instanceof Error ? error.message : String(error)}`,
            HttpStatus.INTERNAL_SERVER_ERROR
          );
        } else {
          // If headers have already been sent, we cannot send a new status code or headers.
          // We should just end the response to prevent the connection from hanging.
                                      console.log("lalalalallala++++ 7")

          res.end();
        }
        // No return needed here as throw will exit, or res.end() will terminate the response.
      });


    } catch (error) {
                                            console.log("lalalalallala++++ 8")

            console.log('Unhandled error during file download - RAW ERROR:', error);
            console.log('Unhandled error during file download - TYPEOF ERROR:', typeof error);

      if (error instanceof HttpException) {
        throw error;
      }
      const errorMessage = (error instanceof Error && error.message)
        ? error.message
        : (error ? String(error) : 'An unknown error occurred');
      
      console.log('Unhandled error during file download - FINAL MESSAGE:', errorMessage);

      throw new HttpException(
        `Download failed: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }


  @Get('all')
  @UseGuards(AccessTokenGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get pending notifications for user' })
  @ApiResponse({ status: 200, description: 'Pending notifications retrieved' })
  async getAllNotifications(@Req() req: Request) {
    const userId = (req as any).user?.sub;

    if (!userId) {
      throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
    }

    const pendingNotifications = await this.notificationsService.getAllNotifications(userId);

    return {
      success: true,
      data: pendingNotifications.map(notification => ({
        id: notification.id,
        notificationId: notification.notificationId,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        status: notification.status,
        downloadUrl: notification.downloadUrl,
        fileName: notification.fileName,
        fileSize: notification.fileSize,
        createdAt: notification.createdAt,
        metadata: notification.metadata
      }))
    };
  }

  @Get('stats')
  @UseGuards(AccessTokenGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get notification system statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved' })
  async getStats() {
    const connectionStats = this.notificationsService.getConnectionStats();
    const totalStorageSize = await this.fileStorageService.getTotalStorageSize();

    return {
      success: true,
      data: {
        ...connectionStats,
        totalStorageSize: totalStorageSize,
        totalStorageSizeMB: Math.round(totalStorageSize / (1024 * 1024) * 100) / 100
      }
    };
  }

  @Post('test-webhook')
  @ApiOperation({ summary: 'Test webhook endpoint' })
  @ApiResponse({ status: 200, description: 'Test webhook processed' })
  async testWebhook(@Body() body: {userId: string}) {
    // Simulate PDF service webhook call
    const payload = {
      notificationId: `namuna_nine_1750678736300_xut07pz`,
      fileName: 'test_report.pdf',
      fileData:"uduuuddddddd....",
      status: 'completed' as const
    };

    // Call real webhook handler

    return this.handleWebhook(payload);
  }

  @Get('test-notification/:userId')
  @ApiOperation({ summary: 'Trigger test notification' })
  @ApiResponse({ status: 200, description: 'Test notification triggered' })
  async triggerTestNotification(@Param('userId') userId: string) {
    // Create minimal mock request object
    const mockRequest = {
      user: { sub: userId },
      headers: {},
      method: 'GET',
      url: '/test-notification'
    } as unknown as Request;

    // Use existing test notification method
    return this.testNotification(mockRequest);
  }

  @Post('offline')
  @UseGuards(AccessTokenGuard)
  @ApiBearerAuth('access极狐-token')
  @ApiOperation({ summary: 'Create offline notification' })
  @ApiResponse({ status: 201, description: 'Offline notification created' })
  async createOfflineNotification(
    @Req() req: Request,
    @Body() body: {
      title: string;
      message: string;
      type?: 'info' | 'warning' | 'error' | 'success';
      metadata?: any;
    }
  ) {
    const userId = (req as any).user?.sub;
    
    if (!userId) {
      throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
    }

    const notificationId = `offline_${Date.now()}`;
    
    const notification = await this.notificationsService.createOfflineNotification(
      userId,
      notificationId,
      body.title,
      body.message,
      body.type,
      body.metadata
    );

    return {
      success: true,
      notificationId: notification.notificationId,
      message: 'Offline notification created successfully'
    };
  }

  @Delete(':notificationId')
  @UseGuards(AccessTokenGuard)
  @ApiOperation({ summary: 'Delete a notification' })
  @ApiResponse({ status: 200, description: 'Notification deleted successfully' })
  async deleteNotification(
    @Param('notificationId') notificationId: string,
    @Req() req: Request
  ) {
    const userId = (req as any).user?.sub;
    if (!userId) {
      throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
    }

    // Find the notification to check ownership
    const notification = await this.offlineNotificationRepository.findByNotificationId(notificationId);

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    if (notification.userId !== userId) {
      throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
    }

    // Delete the notification
    await this.offlineNotificationRepository.delete(notification?.id);

    return { success: true, message: 'Notification deleted' };
  }

  @Patch(':notificationId/mark-as-read')
  @UseGuards(AccessTokenGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Mark a notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read successfully' })
  async markNotificationAsRead(
    @Param('notificationId') notificationId: string,
    @Req() req: Request
  ) {
    const userId = (req as any).user?.sub;
    if (!userId) {
      throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
    }

    const notification = await this.offlineNotificationRepository.findByNotificationId(notificationId);

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    if (notification.userId !== userId) {
      throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
    }

    await this.notificationsService.markNotificationAsRead(notificationId);

    return { success: true, message: 'Notification marked as read' };
  }
}