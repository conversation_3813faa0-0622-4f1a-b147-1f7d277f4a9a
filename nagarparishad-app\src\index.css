/* @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Montserrat:wght@400;500;600;700&family=Noto+Sans+Devanagari:wght@400;500;600;700&display=swap'); */

@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;700&display=swap');


@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html,
  body {
    transition: font-size 0.3s ease; /* Smooth transition for font size changes */
    zoom: 1;
  }
  :root {
    --background: #ffffff;
    --foreground: #ffffff;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: #0c3159;
    --primary-foreground: #ffffff;
    --secondary: #ffffff;
    --secondary-foreground: #000000;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --border-colour: #092542;

    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
  }
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 48%;
}
.devanagari-text {
  font-family: 'Noto Sans Devanagari', sans-serif;
}

.button {
  border-radius: 8px;
  box-shadow: 0px 4px 4px 0px #13457b;
  border-color: #092542;
  background-color: var(--primary);
  cursor: pointer;
  border-width: 1px;
}

/* Hide the spinner arrows for number input */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield; /* Firefox */
}

/* //File Upload */
#form-file-upload {
  height: 16rem;
  width: 28rem;
  max-width: 100%;
  text-align: center;
  position: relative;
}

#input-file-upload {
  display: none;
}

#label-file-upload {
  height: 100%;
  min-height: 245px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-width: 2px;
  border-radius: 1rem;
  border-style: dashed;
  border-color: #cbd5e1;
  background-color: #f8fafc;
}

#label-file-upload.drag-active {
  background-color: #ffffff;
}

.upload-button {
  cursor: pointer;
  padding: 0.25rem;
  font-size: 1rem;
  border: none;
  font-family: "Oswald", sans-serif;
  background-color: transparent;
}

.upload-button:hover {
  text-decoration-line: underline;
}

#drag-file-element {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1rem;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}

@layer utilities {
  /* Custom scrollbar styles for WebKit browsers */
  *::-webkit-scrollbar {
    width: 5px; /* width of the entire scrollbar */
    height: 5px; /* height of the entire scrollbar (for horizontal scrollbar) */
  }

  *::-webkit-scrollbar-track {
    background: #f1f1f1; /* color of the scrollbar track */
  }

  *::-webkit-scrollbar-thumb {
    background: #888; /* color of the scrollbar thumb */
    border-radius: 10px; /* optional: adds a rounded edge to the scrollbar thumb */
  }

  *::-webkit-scrollbar-thumb:hover {
    background: #555; /* color of the scrollbar thumb when hovered */
  }

  /* Custom scrollbar styles for Firefox */
  * {
    scrollbar-width: thin; /* sets the width to thin */

    scrollbar-color: #89898a #f1f1f1; /* thumb color, track color */
  }
  .form-flex {
    @apply flex flex-col sm:flex-row sm:gap-5 gap-2 mb-2;
  }

  .form-element {
    @apply w-full sm:w-1/2 text-left;
  }

  body {
    @apply font-Poppins;
  }

  .tbody tr:nth-child(even) {
    @apply bg-LightBlue;
  }

  .dashboard-calender Button {
    min-width: fit-content;
  }

  .card-shadow {
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  }

  .income-tax-eight-table td,
  .dashboard-table td {
    @apply min-w-[100px];
  }

  .bar-chart {
    @apply sm:w-[600px] !w-full;
  }
 
  /* .recharts-legend-wrapper {
    @apply !top-auto !bottom-0 !max-w-32
  }*/
}

.form-masonary {
  display: grid;
  gap: 10px;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  grid-template-rows: masonry;
}

.pie-chart .recharts-default-legend {
  max-width: 175px;
  font-size: 14px;
}

.pie-chart .recharts-default-legend li {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box !important;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-bottom: 5px;
}

.selected-file-name {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box !important;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  margin-bottom: 5px;
}

.one-line-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box !important;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  margin-bottom: 3px;
}

.property-location-dialog .dialog-close{
    display: none;
}

.collapse-content {
  height: 0;
  overflow: hidden;
  transition: height 0.3s ease;
}

.collapse-content.open {
  height: auto; /* Adjust this value as needed */
}


/* .recharts-wrapper{
  display: flex;
}

.recharts-legend-wrapper{
  border: 1px solid black;
  position: static !important;
} */

.pie-chart .recharts-default-legend{
  max-height: 250px;
  /* border: 1px solid; */
  overflow-y: auto;
}

/* loader style */

/* From Uiverse.io by barisdogansutcu */ 
.loader-svg {
  width: 3.25em;
  transform-origin: center;
  animation: rotate4 2s linear infinite;
 }
 
 .loader-circle {
  fill: none;
  stroke: hsl(214, 97%, 59%);
  stroke-width: 3;
  stroke-dasharray: 1, 200;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: dash4 1.5s ease-in-out infinite;
 }
 
 @keyframes rotate4 {
  100% {
   transform: rotate(360deg);
  }
 }
 
 @keyframes dash4 {
  0% {
   stroke-dasharray: 1, 200;
   stroke-dashoffset: 0;
  }
 
  50% {
   stroke-dasharray: 90, 200;
   stroke-dashoffset: -35px;
  }
 
  100% {
   stroke-dashoffset: -125px;
  }
 }
 

@media screen and (max-width: 1500px) {
  .pie-chart .recharts-default-legend {
    max-width: 150px;
    font-size: 14px;
}
}

@media screen and (max-width: 1400px) {
  .recharts-default-legend {
    max-width: 120px;
  }
}

@media screen and (max-width: 500px) {
  .pie-chart .recharts-default-legend {
      max-width: 100%;
      max-height: 150px;
  }
}

@media screen and (min-width: 501px) and (max-width: 767px) {
  .pie-chart .recharts-default-legend {
    max-width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    font-size: 14px;
  }
  
  .pie-chart .recharts-legend-item {
    margin-right: 5px !important;
  }
}
