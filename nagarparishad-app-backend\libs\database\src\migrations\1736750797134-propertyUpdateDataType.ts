import { MigrationInterface, QueryRunner } from "typeorm";

export class PropertyUpdateDataType1736750797134 implements MigrationInterface {
     name = 'PropertyUpdateDataType1736750797134'
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE property_usage_details 
            ALTER COLUMN length TYPE decimal(10, 2) USING length::decimal,
            ALTER COLUMN width TYPE decimal(10, 2) USING width::decimal,
            ALTER COLUMN are_sq_ft TYPE decimal(10, 2) USING are_sq_ft::decimal,
            ALTER COLUMN are_sq_meter TYPE decimal(10, 2) USING are_sq_meter::decimal;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE property_usage_details 
            ALTER COLUMN length TYPE integer USING length::integer,
            ALTER COLUMN width TYPE integer USING width::integer,
            ALTER COLUMN are_sq_ft TYPE integer USING are_sq_ft::integer,
            ALTER COLUMN are_sq_meter TYPE integer USING are_sq_meter::integer;
        `);
    }
}
