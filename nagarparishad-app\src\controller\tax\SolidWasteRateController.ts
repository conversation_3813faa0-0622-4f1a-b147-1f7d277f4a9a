import { ZoneObjectInterface } from "@/model/zone-master";
import Api from "../../services/ApiServices";
import {
  useQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import TaxListApi from "@/services/TaxServices";

interface ZoneData {
  zoneName: string; // Only zoneName now
}
interface UpdateSolidWasteRateParams {
  id: string;
  payload: {
    financial_year?: string; // Optional for backward compatibility
    reassessment_range_id: string;
    value: number;
    status: string;
    usage_sub_type_master_id: string;
  };
}
const fetchRR_Rate = async () => {
    return new Promise((resolve, reject) => {
      TaxListApi.getSolidWasteRate((response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      });
    });
  };


const createSolidWasteRate = async (solidWasteRateData) => {
  return new Promise((resolve, reject) => {
    TaxListApi.createSolidWasteRate(solidWasteRateData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateSolidWasteRate = async ({ solidWasteRateId, solidWasteRateData }: { solidWasteRateId: string; solidWasteRateData: any }) => {
  return new Promise((resolve, reject) => {
    TaxListApi.updateSolidWasteRate(solidWasteRateId, solidWasteRateData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteSolidWasteRate = async (solidWasteRateId: string) => {
  return new Promise((resolve, reject) => {
   TaxListApi.deleteSolidWasteRate(solidWasteRateId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useSolidWasteController = () => {
  const queryClient = useQueryClient();

  const { data: solidWasteRateData, isLoading: propertyLoading } = useQuery({
    queryKey: ["solidwasteRatemaster"],
    queryFn: fetchRR_Rate,
    staleTime: 10 * 60 * 2, // 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
  });

  // const createSolidWasteRateMutation = useMutation({
  //   mutationFn: (data: UpdateSolidWasteRateParams['payload']) =>
  //     TaxListApi.createSolidWasteRate(data),
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["solidwasteRatemaster"] });
  //   },
  // });
  const createSolidWasteRateMutation = useMutation({
    mutationFn: createSolidWasteRate,
    onMutate: async (newconstructionRates) => {
      await queryClient.cancelQueries({ queryKey: ["solidwasteRatemaster"] });
      const previousconstructionRates = queryClient.getQueryData(["solidwasteRatemaster"]);

      queryClient.setQueryData(["solidwasteRatemaster"], (old: any) => {
             console.log(" construtionold",old)
        const updatedData = [newconstructionRates, ...old.data];
        console.log("updatedData",updatedData)
        return updatedData;
      });

      return { previousconstructionRates };
    },
    onError: (err, newconstructionRates, context) => {
      queryClient.setQueryData(["solidwasteRatemaster"], context.previousconstructionRates);
      console.error("Error creating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["solidwasteRatemaster"] });
    },
  });

  // const updateSolidWasteRateMutation = useMutation({
  //   mutationFn: (data: UpdateSolidWasteRateParams) =>
  //     TaxListApi.updateSolidWasteRate(data),
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["solidwasteRatemaster"] });
  //   },
  // });
const updateSolidWasteRateMutation = useMutation({
    mutationFn: updateSolidWasteRate,
    onMutate: async ({ solidWasteRateId, solidWasteRateData }) => {
      await queryClient.cancelQueries({ queryKey: ["solidwasteRatemaster"] });

      const previousWards = queryClient.getQueryData(["solidwasteRatemaster"]);
      queryClient.setQueryData(["solidwasteRatemaster"], (old: any) => {
        const updatedWards = old?.data?.map((constructionRate: any) =>
          constructionRate.ghanKachra_rate_id === solidWasteRateId ? { ...constructionRate, ...solidWasteRateData } : constructionRate,
        );
        console.log("Updated query data:", updatedWards);
        return updatedWards;
      });

      return { previousWards };
    },
    onError: (err, { solidWasteRateId, solidWasteRateData }, context) => {
      queryClient.setQueryData(["solidwasteRatemaster"], context.previousWards);
      console.error("Error updating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["solidwasteRatemaster"] });
    },
  });
  // const deleteSolidWasteRateMutation = useMutation({
  //   mutationFn: (id: string) => TaxListApi.deleteSolidWasteRate(id),
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["solidwasteRatemaster"] });
  //   },
  // });
  const deleteSolidWasteRateMutation = useMutation({
    mutationFn: deleteSolidWasteRate,

    onMutate: async (solidWasteRateId) => {
      await queryClient.cancelQueries({ queryKey: ["solidwasteRatemaster"] });

      const previousSolidWasteRate = queryClient.getQueryData(["solidwasteRatemaster"]);

      queryClient.setQueryData(["solidwasteRatemaster"], (old: any) => {
        const updatedSolidWasteRate = old?.data?.filter((solidWasteRate: any) => solidWasteRate.rr_construction_rate_id !== solidWasteRateId);
        return updatedSolidWasteRate;
      });

      return { previousSolidWasteRate };
    },
    onError: (err, ward_id, context) => {
      queryClient.setQueryData(["solidwasteRatemaster"], context.previousSolidWasteRate);
      console.error("Error deleting ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["solidwasteRatemaster"] });
    },
  });
  return {
    solidWasteRateList: solidWasteRateData || [],
    propertyLoading,
    createSolidWasteRate: createSolidWasteRateMutation.mutate,
    updateSolidWasteRate: updateSolidWasteRateMutation.mutate,
    deleteSolidWasteRate: deleteSolidWasteRateMutation.mutate,
  };
};


