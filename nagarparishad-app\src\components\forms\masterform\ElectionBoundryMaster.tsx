import WhiteContainer from "@/components/custom/WhiteContainer";
import React, { useState, useContext, useRef } from "react";
import { useTranslation } from "react-i18next";
import { z, object } from "zod";
import { <PERSON>ton } from "@/components/ui/button";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ArrowUpDown } from "lucide-react";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { toast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { useElectionBoundaryController } from "@/controller/master/ElectionBoundryController";
import { ColumnDef } from "@tanstack/react-table";
import { MASTER } from "@/constant/config/api.config";
import ElectionBoundryMasterPopupForm from "../ElectionBoundaryMasterPopupForm";
import AddNewBtn from "../../globalcomponent/AddNewForm";
import { MasterObjectColumnDef } from "@/model/zone-master";
import { ResponseData } from "@/model/auth/authServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

const ElectionBoundryMaster = () => {
  const { t } = useTranslation();
  const userRef = useRef(null);
  const { boundaryList, deleteElectionBoundary } =
    useElectionBoundaryController(); // Changed to useElectionBoundaryController
  const { setMasterComponent, toggle, setOpen } = useContext(GlobalContext);

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Master, FormName.ElectionBoundryMaster, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Master, FormName.ElectionBoundryMaster, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Master, FormName.ElectionBoundryMaster, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Master, FormName.ElectionBoundryMaster, Action.CanDelete);

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const dynamicValues = {
    name: t("electionboundry.boundaryLabel"),
  };
  function handleEdit(item: any): void {
    setOpen(true);
    setMasterComponent(
      <ElectionBoundryMasterPopupForm
        btnTitle={"electionboundry.updateBtn"}
        editData={item && item}
      />,
    );
    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }

  function handleDelete(item: any): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  const handleConfirmDelete = () => {
    if (selectedItem) {
      deleteElectionBoundary(selectedItem.electionBoundary_id, {
        onSuccess: (response: ResponseData) => {
          if (response?.statusCode && response?.statusCode === 200) {
            toast({
              title: t("api.formdelete", dynamicValues),
              variant: "success",
            });
          } else {
            toast({
              title: response?.message,
              variant: "destructive",
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const columns: ColumnDef<MasterObjectColumnDef>[] = [
    {
      accessorKey: `${t("SrNo")}`,
      header: `${t("SrNo")}`,
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: "electionBoundaryName",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("electionboundry.electionBoundaryLabel")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
     cell: ({ row }: { row: any })  => (
        <div className="">{row.original?.electionBoundaryName}</div>
      ),
    },
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: `${t("Actions")}`,
            enableHiding: true,
            cell: ({ row }: { row: any }) => (
              <>
                {canUpdate && (
                  <Button
                    variant="submit"
                    className="h-8 w-8 p-0 justify-center"
                    onClick={() => handleEdit(row?.original)}
                  >
                    {t("table.edit")}
                  </Button>
                )}
                {CanDelete && (
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0 justify-center ml-2  !border-BlueText !text-BlueText"
                    onClick={() => handleDelete(row?.original)}
                  >
                    {t("table.delete")}
                  </Button>
                )}
              </>
            ),
          },
        ]
      : []),
  ];

  const MasterType: string = MASTER.ELECTIONBOUNDRY;

  return (
    <div className=" w-full h-fit sm:p-6 p-3" ref={userRef && userRef}>
      <p className="w-full flex items-center justify-between ml-2  text-[18px] font-semibold mb-2">
        {t("electionboundry.formTitle")}
      </p>
      {CanCreate && <WhiteContainer>
        {MasterType && <AddNewBtn masterType={MASTER.ELECTIONBOUNDRY} />}
      </WhiteContainer>}
      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={boundaryList}
          masterType={MASTER.ELECTIONBOUNDRY}
          searchColumn="electionBoundaryName"
          searchKey="searchElectionBoundry"
        />
      </WhiteContainer>

      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={selectedItem.electionBoundaryName}
          onDelete={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default ElectionBoundryMaster;
