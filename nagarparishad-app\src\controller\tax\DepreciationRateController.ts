import { ZoneObjectInterface } from "@/model/zone-master";
import Api from "../../services/ApiServices";
import {
  useQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import TaxListApi from "@/services/TaxServices";

interface ZoneData {
  zoneName: string; // Only zoneName now
}

const fetchDepreciationRate = async () => {
    return new Promise((resolve, reject) => {
      TaxListApi.getDepreciationRateMaster((response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      });
    });
  };
  

const createDepreciationRate = async (DepreciationRateData: any) => {
  return new Promise((resolve, reject) => {
    TaxListApi.createDepreciationRate(DepreciationRateData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateDepreciationRate = async ({ id, payload }: { id : string; payload: any }) => {
  return new Promise((resolve, reject) => {
    TaxListApi.updateDepreciationRate(id, payload, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteDepreciationRate = async (DepreciationRateId: string) => {
  return new Promise((resolve, reject) => {
    TaxListApi.deleteDepreciationRate(DepreciationRateId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useDepreciationRateController = () => {
  const queryClient = useQueryClient();

  const { data: DepreciationRateData, isLoading: propertyLoading } = useQuery({
    queryKey: ["DepreciationRatemaster"],
    queryFn: fetchDepreciationRate,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
  });

  const createDepreciationRateMutation = useMutation({
    mutationFn: createDepreciationRate,
    onMutate: async (newDepreciationRates) => {
      await queryClient.cancelQueries({ queryKey: ["DepreciationRatemaster"] });
      const previousDepreciationRates = queryClient.getQueryData(["DepreciationRatemaster"]);

      queryClient.setQueryData(["DepreciationRatemaster"], (old: any) => {
             console.log(" construtionold",old)
        const updatedData = [newDepreciationRates, ...old.data];
        console.log("updatedData",updatedData)
        return updatedData;
      });

      return { previousDepreciationRates };
    },
    onError: (err, newDepreciationRates, context) => {
      queryClient.setQueryData(["DepreciationRatemaster"], context.previousDepreciationRates);
      console.error("Error creating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["DepreciationRatemaster"] });
    },
  });

 
  const updateDepreciationRateMutation = useMutation({
    mutationFn: updateDepreciationRate,
    onMutate: async ({ id, payload }) => {
      await queryClient.cancelQueries({ queryKey: ["DepreciationRatemaster"] });

      const previousWards = queryClient.getQueryData(["DepreciationRatemaster"]);
      queryClient.setQueryData(["DepreciationRatemaster"], (old: any) => {
        const updatedWards = old?.data?.map((DepreciationRate: any) =>
          DepreciationRate.depreciation_rate_id === id ? { ...DepreciationRate, ...payload } : DepreciationRate,
        );
        console.log("Updated query data:", updatedWards);
        return updatedWards;
      });

      return { previousWards };
    },
    onError: (err, { id, payload }, context) => {
      queryClient.setQueryData(["DepreciationRatemaster"], context.previousWards);
      console.error("Error updating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["DepreciationRatemaster"] });
    },
  });
  
  const deleteDepreciationRateMutation = useMutation({
    mutationFn: deleteDepreciationRate,
    onMutate: async (DepreciationRateId) => {
      await queryClient.cancelQueries({ queryKey: ["DepreciationRatemaster"] });

      const previousDepreciationRate = queryClient.getQueryData(["DepreciationRatemaster"]);

      queryClient.setQueryData(["DepreciationRatemaster"], (old: any) => {
        const updatedDepreciationRate = old?.data?.filter((DepreciationRate: any) => DepreciationRate.depreciation_rate_id !== DepreciationRateId);
        return updatedDepreciationRate;
      });

      return { previousDepreciationRate };
    },
    onError: (err, ward_id, context) => {
      queryClient.setQueryData(["DepreciationRatemaster"], context.previousDepreciationRate);
      console.error("Error deleting ward:", err);
    },
    onSuccess: () => {
      // Directly update the cache with the new data
      queryClient.setQueryData(["DepreciationRatemaster"], (old: any) => {
        const updatedDepreciationRate = old?.data?.filter((DepreciationRate: any) =>  DepreciationRate.depreciation_rate_id !== DepreciationRateId);
        return updatedDepreciationRate;
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["DepreciationRatemaster"] });
    },
  });
  return {
    DepreciationRateList: DepreciationRateData?.data || [],
    propertyLoading,
    createDepreciationRate: createDepreciationRateMutation.mutate,
    updateDepreciationRate: updateDepreciationRateMutation.mutate,
    deleteDepreciationRate: deleteDepreciationRateMutation.mutate,
  };

  
};


