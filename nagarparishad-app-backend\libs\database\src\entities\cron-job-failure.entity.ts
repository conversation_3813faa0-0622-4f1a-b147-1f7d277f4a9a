import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('cron_job_failures')
export class CronJobFailureEntity {
  @PrimaryGeneratedColumn('uuid')
  failure_id: string;

  @Column({ type: 'varchar', length: 255 })
  job_name: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  job_id: string;

  @Column({ type: 'text' })
  error_message: string;

  @Column({ type: 'int', default: 1 })
  attempt_count: number;

  @Column({ type: 'timestamp' })
  last_attempt: Date;

  @Column({ type: 'timestamp', nullable: true })
  first_failure: Date;

  @Column({ type: 'jsonb', nullable: true })
  job_data: any;

  @Column({ type: 'varchar', length: 50, default: 'failed' })
  status: 'failed' | 'retrying' | 'resolved';

  @Column({ type: 'text', nullable: true })
  resolution_notes: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
