import { MigrationInterface, QueryRunner } from "typeorm";
import { v4 as uuidv4 } from 'uuid';

export class ChangeInBookEntity1740996358443 implements MigrationInterface {
    name = 'ChangeInBookEntity1740996358443';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 1. Drop existing primary key constraint on `book_number`
        await queryRunner.query(`ALTER TABLE "book_number_master" DROP CONSTRAINT IF EXISTS "PK_08e9055ec8df29c3d59e09d2c50"`);

        // 2. Add new `id` column with UUID type and default value
        await queryRunner.query(`ALTER TABLE "book_number_master" ADD COLUMN "id" UUID DEFAULT uuid_generate_v4()`);

        // 3. Ensure existing rows have unique UUIDs
        const bookNumbers = await queryRunner.query(`SELECT "book_number" FROM "book_number_master" WHERE "id" IS NULL`);
        for (const { book_number } of bookNumbers) {
            const uuid = uuidv4();
            await queryRunner.query(`UPDATE "book_number_master" SET "id" = '${uuid}' WHERE "book_number" = '${book_number}'`);
        }

        // 4. Make `id` column NOT NULL
        await queryRunner.query(`ALTER TABLE "book_number_master" ALTER COLUMN "id" SET NOT NULL`);

        // 5. Set new primary key on `id`
        await queryRunner.query(`ALTER TABLE "book_number_master" ADD CONSTRAINT "PK_2050a0d56fe4716e21c637ddc04" PRIMARY KEY ("id")`);

        // 6. Ensure `book_number` is unique, but allow soft-deletion exceptions
        await queryRunner.query(`ALTER TABLE "book_number_master" ADD CONSTRAINT "UQ_book_number_master" UNIQUE ("book_number")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Reverse the changes made in `up` method

        // 1. Drop the unique constraint on `book_number`
        await queryRunner.query(`ALTER TABLE "book_number_master" DROP CONSTRAINT IF EXISTS "UQ_book_number_master"`);

        // 2. Drop the new primary key on `id`
        await queryRunner.query(`ALTER TABLE "book_number_master" DROP CONSTRAINT IF EXISTS "PK_2050a0d56fe4716e21c637ddc04"`);

        // 3. Remove the `id` column
        await queryRunner.query(`ALTER TABLE "book_number_master" DROP COLUMN "id"`);

        // 4. Restore the original primary key on `book_number`
        await queryRunner.query(`ALTER TABLE "book_number_master" ADD CONSTRAINT "PK_08e9055ec8df29c3d59e09d2c50" PRIMARY KEY ("book_number")`);
    }
}
