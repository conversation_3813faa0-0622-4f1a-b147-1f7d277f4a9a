import React, { useContext, useState } from "react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

// Ensure these paths are correct relative to your file structure
import { ChevronDown } from "lucide-react";
import { GlobalContext } from "@/context/GlobalContext";

export interface PopupScreenProps {
  title: string;
  isOpen: boolean;
  toggle: () => void;
  children: React.ReactNode;
}

const GlobalCollapse: React.FC<PopupScreenProps> = ({
  title,
  isOpen,
  toggle,
  children,
}) => {
  // const {isCollapseOpen} = useContext<any>(GlobalContext)

  const { isCollapseOpen, toggleCollapse } = useContext<any>(GlobalContext);

  return (
    <>
      <Collapsible open={true} onOpenChange={toggleCollapse}>
        <CollapsibleTrigger className="w-full flex items-center justify-between px-1 text-[17px] font-semibold">
          {title}
          <ChevronDown
            className={`w-5 h-5 ml-2 transform transition-transform duration-300 ${isCollapseOpen ? "rotate-180" : ""}`}
          />
        </CollapsibleTrigger>
        <CollapsibleContent className={`px-2 pb-2 `}>
          <hr className="mt-3 mb-2" />
          {children}
        </CollapsibleContent>
      </Collapsible>
    </>
  );
};

export default GlobalCollapse;
