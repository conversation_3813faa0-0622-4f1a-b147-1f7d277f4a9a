import { useEffect, useRef, useCallback } from 'react';
import { SSEMessage } from '@/types/notification';

interface UseSSEOptions {
  onMessage?: (message: SSEMessage) => void;
  onError?: (error: Event) => void;
  onOpen?: () => void;
  onClose?: () => void;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export const useSSE = (url: string | null, options: UseSSEOptions = {}) => {
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const isConnectedRef = useRef(false);
  const tokenFormStorage = JSON.parse(localStorage.getItem('AccessToken') || '""');

  const callbacksRef = useRef(options);
  callbacksRef.current = options;

  const { reconnectInterval = 3000, maxReconnectAttempts = 5 } = options;

  const connect = useCallback((token) => {
 
    if (!url) {
      console.log('SSE: No URL provided, skipping connection');
      return;
    }

    if (eventSourceRef.current) {
      return;
      eventSourceRef.current.close();
    }

    try {
      const sseUrl = `${url}/?token=${token}`;
      console.log("token", token, "sseUrl", sseUrl);
      const eventSource = new EventSource(sseUrl);

      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
    
        console.log('SSE connection opened');
        
        isConnectedRef.current = true;
        reconnectAttemptsRef.current = 0;
        callbacksRef.current.onOpen?.();
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
      };

      eventSource.onmessage = (event) => {
        try {
       
          const message: SSEMessage = JSON.parse(event.data);
          callbacksRef.current.onMessage?.(message);
        } catch (error) {
          console.error('Error parsing SSE message:', error);
        }
      };

      eventSource.onerror = (error) => {
            

        console.error('SSE connection error:', error);
  
  
        callbacksRef.current.onError?.(error);

    

        const token = JSON.parse(localStorage.getItem('AccessToken') || '""');
        const userData = localStorage.getItem('UserData');

        if (!token || !userData || !url) {
          console.log('SSE: No valid authentication, stopping reconnection attempts');
          callbacksRef.current.onClose?.();
          return;
        }

        if (reconnectAttemptsRef.current < maxReconnectAttempts && isConnectedRef.current === false) {
          reconnectAttemptsRef.current++;
          console.log(`Attempting to reconnect SSE (${reconnectAttemptsRef.current}/${maxReconnectAttempts})`);

          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
          }

          reconnectTimeoutRef.current = setTimeout(() => {
            connect(tokenFormStorage);
          }, reconnectInterval);
        } else {
          console.error('Max reconnection attempts reached');
          callbacksRef.current.onClose?.();
        }
      };
    } catch (error) {
      console.error('Error creating SSE connection:', error);
      callbacksRef.current.onError?.(error as Event);
    }
  }, [url, reconnectInterval, maxReconnectAttempts, tokenFormStorage]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    isConnectedRef.current = false;
    callbacksRef.current.onClose?.();
  }, [callbacksRef]);

  useEffect(() => {
          console.log("tokenFormStorage  1")

    if (tokenFormStorage) {
      console.log("tokenFormStorage 2")
      connect(tokenFormStorage);
    }

    return () => {
      disconnect();
    };
  }, [connect, disconnect, tokenFormStorage]);

  return {
    isConnected: isConnectedRef.current,
    connect,
    disconnect,
    sendMessage: () => console.warn('SSE is unidirectional. Use HTTP requests for sending data to server.'),
  };
};
