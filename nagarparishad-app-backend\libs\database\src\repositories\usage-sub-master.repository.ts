import { Repository } from 'typeorm';
import { UsageSubTypeMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class UsageSubMasterRepository extends Repository<UsageSubTypeMasterEntity> {
  constructor(
    @InjectRepository(UsageSubTypeMasterEntity)
    private readonly usageSubMasterRepository: Repository<UsageSubTypeMasterEntity>,
  ) {
    super(
      usageSubMasterRepository.target,
      usageSubMasterRepository.manager,
      usageSubMasterRepository.queryRunner,
    );
  }

  // async saveData(input: { subUsage: string }): Promise<UsageSubTypeMasterEntity> {
  //   let data = this.usageSubMasterRepository.create(input);
  //   data = await this.usageSubMasterRepository.save(data);
  //   return data;
  // }

  async findAllData(): Promise<UsageSubTypeMasterEntity[]> {
    return await this.usageSubMasterRepository
      .createQueryBuilder('usage_sub_type_master')
      .select([
        'usage_sub_type_master.usage_sub_type_master_id',
        'usage_sub_type_master.usage_sub_type',
        'usageType.usage_type_id',
        'usageType.usage_type',
      ])
      .leftJoin('usage_sub_type_master.usageType', 'usageType')
      .orderBy('usage_sub_type_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string) {
    return await this.usageSubMasterRepository
      .createQueryBuilder('usage_sub_master')
     
      .leftJoinAndSelect('usage_sub_master.usageType', 'usage_master')
      .where('usage_sub_master.usage_sub_type_master_id = :usage_sub_type_master_id', {
        usage_sub_type_master_id: id,
      })
      .getOne();
  }

  async updateData(id: string, input: any) {
    return await this.usageSubMasterRepository
      .createQueryBuilder('usage_sub_master')
      .update(UsageSubTypeMasterEntity)
      .set(input)
      .where('usageSub_id = :usageSub_id', { usageSub_id: id })
      .execute();
  }

  async deleteData(id: string) {
    return await this.usageSubMasterRepository
      .createQueryBuilder('usage_sub_master')
      .softDelete()
      .where('usageSub_id = :usageSub_id', { usageSub_id: id })
      .execute();
  }

  async findData(id: string) {
    return await this.usageSubMasterRepository
      .createQueryBuilder('usage_sub_master')
      .select(['usage_sub_master.usageSub_id', 'usage_sub_master.subUsage'])
      .where('usage_master.usage_id = :usage_id', { usage_id: id })
      .leftJoin('usage_sub_master.usage', 'usage_master')
      .getMany();
  }
}
