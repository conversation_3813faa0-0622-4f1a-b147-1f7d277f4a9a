import {
  BaseEntity,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Financial_year } from './financial_year.entity'

@Entity('tax_fy_records')
export class Tax_FY_RecordsEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  tax_fy_records_id: string;



  
  @Column({ type: String, nullable: false })
  status: string;

  @Column({ type: 'float', nullable: false })
  total_property: number;


  @Column({ type: 'float', nullable: false })
  total_property_processed: number; 

  @Column({ type: 'varchar', nullable: true })
  financial_year: string; 

  @Column({ type: 'boolean', nullable: false,default:false })
  is_published: boolean;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
