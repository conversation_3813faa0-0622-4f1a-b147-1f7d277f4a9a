import Api from "../../services/ApiServices";
import {
  useQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import TaxListApi from "@/services/TaxServices";


const fetchtaxRate = async () => {
    return new Promise((resolve, reject) => {
      TaxListApi.getTaxRate((response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      });
    });
  };
  

const createtaxRate = async (RR_RateData: any) => {
  return new Promise((resolve, reject) => {
   TaxListApi.createTaxRate(RR_RateData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateTaxRate = async ({ taxRateId, taxRateRateData}: { taxRateId: string; taxRateRateData: any }) => {
  return new Promise((resolve, reject) => {
    TaxListApi.updateTaxRate(taxRateId,taxRateRateData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteTaxRate = async (taxRateId: string) => {
  return new Promise((resolve, reject) => {
    TaxListApi.deleteTaxRate(taxRateId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useTaxRateController = () => {
  const queryClient = useQueryClient();

  const { data: taxRateData, isLoading: propertyLoading } = useQuery({
    queryKey: ["taxRatemaster"],
    queryFn: fetchtaxRate,
    staleTime: 10 * 60 * 2, // 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
  });

  // const createtaxRateMutation = useMutation({
  //   mutationFn: TaxListApi.createTaxRate,
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["taxRatemaster"] });
  //   },
  // });
  const createtaxRateMutation = useMutation({
    mutationFn: createtaxRate,
    onMutate: async (newconstructionRates) => {
      await queryClient.cancelQueries({ queryKey: ["taxRatemaster"] });
      const previousconstructionRates = queryClient.getQueryData(["taxRatemaster"]);

      queryClient.setQueryData(["taxRatemaster"], (old: any) => {
             console.log(" construtionold",old)
        const updatedData = [newconstructionRates, ...old.data];
        console.log("updatedData",updatedData)
        return updatedData;
      });

      return { previousconstructionRates };
    },
    onError: (err, newconstructionRates, context) => {
      queryClient.setQueryData(["taxRatemaster"], context.previousconstructionRates);
      console.error("Error creating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["taxRatemaster"] });
    },
  });
  // const updatetaxRateMutation = useMutation({
  //   mutationFn: TaxListApi.updateTaxRate,
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["taxRatemaster"] });
  //   },
  // });
  const updatetaxRateMutation = useMutation({
    mutationFn: updateTaxRate,

    onMutate: async ({  taxRateId ,taxRateRateData }) => {
      await queryClient.cancelQueries({ queryKey: ["taxRatemaster"] });

      const previousWards = queryClient.getQueryData(["taxRatemaster"]);
      queryClient.setQueryData(["taxRatemaster"], (old: any) => {
        const updatedWards = old?.data?.map((constructionRate: any) =>
          constructionRate.rr_tax_id === taxRateId ? { ...constructionRate, ...taxRateRateData } : constructionRate,
        );
        console.log("Updated query data:", updatedWards);
        return updatedWards;
      });

      return { previousWards };
    },
    onError: (err, { taxRateId, taxRateRateData }, context) => {
      queryClient.setQueryData(["taxRatemaster"], context.previousWards);
      console.error("Error updating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["taxRatemaster"] });
    },
  });
  // const deletetaxRateMutation = useMutation({
  //   mutationFn: TaxListApi.deleteTaxRate,
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["taxRatemaster"] });
  //   },
  // });
  const deletetaxRateMutation = useMutation({
    mutationFn: deleteTaxRate,
    onMutate: async (taxRateId) => {
      await queryClient.cancelQueries({ queryKey: ["taxRatemaster"] });

      const previousConstructionRate = queryClient.getQueryData(["taxRatemaster"]);

      queryClient.setQueryData(["taxRatemaster"], (old: any) => {
        const updatedConstructionRate = old?.data?.filter((constructionRate: any) => constructionRate.rr_tax_id !== taxRateId);
        return updatedConstructionRate;
      });

      return { previousConstructionRate };
    },
    onError: (err, taxRateId, context) => {
      queryClient.setQueryData(["taxRatemaster"], context.previousConstructionRate);
      console.error("Error deleting ward:", err);
    },
    onSuccess: () => {
      // Directly update the cache with the new data
      queryClient.setQueryData(["taxRatemaster"], (old: any) => {
        const updatedConstructionRate = old?.data?.filter((constructionRate: any) => constructionRate.rr_tax_id !== taxRateId);
        return updatedConstructionRate;
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["taxRatemaster"] });
    },
  });
  return {
    taxRateList: taxRateData || [],
    propertyLoading,
    createTaxRate: createtaxRateMutation.mutate,
    updateTaxRate: updatetaxRateMutation.mutate,
    deleteTaxRate: deletetaxRateMutation.mutate,
  };

  
};


