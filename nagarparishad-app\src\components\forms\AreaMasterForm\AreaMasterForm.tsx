import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  AreaSendApiObj,
  AreaMasterObject,
 
} from "../../../model/area-master";
import { useToast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { Loader2 } from "lucide-react";
import { useAreaMasterController } from "@/controller/master/AreaMasterController";

interface AreaMasterInterface {
  btnTitle: string;
  editData?: AreaMasterObject;
}

const schema = z.object({
  areaName: z.string().trim().min(1, "This field is required"),
});

const AreaMasterForm = ({ btnTitle, editData }: AreaMasterInterface) => {
  const { t } = useTranslation();

  const schema = z.object({
    areaName: z.string().trim().min(1, t("errorsRequiredField")),
  });

  const dynamicValues = {
    name: t("areaorlocality.areaLabel"),
  };
  const { createArea, updateArea } = useAreaMasterController();

  const { toast } = useToast();
  const [loader, setLoader] = useState(false);

  const { setOpen,   } =
    useContext(GlobalContext);
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      areaName: editData?.areaName || "",
    },
  });
  const {
    formState: { errors },
    
  } = form;

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();

    const DataResponse: AreaSendApiObj = {
      areaName: data?.areaName,
    };
    if (editData?.area_id !== undefined) {
      setLoader(true);
      updateArea(
        {
          areaId: editData?.area_id,
          areaData: DataResponse,
        },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            form.reset({ areaName: "" });
            setOpen(false);
            setLoader(false);
          },
          onError: (error) => {
            toast({
              title: error.message,
            });
            setLoader(false);
          },
        },
      );
    } else {
      setLoader(true);
      createArea(DataResponse, {
        onSuccess: () => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          form.reset({ areaName: "" });
          setOpen(false);
          setLoader(false);
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });
    }
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        areaName: editData.areaName || "",
      });
    } else {
      form.reset({
        areaName: "",
      });
    }
  }, [editData]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full md:w-full"
        >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="areaName"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("areaorlocality.areaLabel")}</FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1"
                        placeholder={t("areaorlocality.areaLabel")}
                        {...field}
                      />
                    </FormControl>
                    {errors.areaName && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
              <div className="grid-cols-subgrid  mb-1 ml-4 max-md:flex max-md:justify-end  pt-[32px] ">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
          </div>
   
        </form>
      </Form>
    </>
  );
};

export default AreaMasterForm;
