import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ElectionBoundaryMasterService } from './election-boundary_master.service';
import {
  CreateElectionBoundaryMasterDto,
  ElectionBoundaryMasterDto,
  UpdateElectionBoundaryMasterDto,
} from './dto/election-master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@ApiTags('election-boundary-master')
@Controller('electionBoundary-master')
export class ElectionBoundaryMasterController {
  constructor(
    private readonly electionBoundaryMasterService: ElectionBoundaryMasterService,
  ) {}

  @Form('Election Boundry Master')
  @Permissions('can_write')
  @ApiOperation({ summary: 'Create a new Election Boundary' })
  @ApiResponse({
    status: 201,
    description: 'The Election Boundary has been successfully created',
  })
  @Post()
  create(
    @Body() createElectionBoundaryMasterDto: CreateElectionBoundaryMasterDto,
  ) {
    return this.electionBoundaryMasterService.create(
      createElectionBoundaryMasterDto,
    );
  }

  @Form('Election Boundry Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all Election Boundary' })
  @ApiResponse({ status: 200, description: 'Returns all Election Boundary' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.electionBoundaryMasterService.findAll();
  }

  @Form('Election Boundry Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one Election Boundary' })
  @ApiResponse({ status: 200, description: 'Returns Single Election Boundary' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() electionMasterDto: ElectionBoundaryMasterDto) {
    return this.electionBoundaryMasterService.findOne(electionMasterDto);
  }

  @Form('Election Boundry Master')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Update a Election Boundary by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Election Boundary has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Election Boundary not found' })
  @Patch()
  update(
    @Query() electionMasterDto: ElectionBoundaryMasterDto,
    @Body() updateElectionBoundaryMasterDto: UpdateElectionBoundaryMasterDto,
  ) {
    return this.electionBoundaryMasterService.update(
      electionMasterDto,
      updateElectionBoundaryMasterDto,
    );
  }

  @Form('Election Boundry Master')
  @Permissions('can_delete')
  @ApiOperation({ summary: 'Delete a Election Boundary by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Election Boundary has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Election Boundary not found' })
  @Delete()
  remove(@Query() electionMasterDto: ElectionBoundaryMasterDto) {
    return this.electionBoundaryMasterService.remove(electionMasterDto);
  }
}
