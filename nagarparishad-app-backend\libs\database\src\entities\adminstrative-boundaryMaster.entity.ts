import {
  BaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('adminstrative_boundaryMaster')
export class AdminstrativeBoundaryMasterEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  adminstrativeBoundary_id: string;

  @Column({
    name: 'adminstrative_boundary_name',
    type: String,
    nullable: false,
  })
  adminstrativeBoundary_name: string;

  /*
   * Create and Update Date Columns
   */

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
