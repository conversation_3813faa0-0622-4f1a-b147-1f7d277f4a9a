import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON> } from "@/components/ui/button";
import WhiteContainer from "@/components/custom/WhiteContainer";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { useTranslation } from "react-i18next";
import { usePaidDataController } from "@/controller/PaidData/PaidDataController";
import { PaidAndNonPaidDataInterface } from "@/model/PaidData/PaidDataInterfaces";
import React from "react";

const PaymentDashboard = () => {
  const [activeTab, setActiveTab] = useState("payment");
  const { t } = useTranslation();

  // Use the paid data controller
  const {
    paidUsers,
    nonPaidUsers,
    paidUsersLoading,
    nonPaidUsersLoading,
    isGeneratingData,
    paidUsersPagination,
    nonPaidUsersPagination,
    searchValue,
    searchOn,
    setSearchOn,
    setSearchValue,
    
    handleSearchValueChange,
    generateData,
    handlePagination, // Use the new pagination handler instead of pagination
    paidUsersData,
    nonPaidUsersData,
  } = usePaidDataController();

  // // Handle generate data - backend will use current year automatically
  // const handleGenerateData = () => {
  //   generateData(""); // Pass empty string since backend uses current year
  // };
  
  const paymentColumns = [
    {
      accessorKey: "property_number",
      header: "मालमत्ता क्रमांक",
      cell: ({ row }: { row: { original: PaidAndNonPaidDataInterface } }) =>
        row.original.property_number || "-",
    },
    {
      accessorKey: "old_property_number",
      header: "जुना मालमत्ता क्रमांक",
      cell: ({ row }: { row: { original: PaidAndNonPaidDataInterface } }) =>
        row.original.old_property_number || "-",
    },
    {
      accessorKey: "name",
      header: "मालकाचे नाव",
      cell: ({ row }: { row: { original: PaidAndNonPaidDataInterface } }) =>
        row.original.name || "-",
    },
    {
      accessorKey: "paid",
      header: "भरलेली रक्कम",
      cell: ({ row }: { row: { original: PaidAndNonPaidDataInterface } }) => {
        const amount = Number(row.original.paid || 0);
        const formatted = new Intl.NumberFormat("en-IN", {
          style: "currency",
          currency: "INR",
        }).format(amount);
        return formatted;
      },
    },
    {
      accessorKey: "total_tax",
      header: "एकूण रक्कम",
      cell: ({ row }: { row: { original: PaidAndNonPaidDataInterface } }) => {
        const amount = Number(row.original.total_tax || 0);
        const formatted = new Intl.NumberFormat("en-IN", {
          style: "currency",
          currency: "INR",
        }).format(amount);
        return formatted;
      },
    },
    {
      accessorKey: "remaining",
      header: "उर्वरित रक्कम",
      cell: ({ row }: { row: { original: PaidAndNonPaidDataInterface } }) => {
        const amount = Number(row.original.remaining || 0);
        const formatted = new Intl.NumberFormat("en-IN", {
          style: "currency",
          currency: "INR",
        }).format(amount);
        return formatted;
      },
    },
    {
      accessorKey: "is_paid",
      header: "स्थिती",
      cell: ({ row }: { row: { original: PaidAndNonPaidDataInterface } }) => (
        <span
          className={`px-2 py-1 rounded text-xs ${
            row.original.is_paid
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {row.original.is_paid ? "भरलेले" : "न भरलेले"}
        </span>
      ),
    },
  ];

    const handleSearchKeyChange = (value: string) => {
      console.log("valuee->", value)
    setSearchOn(value);
    setSearchValue(""); // Reset search value when changing search field
  };

  return (
    <div className="w-full p-6">
      <WhiteContainer className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">पेमेंट डॅशबोर्ड</h2>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-green-50 p-4 rounded-lg border">
            <h3 className="text-sm font-medium text-green-800">भरलेले युजर</h3>
            <p className="text-2xl font-bold text-green-900">
              {paidUsersData?.total || 0}
            </p>
          </div>
          <div className="bg-red-50 p-4 rounded-lg border">
            <h3 className="text-sm font-medium text-red-800">न भरलेले युजर</h3>
            <p className="text-2xl font-bold text-red-900">
              {nonPaidUsersData?.total || 0}
            </p>
          </div>
          {/* <div className="bg-blue-50 p-4 rounded-lg border">
            <h3 className="text-sm font-medium text-blue-800">
              एकूण भरलेली रक्कम
            </h3>
            <p className="text-lg font-bold text-blue-900">
              ₹
              {paidUsers
                ?.reduce(
                  (sum: number, user: PaidAndNonPaidDataInterface) =>
                    sum + Number(user.paid || 0),
                  0
                )
                .toLocaleString() || 0}
            </p>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg border">
            <h3 className="text-sm font-medium text-orange-800">
              एकूण उर्वरित रक्कम
            </h3>
            <p className="text-lg font-bold text-orange-900">
              ₹
              {nonPaidUsers
                ?.reduce(
                  (sum: number, user: PaidAndNonPaidDataInterface) =>
                    sum + Number(user.remaining || 0),
                  0
                )
                .toLocaleString() || 0}
            </p>
          </div> */}
        </div>

        <Tabs defaultValue="payment" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="payment">
              पेमेंट युजर 
            </TabsTrigger>
            <TabsTrigger value="nonpayment">
              नॉन पेमेंट युजर 
            </TabsTrigger>
          </TabsList>

          <TabsContent value="payment">
            <TanStackTable
              columns={paymentColumns}
              data={paidUsers || []}
              searchKey={"property_number"}
              searchColumn={"property_number"}
              loader={paidUsersLoading}
               searchColumnArray={[
                  "property_number",
                  "owner_details_name",
                  "old_property_number",
                ]}
              onSearch={handleSearchValueChange}
              onSearchFieldChange={handleSearchKeyChange}
              selectedSearchField={searchOn}
              serverPagination={{
                currentPage: paidUsersPagination.page,
                pageSize: paidUsersPagination.limit,
                totalPages: paidUsersPagination.totalPages,
                totalRecords: paidUsersPagination.totalRecords,
                onPageChange: (page) => handlePagination('paid', page),
                onPageSizeChange: (size) => paidUsersPagination.setLimit(size),
              }}
            />
          </TabsContent>

          <TabsContent value="nonpayment">
            <TanStackTable
              columns={paymentColumns}
              data={nonPaidUsers || []}
              searchKey={"property_number"}
              searchColumn={"property_number"}
              loader={nonPaidUsersLoading}
               searchColumnArray={[
                  "property_number",
                  "owner_details_name",
                  "old_property_number",
              
                ]}
              onSearch={handleSearchValueChange}
              onSearchFieldChange={handleSearchKeyChange}
              selectedSearchField={searchOn}
              serverPagination={{
                currentPage: nonPaidUsersPagination.page,
                pageSize: nonPaidUsersPagination.limit,
                totalPages: nonPaidUsersPagination.totalPages,
                totalRecords: nonPaidUsersPagination.totalRecords,
                onPageChange: (page) => handlePagination('nonpaid', page),
                onPageSizeChange: (size) => nonPaidUsersPagination.setLimit(size),
              }}
            />
          </TabsContent>
        </Tabs>
      </WhiteContainer>
    </div>
  );
};

export default PaymentDashboard;
