

import { resolve } from "path";
import ReportApi from "../../services/ReportServices";
import { useQuery } from "@tanstack/react-query";


//namuns 8

const fetchNamunaNine = async () => {
  return new Promise((resolve, reject) => {
    ReportApi.getNamunaNine((res) => {
      if (res.status) {
        console.log("namuan 9",res.data)
        resolve(res.data);
      } else {
        reject(res.data);
      }
    });
  });
};

export const useNamunaNineController = () => {

  const { data: namunaNine, isLoading: namunaNineData } = useQuery({
    queryKey: ["namunanine"],
    queryFn: () => fetchNamunaNine(),
    staleTime: 10 * 60 * 2,
    refetchOnWindowFocus: true,
  });

  return {
    namunaNineDetails:namunaNine || {},
    namunaNineData
  };
};
