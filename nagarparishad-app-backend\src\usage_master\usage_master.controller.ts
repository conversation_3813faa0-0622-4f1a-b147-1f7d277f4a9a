import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { UsageMasterService } from './usage_master.service';
import {
  CreateUsageMasterDto,
  UpdateUsageMasterDto,
  UsageMasterDto,
} from './dto/usage-master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@ApiTags('Usage Master')
@Controller('usage-master')
export class UsageMasterController {
  constructor(private readonly usageMasterService: UsageMasterService) {}

  
  @Form('Usage')
  @Permissions('can_write')
  @ApiOperation({ summary: 'Create a new Usage' })
  @ApiResponse({
    status: 201,
    description: 'The Usage has been successfully created',
  })
  @Post()
  create(@Body() createUsageMasterDto: CreateUsageMasterDto) {
    return this.usageMasterService.create(createUsageMasterDto);
  }

  
  @Form('Usage')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all Usage' })
  @ApiResponse({ status: 200, description: 'Returns all Usage' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.usageMasterService.findAll();
  }

  
  @Form('Usage')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one Usage' })
  @ApiResponse({ status: 200, description: 'Returns Single Usage' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() usageMasterDto: UsageMasterDto) {
    return this.usageMasterService.findOne(usageMasterDto);
  }

  
  @Form('Usage')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Update a Usage by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Usage has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Usage not found' })
  @Patch()
  update(
    @Query() usageMasterDto: UsageMasterDto,
    @Body() updateUsageMasterDto: UpdateUsageMasterDto,
  ) {
    return this.usageMasterService.update(usageMasterDto, updateUsageMasterDto);
  }


  
  @Form('Usage')
  @Permissions('can_delete')
  @ApiOperation({ summary: 'Delete a Usage by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Usage has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Usage not found' })
  @Delete()
  remove(@Query() usageMasterDto: UsageMasterDto) {
    return this.usageMasterService.remove(usageMasterDto);
  }
}
