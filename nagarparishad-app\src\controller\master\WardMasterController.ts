import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import Api from "../../services/ApiServices";

const fetchWards = async () => {
  const response = await Api.getAllWard();

  return response.data.sort((a, b) => Number(a.ward_name) - Number(b.ward_name));
};


const createWard = async (wardData: any) => {
  return new Promise((resolve, reject) => {
    Api.createWard(wardData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateWard = async ({
  wardId,
  wardData,
}: {
  wardId: any;
  wardData: any;
}) => {
  return new Promise((resolve, reject) => {
    Api.updateWard(wardId, wardData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteWard = async (wardId: string) => {
  return new Promise((resolve, reject) => {
    Api.deleteWard(wardId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useWardMasterController = () => {
  const queryClient = useQueryClient();

  const { data: wardData, isLoading:wardLoading} = useQuery({
    queryKey: ["wardmaster"],
    queryFn: fetchWards,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });
  // console.log("warddattttt",wardData)
  const createWardMutation = useMutation({
    mutationFn: createWard,
    onMutate: async (newWard) => {
      await queryClient.cancelQueries({ queryKey: ["wardmaster"] });
      const previousWards = queryClient.getQueryData(["wardmaster"]);
      queryClient.setQueryData(["wardmaster"], (old: any) => {
        const updatedData = [newWard, ...old];
        return updatedData;
      });
      return { previousWards };
    },
    onError: (err, newWard, context) => {
      queryClient.setQueryData(["wardmaster"], context.previousWards);
      console.error("Error creating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["wardmaster"] });
    },
  });

  const updateWardMutation = useMutation({
    mutationFn: updateWard,
    onMutate: async ({ wardId, wardData }) => {
      await queryClient.cancelQueries({ queryKey: ["wardmaster"] });

      const previousWards = queryClient.getQueryData(["wardmaster"]);
      queryClient.setQueryData(["wardmaster"], (old: any) => {
        const updatedWards = old?.map((ward: any) =>
          ward.ward_id === wardId ? { ...ward, ...wardData } : ward,
        );
        console.log("Updated query data:", updatedWards);
        return updatedWards;
      });

      return { previousWards };
    },
    onError: (err, { wardId, wardData }, context) => {
      queryClient.setQueryData(["wardmaster"], context.previousWards);
      console.error("Error updating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["wardmaster"] });
    },
  });

  const deleteWardMutation = useMutation({
    mutationFn: deleteWard,
    onMutate: async (wardId) => {
      await queryClient.cancelQueries({ queryKey: ["wardmaster"] });

      const previousWards = queryClient.getQueryData(["wardmaster"]);

      queryClient.setQueryData(["wardmaster"], (old: any) => {
        const updatedWards = old.filter((ward: any) => ward.ward_id !== wardId);
        return updatedWards;
      });

      return { previousWards };
    },
    onError: (err, ward_id, context) => {
      queryClient.setQueryData(["wardmaster"], context.previousWards);
      console.error("Error deleting ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["wardmaster"] });
    },
  });

  return {
    wardList: wardData || [],
    wardLoading,
    createWard: createWardMutation.mutate,
    updateWard: updateWardMutation.mutate,
    deleteWard: deleteWardMutation.mutate,
  };
};
