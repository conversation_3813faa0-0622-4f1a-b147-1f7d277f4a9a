{"name": "nagarparishad-app", "description": "Nagarparishad App", "identifier": "com.onpoint.nagarparishad-app", "author": "onPoint Software Services", "version": "1.0.0", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx .", "dev": "vite", "format": "prettier --write .", "build": "vite build", "serve": "vite serve dist", "prepare": "husky", "commit": "git-cz", "plop": "plop"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@electron-forge/cli": "^7.4.0", "@electron-forge/maker-deb": "^7.4.0", "@electron-forge/maker-rpm": "^7.4.0", "@electron-forge/maker-squirrel": "^7.4.0", "@electron-forge/maker-zip": "^7.4.0", "@electron-forge/plugin-auto-unpack-natives": "^7.4.0", "@electron-forge/plugin-fuses": "^7.4.0", "@electron-forge/plugin-vite": "^7.4.0", "@electron/fuses": "^1.8.0", "@tanstack/eslint-plugin-query": "^5.35.6", "@types/node": "^20.12.7", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.19", "commitizen": "^4.3.0", "electron": "30.0.1", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "husky": "^9.0.11", "plop": "^4.0.1", "postcss": "^8.4.38", "prettier": "^3.2.5", "tailwindcss": "^3.4.3", "ts-node": "^10.9.2", "typescript": "~4.5.4", "vite": "latest", "vite-plugin-html": "^3.2.2"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.40.0", "@tanstack/react-query-devtools": "^5.40.0", "@tanstack/react-table": "^8.16.0", "@tippyjs/react": "^4.2.6", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "@vitejs/plugin-react": "latest", "@vitejs/plugin-react-swc": "^3.6.0", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "0.2.1", "cz-git": "^1.9.1", "date-fns": "^3.6.0", "electron-squirrel-startup": "^1.0.0", "embla-carousel-react": "^8.2.0", "i18next": "^23.11.3", "i18next-browser-languagedetector": "^7.2.1", "i18next-http-backend": "^2.5.1", "indian-currency-formatter": "^1.0.4", "input-otp": "^1.2.4", "lucide-react": "^0.373.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.5.3", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.51.3", "react-i18next": "^14.1.1", "react-json-view": "^1.21.3", "react-loading-skeleton": "^3.5.0", "react-responsive": "^10.0.0", "react-router-dom": "^6.23.0", "react-select": "^5.8.0", "react-transliterate": "^1.1.9", "react-webcam": "^7.2.0", "recharts": "^2.12.7", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "vite": "latest", "vite-plugin-svgr": "^4.2.0", "zod": "^3.23.4"}, "lint-staged": {"*.js": "prettier", "*.ts": "eslint"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "config": {"commitizen": {"path": "node_modules/cz-git", "useEmoji": true}}}