import { Repository } from 'typeorm';
import { Property_Fod_Details_Entity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class PropertyFodDetailsRepository extends Repository<Property_Fod_Details_Entity> {
  constructor(
    @InjectRepository(Property_Fod_Details_Entity)
    private readonly propertyFodDetailsRepository: Repository<Property_Fod_Details_Entity>,
  ) {
    super(
      propertyFodDetailsRepository.target,
      propertyFodDetailsRepository.manager,
      propertyFodDetailsRepository.queryRunner,
    );
  }

  async findAllFodDetails() {
    return await this.propertyFodDetailsRepository
      .createQueryBuilder('property_fod_details')
      .orderBy('property_fod_details.created_at', 'DESC')
      .getMany();
  }

  async findById(fodDetailsId: string) {
    return await this.propertyFodDetailsRepository
      .createQueryBuilder('property_fod_details')
      .where('property_fod_details.property_fod_details_id = :fodDetailsId', { fodDetailsId })
      .getOne();
  }

  async updateFodDetails(fodDetailsId: string, input: Partial<Property_Fod_Details_Entity>) {
    return await this.propertyFodDetailsRepository
      .createQueryBuilder('property_fod_details')
      .update(Property_Fod_Details_Entity)
      .set(input)
      .where('property_fod_details_id = :fodDetailsId', { fodDetailsId })
      .execute();
  }

  async deleteFodDetails(fodDetailsId: string) {
    return await this.propertyFodDetailsRepository
      .createQueryBuilder('property_fod_details')
      .softDelete()
      .where('property_fod_details_id = :fodDetailsId', { fodDetailsId })
      .execute();
  }

  
  async saveFodDetails(input: Partial<Property_Fod_Details_Entity>) {
    const newFodDetails = this.propertyFodDetailsRepository.create(input);
    return await this.propertyFodDetailsRepository.save(newFodDetails);
  }
}
