import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import {
  RolewiseFormPermissionRepository,
  UserRepository,
} from 'libs/database/repositories';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private userRepository: UserRepository,
    private permissionRepository: RolewiseFormPermissionRepository,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.get<string[]>(
      'permissions',
      context.getHandler(),
    );
    const requiredFormName = this.reflector.get<string>(
      'form',
      context.getHandler(),
    );
    console.log("hereee-->")

    // addding return true to allow access to all routes
    if (!requiredPermissions || !requiredFormName) {
      return true;
    }
    console.log("hereee-->")
      // return true;

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    const dbUser = await this.userRepository.findOne({
      where: { user_id: user.sub },
      relations: ['role'],
    });
    console.log('dbUser', dbUser, user);

    if (!dbUser) {
      throw new ForbiddenException('User not found');
    }

    const permission = await this.permissionRepository.findOne({
      where: {
        role: { role_id: dbUser.role.role_id }, // Match by roleId
        form: { formName: requiredFormName },
        is_valid: true,
      },
      relations: ['form', 'role'], // Include both relations
    });

    if (!permission) {
      throw new ForbiddenException(
        'No permissions found for this role and form',
      );
    }

    const hasPermission = requiredPermissions.every((perm) => permission[perm]);

    if (!hasPermission) {
      throw new ForbiddenException(
        'Access denied due to insufficient permissions',
      );
    }

    return true;
  }
}
