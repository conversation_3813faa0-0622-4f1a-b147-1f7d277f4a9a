export const propertyTypeClassFilter = (row, columnId, filterValue) => {
    const property_type_class = row.original.property_type_class_id?.property_type_class || "";
    return property_type_class.toLowerCase().includes(filterValue?.toLowerCase() || "");
  };
  export const ownerNameFilterFn = (row, columnId, filterValue) => {
    const ownerDetails = row.original.property_owner_details;
    const newValue = ownerDetails.some((owner) =>
      owner.name.toLowerCase().includes(filterValue.toLowerCase())
    );
    return newValue;
  };

  export const zoneFilterFn = (row, columnId, filterValue) => {
    const zoneName = row.original.zone?.zoneName || "";

    const newValue = zoneName.toLowerCase().includes(filterValue.toLowerCase());
    return newValue;
  };
  export const propertyTypeFilterFn = (row, columnId, filterValue) => {
    const propertyType = row.original?.property_type?.propertyType || "";

    const newValue = propertyType.toLowerCase().includes(filterValue.toLowerCase());
    return newValue;
  };
  export const propertyUsageTypeFilterFn = (row, columnId, filterValue) => {
    const propertyUsageType = row.original.usage_type.usage_type || "";

    const newValue = propertyUsageType.toLowerCase().includes(filterValue.toLowerCase());
    return newValue;
  };
  export const propertyUsageSubTypeFilterFn = (row, columnId, filterValue) => {
    const propertyUsageSubType = row.original.UsageSubType?.usage_sub_type || "";

    const newValue = propertyUsageSubType.toLowerCase().includes(filterValue.toLowerCase());
    return newValue;
  };

  export const wardFilterFn = (row, columnId, filterValue) => {
    const wardName = row.original.ward?.ward_name || "";
    return wardName.toLowerCase().includes(filterValue?.toLowerCase() || "");
  };
  export const floorFilterFn = (row, columnId, filterValue) => {
    const floor =row.original?.floor_name || "";
    return floor.toLowerCase().includes(filterValue?.toLowerCase() || "");
  };

  