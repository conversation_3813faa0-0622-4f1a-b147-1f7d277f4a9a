import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallH<PERSON>ler,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { IS_RAW_RESPONSE } from '../decorators/raw-response.decorator';

@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const isRawResponse = this.reflector.getAllAndOverride<boolean>(IS_RAW_RESPONSE, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isRawResponse) {
      return next.handle();
    }

    return next.handle().pipe(
      map((data) => ({
        statusCode: context.switchToHttp().getResponse().statusCode,
        message: data.message,
        data: data.data,
      })),
      catchError((error) => {
        // Handle known exceptions
        if (error instanceof HttpException) {
          throw error;
        }
        // Handle unknown exceptions
        const status = error?.status || HttpStatus.INTERNAL_SERVER_ERROR;
        const message = error?.message || 'Internal Server Error';
        throw new HttpException({ status, message }, status);
      }),
    );
  }
}
