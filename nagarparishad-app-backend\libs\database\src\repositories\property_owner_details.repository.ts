import { Repository } from 'typeorm';
import { Property_Owner_Details_Entity } from '../entities';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

export class Property_Owner_DetailsRepository extends Repository<Property_Owner_Details_Entity> {
  constructor(
    @InjectDataSource() private readonly dataSource: DataSource,

    @InjectRepository(Property_Owner_Details_Entity)
    private readonly property_Owner_DetailsRepository: Repository<Property_Owner_Details_Entity>,
  ) {
    super(
      property_Owner_DetailsRepository.target,
      property_Owner_DetailsRepository.manager,
      property_Owner_DetailsRepository.queryRunner,
    );
  }

  async getProperty_id(property_owner_details_id: string) {
    try {
      // Log the passed parameter
      
      const queryBuilder = this.property_Owner_DetailsRepository
        .createQueryBuilder('property_owner_details')
        .leftJoin('property_owner_details.property', 'property')
        .leftJoin('property_owner_details.owner_type', 'owner_type')
        .select('property.property_id')
        .where(
          'property_owner_details.property_owner_details_id = :property_owner_details_id',
          { property_owner_details_id },
        )
        .andWhere('property_owner_details.deletedAt IS NULL') // Explicit check for soft delete
        .andWhere('property.deletedAt IS NULL'); // Explicit check for soft delete

      // Log the actual SQL query
      
      const result = await queryBuilder.getRawOne(); // Fetching raw result

      console.log('Result:', result); // Log the raw result

      // Access the property_id using the expected alias format
      return result ? result['property_property_id'] : undefined;
    } catch (error) {
      throw error;
    }
  }

  async getData(property_id: string) {
    try {
      return await this.property_Owner_DetailsRepository
        .createQueryBuilder('property_owner_details')
        .select([
          'property_owner_details.property_owner_details_id',
          'property_owner_details.name',
          'property_owner_details.mobile_number',
          'property_owner_details.email_id',
          'property_owner_details.aadhar_number',
          'property_owner_details.pan_card',
          'property_owner_details.marital_status',
          'property_owner_details.partner_name',
          'property_owner_details.gender',
          'property_owner_details.is_owner',
          'property_owner_details.owner_type_id',
          'owner_type.owner_type',
        ])
        .leftJoin('property_owner_details.owner_type', 'owner_type')
        .where('property_owner_details.property_id = :property_id', {
          property_id,
        })
        .andWhere('property_owner_details.deletedAt IS NULL') // To exclude soft-deleted records
        .getMany(); // Changed to getMany() to fetch multiple records
    } catch (error) {
      throw error;
    }
  }

  async checkMobileNumber(mobile_number?: string, property_id?: string) {
    try {
      const queryBuilder = this.property_Owner_DetailsRepository
        .createQueryBuilder('property_owner_details')
        .select([
          'property_owner_details.property_owner_details_id',
          'property_owner_details.mobile_number',
          'property_owner_details.name',
        ]);

      if (mobile_number) {
        queryBuilder.andWhere(
          'property_owner_details.mobile_number = :mobile_number',
          { mobile_number },
        );
      }

      if (property_id) {
        queryBuilder.andWhere(
          'property_owner_details.property_id = :property_id',
          { property_id },
        );
      }
      return await queryBuilder.getOne();
    } catch (error) {
      throw error;
    }
  }

  async updateTaxPayer(owner_id: string, property_id: string) {
    const queryRunner = this.dataSource.createQueryRunner();
  
    await queryRunner.connect();
    await queryRunner.startTransaction();
  
    try {
      // Step 1: Find the current owner who is marked as the payer
      const currentPayer = await queryRunner.manager
        .createQueryBuilder(Property_Owner_Details_Entity, 'property_owner_details')
        .where('property_owner_details.is_payer = :is_payer', { is_payer: true })
        .andWhere('property_owner_details.property_id = :property_id', { property_id })
        .getOne();
  
      if (currentPayer && currentPayer.property_owner_details_id === owner_id) {
        await queryRunner.commitTransaction();
        return;
      }
  
      if (currentPayer) {
        await queryRunner.manager
          .createQueryBuilder()
          .update(Property_Owner_Details_Entity)
          .set({ is_payer: false })
          .where('property_owner_details_id = :owner_id', { owner_id: currentPayer.property_owner_details_id })
          .execute();
      }
  
      await queryRunner.manager
        .createQueryBuilder()
        .update(Property_Owner_Details_Entity)
        .set({ is_payer: true })
        .where('property_owner_details_id = :owner_id', { owner_id })
        .andWhere('property_id = :property_id', { property_id })
        .execute();
  
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }


  async updateAllPropertyOwnerDetails() {
    const queryRunner = this.dataSource.createQueryRunner();
  
    await queryRunner.connect();
    await queryRunner.startTransaction();
  
    try {
      // Step 1: Fetch all properties
      const properties = await queryRunner.manager
        .createQueryBuilder('property', 'property')
        .select(['property.property_id'])
        .getMany();
  
      // Step 2: Iterate over each property and update owner details
      for (const property of properties) {
        const property_id = property.property_id;
  
        // Fetch all owners associated with the given property
        const owners = await queryRunner.manager
          .createQueryBuilder(Property_Owner_Details_Entity, 'property_owner_details')
          .where('property_owner_details.property_id = :property_id', { property_id })
          .getMany();
  
        // Find the first owner with the owner type 'स्वत:'
        const newPayer = owners.find(owner => owner.owner_type.owner_type === 'स्वत:');
  
        if (!newPayer) {
          // If no owner with type 'स्वत:' is found, throw an error or handle accordingly
          throw new Error(`No owner with type "स्वत:" found for property ID: ${property_id}`);
        }
  
        // Update the payer status for all owners in one query
        await queryRunner.manager
          .createQueryBuilder()
          .update(Property_Owner_Details_Entity)
          .set({ is_payer: false }) // Set all to false initially
          .where('property_id = :property_id', { property_id })
          .execute();
  
        // Set the new payer to true
        await queryRunner.manager
          .createQueryBuilder()
          .update(Property_Owner_Details_Entity)
          .set({ is_payer: true })
          .where('property_owner_details_id = :owner_id', { owner_id: newPayer.property_owner_details_id })
          .execute();
      }
  
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
  
  
}
