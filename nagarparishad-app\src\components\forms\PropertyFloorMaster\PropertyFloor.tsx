import WhiteContainer from "@/components/custom/WhiteContainer";
import React, { useContext, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ArrowUpDown } from "lucide-react";

import { ColumnDef } from "@tanstack/react-table";
import { MasterObjectColumnDef } from "@/model/zone-master";
import { MASTER } from "@/constant/config/api.config";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { GlobalContext } from "@/context/GlobalContext";
import WardPopupForm from "../WardPopupForm";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { toast } from "@/components/ui/use-toast";
import AddNewBtn from "../../globalcomponent/AddNewForm";
import { WardUpdateObject } from "@/model/ward-master";
import { ResponseData } from "@/model/auth/authServices";
import PropertyClassMasterForm from "../PropertyClassMasterForm";
import { PropertyClassMasterObject } from "@/model/PropertyClassMaster";
import { usePropertyClassMasterController } from "@/controller/master/PropertyClassController";
import { usePropertyFloorMasterController } from "@/controller/master/PropertyFloorController";
import { PropertyFloorMasterObject } from "@/model/PropertyFloorMaster";
import PropertyFloorMasterForm from "../PropertyFloorMasterForm copy";
import { floorFilterFn } from "@/components/globalcomponent/TanstackFilter";

const PropertyFloorMaster = () => {
  const { t } = useTranslation();
  const { propertyFloorList } = usePropertyFloorMasterController();
   const propertyFloorLists = propertyFloorList || []
  const { setMasterComponent, setOpen } = useContext(GlobalContext);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<PropertyFloorMasterObject | null>(
    null,
  );
  const { deleteWard, propertyFloorLoading } = usePropertyFloorMasterController(); // Assuming you have a deleteWardMutation function from your controller
  const dynamicValues = {
    name: t("propertyfloor.propertyFloorTitle"),
  };

  function handleEdit(item: any): void {
    setOpen(true);
    setMasterComponent(
      <PropertyFloorMasterForm btnTitle={"update"} editData={item && item} />,
    );
  }

  function handleDelete(item: any): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }
  const handleConfirmDelete = () => {
    if (selectedItem) {
      deleteWard(selectedItem.floor_id, {
        onSuccess: (response: ResponseData) => {
          if (response.statusCode && response.statusCode === 200) {
            toast({
              title: t("api.formdelete", dynamicValues),
              variant: "success",
            });
          } else {
            toast({
              title: response.message,
              variant: "destructive",
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const columns: ColumnDef<MasterObjectColumnDef>[] = [
    {
      accessorKey: `${t("SrNo")}`,
      header: `${t("SrNo")}`,
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: "floor_name",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("propertyfloor.propertyfloorName")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
     cell: ({ row }: { row: any })  => <div className="">{row.original?.floor_name}</div>,
     filterFn:floorFilterFn,
     enableSorting:true
    },
    {
      accessorKey: `${t("Actions")}`,
      enableHiding: true,
     cell: ({ row }: { row: any })  => (
        <div className="flex space-x-2">
          <Button
            variant="submit"
            className="h-8 w-8 p-0 justify-center"
            onClick={() => handleEdit(row?.original)}
            //  onClick={toggleCollapse}
          >
            {t("table.edit")}
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0 justify-center ml-2  !border-BlueText !text-BlueText"
            onClick={() => handleDelete(row?.original)}
          >
            {t("table.delete")}
          </Button>
        </div>
      ),
    },
  ];

  const MasterType: string = MASTER.PROPERTYFLOOR;

  return (
    <div className="w-full h-fit p-6">
      <p className="w-full flex items-center justify-between ml-2 text-2xl font-semibold mb-2">
        {t("propertyfloor.propertyFloorTitle")}
      </p>
      <WhiteContainer>
        {MasterType && <AddNewBtn masterType={MASTER.PROPERTYFLOOR} />}
      </WhiteContainer>
      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={propertyFloorLists}
          masterType={MASTER.PROPERTYFLOOR}
          searchKey={"searchward"}
          searchColumn={"floor_name"}
          loader={propertyFloorLoading ? true : false}
        />
      </WhiteContainer>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={selectedItem.floor_name}
          onDelete={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default PropertyFloorMaster;

