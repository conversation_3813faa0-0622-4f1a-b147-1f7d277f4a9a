import { MigrationInterface, QueryRunner } from "typeorm";

export class TaxPendingDuesTable1738059807856 implements MigrationInterface {
    name = 'TaxPendingDuesTable1738059807856'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "tax_fy_records" DROP CONSTRAINT "FK_c9ce89ec90dc8f02667b203ab8c"`);
        await queryRunner.query(`CREATE TABLE "tax_pending_dues" ("dues_id" uuid NOT NULL DEFAULT uuid_generate_v4(),  "sheetIndex" character varying NOT NULL,"old_propertyNumber" character varying NOT NULL, "survey_number" character varying NOT NULL, "streetName" character varying NOT NULL, "property_holder_name" character varying NOT NULL, "possession_holder_name" character varying NOT NULL, "ward" character varying NOT NULL, "house_tax" numeric NOT NULL, "electricity_bill" numeric NOT NULL, "health_tax" numeric NOT NULL, "padsar" numeric NOT NULL, "penalty_amount" numeric NOT NULL, "shasti_fee" character varying NOT NULL, "ghan_kachara" numeric NOT NULL, "total" numeric NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_9d59bcd24ad9fa3d22319a3e619" PRIMARY KEY ("dues_id"))`);
        // await queryRunner.query(`ALTER TABLE "tax_fy_records" DROP COLUMN "financial_year_id"`);
        // await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP COLUMN "tax_data"`);
        // await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD "tax_data" character varying`);
        // await queryRunner.query(`ALTER TABLE "warshikKarTax" DROP COLUMN "tax_data"`);
        // await queryRunner.query(`ALTER TABLE "warshikKarTax" ADD "tax_data" character varying`);
        // await queryRunner.query(`ALTER TABLE "property_owner_details" ADD CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" ADD CONSTRAINT "FK_63d04ce9d740ec67a2d24c0df82" FOREIGN KEY ("usage_type_id") REFERENCES "usage_type_master"("usage_type_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" DROP CONSTRAINT "FK_63d04ce9d740ec67a2d24c0df82"`);
        // await queryRunner.query(`ALTER TABLE "property_owner_details" DROP CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2"`);
        // await queryRunner.query(`ALTER TABLE "warshikKarTax" DROP COLUMN "tax_data"`);
        // await queryRunner.query(`ALTER TABLE "warshikKarTax" ADD "tax_data" json`);
        // await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP COLUMN "tax_data"`);
        // await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD "tax_data" json`);
        // await queryRunner.query(`ALTER TABLE "tax_fy_records" ADD "financial_year_id" uuid`);
        await queryRunner.query(`DROP TABLE "tax_pending_dues"`);
        // await queryRunner.query(`ALTER TABLE "tax_fy_records" ADD CONSTRAINT "FK_c9ce89ec90dc8f02667b203ab8c" FOREIGN KEY ("financial_year_id") REFERENCES "financial_year"("financial_year_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
