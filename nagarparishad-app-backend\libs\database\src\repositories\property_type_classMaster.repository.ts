import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Property_type_class_master } from '../entities';

@Injectable()
export class PropertyTypeClassMasterRepository extends Repository<Property_type_class_master> {
  constructor(
    @InjectRepository(Property_type_class_master)
    private readonly propertyTypeClassMasterRepository: Repository<Property_type_class_master>,
  ) {
    super(
      propertyTypeClassMasterRepository.target,
      propertyTypeClassMasterRepository.manager,
      propertyTypeClassMasterRepository.queryRunner,
    );
  }

  // Method to fetch all property type classes
  async findAll(): Promise<Property_type_class_master[]> {
    const allClasses = await this.propertyTypeClassMasterRepository.find(); // Using TypeORM's built-in find method to fetch all records
    return allClasses;
  }

  // Method to save a new property type class
  async saveData(propertyTypeClassData: Partial<Property_type_class_master>): Promise<Property_type_class_master> {
    const newClass = this.propertyTypeClassMasterRepository.create(propertyTypeClassData); // Create a new instance of the entity
    return await this.propertyTypeClassMasterRepository.save(newClass); // Save the entity to the database
  }

  // Method to find a property type class by ID using Query Builder
  async findById(id: string): Promise<Property_type_class_master | undefined> {
    const propertyTypeClass = await this.propertyTypeClassMasterRepository
      .createQueryBuilder('propertyTypeClass')
      .where('propertyTypeClass.property_type_class_id = :id', { id }) // Assuming 'id' is the primary key field in the entity
      .getOne(); // Fetch a single record

    return propertyTypeClass; // This will return the entity if found, otherwise undefined
  }
}
