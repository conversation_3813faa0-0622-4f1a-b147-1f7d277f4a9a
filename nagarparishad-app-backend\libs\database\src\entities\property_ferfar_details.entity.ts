import {
  BaseEntity,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PropertyEntity } from './property.entity';
import { ReassessmentRange } from './reassesment_range.entity';

@Entity('property_ferfar_details')
export class Property_Ferfar_Detail_Entity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  property_ferfar_detail_id: string;

  // Foreign key to PropertyEntity
  @ManyToOne(() => PropertyEntity, (property) => property.property_id, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

  // Foreign key to ReassessmentRange
  @ManyToOne(() => ReassessmentRange, (reassessmentRange) => reassessmentRange.reassessment_range_id, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'reassessment_range_id' })
  reassessmentRange: ReassessmentRange;

  // Column to store an array of previous owner names
  @Column({ type: 'varchar', array: true, nullable: false })
  previous_owner_names: string[];

  // Column for reason or share details
  @Column({ type: 'text', nullable: true })
  reason: string;

  // Column for image folder path
  @Column({ type: 'varchar', array: true, nullable: true })
  photo_image_paths: string[];

  // Column for document image path (single path or array of paths)
  @Column({ type: 'varchar', nullable: true })
  document_image_path: string[];

  @Column({ type: 'varchar', nullable: true })
  user_email_id: string[];

  // Column for year range like "2024-2025"
  @Column({ type: 'varchar', nullable: false })
  year: string;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
