// src/rr-rate-master/dto/update-rr-rate.dto.ts
import { IsString, IsNumber, IsUUID, IsOptional, IsIn } from 'class-validator';

export class UpdateRrRateDto {
  @IsString()
  @IsOptional()
  financial_year?: string; // Example: "2024-2025", can be updated or kept the same

  @IsString()
  @IsOptional()
  reassessment_range_id?: string; // Reassessment range ID

  @IsNumber()
  @IsOptional()
  value?: number; // New value for RR rate

  @IsString()
  @IsOptional()
  @IsIn(['Active', 'Inactive'])
  status?: string; // Status can be updated to 'Active' or 'Inactive'

  @IsUUID()
  @IsOptional()
  zone_id?: string; // Optional for updating existing RR rate
}
