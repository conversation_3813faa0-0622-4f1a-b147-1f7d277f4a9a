import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OfflineNotificationEntity } from 'libs/database/entities';
import { Repository, LessThan, In, Brackets } from 'typeorm';

@Injectable()
export class OfflineNotificationRepository {
  constructor(
    @InjectRepository(OfflineNotificationEntity)
    private readonly repository: Repository<OfflineNotificationEntity>,
  ) {}

  async create(data: Partial<OfflineNotificationEntity>): Promise<OfflineNotificationEntity> {
    const notification = this.repository.create(data);
    return await this.repository.save(notification);
  }

  async findByUserId(userId: string): Promise<OfflineNotificationEntity[]> {
    return await this.repository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async findByNotificationId(notificationId: string): Promise<OfflineNotificationEntity | null> {
    return await this.repository.findOne({
      where: { notificationId },
    });
  }
async findALLByUserId(userId: string, includeRead: boolean = false): Promise<OfflineNotificationEntity[]> {
  const query = this.repository.createQueryBuilder('n')
    .where('n.user_id = :userId', { userId })
    .andWhere(
      new Brackets(qb => {
        qb.where('n.status IN (:...completedOrFailed)', { completedOrFailed: ['completed', 'failed','delivered'] })
          .orWhere('n.status = :pending', { pending: 'pending' });
      })
    );

  // For 'pending' status, optionally filter isRead
  if (!includeRead) {
    query.andWhere(
      new Brackets(qb => {
        qb.where('(n.status != :pending)').orWhere('(n.status = :pending AND n.is_read = false)', { pending: 'pending' });
      })
    );
  }

  return query
    .orderBy('n.created_at', 'DESC')
    .getMany();
}

  async updateStatus(
    notificationId: string, 
    status: OfflineNotificationEntity['status'],
    additionalData?: Partial<OfflineNotificationEntity>
  ): Promise<void> {
    await this.repository.update(
      { notificationId },
      { status, ...additionalData }
    );
  }

  async markAsDelivered(notificationId: string): Promise<void> {
    await this.repository.update(
      { notificationId },
      { 
        status: 'delivered',
        deliveredAt: new Date()
      }
    );
  }

  async incrementDownloadCount(notificationId: string): Promise<void> {
    await this.repository.increment(
      { notificationId },
      'downloadCount',
      1
    );
    
    await this.repository.update(
      { notificationId },
      { isDownloaded: true }
    );
  }

  async findExpiredNotifications(): Promise<OfflineNotificationEntity[]> {
    return await this.repository.find({
      where: {
        expiresAt: LessThan(new Date()),
        status: 'completed'
      },
    });
  }

  async deleteExpiredNotifications(): Promise<void> {
    const expiredNotifications = await this.findExpiredNotifications();
    if (expiredNotifications.length > 0) {
      await this.repository.remove(expiredNotifications);
    }
  }

  async findById(id: string): Promise<OfflineNotificationEntity | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  async markAsRead(notificationId: string): Promise<void> {
    await this.repository.update(
      { notificationId },
      { isRead: true }
    );
  }
}
