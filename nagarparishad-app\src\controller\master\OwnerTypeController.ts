import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import Api from "../../services/ApiServices";  // Ensure this has the required methods for owner types


const fetchOwnerTypes = () => {
  return new Promise((resolve, reject) => {
    Api.getOwnerType((response) => {
      if (response.status && response.data.statusCode === 200) {
        console.log("owner types----",response)
        resolve(response.data.data);
      } else {
        reject(new Error("Error fetching owner types"));
      }
    });
  });
};

// const createOwnerType = async (ownerData) => {
//   return new Promise((resolve, reject) => {
//     Api.createOwnerType(ownerData, (response) => {
//       if (response.status) {
//         resolve(response.data);
//       } else {
//         reject(response.data);
//       }
//     });
//   });
// };

// const updateOwnerType = async ({ ownerId, ownerData }) => {
//   return new Promise((resolve, reject) => {
//     Api.updateOwnerType(ownerId, ownerData, (response) => {
//       if (response.status) {
//         resolve(response.data);
//       } else {
//         reject(response.data);
//       }
//     });
//   });
// };

// const deleteOwnerType = async (ownerId) => {
//   return new Promise((resolve, reject) => {
//     Api.deleteOwnerType(ownerId, (response) => {
//       if (response.status) {
//         resolve(response.data);
//       } else {
//         reject(response.data);
//       }
//     });
//   });
// };

export const useOwnerTypeController = () => {
  const queryClient = useQueryClient();

  const { data: ownerTypeData } = useQuery({
    queryKey: ["ownertypemaster"],
    queryFn: fetchOwnerTypes,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
    select: (data :any) => 
        data?.map((ownerType) => ({
          owner_type_id: ownerType.owner_type_id,
          owner_type: ownerType.owner_type,
        })),
  });

//   const createOwnerTypeMutation = useMutation({
//     mutationFn: createOwnerType,
//     onMutate: async (newOwnerType) => {
//       await queryClient.cancelQueries({ queryKey: ["ownertypemaster"] });

//       const previousOwnerTypes = queryClient.getQueryData(["ownertypemaster"]);
//       queryClient.setQueryData(["ownertypemaster"], (old) => {
//         const updatedData = [newOwnerType, ...old];
//         return updatedData;
//       });

//       return { previousOwnerTypes };
//     },
//     onError: (err, newOwnerType, context) => {
//       queryClient.setQueryData(["ownertypemaster"], context.previousOwnerTypes);
//       console.error("Error creating owner type:", err);
//     },
//     onSettled: () => {
//       queryClient.invalidateQueries({ queryKey: ["ownertypemaster"] });
//     },
//   });

//   const updateOwnerTypeMutation = useMutation({
//     mutationFn: updateOwnerType,
//     onMutate: async ({ ownerId, ownerData }) => {
//       await queryClient.cancelQueries({ queryKey: ["ownertypemaster"] });

//       const previousOwnerTypes = queryClient.getQueryData(["ownertypemaster"]);

//       queryClient.setQueryData(["ownertypemaster"], (old) => {
//         const updatedOwnerTypes = old.map((ownerType) =>
//           ownerType.owner_id === ownerId
//             ? { ...ownerType, ...ownerData }
//             : ownerType,
//         );
//         return updatedOwnerTypes;
//       });

//       return { previousOwnerTypes };
//     },
//     onError: (err, { ownerId, ownerData }, context) => {
//       queryClient.setQueryData(["ownertypemaster"], context.previousOwnerTypes);
//       console.error("Error updating owner type:", err);
//     },
//     onSettled: () => {
//       queryClient.invalidateQueries({ queryKey: ["ownertypemaster"] });
//     },
//   });

//   const deleteOwnerTypeMutation = useMutation({
//     mutationFn: deleteOwnerType,
//     onMutate: async (ownerId) => {
//       await queryClient.cancelQueries({ queryKey: ["ownertypemaster"] });

//       const previousOwnerTypes = queryClient.getQueryData(["ownertypemaster"]);
//       queryClient.setQueryData(["ownertypemaster"], (old) => {
//         const updatedOwnerTypes = old.filter(
//           (ownerType) => ownerType.owner_id !== ownerId,
//         );
//         return updatedOwnerTypes;
//       });

//       return { previousOwnerTypes };
//     },
//     onError: (err, ownerId, context) => {
//       queryClient.setQueryData(["ownertypemaster"], context.previousOwnerTypes);
//       console.error("Error deleting owner type:", err);
//     },
//     onSettled: () => {
//       queryClient.invalidateQueries({ queryKey: ["ownertypemaster"] });
//     },
//   });

  return {
    ownerTypeList: ownerTypeData || [],
 
  };
};
