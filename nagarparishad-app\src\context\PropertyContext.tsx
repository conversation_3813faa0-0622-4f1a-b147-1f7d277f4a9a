import { PropertyRegistrationInterface } from "@/model/propertyregistration-master";
import React, { useState, createContext } from "react";

type PropertyContext = {
  propertyInformation: PropertyRegistrationInterface;
  setPropertyInformation: React.Dispatch<
    React.SetStateAction<PropertyRegistrationInterface>
  >;
  initialPropertyInformation: PropertyRegistrationInterface;
  propertyNumber: string;
  setPropertyNumber: (T: string) => void;
};

const initialPropertyInformation: PropertyRegistrationInterface = {
  PropertyLocationDetails:{
    gat_number: "",  // New property added
    property_old_number: "",  // Optional property
    property_master_id: "",  // Optional property
    building_permission_number: "",
    city_survey_number: "",
    property_status: "0",  // Assuming 0 as a string
    plot_number: "",
    block_number: "",
    house_number: "",
    location: "",
    house_or_apartment_name: "",
    street: {
      street_id: "",    // Provide actual street ID
      street_name: ""   // Provide actual street name
    },
    landmark: "",
    country: "",
    city: "",
    latitude: "",
    longitude: "",
    ward: {
      ward_id: "",      // Provide actual ward ID
      ward_name: ""     // Provide actual ward name
    },
    register: null,
    zone: {
      zone_id: "",      // Provide actual zone ID
      zoneName: ""      // Provide actual zone name
    },
    adminstrativeBoundary: "",
    electionBoundary: "",
    uploaded_files: [],
    sequence_no: "",
    property_desc: "",
    area: "",
  },
  
  
  AssessmentDetailsForm: {
    firstname: "",
    middlename: "",
    lastname: "",
    organization: "",
    mobile_number: "",
    email_id: "",
    aadhar_number: "",
    pan_card: "",
    gender: "",
    owner_type: ".",
  },
   PropertyAssessmentDetailsForm:{
    property_usage_details_id: "", // Required, empty string
    construction_area: null, // Allowing null
    length: null, // Setting to null as no specific value provided
    width: null, // Setting to null as no specific value provided
    are_sq_ft: null, // Setting to null as no specific value provided
    are_sq_meter: null, // Setting to null as no specific value provided
    construction_start_date: null, // Allowing null, changed from specific date
    construction_end_date: null, // Allowing null
    Building_age: null, // Allowing null
    floor: null, // Allowing null
    flat_no: null, // Allowing null
    authorized: false, // Required, retaining original value
    propertyType: {
      propertyType_id: "", // Required, set to empty string
      propertyType: "", // Required, set to empty string
    },
    usageType: {
      usage_type_id: "", // Required, set to empty string
      usage_type: "", // Required, set to empty string
    },
    usageSubType: {
      usage_sub_type_master_id: "", // Required, set to empty string
      usage_sub_type: "", // Required, set to empty string
    }, // Changed from object to null as it's optional
    construction_year: null, // Setting to null as no specific value provided
    annual_rent: null, // Allowing null
    property_photographs: [], // Allowing empty array
    room_detail: "", // Allowing empty string
    remarks: "", // Allowing empty string
  },
  
  PlotDetailsForm :{
    GISID: "", // Geographic Information System ID, default as an empty string
    propertyDescription: "", // Description of the property, default as an empty string
    completionCertificate: null, // Completion certificate information, default as null
    accessRoad: "", // Information about the access road, default as an empty string
    individualToilet: "no", // Indicates whether there is an individual toilet, default as "no"
    toiletType: "", // Type of toilet (e.g., Indian, Western), default as an empty string
    totalNumber: '', // Total number of something (e.g., flats, units), default as 0
    lightingFacility: "", // Details about lighting facilities, default as an empty string
    tapConnection: null, // Tap connection information, default as null
    totalConnections: '', // Total number of connections (e.g., water, electricity), default as 0
    solarProject: "no", // Indicates whether there is a solar project, default as "no"
    rainWaterHarvesting: "no", // Indicates whether rainwater harvesting is implemented, default as "no"
    sewageSystem: "no", // Indicates whether there is a sewage system, default as "no"
    groundFloorArea: '', // Area of the ground floor, default as 0
    remainingGroundFloorArea: '', // Remaining area of the ground floor, default as 0
  }
  
  
};

export const PropertyContext = createContext({} as PropertyContext);

export const PropertyContextProvider = (props: any) => {
  const { children } = props;
  const [propertyInformation, setPropertyInformation] =
    useState<PropertyRegistrationInterface>(initialPropertyInformation);
  const [propertyNumber, setPropertyNumber] = useState<string>("");

  return (
    <PropertyContext.Provider
      value={{
        initialPropertyInformation,
        propertyInformation,
        setPropertyInformation,
        propertyNumber,
        setPropertyNumber,
      }}
    >
      {children}
    </PropertyContext.Provider>
  );
};
