import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { handleApiError, StatusCode } from './errorHandler';

// Standard response interface
export interface ApiResponse<T = any> {
  status: boolean;
  statusCode: StatusCode;
  message: string;
  data?: T;
  error?: any;
}

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = JSON.parse(localStorage.getItem('AccessToken') || '""');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for standard error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle token expiration
    if (error.response?.status === StatusCode.UNAUTHORIZED) {
      // Clear local storage and redirect to login
      localStorage.removeItem('AccessToken');
      localStorage.removeItem('RefreshToken');
      localStorage.removeItem('UserData');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Generic API request function
export const apiRequest = async <T>(
  method: string,
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  try {
    let response: AxiosResponse;
    
    switch (method.toLowerCase()) {
      case 'get':
        response = await apiClient.get(url, config);
        break;
      case 'post':
        response = await apiClient.post(url, data, config);
        break;
      case 'put':
        response = await apiClient.put(url, data, config);
        break;
      case 'patch':
        response = await apiClient.patch(url, data, config);
        break;
      case 'delete':
        response = await apiClient.delete(url, config);
        break;
      default:
        throw new Error(`Unsupported method: ${method}`);
    }
    
    return {
      status: true,
      statusCode: response.data.statusCode || StatusCode.OK,
      message: response.data.message || 'Success',
      data: response.data.data
    };
  } catch (error) {
    const errorResponse = handleApiError(error);
    return {
      status: false,
      statusCode: errorResponse.statusCode,
      message: errorResponse.message,
      error: errorResponse
    };
  }
};

// Convenience methods
export const apiGet = <T>(url: string, config?: AxiosRequestConfig) => 
  apiRequest<T>('get', url, undefined, config);

export const apiPost = <T>(url: string, data?: any, config?: AxiosRequestConfig) => 
  apiRequest<T>('post', url, data, config);

export const apiPut = <T>(url: string, data?: any, config?: AxiosRequestConfig) => 
  apiRequest<T>('put', url, data, config);

export const apiPatch = <T>(url: string, data?: any, config?: AxiosRequestConfig) => 
  apiRequest<T>('patch', url, data, config);

export const apiDelete = <T>(url: string, config?: AxiosRequestConfig) => 
  apiRequest<T>('delete', url, undefined, config);