import { Controller, Get, Post, Put, Delete, Body, Query } from '@nestjs/common';
import { MasterGhanKachraRateService } from './masterGhanKachraRate.service';
import { Master_ghanKachra_rateEntity } from 'libs/database/entities';
import { CreateUsageSubTypeDto } from './dto/create-usage-sub-type.dto';
import { UpdateUsageSubTypeDto } from './dto/update-usage-sub-type.dto';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@Controller('master-ghanKachra-rate')
export class MasterGhanKachraRateController {
  constructor(private readonly masterGhanKachraRateService: MasterGhanKachraRateService) {}

  
  @Form('Ghan Kachra Rate')
  @Permissions('can_write')
  @Post('create')
  async create(
    @Body() createData: CreateUsageSubTypeDto
  ): Promise<{ message: string; data: Master_ghanKachra_rateEntity }> {
        return this.masterGhanKachraRateService.create(createData);
  }

  
  @Form('Ghan Kachra Rate')
  @Permissions('can_read')
  @Get()
  async findAll(): Promise<{ message: string; data: Master_ghanKachra_rateEntity[] }> {
    return this.masterGhanKachraRateService.findAll();
  }

  
  @Form('Ghan Kachra Rate')
  @Permissions('can_read')
  @Get('findOne')
  async findOne(@Query('id') id: string): Promise<{ message: string; data: Master_ghanKachra_rateEntity }> {
    return this.masterGhanKachraRateService.findOne(id);
  }

  
  @Form('Ghan Kachra Rate')
  @Permissions('can_update')
  @Put('update')
  async update(
    @Query('id') id: string,
    @Body() updateData: UpdateUsageSubTypeDto
  ): Promise<{ message: string; data: Master_ghanKachra_rateEntity }> {
   
    return this.masterGhanKachraRateService.update(id, updateData);
  }

  
  @Form('Ghan Kachra Rate')
  @Permissions('can_delete')
  @Delete('delete')
  async delete(@Query('id') id: string): Promise<{ message: string }> {
    return this.masterGhanKachraRateService.delete(id);
  }
}
