import { MigrationInterface, QueryRunner } from "typeorm";

export class Billdata1739094982159 implements MigrationInterface {
     name = 'Billdata1739094982159'

    public async up(queryRunner: QueryRunner): Promise<void> {
        const tableExists = await queryRunner.hasTable("billdata");

        if (!tableExists) {
            await queryRunner.query(`
                CREATE TABLE "billdata" (
                    "id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
                    "billNo" character varying(50) NOT NULL, 
                    "bill_generation_date" date NOT NULL DEFAULT now(), 
                    "property_id" uuid NOT NULL, 
                    "property_number" character varying(100) NOT NULL, 
                    "fyear" character varying(10) NOT NULL, 
                    CONSTRAINT "PK_billdata_id" PRIMARY KEY ("id")
                
                )
            `);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
           // Reverse the changes: drop the "billdata" table
           await queryRunner.query(`DROP TABLE "billdata"`);
    }

}
