export class CreateTaxPendingDuesDto {
  sheetIndex: string;

    old_propertyNumber: string;
    surveyNumber: string;
    streetName: string;
    propertyHolderName: string;
    possessionHolderName: string;
    ward: string;
    all_property_tax_sum: number;
    tax_type_1?: number;
    tax_type_2?: number;
    tax_type_3?: number;
    tax_type_4: number;
    tax_type_5?: number;
    tax_type_6: number;
    tax_type_7: number;
    tax_type_8: number;
    tax_type_9: number;
    tax_type_10?: number;
    total: number;
  }

  export class TaxPendingDuesDto {
    duesId: string;
  }

  export class UpdateTaxPendingDuesDto {
    sheetIndex: string;
    old_propertyNumber?: string;
    surveyNumber?: string;
    streetName?: string;
    propertyHolderName?: string;
    possessionHolderName?: string;
    ward?: string;
    all_property_tax_sum?: number;
    tax_type_1?: number;
    tax_type_2?: number;
    tax_type_3?: number;
    tax_type_4?: number;
    tax_type_5?: number;
    tax_type_6?: number;
    tax_type_7?: number;
    tax_type_8?: number;
    tax_type_9?: number;
    tax_type_10?: number;
    total?: number;
  }
