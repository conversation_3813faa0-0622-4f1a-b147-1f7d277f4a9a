import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePenaltyTable1746017767472 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the column 'value' exists
    const valueColumnExists = await queryRunner.hasColumn('penalty_fee_yearWise', 'value');

    if (valueColumnExists) {
      // Rename 'value' column to 'actual_value'
      await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" RENAME COLUMN "value" TO "actual_value"`);

      // Add 'penalty_value' column
      await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" ADD "penalty_value" numeric NOT NULL DEFAULT 0`);

      // Update penalty_value based on actual_value and tax_percentage
      // This ensures penalty_value is always calculated as actual_value * tax_percentage / 100
      // Even if actual_value is 0, penalty_value will be 0
  
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if the column 'actual_value' exists
    const actualValueColumnExists = await queryRunner.hasColumn('penalty_fee_yearWise', 'actual_value');

    if (actualValueColumnExists) {
      // Drop 'penalty_value' column
      await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" DROP COLUMN "penalty_value"`);

      // Rename 'actual_value' column back to 'value'
      await queryRunner.query(`ALTER TABLE "penalty_fee_yearWise" RENAME COLUMN "actual_value" TO "value"`);
    }
  }
}
