export interface TaxRateSetting   {
    rr_tax_id: string;
    financial_year?: string; // Optional for backward compatibility
    value: number;
    status: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    property_type: {
        propertyType_id: string;
        propertyType: string;
        createdAt: string;
        updatedAt: string;
        deletedAt: string | null;
    },
    reassessmentRange?: {
        reassessment_range_id: string;
        start_range: string;
        end_range: string;
    }
}

export interface TaxRateApi {
    statusCode: number;
    message: string;
    data: TaxRateSetting[];
  }

  export interface TaxRateCreateApi {
    statusCode: number;
    message: string;
    data: TaxRateSetting;
  }
  export interface TaxRateUpdateApi {
    financial_year?: string; // Optional for backward compatibility
    reassessment_range_id: string;
    value: number;
    status: string;
    propertyType_id: string;
    rr_tax_id:string;
  }

  export interface TaxRateSendApiObj {
    financial_year?: string; // Optional for backward compatibility
    reassessment_range_id: string;
    value: number;
    status: string;
    propertyType_id: string;
  }
