export interface ReactselectInterface {
  value: string;
  label: string;
}

export interface Payment {
  id: string;
  propetyNumber: string;
  ownerName: string;
  taxPending: string;
}

export interface PropertyDetailsInterface {
  currentStep: number;
  handleNextStep: () => void;
  handlePreviousStep: () => void;
}

export interface UserData {
  firstName: string;
  lastName: string;
  email: string;
  role: number;
}
