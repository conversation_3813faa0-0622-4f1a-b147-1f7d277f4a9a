import { Repository } from 'typeorm';
import { PreviousOwnerEntity, ReassessmentRange } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { ReassessmentRangeRepository } from './reassesment.repository';

export class PreviousOwnerRepository extends Repository<PreviousOwnerEntity> {
  constructor(
    @InjectRepository(PreviousOwnerEntity)
    private readonly previousOwnerRepository: Repository<PreviousOwnerEntity>,
    private readonly reassessmentRangeRepository: ReassessmentRangeRepository,
  ) {
    super(
      previousOwnerRepository.target,
      previousOwnerRepository.manager,
      previousOwnerRepository.queryRunner,
    );
  }

  async getCurrentReassessmentRange(): Promise<ReassessmentRange | null> {
    return await this.reassessmentRangeRepository.getCurrentReassesmentRange();
  }

  async saveData(input: Partial<PreviousOwnerEntity>): Promise<PreviousOwnerEntity> {
    // Get the current reassessment range if not provided
    if (!input.reassessmentRange) {
      const currentReassessmentRange = await this.getCurrentReassessmentRange();
      if (currentReassessmentRange) {
        input.reassessmentRange = currentReassessmentRange;
      }
    }

    let data = this.previousOwnerRepository.create(input);
    data = await this.previousOwnerRepository.save(data);
    return data;
  }

  async findAllData(): Promise<PreviousOwnerEntity[]> {
    return await this.previousOwnerRepository
      .createQueryBuilder('previous_owner')
      .leftJoinAndSelect('previous_owner.property', 'property')
      .leftJoinAndSelect('previous_owner.owner_type', 'owner_type')
      .orderBy('previous_owner.createdAt', 'ASC')
      .getMany();
  }

  async findById(id: string): Promise<PreviousOwnerEntity | null> {
    return await this.previousOwnerRepository
      .createQueryBuilder('previous_owner')
      .where('previous_owner.previous_owner_id = :previous_owner_id', {
        previous_owner_id: id,
      })
      .getOne();
  }

  async findByName(name: string): Promise<PreviousOwnerEntity[]> {
    return await this.previousOwnerRepository
      .createQueryBuilder('previous_owner')
      .where('previous_owner.name = :name', { name })
      .getMany();
  }

  async findByProperty(property_id: string): Promise<PreviousOwnerEntity[]> {
    return await this.previousOwnerRepository
      .createQueryBuilder('previous_owner')
      .leftJoinAndSelect('previous_owner.owner_type', 'owner_type')
      .where('previous_owner.property_id = :property_id', { property_id })
      .getMany();
  }

  async deleteOldOwners(): Promise<void> {
    const fiveYearsAgo = new Date();
    fiveYearsAgo.setFullYear(fiveYearsAgo.getFullYear() - 5);

    await this.previousOwnerRepository
      .createQueryBuilder('previous_owner')
      .delete()
      .where('created_at < :fiveYearsAgo', { fiveYearsAgo })
      .execute();
  }
}
