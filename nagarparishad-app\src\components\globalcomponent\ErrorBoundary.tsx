import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render shows the fallback UI.
    return { hasError: true };
  }

  componentDidCatch(error, info) {
    // Log error to an error reporting service if needed
    console.error("Error caught by <PERSON><PERSON><PERSON> Boundary:", error, info);
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      return (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          {/* <h1>Oops! Something went wrong.</h1> */}
          <p>आम्ही या समस्येचे निराकरण करण्याचे काम करत आहोत. कृपया नंतर पुन्हा प्रयत्न करा.</p>
          <button className="bg-transparent hover:bg-blue-500 text-blue-700 font-semibold hover:text-white py-2 px-4 border border-blue-500 hover:border-transparent rounded" onClick={() => window.location.reload()}>Refresh</button>
        </div>
      );
    }

    return this.props.children; 
  }
}

export default ErrorBoundary;
