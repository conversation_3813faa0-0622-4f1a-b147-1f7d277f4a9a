import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,  <PERSON>Optional, IsDate, <PERSON><PERSON>teIf, IsBoolean  } from 'class-validator';

export class PropertyUsageDetailsDto {
  @IsString()
  @IsOptional()
  propertyType_id: string;

  @IsString()
  @IsOptional()
  usage_type_id: string;

  @IsString()
  @IsOptional()
  usage_sub_type_master_id: string;

  @IsString()
  @IsOptional()
  property_type_desc: string;

  @IsNumber()
  @IsOptional()
  construction_area: number;

  @IsNumber()
  @IsOptional()
  length: number;

  @IsNumber()
  @IsOptional()
  width: number;

  @IsNumber()
  @IsOptional()
  are_sq_ft: number;

  @IsNumber()
  @IsOptional()
  are_sq_meter: number;

  @IsString()
  @IsOptional()
  floor_id: string;

  @IsString()
  @IsOptional()
  flat_no: string;

  @IsOptional()
  @IsNumber()
  annual_rent: number;

  @IsOptional()
  @IsString()
  construction_start_date: string | Date | null; // Allowing null

  @IsOptional()
  @IsString()
  construction_end_date: string | Date | null; // Allowing null

  @IsOptional()
  @IsString()
  construction_start_year: string | null; // Allowing null

  @IsBoolean() 
  @IsOptional()
  authorized?: boolean | string; // Change to boolean type
  @IsString()
  @IsOptional()
  remark: string;

  @IsString()
  @IsOptional()
  tapshil: string;
}
