import PaidDataApi from "../../services/PaidDataServices";
import Api from "../../services/ApiServices";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";

// Function to fetch paid users data
const fetchPaidUsersData = async (params: {
  page: number;
  limit: number;
  searchValue?: string;
  searchOn?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}) => {
  const { page, limit, searchValue, searchOn, sortBy, sortOrder } = params;
  return await PaidDataApi.getPaidUsersData(
    page,
    limit,
    searchValue,
    searchOn,

  );
};

// Function to fetch non-paid users data
const fetchNonPaidUsersData = async (params: {
  page: number;
  limit: number;
  searchValue?: string;
  searchOn?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}) => {
  const { page, limit, searchValue, searchOn, sortBy, sortOrder } = params;
  return await PaidDataApi.getNonPaidUsersData(
    page,
    limit,
    searchValue,
    searchOn,
    sortBy,
    sortOrder
  );
};

// Function to fetch all paid and non-paid data
const fetchAllPaidAndNonPaidData = async (params: {
  page: number;
  limit: number;
  searchValue?: string;
  searchOn?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}) => {
  const { page, limit, searchValue, searchOn, sortBy, sortOrder } = params;
  return await PaidDataApi.getPaidAndNonPaidData(
    page,
    limit,
    searchValue,
    searchOn,
    sortBy,
    sortOrder
  );
};

// Function to generate paid and non-paid data
const generatePaidAndNonPaidData = async (financialYearRange: string) => {
  return new Promise((resolve, reject) => {
    PaidDataApi.generatePaidAndNonPaidData(financialYearRange, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const usePaidDataController = () => {
  const queryClient = useQueryClient();
  
  // State for pagination and search
  const [paidPage, setPaidPage] = useState(1);
  const [nonPaidPage, setNonPaidPage] = useState(1);
  const [paidLimit, setPaidLimit] = useState(10);
  const [nonPaidLimit, setNonPaidLimit] = useState(10);
  const [searchValue, setSearchValue] = useState<string>("");
  const [searchOn, setSearchOn] = useState<string>("property_number");
  const [sortBy, setSortBy] = useState<string>("updatedAt");
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('DESC');

  // Query for paid users data
  const {
    data: paidUsersData,
    isLoading: paidUsersLoading,
    refetch: refetchPaidUsers,
  } = useQuery({
    queryKey: ["paidUsers", paidPage, paidLimit, searchValue, searchOn, sortBy, sortOrder],
    queryFn: () =>
      fetchPaidUsersData({
        page: paidPage,
        limit: paidLimit,
        searchValue,
        searchOn,
        sortBy,
        sortOrder,
      }),
    staleTime: 1000 * 60 * 2, // 2 minutes
    enabled: true,
  });

  // Query for non-paid users data
  const {
    data: nonPaidUsersData,
    isLoading: nonPaidUsersLoading,
    refetch: refetchNonPaidUsers,
  } = useQuery({
    queryKey: ["nonPaidUsers", nonPaidPage, nonPaidLimit, searchValue, searchOn, sortBy, sortOrder],
    queryFn: () =>
      fetchNonPaidUsersData({
        page: nonPaidPage,
        limit: nonPaidLimit,
        searchValue,
        searchOn,
        sortBy,
        sortOrder,
      }),
    staleTime: 1000 * 60 * 2, // 2 minutes
    enabled: true,
  });

  // Query for all paid and non-paid data
  const {
    data: allPaidAndNonPaidData,
    isLoading: allDataLoading,
    refetch: refetchAllData,
  } = useQuery({
    queryKey: ["allPaidAndNonPaidData", paidPage, paidLimit, searchValue, searchOn, sortBy, sortOrder],
    queryFn: () =>
      fetchAllPaidAndNonPaidData({
        page: paidPage,
        limit: paidLimit,
        searchValue,
        searchOn,
        sortBy,
        sortOrder,
      }),
    staleTime: 1000 * 60 * 2, // 2 minutes
    enabled: false, // Only fetch when needed
  });



  // Mutation for generating paid and non-paid data
  const generateDataMutation = useMutation({
    mutationFn: generatePaidAndNonPaidData,
    onSuccess: () => {
      // Invalidate and refetch all related queries
      queryClient.invalidateQueries({ queryKey: ["paidUsers"] });
      queryClient.invalidateQueries({ queryKey: ["nonPaidUsers"] });
      queryClient.invalidateQueries({ queryKey: ["allPaidAndNonPaidData"] });
    },
  });

  // Helper functions


  const handleSearchValueChange = (value: string) => {
    console.log("valuee in con",value)
    setSearchValue(value);
    setPaidPage(1); // Reset paid page to first page when search value changes
    setNonPaidPage(1); // Reset non-paid page to first page when search value changes
  };

  const handleSortChange = (field: string, order: 'ASC' | 'DESC') => {
    setSortBy(field);
    setSortOrder(order);
    setPaidPage(1); // Reset paid page to first page when sort changes
    setNonPaidPage(1); // Reset non-paid page to first page when sort changes
  };

  // Handle pagination for different tabs
  const handlePagination = (tab: 'paid' | 'nonpaid', newPage: number) => {
    if (tab === 'paid') {
      setPaidPage(newPage);
    } else {
      setNonPaidPage(newPage);
    }
  };

  // Handle limit change for different tabs
  const handleLimitChange = (tab: 'paid' | 'nonpaid', newLimit: number) => {
    if (tab === 'paid') {
      setPaidLimit(newLimit);
      setPaidPage(1); // Reset to first page when changing limit
    } else {
      setNonPaidLimit(newLimit);
      setNonPaidPage(1); // Reset to first page when changing limit
    }
  };

  // Extract pagination info from the data
  const paidUsersPagination = {
    page: paidUsersData?.page || paidPage,
    limit: paidUsersData?.limit || paidLimit,
    totalPages: paidUsersData?.totalPages || 1,
    totalRecords: paidUsersData?.total || 0,
    setPage: (page: number) => setPaidPage(page),
    setLimit: (limit: number) => handleLimitChange('paid', limit),
  };

  const nonPaidUsersPagination = {
    page: nonPaidUsersData?.page || nonPaidPage,
    limit: nonPaidUsersData?.limit || nonPaidLimit,
    totalPages: nonPaidUsersData?.totalPages || 1,
    totalRecords: nonPaidUsersData?.total || 0,
    setPage: (page: number) => setNonPaidPage(page),
    setLimit: (limit: number) => handleLimitChange('nonpaid', limit),
  };

  return {
    // Data
    paidUsers: paidUsersData?.data || [],
    nonPaidUsers: nonPaidUsersData?.data || [],
    allPaidAndNonPaidData: allPaidAndNonPaidData?.data || [],
    paidUsersData,
    nonPaidUsersData,
    // Loading states
    paidUsersLoading,
    nonPaidUsersLoading,
    allDataLoading,
    isGeneratingData: generateDataMutation.isPending,

    // Pagination
    paidUsersPagination,
    nonPaidUsersPagination,

    // Search and filter states
    searchValue,
    searchOn,
    sortBy,
    sortOrder,

    // Actions
    handleSearchValueChange,
    handleSortChange,
    generateData: generateDataMutation.mutate,
    refetchPaidUsers,
    refetchNonPaidUsers,
    refetchAllData,
    handlePagination,
    handleLimitChange,
    setSearchOn,
    setSearchValue,
  };
};
