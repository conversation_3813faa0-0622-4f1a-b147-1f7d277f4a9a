import { MigrationInterface, QueryRunner } from "typeorm";

export class LogsTableCreate1726214145581 implements MigrationInterface {
    name = 'LogsTableCreate1726214145581'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the "logs" table already exists before creating it
        const tableExists = await queryRunner.hasTable("logs");
        
        if (!tableExists) {
            await queryRunner.query(`
                CREATE TABLE "logs" (
                    "id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
                    "logType" character varying(50) NOT NULL, 
                    "action" character varying(100) NOT NULL, 
                    "message" text, 
                    "data" json, 
                    "createdAt" TIMESTAMP NOT NULL DEFAULT now(), 
                    CONSTRAINT "PK_fb1b805f2f7795de79fa69340ba" PRIMARY KEY ("id")
                )
            `);
        }

        // Perform the rest of the migration (e.g., dropping a column)
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "systemPropertyId"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Reverse the changes: recreate the column and drop the "logs" table if necessary
        await queryRunner.query(`ALTER TABLE "import_property" ADD "systemPropertyId" character varying`);
        await queryRunner.query(`DROP TABLE "logs"`);
    }
}
