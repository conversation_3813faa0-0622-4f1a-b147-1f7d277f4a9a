import { Repository } from "typeorm";
import { WarshilKarTaxEntity } from "../entities";
import { InjectRepository } from "@nestjs/typeorm";


export class WarshikKarTaxRepository extends Repository<WarshilKarTaxEntity>{



    constructor(@InjectRepository(WarshilKarTaxEntity) private readonly warshikarTaxRepository: WarshikKarTaxRepository ){
        super(
            warshikarTaxRepository.target,
            warshikarTaxRepository.manager,
            warshikarTaxRepository.queryRunner,
        )
    }



    async saveData(input: any){
        let data = this.warshikarTaxRepository.create(input);
        data = await this.warshikarTaxRepository.save(data);

        return data;
    }

    async getDataInfoByTaxId(id: string) {
        return await this.warshikarTaxRepository
          .createQueryBuilder('warshikKarTax')
          .leftJoinAndSelect(
            'warshikKarTax.property_usage_details',
            'property_usage_details',
          )
          .select([
            'warshikKarTax',
            'property_usage_details.property_usage_details_id',
          ])
          .where('warshikKarTax.warshik_karTaxId = :warshik_karTaxId', { warshik_karTaxId: id })
          .getOne();
      }
    
      async getAll() {
        return await this.warshikarTaxRepository
          .createQueryBuilder('warshikKarTax')
          .leftJoinAndSelect(
            'warshikKarTax.property_usage_details',
            'property_usage_details'
          )
          .select([
            'warshikKarTax',
            'property_usage_details.property_usage_details_id',
          ])
          .getMany(); 
      }  
    

}