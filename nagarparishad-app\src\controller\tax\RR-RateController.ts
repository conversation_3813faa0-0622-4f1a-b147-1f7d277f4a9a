import { ZoneObjectInterface } from "@/model/zone-master";
import Api from "../../services/ApiServices";
import {
  useQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import TaxListApi from "@/services/TaxServices";

interface ZoneData {
  zoneName: string; // Only zoneName now
}

const fetchRR_Rate = async () => {
    return new Promise((resolve, reject) => {
      TaxListApi.getRR_Rate((response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      });
    });
  };
  

const createRR_Rate = async (RR_RateData) => {
  return new Promise((resolve, reject) => {
    TaxListApi.createRR_Rate(RR_RateData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateRR_Rate = async ({ id, RR_RateData }: { id: string; RR_RateData: any }) => {
  return new Promise((resolve, reject) => {
    TaxListApi.updateRR_Rate(id, RR_RateData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteRR_Rate = async (id: string) => {
  return new Promise((resolve, reject) => {
    TaxListApi.deleteRR_Rate(id, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useRR_RateController = () => {
  const queryClient = useQueryClient();

  const { data: RR_RateData, isLoading: propertyLoading } = useQuery({
    queryKey: ["RR_Ratemaster"],
    queryFn: fetchRR_Rate,
    staleTime: 10 * 60 * 2, // 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
  });

  // const createRR_RateMutation = useMutation({
  //   mutationFn: (data) => TaxListApi.createRR_Rate(data),
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["RR_Ratemaster"] });
  //   },
  // });
  const createRR_RateMutation = useMutation({
    mutationFn: createRR_Rate,
    onMutate: async (newconstructionRates) => {
      await queryClient.cancelQueries({ queryKey: ["RR_Ratemaster"] });
      const previousconstructionRates = queryClient.getQueryData(["RR_Ratemaster"]);

      queryClient.setQueryData(["RR_Ratemaster"], (old: any) => {
             console.log(" construtionold",old)
        const updatedData = [newconstructionRates, ...old];
        console.log("updatedData",updatedData)
        return updatedData;
      });

      return { previousconstructionRates };
    }, 
     onError: (err, newconstructionRates, context) => {
      queryClient.setQueryData(["RR_Ratemaster"], context.previousconstructionRates);
      console.error("Error creating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["RR_Ratemaster"] });
    },
  });

  // const updateRR_RateMutation = useMutation({
  //   mutationFn: (data) => TaxListApi.updateRR_Rate(data),
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["RR_Ratemaster"] });
  //   },
  // });
  const updateRR_RateMutation = useMutation({
    mutationFn: updateRR_Rate,

    onMutate: async ({ id, RR_RateData }) => {
      await queryClient.cancelQueries({ queryKey: ["RR_Ratemaster"] });

      const previousWards = queryClient.getQueryData(["RR_Ratemaster"]);
      queryClient.setQueryData(["RR_Ratemaster"], (old: any) => {
        const updatedWards = old?.data?.map((constructionRate: any) =>
          constructionRate.rr_rate_id === id ? { ...constructionRate, ...RR_RateData } : constructionRate,
        );
        console.log("Updated query data:", updatedWards);
        return updatedWards;
      });

      return { previousWards };
    },
    onError: (err, { id, RR_RateData }, context) => {
      queryClient.setQueryData(["RR_Ratemaster"], context.previousWards);
      console.error("Error updating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["RR_Ratemaster"] });
    },
  });
  // const deleteRR_RateMutation = useMutation({
  //   mutationFn: (id: string) => TaxListApi.deleteRR_Rate(id),
  //   onSuccess: () => {
  //     queryClient.invalidateQueries({ queryKey: ["RR_Ratemaster"] });
  //   },
  // });
  const deleteRR_RateMutation = useMutation({
    mutationFn: deleteRR_Rate,
    onMutate: async (constructionRateId) => {
      await queryClient.cancelQueries({ queryKey: ["RR_Ratemaster"] });

      const previousConstructionRate = queryClient.getQueryData(["RR_Ratemaster"]);

      queryClient.setQueryData(["RR_Ratemaster"], (old: any) => {
        const updatedConstructionRate = old.data.filter((constructionRate: any) => constructionRate.rr_rate_id !== constructionRateId);
        return updatedConstructionRate;
      });

      return { previousConstructionRate };
    },
    onError: (err, ward_id, context) => {
      queryClient.setQueryData(["RR_Ratemaster"], context.previousConstructionRate);
      console.error("Error deleting ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["RR_Ratemaster"] });
    },
  });
  return {
    constructionRateList: RR_RateData || [],
    propertyLoading,
    createRR_Rate: createRR_RateMutation.mutate,
    updateRR_Rate: updateRR_RateMutation.mutate,
    deleteRR_Rate: deleteRR_RateMutation.mutate,
  };
};


