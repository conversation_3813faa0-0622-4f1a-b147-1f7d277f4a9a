import { Controller, Get, Post, Put, Delete, Query, Body } from '@nestjs/common';
import { Master_tax_valueService } from './master-tax-value.service';
import { Master_tax_rateEntity } from 'libs/database/entities';
import { CreateTaxValueDto } from './dto/create-tax-value.dto';
import { UpdateTaxValueDto } from './dto/update-tax-value.dto';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@Controller('master-tax-value')
export class Master_tax_valueController {
  constructor(private readonly taxValueService: Master_tax_valueService) {}

  
  @Form('Tax Rate')
  @Permissions('can_write')
  @Post('create')
  async create(
    @Body() data: CreateTaxValueDto
  ): Promise<{ message: string; data: Master_tax_rateEntity[] }> {
    const savedTaxRate = await this.taxValueService.create(data);
    return {
      message: 'Tax rate created successfully',
      data: savedTaxRate.data,
    };
  }

  
  @Form('Tax Rate')
  @Permissions('can_read')
  @Get()
  async findAll(): Promise<{ message: string; data: Master_tax_rateEntity[] }> {
    const taxRates = await this.taxValueService.findAll();
    return {
      message: 'All tax rates retrieved successfully',
      data: taxRates.data,
    };
  }

  // @Get('findOne')
  // async findOne(
  //   @Query('id') id: string
  // ): Promise<{ message: string; data: Master_tax_rateEntity }> {
  //   const taxRate = await this.taxValueService.findOne(id);
  //   return {
  //     message: 'Tax rate retrieved successfully',
  //     data: taxRate,
  //   };
  // }

  
  @Form('Tax Rate')
  @Permissions('can_update')
  @Put('update')
  async update(
    @Query('id') id: string,
    @Body() data: UpdateTaxValueDto
  ): Promise<{ message: string; data: Master_tax_rateEntity }> {
    const updatedTaxRate = await this.taxValueService.update(id, data);
    return {
      message: 'Tax rate updated successfully',
      data: updatedTaxRate.data,
    };
  }

  
  @Form('Tax Rate')
  @Permissions('can_delete')
  @Delete('delete')
  async delete(@Query('id') id: string): Promise<{ message: string }> {
    await this.taxValueService.delete(id);
    return {
      message: 'Tax rate deleted successfully',
    };
  }
}
