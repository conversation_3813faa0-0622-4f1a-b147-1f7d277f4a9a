import { Injectable } from '@nestjs/common';
import { In, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { BookNumberMasterEntity } from '../entities';
import { v4 as uuidv4 } from 'uuid'; // Import uuid correctly


export enum ReceiptStatus {
    MADE_AVAILABLE = 'MADE_AVAILABLE',
    MADE_IN_USE = 'MADE_IN_USE',
}

@Injectable()
export class BookNumberMasterRepository {
    constructor(
        @InjectRepository(BookNumberMasterEntity)
        private readonly bookNumberRepo: Repository<BookNumberMasterEntity>,
    ) {}

    async createBookNumber(bookNumber: number, availableReceipts: Set<number>): Promise<BookNumberMasterEntity> {
        const bookEntity = new BookNumberMasterEntity();
        bookEntity.id = uuidv4(); // Generate a new UUID

        bookEntity.book_number = bookNumber;
        bookEntity.availableReceipts = Array.from(availableReceipts);
        bookEntity.receiptsInUse = [];

        return await this.bookNumberRepo.save(bookEntity);
    }

    async getAvailableReceipts(bookNumber: number): Promise<Set<number>> {
        const bookEntity = await this.bookNumberRepo.findOne({
            where: { book_number: bookNumber },
        });

        return bookEntity ? new Set(bookEntity.availableReceipts) : new Set();
    }

    async getReceiptsInUse(bookNumber: number): Promise<Set<number>> {
        const bookEntity = await this.bookNumberRepo.findOne({
            where: { book_number: bookNumber },
        });

        return bookEntity ? new Set(bookEntity.receiptsInUse) : new Set();
    }

    async findOneBook(bookNumber: number): Promise<BookNumberMasterEntity | null> {
        return await this.bookNumberRepo.findOne({
            where: { book_number: bookNumber },
        });
    }

    async findAll(): Promise<{ book_number: number }[]> {
        return await this.bookNumberRepo
            .createQueryBuilder('bookNumberMasterEntity')
            .select(['bookNumberMasterEntity.book_number', 'bookNumberMasterEntity.id'])
            .leftJoinAndSelect('bookNumberMasterEntity.collector', 'collector')
            .getMany();
    }
    async deleteByNumber(bookNumber: any): Promise<void> {
        const bookEntity = await this.bookNumberRepo.findOne({
            where: { book_number: bookNumber },
        });
        const reciptInUse=await this.getReceiptsInUse(bookNumber);

        if(reciptInUse.size>0){
            throw new Error('Receipts are in use');
        }
        else{
                        await this.bookNumberRepo.softDelete(bookEntity.id);

        }
    }

    async findMany(bookIds: string[]): Promise<BookNumberMasterEntity[]> {
        if (!bookIds || bookIds.length === 0) {
          return []; 
        }
      
        const books = await this.bookNumberRepo
          .createQueryBuilder('book')
          .where('book.id IN (:...bookIds)', { bookIds })
          .getMany();
      
        return books;
      }
      
    
    async updateReceipts(bookNumber: number, receiptNumber: number, updatedTo: ReceiptStatus): Promise<void> {
        const bookEntity = await this.bookNumberRepo.findOne({ where: { book_number: bookNumber } });

        if (!bookEntity) {
            throw new Error(`Book number ${bookNumber} not found`);
        }

        if (updatedTo === ReceiptStatus.MADE_AVAILABLE) {
            bookEntity.receiptsInUse = bookEntity.receiptsInUse.filter(r => r !== receiptNumber);
            bookEntity.availableReceipts = [...new Set([...bookEntity.availableReceipts, receiptNumber])];
        } else if (updatedTo === ReceiptStatus.MADE_IN_USE) {
            bookEntity.availableReceipts = bookEntity.availableReceipts.filter(r => r !== receiptNumber);
            bookEntity.receiptsInUse = [...new Set([...bookEntity.receiptsInUse, receiptNumber])];
        }

        await this.bookNumberRepo.save(bookEntity);
    }
    async saveBookEntity(bookEntity: BookNumberMasterEntity): Promise<BookNumberMasterEntity> {
        return await this.bookNumberRepo.save(bookEntity);
    }

    async findByIds(ids: string[]): Promise<BookNumberMasterEntity[]> {
        return this.bookNumberRepo.find({
          where: {
            id: In(ids),
          },
        });
      }

      async update(criteria: any, partialEntity: any): Promise<void> {
        await this.bookNumberRepo.update(criteria, partialEntity);
      }
      
}
