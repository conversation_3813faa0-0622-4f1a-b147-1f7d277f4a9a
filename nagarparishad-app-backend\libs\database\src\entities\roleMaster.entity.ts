import {
  BaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('role_master')
export class RoleMasterEntity extends BaseEntity {
  @PrimaryGeneratedColumn()
  role_id: number;

  @Column({ name: 'role_name', type: 'varchar', nullable: false })
  roleName: string;

  @Column({ name: 'created_by', type: 'varchar', nullable: false })
  createdBy: string;

  /*
   * Create and Update Date Columns
   */
  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
