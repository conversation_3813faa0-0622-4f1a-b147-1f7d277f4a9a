import {
  Entity,
  PrimaryGeneratedColumn,
  OneToOne,
  JoinColumn,
  Column,
  Unique,
  CreateDateColumn,
} from 'typeorm';

@Entity('deleted_property')
@Unique(['property_id'])
export class DeletedPropertyEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', nullable: false })
  property_id: string;

  @Column({ type: 'jsonb', nullable: true })
  property: object;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;
}
