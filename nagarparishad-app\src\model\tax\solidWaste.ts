export interface solidWasteRateSetting {
        ghanKachra_rate_id:string,
        financial_year?:string, // Optional for backward compatibility
        value:number,
        status:string,
        createdAt:string,
        updatedAt:string,
        deletedAt:string|null,
        UsageSubType:{
            usage_sub_type_master_id:string,
            usage_sub_type: string,
            createdAt:string,
            updatedAt:string,
            deletedAt:string|null,
        },
        reassessmentRange?: {
            reassessment_range_id: string,
            start_range: string,
            end_range: string,
        }
    }



export interface SolidWasteRateApi {
    statusCode: number;
    message: string;
    data: solidWasteRateSetting[];
  }

  export interface SolidWasteRatemasterCreateApi {
    statusCode: number;
    message: string;
    data: solidWasteRateSetting;
  }
  export interface SolidWasteRatemasterUpdateApi {
    financial_year?: string; // Optional for backward compatibility
    reassessment_range_id: string;
    value: number;
    status: string;
    usage_sub_type_master_id: string;
    ghanKachra_rate_id;
  }

  export interface SolidWasteRatemasterSendApiObj {
    financial_year?: string; // Optional for backward compatibility
    reassessment_range_id: string;
    value: number;
    status: string;
    usage_sub_type_master_id: string;
    }
