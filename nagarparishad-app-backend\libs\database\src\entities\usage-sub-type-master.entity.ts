import {
  <PERSON><PERSON>ntity,
  <PERSON>umn,
  <PERSON>reateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UsageTypeMasterEntity } from './usage-type-master.entity';
import { Master_ghanKachra_rateEntity } from './master_ghanKachra_rate.entity';

@Entity('usage_sub_type_master')
export class UsageSubTypeMasterEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  usage_sub_type_master_id: string;

  @Column({ name: 'usage_sub_type_master', type: 'varchar', nullable: false })
  usage_sub_type: string;

  @ManyToOne(() => UsageTypeMasterEntity, (usageType) => usageType.usageSubTypes)
  @JoinColumn({ name: 'usage_type_id' })
  usageType: UsageTypeMasterEntity;

 


  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
