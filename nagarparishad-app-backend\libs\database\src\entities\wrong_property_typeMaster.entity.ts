import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
  } from 'typeorm';
  import { Ward_Master } from './ward_master.entity';
  
  @Entity('wrong_property_typeMaster')
  export class Wrong_property_typeMaster extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    Property_type_id: string;
  
    @Column({ type: String, name: 'Property_type', nullable: false })
    Property_type: string;
  
 
  
    /*
     * Create and Update Date Columns
     */
  
    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;
  
    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;
  
    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
  }
  