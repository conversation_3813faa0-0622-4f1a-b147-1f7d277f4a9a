import { BaseEntity, <PERSON>umn, CreateDateColumn, Entity, JoinColumn, ManyToMany, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { PropertyEntity } from "./property.entity";
import { BookNumberMasterEntity } from "./book_numberMaster.entity";
import { PaymentStatus } from "./payment_info.entity";

@Entity('backup_payment_details')
export class BackupPaymentDetailsEntity extends BaseEntity{
    @PrimaryGeneratedColumn()
    id: number;
    
    @Column( { type : "numeric", default: 0 })
    amount : number;

    @Column( { type : "varchar" , nullable: true})
    receipt_number : string;

    @Column( { type : "varchar", nullable: true})
    property_id : string;

    @Column( { type : "varchar" , nullable: true})
    book_number_id : string;

    @Column( { type : "timestamp"})
    payment_date : Date;

    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;

    @Column({ type: 'jsonb', nullable: true})
    payment_details: object;
}