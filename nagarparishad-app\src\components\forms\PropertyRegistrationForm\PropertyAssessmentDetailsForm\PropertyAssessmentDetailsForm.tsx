import React, { useState, useContext, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { z, object, string } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { usePropertytypeMasterController } from "@/controller/master/PropertyMasterController";
import { PropertytypeMasterObject } from "@/model/propertytype-master";
import {
  PropertyDetailsInterface,
  ReactselectInterface,
} from "@/model/global-master";
import { PropertysubtypeListObject } from "@/model/propertysubtype-master";
import { usePropertysubtypeMasterController } from "@/controller/master/PropertySubtypeMasterController";
import { useUsageMasterController } from "@/controller/master/UsageMasterController";
import { UsageObjectInterface } from "@/model/usage-master";
import { useUsageSubController } from "@/controller/master/UsageSubController";
import { UsageSubObjectInterface } from "@/model/usagesub-master";
import AsyncSelect from "@/components/ui/react-select";
import { Button } from "@/components/ui/button";
import { PropertyContext } from "@/context/PropertyContext";
import { PropertyRegistrationInterface } from "@/model/propertyregistration-master";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import {
  ChevronsRight,
  ChevronsLeft,
  CalendarIcon,
  UploadCloudIcon,
  Loader2,
  X,
} from "lucide-react";

import { cn } from "@/lib/utils";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { Textarea } from "@/components/ui/textarea";
import { Select } from "@radix-ui/react-select";
import {
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import CommonField from "./CommonField";
import { Edit, Trash } from "lucide-react";
import { GlobalContext } from "@/context/GlobalContext";
import { AssertionError } from "node:assert";
import PropertyApi from "@/services/PropertyServices";
import Collapse from "@/components/globalcomponent/collapse";
import { Navigate } from "react-router-dom";
import { useLocation, useNavigate } from "react-router-dom";
import { toast } from "@/components/ui/use-toast";
import { t } from "i18next";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { usePropertyFloorMasterController } from "@/controller/master/PropertyFloorController";

export const schema = z.object({
  property_usage_details: z.array(
    z
      .object({
        property_usage_details_id: z.string(),
        construction_area: z.union([z.string(), z.null()]), // Allowing '--' or null
        length: z.union([z.number(), z.string(), z.null()]),
        width: z.union([z.number(), z.string(), z.null()]), // Can be 0 or a positive number, but not null
        are_sq_ft: z.union([z.number(), z.string(), z.null()]), // No specific validation
        are_sq_meter: z.union([z.number(), z.string(), z.null()]), // No specific validation
        construction_start_date: z.date().nullable(), // Initially nullable
        construction_end_date: z.date().nullable(),
        Building_age: z.union([z.string(), z.null()]), // Allowing '--' or null
        flat_no: z.union([z.string(), z.null()]), // Allowing '--' or null
        authorized: z.boolean(),
        propertyType: z.object({
          propertyType_id: z.string().nonempty(t("errorsRequiredField")),
          propertyType: z.string(),
        }),
        usageType: z.object({
          usage_type_id: z.string().nonempty(t("errorsRequiredField")),
          usage_type: z.string(),
        }),
        usageSubType: z
          .object({
            usage_sub_type_master_id: z.string().nullable().or(z.literal("")),
            usage_sub_type: z.string().nullable().or(z.literal("")),
          })
          .nullable(),
        construction_year: z.union([z.string().nullable(), z.number()]), // Initially required
        floor: z.object({
          floor_id: z.string().nullable().or(z.literal("")),
          floor_name: z.string(),
        }),
        annual_rent: z.union([z.string(), z.number(), z.null()]),
        property_photographs: z.array(z.any()).optional(),
        room_detail: z.string().optional(),
        remark: z.string().optional(),
      })
      .superRefine((data, ctx) => {
        if (data.propertyType.propertyType !== "पडसर") {
          if (data.construction_start_date === null) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t("errorsRequiredField"),
              path: ["construction_start_date"],
            });
          }
          if (
            data.construction_year === "" ||
            data.construction_year === 0 ||
            data.construction_year === null
          ) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t("errorsRequiredField"),
              path: ["construction_year"],
            });
          }
        }
      })
  ),
});

const PropertyAssessmentDetailsForm: React.FC<PropertyDetailsInterface> = ({
  handleNextStep,
  handlePreviousStep,
}) => {
  const { propertyInformation, setPropertyInformation } =
    useContext(PropertyContext);
  const { t } = useTranslation();
  const {
    updateProperty,
    setUpdateProperty,
    propertyId: currentPropertyMasterId,
  } = useContext(GlobalContext);
  const [selectedPropertyType, setselectedPropertyType] = useState({});
  const sectionRef = useRef<any>(null); // Reference for <h2>
  const [isEditMode, setIsEditMode] = useState(false); // State to track edit mode
  const [isUpdating, setIsUpdating] = useState(updateProperty);
  const [selectedUsage, setselectedUsage] = useState({});
  const [assetDetails, setassetDetails] = useState<any>([]);
  const [deletedRows, setdeletedRows] = useState<any>([]);

  const [tanstackData, setTanstackData] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(0);
  console.log("updaitng", isUpdating);
  const [selectedUsageDetail, setselectedUsageDetail] = useState<any>(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [isLoading, setIsLoading] = useState(false);

  const secondaryFormValidationRef = useRef<() => Promise<boolean>>();

  const navigate = useNavigate();

  useEffect(() => {
    const initialData =
      propertyInformation?.PropertyAssessmentDetailsForm
        ?.property_usage_details || [];

    setassetDetails(initialData);
    setTanstackData(initialData);

    // Set to the first item if available
  }, []);
  useEffect(() => {
    if (assetDetails.length > 0 && selectedIndex !== null && !isUpdating) {
      setselectedUsageDetail(tanstackData[selectedIndex]);
    }
  }, [selectedIndex, assetDetails]);

  useEffect(() => {
    if (selectedUsageDetail) {
      const newSelectedUsageDetail = { ...selectedUsageDetail };

      if (
        newSelectedUsageDetail.construction_year &&
        !newSelectedUsageDetail.construction_start_date
      ) {
        newSelectedUsageDetail.construction_start_date = new Date(
          newSelectedUsageDetail.construction_year,
          3,
          1
        );
      } else if (newSelectedUsageDetail.construction_start_date) {
        newSelectedUsageDetail.construction_start_date = new Date(
          newSelectedUsageDetail.construction_start_date
        );
      }

      // if (newSelectedUsageDetail.construction_end_date) {
      //   newSelectedUsageDetail.construction_end_date = new Date(
      //     newSelectedUsageDetail.construction_end_date
      //   );
      // }
      console.log("hereeeee setting--->", newSelectedUsageDetail);

      form.reset({
        ...form.getValues(),
        property_usage_details: [newSelectedUsageDetail],
      });
    }
  }, [selectedUsageDetail]);
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      property_usage_details: [
        {
          property_usage_details_id:
            selectedUsageDetail?.property_usage_details_id || "",
          construction_area: selectedUsageDetail?.construction_area ?? null, // Allowing null
          length: selectedUsageDetail?.length ?? null,
          width: selectedUsageDetail?.width ?? null,
          are_sq_ft: selectedUsageDetail?.are_sq_ft ?? null,
          are_sq_meter: selectedUsageDetail?.are_sq_meter ?? null,
          construction_start_date: selectedUsageDetail?.construction_start_date
            ? new Date(selectedUsageDetail.construction_start_date)
            : null, // Assuming this should be included
          construction_end_date: selectedUsageDetail?.construction_end_date
            ? new Date(selectedUsageDetail.construction_end_date)
            : null,
          Building_age: selectedUsageDetail?.Building_age ?? null, // Allowing null
          floor: {
            floor_id: selectedUsageDetail?.floor?.floor_id || "", // Defaults to an empty string if floor_id is missing
            floor_name: selectedUsageDetail?.floor?.floor_name || "", // Defaults to an empty string if floor_name is missing
          },
          flat_no: selectedUsageDetail?.flat_no ?? null, // Allowing null
          authorized: selectedUsageDetail?.authorized ?? false,
          propertyType: {
            propertyType_id:
              selectedUsageDetail?.propertyType?.propertyType_id || "", // Assuming you added propertyType_id
            propertyType: selectedUsageDetail?.propertyType?.propertyType || "",
          },
          usageType: {
            usage_type_id: selectedUsageDetail?.usageType?.usage_type_id || "", // Assuming you added usage_type_id
            usage_type: selectedUsageDetail?.usageType?.usage_type || "",
          },
          usageSubType: selectedUsageDetail?.usageSubType
            ? {
                usage_sub_type_master_id:
                  selectedUsageDetail?.usageSubType?.usage_sub_type_master_id ||
                  "", // Set to empty string if null
                usage_sub_type:
                  selectedUsageDetail?.usageSubType?.usage_sub_type || "", // Set to empty string if null
              }
            : {
                usage_sub_type_master_id: "", // Initialize to empty string
                usage_sub_type: "", // Initialize to empty string
              },

          // Allowing null for usageSubType
          construction_year: selectedUsageDetail?.construction_year ?? 0,

          annual_rent: selectedUsageDetail?.annual_rent ?? null, // Allowing null if added
          property_photographs: selectedUsageDetail?.property_photographs ?? [], // Handle file upload
          room_detail: selectedUsageDetail?.tapshil ?? "", // Room details as string
          remark: selectedUsageDetail?.remark ?? "", // remark as string
        },
      ],
    },
  });
  const {
    formState: { errors },
  } = form;
  const handleAdd = (propertyUsageDetails: any) => {
    const newEntry = propertyUsageDetails[0]; // Assuming you add the first entry
    const updatedTanstackData = [...(tanstackData || []), newEntry];

    // Update the state with the new data
    setTanstackData(updatedTanstackData);
  };

  const handleUpdate = (propertyUsageDetails: any) => {
    const updatedTanstackData = tanstackData.map((item, index) => {
      // Check if the current index matches the selected index for update
      if (index === selectedIndex) {
        const matchingDetail = propertyUsageDetails[0]; // Get the updated details from the propertyUsageDetails array

        // Update the item only if the current index matches the selected one
        return {
          ...item,
          ...matchingDetail,
        };
      }

      return item; // Return unchanged item for other indices
    });

    // Update the state with the updated data
    setTanstackData(updatedTanstackData);

    // Exit edit mode and reset the selected item
    setIsEditMode(false);
    setIsUpdating(true);
    setselectedUsageDetail(null);
  };

  const onSubmit = async (data: any) => {
    setIsCollapseOpen(false);

    console.log("datadata", data);
    const propertyUsageDetails = data.property_usage_details;

    console.log(
      "propertyUsageDetails>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",
      propertyUsageDetails
    );

    if (isEditMode && selectedUsageDetail) {
      handleUpdate(propertyUsageDetails);
      toast({
        title: `मालमत्ता मूल्यांकनाचा तपशील यशस्वीरित्या अपडेट झाला.`,
        variant: "success",
      });
    } else {
      handleAdd(propertyUsageDetails);
      toast({
        title: `नवीन मालमत्ता मूल्यांकनाचा तपशील यशस्वीरित्या जोडला गेला.`,
        variant: "success",
      });
    }

    // Reset the form (optional)
    console.log("resting forms");
    form.reset({
      property_usage_details: [
        {
          property_usage_details_id: "",
          construction_area: null,
          length: 0,
          width: 0,
          are_sq_ft: 0,
          are_sq_meter: 0,
          construction_start_date: null,
          construction_end_date: null,
          Building_age: null,
          floor: { floor_id: "", floor_name: "" },
          flat_no: "",
          authorized: false,
          propertyType: { propertyType_id: "", propertyType: "" },
          usageType: { usage_type_id: "", usage_type: "" },
          usageSubType: { usage_sub_type_master_id: "", usage_sub_type: "" },
          construction_year: 0,
          annual_rent: null,
          property_photographs: [],
          room_detail: "",
          remark: "",
        },
      ],
    });

    console.log("form resting value update submit", form.getValues());
  };

  const onError = (errors) => {
    console.error("Form submission errors: valeus", form.getValues());
    console.log("Form submission errors:", errors);
  };

  const handleEdit = (row: any, index: number) => {
    setIsEditMode(true); // Switch to edit mode when an item is being edited
    setSelectedIndex(index);
    setselectedUsageDetail(row);
    if (sectionRef?.current) {
      sectionRef.current.scrollIntoView({
        behavior: "smooth", // Smooth scroll animation
        block: "start", // Scroll to the top of the section
      });
    }
  };

  const propertytypeList: any = usePropertytypeMasterController();
  const { propertyFloorList } = usePropertyFloorMasterController();

  const propertyOptions: ReactselectInterface[] =
    propertytypeList.propertytypeList?.map(
      (property: PropertytypeMasterObject) => ({
        value: property.propertyType_id,
        label: property.propertyType,
      })
    );

  const floorOptions: ReactselectInterface[] = [
    { value: null, label: "मजला नाही" },
    ...propertyFloorList?.map((property: any) => ({
      value: property.floor_id,
      label: property.floor_name,
    })),
  ];
  // const propertysubtypeList: any = usePropertysubtypeMasterController();
  const propertysubtypeList: any = [];

  const filteredPropertySubType =
    propertysubtypeList?.propertysubtypeList?.filter(
      (propertySubtype: any) =>
        propertySubtype?.propertyType?.propertyType_id ===
        selectedPropertyType?.value
    );

  const propertysubOptions: ReactselectInterface[] =
    filteredPropertySubType?.map(
      (propertysubtype: PropertysubtypeListObject) => ({
        value: propertysubtype.propertySub_id,
        label: propertysubtype.propertySub_name,
      })
    );

  const usageList: any = useUsageMasterController();
  const usageOptions: ReactselectInterface[] = usageList.usageList?.map(
    (usagetype: any) => ({
      value: usagetype.usage_type_id,
      label: usagetype.usage_type,
    })
  );

  const usageSubList: any = useUsageSubController();

  const filteredUsage = usageSubList?.usageSubList?.filter(
    (usagesub: any) =>
      usagesub?.usageType?.usage_type_id === selectedUsage?.value
  );
  const usagesubOptions: ReactselectInterface[] =
    usageSubList?.usageSubList?.map((usagesubtype: any) => ({
      value: usagesubtype.usage_sub_type_master_id,
      label: usagesubtype.usage_sub_type,
    }));

  const [unit, setUnit] = useState("sqf");

  const lengthConversionFactor = 3.28084;

  const { setValue, watch } = form;
  const length = watch(`property_usage_details.${0}.length`);
  const width = watch(`property_usage_details.${0}.width`);

  // Watch specific nested fields for IDs and labels
  const propertyTypeId = watch(
    "property_usage_details.0.propertyType.propertyType_id"
  );
  const propertyTypeLabel = watch(
    "property_usage_details.0.propertyType.propertyType"
  );

  const usageTypeId = watch("property_usage_details.0.usageType.usage_type_id");
  const usageTypeLabel = watch("property_usage_details.0.usageType.usage_type");

  const usageSubTypeId = watch(
    "property_usage_details.0.usageSubType.usage_sub_type_master_id"
  );
  const usageSubTypeLabel = watch(
    "property_usage_details.0.usageSubType.usage_sub_type"
  );

  // Log the watched values
  console.group("IDs and Labels");
  console.log("Property Type ID:", propertyTypeId, "Label:", propertyTypeLabel);
  console.log("Usage Type ID:", usageTypeId, "Label:", usageTypeLabel);
  console.log(
    "Usage Sub Type ID:",
    usageSubTypeId,
    "Label:",
    usageSubTypeLabel
  );
  console.groupEnd();
  console.log("all vlayes------>", form.getValues());
  useEffect(() => {
    // This effect can remain, but you can choose to remove the area calculation logic if it's not needed.
    handleAreaCalculation();
  }, [length, width, unit]);

  const handleUnitChange = (newUnit) => {
    setUnit(newUnit);
  };

  const currentYear = new Date().getFullYear();
  const years = Array.from(new Array(125), (_, index) =>
    (currentYear - index).toString()
  );
  const columns = [
    {
      accessorKey: "sr_no",
      header: `${t("SrNo")}`,
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.index + 1}</div>
      ),
    },
    {
      accessorKey: "propertyType.propertyType",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          मालमत्तेचा प्रकार
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">
          {row.original.propertyType.propertyType}
        </div>
      ),
    },
    {
      accessorKey: "usageType.usage_type",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          मालमत्तेचा वापर
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.usageType.usage_type}</div>
      ),
    },
    {
      accessorKey: "usageSubType.usage_sub_type",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          मालमत्तेचा वापर उपप्रकार
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">
          {row.original.usageSubType?.usage_sub_type || ""}
        </div>
      ),
    },
    {
      accessorKey: "floor_floor_type",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          मजला
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.floor?.floor_name || ""}</div>
      ),
    },
    {
      accessorKey: "length",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          लांबी
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.length}</div>
      ),
    },
    {
      accessorKey: "width",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          रुंदी
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.width}</div>
      ),
    },
    // New column for area in square feet
    {
      accessorKey: "are_sq_ft",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          क्षेत्रफळ (चौ.फू)
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.are_sq_ft}</div>
      ),
    },
    // New column for area in square meters
    {
      accessorKey: "are_sq_meter",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          क्षेत्रफळ (चौ.मी)
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.are_sq_meter}</div>
      ),
    },

    {
      accessorKey: "construction_year",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          बांधकाम वर्ष
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original.construction_year}</div>
      ),
    },
    {
      accessorKey: "authorized",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          प्राधिकरण स्थिती
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">
          {row.original.authorized ? "अधिकृत" : "अनधिकृत"}
        </div>
      ),
    },

    {
      accessorKey: `${t("Actions")}`,
      enableHiding: false,
      cell: ({ row }: { row: any }) => (
        <div className="flex space-x-2">
          <button
            className="h-8 w-8 p-0 justify-center"
            onClick={() => handleEdit(row.original, row.index)}
          >
            <Edit className="text-blue-500" />
          </button>
          <button className="h-8 w-8 p-0 justify-center">
            <Trash
              className="text-red-500"
              onClick={() => handleDelete(row.original, row.index)}
            />
          </button>
        </div>
      ),
    },
  ];

  const [isAuthorized, setIsAuthorized] = useState(false);

  const [isOpen, setIsOpen] = useState(false);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentItem, setCurrentItem] = useState(null);
  const [currentIndex, setCurrentIndex] = useState<number | null>(null);

  const toggleDialog = () => {
    setIsDialogOpen(!isDialogOpen);
  };

  const handleDelete = (rowData: any, index: number) => {
    setCurrentItem(rowData);
    setCurrentIndex(index);
    toggleDialog();
  };

  const confirmDelete = () => {
    if (currentIndex !== null) {
      setTanstackData((prevData: any[]) => {
        if (Array.isArray(prevData)) {
          const updatedData = [...prevData];
          updatedData.splice(currentIndex, 1); // Remove the row at the specified index
          console.log("Updated data after deletion:", updatedData); // Log after deletion
          return updatedData; // Return updated data
        }
        return prevData;
      });
      if (currentItem.property_usage_details_id) {
        setdeletedRows((pre) => [
          ...pre,
          currentItem.property_usage_details_id,
        ]);
      }
      console.log("Deleted row:", currentIndex, currentItem);
      toast({
        title: `${"मालमत्ता मूल्यांकनाचा तपशील यशस्वीरित्या हटविला गेला."}`,
        variant: "destructive",
      });
    }
    toggleDialog();
  };

  function handleDialogOpen() {
    setIsOpen(true);
  }

  function handleDialogClose() {
    setIsOpen(false);
  }

  const [image, setImage] = useState(null);

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setImage(imageUrl);
    }
  };

  // const handleAreaCalculation = () => {
  //   const length = form.getValues(`property_usage_details.${0}.length`);
  //   const width = form.getValues(`property_usage_details.${0}.width`);

  //   if (unit === "sqm") {
  //     const areaSqMeter = length * width;
  //     const areaSqFt = areaSqMeter * 10.7639; // 1 square meter = 10.7639 square feet

  //     const formattedAreaSqFt = parseFloat(areaSqFt.toFixed(2));
  //     form.setValue(`property_usage_details.${0}.are_sq_meter`, areaSqMeter);
  //     form.setValue(`property_usage_details.${0}.are_sq_ft`, formattedAreaSqFt); // Optional if you want both values
  //   } else if (unit === "sqf") {
  //     const areaSqFt = length * width;
  //     const areaSqMeter = areaSqFt / 10.7639; // 1 square meter = 10.7639 square feet

  //     // Format area in square feet to two decimal places
  //     const formattedAreaSqFt = parseFloat(areaSqMeter.toFixed(2));

  //     form.setValue(`property_usage_details.${0}.are_sq_ft`, areaSqFt);
  //     form.setValue(
  //       `property_usage_details.${0}.are_sq_meter`,
  //       formattedAreaSqFt
  //     ); // Optional if you want both values
  //   }
  // };
  const handleAreaCalculation = () => {
    console.log("called handele length" + length, width, form.getValues());
    if (length && width && !isNaN(length) && !isNaN(width)) {
      if (unit === "sqm") {
        // Calculate in square meters first
        const areaSqMeter = length * width;
        const areaSqFt = areaSqMeter * 10.7639;

        form.setValue(
          `property_usage_details.${0}.are_sq_meter`,
          Number(areaSqMeter.toFixed(2))
        );
        form.setValue(
          `property_usage_details.${0}.are_sq_ft`,
          Number(areaSqFt.toFixed(2))
        );
      } else if (unit === "sqf") {
        // Calculate in square feet first
        const areaSqFt = length * width;
        const areaSqMeter = areaSqFt / 10.7639;

        form.setValue(
          `property_usage_details.${0}.are_sq_ft`,
          Number(areaSqFt.toFixed(2))
        );
        form.setValue(
          `property_usage_details.${0}.are_sq_meter`,
          Number(areaSqMeter.toFixed(2))
        );
      }
    } else {
      // Clear both area fields if length or width is invalid
      form.setValue(`property_usage_details.${0}.are_sq_meter`, null);
      form.setValue(`property_usage_details.${0}.are_sq_ft`, null);
    }
  };

  const lastFieldRef = useRef(null);
  const [lastUpdatedField, setLastUpdatedField] = useState<string | null>(null);

  const areaSqMeter = form.watch("property_usage_details.0.are_sq_meter");
  const areaSqFt = form.watch("property_usage_details.0.are_sq_ft");

  useEffect(() => {
    // Update square feet when square meters are updated
    if (
      lastUpdatedField !== "are_sq_ft" &&
      areaSqMeter !== null &&
      !isNaN(areaSqMeter)
    ) {
      const calculatedSqFt = areaSqMeter * 10.7639;
      form.setValue(
        "property_usage_details.0.are_sq_ft",
        Number(calculatedSqFt.toFixed(2)),
        { shouldDirty: true }
      );
      setLastUpdatedField("are_sq_meter"); // Mark that square meters were updated
    }

    // Update square meters when square feet are updated
    if (
      lastUpdatedField !== "are_sq_meter" &&
      areaSqFt !== null &&
      !isNaN(areaSqFt)
    ) {
      const calculatedSqMeter = areaSqFt / 10.7639;
      form.setValue(
        "property_usage_details.0.are_sq_meter",
        Number(calculatedSqMeter.toFixed(2)),
        { shouldDirty: true }
      );
      setLastUpdatedField("are_sq_ft"); // Mark that square feet were updated
    }
  }, [areaSqMeter, areaSqFt, form, lastUpdatedField, length, width]);

  const handleDirectAreaInput = async (value: number | null, field: string) => {
    // Clear length and width when directly entering area
    form.setValue("property_usage_details.0.length", null, {
      shouldDirty: true,
      shouldValidate: true,
    });
    form.setValue("property_usage_details.0.width", null, {
      shouldDirty: true,
      shouldValidate: true,
    });

    setLastUpdatedField(field); // Set the last updated field

    // Handle null or NaN values
    if (value === null || isNaN(value)) {
      form.setValue("property_usage_details.0.are_sq_meter", null, {
        shouldDirty: true,
        shouldValidate: true,
      });
      form.setValue("property_usage_details.0.are_sq_ft", null, {
        shouldDirty: true,
        shouldValidate: true,
      });
      return;
    }

    const numValue = Number(value);

    if (field === "are_sq_meter") {
      form.setValue("property_usage_details.0.are_sq_meter", numValue, {
        shouldDirty: true,
        shouldValidate: true,
      });
      form.setValue(
        "property_usage_details.0.are_sq_ft",
        Number((numValue * 10.7639).toFixed(2)),
        { shouldDirty: true, shouldValidate: true }
      );
    } else if (field === "are_sq_ft") {
      form.setValue("property_usage_details.0.are_sq_ft", numValue, {
        shouldDirty: true,
        shouldValidate: true,
      });
      form.setValue(
        "property_usage_details.0.are_sq_meter",
        Number((numValue / 10.7639).toFixed(2)),
        { shouldDirty: true, shouldValidate: true }
      );
    }

    // Force the form to update and reflect the changes
  };

  const scrollToCommonField = () => {
    if (commonFieldRef.current) {
      commonFieldRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }

    // navigate("/property");
  };

  const UpdatePropertyApi = async (propertyId) => {
    const currentdata = tanstackData || [];

    console.log("current datat", currentdata);

    const isSecondaryValid = await secondaryFormValidationRef.current?.();

    if (!isSecondaryValid) {
      // Open the Collapse if it's closed
      if (!isCollapseOpen) {
        setIsCollapseOpen(true);
      }
      // Scroll to the CommonField component
      scrollToCommonField();
    }

    console.log("isSecondaryValid", secondaryFormValidationRef.current?.());

    if (isSecondaryValid) {
      console.log("Both forms are valid", isSecondaryValid);
      // console.log("Main Form Submitted:", data);
    } else {
      console.log("Secondary form is invalid", isSecondaryValid);
    }

    let updatedPropertyInformation;
    setPropertyInformation((prev) => {
      updatedPropertyInformation = {
        ...prev,
        PropertyAssessmentDetailsForm: {
          property_usage_details: currentdata,
        },
      };

      return updatedPropertyInformation;
    });

    // Create the new object for backend submission for creation
    const filteredDataForCreation = {
      old_propertyNumber:
        updatedPropertyInformation.PropertyLocationDetails
          .property_old_number || "", // Old property number
      city_survey_number:
        updatedPropertyInformation.PropertyLocationDetails.city_survey_number.trim(), // City survey number
      address:
        `${updatedPropertyInformation.PropertyLocationDetails.house_or_apartment_name || ""} ${updatedPropertyInformation.PropertyLocationDetails.location || ""}, ${updatedPropertyInformation.PropertyLocationDetails.street.street_name}, ${updatedPropertyInformation.PropertyLocationDetails.city || ""}, ${updatedPropertyInformation.PropertyLocationDetails.gat_number || ""}`.trim(), // Construct address
      house_or_apartment_name:
        updatedPropertyInformation.PropertyLocationDetails.house_or_apartment_name.trim(), // House or apartment name
      city: updatedPropertyInformation.PropertyLocationDetails?.city || "",
      gat_no:
        updatedPropertyInformation.PropertyLocationDetails?.gat_number || "",
      house_number:
        updatedPropertyInformation.PropertyLocationDetails?.house_number || "",
      block_number:
        updatedPropertyInformation.PropertyLocationDetails?.block_number || "",
      property_remark:
        updatedPropertyInformation.PropertyLocationDetails?.property_remark ||
        "",
      plot_number:
        updatedPropertyInformation.PropertyLocationDetails?.plot_number || "",

      latitude:
        updatedPropertyInformation.PropertyLocationDetails.latitude || "", // Latitude
      longitude:
        updatedPropertyInformation.PropertyLocationDetails.longitude || "", // Longitude
      mobile_number:
        updatedPropertyInformation.AssessmentDetailsForm.mobile_number || "", // Mobile number
      email_id: updatedPropertyInformation.AssessmentDetailsForm.email_id || "", // Email ID
      plot_area:
        updatedPropertyInformation.PropertyAssessmentDetailsForm.plot_area ||
        0.0, // Plot area
      Plot_construction_area:
        updatedPropertyInformation.PlotDetailsForm.construction_area || 0.0, // Plot construction area
      construction_area:
        updatedPropertyInformation.PropertyAssessmentDetailsForm
          .property_usage_details[0]?.construction_area || 0.0, // Construction area
      remark: updatedPropertyInformation.PlotDetailsForm.remark || "", // Remark
      street_id:
        updatedPropertyInformation.PropertyLocationDetails.street.street_id, // Street ID
      ward_id: updatedPropertyInformation.PropertyLocationDetails.ward.ward_id, // Ward ID
      register_id:
        updatedPropertyInformation.PropertyLocationDetails.register
          ?.register_id || null, // Added register_id
      zone_id: updatedPropertyInformation.PropertyLocationDetails.zone.zone_id, // Zone ID
      // uploaded_files: updatedPropertyInformation.PropertyLocationDetails.uploaded_files || [], // Ward ID

      landmark:
        updatedPropertyInformation.PropertyLocationDetails?.landmark || "",
      property_desc:
        updatedPropertyInformation.PropertyLocationDetails?.property_desc || "",
      property_owner_details:
        updatedPropertyInformation.AssessmentDetailsForm.property_owner_details
          ?.length > 0
          ? updatedPropertyInformation.AssessmentDetailsForm.property_owner_details.map(
              (owner) => ({
                owner_type_id: owner?.owner_type || "", // Map owner type
                name: owner?.name || "", // Owner name
                mobile_number: owner?.mobile_number || "", // Owner mobile number
                email_id: owner?.email_id || "", // Owner email ID
                aadhar_number: owner?.aadhar_number || "", // Aadhar number
                pan_card: owner?.pan_card || "", // PAN card
                gender: owner?.gender || null, // Map gender
                marital_status: owner?.marriage_flag || "no", // Marital status
                partner_name: owner?.partner_name || "", // Placeholder for partner name
              })
            )
          : [],
      property_usage_details:
        updatedPropertyInformation.PropertyAssessmentDetailsForm.property_usage_details.map(
          (usage) => ({
            propertyType_id: usage.propertyType.propertyType_id || null, // Property type ID
            usage_type_id: usage.usageType.usage_type_id || null, // Usage type ID
            usage_sub_type_master_id:
              usage.usageSubType?.usage_sub_type_master_id || null, // Default to empty if usage_type_id is not found

            property_type_desc: usage.remark || "", // Property type description
            construction_area: usage.construction_area || 0.0, // Construction area
            length: usage.length || 0.0, // Length
            width: usage.width || 0.0, // Width
            are_sq_ft: usage.are_sq_ft || 0.0, // Area in sq ft
            are_sq_meter: usage.are_sq_meter || 0.0, // Area in sq meter
            floor_id: usage.floor.floor_id || null,
            flat_no: usage.flat_no || "", // Flat number
            annual_rent: Number(usage.annual_rent) || 0.0, // Annual rent
            construction_start_date: usage.construction_start_date || null, // Construction start date
            construction_end_date: usage.construction_end_date || null, // Construction end date
            construction_start_year: usage.construction_year.toString() || null,
            authorized: usage?.authorized || false, // Placeholder for authorized
            remark: usage?.remark || "",
            tapshil: usage?.room_detail || "",
          })
        ),
      commonFields: {
        GISID: updatedPropertyInformation.PlotDetailsForm?.GISID || "", // GIS ID, default to empty string if not available
        propertyDescription:
          updatedPropertyInformation.PlotDetailsForm?.propertyDescription || "", // Property description, default to empty string
        completionCertificate:
          updatedPropertyInformation.PlotDetailsForm?.completionCertificate ||
          "", // Placeholder for completion certificate, as per your requirement
        accessRoad:
          updatedPropertyInformation.PlotDetailsForm?.accessRoad || "", // Access road, default to empty string
        individualToilet:
          updatedPropertyInformation.PlotDetailsForm?.individualToilet || "yes", // Assuming 'yes' as default if not available
        toiletType:
          updatedPropertyInformation.PlotDetailsForm?.toiletType || "", // Toilet type, default to empty string
        totalNumber:
          Number(updatedPropertyInformation.PlotDetailsForm?.totalNumber) ||
          null, // Total number, convert to number or default to 0
        lightingFacility:
          updatedPropertyInformation.PlotDetailsForm?.lightingFacility || "", // Lighting facility, default to empty string
        tapConnection:
          updatedPropertyInformation.PlotDetailsForm?.tapConnection || "", // Tap connection, default to empty string
        totalConnections:
          Number(
            updatedPropertyInformation.PlotDetailsForm?.totalConnections
          ) || null, // Total connections, convert to number or default to 0
        solarProject:
          updatedPropertyInformation.PlotDetailsForm?.solarProject || "no", // Assuming 'yes' as default for solar project
        rainWaterHarvesting:
          updatedPropertyInformation.PlotDetailsForm?.rainWaterHarvesting ||
          "no", // Assuming 'yes' as default for rainwater harvesting
        sewageSystem:
          updatedPropertyInformation.PlotDetailsForm?.sewageSystem || "no", // Assuming 'yes' as default for sewage system
        groundFloorArea:
          Number(updatedPropertyInformation.PlotDetailsForm?.groundFloorArea) ||
          null, // Ground floor area, convert to number or default to 0
        remainingGroundFloorArea:
          Number(
            updatedPropertyInformation.PlotDetailsForm?.remainingGroundFloorArea
          ) || null, // Remaining ground floor area, convert to number or default to 0
      },
    };

    const existingData = updatedPropertyInformation;
    console.log("existingData", existingData);
    // for update property
    const filterDataForUpdate = {
      old_propertyNumber:
        existingData.PropertyLocationDetails?.property_old_number || "",
      city_survey_number: existingData.PropertyLocationDetails
        ?.city_survey_number
        ? existingData.PropertyLocationDetails.city_survey_number.trim()
        : "",
      address: existingData.PropertyLocationDetails?.location
        ? existingData.PropertyLocationDetails.location.trim()
        : "",

      gat_no: existingData.PropertyLocationDetails?.gat_number
        ? existingData.PropertyLocationDetails.gat_number.trim()
        : "",

      city: existingData?.PropertyLocationDetails?.city || "",

      house_number: existingData?.PropertyLocationDetails?.house_number || "",
      block_number: existingData?.PropertyLocationDetails?.block_number || "",
      plot_number: existingData?.PropertyLocationDetails?.plot_number || "",
      house_or_apartment_name: existingData.PropertyLocationDetails
        ?.house_or_apartment_name
        ? existingData.PropertyLocationDetails.house_or_apartment_name.trim()
        : "",
      latitude: existingData.PropertyLocationDetails?.latitude
        ? existingData.PropertyLocationDetails.latitude.trim()
        : "",
      longitude: existingData.PropertyLocationDetails?.longitude
        ? existingData.PropertyLocationDetails.longitude.trim()
        : "",
      mobile_number: existingData.AssessmentDetailsForm
        ?.property_owner_details[0]?.mobile_number
        ? existingData.AssessmentDetailsForm.property_owner_details[0].mobile_number.trim()
        : "",
      email_id: existingData.AssessmentDetailsForm?.property_owner_details[0]
        ?.email_id
        ? existingData.AssessmentDetailsForm.property_owner_details[0].email_id.trim()
        : "",
      plot_area: parseFloat(existingData.PlotDetailsForm?.assessable_area) || 0,
      Plot_construction_area:
        parseFloat(existingData.PlotDetailsForm?.construction_area) || 0,
      construction_area:
        parseFloat(
          existingData.PropertyAssessmentDetailsForm?.property_usage_details[0]
            ?.construction_area
        ) || 0,
      property_remark: existingData.PropertyLocationDetails?.property_remark
        ? existingData.PropertyLocationDetails.property_remark.trim()
        : "", // Ensure it defaults to empty if not found
      street_id: existingData.PropertyLocationDetails?.street?.street_id || "", // Default to empty if street_id is not found
      ward_id: existingData.PropertyLocationDetails?.ward?.ward_id || "", // Default to empty if ward_id is not found
      register_id:
        existingData.PropertyLocationDetails?.register?.register_id || null, // Added register_id
      zone_id: existingData.PropertyLocationDetails?.zone?.zone_id || "", // Default to empty if zone_id is not found
      landmark: existingData.PropertyLocationDetails?.landmark || "",
      property_desc: existingData.PropertyLocationDetails?.property_desc || "",
      // uploaded_files: existingData.PropertyLocationDetails.uploaded_files || [], // Ward ID

      property_owner_details:
        existingData.AssessmentDetailsForm?.property_owner_details?.map(
          (owner) => ({
            property_owner_details_id: owner.property_owner_details_id || null, // Assuming property_owner_details_id exists in owner
            owner_type_id: owner.owner_type,
            name: owner.name?.trim() || "", // Default to empty if name is not found
            mobile_number: owner.mobile_number?.trim() || "", // Default to empty if mobile_number is not found
            email_id: owner.email_id?.trim() || "", // Default to empty if email_id is not found
            aadhar_number: owner.aadhar_number?.trim() || "", // Default to empty if aadhar_number is not found
            pan_card: owner.pan_card?.trim() || "", // Ensure pan_card defaults to empty if not found
            gender: owner.gende || null, // Assuming gender might exist
            marital_status: owner.marriage_flag || "no", // Assuming this is the field for marital status
            partner_name: owner.partner_name || "", // Placeholder for partner name if needed
          })
        ) || [],
      property_usage_details:
        existingData.PropertyAssessmentDetailsForm?.property_usage_details?.map(
          (usage) => ({
            property_usage_details_id: usage.property_usage_details_id || null, // Ensure defaults to empty if not found
            propertyType_id: usage.propertyType?.propertyType_id || null, // Default to empty if propertyType_id is not found
            usage_type_id: usage.usageType?.usage_type_id || null,
            usage_sub_type_master_id:
              usage.usageSubType?.usage_sub_type_master_id || null, // Default to empty if usage_type_id is not found
            property_type_desc: usage.propertyType?.propertyType || "", // Default to empty if propertyType is not found
            construction_area: parseFloat(usage.construction_area) || 0,
            length: usage.length || 0, // Default to 0 if length is not found
            width: usage.width || 0, // Default to 0 if width is not found
            are_sq_ft: usage.are_sq_ft || 0, // Default to 0 if not found
            are_sq_meter: usage.are_sq_meter || 0, // Default to 0 if not found
            floor_id: usage.floor.floor_id || null,
            flat_no: usage.flat_no || "",
            annual_rent: Number(usage.annual_rent) || 0, // Placeholder for annual rent if needed
            construction_start_date: usage.construction_start_date || null, // Placeholder if needed
            construction_end_date: usage.construction_end_date || null, // Placeholder if needed
            construction_start_year: usage.construction_year
              ? usage.construction_year.toString()
              : null,
            authorized: usage.authorized ? "yes" : "no",
            remark: usage?.remark || "",
            tapshil: usage?.room_detail || "",
          })
        ) || [],
      commonFields: {
        commonFieldId: existingData.PlotDetailsForm?.commomDetailId || "", // ID from existingData
        GISID: existingData.PlotDetailsForm?.GISID || "", // Default to empty if not found
        propertyDescription:
          existingData.PlotDetailsForm?.propertyDescription || "", // Default to empty if not found
        completionCertificate:
          existingData.PlotDetailsForm?.completionCertificate || "", // Default to empty if null
        accessRoad: existingData.PlotDetailsForm?.accessRoad
          ? existingData.PlotDetailsForm.accessRoad.trim()
          : "", // If access road is available, trim it
        individualToilet:
          existingData.PlotDetailsForm?.individualToilet || "no", // Default to 'yes' if null
        toiletType: existingData.PlotDetailsForm?.toiletType || "", // Default to empty if not found
        totalNumber: existingData.PlotDetailsForm?.totalNumber
          ? Number(existingData.PlotDetailsForm.totalNumber)
          : null, // Parse number or default to 0 if not found
        lightingFacility: existingData.PlotDetailsForm?.lightingFacility
          ? existingData.PlotDetailsForm.lightingFacility.trim()
          : "", // If lighting facility is available, trim it
        tapConnection: existingData.PlotDetailsForm?.tapConnection
          ? existingData.PlotDetailsForm.tapConnection.trim()
          : "", // If tap connection is available, trim it
        totalConnections:
          Number(existingData.PlotDetailsForm?.totalConnections) || null, // Default to 0 if null
        solarProject: existingData.PlotDetailsForm?.solarProject || "no", // Assuming 'yes'
        rainWaterHarvesting:
          existingData.PlotDetailsForm?.rainWaterHarvesting || "no", // Assuming 'yes'
        sewageSystem: existingData.PlotDetailsForm?.sewageSystem || "no", // Assuming 'yes'
        groundFloorArea:
          Number(existingData.PlotDetailsForm?.groundFloorArea) || null, // Default to 0 if not found
        remainingGroundFloorArea:
          Number(existingData.PlotDetailsForm?.remainingGroundFloorArea) ||
          null, // Default to 0 if not found
      },
      deleted_property_usage_details_id: deletedRows,
    };

    try {
      setIsLoading(true); // Disable the button while the request is in progress

      if (updateProperty === true) {
        const response = await PropertyApi.updateProperty(
          currentPropertyMasterId,
          filterDataForUpdate
        );

        if (response.status && response.statusCode === 200) {
          toast({
            title: `${t("propertys.updateSuccessFully")}`,
            variant: "success",
          });
          navigate("/property");
        } else {
          throw new Error(response.data?.message || "Update failed");
        }
      } else {
        const response = await PropertyApi.createProperty(
          filteredDataForCreation
        );

        if (response.status && response.statusCode === 201) {
          toast({
            title: t("प्रॉपर्टी यशस्वीरित्या निर्माण झाली!"),
            variant: "success",
          });
          navigate("/property");
        } else {
          throw new Error(response.data?.message || "Creation failed");
        }
      }
    } catch (error) {
      console.error("Error updating or creating property information:", error);

      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "An unknown error occurred.";

      toast({
        title: t("An error occurred!"),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false); // Re-enable the button after the request is complete
    }
  };
  const commonFieldRef = useRef<HTMLDivElement>(null); // To reference the CommonField component

  const [isCollapseOpen, setIsCollapseOpen] = useState(false); // Track Collapse state
  const formatNumber = (num: number | string) => {
    const value = Number(num || 0);
    return value.toFixed(2);
  };
  return (
    <div className="h-fit">
      <div>
        <Collapse
          title={"सामान्य क्षेत्र "}
          isOpen={isCollapseOpen}
          onToggle={() => setIsCollapseOpen(!isCollapseOpen)}
        >
          <div ref={commonFieldRef}>
            <CommonField
              triggerValidation={(validationFunc) =>
                (secondaryFormValidationRef.current = validationFunc)
              }
            />
          </div>
        </Collapse>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit, onError)}>
          <div className="px-4 py-4 border rounded-xl ">
            <h2 ref={sectionRef} className="text-xl font-semibold font-Noto">
              {t("मालमत्ता मूल्यांकनाचे तपशील")}
            </h2>
            <hr className="my-3" />
            <div className="grid grid-cols-1  md:grid-cols-3 gap-2 gap-x-5">
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="property_usage_details.0.propertyType.propertyType_id" // This targets the nested propertyType_id field
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("propertyAssessmentDetailsForm.propertyType")}
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Controller
                          control={form.control}
                          name="property_usage_details.0.propertyType.propertyType_id" // Explicitly target propertyType_id field
                          render={({ field: controllerField }) => (
                            <AsyncSelect
                              placeholder={t(
                                "propertyAssessmentDetailsForm.propertyType"
                              )}
                              options={propertyOptions}
                              value={
                                propertyOptions.find(
                                  (option: any) =>
                                    option.value === controllerField.value
                                ) || null
                              }
                              onChange={(selectedOption: any) => {
                                controllerField.onChange(selectedOption.value);

                                form.setValue(
                                  "property_usage_details.0.propertyType.propertyType",
                                  selectedOption.label
                                );
                              }}
                              colourOptions={propertyOptions}
                            />
                          )}
                        />
                      </FormControl>
                      {/* Access and display the error for propertyType_id */}
                      {errors.property_usage_details?.[0]?.propertyType
                        ?.propertyType_id && (
                        <FormMessage className="ml-1">
                          Enter Property Type
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="property_usage_details.0.usageType.usage_type_id" // Target the nested usage_type_id field
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("propertyAssessmentDetailsForm.propertyUsageType")}
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Controller
                          control={form.control}
                          name="property_usage_details.0.usageType.usage_type_id"
                          render={({ field: controllerField }) => (
                            <AsyncSelect
                              placeholder={t(
                                "propertyAssessmentDetailsForm.propertyUsageType"
                              )}
                              options={usageOptions}
                              value={
                                usageOptions.find(
                                  (option: any) =>
                                    option.value === controllerField.value
                                ) || null
                              }
                              onChange={(selectedOption: any) => {
                                controllerField.onChange(selectedOption.value);

                                form.setValue(
                                  "property_usage_details.0.usageType.usage_type",
                                  selectedOption.label
                                );

                                form.setValue(
                                  "property_usage_details.0.usageType.usage_type_id",
                                  selectedOption.value
                                );
                              }}
                              colourOptions={usageOptions}
                            />
                          )}
                        />
                      </FormControl>
                      {/* Access and display the error for usage_type_id */}
                      {errors.property_usage_details?.[0]?.usageType
                        ?.usage_type_id && (
                        <FormMessage className="ml-1">
                          Enter Property Usage Type
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.usageSubType.usage_sub_type_master_id`} // Adjust the index as needed
                  render={({ field }) => (
                    <FormItem className="">
                      <FormLabel>
                        {t(
                          "propertyAssessmentDetailsForm.propertyUsageSubType"
                        )}
                      </FormLabel>
                      <FormControl>
                        <Controller
                          control={form.control}
                          name={`property_usage_details.${0}.usageSubType.usage_sub_type_master_id`} // Use the same name for consistency
                          render={({ field: controllerField }) => (
                            <AsyncSelect
                              placeholder={t(
                                "propertyAssessmentDetailsForm.propertyUsageSubType"
                              )}
                              options={usagesubOptions}
                              value={
                                (usagesubOptions &&
                                  usagesubOptions.find(
                                    (option: any) =>
                                      option.value === controllerField.value
                                  )) ||
                                null
                              }
                              onChange={(selectedOption: any) => {
                                controllerField.onChange(selectedOption.value); // Update the form state

                                form.setValue(
                                  "property_usage_details.0.usageSubType.usage_sub_type",
                                  selectedOption?.label
                                    ? selectedOption.label
                                    : "" // If label exists, use it; otherwise, set to an empty string
                                );
                              }}
                              colourOptions={usagesubOptions}
                            />
                          )}
                        />
                      </FormControl>
                      {errors.property_usage_details?.[0]?.usageSubType
                        ?.usage_sub_type_master_id && (
                        <FormMessage className="ml-1">
                          Enter Property Usage Sub Type
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.flat_no`} // Adjust the index as needed
                  render={({ field }) => (
                    <FormItem className="">
                      <FormLabel>{t("plotDetailsForm.flatNumber")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.flatNumber")}
                          value={field.value !== null ? field.value : ""}
                          {...field} // Spread field props to connect to form state
                        />
                      </FormControl>
                      {errors.property_usage_details?.[0]?.flat_no && (
                        <FormMessage className="ml-1">
                          Enter Flat Number
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              {/* fllor type master */}
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name="property_usage_details.0.floor.floor_id" // This targets the nested propertyType_id field
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("plotDetailsForm.floorNumber")}
                        {/* <span className="ml-1 text-red-500">*</span> */}
                      </FormLabel>
                      <FormControl>
                        <Controller
                          control={form.control}
                          name="property_usage_details.0.floor.floor_id" // This targets the nested propertyType_id field
                          render={({ field: controllerField }) => (
                            <AsyncSelect
                              placeholder={t("plotDetailsForm.floorNumber")}
                              options={floorOptions} // Using floorOptions
                              value={
                                floorOptions.find(
                                  (option: any) =>
                                    option.value === controllerField.value
                                ) || null
                              }
                              onChange={(selectedOption: any) => {
                                controllerField.onChange(selectedOption.value);

                                form.setValue(
                                  "property_usage_details.0.floor.floor_name",
                                  selectedOption.label
                                );
                              }}
                              colourOptions={floorOptions} // Using floorOptions
                            />
                          )}
                        />
                      </FormControl>
                      {errors.property_usage_details?.[0]?.floor?.floor_id && (
                        <FormMessage className="ml-1">
                          Enter floorOptions
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.length`} // Adjust the index as needed
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        लांबी
                        {/* <span className="ml-1 text-red-500">*</span> */}
                      </FormLabel>
                      <FormControl>
                        <div className="flex items-center mt-1">
                          <div className="w-[80px]">
                            <select
                              className="block w-full h-10 px-2 py-1 border border-[#0000006b] rounded-l-md text-[13px]"
                              onChange={(e) => handleUnitChange(e.target.value)}
                              value={unit}
                            >
                              <option value="sqm" className="text-xs">
                                मी
                              </option>
                              <option value="sqf">फु</option>
                            </select>
                          </div>
                          <Input
                            type="number"
                            className="rounded-l-none"
                            placeholder="लांबी"
                            {...field}
                            value={field.value}
                            min={0}
                            step="0.01"
                            onChange={(e) => {
                              const length = parseFloat(e.target.value) || 0;
                              field.onChange(length);
                              handleAreaCalculation(); // Update area when length changes
                            }}
                          />
                        </div>
                      </FormControl>
                      {errors.property_usage_details?.[0]?.length && (
                        <FormMessage className="ml-1">Enter Length</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.width`} // Adjust the index as needed
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        रुंदी
                        {/* <span className="ml-1 text-red-500">*</span> */}
                      </FormLabel>
                      <FormControl>
                        <div className="flex items-center mt-1">
                          <div className="w-[80px]">
                            <select
                              className="block w-full h-10 px-2 py-1 border border-[#0000006b] rounded-l-md text-[13px]"
                              onChange={(e) => handleUnitChange(e.target.value)}
                              value={unit}
                            >
                              <option value="sqm">मी</option>
                              <option value="sqf">फु</option>
                            </select>
                          </div>
                          <Input
                            type="number"
                            className="rounded-l-none"
                            placeholder="रुंदी"
                            {...field}
                            value={field.value == null ? null : field.value}
                            min={0}
                            step="0.01"
                            onChange={(e) => {
                              const width = parseFloat(e.target.value) || 0;
                              field.onChange(width);
                              handleAreaCalculation(); // Update area when width changes
                            }}
                          />
                        </div>
                      </FormControl>
                      {errors.property_usage_details?.[0]?.width && (
                        <FormMessage className="ml-1">Enter Width</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              {/* Area in Square Meters */}
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.are_sq_meter`} // Adjust the index as needed
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        क्षेत्रफळ (चौ. मी)
                        {/* <span className="ml-1 text-red-500">*</span> */}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="क्षेत्रफळ"
                          value={field.value}
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value
                              ? parseFloat(e.target.value)
                              : 0;
                            field.onChange(value);
                            handleDirectAreaInput(value, "are_sq_meter");
                          }}
                        />
                      </FormControl>
                      {errors.property_usage_details?.[0]?.are_sq_meter && (
                        <FormMessage className="ml-1">Enter Area</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              {/* Area in Square Feet */}
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.are_sq_ft`} // Adjust the index as needed
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        क्षेत्रफळ (चौ. फु)
                        {/* <span className="ml-1 text-red-500">*</span> */}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="क्षेत्रफळ"
                          value={field.value}
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value
                              ? parseFloat(e.target.value)
                              : 0;
                            field.onChange(value);
                            handleDirectAreaInput(value, "are_sq_ft");
                          }}
                        />
                      </FormControl>
                      {errors.property_usage_details?.[0]?.are_sq_ft && (
                        <FormMessage className="ml-1">Enter Area</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                {/* Construction Year Field */}
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.construction_year`} // Dynamically refer to the index
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        बांधकाम वर्ष{" "}
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="flex items-center mt-1">
                          <Input
                            type="number"
                            inputMode="numeric"
                            maxLength={4}
                            value={field.value || ""}
                            onChange={(e) => {
                              const value = e.target.value;

                              // Only allow digits and limit to 4 characters
                              if (!/^\d{0,4}$/.test(value)) {
                                field.onChange("");
                                return;
                              }

                              // If the input is empty, clear the field
                              if (!value) {
                                field.onChange("");
                                setSelectedYear(null);
                                form.setValue(
                                  "property_usage_details.0.construction_start_date",
                                  null
                                );
                                return;
                              }

                              const year = parseInt(value, 10);
                              const currentYear = new Date().getFullYear();

                              // Only set the value if it's a valid year between 1800 and current year
                              if (year >= 1800 && year <= currentYear) {
                                field.onChange(year);
                                setSelectedYear(year);
                                form.setValue(
                                  "property_usage_details.0.construction_start_date",
                                  new Date(year, 3, 1)
                                ); // Set to April 1st
                              } else if (value.length === 4) {
                                // Clear the field if a complete invalid year is entered
                                field.onChange("");
                                setSelectedYear(null);
                                form.setValue(
                                  "property_usage_details.0.construction_start_date",
                                  null
                                );
                              } else {
                                // Allow partial input while typing
                                field.onChange(value);
                              }
                            }}
                            placeholder="बांधकाम वर्ष"
                          />
                        </div>
                      </FormControl>
                      {errors.property_usage_details?.[0]
                        ?.construction_year && (
                        <FormMessage className="ml-1">
                          बांधकाम वर्ष 1800 ते चालू वर्षाच्या दरम्यान असावे
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                {/* Construction Start Date Field */}
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.construction_start_date`} // Using index
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t(
                          "propertyAssessmentDetailsForm.propertyConstructionStartDate"
                        )}
                        {/* <span className="ml-1 text-red-500">*</span> */}
                      </FormLabel>
                      <FormControl>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full h-10 mt-1 p-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value && !isNaN(field.value.getTime()) ? (
                                format(field.value, "MM-dd-yyyy")
                              ) : (
                                <span>
                                  {t(
                                    "propertyAssessmentDetailsForm.propertyConstructionStartDate"
                                  )}
                                </span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0" align="start">
                            <Calendar
                              className="dashboard-calender w-full"
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              defaultMonth={
                                field.value ? new Date(field.value) : new Date() // Default to today's date
                              }
                              disabled={
                                (date) => date > new Date() // Disable dates in the future
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </FormControl>
                      {errors.property_usage_details?.[0]
                        ?.construction_start_date && (
                        <FormMessage className="ml-1">
                          Enter Construction Start Date
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormLabel>
                  {" "}
                  {t("plotDetailsForm.PropertyPhotographs")}
                </FormLabel>
                <div className="relative" onClick={() => handleDialogOpen()}>
                  <Input
                    type="file"
                    placeholder={t("plotDetailsForm.PropertyPhotographs")}
                    multiple
                    className="text-transparent mt-2"
                  />
                  <Button
                    className="h-10 mt-[-2px] px-4 py-5 rounded-r-8 rounded-none !absolute !top-[6%] right-0"
                    type="button"
                  >
                    <UploadCloudIcon className="w-6 h-6 mr-2" />
                    {t("propertyLocationDetailsForm.uploadFile")}
                  </Button>
                  {/* <UploadCloudIcon className="absolute top-[33%] right-5" /> */}
                  {/* <img className="absolute top-[33%] right-5" src={UploadICon} alt="" /> */}
                </div>
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.annual_rent`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("plotDetailsForm.annualRent")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="number"
                          placeholder={t("plotDetailsForm.annualRent")}
                          value={field.value || ""}
                          onChange={(e) => field.onChange(e.target.value)} // Ensure correct value type
                        />
                      </FormControl>
                      {errors.property_usage_details?.[0]?.annual_rent && (
                        <FormMessage className="ml-1">
                          Enter Annual Rent
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.authorized`} // Dynamically handle name with index
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>परवानगीची स्थिती</FormLabel>
                      <FormControl>
                        <RadioGroup
                          value={field.value ? "authorized" : "unauthorized"} // Set default value based on field value
                          onValueChange={(value) => {
                            const authorized = value === "authorized";
                            field.onChange(authorized); // Update the form field
                            setIsAuthorized(authorized); // Update local state if needed
                          }}
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="authorized" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              अधिकृत
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="unauthorized" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              अनधिकृत
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      {errors.property_usage_details?.[0]?.authorized && ( // Check for errors on the authorized field
                        <FormMessage className="ml-1">
                          Select one option
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.room_detail`} // Dynamically handle name with index
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>{t("plotDetailsForm.roomDetails")}</FormLabel>
                      <FormControl>
                        <Textarea
                          className="mt-1 block w-full"
                          placeholder={t("plotDetailsForm.roomDetails")}
                          {...field}
                        />
                      </FormControl>
                      {errors.property_usage_details?.[0]?.room_detail && (
                        <FormMessage className="ml-1">
                          Enter Room Details
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={form.control}
                  name={`property_usage_details.${0}.remark`} // Dynamically handle name with index
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>शेरा</FormLabel>
                      <FormControl>
                        <Textarea
                          className="mt-1 block w-full"
                          placeholder={"शेरा"}
                          {...field}
                        />
                      </FormControl>
                      {errors.property_usage_details?.[0]?.remark && (
                        <FormMessage className="ml-1">Enter remark</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
          <div className="w-full justify-end flex my-5">
            {!isEditMode ? (
              <Button type="submit">{t("add")}</Button>
            ) : (
              <Button type="submit">{t("update")}</Button>
            )}
          </div>
        </form>
      </Form>
      <TanStackTable columns={columns} data={tanstackData} />
      <div className="w-full text-end flex justify-end items-center mt-2">
        <>
          <Button
            variant="outline"
            className="mr-4 border-BlueText text-BlueText "
            onClick={handlePreviousStep}
          >
            {<ChevronsLeft className="w-5 h-5 font-bold " />}{" "}
          </Button>

          <Button
            variant="submit"
            type={"button"}
            disabled={isLoading}
            onClick={UpdatePropertyApi}
          >
            {updateProperty ? t("propertys.update") : t("propertys.AddBtn")}
          </Button>
        </>

        {}
      </div>
      <DeletePopUpScreen
        isOpen={isDialogOpen}
        toggle={toggleDialog}
        itemName={currentItem?.name || ""}
        onDelete={confirmDelete} // Function to confirm deletion
      />
    </div>
  );
};

export default PropertyAssessmentDetailsForm;
