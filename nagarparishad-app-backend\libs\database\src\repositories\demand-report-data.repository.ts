import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DemandReportData } from "../entities";
import { Repository } from "typeorm";

@Injectable()
export class DemandRecordDataRepository extends Repository<DemandReportData> {
  constructor(
    @InjectRepository(DemandReportData)
    private readonly demandRecordRepo: Repository<DemandReportData>,
  ) {
    super(
        demandRecordRepo.target,
        demandRecordRepo.manager,
        demandRecordRepo.queryRunner,
      );
  }

  async updateDemandRecord(propertyId: number, financialYear: string, remainingTax: number) {
    return this.demandRecordRepo
      .createQueryBuilder()
      .update(DemandReportData)
    //   .set({ remainingTax })
      .where("property_id = :propertyId AND financial_year = :financialYear", { propertyId, financialYear })
      .execute();
  }
}
