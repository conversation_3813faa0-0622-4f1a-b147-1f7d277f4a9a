import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON>y, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

export enum FerfarOperationType {
  ADD = 'add',
  UPDATE = 'update',
  DELETE = 'delete',
  TRANSFER = 'transfer',
}

export class OwnerDataDto {
  @ApiProperty({ description: 'Owner ID (required for update/delete)', example: 'uuid' })
  @IsUUID()
  @IsOptional()
  property_owner_details_id?: string;

  @ApiProperty({ description: 'Owner type ID', example: 'uuid' })
  @IsUUID()
  @IsOptional()
  owner_type_id?: string;

  @ApiProperty({ description: 'Owner name', example: '<PERSON>' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Mobile number', example: '9876543210' })
  @IsString()
  @IsOptional()
  mobile_number?: string;

  @ApiProperty({ description: 'Email ID', example: '<EMAIL>' })
  @IsString()
  @IsOptional()
  email_id?: string;

  @ApiProperty({ description: 'Aadhar number', example: '123456789012' })
  @IsString()
  @IsOptional()
  aadhar_number?: string;

  @ApiProperty({ description: 'Gender', example: 'MALE' })
  @IsString()
  @IsOptional()
  gender?: string;

  @ApiProperty({ description: 'Marital status', example: 'yes' })
  @IsString()
  @IsOptional()
  marital_status?: string;

  @ApiProperty({ description: 'PAN card number', example: '**********' })
  @IsString()
  @IsOptional()
  pan_card?: string;

  @ApiProperty({ description: 'Partner name', example: 'Jane Doe' })
  @IsString()
  @IsOptional()
  partner_name?: string;

  @ApiProperty({ description: 'Operation to perform on this owner', example: 'add' })
  @IsEnum(FerfarOperationType)
  operation: FerfarOperationType;
}

export class PropertyFerfarDto {
  @ApiProperty({ description: 'Property ID', example: 'uuid' })
  @IsUUID()
  property_id: string;

  @ApiProperty({ description: 'Remark for the ferfar operation', example: 'Property transferred due to sale' })
  @IsString()
  @IsOptional()
  remark: string;

  @ApiProperty({ description: 'Array of previous owner names', type: [String] })
  @IsArray()
  @IsOptional()
  previous_owner_names: string[];

  @ApiProperty({ description: 'Reason for property transfer', example: 'Sale of property' })
  @IsString()
  @IsOptional()
  reason: string;

  @ApiProperty({ description: 'Array of photo image paths', type: [String] })
  @IsArray()
  @IsOptional()
  photo_image_paths: string[];

  @ApiProperty({ description: 'Array of document image paths', type: [String] })
  @IsArray()
  @IsOptional()
  document_image_paths: string[];

  @ApiProperty({ description: 'Year of the ferfar operation', example: '2023-2024' })
  @IsString()
  @IsOptional()
  year: string;

  @ApiProperty({ description: 'Reassessment range ID', example: 'uuid' })
  @IsUUID()
  @IsOptional()
  reassessment_range_id: string;

  @ApiProperty({ description: 'User email ID', example: '<EMAIL>' })
  @IsString()
  @IsOptional()
  user_email_id: string;

  @ApiProperty({ description: 'Array of owners with operations to perform', type: [OwnerDataDto] })
  @IsArray()
  owners: OwnerDataDto[];
}
