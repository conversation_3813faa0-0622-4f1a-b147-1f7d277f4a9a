import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportPropertyupdatecoladdded11724825479325 implements MigrationInterface {
    name = 'ImportPropertyupdatecoladdded11724825479325'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ADD "sr_no_system" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "sr_no_system"`);
    }

}
