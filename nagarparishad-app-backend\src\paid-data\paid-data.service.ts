import { Injectable, Logger } from '@nestjs/common';
import { PaidDataRepository, PaidAndNonPaidDataRepository, PropertyMasterRepository, Financial_yearRepository, PaymentInfoRepository, WarshikKarRepository } from 'libs/database/repositories';
import { PaginationOptions, PaginatedResult } from '@helper/helpers/Pagination';
import { PaidAndNonPaidDataEntity } from 'libs/database/entities';

@Injectable()
export class PaidDataService {
  private readonly logger = new Logger(PaidDataService.name);

  constructor(
    private readonly paidDataRepository: PaidDataRepository,
    private readonly paidAndNonPaidDataRepository: PaidAndNonPaidDataRepository,
    private readonly propertyMasterRepository: PropertyMasterRepository,
    private readonly financialYearRepository: Financial_yearRepository,
    private readonly paymentInfoRepository: PaymentInfoRepository,
    private readonly warshikKarRepository: WarshikKarRepository,
  ) {}

  /**
   * Get year-wise data for a specific property by property ID
   * @param propertyId The ID of the property
   * @param year Optional: The specific year to retrieve data for
   * @returns The year-wise data for the specified property and year, or all years if no year is specified
   */
  async getYearWiseDataByPropertyId(propertyId: string, year?: string) {
    return this.paidDataRepository.getYearWiseDataByPropertyId(propertyId, year);
  }

  /**
   * Add or update year-wise data for a specific paid data record
   * @param paidDataId The ID of the paid data record
   * @param year The year for which to store data
   * @param data The data to store for the year
   */
  async addYearWiseData(paidDataId: string, year: string, data: any) {
    return this.paidDataRepository.addYearWiseData(paidDataId, year, data);
  }

  /**
   * Get all paid data for a specific property
   * @param propertyId The ID of the property
   * @returns All paid data records for the specified property
   */
  async getPaidDataByPropertyId(propertyId: string) {
    // Find all paid data records for the property
    const paidData = await this.paidDataRepository.find({
      where: { property: { property_id: propertyId } },
      order: { createdAt: 'DESC' },
      relations: ['property']
    });

    return paidData;
  }

  /**
   * Generate paid and non-paid data for all properties in a financial year
   * @param financialYearRange The financial year range (e.g., "2023-2024")
   * @returns Success message with processing statistics
   */
  async generatePaidAndNonPaidData() {
    try {
    const currentFinancialYear = await this.financialYearRepository.findOne({
      where: { is_current: true },
    });
    let financialYearRange = currentFinancialYear.financial_year_range;

      // Get the financial year entity
      const financialYear = await this.financialYearRepository.findOne({
        where: { financial_year_range: financialYearRange }
      });

      if (!financialYear) {
        throw new Error(`Financial year ${financialYearRange} not found`);
      }

      // Clear existing data for this financial year to regenerate
      await this.paidAndNonPaidDataRepository.deleteByFinancialYear(financialYearRange);

      // Get all properties with their owner details
      const properties = await this.propertyMasterRepository.find({
        relations: ['property_owner_details', 'property_owner_details.owner_type'],
        order: { updatedAt: 'DESC' }
      });

      this.logger.log(`Found ${properties.length} properties to process`);

      let processedCount = 0;
      let errorCount = 0;
      const errors = [];

      // Process each property
      for (const property of properties) {
        try {
          // Get owner name (prioritize स्वत: owner type, fallback to any available owner)
          let ownerName = 'Unknown';
          if (property.property_owner_details && property.property_owner_details.length > 0) {
            const preferredOwner = property.property_owner_details.find(
              owner => owner.owner_type?.owner_type === 'स्वत:'
            ) || property.property_owner_details[0];

            ownerName = preferredOwner.name || 'Unknown';
          }

          // Check if property has any payments in the specified financial year
          const payments = await this.paymentInfoRepository.find({
            where: { property: { property_id: property.property_id } },
            relations: ['property']
          });

          // Calculate total tax from WarshikKar entity for the financial year
          let totalTax = 0;
          let paidAmount = 0;
          let isPaid = false;

          // Get total tax from WarshikKar for this property and financial year
          const warshikKarData = await this.warshikKarRepository.findOne({
            where: {
              property: { property_id: property.property_id },
              financial_year: financialYearRange,
              status: 'active'
            },
            relations: ['property']
          });

          if (warshikKarData) {
            totalTax = Number(warshikKarData.total_tax) || 0;
          }

          // Calculate paid amount from payments
          if (payments && payments.length > 0) {
            paidAmount = payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
            isPaid = paidAmount > 0;
          }

          const remainingAmount = Math.max(0 , (totalTax - paidAmount));

          // Save the record
          await this.paidAndNonPaidDataRepository.saveData({
            property_id: property.property_id,
            financial_year_id: financialYear.financial_year_id,
            financial_year_range: financialYearRange,
            name: ownerName,
            property_number: property.propertyNumber,
            old_property_number: property.old_propertyNumber,
            total_tax: totalTax,
            paid: paidAmount,
            remaining: remainingAmount,
            is_paid: isPaid
          });

          processedCount++;

          if (processedCount % 100 === 0) {
            this.logger.log(`Processed ${processedCount} properties...`);
          }

        } catch (error) {
          errorCount++;
          errors.push({
            property_id: property.property_id,
            property_number: property.propertyNumber,
            error: error.message
          });
          this.logger.error(`Error processing property ${property.propertyNumber}: ${error.message}`);
        }
      }

      this.logger.log(`Completed processing. Processed: ${processedCount}, Errors: ${errorCount}`);

      return {
        success: true,
        message: 'Paid and non-paid data generated successfully',
        data: {
          financialYear: financialYearRange,
          totalProperties: properties.length,
          processedCount,
          errorCount,
          errors: errors.slice(0, 10) // Return only first 10 errors
        }
      };

    } catch (error) {
      this.logger.error(`Error generating paid and non-paid data: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get paid and non-paid data with pagination
   * @param financialYearRange Optional financial year filter
   * @param options Pagination options
   * @returns Paginated data
   */
  async getPaidAndNonPaidData( options: PaginationOptions = {}): Promise<PaginatedResult<PaidAndNonPaidDataEntity>> {
    // Set default pagination values
    options.page = options.page || 1;
    options.limit = options.limit || 200;

       const currentFinancialYear = await this.financialYearRepository.findOne({
      where: { is_current: true },
    });

    if (currentFinancialYear) {
      return await this.paidAndNonPaidDataRepository.findByFinancialYear(currentFinancialYear?.financial_year_range, options);
    }
    return await this.paidAndNonPaidDataRepository.findAllData(options);
  }

  /**
   * Get only paid users data
   * @param financialYearRange Optional financial year filter
   * @param options Pagination options
   * @returns Paginated paid users data
   */
  async getPaidUsersData( options: PaginationOptions = {},value?: string,searchOn?: string): Promise<PaginatedResult<PaidAndNonPaidDataEntity>> {
    // Set default pagination values
    options.page = options.page || 1;
    options.limit = options.limit || 200;
        const currentFinancialYear = await this.financialYearRepository.findOne({
      where: { is_current: true },
    });


    return await this.paidAndNonPaidDataRepository.findPaidUsers(currentFinancialYear?.financial_year_range, options,value,searchOn);
  }

  /**
   * Get only non-paid users data
   * @param financialYearRange Optional financial year filter
   * @param options Pagination options
   * @returns Paginated non-paid users data
   */
  async getNonPaidUsersData( options: PaginationOptions = {},value?: string,searchOn?: string): Promise<PaginatedResult<PaidAndNonPaidDataEntity>> {
    // Set default pagination values
    options.page = options.page || 1;
    options.limit = options.limit || 200;

        const currentFinancialYear = await this.financialYearRepository.findOne({
      where: { is_current: true },
    });


    return await this.paidAndNonPaidDataRepository.findNonPaidUsers(currentFinancialYear?.financial_year_range, options,value,searchOn);
  }

  /**
   * Update paid/non-paid data for a specific property when payment is created or deleted
   * @param propertyId The property ID to update
   * @param financialYearRange Optional financial year range (defaults to current year)
   * @returns Updated record or null if not found
   */
  async updatePropertyPaidData(propertyId: string, financialYearRange?: string) {
    try {
      // Get current financial year if not provided
      if (!financialYearRange) {
        const currentFinancialYear = await this.financialYearRepository.findOne({
          where: { is_current: true },
        });
        financialYearRange = currentFinancialYear?.financial_year_range;
      }

      if (!financialYearRange) {
        this.logger.error('No financial year found for updating paid data');
        return null;
      }

      // Check if record exists for this property and financial year
      const existingRecord = await this.paidAndNonPaidDataRepository.findByPropertyAndFinancialYear(
        propertyId,
        financialYearRange
      );

      if (!existingRecord) {
        this.logger.warn(`No paid/non-paid data record found for property ${propertyId} in year ${financialYearRange}`);
        return null;
      }

      // Get property details for owner name
      const property = await this.propertyMasterRepository.findOne({
        where: { property_id: propertyId },
        relations: ['property_owner_details', 'property_owner_details.owner_type'],
      });

      if (!property) {
        this.logger.error(`Property ${propertyId} not found`);
        return null;
      }

      // Get owner name (prioritize स्वत: owner type, fallback to any available owner)
      let ownerName = 'Unknown';
      if (property.property_owner_details && property.property_owner_details.length > 0) {
        const preferredOwner = property.property_owner_details.find(
          owner => owner.owner_type?.owner_type === 'स्वत:'
        ) || property.property_owner_details[0];

        ownerName = preferredOwner.name || 'Unknown';
      }

      // Calculate total tax from WarshikKar entity for the financial year
      let totalTax = 0;
      const warshikKarData = await this.warshikKarRepository.findOne({
        where: {
          property: { property_id: propertyId },
          financial_year: financialYearRange,
          status: 'active'
        },
        relations: ['property']
      });

      if (warshikKarData) {
        totalTax = Number(warshikKarData.total_tax) || 0;
      }

      // Calculate paid amount from all payments for this property
      const payments = await this.paymentInfoRepository.find({
        where: { property: { property_id: propertyId } },
        relations: ['property']
      });

      let paidAmount = 0;
      if (payments && payments.length > 0) {
        paidAmount = payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
      }

      const remainingAmount = totalTax - paidAmount;
      const isPaid = paidAmount > 0;

      // Update the existing record
      const updatedRecord = await this.paidAndNonPaidDataRepository.updateData(
        existingRecord.paid_and_non_paid_data_id,
        {
          name: ownerName,
          total_tax: totalTax,
          paid: paidAmount,
          remaining: remainingAmount,
          is_paid: isPaid
        }
      );

      this.logger.log(`Updated paid/non-paid data for property ${propertyId}: paid=${paidAmount}, remaining=${remainingAmount}, isPaid=${isPaid}`);

      return updatedRecord;

    } catch (error) {
      this.logger.error(`Error updating paid data for property ${propertyId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update paid/non-paid data for multiple properties
   * @param propertyIds Array of property IDs to update
   * @param financialYearRange Optional financial year range
   * @returns Array of updated records
   */
  async updateMultiplePropertiesPaidData(propertyIds: string[], financialYearRange?: string) {
    const results = [];

    for (const propertyId of propertyIds) {
      try {
        const result = await this.updatePropertyPaidData(propertyId, financialYearRange);
        results.push({ propertyId, success: true, data: result });
      } catch (error) {
        results.push({ propertyId, success: false, error: error.message });
      }
    }

    return results;
  }

  /**
   * Batch update paid/non-paid data for multiple properties (async processing)
   * @param propertyIds Array of property IDs to update
   * @param financialYearRange Optional financial year range
   * @returns Promise that resolves when batch processing starts
   */
  async batchUpdatePropertiesPaidData(propertyIds: string[], financialYearRange?: string) {
    this.logger.log(`Starting batch update for ${propertyIds.length} properties`);

    // Process in background without blocking the response
    setImmediate(async () => {
      try {
        const results = await this.updateMultiplePropertiesPaidData(propertyIds, financialYearRange);
        const successCount = results.filter(r => r.success).length;
        const errorCount = results.filter(r => !r.success).length;

        this.logger.log(`Batch update completed: ${successCount} successful, ${errorCount} failed`);
      } catch (error) {
        this.logger.error(`Batch update failed: ${error.message}`);
      }
    });

    return {
      message: `Batch update started for ${propertyIds.length} properties`,
      propertyCount: propertyIds.length
    };
  }
}
