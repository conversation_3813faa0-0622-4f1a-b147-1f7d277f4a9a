import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddedCascade1741775748627 implements MigrationInterface {
  name = 'AddedCascade1741775748627';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "common_fields_of_property" DROP CONSTRAINT "FK_a4dee4e94aee71ce01ff0919c39"`,
    );
    await queryRunner.query(
      `ALTER TABLE "common_fields_of_property" ADD CONSTRAINT "FK_a4dee4e94aee71ce01ff0919c39" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "common_fields_of_property" DROP CONSTRAINT "FK_a4dee4e94aee71ce01ff0919c39"`,
    );
    await queryRunner.query(
      `ALTER TABLE "common_fields_of_property" ADD CONSTRAINT "FK_a4dee4e94aee71ce01ff0919c39" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
