import React from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useTranslation } from "react-i18next";

export interface ConfirmScreenProps {
  isOpen: boolean;
  toggle: () => void;
  itemName: string;
  onConfirm: () => void;
}

const ConfirmPopUpScreen: React.FC<ConfirmScreenProps> = ({
  isOpen,
  toggle,
  itemName,
  onConfirm,
}) => {
  const { t } = useTranslation();

  const handleClose = () => {
    toggle();
  };

  const handleConfirm = () => {
    onConfirm();
    handleClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[450px] bg-white">
        <DialogHeader>
          <DialogTitle>
            {t("confirmsreen.title")}
          </DialogTitle>
          <DialogDescription className="flex justify-center">
            {t("confirmsreen.description", { itemName })}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="!justify-center">
          <Button onClick={handleClose}>{t("confirmsreen.no")}</Button>
          <Button className="bg-green-500 hover:bg-green-600" onClick={handleConfirm}>
            {t("confirmsreen.yes")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmPopUpScreen;
