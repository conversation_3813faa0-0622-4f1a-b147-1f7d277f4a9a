import { Repository } from 'typeorm';
import { Master_ghanKachra_rateEntity, ReassessmentRange } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { NotFoundException } from '@nestjs/common';
import { UpdateUsageSubTypeDto } from 'src/tax_Masters/ghan_kachara_master/dto/update-usage-sub-type.dto';
import { UsageSubMasterRepository } from './usage-sub-master.repository';

export class MasterGhanKachraRateRepository extends Repository<Master_ghanKachra_rateEntity> {
  constructor(
    @InjectRepository(Master_ghanKachra_rateEntity)
    private readonly masterGhanKachraRateRepository: Repository<Master_ghanKachra_rateEntity>,
    private readonly usageSubMasterRepository: UsageSubMasterRepository,

  ) {
    super(
      masterGhanKachraRateRepository.target,
      masterGhanKachraRateRepository.manager,
      masterGhanKachraRateRepository.queryRunner,
    );
  }
  async getAllWithUsageSubType(): Promise<Master_ghanKachra_rateEntity[]> {
    return this.masterGhanKachraRateRepository.find({
      relations: ['UsageSubType', 'reassessmentRange'],  // Include property class and reassessment range relations
    });
  }
  async updateGhanKachra(
    id: string,
    data: UpdateUsageSubTypeDto
  ): Promise<{ message: string; data: Master_ghanKachra_rateEntity }> {
    // Check if the record exists
    console.log("2")

    const existingRecord = await this.masterGhanKachraRateRepository.findOne({
      where: { ghanKachra_rate_id: id },
      relations: ['UsageSubType', 'reassessmentRange'], // Ensure the relations are fetched
    });

    if (!existingRecord) {
      return {
        message: 'No record found to update',
        data: undefined,
      };
    }

    if (data.usage_sub_type_master_id) {
      // Fetch the new UsageSubType entity
      const newUsageSubType = await this.usageSubMasterRepository.findOne({
        where: { usage_sub_type_master_id: data.usage_sub_type_master_id }
      });

      // If the new UsageSubType does not exist, throw an error
      if (!newUsageSubType) {
        return {
          message: 'New Usage Sub Type not found',
          data: undefined,
        };
      }

      // Assign the new UsageSubType to the existing record
      existingRecord.UsageSubType = newUsageSubType;
    }

    // Update the properties of the existing record
    Object.assign(existingRecord, data);

    // Save the updated record back to the database
    await this.masterGhanKachraRateRepository.save(existingRecord);


    return {
      message: 'Ghan Kachra rate updated successfully',
      data: existingRecord,
    };
  }

  async generateForNewFinancialYear(newFinancialYear: string, currentReassessmentRange: ReassessmentRange): Promise<{ message: string; data?: Master_ghanKachra_rateEntity[] }> {
    // Fetch all existing records
    const existingRecords = await this.getAllWithUsageSubType();

    if (existingRecords.length === 0) {
      return {
        message: 'No records found to duplicate',
      };
    }

    const duplicatedRecords: Master_ghanKachra_rateEntity[] = [];

    for (const record of existingRecords) {
      // Create a new record based on the existing one
      const newRecord = new Master_ghanKachra_rateEntity();
      newRecord.financial_year = newFinancialYear; // Keep for backward compatibility
      newRecord.value = record.value;
      newRecord.status = record.status;
      newRecord.UsageSubType = record.UsageSubType;
      newRecord.usage_type = record.usage_type;
      newRecord.reassessmentRange = currentReassessmentRange;

      // Save the new record to the database
      const savedRecord = await this.masterGhanKachraRateRepository.save(newRecord);
      duplicatedRecords.push(savedRecord);
    }

    return {
      message: 'Records generated successfully for the new reassessment range',
      data: duplicatedRecords,
    };
  }



}
