import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogClose, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { X } from "lucide-react";
import React, { useState } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select";
import { useTranslation } from "react-i18next";
import { Form, FormItem, FormLabel, FormControl, FormMessage, FormField } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const CommonDialog = ({ isOpen, onClose, onSubmit }) => {
  const { t } = useTranslation();

  const [selectedSearchType, setSelectedSearchType] = useState("मालमत्ता धाराकाचे नाव");

  const form = useForm({
    defaultValues: {
      yearRange: "2022-2023",
      searchType: "मालमत्ता धाराकाचे नाव",
      propertyNumber: "",
    },
  });
  const tableHeaders = [
    "निवडा ",
    "मालमत्ता क्रमांक",
    "मालमत्ता धारक",
    "प्रभाग",
    "मोबाईल",
  ];

  const { resetField } = form; // Destructure resetField from the form object

  const yearRanges = ["2020-2021", "2021-2022", "2022-2023", "2023-2024"];
  const searchTypes = ["मालमत्ता धाराकाचे नाव", "मिळकत क्रमांक", "भोगवटाधारकाचे नाव"];

  const handleSubmit = (data: any) => {
    console.log("Submitted data:", data);
    onSubmit(data);
    // onClose();
  };

  const handleDialogClose = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen}>
      <DialogContent className="sm:max-w-[800px] bg-white">
        <DialogHeader>
          <DialogTitle className="text-left">Advance Search</DialogTitle>
        </DialogHeader>
        <DialogClose
          onClick={handleDialogClose}
          className="w-6 h-6 absolute top-[12px] right-[12px] z-10"
        >
          <X className="w-6 h-6 z-0" />
        </DialogClose>
        <div className="flex">
          <Form {...form}>
            <form
              id="form-file-upload"
              className="!w-full !h-auto bg-lightGray-0 rounded-borderRadius10"
              onSubmit={form.handleSubmit(handleSubmit)}
            >
              <div className="grid md:grid-cols-2 gap-x-3">
                <FormField
                  control={form.control}
                  name="yearRange"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("taxDemand.selectRange")}</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            {field.value || t("taxDemand.selectRange")}
                          </SelectTrigger>
                          <SelectContent>
                            {yearRanges.map((range, index) => (
                              <SelectItem key={index} value={range}>
                                {range}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <br />
                <FormField
                  control={form.control}
                  name="searchType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>पर्याय निवडा</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => {
                            field.onChange(value);
                            setSelectedSearchType(value);
                            resetField("propertyNumber");
                          }}
                        >
                          <SelectTrigger>
                            {field.value || t("taxDemand.selectRange")}
                          </SelectTrigger>
                          <SelectContent>
                            {searchTypes.map((type, index) => (
                              <SelectItem key={index} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="propertyNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{selectedSearchType || "Property Number"}</FormLabel>
                      <FormControl>
                        <Input className="mt-1"
                          {...field}
                          placeholder={selectedSearchType}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="mt-2">
                <Button className="w-fit min-w-44 float-end capitalize" type="submit">
                  Submit
                </Button>
              </div>
            </form>
          </Form>
        </div>


        <div className="mb-5 mt-4 overflow-x-auto">
          <Table className="w-full text-sm text-left text-black border border-[#e5e7eb] income-tax-eight-table ">
            <TableHeader className="uppercase bg-gray-100  border-[#e5e7eb] font-bold">
              <TableRow className="">
                {tableHeaders.map((header) => (
                  <TableHead
                    key={header}
                    className="py-3 px-6 border-b border-[#e5e7eb] bg-[#f3f4f6] font-semibold text-black text-center text-base"
                  >
                    {t(header)}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* {namanuDetail?.tax_propertywise?.map((item, index) => ( */}
              <TableRow key={"1"} className="text-center border-b border-gray-500">
                <TableCell className="py-3 px-6">                           <input type="checkbox" name="" id="" />
                </TableCell>
                <TableCell className="py-3 px-6">
                SNP404947
                </TableCell>
                <TableCell className="py-3 px-6">
                अरुण दिनकर माने
                </TableCell>
                <TableCell className="py-3 px-6">
                प्रभाग १
                </TableCell>
                <TableCell className="py-3 px-6">
                9764773668
                </TableCell>

              </TableRow>
              {/* ))} */}
            </TableBody>
          </Table>
          <div>
            <Button onClick={onClose} className="w-fit min-w-44 float-end capitalize mt-4" type="submit">
              पहा
            </Button>
            <Button variant="outline" className="w-fit min-w-44 float-end capitalize mr-4 mt-4">
              रीसेट
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CommonDialog;
