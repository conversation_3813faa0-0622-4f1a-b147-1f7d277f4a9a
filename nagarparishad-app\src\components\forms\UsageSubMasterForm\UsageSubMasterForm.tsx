import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  UsageSubMasterApiResponse,
  UsageSubUpdateObject,
  UsageSubObjectInterface,
} from "@/model/usagesub-master";
import { toast, useToast } from "@/components/ui/use-toast";
import Api from "@/services/ApiServices";
import { GlobalContext } from "@/context/GlobalContext";
import { useUsageMasterController } from "@/controller/master/UsageMasterController";
import { UsageObjectInterface } from "@/model/usage-master";
import { Loader2 } from "lucide-react";
import { useUsageSubController } from "@/controller/master/UsageSubController";

interface UsageSubMasterFormInterface {
  btnTitle: string;
  editData?: UsageSubObjectInterface;
}

const UsageSubMasterForm = ({ btnTitle, editData }: any) => {
  const { t } = useTranslation();
  const { setOpen, refreshUsageSubList, setRefreshUsageSubList } =
    useContext(GlobalContext);
  const [loader, setLoader] = useState(false);
  const schema = z.object({
    subUsage: z.string().trim().min(1, t("errorsRequiredField")),
    usage: z.string().trim().min(1, t("errorsRequiredField")),
  });
  const dynamicValues = {
    name: t("usage_sub.UsageLabel"),
  };
  const { usageList } = useUsageMasterController();
  const { createUsageSub, updateUsageSub } = useUsageSubController();

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),

    defaultValues: {
      subUsage: editData?.subUsage || "",
      usage: editData?.usage?.usage_type_id || "",
    },
  });
  const {
    formState: { errors },
    reset,
  } = form;

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();
    const dataResponse = {
      subUsage: data?.subUsage,
      usage: data?.usage,
    };
    if (editData?.usageSub_id !== undefined) {
      setLoader(true);
      updateUsageSub(
        {
          subUsageId: editData?.usageSub_id,
          subUsageData: dataResponse,
        },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            form.reset({ usage: "", subUsage: "" });
            setOpen(false);
            setRefreshUsageSubList((pre) => !pre);
            setLoader(false);
          },
          onError: (error) => {
            toast({
              title: error.message,
            });
            setLoader(false);
          },
        },
      );
    } else {
      setLoader(true);

      createUsageSub(dataResponse, {
        onSuccess: () => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          form.reset({ usage: "", subUsage: "" });
          setOpen(false);
          setLoader(false);
        },
        onError: (error) => {
          toast({
            title: error.message,
          });
          setLoader(false);
        },
      });
    }
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        subUsage: editData?.subUsage || "",
        usage: editData?.usage?.usage_type_id || "",
      });
    } else {
      form.reset({
        subUsage: "",
        usage: "",
      });
    }
  }, [editData]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full md:w-full"
        >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="grid-cols-subgrid">
              <FormField
                control={form.control}
                name="usage"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("usage.UsageLabel")}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {(usageList &&
                            usageList?.length > 0 &&
                            usageList.filter(
                              (usage: any) => usage.usage_type_id === field.value,
                            )[0]?.usage_type) ||
                            "Select A Type"}
                        </SelectTrigger>
                      </FormControl>

                      <SelectContent>
                        {usageList && usageList?.length > 0 && (
                          <>
                            {usageList.map((usage: UsageObjectInterface) => (
                              <SelectItem
                                value={usage.usage_type_id}
                                key={usage.createdAt}
                              >
                                {usage.usage_type}
                              </SelectItem>
                            ))}
                          </>
                        )}
                      </SelectContent>
                    </Select>
                    {errors.usage && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="grid-cols-subgrid">
            <FormField
                control={form.control}
                name="subUsage"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("usage_sub.UsageLabel")}</FormLabel>
                    <FormControl className="mt-1">
                      <Input
                        placeholder={t("usage_sub.UsageLabel")}
                        {...field}
                      />
                    </FormControl>
                    {errors.subUsage && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
              <div className="grid-cols-subgrid  mb-1 ml-4 max-md:flex max-md:justify-end  pt-[32px] ">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
          </div>
        
        </form>
      </Form>
    </>
  );
};

export default UsageSubMasterForm;
