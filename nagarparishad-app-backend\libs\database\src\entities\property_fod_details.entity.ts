import {
  BaseEntity,
  ManyToOne,
  JoinC<PERSON>umn,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PropertyEntity } from './property.entity';
import { ReassessmentRange } from './reassesment_range.entity';

@Entity('property_fod_details')
export class Property_Fod_Details_Entity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  property_fod_details_id: string;

  // Foreign key to PropertyEntity
  @ManyToOne(() => PropertyEntity, (property) => property.property_id, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

  // Column to store the new property number generated
  @Column({ type: 'varchar', array: true, nullable: false })
  new_property_numbers: string[];

  // Column for reason or share details
  @Column({ type: 'text', nullable: true })
  reason: string;

  // Column for image folder path
  @Column({ type: 'varchar', array: true, nullable: true })
  photo_image_paths: string[];

  // Column for document image path (single path or array of paths)
  @Column({ type: 'varchar', nullable: true })
  document_image_path: string[];

  @Column({ type: 'varchar', nullable: true })
  user_email_id: string[];
  // Foreign key to ReassessmentRange
  @ManyToOne(() => ReassessmentRange, (reassessmentRange) => reassessmentRange.reassessment_range_id, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'reassessment_range_id' })
  reassessmentRange: ReassessmentRange;
  @Column({ type: 'varchar', nullable: false })
  year: string;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
