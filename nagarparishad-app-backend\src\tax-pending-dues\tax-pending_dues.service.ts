import {
    Injectable,
    InternalServerErrorException,
    NotAcceptableException,
    NotFoundException,
  } from '@nestjs/common';
  import { PropertyMasterRepository, TaxPendingDuesRepository ,ImportPropertyMasterRepository} from 'libs/database/repositories';

  import * as XLSX from 'xlsx';
  import * as ExcelJS from 'exceljs';


  import {
    CreateTaxPendingDuesDto,
    TaxPendingDuesDto,
    UpdateTaxPendingDuesDto,
  } from './dto/TaxPendingDues.dto';

  @Injectable()
  export class TaxPendingDuesService {
    constructor(
      private readonly taxPendingDuesRepository: TaxPendingDuesRepository,
      private readonly propertyMasterRepository: PropertyMasterRepository,
      private readonly ImportPropertyMasterRepository: ImportPropertyMasterRepository,


    ) {}
    async processFile(file: Express.Multer.File) {
        try {
          // console.log(" insodefileeeein in")
          let All_rows = [];
          const marathiToEnglishDigits = {
            '०': '0', '१': '1', '२': '2', '३': '3', '४': '4',
            '५': '5', '६': '6', '७': '7', '८': '8', '९': '9'
          };

          // Function to convert Marathi digits to English digits
          function convertMarathiToEnglishDigits(marathiNumber) {
            return marathiNumber.split('').map(char => marathiToEnglishDigits[char] || char).join('');
          }

          for (let i = 0; i <= 0; i++) {
            const workbook = XLSX.read(file.buffer, { type: 'buffer' });
            const sheetName = workbook.SheetNames[i];
            const worksheet = workbook.Sheets[sheetName];
            const parsedData = XLSX.utils.sheet_to_json(worksheet, { defval: '' });

            const errors: string[] = [];
            if (parsedData.length <= 1) {
              errors.push('File does not contain enough rows.');
              return {
                error: true,
                message: 'File Validation Failed',
                errorsArray: errors,
              };
            }

            // Skip the first four rows
            const currentTime = new Date().toISOString();
            const dataStartingFromRow5 = parsedData;
            let sr_no: number = 1;
            for (const rowData of dataStartingFromRow5) {
              let new_row = {
                sheetIndex:rowData['अ.क्र.'],
                old_propertyNumber: convertMarathiToEnglishDigits(rowData['मिळकत क्रमांक']),
                surveyNumber: rowData['सर्वे नंबर'],
                streetName: rowData['रस्त्याचे नाव'],
                propertyHolderName: rowData['मिळकत धारकाचे नाव'],
                possessionHolderName: rowData['भोगवटा धारक नाव'],
                ward: rowData["वॉर्डचे नाव"],
                all_property_tax_sum: parseFloat(rowData['घरपट्टी']) || 0,
                tax_type_1: 0, // Initialize tax_type_1
                tax_type_6: parseFloat(rowData['दिवाबत्ती कर']) || 0,
                tax_type_7: parseFloat(rowData['आरोग्य कर']) || 0,
                tax_type_8: parseFloat(rowData['पडसर']) || 0,
                tax_type_9: parseFloat(rowData['दंड रक्कम']) || 0,
                tax_type_5: parseFloat(rowData['शास्ती']) || 0,
                tax_type_4: parseFloat(rowData['घनकचरा शुल्क']) || 0,
                tax_type_2: 0,
                tax_type_3: 0,
                tax_type_10: 0,
                total: parseFloat(rowData['एकूण']) || 0,
              };

              All_rows.push(new_row);
              sr_no++;
            }

            // Save all rows to the database
            let savedCount = 0;
            let skippedCount = 0;

            for (const row of All_rows) {
              // Only process rows with non-empty property number
              if (row.old_propertyNumber.trim() !== '') {
                // The saveData method now checks for total > 0 internally
                const result = await this.taxPendingDuesRepository.saveData(row);
                if (result) {
                  savedCount++;
                } else {
                  skippedCount++;
                }
              }
            }

            console.log(`Processed ${All_rows.length} rows: ${savedCount} saved, ${skippedCount} skipped due to no pending dues`);

          }

          return { message: 'File processed successfully',data:[] };
        } catch (error) {
          throw new InternalServerErrorException('Error processing file');
        }
      }

      async exportCsvData(wardNumber: string): Promise<Buffer> {
        const taxPendingDuesData = await this.taxPendingDuesRepository.find();

        const propertyMasterData = await this.ImportPropertyMasterRepository.find();

        if (!taxPendingDuesData || taxPendingDuesData.length === 0) {
          throw new NotFoundException(
            `No data found for ward number ${wardNumber} in TaxPendingDuesRepository`,
          );
        }

        if (!propertyMasterData || propertyMasterData.length === 0) {
          throw new NotFoundException(
            `No data found for ward number ${wardNumber} in PropertyMasterRepository`,
          );
        }
        const matchedData = taxPendingDuesData.map((taxDues) => {
          const matchingProperty = propertyMasterData.find(
            (property) => property.old_propertyNumber === taxDues.old_propertyNumber,
          );
          if(matchingProperty){
          return {
            old_propertyNumber: taxDues.old_propertyNumber,
            propertyHolderName: taxDues.propertyHolderName,
            possessionHolderName: taxDues.possessionHolderName,
            owner_name: matchingProperty ? matchingProperty.owner_name : '',
            bhogawat_owner_name: matchingProperty ? matchingProperty.bhogawat_owner_name : '',
            property_id: matchingProperty ? matchingProperty.property_id : '',
          };
        }else{
          return null;
        }
        }).filter((item)=> item!==null );

        if (matchedData.length === 0) {
          throw new NotFoundException(
            `No matching data found for ward number ${wardNumber}`,
          );
        }

        const dataToExport = matchedData.map((item) => ({
          old_propertyNumber: item.old_propertyNumber,
          propertyHolderName: item.propertyHolderName,
          possessionHolderName: item.possessionHolderName,
          owner_name: item.owner_name,
          bhogawat_owner_name: item.bhogawat_owner_name,
          property_id: item.property_id,
        }));

        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Tax Pending dues');

        worksheet.columns = [
          { header: 'जुना मालमत्ता क्रमांक', key: 'old_propertyNumber' },
          { header: 'मालमत्ता धारकाचे नाव (थक बाकी )', key: 'propertyHolderName' },
          { header: 'भोगवटा धारकाचे नाव (थक बाकी )', key: 'possessionHolderName' },
          { header: 'मालकाचे नाव (मालमत्ता System)', key: 'owner_name' },
          { header: 'भोगवटा धारकाचे नाव (मालमत्ता System)', key: 'bhogawat_owner_name' },
          { header: 'मालमत्ता आयडी', key: 'property_id' },
        ];

        worksheet.addRows(dataToExport);
        worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
          if (rowNumber === 1) {
            row.eachCell((cell) => {
              cell.font = { bold: true };
            });
          }
        });
        // Read the CSV file into a buffer
        const excelBuffer: any = await workbook.xlsx.writeBuffer();

        return excelBuffer;
      }


  }
