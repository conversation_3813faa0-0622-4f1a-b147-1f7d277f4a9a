import React from "react";
import AsyncSelect from "react-select/async";

interface ColourOption {
  value: string;
  label: string;
}
interface GlobalAsyncSelectProps {
  colourOptions: ColourOption[];
  [key: string]: any;
}

const GlobalAsyncSelect: React.FC<GlobalAsyncSelectProps> = ({
  colourOptions,
  ...props
}) => {
  const filterColors = (inputValue: string) => {
    return colourOptions.filter((i) =>
      i.label.toLowerCase().includes(inputValue.toLowerCase())
    );
  };
  const loadOptions = (
    inputValue: string,
    callback: (options: ColourOption[]) => void
  ) => {
    setTimeout(() => {
      callback(filterColors(inputValue));
    }, 1000);
  };
  const customStyles = {
    control: (provided: any, state: any) => ({
      ...provided,
      display: "flex",
      height: "40px",
      width: "100%",
      marginTop: "4px",
      borderRadius: "0.375rem",
      borderWidth: state.isFocused ? "2px" : "1.5px",
      borderColor: state.isFocused ? "black" : "rgba(0, 0, 0, 0.42)",
      backgroundColor: "#fff",
      fontSize: "0.875rem",
      boxShadow: "none",
      "&:hover": {
        borderColor: "none",
      },
    }),
    placeholder: (provided: any) => ({
      ...provided,
      color: "#94A3B7",
    }),
    singleValue: (provided: any) => ({
      ...provided,
      color: "#2D3748",
      backgroundColor: "#fff",
      lineClamp: 1,
      textOverflow: "ellipsis",
      whiteSpace: "nowrap",
      overflow: "hidden",
    }),
    menu: (provided: any) => ({
      ...provided,
      zIndex: 20,
      backgroundColor: "#fff",
      borderRadius: "0.375rem",
      padding: "5px",
    }),
    menuPortal: (base: any) => ({
      ...base,
      zIndex: 9999,
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected ? "#ADD8E6" : "transparent",
      borderRadius: "0.375rem",
      cursor: "pointer",
      "&:hover": {
        backgroundColor: state.isSelected
          ? "#ADD8E6"
          : "rgba(173, 216, 230, 0.3)",
      },
      border: "1px solid lightGray",
      marginTop: "2px",
    }),
  };

  return (
    <AsyncSelect
      cacheOptions
      loadOptions={loadOptions}
      defaultOptions={colourOptions}
      styles={customStyles}
      {...props}
    />
  );
};

export default GlobalAsyncSelect;
