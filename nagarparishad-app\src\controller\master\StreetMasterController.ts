import { StreetMasterObject, StreetSendApiObj } from "@/model/street-master";
import Api from "../../services/ApiServices";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

const fetchStreets = async () => {
  const response = await Api.getAllStreet();
  return response.data;
};

const createStreet = async (streetData: StreetSendApiObj) => {
  return new Promise((resolve, reject) => {
    Api.createStreet(streetData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateStreet = async ({ streetId, streetData }) => {
  return new Promise((resolve, reject) => {
    Api.updateStreet(streetId, streetData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteStreet = async (streetId: string) => {
  return new Promise((resolve, reject) => {
    Api.deleteStreet(streetId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useStreetMasterController = () => {
  const queryClient = useQueryClient();

  const { data: streetListResponse, error, isLoading:streetLoading } = useQuery({
    queryKey: ["streetmaster"],
    queryFn: fetchStreets,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  if (error) {
    console.error("Error fetching streets:", error);
  }

  const createStreetMutation = useMutation({
    mutationFn: createStreet,
    onMutate: async (newStreet) => {
      await queryClient.cancelQueries({ queryKey: ["streetmaster"] });
      const previousStreet = queryClient.getQueryData(["streetmaster"]);
      queryClient.setQueryData(["streetmaster"], (old: StreetMasterObject) => {
        const updatedData = [newStreet, ...old];
        // Log the updated data
        return updatedData;
      });
      console.log(JSON.stringify(previousStreet));

      return { previousStreet };
    },
    onError: (err, newWard, context) => {
      queryClient.setQueryData(["streetmaster"], context.previousStreet);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["streetmaster"] });
    },
  });

  const updateStreetMutation = useMutation({
    mutationFn: updateStreet,
    onMutate: async ({ streetId, streetData }) => {
      await queryClient.cancelQueries({ queryKey: ["streetmaster"] });

      const previousStreets = queryClient.getQueryData(["streetmaster"]);

      queryClient.setQueryData(["streetmaster"], (old: any) => {
        const updatedZones = old?.map((street: any) =>
          street.streetOrRoadId === streetId
            ? { ...street, ...streetData }
            : street,
        );

        console.log("Updated query data:", updatedZones);
        return updatedZones;
      });

      return { previousStreets };
    },
    onError: (err, context: any) => {
      queryClient.setQueryData(["streetmaster"], context.previousStreets);
      console.error("Error updating zone:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["streetmaster"] });
    },
  });

  const deleteStreetMutation = useMutation({
    mutationFn: deleteStreet,
    onMutate: async (streetId) => {
      await queryClient.cancelQueries({ queryKey: ["streetmaster"] });

      const previousStreets = queryClient.getQueryData(["streetmaster"]);

      queryClient.setQueryData(["streetmaster"], (old: any) => {
        const updatedWards = old.filter(
          (street: any) => street.streetOrRoadId !== streetId,
        );
        return updatedWards;
      });
      return { previousStreets };
    },
    onError: (err, wardId, context) => {
      queryClient.setQueryData(["streetmaster"], context.previousStreets);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["streetmaster"] });
    },
  });

  return {
    streetList: streetListResponse || [],
    streetLoading,
    createStreet: createStreetMutation.mutate,
    updateStreet: updateStreetMutation.mutate,
    deleteStreet: deleteStreetMutation.mutate,
  };
};
