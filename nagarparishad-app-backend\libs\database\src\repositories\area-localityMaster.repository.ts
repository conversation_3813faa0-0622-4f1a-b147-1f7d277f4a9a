import { Repository } from 'typeorm';
import { AreaLocalityMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class AreaLocalityMasterRepository extends Repository<AreaLocalityMasterEntity> {
  constructor(
    @InjectRepository(AreaLocalityMasterEntity)
    private readonly areaLocalityMasterRepository: Repository<AreaLocalityMasterEntity>,
  ) {
    super(
      areaLocalityMasterRepository.target,
      areaLocalityMasterRepository.manager,
      areaLocalityMasterRepository.queryRunner,
    );
  }

  async saveArea(input: {
    areaName: string;
  }): Promise<AreaLocalityMasterEntity> {
    let area = this.areaLocalityMasterRepository.create(input);
    area = await this.areaLocalityMasterRepository.save(area);
    return area;
  }

  async findAllArea(): Promise<AreaLocalityMasterEntity[]> {
    return await this.areaLocalityMasterRepository
      .createQueryBuilder('area_master')
      .orderBy('area_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string): Promise<AreaLocalityMasterEntity> {
    return await this.areaLocalityMasterRepository
      .createQueryBuilder('area_master')
      .where('area_id = :id', { id })
      .getOne();
  }

  async updateArea(
    id: string,
    input: {
      areaName?: string;
    },
  ) {
    return await this.areaLocalityMasterRepository
      .createQueryBuilder('area_master')
      .update(AreaLocalityMasterEntity)
      .set(input)
      .where('area_id = :area_id', { area_id: id })
      .execute();
  }

  async deleteArea(id: string) {
    return await this.areaLocalityMasterRepository
      .createQueryBuilder('area_master')
      .softDelete()
      .where('area_id = :area_id', { area_id: id })
      .execute();
  }
}
