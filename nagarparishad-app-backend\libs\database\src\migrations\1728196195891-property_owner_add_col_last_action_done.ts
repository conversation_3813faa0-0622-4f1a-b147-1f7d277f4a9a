import { MigrationInterface, QueryRunner } from "typeorm";

export class PropertyOwnerAddColLastActionDone1728196195891 implements MigrationInterface {
    name = 'PropertyOwnerAddColLastActionDone1728196195891'

    public async up(queryRunner: QueryRunner): Promise<void> {
        
        await queryRunner.query(`ALTER TABLE "property_owner_details" ADD "last_action_done" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP COLUMN "last_action_done"`);
       
    }

}
