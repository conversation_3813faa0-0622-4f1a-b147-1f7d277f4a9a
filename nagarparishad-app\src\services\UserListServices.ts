import axios from "axios";
import {
  UserRegisterListObj,
  UserRegisterUpdateApiResponse,
} from "@/model/user-register";
import {
  GET_ALLUSERLIST,
  UPDATE_USER,
  DELETE_USER,
} from "@/constant/utils/userListUtils";

const baseURL = import.meta.env.VITE_APP_BASE_URL;

const VITE_BASE_URL = baseURL;

// Define the API endpoints
const REACT_APP_GET_ALLUSERS = VITE_BASE_URL + GET_ALLUSERLIST;
// const REACT_APP_GET_ROLE = (roleId: number) => `${VITE_BASE_URL}${GET_ALLUSERLIST}${roleId}`;
const REACT_APP_UPDATE_ROLE = VITE_BASE_URL + UPDATE_USER;
const REACT_APP_DELETE_USER = VITE_BASE_URL + DELETE_USER;

const REACT_APP_GET_ALL_COLLECTORS = `${VITE_BASE_URL}/v1/collector-master`;
const REACT_APP_UPDATE_COLLECTOR = `${VITE_BASE_URL}/v1/collector-master`;
const REACT_APP_DELETE_COLLECTOR = `${VITE_BASE_URL}/v1/collector-master`;

class UserListApi {
  static getStoredToken = () => {
    const UserToken = JSON.parse(localStorage.getItem("AccessToken") || "{}");
    return UserToken !== undefined ? UserToken : false;
  };

  // Get All Users
  static getAllUsers(): Promise<any> {
    return axios
      .get(`${REACT_APP_GET_ALLUSERS}`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${UserListApi.getStoredToken()}`,
        },
      })
      .then((response) => response.data)
      .catch((error) => {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          console.error(error);
        }
      });
  }

  // Update User
  static updateUser = async (
    userId: string,
    userRegisterData: UserRegisterListObj,
    callback: (response: {
      status: boolean;
      data: UserRegisterUpdateApiResponse;
    }) => void
  ) => {
    try {
      const response = await axios.patch(
        REACT_APP_UPDATE_ROLE + userId,
        userRegisterData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${UserListApi.getStoredToken()}`,
          },
        }
      );
      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, data: err });
    }
  };

  //Delete User
  static deleteUser = async (
    userId: string,
    callback: (response: { status: boolean; data: any }) => void
  ) => {
    const url = new URL(`${REACT_APP_DELETE_USER}${userId}`);

    try {
      const response = await axios.delete(url.toString(), {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${UserListApi.getStoredToken()}`,
        },
      });

      if (response.status === 200) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err) {
      console.error(err); // Log the error for debugging
      callback({ status: false, data: err });
    }
  };

  static getAllCollectors = async (): Promise<any> => {
    try {
      const response = await axios.get(REACT_APP_GET_ALL_COLLECTORS, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${UserListApi.getStoredToken()}`,
        },
      });
      
      if (response.status == 200) {
        return { status: true, data: response.data.data };
      } else {
        return { status: false, data: response.data };
      }
    } catch (error) {
      console.error("Error fetching collectors:", error);
      throw error;
    }
  };

  static updateCollector = async (
    collectorId: string,
    updateData: Partial<any>
  ): Promise<any> => {
    try {
      const response = await axios.put(
        `${REACT_APP_UPDATE_COLLECTOR}/${collectorId}`,
        updateData,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${UserListApi.getStoredToken()}`,
          },
        }
      );
      return {status: true, data: response.data};
    } catch (error) {
      console.error("Error updating collector:", error);
      throw error;
    }
  };

  static deleteCollector = async (collectorId: string): Promise<void> => {
    try {
      await axios.delete(`${REACT_APP_DELETE_COLLECTOR}/${collectorId}`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${UserListApi.getStoredToken()}`,
        },
      });
    } catch (error) {
      console.error("Error deleting collector:", error);
      throw error;
    }
  };
}

export default UserListApi;
