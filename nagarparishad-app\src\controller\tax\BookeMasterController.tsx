import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import TaxListApi from "@/services/TaxServices";

interface BookData {
  book_id: string;
  book_number: string;
  value: number;
  status: string;
}

const fetchBooks = async () => {
  return new Promise((resolve, reject) => {
    TaxListApi.getBookMaster((response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const createBook = async (bookData: any) => {
  return new Promise((resolve, reject) => {
    TaxListApi.createBook(bookData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateBook = async ({ id, payload }: { id: string; payload: any }) => {
  return new Promise((resolve, reject) => {
    TaxListApi.updateBook(id, payload, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteBook = async (bookId: string) => {
  return new Promise((resolve, reject) => {
    TaxListApi.deleteBook(bookId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const fetchReceiptNumbers = async (bookNumber: string) => {
  return new Promise((resolve, reject) => {
    TaxListApi.getReceiptNumbers(bookNumber, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useBookController = () => {
  const queryClient = useQueryClient();

  const { data: bookData, isLoading: bookLoading } = useQuery({
    queryKey: ["bookMaster"],
    queryFn: fetchBooks,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
  });

  const createBookMutation = useMutation({
    mutationFn: createBook,
    onMutate: async (newBook) => {
      await queryClient.cancelQueries({ queryKey: ["bookMaster"] });
      const previousBooks = queryClient.getQueryData(["bookMaster"]);

      queryClient.setQueryData(["bookMaster"], (old: any) => {
        const updatedData = [newBook, ...old.data];
        return updatedData;
      });

      return { previousBooks };
    },
    onError: (err, newBook, context) => {
      queryClient.setQueryData(["bookMaster"], context.previousBooks);
      console.error("Error creating book:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["bookMaster"] });
    },
  });

  const updateBookMutation = useMutation({
    mutationFn: updateBook,
    onMutate: async ({ id, payload }) => {
      await queryClient.cancelQueries({ queryKey: ["bookMaster"] });

      const previousBooks = queryClient.getQueryData(["bookMaster"]);
      queryClient.setQueryData(["bookMaster"], (old: any) => {
        const updatedBooks = old?.data?.map((book: any) =>
          book.book_id === id ? { ...book, ...payload } : book
        );
        return updatedBooks;
      });

      return { previousBooks };
    },
    onError: (err, { id, payload }, context) => {
      queryClient.setQueryData(["bookMaster"], context.previousBooks);
      console.error("Error updating book:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["bookMaster"] });
    },
  });

  const deleteBookMutation = useMutation({
    mutationFn: deleteBook,
    onMutate: async (bookId) => {
      await queryClient.cancelQueries({ queryKey: ["bookMaster"] });

      const previousBooks = queryClient.getQueryData(["bookMaster"]);

      queryClient.setQueryData(["bookMaster"], (old: any) => {
        const updatedBooks = old?.data?.filter((book: any) => book.book_id !== bookId);
        return updatedBooks;
      });

      return { previousBooks };
    },
    onError: (err, bookId, context) => {
      queryClient.setQueryData(["bookMaster"], context.previousBooks);
      console.error("Error deleting book:", err);
    },
    onSuccess: () => {
      queryClient.setQueryData(["bookMaster"], (old: any) => {
        const updatedBooks = old?.data?.filter((book: any) => book.book_id !== bookId);
        return updatedBooks;
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["bookMaster"] });
    },
  });

  // const { data: receiptData, isLoading: receiptLoading, refetch: fetchReceipts } = useQuery({
  //   queryKey: ["receiptNumbers", ],
  //   queryFn: () => fetchReceiptNumbers(bookNumber),
  //   enabled: false, // Disable automatic fetching
  // });

  return {
    BookList: bookData?.data || [],
    bookLoading,
    createBook: createBookMutation.mutate,
    updateBook: updateBookMutation.mutate,
    deleteBook: deleteBookMutation.mutate,
  
  };
};
