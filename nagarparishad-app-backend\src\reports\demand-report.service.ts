import { Injectable } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import { InjectRepository } from '@nestjs/typeorm';
import { Financial_yearRepository, PaidDataRepository, PropertyMasterRepository, DemandRecordDataRepository, MilkatKareRepository, MilkatKarTaxeRepository, WarshikKarRepository } from 'libs/database/repositories';
import { AnnualKarAkaraniService } from 'src/annual-kar-akarani/annual-kar-akarani.service';
import { DemandReportData } from 'libs/database/entities/demandeReportData.entity';
import { TAX_TYPES } from 'libs/helpers/src/tax-types.helper';
import { Between, IsNull, Not } from 'typeorm';

@Injectable()
export class DemandReportService {
  constructor(
    private readonly propertyRepository: PropertyMasterRepository,
    private readonly annualKarAkaraniService: AnnualKarAkaraniService,
    private readonly paidDataRepository: PaidDataRepository,
    private readonly financialYearRepository: Financial_yearRepository,
    private readonly demandReportDataRepository: DemandRecordDataRepository,
    private readonly milkatKarRepository: MilkatKareRepository,
    private readonly warshikKarRepository: WarshikKarRepository,

    private readonly milkatKarTaxRepository: MilkatKarTaxeRepository,
  ) {}

  async exportDemandReportToExcel(): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Demand Report');

    const staticColumns = [
      { header: 'Property Number', key: 'property_number', width: 20 },
      { header: 'Financial Year', key: 'financial_year', width: 15 },
      { header: 'Status', key: 'status', width: 15 },
    ];

    const taxColumns = Object.entries(TAX_TYPES).flatMap(([taxKey, taxLabel]) => [
      { header: `${taxLabel} मागील`, key: `${taxKey}_prev_remaining`, width: 18 },
      { header: `${taxLabel} चालू`, key: `${taxKey}_curr_remaining`, width: 18 },
      { header: `${taxLabel} total`, key: `${taxKey}_remaining`, width: 18 },
      { header: `${taxLabel} मागील paid`, key: `${taxKey}_prev_paid`, width: 18 },
      { header: `${taxLabel} चालू paid`, key: `${taxKey}_curr_paid`, width: 18 },
      { header: `${taxLabel} paid total`, key: `${taxKey}_paid`, width: 18 },
    ]);

    worksheet.columns = [
      { header: 'Index', key: 'index', width: 8 },
      ...staticColumns,
      { header: 'इमारत मागील Remaining', key: 'all_property_tax_sum_prev_remaining', width: 20 },
      { header: 'इमारत चालू Remaining', key: 'all_property_tax_sum_curr_remaining', width: 20 },
      { header: 'इमारत Remaining total', key: 'all_property_tax_sum_remaining', width: 20 },
      { header: 'इमारत मागील Paid', key: 'all_property_tax_sum_prev_paid', width: 20 },
      { header: 'इमारत चालू Paid', key: 'all_property_tax_sum_curr_paid', width: 20 },
      { header: 'इमारत Paid total', key: 'all_property_tax_sum_paid', width: 20 },
      ...taxColumns,
      { header: 'Total Amount Paid', key: 'total_amount_paid', width: 20 },
      { header: 'Total Amount Remaining', key: 'total_amount_remaining', width: 20 },
      { header: 'Property Type Discount', key: 'property_type_discount', width: 20 },
      { header: 'Other Discount', key: 'other_discount', width: 20 },
      { header: 'Remaining Amount', key: 'remaining_amount', width: 20 },
      { header: 'Book Number', key: 'book_number', width: 15 },
      { header: 'Receipt Number', key: 'book_receipt_number', width: 18 },
    ];

    const columnKeys = worksheet.columns.map(col => col.key);
    const properties = await this.propertyRepository.find();
    let propertyIndex = 1;

    for (const property of properties) {
      const demandRows = await this.demandReportDataRepository.find({
        where: { property: { property_id: property.property_id } },
        order: { createdAt: 'ASC' },
        relations: ['ReciptInfo', 'ReciptInfo.bookNumber']
      });

      let isFirstRow = true;
      for (const row of demandRows) {
        const filteredRow = {};
        columnKeys.forEach(key => {
          if (key === 'index') {
            filteredRow[key] = isFirstRow ? propertyIndex : '';
          } else if (key === 'property_number') {
            filteredRow[key] = isFirstRow ? row.property_number : '';
          } else if (key === 'financial_year') {
            filteredRow[key] = isFirstRow ? row.financial_year : '';
          } else if (key === 'book_number') {
            filteredRow[key] = row.ReciptInfo?.bookNumber?.book_number || row.ReciptInfo?.book_number || '';
          } else if (key === 'book_receipt_number') {
            filteredRow[key] = row.ReciptInfo?.book_receipt_number || '';
          } else {
            filteredRow[key] = row[key];
          }
        });

        worksheet.addRow(filteredRow);
        const lastRow = worksheet.lastRow;
        if (lastRow) {
          lastRow.eachCell(cell => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            };
          });
        }
        isFirstRow = false;
      }
      if (demandRows.length > 0) propertyIndex++;
    }

    worksheet.getRow(1).eachCell(cell => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD3D3D3' }
      };
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

async exportAllMilkatKarDataToExcel(financialYear: string): Promise<Buffer> {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Milkat Kar Data');

  const taxColumns = Object.entries(TAX_TYPES).flatMap(([taxKey, taxLabel]) => [
    { header: `${taxLabel} मागील`, key: `${taxKey}_previous`, width: 18 },
    { header: `${taxLabel} चालू`, key: `${taxKey}_current`, width: 18 },
    { header: `${taxLabel} Total`, key: taxKey, width: 18 },
  ]);

  worksheet.columns = [
    { header: 'Updated Date', key: 'updatedDate', width: 20 },
    { header: 'Property Number', key: 'propertyNumber', width: 20 },
    { header: 'Old Property Number', key: 'old_propertyNumber', width: 20 },
    { header: 'Register Number', key: 'register_number', width: 20 },
    { header: 'Owner Name', key: 'owner_name', width: 20 },
    { header: 'Ward', key: 'ward', width: 15 },
    { header: 'Zone Name', key: 'zone_name', width: 15 },
    { header: 'Financial Year', key: 'financial_year', width: 15 },
    { header: 'इमारत मागील', key: 'all_property_tax_sum', width: 15 },
    { header: 'इमारत चालू', key: 'all_property_tax_sum_current', width: 15 },
    { header: 'इमारत Total', key: 'all_property_tax_sum_total', width: 15 },
    ...taxColumns,
    { header: 'Total Tax Previous', key: 'total_tax_previous', width: 15 },
    { header: 'Total Tax Current', key: 'total_tax_current', width: 15 },
    { header: 'Total Tax', key: 'total_tax', width: 15 },
  ];

    const startDate = new Date('2025-09-08T00:00:00');
  const endDate = new Date('2025-09-20T23:59:59');
  
  const query = this.propertyRepository.createQueryBuilder('property')
    .leftJoinAndSelect('property.ward', 'ward')
    .leftJoinAndSelect('property.zone', 'zone')
    .leftJoinAndSelect('property.property_owner_details', 'property_owner_details')
    .leftJoinAndSelect('property.register_number', 'register_number')
    .where('property.updatedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
    .orderBy('property.updatedAt', 'DESC');

  console.log('Generated SQL:', query.getSql());

  const properties = await query.getMany();
  console.log("properties---", JSON.stringify(properties.length));

  for (const property of properties) {
    let ownerName = '';
    if (property.property_owner_details && property.property_owner_details.length > 0) {
      const mainOwner = property.property_owner_details.find(o => o.is_payer) || property.property_owner_details[0];
      ownerName = mainOwner?.name || '';
    }

    const milkatData = await this.warshikKarRepository.find({
      where: {
        property: { property_id: property.property_id },
        financial_year: financialYear,
      },
      order: {
        updatedAt: 'ASC',
      },
    });

    for (const milkat of milkatData) {
      const rowData: any = {
        updatedDate: property.updatedAt ? new Date(property.updatedAt).toLocaleDateString() : '',
        propertyNumber: property.propertyNumber,
        old_propertyNumber: property.old_propertyNumber || '',
        register_number: property.register_number?.register_name || '',
        owner_name: ownerName,
        ward: property.ward?.ward_name || '',
        zone_name: property.zone?.zoneName || '',
        financial_year: milkat.financial_year || '',
        all_property_tax_sum: milkat.all_property_tax_sum || 0,
        all_property_tax_sum_total: milkat.all_property_tax_sum_total || 0,
        all_property_tax_sum_current: milkat.all_property_tax_sum_current || 0,
        total_tax: milkat.total_tax || 0,
        total_tax_current: milkat.total_tax_current || 0,
        total_tax_previous: milkat.total_tax_previous || 0,
        other_tax_sum_tax: milkat.other_tax_sum_tax || 0,
        other_tax_sum_tax_current: milkat.other_tax_sum_tax_current || 0,
        other_tax_sum_tax_previous: milkat.other_tax_sum_tax_previous || 0,
      };

      // dynamically map tax columns
      Object.keys(TAX_TYPES).forEach(taxKey => {
        rowData[taxKey] = milkat[taxKey] || 0;
        rowData[`${taxKey}_current`] = milkat[`${taxKey}_current`] || 0;
        rowData[`${taxKey}_previous`] = milkat[`${taxKey}_previous`] || 0;
      });

      worksheet.addRow(rowData);
    }
  }

  // style
  worksheet.getRow(1).eachCell(cell => {
    cell.font = { bold: true };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD3D3D3' },
    };
  });

  worksheet.eachRow((row, rowNumber) => {
    if (rowNumber > 1) {
      row.eachCell(cell => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });
    }
  });

  const buffer = await workbook.xlsx.writeBuffer();
  return Buffer.from(buffer);
}



  async exportAllWarshikKarDataToExcel(financialYear: string): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Warshik Kar Data');

    const taxLabels = Object.values(TAX_TYPES);
    const taxKeys = Object.keys(TAX_TYPES);

    const headerRow1 = [
      'अ.क्र. (Sr No)',
      'रस्त्याचे नाव/गल्लीचे नाव (Street Name)',
      'गट क्र.(Gat No)',
       'जुना मालमत्ता क्रमांक (Old Property Number)','मालमत्ता क्रमांक (Property Number)', 'Register Number', 'मालकाचे नाव (Owner Name)',
      'भोगवटदाराचे नाव', 'प्रभाग (Ward)', 'क्षेत्र (Zone)', 'मालमत्ता प्रकार (Property Type)', 'मजला (Floor)','वापर प्रकार (Usage Type)',
      'बांधकाम वर्ष',  'क्षेत्रफळ (चौ.फूट) (Area Sq Ft)', 'क्षेत्रफळ (चौ.मीटर) (Area Sq Meter)','लांबी (Length)', 'रुंदी (Breadth)', 
      ,'RR Rate','बांधकाम' ,'Depreciation Rate', 'Weighting',
      'भांडवली मूय',  
    ];

    headerRow1.push("इमारत कर");
    taxLabels.forEach(label => headerRow1.push(label));
    headerRow1.push('एकूण कर (Total Tax)');

    const lengthOfHeaderRow1 = headerRow1.length;
    headerRow1.push("इमारत कर", '');
    taxLabels.forEach(label => headerRow1.push(label, ''));
    headerRow1.push('एकूण कर (Total Tax)', '');
    headerRow1.push('बुक नंबर', 'पावती क्र.', 'पावती दिनांक');
    headerRow1.push(`वसूल इमारत कर`, '');
    taxLabels.forEach(label => headerRow1.push(`वसूल ${label}`, ''));
    headerRow1.push('वसूल एकूण कर (Total Tax)', '');
    headerRow1.push(`शिल्लक इमारत कर`, '');
    taxLabels.forEach(label => headerRow1.push(`शिल्लक ${label}`, ''));
    headerRow1.push('शिल्लक एकूण कर (Total Tax)', '');

    worksheet.addRow(headerRow1);

    const headerRow2 = Array(lengthOfHeaderRow1).fill('');
          headerRow2.push('मागील बाकी', 'चालू बाकी');

    taxLabels.forEach(() => {
      headerRow2.push('मागील बाकी', 'चालू बाकी');
    });
    
    headerRow2.push('मागील बाकी', 'चालू बाकी', '', '', '');
    taxLabels.forEach(() => {
      headerRow2.push('मागील बाकी', 'चालू बाकी');
    });
    headerRow2.push('मागील बाकी', 'चालू बाकी');
    taxLabels.forEach(() => {
      headerRow2.push('मागील बाकी', 'चालू बाकी');
    });
    headerRow2.push('मागील बाकी', 'चालू बाकी');
          headerRow2.push('मागील बाकी', 'चालू बाकी');
          headerRow2.push('मागील बाकी', 'चालू बाकी');

    worksheet.addRow(headerRow2);

    let col = 36;
    const mergeHeaders = (count: number) => {
      for (let i = 0; i < count; i++) {
        worksheet.mergeCells(1, col, 1, col + 1);
        col += 2;
      }
    };
let total_mergerdcell=taxLabels.length+1;
    mergeHeaders(total_mergerdcell);
    worksheet.mergeCells(1, col, 1, col + 1);
    col += 3;
    mergeHeaders(total_mergerdcell);
    worksheet.mergeCells(1, col, 1, col + 1);
    col += 2;
    mergeHeaders(total_mergerdcell);
    worksheet.mergeCells(1, col, 1, col + 1);

    [worksheet.getRow(1), worksheet.getRow(2)].forEach(row => {
      row.eachCell(cell => {
        cell.font = { bold: true };
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } };
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.border = {
          top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' },
        };
      });
    });


     const applyColorToRange = (startCol: string, endCol: string, color: string) => {
        for (let i = 1; i <= 2; i++) { // Apply to first two rows
            const row = worksheet.getRow(i);
            for (let colLetter = startCol; colLetter <= endCol; colLetter = String.fromCharCode(colLetter.charCodeAt(0) + 1)) {
                const cell = row.getCell(colLetter);
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: color }
                };
            }
        }
    };


    // applyColorToRange('R', 'AC', 'ADD8E6'); // Light blue
    // applyColorToRange('AD', 'BA', '90EE90'); // green 
    // applyColorToRange('AA', 'BZ', 'FFFF00'); // Light Yellow 
    // applyColorToRange('CA', 'CX', 'D3D3D3'); // Gray
    const properties = await this.propertyRepository.find({
      relations: [
        'ward', 'zone', 'property_owner_details', 'property_usage_details','property_owner_details.owner_type',
        'property_usage_details.propertyType', 'property_usage_details.usageType',
        'property_usage_details.floorType',
        'warshikKar', 'warshikKar.warshilKarTax', 'warshikKar.warshilKarTax.property_usage_details',
        'register_number'
      ],
      where: {
        register_number: Not(IsNull())
      },
    });

    let srNo = 1;
    for (const property of properties) {
       const ownerName = property.property_owner_details?.filter(    (owner) => owner.owner_type.owner_type === "स्वत:" ).map((owner) => owner.name) .join(", ") || "--";
       const otherOwner = property.property_owner_details?.filter(    (owner) => owner.owner_type.owner_type !== "स्वत:" ).map((owner) => owner.name) .join(", ") || "--";

      const warshikData = await this.annualKarAkaraniService.getWarshikKarAkarni(property.propertyNumber, 'propertyNumber', financialYear);
      const warshikKarData = (warshikData?.data as any)?.warshikKar?.[0] || {};

      const demandRecords = await this.demandReportDataRepository.find({
        where: { property: { property_id: property.property_id }, financial_year: financialYear },
        relations: ['ReciptInfo', 'ReciptInfo.bookNumber'],
        order: { createdAt: 'ASC' }
      });

      const paid = { total_current: 0, total_previous: 0, total_overall: 0 };
      const remaining = { total_current: 0, total_previous: 0, total_overall: 0 };

        paid[`all_property_tax_sum_curr_paid`] = 0;
        paid[`all_property_tax_sum_prev_paid`] = 0;
        remaining[`all_property_tax_sum_curr_remaining`] = 0;
        remaining[`all_property_tax_sum_prev_remaining`] = 0;

      taxKeys.forEach(key => {
        paid[`${key}_current`] = 0;
        paid[`${key}_previous`] = 0;
        remaining[`${key}_current`] = 0;
        remaining[`${key}_previous`] = 0;
      });

      const bookNumbers = [];
      const receiptNumbers = [];
      const receiptDates = [];
      for (const record of demandRecords) {
       paid[`all_property_tax_sum_curr_paid`]  += (record[`all_property_tax_sum_curr_paid`] || 0);
        paid[`all_property_tax_sum_prev_paid`] += (record[`all_property_tax_sum_prev_paid`] || 0);
        remaining[`all_property_tax_sum_curr_remaining`] += (record[`all_property_tax_sum_curr_remaining`] || 0);
        remaining[`all_property_tax_sum_prev_remaining`] += (record[`all_property_tax_sum_prev_remaining`] || 0);

        taxKeys.forEach(key => {
          paid[`${key}_current`] += (record[`${key}_curr_paid`] || 0);
          paid[`${key}_previous`] += (record[`${key}_prev_paid`] || 0);
          remaining[`${key}_current`] += (record[`${key}_curr_remaining`] || 0);
          remaining[`${key}_previous`] += (record[`${key}_prev_remaining`] || 0);
        });
        paid.total_current += taxKeys.reduce((sum, key) => sum + (record[`${key}_curr_paid`] || 0), 0) + (record[`all_property_tax_sum_curr_paid`] || 0);
        paid.total_previous += taxKeys.reduce((sum, key) => sum + (record[`${key}_prev_paid`] || 0), 0)+(record[`all_property_tax_sum_prev_paid`] || 0);
        paid.total_overall += record.total_amount_paid || 0;
        remaining.total_current += taxKeys.reduce((sum, key) => sum + (record[`${key}_curr_remaining`] || 0), 0)+(record[`all_property_tax_sum_curr_remaining`] || 0);
        remaining.total_previous += taxKeys.reduce((sum, key) => sum + (record[`${key}_prev_remaining`] || 0), 0) +(record[`all_property_tax_sum_prev_remaining`] || 0);
        remaining.total_overall += record.total_amount_remaining || 0;

        if (record.ReciptInfo) {
          const date = new Date(record.ReciptInfo.createdAt).toLocaleDateString();
          bookNumbers.push(record.ReciptInfo.bookNumber?.book_number || '');
          receiptNumbers.push(record.ReciptInfo.book_receipt_number || '');
          receiptDates.push(date);
        }
      }

      const processUsageDetails = (usageDetails) => {
        if (!usageDetails || usageDetails.length === 0) {
          return [{}];
        }
        return usageDetails;
      };

  for (const [index, usage] of processUsageDetails(property.property_usage_details).entries()) {
      const rowData = [];
      const warshikKar = property.warshikKar.find(w => w.financial_year === financialYear);
      const warshikKarTaxData = warshikKar?.warshilKarTax.find(t => t.property_usage_details && t.property_usage_details.property_usage_details_id === usage.property_usage_details_id);

      let capitalValue = 0;
      let rrConstructionRate = 0;
      let rrRate = 0;
      let depreciationRate = 0;
      let weighting = 0;

      if (warshikKarTaxData) {
        capitalValue = warshikKarTaxData.capital_value || 0;
        rrConstructionRate = warshikKarTaxData.rr_construction_rate || 0;
        rrRate = warshikKarTaxData.rr_rate || 0;
        depreciationRate = warshikKarTaxData.depreciation_rate || 0;
        weighting = warshikKarTaxData.weighting || 0;
      }


      if (index === 0) {
        rowData.push(
          srNo++,
          property.street?.street_name || '',
          property.gat_no || '',
          property.old_propertyNumber || '',
          property.propertyNumber || '',
          property.register_number?.register_name || '--',
          ownerName,
          otherOwner,
          property.ward?.ward_name || '',
          property.zone?.zoneName || '',
          usage.propertyType?.propertyType || '',
          usage.floorType?.floor_name || '',
          usage.usageType?.usage_type || '',
          usage.construction_start_year || '',
          Number(usage.are_sq_ft || 0),
          Number(usage.are_sq_meter || 0),
          Number(usage.length || 0),
          Number(usage.width || 0),
          rrRate,
          rrConstructionRate,
          depreciationRate,
          weighting,
          capitalValue,
        );
      } else {
        rowData.push(
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          Number(usage.are_sq_ft || 0),
          Number(usage.are_sq_meter || 0),
          Number(usage.length || 0),
          Number(usage.width || 0),
          rrRate,
          rrConstructionRate,
          depreciationRate,
          weighting,
          capitalValue,
        );
      }

        rowData.push(Number(warshikKarData[`all_property_tax_sum_total`] || 0));
        taxKeys.forEach(key => rowData.push(Number(warshikKarData[`${key}`] || 0)));
        rowData.push(Number(warshikKarData['total_tax'] || 0));
        rowData.push(Number(warshikKarData[`all_property_tax_sum`] || 0));
        rowData.push(Number(warshikKarData[`all_property_tax_sum_current`] || 0));

        taxKeys.forEach(key => {
          rowData.push(Number(warshikKarData[`${key}_previous`] || 0));
          rowData.push(Number(warshikKarData[`${key}_current`] || 0));
        });

        rowData.push(Number(warshikKarData['total_tax_previous'] || 0));
        rowData.push(Number(warshikKarData['total_tax_current'] || 0));
        rowData.push(bookNumbers.join(', '), receiptNumbers.join(', '), receiptDates.join(', '));

        rowData.push(Number(paid[`all_property_tax_sum_prev_paid`] || 0));
        rowData.push(Number(paid[`all_property_tax_sum_curr_paid`] || 0));

        taxKeys.forEach(key => {
          rowData.push(Number(paid[`${key}_previous`] || 0));
          rowData.push(Number(paid[`${key}_current`] || 0));
        });

        rowData.push(Number(paid.total_previous || 0));
        rowData.push(Number(paid.total_current || 0));

          const prevRemaining = (warshikKarData[`all_property_tax_sum`] || 0) - (paid[`all_property_tax_sum_prev_paid`] || 0);
          const currRemaining = (warshikKarData[`all_property_tax_sum_current`] || 0) - (paid[`all_property_tax_sum_curr_paid`] || 0);
          rowData.push(Number(prevRemaining > 0 ? prevRemaining : 0));
          rowData.push(Number(currRemaining > 0 ? currRemaining : 0));

        taxKeys.forEach(key => {
          const prevRemaining = (warshikKarData[`${key}_previous`] || 0) - (paid[`${key}_previous`] || 0);
          const currRemaining = (warshikKarData[`${key}_current`] || 0) - (paid[`${key}_current`] || 0);
          rowData.push(Number(prevRemaining > 0 ? prevRemaining : 0));
          rowData.push(Number(currRemaining > 0 ? currRemaining : 0));
        });

        const totalPrevRemaining = (warshikKarData['total_tax_previous'] || 0) - (paid.total_previous || 0);
        const totalCurrRemaining = (warshikKarData['total_tax_current'] || 0) - (paid.total_current || 0);
        rowData.push(Number(totalPrevRemaining > 0 ? totalPrevRemaining : 0));
        rowData.push(Number(totalCurrRemaining > 0 ? totalCurrRemaining : 0));

        const row = worksheet.addRow(rowData);
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' },
          };
        });
      }
    }

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  async exportPaidUserReportToExcel(financialYear: string): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Paid Users');

    worksheet.columns = [
      { header: 'Property Number', key: 'propertyNumber', width: 20 },
      { header: 'Owner Name', key: 'owner_name', width: 20 },
      { header: 'Financial Year', key: 'financial_year', width: 15 },
      { header: 'Total Paid Amount', key: 'total_paid_amount', width: 20 },
      { header: 'Payment Date', key: 'payment_date', width: 18 },
    ];

    const paidProperties = await this.paidDataRepository.find({
      where: { financial_year: financialYear },
      relations: ['property', 'property.property_owner_details'],
    });

    for (const paidData of paidProperties) {
      let ownerName = '';
      if (paidData.property?.property_owner_details && paidData.property.property_owner_details.length > 0) {
        const mainOwner = paidData.property.property_owner_details.find(o => o.is_payer) || paidData.property.property_owner_details[0];
        ownerName = mainOwner?.name || '';
      }

      worksheet.addRow({
        propertyNumber: paidData.property?.propertyNumber || '',
        owner_name: ownerName,
        financial_year: paidData.financial_year,
        total_paid_amount: paidData.total_amount,
        payment_date: paidData.createdAt ? new Date(paidData.createdAt).toLocaleDateString() : '',
      });
    }

    worksheet.getRow(1).eachCell(cell => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD3D3D3' }
      };
    });

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  async exportNotPaidUserReportToExcel(financialYear: string): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Not Paid Users');

    worksheet.columns = [
      { header: 'Property Number', key: 'propertyNumber', width: 20 },
      { header: 'Old Property Number', key: 'old_propertyNumber', width: 20 },
      { header: 'Owner Name', key: 'owner_name', width: 20 },
      { header: 'Ward', key: 'ward', width: 15 },
      { header: 'Zone Name', key: 'zone_name', width: 15 },
      { header: 'Financial Year', key: 'financial_year', width: 15 },
    ];

    const allProperties = await this.propertyRepository.find({ relations: ['property_owner_details', 'ward', 'zone'] });
    const paidPropertyNumbers = (await this.paidDataRepository.find({
      where: { financial_year: financialYear },
      select: ['property'],
      relations: ['property'],
    })).map(paidData => paidData.property?.propertyNumber);

    const notPaidProperties = allProperties.filter(property => !paidPropertyNumbers.includes(property.propertyNumber));

    for (const property of notPaidProperties) {
      let ownerName = '';
      if (property.property_owner_details && property.property_owner_details.length > 0) {
        const mainOwner = property.property_owner_details.find(o => o.is_payer) || property.property_owner_details[0];
        ownerName = mainOwner?.name || '';
      }

      worksheet.addRow({
        propertyNumber: property.propertyNumber,
        old_propertyNumber: property.old_propertyNumber || '',
        owner_name: ownerName,
        ward: property.ward?.ward_name || '',
        zone_name: property.zone?.zoneName || '',
        financial_year: financialYear,
      });
    }

    worksheet.getRow(1).eachCell(cell => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD3D3D3' }
      };
    });

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }


async exportPropertyReportToExcel(financialYear: string): Promise<Buffer> {
  const workbook = new ExcelJS.Workbook();

  const properties = await this.propertyRepository.find({
    relations: [
      'property_owner_details',
      'ward',
      'zone',
      'property_usage_details',
      'property_usage_details.propertyType',
      'property_usage_details.usageType',
      'register_number',
    ],
    where: {
    register_number: IsNull(),
    },
    order: {
      updatedAt: 'ASC',
    },
  });

  const propertiesByWard = properties.reduce((acc, property) => {
    const wardName = property.ward?.ward_name || 'Unknown Ward';
    if (!acc[wardName]) {
      acc[wardName] = [];
    }
    acc[wardName].push(property);
    return acc;
  }, {});

  for (const wardName in propertiesByWard) {
    const worksheet = workbook.addWorksheet(`Ward ${wardName}`);
    
    worksheet.columns = [
      { header: 'Index', key: 'index', width: 10 },
      { header: 'Property Number', key: 'propertyNumber', width: 20 },
      { header: 'Old Property Number', key: 'old_propertyNumber', width: 20 },
            { header: 'Register Number', key: 'register_number', width: 20 },

      { header: 'Owner Name', key: 'owner_name', width: 20 },
      { header: 'Zone Name', key: 'zone_name', width: 15 },
      { header: 'Property Type', key: 'property_type', width: 15 },
      { header: 'Usage Type', key: 'usage_type', width: 15 },
      { header: 'Financial Year', key: 'financial_year', width: 15 },
    ];

    let index = 1;
    for (const property of propertiesByWard[wardName]) {
      let ownerName = '';
      if (property.property_owner_details && property.property_owner_details.length > 0) {
        const mainOwner = property.property_owner_details.find(o => o.is_payer) || property.property_owner_details[0];
        ownerName = mainOwner?.name || '';
      }

      worksheet.addRow({
        index: index++,
        propertyNumber: property.propertyNumber,
        old_propertyNumber: property.old_propertyNumber || '',
                register_number:  '--',

        owner_name: ownerName,
        zone_name: property.zone?.zoneName || '',
        property_type: property.property_usage_details?.[0]?.propertyType?.propertyType || '',
        usage_type: property.property_usage_details?.[0]?.usageType?.usage_type || '',
        financial_year: financialYear,
      });
    }

    worksheet.getRow(1).eachCell(cell => {
      cell.font = { bold: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD3D3D3' },
      };
    });

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        });
      }
    });
  }

  const buffer = await workbook.xlsx.writeBuffer();
  return Buffer.from(buffer);
}

}
