import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  BaseEntity,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany
} from 'typeorm';



@Entity('import_property')
export class Import_PropertyEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  property_id: string;

  @Column({ name: 'sr_no', type: 'integer' })
  sr_no: number;

  @Column({ name: 'street_name', type: String })
  street_name: string;

  @Column({ type: String, name: 'zone_name', nullable: false })
  zoneName: string;

  @Column({ type: String, name: 'ward_name', nullable: false })
  ward_name: string;

  @Column({ type: 'varchar', nullable: false })
  propertyNumber: string;


  @Column({ type: 'varchar', nullable: true })
  old_propertyNumber: string; 

  @Column({ type: 'timestamp', nullable: true })
  import_date!: Date;

  @Column({ type: String, name: 'ward_number', nullable: true})
  ward_number!: string;

  @Column({ type: String, name: 'owner_name', nullable: true})
  owner_name!: string;

  @Column({ type: String, name: 'owner_type', nullable: true})
  owner_type!: string;

  @Column({ type: String, name: 'bhogawat_owner_name', nullable: true})
  bhogawat_owner_name!: string;


  @Column({ type: String, name: 'usage_type', nullable: true})
  usage_type!: string;

  @Column({ type: String, name: 'usage_desc', nullable: true})
  usage_desc: string;

  @Column({ type: String, name: 'construction_year', nullable: true})
  construction_year: string;


  @Column({ type: String, name: 'length', nullable: true})
  length: string;

  @Column({ type: String, name: 'width', nullable: true})
  width: string;

  @Column({ type: String, name: 'sq_ft', nullable: true})
  sq_ft: string;

  @Column({ type: String, name: 'sq_meter', nullable: true})
  sq_meter: string;

  @Column({ type: String, name: 'missing_desc', nullable: true})
  missing_desc: string;

  @Column({ type: String, name: 'is_merge', nullable: true})
  is_merge: string;

  @Column({ type: String, name: 'status', nullable: true})
  status: string;

  @Column({ type: String, name: 'sr_no_excel', nullable: true})
  sr_no_excel: string;

  @Column({ type: String, name: 'property_number_merge', nullable: true})
  property_number_merge: string;

  @Column({ type: String, name: 'is_new_property', nullable: true})
  is_new_property: string;

  @Column({ type: String, name: 'duplicate_property_no', nullable: true})
  duplicate_property_no: string;

  @Column({ type: String, name: 'import_done_status', nullable: true})
  import_done_status: string;

//emarat kar
  @Column({ type: String, name: 'emarat_kar', nullable: true})
  emarat_kar: string;

  @Column({ type: String, name: 'owner_table_id', nullable: true})
  owner_table_id: string;

  @Column({ type: String, name: 'usage_details_table_id', nullable: true})
  usage_details_table_id: string;

  @Column({ type: String, name: 'property_table_id', nullable: true})
  property_table_id: string;  

  //bhandavali mulya
  @Column({ type: String, name: 'capital_value', nullable: true})
  capital_value: string;

  //ekun_kar
  @Column({ type: String, name: 'total_tax', nullable: true})
  total_tax: string;
  @Column({ type: String, name: 'mobile', nullable: true }) 
  mobile: string;



  /*
   * Create and Update Date Columns
   */

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;


}
