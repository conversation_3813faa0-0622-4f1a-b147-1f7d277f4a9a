import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Master_rr_construction_rate } from 'libs/database/entities';
import {
  Master_rr_construction_rateRepository,
  PropertyTypeClassMasterRepository,
} from 'libs/database/repositories';

@Injectable()
export class Master_rr_construction_rateService {
  constructor(
    @InjectRepository(Master_rr_construction_rateRepository)
    private readonly rrConstructionRateRepository: Master_rr_construction_rateRepository,
    private readonly propertyTypeClassMasterRepository: PropertyTypeClassMasterRepository,
  ) {}

  async create(
    data: any,
  ): Promise<{ message: string; data: Master_rr_construction_rate[] }> {
    // Assuming there are related entities to check for, for example, a zone
    const propertyTypeClass =
      await this.propertyTypeClassMasterRepository.findById(
        data.property_type_class_id,
      ); // Replace with actual logic to find zone

    if (!propertyTypeClass) {
      throw new NotFoundException('propertyTypeClass not found');
    }

    // Handle reassessment range if provided
    let reassessmentRange = null;
    if (data.reassessment_range_id) {
      const { ReassessmentRange } = await import('libs/database/entities');
      reassessmentRange = await this.rrConstructionRateRepository.manager
        .getRepository(ReassessmentRange)
        .findOne({
          where: { reassessment_range_id: data.reassessment_range_id }
        });
      if (!reassessmentRange) {
        throw new NotFoundException('Reassessment range not found');
      }
    }

    // Create a new instance of Master_rr_construction_rate
    const newRate =  this.rrConstructionRateRepository.create({
      ...data,
      property_type_class_id: propertyTypeClass, // Set the actual zone entity here
      reassessmentRange: reassessmentRange, // Set the reassessment range entity
    });

    // Save the new rate to the repository
    const savedRate = await this.rrConstructionRateRepository.save(newRate);

    // Return a structured response
    return {
      message: 'Construction rate created successfully',
      data: savedRate,
    };
  }

  async findAll(): Promise<{
    message: string;
    data: Master_rr_construction_rate[];
  }> {
    const allRates =
      await this.rrConstructionRateRepository.findWithPropertyClass();
    return {
      message: 'Construction rates fetched successfully',
      data: allRates,
    };
  }

  // async findOne(
  //   id: string,
  // ): Promise<{
  //   message: string;
  //   data: Master_rr_construction_rate | undefined;
  // }> {
  //   const rate = await this.rrConstructionRateRepository.findOne(id);
  //   if (!rate) {
  //     return {
  //       message: 'Construction rate not found',
  //       data: undefined,
  //     };
  //   }
  //   return {
  //     message: 'Construction rate fetched successfully',
  //     data: rate,
  //   };
  // }

  async update(
    id: string,
    data: any,
  ): Promise<{
    message: string;
    data: Master_rr_construction_rate | undefined;
  }> {
    const updatedRate = await this.rrConstructionRateRepository.updateConstructionRate(
      id,
      data,
    );

    return {
      message: 'Construction rate updated successfully',
      data: updatedRate.data,
    };
  }

  async delete(id: string): Promise<{ message: string }> {
    await this.rrConstructionRateRepository.softDelete(id);
    return {
      message: 'Construction rate deleted successfully',
    };
  }
}
