import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
  } from "@/components/ui/table";
  import {
    flexRender,
    getCoreRowModel,
    useReactTable,
    getSortedRowModel,
    SortingState,
    getPaginationRowModel,
    getFilteredRowModel,
  } from "@tanstack/react-table";
  import { useEffect, useState } from "react";
  import { Button } from "@/components/ui/button";
  import { format } from "date-fns";
  import { ChevronUp, ChevronDown, ArrowUpDown, Trash, Edit } from "lucide-react";
  import { Input } from "@/components/ui/input";
  import React from "react";
  import { toast, useToast } from "@/components/ui/use-toast";
  import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
  } from "@/components/ui/dialog";
  import WhiteContainer from "@/components/custom/WhiteContainer";
  import TanStackTable from "@/components/globalcomponent/tanstacktable";
  import { usePaymentListRateController } from "@/controller/Bill/BillController";
  import { useTranslation } from "react-i18next";
  import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
  } from "@/components/ui/select";
  import { useBookController } from "@/controller/tax/BookeMasterController";
  import TaxListApi from "@/services/TaxServices";
  
  const BillHistoryData = ({ data }) => {
    const [sorting, setSorting] = useState<SortingState>([]);
    const [globalFilter, setGlobalFilter] = useState("");
    const { toast } = useToast();
    const [creatingPayment, setCreatingPayment] = useState(null);
    const { PaymentListRateList, paymentLoading } =
      usePaymentListRateController();
    const { t } = useTranslation();
  console.log("datadatadata--->",data)
    const columns = [
      {
        accessorKey: "SrNo",
        header: "अ. क्र.",
        cell: ({ row }) => <div className="capitalize">{row.index + 1}</div>,
      },
      {
        accessorKey: "remaing_amount",
        header: "उर्वरित रक्कम",
        cell: ({ row }) => {
          const paidData = row.original.paidData;

          const amount = parseFloat(paidData.remaining_amount);
          const formatted = new Intl.NumberFormat("en-IN", {
            style: "currency",
            currency: "INR",
          }).format(amount);
          return formatted;
        },
      },
   
      {
        accessorKey: "amount",
        header: "रक्कम",
        cell: ({ row }) => {
          const amount = parseFloat(row.getValue("amount"));
          const formatted = new Intl.NumberFormat("en-IN", {
            style: "currency",
            currency: "INR",
          }).format(amount);
          return formatted;
        },
      },
      {
        accessorKey: "payment_mode",
        header: "भरणा पद्धती",
        cell: ({ row }) => {
          const mode = row.getValue("payment_mode") as string;
          return mode.charAt(0).toUpperCase() + mode.slice(1).toLowerCase();
        },
      },
      {
        accessorKey: "payment_date",
        header: "दिनांक",
        cell: ({ row }) => {
          return format(new Date(row.getValue("payment_date")), "dd MMM yyyy");
        },
      },
      {
        accessorKey: "receipts.book_number",
        header: "बुक  क्रमांक",
        cell: ({ row }) => {
          const receipts = row.original.receipts;
          return receipts?.book_number || "-";
        },
      },
      {
        accessorKey: "receipts.book_receipt_number",
        header: "पावती क्रमांक",
        cell: ({ row }) => {
          const receipts = row.original.receipts;
          return receipts?.book_receipt_number || "-";
        },
      },
    ];
  
    const table = useReactTable({
      data: data ?? [],
      columns,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFilteredRowModel: getFilteredRowModel(),
      onSortingChange: setSorting,
      state: {
        sorting,
        globalFilter,
      },
      onGlobalFilterChange: setGlobalFilter,
    });
  
    return (
      <div className="w-full h-fit p-6">
        <p className="w-full flex items-center justify-between ml-2 text-2xl font-semibold mb-2">
          Payment History
        </p>
  
          <TanStackTable
            columns={columns}
            data={data ?? []}
         
            loader={paymentLoading}
          />
      </div>
    );
  };
  
  export default BillHistoryData;
  