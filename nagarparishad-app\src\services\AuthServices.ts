import axios from "axios";
import {
  SIGN_UP,
  SIGN_IN,
  VALIDATE_OTP,
  SIGN_OUT,
  FORGOT_PASSWORD,
  VERIFY_FORGOT_PASSWORD_OTP,
  RESET_PASSWORD,
} from "@/constant/utils/authUtils";
import {
  UserLoginData,
  OtpValidationData,
  OtpAuthResponseData,
  ApiResponse,
  AuthUserRegister,
} from "@/model/auth/authServices";
const baseURL = import.meta.env.VITE_APP_BASE_URL;

// Create axios instance with interceptors
const apiClient = axios.create({
  baseURL: baseURL,
});

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = Api.getStoredToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle 401 and refresh token
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        }).catch((err) => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = Api.getStoredRefreshToken();
        if (!refreshToken) {
          // No refresh token, redirect to login
          Api.handleLogout();
          return Promise.reject(error);
        }

        const newTokens = await Api.refreshAccessToken(refreshToken);
        console.log("Token refresh response:", newTokens);
        if (newTokens.status) {
          const newAccessToken = newTokens.data.data.accessToken;
          const newRefreshToken = newTokens.data.data.refreshToken;
          console.log("New access token:", newAccessToken);
          console.log("New refresh token:", newRefreshToken);

          // Update stored tokens
          localStorage.setItem('AccessToken', JSON.stringify(newAccessToken));
          localStorage.setItem('RefreshToken', JSON.stringify(newRefreshToken));

          // Update the original request with new token
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

          // Process queued requests
          processQueue(null, newAccessToken);

          // Retry the original request
          return apiClient(originalRequest);
        } else {
          // Refresh failed, logout user
          processQueue(error, null);
          Api.handleLogout();
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Refresh failed, logout user
        processQueue(refreshError, null);
        Api.handleLogout();
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

class Api {
  static getStoredToken = () => {
    const UserToken = JSON.parse(localStorage.getItem("AccessToken") || "{}");
    return UserToken !== undefined ? UserToken : false;
  };

  static getStoredRefreshToken = () => {
    const RefreshToken = JSON.parse(localStorage.getItem("RefreshToken") || "{}");
    return RefreshToken !== undefined ? RefreshToken : false;
  };

  static refreshAccessToken = async (refreshToken: string) => {
    const url = `${baseURL}/v1/auth/refreshtoken`;
    try {
      const response = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${refreshToken}`
        },
      });

      if (response.status === 200) {
        return { status: true, data: response.data };
      } else {
        console.log("Error refreshing token: " + JSON.stringify(response.data));
        return { status: false, data: response.data };
      }
    } catch (err) {
      console.log("Error refreshing token:", err);
      return { status: false, data: err };
    }
  };

  static handleLogout = () => {
    // Clear all tokens from localStorage
    localStorage.removeItem('AccessToken');
    localStorage.removeItem('RefreshToken');
    localStorage.removeItem('UserData');

    // Redirect to login page
    window.location.href = '/login';
  };

  static signIn = async (
    credentials: UserLoginData,
    callback: (response: ApiResponse | any) => void,
  ) => {
    try {
      const response = await axios.post(SIGN_IN, credentials, {
        baseURL: baseURL,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }    } catch (err: any) {
      callback({ statusCode: 500, message: err.message });
    }
  };

  static validateOtp = async (
    otpData: OtpValidationData,
    callback: (response: OtpAuthResponseData) => void,
  ) => {
    try {
      const response = await axios.post(VALIDATE_OTP, otpData, {
        baseURL: baseURL,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      const result = response.data;
      if (result.statusCode === 201) {
        localStorage.setItem(
          "AccessToken",
          JSON.stringify(result?.data?.accessToken),
        );
        localStorage.setItem(
          "RefreshToken",
          JSON.stringify(result?.data?.refreshToken),
        );
        localStorage.setItem("UserData", JSON.stringify(result?.data?.user));
      }
      callback(result);
    } catch (err: any) {
      callback({ statusCode: 500, message: err.message, data: null });
    }
  };

  static signOut = async (callback: (response: ApiResponse) => void) => {
    try {
      const response = await apiClient.post(
        SIGN_OUT,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      const result = response.data;
      if (result.statusCode === 201) {
        localStorage.removeItem("AccessToken");
        localStorage.removeItem("RefreshToken");
      }
      callback(result);
    } catch (err: any) {
      callback({
        statusCode: 500,
        message: err.message,
        data: undefined,
        status: undefined,
      });
    }
  };

  static userRegister = async (
    userData: AuthUserRegister,
    callback: (response: any) => void,
  ) => {
    try {

      const response = await axios.post(SIGN_UP, userData, {
        baseURL: baseURL,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data.message });
      }    } catch (err: any) {
      callback({
        statusCode: 500,
        message: err.message,
        data: undefined,
        status: undefined,
      });
    }
  };

  static sendPasswordResetOTP = async (
    email: string,
    callback: (response: any) => void,
  ) => {
    try {
      const response = await axios.post(
        FORGOT_PASSWORD,
        { email },
        {
          baseURL: baseURL,
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, message: err.response.data.message });
    }
  };

  static verifyPasswordResetOTP = async (
    email: string,
    otp: string,
    callback: (response: any) => void,
  ) => {
    try {
      const response = await axios.post(
        VERIFY_FORGOT_PASSWORD_OTP,
        { email, otp },
        {
          baseURL: baseURL,
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      console.log("response-->",response)
      if (response.status==201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, message: err.response.data.message });
    }
  };

  static resetPassword = async (
    email: string,
    otp: string,
    password: string,
    callback: (response: any) => void,
  ) => {
    try {
      const response = await axios.post(
        RESET_PASSWORD,
        { email, otp, password },
        {
          baseURL: baseURL,
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        },
      );
      if (response.status === 201) {
        callback({ status: true, data: response.data });
      } else {
        callback({ status: false, data: response.data });
      }
    } catch (err: any) {
      callback({ status: false, message: err.response.data.message });
    }
  };
}

export default Api;
