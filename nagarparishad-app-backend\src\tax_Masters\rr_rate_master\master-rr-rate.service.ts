import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Master_rr_rateEntity, ZoneMaster } from 'libs/database/entities';
import { Master_rr_rateRepository, ZoneMasterRepository } from 'libs/database/repositories';

@Injectable()
export class MasterRRRateService {
  constructor(
    @InjectRepository(Master_rr_rateRepository)
    private readonly rrRateRepository: Master_rr_rateRepository,
    private readonly zoneRepository: ZoneMasterRepository,

  ) {}

  async create(data: any): Promise<{ message: string; data: Master_rr_rateEntity[] }> {
    const zone = await this.zoneRepository.findById(data.zone_id );

    if (!zone) {
        throw new NotFoundException('Zone not found');
    }

    // Handle reassessment range if provided
    let reassessmentRange = null;
    if (data.reassessment_range_id) {
      const { ReassessmentRange } = await import('libs/database/entities');
      reassessmentRange = await this.rrRateRepository.manager
        .getRepository(ReassessmentRange)
        .findOne({
          where: { reassessment_range_id: data.reassessment_range_id }
        });
      if (!reassessmentRange) {
        throw new NotFoundException('Reassessment range not found');
      }
    }

    const newRate = this.rrRateRepository.create({
        ...data,
        zone: zone, // Set the actual entity here
        reassessmentRange: reassessmentRange, // Set the reassessment range entity
    });

    const savedRate = await this.rrRateRepository.save(newRate);

    return {
        message: 'RR rate created successfully',
        data: savedRate,
    };
}


  async findAll(): Promise<{ message: string; data: Master_rr_rateEntity[] }> {
    const allRates = await this.rrRateRepository.getAllWithZone();
    return {
      message: 'RR rates fetched successfully',
      data: allRates,
    };
  }

  async findOne(id: string): Promise<{ message: string; data: Master_rr_rateEntity | undefined }> {
    const rate = await this.rrRateRepository.findOne({
      where: { rr_rate_id:id },
      relations:["zone"]
    });
    if (!rate) {
      return {
        message: 'RR rate not found',
        data: undefined,
      };
    }
    return {
      message: 'RR rate fetched successfully',
      data: rate,
    };
  }

  async update(id: string, data: any): Promise<{ message: string; data: Master_rr_rateEntity }> {
        const updatedRate =  await this.rrRateRepository.updateRR_rate(id, data);
    return {
      message: 'RR rate updated successfully',
      data: updatedRate.data,
    };
  }

  async delete(id: string): Promise<{ message: string }> {
    await this.rrRateRepository.softDelete(id);
    return {
      message: 'RR rate deleted successfully',
    };
  }
}
