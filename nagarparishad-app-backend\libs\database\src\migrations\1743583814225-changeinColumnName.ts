import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeinColumnName1743583814225 implements MigrationInterface {
    name = 'ChangeinColumnName1743583814225'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "payment_info" DROP CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9"`);
  await queryRunner.query(`ALTER TABLE "payment_info" ADD CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
 }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE "payment_info" DROP CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9"`);
     
        await queryRunner.query(`ALTER TABLE "payment_info" ADD CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
