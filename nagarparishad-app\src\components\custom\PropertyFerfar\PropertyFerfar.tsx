import React, { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { object, z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import WhiteContainer from "../WhiteContainer";
import { MoreVertical } from "lucide-react";
import TanStackTable from "@/components/globalcomponent/tanstacktable";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useZoneMasterController } from "@/controller/master/ZoneMasterController";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { usePropertyRegistrationController } from "@/controller/property-registration/PropertyRegistrationController";

import { ColumnDef } from "@tanstack/react-table";
import { MasterObjectColumnDef } from "@/model/zone-master";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { formatAdharNo } from "@/controller/hepler/formatAdhar";
import OwnerForm from "./OwnerFrom";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useNamunaController } from "@/controller/report/MilkatKarAkarniEight";
import { toast } from "@/components/ui/use-toast";
import axios from "axios";
import { Label } from "@/components/ui/label";
import { Loader } from "@/components/globalcomponent/Loader";
import { useLocation } from "react-router-dom";
import { useOwnerTypeController } from "@/controller/master/OwnerTypeController";
import PropertyApi from "@/services/PropertyServices";
const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";
const PropertyFerfar = () => {
  const { t } = useTranslation();
  const divRef = useRef(null);

  const panCardRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;

  const [assetDetails, setassetDetails] = useState("");

  // Assuming `t` is a translation function
  const schema = z.object({
    ownerName: z.string().optional(),
    zone: z.string().optional(),
    ward: z.string().optional(),
    propertyNumber: z.string().optional(),
    propertyOldNumber: z.string().optional(),
    bhogwataDharakName: z.string().optional(),
  });
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      ownerName: "",
      zone: "",
      ward: "",
      propertyNumber: "",
      propertyOldNumber: "",
      bhogwataDharakName: "",
    },
  });

  const {
    control,
    watch,
    reset,
    formState: { errors },
  } = form;

  const [selectedWard, setSelectedWard] = useState("");
  const [searchDisabled, setSearchDisabled] = useState(false);
  const [position, setPosition] = useState("bottom");
  const [selecteValue, setselecteValue] = useState(null);
  const [selecteProperty, setselecteProperty] = useState(null);
  const [propertyNum, setPropertyNum] = useState("");
  // const [propertyOldNumber, setPropertyOldNumber] = useState("");

  const { namunaEightDetails } = useNamunaController();
  const [printloading, setPrintLoading] = useState(false);
  const [oldPropertyNumber, setPropertyOldNumber] = useState<any>();
  const [propertyNumber, setpropertyNumber] = useState<any>();
  const [searchOnParamter, setsearchOnParamter] = useState<string>();
  const [loading, setLoading] = useState(false);
  const [namanuDetail, setnamanuDetail] = useState(null);
  // const [propertyDetails, setPropertyDetails] = useState(null);

  const [isMarried, setIsMarried] = useState(false);

  const { propertyList, deleteProperty } = usePropertyRegistrationController();

  const { zoneList } = useZoneMasterController();
  const { wardList } = useWardMasterController();
  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Property, FormName.PropertyMutation,Action.CanRead );
  const canUpdate = canPerformAction(ModuleName.Property, FormName.PropertyMutation,Action.CanUpdate );
  const CanCreate = canPerformAction(ModuleName.Property, FormName.PropertyMutation,Action.CanCreate );
  const CanDelete = canPerformAction(ModuleName.Property, FormName.PropertyMutation,Action.CanDelete );
  
  const filteredZones = zoneList.filter(
    (zone: { ward: { ward_id: string } }) => zone.ward?.ward_id === selectedWard
  );

  const propertyNumberValue = watch("propertyNumber");
  const propertyOldNumberValue = watch("propertyOldNumber");

  const onSubmit = (data: any) => {
    console.log(data);
  };
  const handleAction = (name: string, item: any) => {
    console.log("value", name);
    setselecteValue(name);
    console.log("item", item);
    setselecteProperty(item);

    setTimeout(() => {
      if (divRef.current) {
        divRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    }, 0);
  };
  const handleReset = () => {
    reset();
    setSelectedWard("");
  };

  const handleFerfar = async () => {
    const SelectedpropertyNumber = oldPropertyNumber || propertyNumber;
    if (!SelectedpropertyNumber) {
      setnamanuDetail(null);

      toast({
        title: `${t("enterPropertyNumber")}`,
        variant: "destructive",
      });
      return;
    }
    setLoading(true);
    setnamanuDetail(null);

    try {
      const response = await PropertyApi.getPropertyDetailsByNumber(
        SelectedpropertyNumber,
        searchOnParamter
      );

      if (response.status && response.data.data) {
        console.log("namuna eight >>>>>>>>>>>>", response.data);
        setnamanuDetail(response.data.data);
      } else {
        toast({
          title: `${t("noDataFound")}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: `${t("tryAgainLater")}`,
        variant: "destructive",
      });
      console.error("Error fetching property details:", error);
    } finally {
      setLoading(false);
    }
  };

  const ownerData = namanuDetail?.property_owner_details[0];

  const ownerColumn = [
    {
      accessorKey: "index",
      header: `${t("ownerDetails.SrNo")}`,
      cell: ({ row }: { row: any }) => <div>{row.index + 1}</div>,
    },
    {
      accessorKey: "name",
      header: `${t("ownerDetails.name")}`,
      cell: ({ row }: { row: any }) => <div>{row.original?.name}</div>,
    },
    {
      accessorKey: "owner_type",
      header: `${t("ownerDetails.type")}`,
      cell: ({ row }: { row: any }) => (
        <div>{row.original.owner_type?.owner_type}</div>
      ),
    },
    {
      accessorKey: "mobile_number",
      header: `${t("ownerDetails.mobileNumber")}`,
      cell: ({ row }: { row: any }) => (
        <div>{row.original?.property_owner_details[0]?.mobile_number}</div>
      ),
    },
    {
      accessorKey: "email_id",
      header: `${t("ownerDetails.emailId")}`,
      cell: ({ row }: { row: any }) => <div>{row.original?.email_id}</div>,
    },
    {
      accessorKey: "aadhar_number",
      header: `${t("ownerDetails.adharNumber")}`,
      cell: ({ row }: { row: any }) => (
        <div>{formatAdharNo(row.original?.aadhar_number)}</div>
      ),
    },
  ];


  useEffect(() => {}, [namanuDetail]);
  console.log("propertyNumber", namanuDetail);
  const handleOldNumberChange = (e) => {
    setPropertyOldNumber(e.target.value.trim().toUpperCase());
    setpropertyNumber(() => ""); // Clear the other field
    setsearchOnParamter("old_propertyNumber");
  };

  const handlePropertyNumberChange = (e) => {
    setpropertyNumber(e.target.value.trim().toUpperCase());
    setPropertyOldNumber(() => ""); // Clear the other field
    setsearchOnParamter("propertyNumber");
  };
  const propertyNo = (e: any) => {
    setPropertyNum(e.target.value);
    // console.log("hii")
  };

  return (
    <div className="flex h-fit">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold font-Poppins   w-full ml-3 ">
          {t("मालमता फेरफार")}
        </h1>

        <WhiteContainer>
          <Form {...form}>
            <form key={1} onSubmit={form.handleSubmit(onSubmit)}>
              {/* Ward Number */}
              <div className="grid grid-cols-1 sm:grid-cols-2 min-[769px]:grid-cols-3 gap-y-3 gap-x-6">
                <div className="grid-cols-subgrid">
                  {/* Survey Number and Gat Number */}
                  <FormField
                    name="propertyNumber"
                    control={control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("property.propertyNumberColumn")}
                        </FormLabel>
                        <Input
                          className="mt-1 block w-full"
                          transliterate={false}
                          placeholder={t("property.propertyNumberColumn")}
                          value={propertyNumber}
                          onChange={handlePropertyNumberChange}
                        />
                        {errors.propertyOldNumber && (
                          <FormMessage className="mt-1">
                            {errors.propertyOldNumber.message}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid-cols-subgrid">
                  <FormField
                    control={control}
                    name="propertyOldNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("property.oldPropertyNumber")}</FormLabel>
                        <Input
                          className="mt-1"
                          transliterate={false}
                          placeholder={t("property.oldPropertyNumber")}
                          value={oldPropertyNumber}
                          onChange={handleOldNumberChange}
                        />
                        {errors.propertyNumber && (
                          <FormMessage className="mt-1">
                            {errors.propertyNumber.message}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex space-x-4 px-3 py-1 items-end">
                  <Button
                    type="submit"
                    variant="submit"
                    className="w-fit px-5"
                    onClick={handleFerfar}
                  >
                    {t("search")}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
          <p className="text-xs italic font-semibold mb-0 mt-2 text-[#3c3c3c]">
            {" "}
            {t("selectAnyFieldNote")}{" "}
          </p>
        </WhiteContainer>

        {loading && (
          <div className="flex justify-center items-center my-4">
            <Loader />
          </div>
        )}

        {namanuDetail && !loading && (
          <>
            <WhiteContainer>
              <div className="flex items-center justify-between w-full h-full">
                <h1 className="text-[18px] font-semibold font-Poppins w-full ml-3">
                  {t("propertyView.basicInformation")}
                </h1>
              </div>
              <div className="flex gap-5">
                <Carousel className="2xl:w-52 2xl:min-w-52 w-44 min-w-44 2xl:h-52 h-44 border mb-14 mt-3 rounded-2xl">
                  <CarouselContent>
                    <CarouselItem className="w-full h-full flex justify-center items-center">
                      <img
                        className="w-full h-full object-contain"
                        src="/src/assets/img/homepage/logo.png"
                        alt=""
                      />
                    </CarouselItem>
                    <CarouselItem>...</CarouselItem>
                    <CarouselItem>...</CarouselItem>
                  </CarouselContent>
                  <CarouselPrevious className="-bottom-14 top-auto left-2" />
                  <CarouselNext className="-bottom-14 top-auto right-2" />
                </Carousel>

                <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-x-3 gap-y-0 mx-3 mt-4 mb-8">
                  <div>
                    <p className="font-semibold">
                      {t("malmattaFerfar.SrNo")}:{" "}
                      <span className="font-normal">
                        {/* {singlePropertyDetails?.sr_no} */}
                        {namanuDetail?.sr_no || "--"}
                      </span>
                    </p>
                  </div>
                  <div>
                    <p className="font-semibold">
                      {t("malmattaFerfar.propertyNumber")}:{" "}
                      <span className="font-normal">
                        {/* {singlePropertyDetails?.city_survey_number} */}
                        {/* SNP403893 */}
                        {namanuDetail?.propertyNumber || "--"}
                      </span>
                    </p>
                  </div>
                  <div>
                    <p className="font-semibold">
                      {t("malmattaFerfar.propertyOldNumber")}:{" "}
                      <span className="font-normal">
                        {/* {singlePropertyDetails?.ward?.ward_name} */}
                        {namanuDetail?.old_propertyNumber || "--"}{" "}
                      </span>
                    </p>
                  </div>
                  <div>
                    <p className="font-semibold">
                      {t("malmattaFerfar.propertyOwner")}:{" "}
                      <span className="font-normal">
                        {/* {singlePropertyDetails?.zone?.zoneName} */}
                        {/* अरुण दिनकर माने */}
                        {namanuDetail?.property_owner_details
                          ?.filter(
                            (owner) => owner.owner_type.owner_type === "स्वतः" || "स्वत:"
                          )
                          .map((owner) => owner.name)
                          .join(", ") || "--"}
                      </span>
                    </p>
                  </div>

                  <div>
                    <p className="font-semibold">
                      {t("malmattaFerfar.occupancyHolder")}:{" "}
                      <span className="font-normal">
                        {/* {singlePropertyDetails?.house_or_apartment_name} */}
                        {/* अरुण दिनकर माने */}
                        {namanuDetail?.property_owner_details
                          ?.filter(
                            (owner) =>
                              owner.owner_type.owner_type === "भोगवटादार" ||
                              owner.owner_type.owner_type === "भाडेकरू"
                          )
                          .map((owner) => owner.name)
                          .join(", ") || "--"}
                      </span>
                    </p>
                  </div>
                  <div>
                    <p className="font-semibold">
                      {t("malmattaFerfar.mobileNumber")}:{" "}
                      <span className="font-normal">
                        {/* {singlePropertyDetails?.street?.street_name} */}
                        {namanuDetail?.property_owner_details[0]
                          ?.mobile_number || "--"}
                      </span>
                    </p>
                  </div>

                  <div>
                    <p className="font-semibold">
                      {t("malmattaFerfar.wardName")}:{" "}
                      <span className="font-normal">
                        {/* {singlePropertyDetails?.gis_number} */}

                        {namanuDetail?.ward?.ward_name || "--"}
                      </span>
                    </p>
                  </div>
                  <div>
                    <p className="font-semibold">
                      {t("malmattaFerfar.zone")}:{" "}
                      <span className="font-normal">
                        {/* {singlePropertyDetails?.latitude} */}
                        {namanuDetail?.zone?.zoneName || "--"}
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </WhiteContainer>

    

            <WhiteContainer className="overflow-x-auto">
              <Tabs defaultValue="owner" className="w-full ">
                <TabsList className="w-[100%] md:w-fit !h-12">
                  <TabsTrigger
                    className=" p-[10px]  rounded text-[15px] "
                    value="owner"
                  >
                    {t("propertyView.ownerDetails")}{" "}
                  </TabsTrigger>
                  {/* <TabsTrigger
                    className=" p-[10px]  rounded text-[15px] "
                    value="property"
                  >
                    {t("propertyView.propertyDetails")}{" "}
                  </TabsTrigger> */}
                </TabsList>

                <TabsContent value="owner">
                  <OwnerForm
                    tableData={namanuDetail}
                    title={selecteValue}
                    divRef={divRef}
                  />
                </TabsContent>
                <TabsContent value="documents"></TabsContent>
              </Tabs>
            </WhiteContainer>
          </>
        )}
      </div>
    </div>
  );
};

export default PropertyFerfar;
