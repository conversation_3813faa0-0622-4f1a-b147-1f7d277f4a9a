import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTable51724152316783 implements MigrationInterface {
    name = 'UpdateTable51724152316783'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property_usage_details" ADD "construction_start_year" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property_usage_details" DROP COLUMN "construction_start_year"`);
    }

}
