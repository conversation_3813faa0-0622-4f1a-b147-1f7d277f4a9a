import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber } from 'class-validator';

export class UpdateTaxDetailsDto {
  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  all_property_tax_sum?: number;
  //heelo

  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  other_tax_sum_tax_previous?: number;

  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  tax_type_1_previous?: number;

  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  tax_type_2_previous?: number;

  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  tax_type_3_previous?: number;

  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  tax_type_4_previous?: number;

  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @IsNumber()
  tax_type_5_previous?: number;

  @ApiProperty({ type: Number, required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  tax_type_6_previous?: number;

  @ApiProperty({ type: Number, required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  tax_type_7_previous?: number;

  @ApiProperty({ type: Number, required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  tax_type_8_previous?: number;

  @ApiProperty({ type: Number, required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  tax_type_9_previous?: number;

  @ApiProperty({ type: Number, required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  tax_type_9_current?: number;

  @ApiProperty({ type: Number, required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  tax_type_10_previous?: number;
  @ApiProperty({ type: Number, required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  total_tax_previous?: number;



  @ApiProperty({ type: String, required: false })
  @IsOptional()
  owner_id?: string;

}
