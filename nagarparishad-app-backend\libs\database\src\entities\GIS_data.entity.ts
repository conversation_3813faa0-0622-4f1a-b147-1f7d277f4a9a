import {
  Entity,
  Column,
  PrimaryColumn,
  BaseEntity
} from 'typeorm';

@Entity('forms')
export class GIS_data_Entity extends BaseEntity {
  @PrimaryColumn({ type: 'int' })
  gid: number;

  @Column({ type: 'float', nullable: true })
  join_count: number;

  @Column({ type: 'float', nullable: true })
  target_fid: number;

  @Column({ type: 'float', nullable: true })
  form_id: number;

  @Column({ type: 'varchar', length: 254, nullable: true })
  form_number: string;

  @Column({ type: 'float', nullable: true })
  user_id: number;

  @Column({ type: 'numeric', nullable: true })
  longitude: number;

  @Column({ type: 'numeric', nullable: true })
  latitude: number;

  @Column({ type: 'varchar', length: 24, nullable: true })
  created_on: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  owner_name: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  old_property_no: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  new_property_no: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  property_name: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  property_address: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  property_user_type: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  property_user: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  resurvey_no: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  gat_no: string;

  @Column({ type: 'numeric', nullable: true })
  zone: number;

  @Column({ type: 'float', nullable: true })
  ward: number;

  @Column({ type: 'varchar', length: 254, nullable: true })
  mobile: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  email: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  aadhar_no: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  grid_no: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  gis_id: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  property_type: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  property_release_date: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  build_permission: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  build_completion_form: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  metal_road: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  is_toilet_available: string;

  @Column({ type: 'float', nullable: true })
  total_toilet: number;

  @Column({ type: 'varchar', length: 254, nullable: true })
  toilet_type: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  is_streetlight_available: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  is_water_line_available: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  total_water_line1: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  water_use_type: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  solar_panel_available: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  solar_panel_type: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  rain_water_harvesting: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  form_status: string;

  @Column({ type: 'smallint', nullable: true })
  approve_status: number;

  @Column({ type: 'smallint', nullable: true })
  approved_by: number;

  @Column({ type: 'varchar', length: 254, nullable: true })
  form_comment: string;

  @Column({ type: 'smallint', nullable: true })
  no_of_floor: number;

  @Column({ type: 'varchar', length: 254, nullable: true })
  property_images: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  plan_attachment: string;

  @Column({ type: 'numeric', nullable: true })
  plot_area: number;

  @Column({ type: 'numeric', nullable: true })
  property_area: number;

  @Column({ type: 'numeric', nullable: true })
  total_area: number;

  @Column({ type: 'float', nullable: true })
  fid_1: number;

  @Column({ type: 'varchar', length: 254, nullable: true })
  total_water_line2: string;

  @Column({ type: 'numeric', nullable: true })
  unique_numer: number;

  @Column({ type: 'varchar', length: 254, nullable: true })
  form_mode: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  is_drainage_available: string;

  @Column({ type: 'varchar', length: 254, nullable: true })
  snp_ward_1: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  zone_code: string;
}
