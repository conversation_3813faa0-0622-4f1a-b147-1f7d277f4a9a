import React, { Suspense, lazy } from "react";
import ErrorBoundary from "@/components/globalcomponent/ErrorBoundary";
import { Loader } from "@/components/globalcomponent/Loader";

const withLoader = (WrappedComponent) => {
  return (props) => (
    <ErrorBoundary>
      <Suspense fallback={<div className="flex justify-center items-center my-4"><Loader /></div>}>
        <WrappedComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

export default withLoader;
