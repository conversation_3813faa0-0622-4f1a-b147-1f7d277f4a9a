import {
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { parseCsv } from '../utils/helper';
import { CreatePropertyMasterDto } from './dto/create-property_master.dto';
import { UpdatePropertyMasterDto } from './dto/update-property_master.dto';
import { PropertyOwnerDetailsDto } from './dto/property-owner-details.dto';
import {
  PropertyMasterRepository,
  WardMasterRepository,
  ZoneMasterRepository,
  Property_Usage_DetailsRepository,
  Property_Owner_DetailsRepository,
  StreetMasterRepository,
  OwnerTypeRepository,
  PropertyTypeMasterRepository,
  UsageMasterRepository,
  PreviousOwnerRepository,
  PropertyFerfarDetailsRepository,
  ReassessmentRangeRepository,
  LogsRepository,
} from 'libs/database/repositories';
import { api, log_sub_type, log_type } from 'src/utils/constants';
import { PropertyMasterIdDto } from './dto/property_master.dto';
import { SearchPropertyDto } from './dto/search-property_master.dto';
import { AuthHelper } from '@helper/helpers';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { CSVDTO } from './dto/csv.validator.dto';
import * as csv from 'csv-parse';
import * as XLSX from 'xlsx';
import { UpdatePropertyDto } from './dto/update-property_master.dto';
import {
  UpdateOwnerDetailsDto,
  ChangeOwnershipDto,
  DeleteOwnerDto,
} from './dto/property-owner-details.dto';
import { PropertyFerfarAddUpdateDto } from './dto/property-ferfar-add-update.dto';
import { PropertyFerfarDeleteDto } from './dto/property-ferfar-delete.dto';
import {
  Gender,
  MaritalStatus,
  Property_Owner_Details_Entity,
  Property_Ferfar_Detail_Entity,
} from 'libs/database/entities';
import { EntityManager, In } from 'typeorm';
import { OwnerWrapperDto } from './dto/add-owner.dto';

@Injectable()
export class PropertyOwnerService {
  constructor(
    private readonly propertyMasterRepository: PropertyMasterRepository,
    private readonly wardRepository: WardMasterRepository,
    private readonly zoneRepository: ZoneMasterRepository,
    private readonly authHelper: AuthHelper,
    private readonly property_Usage_DetailsRepository: Property_Usage_DetailsRepository,
    private readonly property_Owner_DetailsRepository: Property_Owner_DetailsRepository,
    private readonly streetMasterRepository: StreetMasterRepository,
    private readonly ownerTypeRepository: OwnerTypeRepository,
    private readonly propertyTypeMasterRepository: PropertyTypeMasterRepository,
    private readonly usageTypeMasterRepository: UsageMasterRepository,
    private readonly previousOwnerRepository: PreviousOwnerRepository,
    private readonly propertyFerfarDetailsRepository: PropertyFerfarDetailsRepository,
    private readonly reassessmentRangeRepository: ReassessmentRangeRepository,
    private readonly logsRepository: LogsRepository,
  ) {}

  // Helper function to fetch all owners by property_id
  private async getAllPropertyOwners(property_id: string): Promise<any> {
    return await this.property_Owner_DetailsRepository.getData(property_id);
  }
  // Add a new owner
  async addNewOwners(
    property_id: string,
    ownerDetails: OwnerWrapperDto,
  ): Promise<any> {
    const currentFile = 'property_owner.service.ts';
    const currentApi = '/api/v1/property-owner/:property_id/add-new-owner';
    try {
      const property = await this.propertyMasterRepository.findOne({
        where: { property_id },
      });

      if (!property) throw new NotFoundException('Property not found');

      // Update ferfar remark
      console.log("addNewOwneraddNewOwner",ownerDetails)
      await this.propertyMasterRepository.update(
        { property_id },
        { ferfarRemark: ownerDetails.remark },
      );

      // Loop over all owners and save each
      for (const owner of ownerDetails.owners) {
        // Fetch owner type if needed
        const owner_type = await this.ownerTypeRepository.findOne({
          where: { owner_type_id: owner.owner_type_id },
        });

        const ownerEntity = this.property_Owner_DetailsRepository.create({
          ...owner,
          property,
          owner_type, // this should be an object or FK depending on your entity setup
          marital_status: owner.marital_status as MaritalStatus,
          gender: owner.gender as Gender,
        });

        await this.property_Owner_DetailsRepository.save(ownerEntity);
      }

      const property_owners = await this.getAllPropertyOwners(property_id);

      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.SAVE,
        file: currentFile,
        api: currentApi,
        message: 'New owner added successfully',
        data: { property_id, new_owners: ownerDetails.owners },
        user_id: null, // User ID should be passed from the controller if available
      });

      return {
        data: property_owners,
        message: 'Owner(s) added successfully',
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error adding new owner',
        data: { error: error.message, stack: error.stack },
        user_id: null,
      });
      throw error;
    }
  }

  // Delete owner (soft delete)
  async deleteOwner(
    property_owner_details_id: string,
    deleteOwnerDto?: any,
  ): Promise<any> {
    const currentFile = 'property_owner.service.ts';
    const currentApi = '/api/v1/property-owner/delete-owner/:id';
    try {
      const owner = await this.property_Owner_DetailsRepository.findOne({
        where: { property_owner_details_id },
        relations: ['property', 'owner_type'],
      });
      if (!owner) throw new NotFoundException('Owner not found');

      const owner_data =
        await this.property_Owner_DetailsRepository.getProperty_id(
          property_owner_details_id,
        );

      await this.previousOwnerRepository.saveData({
        property: owner.property,
        owner_type: owner.owner_type,
        name: owner.name,
        mobile_number: owner.mobile_number,
        email_id: owner.email_id,
        aadhar_number: owner.aadhar_number,
        pan_card: owner.pan_card,
          recordCreatedTime: owner.createdAt, 

      });

      owner.deletedAt = new Date();
      owner.last_action_done = 'delete_owner';
      await this.property_Owner_DetailsRepository.save(owner);

      const property_owners = await this.getAllPropertyOwners(owner_data);

      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.DELETE,
        file: currentFile,
        api: currentApi,
        message: 'Owner deleted successfully',
        data: { property_owner_details_id },
        user_id: null,
      });

      return {
        data: property_owners,
        message: 'Owner deleted successfully and moved to previous owners.',
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error deleting owner',
        data: { error: error.message, stack: error.stack },
        user_id: null,
      });
      throw error;
    }
  }

  async changeOwnership(
    property_owner_details_id: string,
    is_owner: boolean,
  ): Promise<any> {
    const currentFile = 'property_owner.service.ts';
    const currentApi = '/api/v1/property-owner/change-ownership/:id';
    try {
      const owner = await this.property_Owner_DetailsRepository.findOne({
        where: { property_owner_details_id },
      });
      if (!owner) throw new NotFoundException('Owner not found');
      const owner_data =
        await this.property_Owner_DetailsRepository.getProperty_id(
          property_owner_details_id,
        );
      owner.is_owner = is_owner;
      owner.last_action_done = 'is_owner_change';
      await this.property_Owner_DetailsRepository.save(owner);
      const property_owners = await this.getAllPropertyOwners(owner_data);

      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.UPDATE,
        file: currentFile,
        api: currentApi,
        message: 'Ownership changed successfully',
        data: { property_owner_details_id, is_owner },
        user_id: null,
      });

      return {
        data: property_owners,
        message: 'Ownership changed successfully',
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error changing ownership',
        data: { error: error.message, stack: error.stack },
        user_id: null,
      });
      throw error;
    }
  }

  async updateOwner(
    property_owner_details_id: string,
    updateOwnerDetails: UpdateOwnerDetailsDto,
  ): Promise<any> {
    const currentFile = 'property_owner.service.ts';
    const currentApi = '/api/v1/property-owner/correction/:id';
    try {
      const owner = await this.property_Owner_DetailsRepository.findOne({
        where: { property_owner_details_id },
        relations: ['property'],
      });
      if (!owner) throw new NotFoundException('Owner not found');

      const ownerData =
        await this.property_Owner_DetailsRepository.getProperty_id(
          property_owner_details_id,
        );

      if (updateOwnerDetails.owner_type_id) {
        const ownerType = await this.ownerTypeRepository.findOne({
          where: { owner_type_id: updateOwnerDetails.owner_type_id },
        });
        if (!ownerType) throw new NotFoundException('Owner type not found');
        owner.owner_type = ownerType;
      }

      Object.assign(owner, updateOwnerDetails);
      owner.last_action_done = 'correction';

      await this.property_Owner_DetailsRepository.save(owner);

      const propertyOwners = await this.getAllPropertyOwners(ownerData);

      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.UPDATE,
        file: currentFile,
        api: currentApi,
        message: 'Owner details updated successfully',
        data: { property_owner_details_id, changes: updateOwnerDetails },
        user_id: null,
      });

      return {
        data: propertyOwners,
        message: 'Owner updated successfully',
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error updating owner details',
        data: { error: error.message, stack: error.stack },
        user_id: null,
      });
      throw error;
    }
  }
  async processFerfarAddUpdate(ferfarDto: PropertyFerfarAddUpdateDto): Promise<any> {
    // Start a transaction to ensure data consistency
    return await this.propertyMasterRepository.manager.transaction(
      async (transactionManager: EntityManager) => {
        try {
          // 1. Validate property exists
          const property = await transactionManager.findOne(
            this.propertyMasterRepository.target,
            {
              where: { property_id: ferfarDto.property_id },
            },
          );

          if (!property) {
            throw new NotFoundException('Property not found');
          }

          // 2. Update property remark
          if (ferfarDto.remark) {
            await transactionManager.update(
              this.propertyMasterRepository.target,
              { property_id: ferfarDto.property_id },
              { ferfarRemark: ferfarDto.remark },
            );
          }

          // 3. Process each owner (add or update)
          const processedOwners = [];
          for (const ownerData of ferfarDto.owners) {
            // Check if this is an update (has property_owner_details_id) or add operation
            if (ownerData.property_owner_details_id) {
              // Update existing owner
              const existingOwner = await transactionManager.findOne(
                this.property_Owner_DetailsRepository.target,
                {
                  where: { property_owner_details_id: ownerData.property_owner_details_id },
                  relations: ['property', 'owner_type'],
                },
              );

              if (!existingOwner) {
                throw new NotFoundException(`Owner with ID ${ownerData.property_owner_details_id} not found`);
              }

              // Update owner type if changed
              if (ownerData.owner_type_id && ownerData.owner_type_id !== existingOwner.owner_type.owner_type_id) {
                const ownerType = await transactionManager.findOne(
                  this.ownerTypeRepository.target,
                  {
                    where: { owner_type_id: ownerData.owner_type_id },
                  },
                );

                if (!ownerType) {
                  throw new NotFoundException('Owner type not found');
                }

                existingOwner.owner_type = ownerType;
              }

              // Update other owner fields
              Object.assign(existingOwner, {
                name: ownerData.name || existingOwner.name,
                mobile_number: ownerData.mobile_number || existingOwner.mobile_number,
                email_id: ownerData.email_id || existingOwner.email_id,
                aadhar_number: ownerData.aadhar_number || existingOwner.aadhar_number,
                gender: ownerData.gender || existingOwner.gender,
                marital_status: ownerData.marital_status || existingOwner.marital_status,
                pan_card: ownerData.pan_card || existingOwner.pan_card,
                partner_name: ownerData.partner_name || existingOwner.partner_name,
                remark: ferfarDto.remark || existingOwner.remark,
                last_action_done: 'correction',
              });

              const updatedOwner = await transactionManager.save(existingOwner);
              processedOwners.push(updatedOwner);
            } else {
              // Add new owner
              const owner_type = await transactionManager.findOne(
                this.ownerTypeRepository.target,
                {
                  where: { owner_type_id: ownerData.owner_type_id },
                },
              );

              if (!owner_type) {
                throw new NotFoundException(`Owner type with ID ${ownerData.owner_type_id} not found`);
              }

              const ownerEntity = transactionManager.create(
                this.property_Owner_DetailsRepository.target,
                {
                  ...ownerData,
                  property,
                  owner_type,
                  marital_status: ownerData.marital_status as MaritalStatus,
                  gender: ownerData.gender as Gender,
                  remark: ferfarDto.remark,
                  last_action_done: 'add_owner',
                },
              );

              const newOwner = await transactionManager.save(ownerEntity);
              processedOwners.push(newOwner);
            }
          }

          // 4. Create ferfar record if needed
          if (ferfarDto.remark || ferfarDto.photos?.length > 0 || ferfarDto.documents?.length > 0) {
            const ferfarRecord = transactionManager.create(
              Property_Ferfar_Detail_Entity,
              {
                property,
                reason: ferfarDto.remark || '',
                photo_image_paths: ferfarDto.photos || [],
                document_image_path: ferfarDto.documents || [],
                previous_owner_names: [],
                year: new Date().getFullYear().toString(),
              },
            );
            await transactionManager.save(ferfarRecord);
          }

          // 5. Get updated property owners
          const updatedOwners = await this.property_Owner_DetailsRepository.getData(
            ferfarDto.property_id,
          );

          return {
            success: true,
            message: 'Property ferfar add/update processed successfully',
            data: {
              property,
              processedOwners,
              allOwners: updatedOwners,
            },
          };
        } catch (error) {
          throw error;
        }
      },
    );
  }

  // Process property ferfar delete operations
  async processFerfarDelete(ferfarDto: PropertyFerfarDeleteDto): Promise<any> {
    // Start a transaction to ensure data consistency
    return await this.propertyMasterRepository.manager.transaction(
      async (transactionManager: EntityManager) => {
        try {
          // 1. Validate property exists
          const property = await transactionManager.findOne(
            this.propertyMasterRepository.target,
            {
              where: { property_id: ferfarDto.property_id },
            },
          );

          if (!property) {
            throw new NotFoundException('Property not found');
          }

          // 2. Update property remark
          if (ferfarDto.remark) {
            await transactionManager.update(
              this.propertyMasterRepository.target,
              { property_id: ferfarDto.property_id },
              { ferfarRemark: ferfarDto.remark },
            );
          }

          // 3. Process each owner to delete
          const deletedOwners = [];
          for (const ownerId of ferfarDto.owner_ids) {
            const owner = await transactionManager.findOne(
              this.property_Owner_DetailsRepository.target,
              {
                where: { property_owner_details_id: ownerId },
                relations: ['property', 'owner_type'],
              },
            );

            if (!owner) {
              throw new NotFoundException(`Owner with ID ${ownerId} not found`);
            }

            // Create entry in previous owners table
            const previousOwner = transactionManager.create(
              this.previousOwnerRepository.target,
              {
                property: owner.property,
                owner_type: owner.owner_type,
                name: owner.name,
                mobile_number: owner.mobile_number,
                email_id: owner.email_id,
                aadhar_number: owner.aadhar_number,
                pan_card: owner.pan_card,
              },
            );
            await transactionManager.save(previousOwner);

            // Soft delete the owner
            owner.deletedAt = new Date();
            owner.last_action_done = 'delete_owner';
            await transactionManager.save(owner);

            deletedOwners.push(ownerId);
          }

          // 4. Create ferfar record if needed
          if (ferfarDto.remark || ferfarDto.photos?.length > 0 || ferfarDto.documents?.length > 0) {
            const ferfarRecord = transactionManager.create(
              Property_Ferfar_Detail_Entity,
              {
                property,
                reason: ferfarDto.remark || '',
                photo_image_paths: ferfarDto.photos || [],
                document_image_path: ferfarDto.documents || [],
                previous_owner_names: [],
                year: new Date().getFullYear().toString(),
              },
            );
            await transactionManager.save(ferfarRecord);
          }

          // 5. Get updated property owners
          const updatedOwners = await this.property_Owner_DetailsRepository.getData(
            ferfarDto.property_id,
          );

          return {
            success: true,
            message: 'Property ferfar delete processed successfully',
            data: {
              property,
              deletedOwners,
              allOwners: updatedOwners,
            },
          };
        } catch (error) {
          throw error;
        }
      },
    );
  }

  // Add multiple owners to a property
  async addMultipleOwners(property_id: string, payload: any): Promise<any> {
    const { owners, remark, photos, documents } = payload;

    if (!owners || !Array.isArray(owners) || owners.length === 0) {
      throw new BadRequestException('At least one owner must be provided');
    }

    const property = await this.propertyMasterRepository.findOne({
      where: { property_id },
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    // Update property remark if provided
    if (remark) {
      await this.propertyMasterRepository.update(
        { property_id },
        { ferfarRemark: remark },
      );
    }

    // Process each owner
    const addedOwners = [];
    for (const ownerData of owners) {
      const owner_type = await this.ownerTypeRepository.findOne({
        where: { owner_type_id: ownerData.owner_type_id },
      });

      if (!owner_type) {
        throw new NotFoundException(`Owner type with ID ${ownerData.owner_type_id} not found`);
      }

      const ownerEntity = this.property_Owner_DetailsRepository.create({
        ...ownerData,
        property,
        owner_type,
        marital_status: ownerData.marital_status as MaritalStatus,
        gender: ownerData.gender as Gender,
        remark: remark, // Use the global remark
        last_action_done: 'add_owner',
      });

      const savedOwner = await this.property_Owner_DetailsRepository.save(ownerEntity);
      addedOwners.push(savedOwner);
    }

    // Create ferfar record if needed
    // This could be optional based on your business logic
    if (remark || photos?.length > 0 || documents?.length > 0) {
      const ferfarRecord = this.propertyFerfarDetailsRepository.create({
        property,
        reason: remark || '',
        photo_image_paths: photos || [],
        document_image_path: documents || [],
        previous_owner_names: [],
        year: new Date().getFullYear().toString(),
      });
      await this.propertyFerfarDetailsRepository.save(ferfarRecord);
    }

    const updatedOwners = await this.getAllPropertyOwners(property_id);
    return {
      success: true,
      message: 'Owners added successfully',
      data: updatedOwners,
    };
  }

  // Update an owner and add new owners
  async updateWithNewOwners(property_id: string, payload: any): Promise<any> {
    const { updatedOwner, newOwners, remark, photos, documents } = payload;

    if (!updatedOwner || !updatedOwner.property_owner_details_id) {
      throw new BadRequestException('Updated owner details must be provided');
    }

    const property = await this.propertyMasterRepository.findOne({
      where: { property_id },
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    // Update property remark if provided
    if (remark) {
      await this.propertyMasterRepository.update(
        { property_id },
        { ferfarRemark: remark },
      );
    }

    // Update the existing owner
    const existingOwner = await this.property_Owner_DetailsRepository.findOne({
      where: { property_owner_details_id: updatedOwner.property_owner_details_id },
      relations: ['property', 'owner_type'],
    });

    if (!existingOwner) {
      throw new NotFoundException('Owner not found');
    }

    // Update owner type if changed
    if (updatedOwner.owner_type_id && updatedOwner.owner_type_id !== existingOwner.owner_type.owner_type_id) {
      const ownerType = await this.ownerTypeRepository.findOne({
        where: { owner_type_id: updatedOwner.owner_type_id },
      });

      if (!ownerType) {
        throw new NotFoundException('Owner type not found');
      }

      existingOwner.owner_type = ownerType;
    }

    // Update other owner fields
    Object.assign(existingOwner, {
      name: updatedOwner.name || existingOwner.name,
      mobile_number: updatedOwner.mobile_number || existingOwner.mobile_number,
      email_id: updatedOwner.email_id || existingOwner.email_id,
      aadhar_number: updatedOwner.aadhar_number || existingOwner.aadhar_number,
      gender: updatedOwner.gender || existingOwner.gender,
      marital_status: updatedOwner.marital_status || existingOwner.marital_status,
      pan_card: updatedOwner.pan_card || existingOwner.pan_card,
      partner_name: updatedOwner.partner_name || existingOwner.partner_name,
      remark: remark || existingOwner.remark,
      last_action_done: 'correction',
    });

    await this.property_Owner_DetailsRepository.save(existingOwner);

    // Add new owners if provided
    const addedOwners = [];
    if (newOwners && Array.isArray(newOwners) && newOwners.length > 0) {
      for (const newOwnerData of newOwners) {
        const owner_type = await this.ownerTypeRepository.findOne({
          where: { owner_type_id: newOwnerData.owner_type_id },
        });

        if (!owner_type) {
          throw new NotFoundException(`Owner type with ID ${newOwnerData.owner_type_id} not found`);
        }

        const ownerEntity = this.property_Owner_DetailsRepository.create({
          ...newOwnerData,
          property,
          owner_type,
          marital_status: newOwnerData.marital_status as MaritalStatus,
          gender: newOwnerData.gender as Gender,
          remark: remark, // Use the global remark
          last_action_done: 'add_owner',
        });

        const savedOwner = await this.property_Owner_DetailsRepository.save(ownerEntity);
        addedOwners.push(savedOwner);
      }
    }

    // Create ferfar record if needed
    if (remark || photos?.length > 0 || documents?.length > 0) {
      const ferfarRecord = this.propertyFerfarDetailsRepository.create({
        property,
        reason: remark || '',
        photo_image_paths: photos || [],
        document_image_path: documents || [],
        previous_owner_names: [],
        year: new Date().getFullYear().toString(),
      });
      await this.propertyFerfarDetailsRepository.save(ferfarRecord);
    }

    const updatedOwners = await this.getAllPropertyOwners(property_id);
    return {
      success: true,
      message: 'Owner updated and new owners added successfully',
      data: {
        updatedOwner: existingOwner,
        newOwners: addedOwners,
        allOwners: updatedOwners,
      },
    };
  }




}
