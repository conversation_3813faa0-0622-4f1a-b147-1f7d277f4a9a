import {
  BaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('election_boundary_master')
export class ElectionBoundaryMasterEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  electionBoundary_id: string;

  @Column({ name: 'election_boundary_name', type: String, nullable: false })
  electionBoundaryName: string;

  /*
   * Create and Update Date Columns
   */
  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
