import { resolve } from "path";
import ReportApi from "../../services/ReportServices";
import { useQuery } from "@tanstack/react-query";
import { rejects } from "assert";

//namuns 8
const fetchMilkatKarAkarni = async () => {
  return new Promise((resolve, reject) => {
    ReportApi.getMilkatKarAkarni((response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};


export const useNamunaController = () => {
  //namuns 8
  const { data: namunaeight, isLoading: namunaEightDataLoading } = useQuery({
    queryKey: ["namunaeight"],
    queryFn: () => fetchMilkatKarAkarni(),
    staleTime: 10 * 60 * 2,
    refetchOnWindowFocus: true,
  });



  return {
    namunaEightDetails: namunaeight || {},
    namunaEightDataLoading
  };
};
