import {
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import { CreateZoneMasterDto } from './dto/create-zone-master.dto';

import { ZoneMasterRepository } from 'libs/database/repositories/zone-master.repository';
import { GetWardMastereDto, ZoneMasterDto } from './dto/zone-master.dto';
import { UpdateZoneMasterDto } from './dto/update-zone-master.dto';
import { WardMasterRepository } from 'libs/database/repositories';

@Injectable()
export class ZoneMasterService {
  constructor(
    private readonly zoneMasterRepository: ZoneMasterRepository,
    private readonly wardMasterRepository: WardMasterRepository,
  ) {}

  async create(createZoneMasterDto: CreateZoneMasterDto) {
    try {
      const SaveZone =
        await this.zoneMasterRepository.saveZone(createZoneMasterDto);

      return {
        message: 'Zone Saved SuccessFully',
        data: SaveZone,
      };
    } catch (error) {
      throw error;
    }
  }

  async findAllZone() {
    try {
      const getAllZone = await this.zoneMasterRepository.findAllZone();

      if (!getAllZone) {
        throw new NotFoundException('Zone Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllZone,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(zoneMasterDto: ZoneMasterDto) {
    try {
      const { zone_id } = zoneMasterDto;
      const checkZone = await this.zoneMasterRepository.findById(zone_id);

      if (!checkZone) {
        throw new NotFoundException('Zone Not Found');
      }

      return {
        message: 'Ward Found Success',
        data: checkZone,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    zoneMasterDto: ZoneMasterDto,
    updateZoneMasterDto: UpdateZoneMasterDto,
  ) {
    try {
      const { zone_id } = zoneMasterDto;

      const checkzone = await this.zoneMasterRepository.findById(zone_id);

      if (!checkzone) {
        throw new NotFoundException('Ward Not Found');
      }

      const updateZone = await this.zoneMasterRepository.updateZone(
        zone_id,
        updateZoneMasterDto,
      );

      if (updateZone.affected === 0) {
        throw new NotAcceptableException('Failed to update ');
      }
      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(zoneMasterDto: ZoneMasterDto) {
    try {
      const { zone_id } = zoneMasterDto;

      const checkWard = await this.zoneMasterRepository.findById(zone_id);

      if (!checkWard) {
        throw new NotFoundException('Ward Not Found');
      }

      const deleteZone = await this.zoneMasterRepository.deleteZone(zone_id);

      if (deleteZone.affected === 0) {
        throw new NotAcceptableException('Failed To Delete ward');
      }

      return {
        message: 'Zone Deleted SuccessFully',
        data: deleteZone,
      };
    } catch (error) {
      throw error;
    }
  }

  async getByWard(getByWard: GetWardMastereDto) {
    try {
      const { ward } = getByWard;
      const checkWard = await this.wardMasterRepository.findWardById(ward);

      if (!checkWard) {
        throw new NotFoundException('Ward Not Found');
      }

      const getData = await this.zoneMasterRepository.getData(ward);

      if (!getData || getData.length === 0) {
        throw new NotFoundException('Ward Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getData,
      };
    } catch (error) {
      throw error;
    }
  }
}
