import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import {
  ConstructionClassDto,
  CreateConstructionClassDto,
  UpdateConstructionClassDto,
} from './dto/constructionClass.dto';
import { ConstructionClassRepository } from 'libs/database/repositories';

@Injectable()
export class ConstructionClassService {
  constructor(
    private readonly constructionClassRepository: ConstructionClassRepository,
  ) {}
  async create(createConstructionClassDto: CreateConstructionClassDto) {
    try {
      const saveData = await this.constructionClassRepository.saveData(
        createConstructionClassDto,
      );

      return {
        message: 'Record Saved SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllData =
        await this.constructionClassRepository.findAllLocation();

      if (!getAllData) {
        throw new NotFoundException('Record Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(constructionClassDto: ConstructionClassDto) {
    try {
      const { constructionClass_id } = constructionClassDto;
      const checkData =
        await this.constructionClassRepository.findById(constructionClass_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      return {
        message: 'Data found Sucess',
        data: checkData,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    constructionClassDto: ConstructionClassDto,
    updateConstructionClassDto: UpdateConstructionClassDto,
  ) {
    try {
      const { constructionClass_id } = constructionClassDto;
      const checkData =
        await this.constructionClassRepository.findById(constructionClass_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      const updateData = await this.constructionClassRepository.updateData(
        constructionClass_id,
        updateConstructionClassDto,
      );

      if (updateData.affected === 0) {
        throw new NotAcceptableException('Failed to update Data');
      }

      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(constructionClassDto: ConstructionClassDto) {
    try {
      const { constructionClass_id } = constructionClassDto;
      const checkData =
        await this.constructionClassRepository.findById(constructionClass_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      const deleteData =
        await this.constructionClassRepository.deleteData(constructionClass_id);

      if (deleteData.affected === 0) {
        throw new NotAcceptableException('Failed to delete data');
      }

      return {
        message: 'data Delete Successfully',
      };
    } catch (error) {
      throw error;
    }
  }
}
