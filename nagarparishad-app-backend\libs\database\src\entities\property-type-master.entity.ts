import {
  BaseEntity,
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Property_type_class_master } from './property_type_classMaster.entity';

@Entity('property_type_master')
export class PropertyTypeMasterEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  propertyType_id: string;

  @Column({ name: 'property_type', type: String, nullable: false })
  propertyType: string;

  @ManyToOne(() =>Property_type_class_master , (Class) => Class.property_type_class_id)
  @JoinColumn({ name: 'property_type_class_id' })
  property_type_class: Property_type_class_master; //wardId to ward

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
