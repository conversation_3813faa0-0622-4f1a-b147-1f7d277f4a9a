import React, { useState, useRef } from "react";
import { CircleX, Eye, UploadCloudIcon, X } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { FormLabel } from "@/components/ui/form";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { Trash2 } from 'lucide-react';
import pdfIcon from "@/assets/img/files/pdf.png";
import wordIcon from "@/assets/img/files/word.png";

const DocumentUpload = ({ onUploadComplete, title, btnText, uploadIcon }: any) => {
    const [isOpen, setIsOpen] = useState(false);
    const [progress, setProgress] = useState(0);
    const inputRef = useRef(null);
    const [uploadStatus, setUploadStatus] = useState("select");
    const [selectedFiles, setSelectedFiles] = useState<any[]>([]);
    const [filePreviews, setFilePreviews] = useState<any[]>([]);
    const [selectedFileCount, setSelectedFileCount] = useState(0);

    const { t } = useTranslation();

    function handleDialogOpen() {
        setIsOpen(true);
    }

    function handleDialogClose() {
        setIsOpen(false);
    }

    const handleFileChange = async (event: any) => {
        const newFiles: File[] = Array.from(event.target.files);
        if (!newFiles.length) return;

        const previews = await Promise.all(
            newFiles.map((file) =>
                new Promise((resolve) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        resolve({
                            name: file.name,
                            url: e.target?.result,
                            size: file.size,
                            type: file.type,
                previewUrl: URL.createObjectURL(file),
                        });
                    };
                    reader.readAsDataURL(file);
                })
            )
        );

        setSelectedFiles((prev) => [...prev, ...newFiles]);
        setFilePreviews((prev) => [...prev, ...previews]);
        setSelectedFileCount((prev) => prev + newFiles.length);
    };

    const handleUpload = async () => {
        try {
            setUploadStatus("uploading");
            await new Promise((res) => setTimeout(res, 500)); // fake delay
            onUploadComplete(filePreviews);
            setUploadStatus("uploaded");
        } catch (error) {
            console.error("Upload failed:", error);
            setUploadStatus("select");
        }
        handleDialogClose();
        // handleClear();
    };

    const handleClear = () => {
        setProgress(0);
        setSelectedFiles([]);
        setFilePreviews([]);
        setSelectedFileCount(0);
    };

    const handleRemoveFile = (fileToRemove: any) => {
        const updatedFiles = selectedFiles.filter((file) => file.name !== fileToRemove.name);
        const updatedPreviews = filePreviews.filter((file) => file.name !== fileToRemove.name);

        setSelectedFiles(updatedFiles);
        setFilePreviews(updatedPreviews);
        setSelectedFileCount((prev) => Math.max(prev - 1, 0));
                    onUploadComplete(updatedPreviews);

    };

    const getIconForFileType = (fileType: string) => {
        if (fileType === "application/pdf") return pdfIcon;
        if (
            fileType === "application/msword" ||
            fileType === "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
            return wordIcon;
        return null;
    };

    return (
        <>
            <div className="grid-cols-subgrid">
                <FormLabel>{t("propertyLocationDetailsForm.uploadFile")}</FormLabel>
                <div className="relative">
                    <div className="2xl:w-[90%] w-full h-fit py-5 border rounded-20 mt-2 flex flex-col justify-center items-center">
                        <div>
                            {uploadIcon}
                            <h2 className="text-center mt-2 text-lg font-medium text-[#222222]">
                                {title}
                            </h2>
                            <div className="w-full relative my-3">
                                <Input
                                    placeholder={t("propertyLocationDetailsForm.uploadFile")}
                                    multiple
                                    onClick={() => handleDialogOpen()}
                                    className="text-transparent w-full absolute top-0 left-0 opacity-0 cursor-pointer"
                                />
                                <Button className="h-10 mt-[-2px] px-4 py-5 cursor-pointer" type="button">
                                    {btnText}
                                </Button>
                            </div>
                        </div>
                        <hr className="w-full my-3" />

                        <div className="w-full mt-2 px-5">
                            {filePreviews.length > 0 ? (
                                <div>
                                    <div className="flex justify-between">
                                        <h3 className="text-sm mb-2">{t("uploadedFiles")}</h3>
                                        <p className="text-sm font-normal">
                                            {selectedFileCount}{" "}
                                            {(selectedFileCount > 1) ? "files" : "file"}
                                        </p>
                                    </div>
                                    <div className="list-disc flex gap-3 max-w-full overflow-x-auto">
                                        {filePreviews.map((file, index) => (
                                            <div
                                                key={index}
                                                className="mb-2 min-w-[145px] w-[160px] h-full border border-gray-200 p-2 rounded-xl shadow-sm relative"
                                            >
                                                {file.type.startsWith("image/") ? (
                                                    <img src={file.url} alt={file.name} className="w-full h-20 object-contain mb-2" />
                                                ) : (
                                                    <img src={getIconForFileType(file.type)} alt={file.name} className="w-full h-20 object-contain mb-2" />
                                                )}
                                                <p className="break-words text-xs one-line-ellipsis" title={file.name}>
                                                    {file.name}
                                                </p>
                                                <div className="bg-slate-200/60 w-full h-full absolute top-0 left-0 flex items-center justify-center gap-3 opacity-0 hover:opacity-100 transition-opacity duration-500">
                                                    <a
                                                        className="text-gray-600 bg-gray-300 p-2 rounded-full h-fit"
                                                        href={file.previewUrl}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                    >
                                                        <Eye />
                                                    </a>
                                                    <button
                                                        type="button"
                                                        className="text-red-400 bg-red-200 p-2 rounded-full h-fit"
                                                        onClick={() => handleRemoveFile(file)}
                                                    >
                                                        <Trash2 />
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ) : (
                                <p className="text-center">No files uploaded.</p>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            <Dialog open={isOpen}>
                <DialogContent className="sm:max-w-[800px] bg-white property-location-dialog">
                    <DialogHeader>
                        <DialogTitle className="text-left">Upload</DialogTitle>
                    </DialogHeader>
                    <DialogClose
                        onClick={() => handleDialogClose()}
                        className="w-6 h-6 absolute top-[12px] right-[12px]"
                    >
                        <X className="w-6 h-6 z-0" />
                    </DialogClose>
                    <div className="flex">
                        <form
                            className="w-full h-auto pr-4 bg-lightGray-0 rounded-borderRadius10"
                            onSubmit={(e) => e.preventDefault()}
                        >
                            <input
                                type="file"
                                accept="image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                                onChange={handleFileChange}
                                ref={inputRef}
                                className="hidden"
                                id="input-file-upload"
                                multiple
                            />
                            <label
                                htmlFor="input-file-upload"
                                className="rounded-lg border border-dashed border-[#8E929A] bg-[#f6f7f9] p-4 block text-center cursor-pointer"
                            >
                                <UploadCloudIcon className="m-auto w-10 h-10 text-BlueText" />
                                <p className="text-md font-semibold text-TextGray">Drag & drop or <span className="underline text-BlueText">Browse</span></p>
                                <p className="text-sm mt-1 mb-5">Supported: JPEG, PNG, PDF, Word</p>
                            </label>
                        </form>
                        <div className="w-full h-auto px-4 bg-lightGray-0">
                            {selectedFiles.length > 0 ? (
                                <>
                                    <div className="flex justify-between">
                                        <p className="text-sm font-semibold">Ready to upload</p>
                                        <p className="text-sm font-normal">{selectedFiles.length} {(selectedFiles.length > 1) ? "files" : "file"}</p>
                                    </div>
                                    <div className="max-h-44 pr-1 overflow-y-auto">
                                        {filePreviews.map((file, index) => (
                                            <div key={index} className="mt-2">
                                                <div className="px-2 py-2 flex justify-between items-center rounded-md border border-gray-400">
                                                    <p className="text-sm selected-file-name" title={file.name}>{file.name}</p>
                                                    <p className="text-md cursor-pointer" onClick={() => handleRemoveFile(file)}>&times;</p>
                                                </div>
                                                <Progress value={progress} className="w-full h-1 bg-green-800" />
                                            </div>
                                        ))}
                                    </div>
                                    <div className="mt-2">
                                        <Button
                                            className="w-full capitalize"
                                            onClick={handleUpload}
                                            disabled={filePreviews.length !== selectedFiles.length}
                                        >
                                            UPLOAD FILES
                                        </Button>
                                    </div>
                                </>
                            ) : (
                                <p className="text-sm font-semibold text-center">NO FILES SELECTED</p>
                            )}
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default DocumentUpload;
