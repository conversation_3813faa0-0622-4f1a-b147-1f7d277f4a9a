import { RouteObject } from "react-router";
import React, { lazy, Suspense } from "react";
import Layout from "@/components/custom/Layout";
import DashboardLayout from "@/components/custom/DashboardLayout";
import ErrorBoundary from "@/components/globalcomponent/ErrorBoundary";
import { Loader } from "@/components/globalcomponent/Loader";
import withLoader from "./WithLoader";
import { AccessDenied } from "@/components/custom/AccessDenied/AccessDenied";
import PaymentDashboard from "@/pages/PaymentDashboard/PaymentDashboard";import CollectorMaster from "@/components/forms/CollectorMaster";
import ProtectedRoute from "./ProtectedRoute"; // Import the ProtectedRoute component
import ReassessmentMaster from "@/components/forms/SettingMasterForm/Tax_related_forms/ReAssesment";
import UserGuide from "@/components/custom/UserGuide/UserGuide";

const HomePage = withLoader(lazy(() => import("@/pages/Home")));
const LoginPage = withLoader(lazy(() => import("@/pages/LoginPage")));
const Dashboard = withLoader(lazy(() => import("@/pages/Dashboard/Dashboard")));
const PropertyRegistration = withLoader(lazy(() => import("@/pages/PropertyRegistration")));
const ElectionBoundryMaster = withLoader(lazy(() => import("@/components/forms/masterform/ElectionBoundryMaster")));
const UsageMaster = withLoader(lazy(() => import("@/components/forms/masterform/UsageMaster")));
const UsageSubMaster = withLoader(lazy(() => import("@/components/forms/masterform/UsageSubMaster")));
const LocationMaster = withLoader(lazy(() => import("@/components/forms/masterform/LocationMaster")));
const WardMaster = withLoader(lazy(() => import("@/components/forms/WardMaster")));
const AreaMaster = withLoader(lazy(() => import("@/components/forms/masterform/AreaMaster")));
const PropertyTypeMaster = withLoader(lazy(() => import("@/components/forms/masterform/PropertyTypeMaster")));
const PropertysubTypeMaster = withLoader(lazy(() => import("@/components/forms/masterform/PropertySubTypeMaster")));
const ConstructionClassMaster = withLoader(lazy(() => import("@/components/forms/masterform/ConstructionClassMaster")));
const AdministrativeBoundaryMaster = withLoader(lazy(() => import("@/components/forms/masterform/AdministrativeBoundaryMaster")));
const UserRegister = withLoader(lazy(() => import("@/components/forms/masterform/UserRegister")));
const UserRoleRegister = withLoader(lazy(() => import("@/components/custom/UserRoleRegister")));
const PropertyListing = withLoader(lazy(() => import("@/components/custom/Propertylisting")));
const ContactUs = withLoader(lazy(() => import("@/components/forms/ContactUs")));
const CreateRole = withLoader(lazy(() => import("@/components/custom/CreateRole")));
const SerchedUserList = withLoader(lazy(() => import("@/components/custom/SerchedUserList")));
const PaymentForm = withLoader(lazy(() => import("@/components/custom/PaymentForm")));
const PropertyDetails = withLoader(lazy(() => import("@/components/forms/PropertyDetails")));
const PropertyTaxRateMaster = withLoader(lazy(() => import("@/components/forms/masterform/PropertyTaxRateMaster")));
const ReadyRecknerRateForm = withLoader(lazy(() => import("@/components/forms/masterform/ReadyReckonerRateMaster")));
const PropertyDemand = withLoader(lazy(() => import("@/components/forms/DemandComponent")));
const GenerateMilkatKar = withLoader(lazy(() => import("@/components/forms/GenerateMilkatKar")));
const GenerateWarshikKar = withLoader(lazy(() => import("@/components/forms/GenerateWarshikKar")));
const ZoneMaster = withLoader(lazy(() => import("@/components/forms/masterform/ZoneMaster")));
const PropertyFerfar = withLoader(lazy(() => import("@/components/custom/PropertyFerfar")));
const PropertyFod = withLoader(lazy(() => import("@/components/custom/PropertyFod")));
const StreetMaster = withLoader(lazy(() => import("@/components/forms/masterform/StreetMaster")));
const PropertyView = withLoader(lazy(() => import("@/components/custom/PropertyView")));
const NamunaEight = withLoader(lazy(() => import("@/pages/NamunaEight")));
const NamunaNine = withLoader(lazy(() => import("@/pages/NamunaNine")));
const PropertyDemandView = withLoader(lazy(() => import("@/components/forms/DemandComponent/DemandViewDetails")));
const MilkatKarAkarniEightReport = withLoader(lazy(() => import("@/components/reports/MilkatKarAkarniEightReport")));
const VarshikAkarniNineReport = withLoader(lazy(() => import("@/components/reports/VarshikAkarniNineReport")));
const AssessmentReport = withLoader(lazy(() => import("@/components/reports/AssessmentReport")));
const NamunaTen = withLoader(lazy(() => import("@/pages/NamunaTen")));
const PropertyViewDetail = withLoader(lazy(() => import("@/components/custom/PropertyViewDetail/PropertyViewDetail")));
const GisCompare = withLoader(lazy(() => import("@/components/custom/GisCompare/GisCompare")));
const GisDataTable = withLoader(lazy(() => import("@/components/custom/GisDataTable")));
const DepreciationRateMaster = withLoader(lazy(() => import("@/components/forms/SettingMasterForm/Tax_related_forms/DepreciationRateMaster")));
const RRConstructionRateMaster = withLoader(lazy(() => import("@/components/forms/SettingMasterForm/Tax_related_forms/RRConstructionRateMaster")));
const RR_RateMaster = withLoader(lazy(() => import("@/components/forms/SettingMasterForm/Tax_related_forms/RR_RateMaster")));
const TaxRateMaster = withLoader(lazy(() => import("@/components/forms/SettingMasterForm/Tax_related_forms/TaxRateMaster")));
const WeightingRateMaster = withLoader(lazy(() => import("@/components/forms/SettingMasterForm/Tax_related_forms/WeightingRateMaster")));
const PropertyClassMaster = withLoader(lazy(() => import("@/components/forms/PropertyClassMaster")));
const PropertyFloorMaster = withLoader(lazy(() => import("@/components/forms/PropertyFloorMaster")));
const SolidWasteMaster = withLoader(lazy(() => import("@/components/forms/SettingMasterForm/Tax_related_forms/SolidWasteMaster")));
const Bill = withLoader(lazy(() => import("@/pages/Bill")));
const BillHistory = withLoader(lazy(() => import("@/pages/BillHistory")));
const BookMaster = withLoader(lazy(() => import("@/components/forms/SettingMasterForm/Tax_related_forms/BooKMaster")));
const FinancialYearMaster = withLoader(lazy(() => import("@/components/forms/SettingMasterForm/Tax_related_forms/FinancialYearMaster")));
const ReportModule = withLoader(lazy(() => import("@/components/reports/ReportModule"))); // Import the new ReportModule

// Import new pages
const PrivacyPolicy = withLoader(lazy(() => import("@/pages/PrivacyPolicy")));
const ForgotPassword = withLoader(lazy(() => import("@/pages/ForgotPassword")));





const NotFoundPage: React.FC = () => {
  return <div>404 - Page Not Found</div>;
};

const NotFoundPages: React.FC = () => {
  return <AccessDenied />;
};

const routes: RouteObject[] = [
  {
    path: "/",
    element: <Layout children={""} />,
    children: [
      { path: "", element: <HomePage /> },
      { path: "login", element: <LoginPage /> },
      { path: "forgot-password", element: <ForgotPassword /> },
      { path: "privacy-policy", element: <PrivacyPolicy /> },
      { path: "contact-us", element: <ContactUs /> },
      {
        path: "additional-collector-access",
        element: <ProtectedRoute element={<ErrorBoundary><div>Additional Collector Access Page</div></ErrorBoundary>} />,
      },
      { path: "add-property", element: <ProtectedRoute element={<ErrorBoundary><div>Add Property Page</div></ErrorBoundary>} /> },
      { path: "search-property", element: <ProtectedRoute element={<ErrorBoundary><div>Search Property Page</div></ErrorBoundary>} /> },
      { path: "search-list",  element:<SerchedUserList />  },
      { path: "search-list/detail", element: <PropertyViewDetail /> },
      { path: "payment", element:<PaymentForm /> },
      { path: "property-details", element:<PropertyDetails /> },
      { path: "*", element: <NotFoundPage /> },
    ],
  },
  {
    path: "/dashboard",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [
      { path: "", element: <Dashboard /> },
      { path: "user-registration", element: <UserRegister /> },
      { path: "user-role", element: <UserRoleRegister /> },
      { path: "create-role", element: <CreateRole /> },
      { path: "search-list", element: <SerchedUserList /> },
      { path: "user-guide", element: <UserGuide /> },
      { path: "*", element: <NotFoundPage /> },
    ],
  },
  {
    path: "/property",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [
      { path: "", element: <PropertyListing /> },
      { path: "property-registration", element: <PropertyRegistration /> },
      { path: "property-view", element: <PropertyView /> },
      { path: "property-demand", element: <PropertyDemand /> },
      { path: "property-demand-view", element: <PropertyDemandView /> },
      { path: "generate-milkat-kar", element: <GenerateMilkatKar /> },
      { path: "generate-warshik-kar", element: <GenerateWarshikKar /> },
      { path: "ferfar", element: <PropertyFerfar /> },
      { path: "property-fod", element: <PropertyFod /> },
      { path: "*", element: <NotFoundPage /> },
    ],
  },
  {
    path: "/namuna-eight",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [{ path: "", element: <NamunaEight /> }],
  },
  {
    path: "/namuna-nine",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [{ path: "", element: <NamunaNine /> }],
  },
  {
    path: "/namuna-ten",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [{ path: "", element: <NamunaTen /> }],
  },
  {
    path: "/gis",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [{ path: "", element: <GisCompare /> }],
  },
  {
    path: "/gis_data",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [{ path: "", element: <GisDataTable /> }],
  },
  {
    path: "/bill",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [{ path: "", element: <Bill /> }],
  },
  {
    path: "/BillHistory",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [{ path: "", element: <BillHistory /> }],
  },
  {
    path: "/register",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [
      { path: "namuna-eight-report", element: <MilkatKarAkarniEightReport /> },
      { path: "namuna-nine-report", element: <VarshikAkarniNineReport /> },
      { path: "assessment-report", element: <AssessmentReport /> },
    ],
  },
  {
    path: "/master",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [
      { path: "zone-master", element: <ZoneMaster /> },
      { path: "ward-master", element: <WardMaster /> },
      { path: "location-master", element: <LocationMaster /> },
      { path: "street-master", element: <StreetMaster /> },
      { path: "election-boundry-master", element: <ElectionBoundryMaster /> },
      { path: "usage-master", element: <UsageMaster /> },
      { path: "usage-sub-master", element: <UsageSubMaster /> },
      { path: "area-master", element: <AreaMaster /> },
      { path: "propertytype-master", element: <PropertyTypeMaster /> },
      { path: "propertysubtype-master", element: <PropertysubTypeMaster /> },
      { path: "propertyclass-master", element: <PropertyClassMaster /> },
      { path: "propertyfloor-master", element: <PropertyFloorMaster /> },
      { path: "construction-master", element: <ConstructionClassMaster /> },
      { path: "propertytaxrate-master", element: <PropertyTaxRateMaster /> },
      { path: "administrativeboundary-master", element: <AdministrativeBoundaryMaster /> },
      { path: "readyreckonerrate-master", element: <ReadyRecknerRateForm /> },
      { path: "collector-master", element: <CollectorMaster /> },
      { path: "*", element: <NotFoundPage /> },
    ],
  },
  {
    path: "/settings",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [
      { path: "master-depreciation-rate", element: <DepreciationRateMaster /> },
      { path: "master-rr-construction-rate", element: <RRConstructionRateMaster /> },
      { path: "master-rr-rate", element: <RR_RateMaster /> },
      { path: "master-tax-rate", element: <TaxRateMaster /> },
      { path: "master-weighting-rate", element: <WeightingRateMaster /> },
      { path: "master-solid-waste", element: <SolidWasteMaster /> },
      { path: "master-book", element: <BookMaster /> },
      { path: "master-financial-year", element: <FinancialYearMaster /> },
      { path: "reassesment", element: <ReassessmentMaster /> },
      { path: "*", element: <NotFoundPage /> },
    ],
  },

  {
    path: "/payment-dashboard",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [
      { path: "", element: <PaymentDashboard /> },
    ],
  },
  {
    path: "/reports",
    element: <ProtectedRoute element={<DashboardLayout children={""} />} />,
    children: [
      { path: "", element: <ReportModule /> }, // Route for the new ReportModule
    ],
  },
];

export default routes;
