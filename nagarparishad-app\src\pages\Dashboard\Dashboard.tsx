import React, { useContext, useEffect, useMemo, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import {
  AlertTriangle,
  ArrowUpDown,
  CalendarIcon,
  ChevronDown,
  ChevronsUpDown,
  ChevronUp,
  Factory,
  FileText,
  Home,
  MapPin,
  ShoppingBag,
  TrendingUp,
  Wheat,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import axios from "axios";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { toast } from "@/components/ui/use-toast";
import { useTranslation } from "react-i18next";
import PopUpScreen from "@/components/custom/PopUpScreen";
import { GlobalContext } from "@/context/GlobalContext";
import { DropdownMenuCheckboxItemProps } from "@radix-ui/react-dropdown-menu";
import Dashboardtable from "./Dashboardtable";
import { formatToRupees } from "../../controller/hepler/helper";
import {
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Tooltip,
  Legend,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LineChart,
  Line,
  Area,
  AreaChart,
  ComposedChart,
} from "recharts";
import { useMediaQuery } from "react-responsive";
import { usePropertytypeMasterController } from "@/controller/master/PropertyMasterController";
import BreadCrumb from "@/components/custom/BreadCrumb";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import DataTable from "./DataTable";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import PropertyApi from "@/services/PropertyServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";
import CountUp from "react-countup";
import { Loader } from "@/components/globalcomponent/Loader";
import DashboardService from "@/services/DashboardServices";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  Clock,
  XCircle,
  CreditCard,
  Banknote,
  Smartphone,
} from "lucide-react";
import Api from "@/services/ApiServices";
import { formatTimestamp } from "@/utils/helperFuntion";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/cards";

interface Transaction {
  id: string;
  propertyId: string;
  taxpayerName: string;
  amount: number;
  paymentMethod: "cash" | "online" | "cheque";
  status: "completed" | "pending" | "failed";
  timestamp: string;
}

export const RecentTransactions = ({ transactions, isLoading }) => {
  if (isLoading) {
    return (
      <Card className="bg-white dark:bg-zinc-700 shadow rounded-2xl p-2 h-full">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            अलीकडील व्यवहार
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton height={60} count={3} />
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-success" />;
      case "pending":
        return <Clock className="h-4 w-4 text-warning" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-destructive" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusBadge = (status) => {
    const variants = {
      completed: "bg-success/10 text-success border-success/20",
      pending: "bg-warning/10 text-warning border-warning/20",
      failed: "bg-destructive/10 text-destructive border-destructive/20",
    };
    return (
      <Badge variant="outline" className={variants[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPaymentIcon = (method) => {
    switch (method) {
      case "cash":
        return <Banknote className="h-4 w-4 text-muted-foreground" />;
      case "online":
        return <Smartphone className="h-4 w-4 text-muted-foreground" />;
      case "cheque":
        return <CreditCard className="h-4 w-4 text-muted-foreground" />;
      default:
        return <CreditCard className="h-4 w-4 text-muted-foreground" />;
    }
  };

  return (
    <Card className="bg-white dark:bg-zinc-700 shadow rounded-2xl p-2 h-full flex flex-col">
      <CardHeader className="pb-2 space-y-1 pt-3">
        <CardTitle className="text-lg font-semibold">अलीकडील व्यवहार</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto p-3 pt-1">
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <div
              key={transaction.id}
              className="flex items-center justify-between p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors duration-200"
            >
              <div className="flex items-center space-x-3">
                <div className="space-y-1">
                  <p className="text-sm font-medium">
                    {transaction.taxpayerName}
                  </p>
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <span>{transaction.propertyNumber}</span>
                    <span>•</span>
                    <span>{transaction.oldPropertyNumber}</span>
                    <span>•</span>
                    <div className="flex items-center space-x-1">
                      {getPaymentIcon(transaction.paymentMethod)}
                      <span className="capitalize">
                        {transaction.paymentMethod}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="text-right space-y-1">
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-muted-foreground">
                    {formatTimestamp(transaction?.timestamp)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

const FormSchema = z.object({
  fromDate: z.date({
    required_error: "A from date is required.",
  }),
  toDate: z.date({
    required_error: "A to date is required.",
  }),
});

const Widget = ({ title, children, className = "" }) => (
  <div
    className={cn(
      "bg-white dark:bg-zinc-700 shadow rounded-2xl p-4 flex flex-col",
      className
    )}
  >
    {title && <h2 className="text-lg font-semibold mb-4">{title}</h2>}
    <div className="flex-1 h-full">{children}</div>
  </div>
);

const Dashboard = () => {
  const baseURL = import.meta.env.VITE_APP_BASE_URL;
  const { t } = useTranslation();
  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(
    ModuleName.Property,
    FormName.Property,
    Action.CanRead
  );
  const userData = localStorage.getItem("UserData");
  const userData1 = JSON.parse(userData);
  const currentHour = new Date().getHours();
  const {
    propertytypeList,
    propertytypeStats,
    isLoading: isLoadingPropertyTypeStatsData,
  } = usePropertytypeMasterController();
  const [paymentModes, setPaymentModes] = useState({});
  const [usageTypeStats, setUsageTypeStats] = useState([]);
  const [totalPropertyStats, setTotalPropertyStats] = useState({});
  const [wardPropertyStats, setWardPropertyStats] = useState([]);
  const [recentTransactions, setRecentTransactions] = useState([]);
  const [taxCollectionTrend, setTaxCollectionTrend] = useState([]);
  const [wardPerformance, setWardPerformance] = useState([]);
  const [defaulterAnalysis, setDefaulterAnalysis] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: null });
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingWardPerformance, setIsLoadingWardPerformance] =
    useState(true);
  const [isLoadingUsageTypeStats, setIsLoadingUsageTypeStats] = useState(true);
  const [financialYears, setFinancialYears] = useState({
    loading: true,
    currentYear: null,
  });

  const totalTax =
    totalPropertyStats?.totalPaidTax + totalPropertyStats?.remainingTax;
  const collectedPercentage = totalTax
    ? (totalPropertyStats?.totalPaidTax / totalTax) * 100
    : 0;

  // Media queries for responsive design
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
  const isTablet = useMediaQuery({ query: "(max-width: 1024px)" });

  useEffect(() => {
    const fetchFinancialYears = async () => {
      setFinancialYears((prev) => ({ ...prev, loading: true }));
      try {
        const response = await Api.fyYears();
        if (response.data.statusCode === 200 && response.data.data) {
          const currentYear = response.data.data.find((pre) => pre.is_current);
          setFinancialYears((pre) => ({
            ...pre,
            loading: false,
            currentYear: currentYear?.financial_year_range,
          }));
        } else {
          setFinancialYears((prev) => ({ ...prev, loading: false }));
        }
      } catch (error) {
        console.error("Error fetching financial years:", error);
        toast({
          title: "Failed to fetch financial years.",
          variant: "destructive",
        });
        setFinancialYears((prev) => ({ ...prev, loading: false }));
      }
    };
    fetchFinancialYears();
  }, []);

  useEffect(() => {
    if (!financialYears.currentYear) return;

    const fetchDashboardData = async () => {
      setIsLoading(true);
      try {
        // Fire-and-forget calls with callbacks
        PropertyApi.fetchPaymentModes((response) => {
          if (response.status) setPaymentModes(response.data);
          else console.error("Error fetching payment modes:", response.data);
        });
        PropertyApi.fetchTotalPropertyStats((response) => {
          if (response.status) setTotalPropertyStats(response.data);
          else
            console.error(
              "Error fetching total property stats:",
              response.data
            );
        }, financialYears.currentYear);

        PropertyApi.fetchWardPropertyStats((response) => {
          if (response.status) setWardPropertyStats(response.data);
          else
            console.error("Error fetching ward property stats:", response.data);
        });

        // Promise-based calls
        await Promise.all([
          DashboardService.getRecentTransactions().then((response) => {
            if (response.status) {
              setRecentTransactions(
                Array.isArray(response.data.data) ? response.data.data : []
              );
            } else {
              console.error(
                "Error fetching recent transactions:",
                response.data
              );
              setRecentTransactions([]);
            }
          }),
          DashboardService.getTaxCollectionTrend(
            financialYears.currentYear
          ).then((response) => {
            if (response.status) {
              setTaxCollectionTrend(
                Array.isArray(response.data.data) ? response.data.data : []
              );
            } else {
              console.error(
                "Error fetching tax collection trend:",
                response.data
              );
              setTaxCollectionTrend([]);
            }
          }),
          DashboardService.getDefaulterAnalysis().then((response) => {
            if (response.status) {
              setDefaulterAnalysis(
                Array.isArray(response.data.data) ? response.data.data : []
              );
            } else {
              console.error(
                "Error fetching defaulter analysis:",
                response.data
              );
              setDefaulterAnalysis([]);
            }
          }),
        ]);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        toast({
          title: "Failed to fetch dashboard data.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    const fetchWardPerformanceData = async () => {
      setIsLoadingWardPerformance(true);
      try {
        const response = await DashboardService.getWardPerformance();
        if (response.status) {
          setWardPerformance(
            Array.isArray(response.data.data) ? response.data.data : []
          );
        } else {
          console.error("Error fetching ward performance:", response.data);
          setWardPerformance([]);
        }
      } catch (error) {
        console.error("Error fetching ward performance:", error);
        setWardPerformance([]);
      } finally {
        setIsLoadingWardPerformance(false);
      }
    };

    const fetchUsageTypeStatsData = () => {
      setIsLoadingUsageTypeStats(true);
      PropertyApi.fetchUsageTypePropertyStats((response) => {
        if (response.status) {
          setUsageTypeStats(response.data);
        } else {
          console.error("Error fetching usage type stats:", response.data);
          setUsageTypeStats([]);
        }
        setIsLoadingUsageTypeStats(false);
      });
    };

    fetchDashboardData();
    fetchWardPerformanceData();
    fetchUsageTypeStatsData();
  }, [financialYears.currentYear]);

  const data = useMemo(
    () =>
      propertytypeStats
        .filter(
          (item) => item.propertyType !== null && item.propertyType !== "एकूण"
        )
        .map((item) => ({
          name: item.propertyType,
          value: parseInt(item.propertyCount),
        })),
    [propertytypeStats]
  );

  let greetingMessage = "";
  if (currentHour < 12) {
    greetingMessage = t("goodMorning");
  } else if (currentHour < 18) {
    greetingMessage = t("goodAfternoon");
  } else {
    greetingMessage = t("goodnight");
  }

  const dynamicValues = {
    name: userData1?.firstName,
  };

  const { commonDialogue, setCommonDialogue } = useContext(GlobalContext);
  const [viewMore, setViewMore] = useState(false);
  const handleViewMore = () => {
    setViewMore(!viewMore);
  };

  const [usageTypeSortConfig, setUsageTypeSortConfig] = useState({
    key: null,
    direction: null,
  });

  const [wardSortConfig, setWardSortConfig] = useState({
    key: null,
    direction: null,
  });

  const requestSort = (table, key) => {
    const setSortConfig =
      table === "usageType" ? setUsageTypeSortConfig : setWardSortConfig;
    const currentSortConfig =
      table === "usageType" ? usageTypeSortConfig : wardSortConfig;
    let direction = "ascending";
    if (
      currentSortConfig.key === key &&
      currentSortConfig.direction === "ascending"
    ) {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  const sortedUsageTypeStats = React.useMemo(() => {
    let sortableItems = [...usageTypeStats];
    if (usageTypeSortConfig.key !== null) {
      sortableItems.sort((a, b) => {
        const aValue =
          usageTypeSortConfig.key === "usage_type"
            ? a[usageTypeSortConfig.key]
            : parseFloat(a[usageTypeSortConfig.key]);
        const bValue =
          usageTypeSortConfig.key === "usage_type"
            ? b[usageTypeSortConfig.key]
            : parseFloat(b[usageTypeSortConfig.key]);
        if (aValue < bValue)
          return usageTypeSortConfig.direction === "ascending" ? -1 : 1;
        if (aValue > bValue)
          return usageTypeSortConfig.direction === "ascending" ? 1 : -1;
        return 0;
      });
    }
    return sortableItems;
  }, [usageTypeStats, usageTypeSortConfig]);

  const sortedWardPropertyStats = React.useMemo(() => {
    let sortableItems = [...wardPropertyStats];
    if (wardSortConfig.key !== null) {
      sortableItems.sort((a, b) => {
        const aValue =
          wardSortConfig.key === "ward_name"
            ? a[wardSortConfig.key]
            : parseFloat(a[wardSortConfig.key]);
        const bValue =
          wardSortConfig.key === "ward_name"
            ? b[wardSortConfig.key]
            : parseFloat(b[wardSortConfig.key]);
        if (aValue < bValue)
          return wardSortConfig.direction === "ascending" ? -1 : 1;
        if (aValue > bValue)
          return wardSortConfig.direction === "ascending" ? 1 : -1;
        return 0;
      });
    }
    return sortableItems;
  }, [wardPropertyStats, wardSortConfig]);

  const generateColors = (count) => {
    const colors = [];
    for (let i = 0; i < count; i++) {
      const hue = (i * 360) / count;
      colors.push(`hsl(${hue}, 70%, 60%)`);
    }
    return colors;
  };

  const dynamicColors = useMemo(
    () => generateColors(data.length),
    [data.length]
  );

  return (
    <>
      <div className="flex h-fit">
        <div className="w-full mx-auto px-4 sm:px-6 lg:px-5 py-6">
          <div className="mb-6 lg:flex sm:justify-between items-center">
            <div>
              <p className="text-BlueText sm:text-3xl text-2xl font-bold lg:mb-0 mb-3 capitalize">
                {t("welcomeText", { name: dynamicValues.name }) +
                  greetingMessage}
              </p>
            </div>
          </div>

          {/* Mobile/Tablet Layout - Single Column */}
          {isMobile ? (
            <div className="space-y-6">
              {/* Property Summary Card */}
              <Widget className="h-auto" title={t("propertySummary")}>
                {isLoading ? (
                  <div className="grid grid-cols-1 gap-4 mt-5">
                    <Skeleton height={100} />
                    <Skeleton height={100} />
                    <Skeleton height={100} />
                  </div>
                ) : (
                  <div className="grid grid-cols-1 gap-4 mt-5">
                    <div className="p-4 rounded-lg card-shadow min-h-28 flex flex-col justify-between py-4 border-b-4 border-BlueText">
                      <p className="mt-2 text-base font-bold leading-4 font-Poppins">
                        {t("totalProperty")}
                      </p>
                      <p className="text-xl font-bold text-BlueText">
                        <CountUp
                          end={totalPropertyStats?.totalPropertyCount}
                          duration={2.5}
                          separator=","
                          decimals={0}
                        />
                      </p>
                    </div>
                    <div className="p-4 rounded-lg card-shadow min-h-28 flex flex-col justify-between py-4 border-b-4 border-green-600 relative">
                      <p className="mt-2 text-base font-bold leading-4 font-Poppins">
                        {t("taxCollected")}
                      </p>
                      <p className="text-xl font-bold text-green-600">
                        ₹{" "}
                        <CountUp
                          end={totalPropertyStats?.totalPaidTax || 0}
                          duration={2.5}
                          separator=","
                          decimals={0}
                        />
                      </p>
                      <span className="text-sm font-semibold absolute bottom-0 right-2 text-green-600">
                        {collectedPercentage.toFixed(2)}% एकूण
                      </span>
                    </div>
                    <div className="p-4 rounded-lg card-shadow min-h-28 flex flex-col justify-between py-4 border-b-4 border-red-500">
                      <p className="mt-2 text-base font-bold leading-4 font-Poppins">
                        {t("taxisdue")}
                      </p>
                      <p className="text-xl font-bold text-red-500">
                        ₹{" "}
                        <CountUp
                          end={totalPropertyStats?.remainingTax || 0}
                          duration={2.5}
                          separator=","
                          decimals={0}
                        />
                      </p>
                    </div>
                  </div>
                )}
                <div className="flex space-x-2 justify-center my-2 mt-5">
                  <div className="flex justify-center items-center">
                    <div className="w-4 h-4 rounded-full bg-BlueText mr-2"></div>
                    <span>
                      {financialYears?.loading ? (
                        <>...</>
                      ) : (
                        financialYears?.currentYear
                      )}
                    </span>
                  </div>
                </div>
              </Widget>

              {/* Property Type Chart */}
              <Widget title={t("Typeofproperty")}>
                {isLoading ? (
                  <div className="flex justify-center">
                    <Skeleton circle={true} height={250} width={250} />
                  </div>
                ) : data.length > 0 ? (
                  <ResponsiveContainer
                    className="pie-chart"
                    width="100%"
                    height={350}
                  >
                    <PieChart>
                      <Pie
                        data={data}
                        dataKey="value"
                        nameKey="name"
                        innerRadius={50}
                        outerRadius={80}
                        labelLine={false}
                      >
                        {data.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={dynamicColors[index % dynamicColors.length]}
                          />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value, name) => [`${value}`, `${name}`]}
                      />
                      <Legend
                        layout="horizontal"
                        align="center"
                        verticalAlign="bottom"
                        wrapperStyle={{ maxWidth: "100%" }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-muted-foreground">No data available</p>
                  </div>
                )}
              </Widget>

              {/* Usage Type Stats */}
              <Widget title={t("dashboard.usagewiseProperty")}>
                {isLoadingUsageTypeStats ? (
                  <div className="space-y-4 p-4">
                    <Skeleton height={40} count={6} />
                  </div>
                ) : (
                  <div className="overflow-y-auto h-full">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead
                            className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            onClick={() =>
                              requestSort("usageType", "usage_type")
                            }
                          >
                            {t(
                              "propertyAssessmentDetailsForm.propertyUsageType"
                            )}
                            <ArrowUpDown className="inline ml-1 h-4 w-4" />
                          </TableHead>
                          <TableHead
                            className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                            onClick={() => requestSort("usageType", "total")}
                          >
                            {t("एकूण")}
                            <ArrowUpDown className="inline ml-1 h-4 w-4" />
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {sortedUsageTypeStats.map((stat, index) => (
                          <TableRow
                            key={index}
                            className={
                              index % 2 === 0 ? "bg-white" : "bg-gray-50"
                            }
                          >
                            <TableCell className="px-2 py-3 text-sm font-medium text-gray-900">
                              {stat.usage_type}
                            </TableCell>
                            <TableCell className="px-2 py-3 text-sm text-gray-500">
                              {stat.total}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </Widget>

              {/* Ward Performance */}
              <Widget title="वार्ड-निहाय संकलन कामगिरी">
                {isLoadingWardPerformance ? (
                  <div className="space-y-4 p-4">
                    <Skeleton height={80} count={4} />
                  </div>
                ) : (
                  <div className="space-y-4 overflow-y-auto h-full">
                    <div className="space-y-4">
                      {wardPerformance.map((ward, index) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium text-sm">
                              Ward {ward.ward_name}
                            </span>
                            <Badge
                              variant={
                                ward.rate >= 90
                                  ? "default"
                                  : ward.rate >= 85
                                    ? "secondary"
                                    : "destructive"
                              }
                            >
                              {ward.rate.toFixed(2)}% संकलित
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <div className="text-muted-foreground">
                                मालमत्ता
                              </div>
                              <div className="font-medium">{ward.count}</div>
                            </div>
                            <div>
                              <div className="text-muted-foreground">
                                संकलित
                              </div>
                              <div className="font-medium text-green-600">
                                ₹{(ward.collected / 100000).toFixed(1)}L
                              </div>
                            </div>
                            <div>
                              <div className="text-muted-foreground">
                                प्रलंबित
                              </div>
                              <div className="font-medium text-red-600">
                                ₹{(ward.pending / 100000).toFixed(1)}L
                              </div>
                            </div>
                            <div>
                              <div className="text-muted-foreground">एकूण</div>
                              <div className="font-medium">
                                ₹
                                {(
                                  (ward.collected + ward.pending) /
                                  100000
                                ).toFixed(1)}
                                L
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </Widget>

              {/* Recent Transactions */}
              <RecentTransactions
                transactions={recentTransactions}
                isLoading={isLoading}
              />
            </div>
          ) : (
            /* Desktop Layout - Complex Grid (Keep Original) */
            <div className="grid grid-cols-12 grid-rows-16 gap-8 p-4">
              <div className="col-span-8 max-[1700px]:col-span-7 row-span-4">
                <Widget className="h-full" title={t("propertySummary")}>
                  {isLoading ? (
                    <div className="grid grid-cols-1 sm:grid-cols-3 sm:mt-10 mt-5 gap-4">
                      <Skeleton height={100} />
                      <Skeleton height={100} />
                      <Skeleton height={100} />
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-3 sm:mt-10 mt-5 gap-4 ">
                      <div className="p-4 rounded-lg card-shadow min-h-28 flex flex-col justify-between py-4 border-b-4 border-BlueText">
                        <p className="mt-2 text-base font-bold leading-4 font-Poppins">
                          {t("totalProperty")}
                        </p>
                        <p className="sm:text-2xl text-xl font-bold text-BlueText">
                          <CountUp
                            end={totalPropertyStats?.totalPropertyCount}
                            duration={2.5}
                            separator=","
                            decimals={0}
                          />
                        </p>
                      </div>
                      <div className="p-4 rounded-lg card-shadow min-h-28 flex flex-col justify-between py-4 border-b-4 border-green-600 relative">
                        <p className="mt-2 text-base font-bold leading-4 font-Poppins">
                          {t("taxCollected")}
                        </p>
                        <p className="sm:text-2xl text-xl font-bold text-green-600">
                          ₹{" "}
                          <CountUp
                            end={totalPropertyStats?.totalPaidTax || 0}
                            duration={2.5}
                            separator=","
                            decimals={0}
                          />
                        </p>
                        <span className="text-sm font-semibold absolute bottom-0 right-2 text-green-600">
                          {" "}
                          {collectedPercentage.toFixed(2)}% एकूण
                        </span>
                      </div>
                      <div className="p-4 rounded-lg card-shadow min-h-28 flex flex-col justify-between py-4 border-b-4 border-red-500">
                        <p className="mt-2 text-base font-bold leading-4 font-Poppins">
                          {t("taxisdue")}
                        </p>
                        <p className="sm:text-2xl text-xl font-bold text-red-500">
                          ₹{" "}
                          <CountUp
                            end={totalPropertyStats?.remainingTax || 0}
                            duration={2.5}
                            separator=","
                            decimals={0}
                          />
                        </p>
                      </div>
                    </div>
                  )}
                  <div className="flex space-x-2 justify-center my-2 mt-5">
                    <div className="flex justify-center items-center">
                      <div className="w-4 h-4 rounded-full bg-BlueText mr-2"></div>
                      <span>
                        {financialYears?.loading ? (
                          <>...</>
                        ) : (
                          financialYears?.currentYear
                        )}
                      </span>
                    </div>
                  </div>
                </Widget>
              </div>
              <div className="col-span-4 max-[1700px]:col-span-5 row-span-4 col-start-9">
                <Widget title={t("Typeofproperty")}>
                  {isLoading ? (
                    <Skeleton circle={true} height={300} width={300} />
                  ) : data.length > 0 ? (
                    <ResponsiveContainer
                      className="pie-chart"
                      width="100%"
                      height={isTablet ? 450 : 280}
                    >
                      <PieChart>
                        <Pie
                          data={data}
                          dataKey="value"
                          nameKey="name"
                          innerRadius={50}
                          outerRadius={isTablet ? 80 : 110}
                          labelLine={false}
                        >
                          {data.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={dynamicColors[index % dynamicColors.length]}
                            />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value, name) => [`${value}`, `${name}`]}
                        />
                        <Legend
                          layout={isTablet ? "horizontal" : "vertical"}
                          align={isTablet ? "center" : "right"}
                          verticalAlign={isTablet ? "bottom" : "middle"}
                          wrapperStyle={
                            isTablet
                              ? { maxWidth: "100%" }
                              : { maxWidth: "100%" }
                          }
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground">No data available</p>
                    </div>
                  )}
                </Widget>
              </div>

              <div className="col-span-8 row-span-10 row-start-5">
                <Widget title="वार्ड-निहाय संकलन कामगिरी">
                  {isLoadingWardPerformance ? (
                    <div className="space-y-4 p-4">
                      <Skeleton height={80} count={5} />
                    </div>
                  ) : (
                    <div className="space-y-4 overflow-y-auto h-full">
                      <div className="space-y-4">
                        {wardPerformance.map((ward, index) => (
                          <div
                            key={index}
                            className="p-4 bg-gray-50 rounded-lg"
                          >
                            <div className="flex justify-between items-center mb-2">
                              <span className="font-medium">
                                Ward {ward.ward_name}
                              </span>
                              <Badge
                                variant={
                                  ward.rate >= 90
                                    ? "default"
                                    : ward.rate >= 85
                                      ? "secondary"
                                      : "destructive"
                                }
                              >
                                {ward.rate.toFixed(2)}% संकलित
                              </Badge>
                            </div>
                            <div className="grid grid-cols-4 gap-2 text-sm">
                              <div>
                                <div className="text-muted-foreground">
                                  मालमत्ता
                                </div>
                                <div className="font-medium">{ward.count}</div>
                              </div>
                              <div>
                                <div className="text-muted-foreground">
                                  संकलित
                                </div>
                                <div className="font-medium text-green-600">
                                  ₹{(ward.collected / 100000).toFixed(1)}L
                                </div>
                              </div>
                              <div>
                                <div className="text-muted-foreground">
                                  प्रलंबित
                                </div>
                                <div className="font-medium text-red-600">
                                  ₹{(ward.pending / 100000).toFixed(1)}L
                                </div>
                              </div>

                              <div>
                                <div className="text-muted-foreground">
                                  एकूण
                                </div>
                                <div className="font-medium">
                                  ₹
                                  {(
                                    (ward.collected + ward.pending) /
                                    100000
                                  ).toFixed(1)}
                                  L
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </Widget>
              </div>
              <div className="col-span-4 row-span-7 row-start-5 col-start-9">
                <Widget title={t("dashboard.usagewiseProperty")}>
                  {isLoadingUsageTypeStats ? (
                    <div className="space-y-4 p-4">
                      <Skeleton height={40} count={8} />
                    </div>
                  ) : (
                    <div className="overflow-y-auto h-full">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead
                              className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer whitespace-nowrap"
                              onClick={() =>
                                requestSort("usageType", "usage_type")
                              }
                            >
                              {t(
                                "propertyAssessmentDetailsForm.propertyUsageType"
                              )}
                              <ArrowUpDown className="inline ml-1 h-4 w-4" />
                            </TableHead>
                            <TableHead
                              className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer whitespace-nowrap"
                              onClick={() => requestSort("usageType", "total")}
                            >
                              {t("एकूण")}
                              <ArrowUpDown className="inline ml-1 h-4 w-4" />
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {sortedUsageTypeStats.map((stat, index) => (
                            <TableRow
                              key={index}
                              className={
                                index % 2 === 0 ? "bg-white" : "bg-gray-50"
                              }
                            >
                              <TableCell className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                {stat.usage_type}
                              </TableCell>
                              <TableCell className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                                {stat.total}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </Widget>
              </div>

              <div className="col-span-4 row-span-3 col-start-9 row-start-14">
                <RecentTransactions
                  transactions={recentTransactions}
                  isLoading={isLoading}
                />
              </div>
            </div>
          )}
          <PopUpScreen
            title={""}
            isOpen={viewMore}
            toggle={() => handleViewMore()}
          >
            {commonDialogue}
          </PopUpScreen>
        </div>
      </div>
    </>
  );
};

export default Dashboard;
