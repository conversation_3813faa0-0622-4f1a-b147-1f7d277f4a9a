import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Wrong_property_typeMaster } from '../entities';

export class WrongPropertyTypeMasterRepository extends Repository<Wrong_property_typeMaster> {
  constructor(
    @InjectRepository(Wrong_property_typeMaster)
    private readonly wrongPropertyTypeMasterRepository: Repository<Wrong_property_typeMaster>,
  ) {
    super(
      wrongPropertyTypeMasterRepository.target,
      wrongPropertyTypeMasterRepository.manager,
      wrongPropertyTypeMasterRepository.queryRunner,
    );
  }

  async saveWrongPropertyType(input: any) {
    let wrongPropertyType = this.wrongPropertyTypeMasterRepository.create(input);
    wrongPropertyType = await this.wrongPropertyTypeMasterRepository.save(wrongPropertyType);
    return wrongPropertyType;
  }

  async findAllWrongPropertyTypes() {
    return await this.wrongPropertyTypeMasterRepository
      .createQueryBuilder('wrong_property_type_master')
      .select([
        'wrong_property_type_master.id', // Replace with your actual column name
        'wrong_property_type_master.propertyType', // Replace with your actual column name
      ])
      .orderBy('wrong_property_type_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string) {
    return await this.wrongPropertyTypeMasterRepository
      .createQueryBuilder('wrong_property_type_master')
      .select([
        'wrong_property_type_master.id', // Replace with your actual column name
        'wrong_property_type_master.propertyType', // Replace with your actual column name
      ])
      .where('wrong_property_type_master.id = :id', { id })
      .getOne();
  }

  async updateWrongPropertyType(id: string, input: any) {
    return await this.wrongPropertyTypeMasterRepository
      .createQueryBuilder('wrong_property_type_master')
      .update(Wrong_property_typeMaster)
      .set(input)
      .where('id = :id', { id })
      .execute();
  }

  async deleteWrongPropertyType(id: string) {
    return await this.wrongPropertyTypeMasterRepository
      .createQueryBuilder('wrong_property_type_master')
      .softDelete()
      .where('id = :id', { id })
      .execute();
  }

  async getData() {
    return await this.wrongPropertyTypeMasterRepository
      .createQueryBuilder('wrong_property_type_master')
      .select(['wrong_property_type_master.id', 'wrong_property_type_master.propertyType']) // Add more fields as necessary
      .getMany();
  }
}
