import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportTable21723200309685 implements MigrationInterface {
    name = 'ImportTable21723200309685'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ADD "ward_name" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "import_date" TIMESTAMP`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "import_date"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "ward_name"`);
    }

}
