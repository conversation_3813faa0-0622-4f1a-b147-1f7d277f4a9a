import { PropertysubtypeMasterObject } from "@/model/propertysubtype-master";
import Api from "../../services/ApiServices";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

const fetchPropertysubtype = async () => {
  const response = await Api.getAllPropertySubType();
  return response.data || [];
};

const createPropertysubtype = async (
  propertysubtypeData: PropertysubtypeMasterObject,
) => {
  return new Promise((resolve, reject) => {
    Api.createPropertySubType(propertysubtypeData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updatePropertysubtype = async ({
  propertysubtypeId,
  propertysubtypeData,
}) => {
  return new Promise((resolve, reject) => {
    Api.updatePropertySubType(
      propertysubtypeId,
      propertysubtypeData,
      (response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      },
    );
  });
};

const deletePropertysubtype = async (propertysubtypeId: string) => {
  return new Promise((resolve, reject) => {
    Api.deletePropertySubType(propertysubtypeId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const usePropertysubtypeMasterController = () => {
  const queryClient = useQueryClient();

  const { data: propertysubtypeListResponse, error, isLoading:propertySubTypeLoading } = useQuery({
    queryKey: ["propertysubtypemaster"],
    queryFn: fetchPropertysubtype,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  if (error) {
    console.error("Error fetching streets:", error);
  }

  const createPropertysubtypeMutation = useMutation({
    mutationFn: createPropertysubtype,
    onMutate: async (newStreet) => {
      await queryClient.cancelQueries({ queryKey: ["propertysubtypemaster"] });
      const previousStreet = queryClient.getQueryData([
        "propertysubtypemaster",
      ]);
      queryClient.setQueryData(
        ["propertysubtypemaster"],
        (old: PropertysubtypeMasterObject) => {
          const updatedData = [newStreet, ...old];
          // Log the updated data
          return updatedData;
        },
      );
      return { previousStreet };
    },
    onError: (err, newWard, context) => {
      queryClient.setQueryData(
        ["propertysubtypemaster"],
        context.previousStreet,
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["propertysubtypemaster"] });
    },
  });

  const updatePropertysubtypeMutation = useMutation({
    mutationFn: updatePropertysubtype,
    onMutate: async ({ propertysubtypeId, propertysubtypeData }) => {
      await queryClient.cancelQueries({ queryKey: ["propertysubtypemaster"] });

      const previouspropertysubtype = queryClient.getQueryData([
        "propertysubtypemaster",
      ]);
      queryClient.setQueryData(["propertysubtypemaster"], (old) => {
        const updatedpropertytype = old?.map((propertysubtype: any) =>
          propertysubtype?.propertySub_id === propertysubtypeId
            ? { ...propertysubtype, ...propertysubtypeData }
            : propertysubtype,
        );

        return updatedpropertytype;
      });

      return { previouspropertysubtype };
    },
    onError: (err, context) => {
      queryClient.setQueryData(
        ["propertysubtypemaster"],
        context.previouspropertysubtype,
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["propertysubtypemaster"] });
    },
  });

  const deletePropertysubtypeMutation = useMutation({
    mutationFn: deletePropertysubtype,
    onMutate: async (propertysubtypeId) => {
      await queryClient.cancelQueries({ queryKey: ["propertysubtypemaster"] });

      const previouspropertysubtype = queryClient.getQueryData([
        "propertysubtypemaster",
      ]);

      queryClient.setQueryData(["propertysubtypemaster"], (old) => {
        const updatedPropertytype = old.filter(
          (propertytype: PropertysubtypeMasterObject) =>
            propertytype.propertyType !== propertysubtypeId,
        );
        return updatedPropertytype;
      });
      return { previouspropertysubtype };
    },
    onError: (err, wardId, context) => {
      queryClient.setQueryData(
        ["propertysubtypemaster"],
        context.previouspropertysubtype,
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["propertysubtypemaster"] });
    },
  });
  return {
    propertysubtypeList: propertysubtypeListResponse || [],
    propertySubTypeLoading,
    createPropertysubtype: createPropertysubtypeMutation.mutate,
    updatePropertysubtype: updatePropertysubtypeMutation.mutate,
    deletePropertysubtype: deletePropertysubtypeMutation.mutate,
  };
};
