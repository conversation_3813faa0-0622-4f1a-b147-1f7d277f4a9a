import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ConstructionClassService } from './construction_class.service';
import {
  ConstructionClassDto,
  CreateConstructionClassDto,
  UpdateConstructionClassDto,
} from './dto/constructionClass.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@ApiTags('Construction Class')
@Controller('construction-class')
export class ConstructionClassController {
  constructor(
    private readonly constructionClassService: ConstructionClassService,
  ) {}

  @Form('Construction Class Master')
  @Permissions('can_write')
  @ApiOperation({ summary: 'Create a new Construction Class' })
  @ApiResponse({
    status: 201,
    description: 'The Construction Class has been successfully created',
  })
  @Post()
  create(@Body() createConstructionClassDto: CreateConstructionClassDto) {
    return this.constructionClassService.create(createConstructionClassDto);
  }

  @Form('Construction Class Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all Construction Class' })
  @ApiResponse({ status: 200, description: 'Returns all Construction Class' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.constructionClassService.findAll();
  }

  @Form('Construction Class Master')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one Construction Class' })
  @ApiResponse({
    status: 200,
    description: 'Returns Single Construction Class',
  })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() constructionClassDto: ConstructionClassDto) {
    return this.constructionClassService.findOne(constructionClassDto);
  }

  @Form('Construction Class Master')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Update a Construction Class by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Construction Class has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Construction Class not found' })
  @Patch()
  update(
    @Query() constructionClassDto: ConstructionClassDto,
    @Body() updateConstructionClassDto: UpdateConstructionClassDto,
  ) {
    return this.constructionClassService.update(
      constructionClassDto,
      updateConstructionClassDto,
    );
  }

  @Form('Construction Class Master')
  @Permissions('can_delete')
  @ApiOperation({ summary: 'Delete a Construction Class by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Construction Class has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Construction Class not found' })
  @Delete()
  remove(@Query() constructionClassDto: ConstructionClassDto) {
    return this.constructionClassService.remove(constructionClassDto);
  }
}
