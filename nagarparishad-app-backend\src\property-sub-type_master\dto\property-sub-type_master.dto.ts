import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class CreatePropertySubTypeMasterDto {
  @ApiProperty({ name: 'propertySub_name', type: 'string' })
  @IsNotEmpty()
  @IsString()
  propertySub_name: string;

  @ApiProperty({ name: 'propertyType', type: 'string' })
  @IsNotEmpty()
  @IsUUID()
  propertyType: string;
}

export class UpdatePropertySubTypeMasterDto extends PartialType(
  CreatePropertySubTypeMasterDto,
) {}

export class PropertySubTypeMasterDto {
  @ApiProperty({ name: 'propertySub_id', type: 'string' })
  @IsNotEmpty()
  @IsUUID()
  propertySub_id: string;
}

export class PropertypeIdMasterDto {
  @ApiProperty({ name: 'propertyType', type: 'string' })
  @IsNotEmpty()
  @IsUUID()
  propertyType: string;
}
