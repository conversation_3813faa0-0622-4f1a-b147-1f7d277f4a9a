import { MigrationInterface, QueryRunner } from "typeorm";

export class WarshikKarColAddes1728386656262 implements MigrationInterface {
    name = 'WarshikKarColAddes1728386656262'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "warshik_kar" ADD "billNo" character varying`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" ADD "bill_generation_date" date`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "warshik_kar" DROP COLUMN "bill_generation_date"`);
        await queryRunner.query(`ALTER TABLE "warshik_kar" DROP COLUMN "billNo"`);
    }

}
