import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import {
  CollectorMasterRepository,
  LogsRepository,
  UserRepository,
} from 'libs/database/repositories';
import { api, log_sub_type, log_type } from 'src/utils/constants';
import { UserField } from '@helper/helpers';
import { GetSingleUserDto, GetUsersDto } from './dto/get-users.dto';
import { CollectorMaster } from 'libs/database/entities';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly collectorRepo: CollectorMasterRepository,
    private readonly logsRepository: LogsRepository,
  ) {}
  async create(createUserDto: CreateUserDto) {
    const currentFile = 'user.service.ts';
    const currentApi = '/api/v1/user';
    try {
      const emailExists = await this.userRepository.checkUserExists(
        UserField.EMAIL,
        createUserDto.email,
      );

      if (emailExists) {
        throw new HttpException('Email already exists', HttpStatus.CONFLICT);
      }
      const checkPhoneNumber = await this.userRepository.checkUserExists(
        UserField.MOBILE_NUMBER,
        createUserDto.mobileNumber,
      );

      if (checkPhoneNumber) {
        throw new HttpException(
          'Mobile number already exists',
          HttpStatus.CONFLICT,
        );
      }

      const saveUser = await this.userRepository.saveData(createUserDto);

      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.SAVE,
        file: currentFile,
        api: currentApi,
        message: 'User created successfully',
        data: { userId: saveUser.user_id, email: saveUser.email },
        user_id: saveUser.user_id,
      });

      return {
        message: 'User Saved Successfully',
        data: saveUser,
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error during user creation',
        data: { error: error.message, stack: error.stack },
        user_id: null,
      });
      throw error;
    }
  }

  async findAll(getUsers: GetUsersDto) {
    try {
      const getAllUsers = await this.userRepository.getUsers(getUsers);
      if (!getAllUsers || getAllUsers.totalPages === 0) {
        throw new NotFoundException('User Not Found');
      }
      return {
        message: "User's Fetched Successfully",
        data: getAllUsers,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(user: GetSingleUserDto) {
    try {
      const getUser = await this.userRepository.findById(user.user_id);

      if (!getUser) {
        throw new NotFoundException('User Not Found');
      }

      return {
        message: 'User Fetched Successfully',
        data: getUser,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(user: GetSingleUserDto, updateUserDto: UpdateUserDto) {
    const currentFile = 'user.service.ts';
    const currentApi = '/api/v1/user';
    try {
      const getUser = await this.userRepository.findById(user.user_id);
      if (!getUser) {
        throw new NotFoundException('User Not Found');
      }
      let roleId = updateUserDto.role_id || null;

      if (user.collector_id) {
        const collector = await this.collectorRepo.getById(user.collector_id);
        if (!collector) {
          throw new NotFoundException('Collector Not Found');
        }

        collector.user = { user_id: user.user_id } as any;

        if (user.ward_id)
          collector.ward = { ward_id: user.ward_id } as any;
        await this.collectorRepo.save(collector);

        roleId = collector.role?.role_id || null;
      }

      const updateUser = await this.userRepository.updateData(getUser.user_id, {
        ...updateUserDto,
        role_id: roleId,
      });

      if (updateUser.affected === 0) {
        throw new BadRequestException('Failed To Update User');
      }

      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.UPDATE,
        file: currentFile,
        api: currentApi,
        message: 'User updated successfully',
        data: { userId: user.user_id, changes: updateUserDto },
        user_id: user.user_id,
      });

      return {
        message: 'User Updated Successfully',
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error during user update',
        data: { error: error.message, stack: error.stack },
        user_id: user.user_id,
      });
      throw error;
    }
  }

  async remove(user: GetSingleUserDto) {
    const currentFile = 'user.service.ts';
    const currentApi = '/api/v1/user';
    try {
      const getUser = await this.userRepository.findById(user.user_id);

      if (!getUser) {
        throw new NotFoundException('User Not Found');
      }

      const deleteUser = await this.userRepository.deleteData(getUser.user_id);

      if (deleteUser.affected === 0) {
        throw new BadRequestException('Failed To Delete User');
      }

      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.DELETE,
        file: currentFile,
        api: currentApi,
        message: 'User deleted successfully',
        data: { userId: user.user_id },
        user_id: user.user_id, // Or the ID of the admin performing the action
      });

      return {
        message: 'User Deleted Successfully',
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error during user deletion',
        data: { error: error.message, stack: error.stack },
        user_id: user.user_id,
      });
      throw error;
    }
  }
}
