import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PenaltyFeeYearWiseController } from './penalty_fee.controller';
import { PenaltyFeeYearWiseService } from './penalty_fee.services';
import { 
  PenaltyFeeYearWiseEntity, 
  Financial_year, 
  PaidDataEntity, 
  WarshilKarEntity 
} from 'libs/database/entities';
import { 
  PenaltyFeeYearWiseRepository, 
  Financial_yearRepository, 
  PaidDataRepository, 
  WarshikKarRepository 
} from 'libs/database/repositories';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PenaltyFeeYearWiseEntity,
      Financial_year,
      PaidDataEntity,
      WarshilKarEntity
    ]),
  ],
  controllers: [PenaltyFeeYearWiseController],
  providers: [
    PenaltyFeeYearWiseService,
    PenaltyFeeYearWiseRepository,
    Financial_yearRepository,
    PaidDataRepository,
    WarshikKarRepository
  ],
  exports: [PenaltyFeeYearWiseService],
})
export class PenaltyFeeYearWiseModule {}
