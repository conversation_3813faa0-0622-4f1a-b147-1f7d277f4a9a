import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Master_depreciation_rate } from 'libs/database/entities';
import { CreateDepreciationRateDto } from './dto/create-depreciation-rate.dto';
import { UpdateDepreciationRateDto } from './dto/update-depreciation-rate.dto';
import { Master_depreciationRepository, PropertyTypeClassMasterRepository } from 'libs/database/repositories';

@Injectable()
export class MasterDepreciationService {
  constructor(
    @InjectRepository(Master_depreciationRepository) // This should inject the entity, not the repository
    private readonly depreciationRepository: Master_depreciationRepository,
    private readonly propertyTypeClassMasterRepository: PropertyTypeClassMasterRepository,
  ) {}

  async create(
    data: CreateDepreciationRateDto,
  ): Promise<{ message: string; data: Master_depreciation_rate }> {
    const propertyTypeClass =
      await this.propertyTypeClassMasterRepository.findById(data.property_type_class_id);

    if (!propertyTypeClass) {
      throw new NotFoundException('Property type class not found');
    }

    // Handle reassessment range if provided
    let reassessmentRange = null;
    if (data.reassessment_range_id) {
      const { ReassessmentRange } = await import('libs/database/entities');
      reassessmentRange = await this.depreciationRepository.manager
        .getRepository(ReassessmentRange)
        .findOne({
          where: { reassessment_range_id: data.reassessment_range_id }
        });
      if (!reassessmentRange) {
        throw new NotFoundException('Reassessment range not found');
      }
    }

    const newRecord = this.depreciationRepository.create({
      ...data,
      property_type_class_id: propertyTypeClass, // Set the actual entity here
      reassessmentRange: reassessmentRange, // Set the reassessment range entity
    });

    const savedRecord = await this.depreciationRepository.save(newRecord);

    return {
      message: 'Depreciation rate created successfully',
      data: savedRecord,
    };
  }

  async findAll(): Promise<{
    message: string;
    data: Master_depreciation_rate[];
  }> {
    const allRecords = await this.depreciationRepository.findWithPropertyClass();
    return {
      message: 'Depreciation rates fetched successfully',
      data: allRecords,
    };
  }

  async findOne(
    id: string,
  ): Promise<{ message: string; data: Master_depreciation_rate }> {
    const record = await this.depreciationRepository.findById(id);
    if (!record) {
      return {
        message: 'Depreciation rate not found',
        data: undefined,
      };
    }
    return {
      message: 'Depreciation rate fetched successfully',
      data: record.data, // Return the record directly
    };
  }

  async update(
    id: string,
    data: UpdateDepreciationRateDto,
  ): Promise<{ message: string; data: Master_depreciation_rate | undefined }> {
    const updatedRecord =await this.depreciationRepository.updateRate(id, data);
    return {
      message: 'Depreciation rate updated successfully',
      data: updatedRecord.data, // Return the updated record directly
    };
  }

  async delete(id: string): Promise<{ message: string }> {
    await this.depreciationRepository.softDelete(id);
    return {
      message: 'Depreciation rate deleted successfully',
    };
  }
}
