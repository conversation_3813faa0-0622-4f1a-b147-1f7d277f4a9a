import React, { useState } from 'react';
import { Bell, Download, X, CheckCircle, AlertCircle, Clock, Loader2 } from 'lucide-react';
import { useNotifications } from '@/context/NotificationContext';
import { Notification } from '@/types/notification';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';

interface NotificationBellProps {
  handleSidebarOpen: () => void;
}

const NotificationBell: React.FC<NotificationBellProps> = ({ handleSidebarOpen }) => {

  const { notifications, unreadCount, markAsRead, removeNotification, downloadNotificationFile } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);

  const getNotificationIcon = (notification: Notification) => {
    switch (notification.status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'processing':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getNotificationColor = (notification: Notification) => {
    switch (notification.status) {
      case 'completed':
        return 'border-l-green-500 bg-green-50';
      case 'failed':
        return 'border-l-red-500 bg-red-50';
      case 'processing':
        return 'border-l-blue-500 bg-blue-50';
      default:
        return 'border-l-gray-500 bg-gray-50';
    }
  };

  const handleDownload = async (notification: Notification) => {
    if (notification.downloadUrl) {
      try {
        const result = await downloadNotificationFile(notification.id);
        if (result.success) {
          // Mark as read after successful download
          markAsRead(notification.id);
        } else {
          console.error('Download failed:', result.message);
          // You could show a toast notification here
        }
      } catch (error) {
        console.error('Download error:', error);
      }
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    if (notification.status === 'completed' && notification.downloadUrl) {
      handleDownload(notification);
    } else {
      markAsRead(notification.id);
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative" onClick={handleSidebarOpen}>
          <Bell className="w-5 h-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      {/* <DropdownMenuContent align="end" className="w-80 p-0">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Notifications</h3>
            {unreadCount > 0 && (
              <Badge variant="secondary">{unreadCount} new</Badge>
            )}
          </div>
        </div>

        <ScrollArea className="max-h-96 overflow-y-scroll">
          {notifications.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No notifications</p>
            </div>
          ) : (
            <div className="p-2">
              {notifications.slice(0, 10).map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 mb-2 rounded-lg border-l-4 cursor-pointer hover:bg-gray-50 transition-colors ${getNotificationColor(notification)} ${
                    notification.persistent ? 'ring-2 ring-blue-200' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-2 flex-1">
                      {getNotificationIcon(notification)}
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm truncate">
                          {notification.title}
                        </p>
                        <p className="text-xs text-gray-600 mt-1">
                          {notification.message}
                        </p>
                        
                        {notification.status === 'processing' && notification.progress !== undefined && (
                          <div className="mt-2">
                            <Progress value={notification.progress} className="h-2" />
                            <p className="text-xs text-gray-500 mt-1">
                              {notification.progress}% complete
                              {notification.metadata?.processedRecords && notification.metadata?.totalRecords && (
                                <span className="ml-2">
                                  ({notification.metadata.processedRecords}/{notification.metadata.totalRecords} records)
                                </span>
                              )}
                            </p>
                          </div>
                        )}
                        
                        {notification.status === 'completed' && notification.downloadUrl && (
                          <div className="mt-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-6 text-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownload(notification);
                              }}
                            >
                              <Download className="w-3 h-3 mr-1" />
                              Download
                            </Button>
                          </div>
                        )}
                        
                        <p className="text-xs text-gray-400 mt-1">
                          {formatTimeAgo(notification.createdAt)}
                        </p>
                      </div>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 ml-2"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeNotification(notification.id);
                      }}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
        
        {notifications.length > 10 && (
          <div className="p-2 border-t">
            <Button variant="ghost" size="sm" className="w-full">
              View all notifications
            </Button>
          </div>
        )}
      </DropdownMenuContent> */}
    </DropdownMenu>
  );
};

export default NotificationBell;
