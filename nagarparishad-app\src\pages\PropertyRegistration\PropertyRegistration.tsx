import React, { useState } from "react";
import WhiteContainer from "@/components/custom/WhiteContainer";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import PropertyLocationDetails from "@/components/forms/PropertyRegistrationForm/PropertyLocationDetails";
import AssetDetailForm from "@/components/forms/PropertyRegistrationForm/AssetDetailForm";
import PropertyAssessmentDetailsForm from "@/components/forms/PropertyRegistrationForm/PropertyAssessmentDetailsForm";
import PlotDetails from "@/components/forms/PropertyRegistrationForm/PlotDetails";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import BreadCrumb from "@/components/custom/BreadCrumb";

const PropertyRegistration = () => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState<number>(1);
  const handleNextStep = () => {
    setCurrentStep((prevStep) => prevStep + 1);
  };

  const handlePreviousStep = () => {
    setCurrentStep((prevStep) => prevStep - 1);
  };

  const getBorderColor = (step: any) => {
    return step <= currentStep ? "border-BlueText" : "border-gray-300";
  };

  return (
    <div className="flex h-fit   ">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex justify-between items-baseline">
          <h1 className="text-2xl font-semibold font-Poppins w-fit ml-3 ">
            {t("propertyregisteartion")}
          </h1>
          <BreadCrumb className={"bg-Secondary"} />
        </div>
        <WhiteContainer>
          <div className="w-full flex flex-col items-center justify-center">
            <Wizard className="w-full mx-4 my-8 sm:px-8 px-3 md:px-10 space-y-6 text-center">
              <WizardHeader className="space-y-2 md:overflow-visible overflow-x-auto px-5">
                <div className="w-full sm:min-w-auto min-w-[560px] flex items-center justify-between mb-14">
                  <div className="flex flex-col items-center justify-start relative">
                    <Badge
                      className={cn(
                        "sm:w-11 sm:h-11 w-9 h-9 text-xl text-white rounded-full flex items-center justify-center shadow-md",
                        {
                          "bg-BlueText": currentStep >= 1,
                          "bg-gray-200": currentStep < 1,
                        }
                      )}
                    >
                      1
                    </Badge>
                    <WizardTitle
                      className={cn(
                        "min-w-40 mx-2 absolute top-14 sm:text-base text-sm !leading-5 text-LightDark font-Noto",
                        currentStep == 1 ? "font-bold  text-black" : ""
                      )}
                    >
                      {t("propertyLocationFormWizardStepOne")}
                      <br />
                      {t("propertyLocationFormWizardStepOneTitle")}
                    </WizardTitle>
                  </div>
                  <hr
                    className={`w-full border-t-[3px] rounded-full mx-2 ${getBorderColor(1)}`}
                  />
                  <div className="flex flex-col items-center justify-start relative">
                    <Badge
                      className={cn(
                        "sm:w-11 sm:h-11 w-9 h-9 text-xl text-white rounded-full flex items-center justify-center shadow-md",
                        {
                          "bg-BlueText": currentStep >= 2,
                          "bg-gray-200": currentStep < 2,
                        }
                      )}
                    >
                      2
                    </Badge>
                    <WizardTitle
                      className={cn(
                        "min-w-40 mx-2 absolute top-14 sm:text-base text-sm !leading-5 text-LightDark font-Noto",
                        currentStep == 2 ? "font-bold  text-black" : ""
                      )}
                    >
                      {t("propertyLocationFormWizardStepTwo")} <br />
                      {t("propertyLocationFormWizardStepTwoTitle")}
                    </WizardTitle>
                  </div>
                  <hr
                    className={`w-full border-t-[3px] rounded-full mx-2 ${getBorderColor(2)}`}
                  />
                  <div className="flex flex-col items-center justify-start relative">
                    <Badge
                      className={cn(
                        "sm:w-11 sm:h-11 w-9 h-9 text-xl text-white rounded-full flex items-center justify-center shadow-md",
                        {
                          "bg-BlueText": currentStep >= 3,
                          "bg-gray-200": currentStep < 3,
                        }
                      )}
                    >
                      3
                    </Badge>
                    <WizardTitle
                      className={cn(
                        "min-w-40 mx-2 absolute top-14 sm:text-base text-sm !leading-5 text-LightDark font-Noto",
                        currentStep == 3 ? "font-bold  " : ""
                      )}
                    >
                      {t("propertyLocationFormWizardStepThree")} <br />
                      {t("propertyLocationFormWizardStepThreeTitle")}
                    </WizardTitle>
                  </div>

                  {/* <hr
                    className={`w-full border-t-[3px] rounded-full mx-2 ${getBorderColor(3)}`}
                  /> */}
                  {/* <div className="flex flex-col items-center justify-start relative">
                    <Badge
                      className={cn(
                        "sm:w-11 sm:h-11 w-9 h-9 text-xl text-white rounded-full flex items-center justify-center shadow-md",
                        {
                          "bg-BlueText": currentStep >= 4,
                          "bg-gray-200": currentStep < 4,
                        }
                      )}
                    >
                      4
                    </Badge>
                    <WizardTitle className="min-w-40 mx-2 absolute top-14 sm:text-base text-sm !leading-5 text-LightDark font-Noto">
                      {t("propertyLocationFormWizardStepFour")} <br />{" "}
                      <span>
                        {t("propertyLocationFormWizardStepFourTitle")}
                      </span>
                    </WizardTitle>
                  </div> */}
                </div>
              </WizardHeader>
              <WizardContent className="text-left !mt-12 h-fit ">
                {currentStep === 1 && (
                  <PropertyLocationDetails
                    currentStep={currentStep}
                    handleNextStep={handleNextStep}
                    handlePreviousStep={handlePreviousStep}
                  />
                )}
                {currentStep === 2 && (
                  <AssetDetailForm
                    currentStep={currentStep}
                    handleNextStep={handleNextStep}
                    handlePreviousStep={handlePreviousStep}
                  />
                )}
                {currentStep === 3 && (
                  <PropertyAssessmentDetailsForm
                    currentStep={currentStep}
                    handleNextStep={handleNextStep}
                    handlePreviousStep={handlePreviousStep}
                  />
                )}
                {/* {currentStep === 4 && (
                  <PlotDetails
                    currentStep={currentStep}
                    handleNextStep={handleNextStep}
                    handlePreviousStep={handlePreviousStep}
                  />
                )} */}
              </WizardContent>
              {/* {/* <WizardFooter>
                <div className="w-full text-end">
                  {currentStep > 1 && (
                    <Button
                      className="min-w-[120px] mr-4"
                      onClick={handlePreviousStep}
                    >
                      Previous
                    </Button>
                  )}
                  {currentStep < 4 ? (
                    <Button className="min-w-[120px]" onClick={handleNextStep}>
                      Next
                    </Button>
                  ) : (
                    <Button className="min-w-[120px]">Submit</Button>
                  )}
                </div> 
              </WizardFooter>{" "} */}
            </Wizard>
          </div>
        </WhiteContainer>
      </div>
    </div>
  );
};

export default PropertyRegistration;
