import { BaseEntity, Column, CreateDateColumn, DeleteDateColumn, Entity, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { PaymentInfoEntity, PropertyEntity, ReceiptEntity } from "./index";

@Entity('demand_report_data')
export class DemandReportData extends BaseEntity {

    @PrimaryGeneratedColumn('uuid')
    demand_report_data_id: string;

    @OneToOne(() => ReceiptEntity, (receipt) => receipt.DemandReportData, { onDelete: 'CASCADE' }) 
    @JoinColumn({ name: 'receipt_id' })
    ReciptInfo: ReceiptEntity;
   
    @Column({ type: 'float', nullable: true, default: 0 })
    all_property_tax_sum_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    all_property_tax_sum_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    all_property_tax_sum_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    all_property_tax_sum_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    all_property_tax_sum_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    all_property_tax_sum_curr_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    total_amount_paid: number;
    @Column({ type: 'float', nullable: true, default: 0 })
    total_amount_remaining: number;

  

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_1_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_1_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_1_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_1_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_1_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_1_curr_paid: number;


    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_2_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_2_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_2_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_2_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_2_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_2_curr_paid: number;

 

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_3_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_3_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_3_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_3_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_3_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_3_curr_paid: number;



    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_4_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_4_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_4_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_4_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_4_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_4_curr_paid: number;


    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_5_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_5_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_5_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_5_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_5_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_5_curr_paid: number;



    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_6_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_6_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_6_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_6_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_6_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_6_curr_paid: number;



    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_7_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_7_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_7_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_7_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_7_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_7_curr_paid: number;

  

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_8_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_8_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_8_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_8_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_8_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_8_curr_paid: number;


    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_9_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_9_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_9_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_9_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_9_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_9_curr_paid: number;



    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_10_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_10_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_10_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_10_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_10_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    tax_type_10_curr_paid: number;


    @Column({ type: 'float', nullable: true, default: 0 })
    other_tax_sum_tax_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    other_tax_sum_tax_prev_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    other_tax_sum_tax_curr_remaining: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    other_tax_sum_tax_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    other_tax_sum_tax_prev_paid: number;

    @Column({ type: 'float', nullable: true, default: 0 })
    other_tax_sum_tax_curr_paid: number;

    @Column({ type: String, nullable: true })
    status: string;

    @Column({ type: String, nullable: false })
    financial_year: string;

    @Column({ type: String, nullable: true })
    remaining_amount: string;

     

    @Column({ type: 'float', nullable: true, default: 0 })
    property_type_discount: string;

    @Column({ type: 'float', nullable: true, default: 0 })
    other_discount: string;



    @ManyToOne(() => PropertyEntity, (property) => property.demandReportData, {
      cascade: true,
      onDelete: 'CASCADE', // This will delete related payment_info records when a property is deleted
    })
    @JoinColumn({ name: 'property_id' })
    property: PropertyEntity;
    

    @Column({ type: String, nullable: true })
    property_number: string;

    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;

    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;

    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
}
