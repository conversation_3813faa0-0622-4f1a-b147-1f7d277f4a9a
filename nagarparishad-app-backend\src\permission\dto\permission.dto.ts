import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';

export class CreatePermissionDto {
  @ApiProperty({
    name: 'role',
    type: 'number',
  })
  @IsNumber()
  @IsNotEmpty()
  role: number;

  @ApiProperty({
    name: 'roles',
  })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => FormPermissionDto)
  forms: FormPermissionDto[];
}

export class UpdatePermissionDto {
  @ApiProperty({
    name: 'role',
    type: 'number',
  })
  @IsNumber()
  @IsOptional()
  role: number;

  @ApiProperty({
    name: 'roles',
  })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => UpdateFormPermissionDto)
  forms: UpdateFormPermissionDto[];
}

export class PermissionIdDto {
  @ApiProperty({ name: 'action', type: 'number' })
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => parseInt(value, 10))
  action_id: number;
}

export class FormIdDto {
  @ApiProperty({
    name: 'form',
    type: 'number',
    description: 'form master reference key',
  })
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => parseInt(value, 10))
  form: number;
}

export class FormPermissionDto {
  @ApiProperty({
    name: 'form',
    type: 'number',
  })
  @IsNumber()
  @IsNotEmpty()
  form: number;

  @ApiProperty({
    name: 'can_read',
    type: 'boolean',
  })
  @IsBoolean()
  can_read: boolean;

  @ApiProperty({
    name: 'can_write',
    type: 'boolean',
  })
  @IsBoolean()
  can_write: boolean;

  @ApiProperty({
    name: 'can_update',
    type: 'boolean',
  })
  @IsBoolean()
  can_update: boolean;

  @ApiProperty({
    name: 'can_delete',
    type: 'boolean',
  })
  @IsBoolean()
  can_delete: boolean;

  @ApiProperty({
    name: 'is_valid',
    type: 'boolean',
  })
  @IsBoolean()
  is_valid: boolean;
}

export class UpdateFormPermissionDto extends FormPermissionDto {
  @ApiProperty({
    name: 'action_id',
    type: 'number',
    description: 'action  id reference key',
  })
  @IsNumber()
  @IsNotEmpty()
  action_id: number;
}

export class RoleIdDto {
  @ApiProperty({ name: 'role', type: 'number' })
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => parseInt(value, 10))
  role: number;
}
