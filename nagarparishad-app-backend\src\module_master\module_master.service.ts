import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateModuleMasterDto } from './dto/module-master.dto';
import { ModuleMasterRepository } from 'libs/database/repositories';

@Injectable()
export class ModuleMasterService {
  constructor(
    private readonly moduleMasterRepository: ModuleMasterRepository,
  ) {}

  async create(input: CreateModuleMasterDto) {
    try {
      await this.moduleMasterRepository.saveData(input);

      return {
        message: 'Data Saved SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }

  async getData() {
    try {
      const getData = await this.moduleMasterRepository.getAll();

      if (!getData || getData.length === 0) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getData,
      };
    } catch (error) {
      throw error;
    }
  }
}
