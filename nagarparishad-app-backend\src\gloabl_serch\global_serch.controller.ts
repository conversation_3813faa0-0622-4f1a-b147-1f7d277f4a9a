import { Controller, Post, Body, Get, Query } from '@nestjs/common';
import { GlobalSearchDto } from './dto/gloabl_serch.dto';
import { GlobalSearchService } from './gloabal_serch.service';

@Controller('global-search') // Base route for the controller
export class GlobalSearchController {
    constructor(private readonly globalSearchService: GlobalSearchService) {}


    @Get() // Endpoint for checking property details
    async checkPropertyDetails(@Query() publicUserDto: GlobalSearchDto) {
        console.log('Received query params:', publicUserDto); // Debugging line

        return await this.globalSearchService.checkPropertyDetails(publicUserDto);
    }
}
