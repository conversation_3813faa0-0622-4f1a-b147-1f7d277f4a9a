// usePermissionController.tsx
import { useContext, useEffect, useState } from "react";
import PermissionApi from "../../services/PermissionServices";
import { GlobalContext } from "@/context/GlobalContext";
import {
  PermissionActionIdWise,
  PermissionFormWise,
  PermissionRoleWise,
} from "@/model/permission/permissonInerface";

export const usePermissionController = ({
  roleId,
  formId,
  actionId,
}: {
  roleId?: number;
  formId?: number;
  actionId?: number;
}) => {
  const [rolePermissions, setRolePermissions] = useState<
    PermissionRoleWise["data"]
  >([]);
  const [formPermissions, setFormPermissions] = useState<
    PermissionFormWise["data"]
  >([]);
  const [actionPermissions, setActionPermissions] = useState<
    PermissionActionIdWise["data"] | null
  >(null);
  const { refreshPermissionList } = useContext(GlobalContext);

  useEffect(() => {
    if (roleId !== undefined) {
      PermissionApi.getPermissionsByRole(
        roleId,
        (response: { status: boolean; data: PermissionRoleWise }) => {
          if (response.status && response.data.statusCode === 200) {
            setRolePermissions(response.data.data);
          }
        },
      );
    }
  }, [refreshPermissionList, roleId]);

  useEffect(() => {
    if (formId !== undefined) {
      PermissionApi.getPermissionsByFormId(
        formId,
        (response: { status: boolean; data: PermissionFormWise }) => {
          if (response.status && response.data.statusCode === 200) {
            setFormPermissions(response.data.data);
          }
        },
      );
    }
  }, [refreshPermissionList, formId]);

  useEffect(() => {
    if (actionId !== undefined) {
      PermissionApi.getPermissionsByActionId(
        actionId,
        (response: { status: boolean; data: PermissionActionIdWise }) => {
          if (response.status && response.data.statusCode === 200) {
            setActionPermissions(response.data.data);
          }
        },
      );
    }
  }, [refreshPermissionList, actionId]);

  return {
    rolePermissions,
    formPermissions,
    actionPermissions,
  };
};
