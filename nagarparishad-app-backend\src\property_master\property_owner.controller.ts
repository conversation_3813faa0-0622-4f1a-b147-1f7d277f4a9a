import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Put,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  HttpStatus,
  InternalServerErrorException,
  NotFoundException
} from '@nestjs/common';
import { PropertyOwnerService } from './property_owner.service';
import { CreatePropertyMasterDto } from './dto/create-property_master.dto';
import { UpdatePropertyMasterDto } from './dto/update-property_master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PropertyMasterIdDto } from './dto/property_master.dto';
import { SearchPropertyDto } from './dto/search-property_master.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { UpdatePropertyDto } from './dto/update-property_master.dto';
import { diskStorage } from 'multer';
import { extname } from 'path';
import csv from 'csv-parser';
import fs from 'fs';
import path from 'path';
import multer from 'multer';
import { CsvModule } from 'nest-csv-parser'
import { memoryStorage } from 'multer';
import { CSVDTO } from './dto/csv.validator.dto';
import { PropertyOwnerDetailsDto, ChangeOwnershipDto , DeleteOwnerDto, UpdateOwnerDetailsDto} from './dto/property-owner-details.dto';
import { PropertyFerfarAddUpdateDto } from './dto/property-ferfar-add-update.dto';
import { PropertyFerfarDeleteDto } from './dto/property-ferfar-delete.dto';
import { OwnerWrapperDto } from './dto/add-owner.dto';

@ApiTags('Property Owner')
@Controller('property-owner')
export class PropertyOwnerController {
  constructor(private readonly propertyOwnerService: PropertyOwnerService) {}
  @Post(':property_id/add-new-owner')
  async addNewOwner(
    @Param('property_id') property_id: string,
    @Body() ownerDetails: OwnerWrapperDto,
  ) {
    return this.propertyOwnerService.addNewOwners(property_id, ownerDetails);
  }

  @Patch('delete-owner/:property_owner_details_id')
  async deleteOwner(@Param('property_owner_details_id') property_owner_details_id: string,@Body() deleteOwnerDto: any) {
    await this.propertyOwnerService.deleteOwner(property_owner_details_id,deleteOwnerDto);
    return { message: 'Owner deleted successfully' };
  }

  @Patch('change-ownership/:property_owner_details_id')
  async changeOwnership(
    @Param('property_owner_details_id') property_owner_details_id: string,
    @Body() changeOwnershipDto: ChangeOwnershipDto,
  ) {
    return this.propertyOwnerService.changeOwnership(
      property_owner_details_id,
      changeOwnershipDto.is_owner,
    );
  }

  @Patch('correction/:property_owner_details_id')
  async updateOwner(
    @Param('property_owner_details_id') property_owner_details_id: string,
    @Body() updateOwnerDetails: UpdateOwnerDetailsDto,
  ) {
    return this.propertyOwnerService.updateOwner(property_owner_details_id, updateOwnerDetails);
  }

  @ApiOperation({ summary: 'Process property ferfar add/update operations' })
  @ApiResponse({ status: 201, description: 'Property ferfar add/update processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 404, description: 'Property not found' })
  @Post('ferfar/add-update')
  async processFerfarAddUpdate(@Body() ferfarDto: PropertyFerfarAddUpdateDto) {
    return this.propertyOwnerService.processFerfarAddUpdate(ferfarDto);
  }

  @ApiOperation({ summary: 'Process property ferfar delete operations' })
  @ApiResponse({ status: 200, description: 'Property ferfar delete processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 404, description: 'Property or owner not found' })
  @Post('ferfar/delete')
  async processFerfarDelete(@Body() ferfarDto: PropertyFerfarDeleteDto) {
    return this.propertyOwnerService.processFerfarDelete(ferfarDto);
  }

  @ApiOperation({ summary: 'Add multiple owners to a property' })
  @ApiResponse({ status: 201, description: 'Owners added successfully' })
  @ApiResponse({ status: 404, description: 'Property not found' })
  @Post(':property_id/add-multiple-owners')
  async addMultipleOwners(
    @Param('property_id') property_id: string,
    @Body() payload: any,
  ) {
    return this.propertyOwnerService.addMultipleOwners(property_id, payload);
  }

  @ApiOperation({ summary: 'Update an owner and add new owners' })
  @ApiResponse({ status: 200, description: 'Owner updated and new owners added successfully' })
  @ApiResponse({ status: 404, description: 'Property or owner not found' })
  @Post(':property_id/update-with-new-owners')
  async updateWithNewOwners(
    @Param('property_id') property_id: string,
    @Body() payload: any,
  ) {
    return this.propertyOwnerService.updateWithNewOwners(property_id, payload);
  }
}
