import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import Api from "../../services/ApiServices";

const QUERY_KEY = ["propertyfloormaster"]; // Consistent query key

const fetchfloors = async () => {
  const response = await Api.getAllPropertyFloor();
  return response.data;
};

const createFloor = async (wardData: any) => {
  return new Promise((resolve, reject) => {
    Api.createPropertFloor(wardData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateFloor = async ({
  wardId,
  wardData,
}: {
  wardId: any;
  wardData: any;
}) => {
  return new Promise((resolve, reject) => {
    Api.updateFloor(wardId, wardData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteWard = async (wardId: string) => {
  return new Promise((resolve, reject) => {
    Api.deleteFloor(wardId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const usePropertyFloorMasterController = () => {
  const queryClient = useQueryClient();

  const { data: propertyFloorData, isLoading: propertyFloorLoading } = useQuery({
    queryKey: QUERY_KEY,
    queryFn: fetchfloors,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  const createWardMutation = useMutation({
    mutationFn: createFloor,
    onSuccess: (newData) => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      
      // Optimistically update the cache
      queryClient.setQueryData(QUERY_KEY, (old: any) => {
        const oldData = old?.data || [];
        return {
          ...old,
          data: [newData, ...oldData]
        };
      });
    },
    onError: (error) => {
      console.error("Error creating floor:", error);
    }
  });

  const updateWardMutation = useMutation({
    mutationFn: updateFloor,
    onSuccess: (updatedData, variables) => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      
      // Optimistically update the cache
      queryClient.setQueryData(QUERY_KEY, (old: any) => {
        const oldData = old?.data || [];
        return {
          ...old,
          data: oldData.map((item: any) =>
            item.floor_id === variables.wardId ? { ...item, ...variables.wardData } : item
          )
        };
      });
    },
    onError: (error) => {
      console.error("Error updating floor:", error);
    }
  });

  const deleteWardMutation = useMutation({
    mutationFn: deleteWard,
    onSuccess: (_, wardId) => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: QUERY_KEY });
      
      // Optimistically update the cache
      queryClient.setQueryData(QUERY_KEY, (old: any) => {
        const oldData = old?.data || [];
        return {
          ...old,
          data: oldData.filter((item: any) => item.floor_id !== wardId)
        };
      });
    },
    onError: (error) => {
      console.error("Error deleting floor:", error);
    }
  });

  return {
    propertyFloorList: propertyFloorData || [],
    propertyFloorLoading,
    createFloor: createWardMutation.mutate,
    updateFloor: updateWardMutation.mutate,
    deleteWard: deleteWardMutation.mutate,
  };
};