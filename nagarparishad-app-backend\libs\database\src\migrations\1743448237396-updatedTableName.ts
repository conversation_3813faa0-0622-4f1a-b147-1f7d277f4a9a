import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatedTableName1743448237396 implements MigrationInterface {
  name = 'UpdatedTableName1743448237396';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "demand_report_data" ("demand_report_data_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "all_property_tax_sum" double precision DEFAULT '0', "all_property_tax_sum_prev_remaining" double precision DEFAULT '0', "all_property_tax_sum_curr_remaining" double precision DEFAULT '0', "total_amount" double precision DEFAULT '0', "tax_type_1_remaining" double precision DEFAULT '0', "tax_type_1_prev_remaining" double precision DEFAULT '0', "tax_type_1_curr_remaining" double precision DEFAULT '0', "tax_type_1_paid" double precision DEFAULT '0', "tax_type_1_prev_paid" double precision DEFAULT '0', "tax_type_1_curr_paid" double precision DEFAULT '0', "tax_type_2_remaining" double precision DEFAULT '0', "tax_type_2_prev_remaining" double precision DEFAULT '0', "tax_type_2_curr_remaining" double precision DEFAULT '0', "tax_type_2_paid" double precision DEFAULT '0', "tax_type_2_prev_paid" double precision DEFAULT '0', "tax_type_2_curr_paid" double precision DEFAULT '0', "tax_type_3_remaining" double precision DEFAULT '0', "tax_type_3_prev_remaining" double precision DEFAULT '0', "tax_type_3_curr_remaining" double precision DEFAULT '0', "tax_type_3_paid" double precision DEFAULT '0', "tax_type_3_prev_paid" double precision DEFAULT '0', "tax_type_3_curr_paid" double precision DEFAULT '0', "tax_type_4_remaining" double precision DEFAULT '0', "tax_type_4_prev_remaining" double precision DEFAULT '0', "tax_type_4_curr_remaining" double precision DEFAULT '0', "tax_type_4_paid" double precision DEFAULT '0', "tax_type_4_prev_paid" double precision DEFAULT '0', "tax_type_4_curr_paid" double precision DEFAULT '0', "tax_type_5_remaining" double precision DEFAULT '0', "tax_type_5_prev_remaining" double precision DEFAULT '0', "tax_type_5_curr_remaining" double precision DEFAULT '0', "tax_type_5_paid" double precision DEFAULT '0', "tax_type_5_prev_paid" double precision DEFAULT '0', "tax_type_5_curr_paid" double precision DEFAULT '0', "tax_type_6_remaining" double precision DEFAULT '0', "tax_type_6_prev_remaining" double precision DEFAULT '0', "tax_type_6_curr_remaining" double precision DEFAULT '0', "tax_type_6_paid" double precision DEFAULT '0', "tax_type_6_prev_paid" double precision DEFAULT '0', "tax_type_6_curr_paid" double precision DEFAULT '0', "tax_type_7_remaining" double precision DEFAULT '0', "tax_type_7_prev_remaining" double precision DEFAULT '0', "tax_type_7_curr_remaining" double precision DEFAULT '0', "tax_type_7_paid" double precision DEFAULT '0', "tax_type_7_prev_paid" double precision DEFAULT '0', "tax_type_7_curr_paid" double precision DEFAULT '0', "tax_type_8_remaining" double precision DEFAULT '0', "tax_type_8_prev_remaining" double precision DEFAULT '0', "tax_type_8_curr_remaining" double precision DEFAULT '0', "tax_type_8_paid" double precision DEFAULT '0', "tax_type_8_prev_paid" double precision DEFAULT '0', "tax_type_8_curr_paid" double precision DEFAULT '0', "tax_type_9_remaining" double precision DEFAULT '0', "tax_type_9_prev_remaining" double precision DEFAULT '0', "tax_type_9_curr_remaining" double precision DEFAULT '0', "tax_type_9_paid" double precision DEFAULT '0', "tax_type_9_prev_paid" double precision DEFAULT '0', "tax_type_9_curr_paid" double precision DEFAULT '0', "tax_type_10_remaining" double precision DEFAULT '0', "tax_type_10_prev_remaining" double precision DEFAULT '0', "tax_type_10_curr_remaining" double precision DEFAULT '0', "tax_type_10_paid" double precision DEFAULT '0', "tax_type_10_prev_paid" double precision DEFAULT '0', "tax_type_10_curr_paid" double precision DEFAULT '0', "other_tax_sum_tax_remaining" double precision DEFAULT '0', "other_tax_sum_tax_prev_remaining" double precision DEFAULT '0', "other_tax_sum_tax_curr_remaining" double precision DEFAULT '0', "other_tax_sum_tax_paid" double precision DEFAULT '0', "other_tax_sum_tax_prev_paid" double precision DEFAULT '0', "other_tax_sum_tax_curr_paid" double precision DEFAULT '0', "status" character varying, "financial_year" character varying NOT NULL, "remaining_amount" character varying, "property_number" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "payment_info_id" uuid, "property_id" uuid, CONSTRAINT "REL_d14ec378ce1e5bf7c014001fc9" UNIQUE ("payment_info_id"), CONSTRAINT "PK_0959cbb6e12261a2d856363fbf3" PRIMARY KEY ("demand_report_data_id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "demand_report_data" ADD CONSTRAINT "FK_d14ec378ce1e5bf7c014001fc9e" FOREIGN KEY ("payment_info_id") REFERENCES "receipt"("receipt_id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "demand_report_data" ADD CONSTRAINT "FK_acee47cdb6ce978a5883c61e24e" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "demand_report_data" DROP CONSTRAINT "FK_acee47cdb6ce978a5883c61e24e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "demand_report_data" DROP CONSTRAINT "FK_d14ec378ce1e5bf7c014001fc9e"`,
    );

    await queryRunner.query(`DROP TABLE "demand_report_data"`);
  }
}
