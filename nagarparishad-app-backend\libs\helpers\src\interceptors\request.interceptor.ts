import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class RequestInterceptor implements NestInterceptor {
  private readonly logger = new Logger(RequestInterceptor.name);
  intercept(
    context: ExecutionContext,
    next: <PERSON><PERSON><PERSON><PERSON><any>,
  ): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const userAgent = request.get('user-agent') || '';
    const ipXFF =
      request.headers['x-forwarded-for'] || request.connection.remoteAddress;

    const { ip, method, path: url } = request;

    this.logger.log(
      `${method} ${url} ${userAgent} ip: ${ip} IpXFF ${ipXFF}: ${context.getClass().name} ${
        context.getHandler().name
      }   invoked...`,
    );

    return next.handle();
  }
}
