import React, { useContext } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { GlobalContext } from "@/context/GlobalContext";

const DialogDemo = ({
  title,
  description,
  isOpen,
  toggle,
  children,
  classname,
}: any) => {
  const handlechange = () => {
    toggle();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handlechange}>
      <DialogContent className={` bg-white ${classname} `}>
        {children}
      </DialogContent>
    </Dialog>
  );
};

export default DialogDemo;
