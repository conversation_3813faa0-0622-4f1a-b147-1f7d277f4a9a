import { IsOptional, IsString, IsInt, IsPositive } from 'class-validator';
import { Type } from 'class-transformer';

export class GlobalSearchDto {
    @IsOptional()
    @IsString()
    mobile_number?: string;
    @IsOptional()
    @IsString()
    value?: string;


    @IsOptional()
    @IsString()
    propertyNumber?: string;

    @IsOptional()
    @IsString()
    ward?: string;

    @IsOptional()
    @IsString()
    zone?: string;

    @IsOptional()
    @IsString()
    name?: string;

    @IsOptional()
    @IsInt()
    @IsPositive()
    @Type(() => Number) // Ensure conversion to number
    page?: number; 

    @IsOptional()
    @IsInt()
    @IsPositive()
    @Type(() => Number) // Ensure conversion to number
    limit?: number; 
}
