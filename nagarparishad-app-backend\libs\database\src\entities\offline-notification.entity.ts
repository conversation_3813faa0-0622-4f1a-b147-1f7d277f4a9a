import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserMasterEntity } from './userMaster.entity';

@Entity('offline_notifications')
export class OfflineNotificationEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => UserMasterEntity, (user) => user.user_id)
  @JoinColumn({ name: 'user_id' })
  user: UserMasterEntity;

  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Column({ name: 'notification_id', type: 'varchar', length: 255 })
  notificationId: string;

  @Column({ name: 'title', type: 'varchar', length: 255 })
  title: string;

  @Column({ name: 'message', type: 'text' })
  message: string;

  @Column({ 
    name: 'type', 
    type: 'enum', 
    enum: ['info', 'success', 'warning', 'error', 'progress'],
    default: 'info'
  })
  type: 'info' | 'success' | 'warning' | 'error' | 'progress';

  @Column({ 
    name: 'status', 
    type: 'enum', 
    enum: ['pending', 'processing', 'completed', 'failed', 'delivered'],
    default: 'pending'
  })
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'delivered';

  @Column({ name: 'progress', type: 'int', nullable: true })
  progress: number;

  @Column({ name: 'file_path', type: 'varchar', length: 500, nullable: true })
  filePath: string;

  @Column({ name: 'file_name', type: 'varchar', length: 255, nullable: true })
  fileName: string;

  @Column({ name: 'file_size', type: 'bigint', nullable: true })
  fileSize: number;

  @Column({ name: 'download_url', type: 'varchar', length: 500, nullable: true })
  downloadUrl: string;

  @Column({ name: 'is_downloaded', type: 'boolean', default: false })
  isDownloaded: boolean;

  @Column({ name: 'download_count', type: 'int', default: 0 })
  downloadCount: number;

  @Column({ name: 'is_read', type: 'boolean', default: false })
  isRead: boolean;

  @Column({ name: 'expires_at', type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: {
    reportType?: 'namuna_eight' | 'namuna_nine';
    totalRecords?: number;
    processedRecords?: number;
    originalFileName?: string;
    webhookData?: any;
  };

  @Column({ name: 'webhook_received_at', type: 'timestamp', nullable: true })
  webhookReceivedAt: Date;

  @Column({ name: 'delivered_at', type: 'timestamp', nullable: true })
  deliveredAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
