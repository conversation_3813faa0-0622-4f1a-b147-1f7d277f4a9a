import React, { useContext, useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormControl,
} from "@/components/ui/form";
import { PropertyTaxObjectInterface } from "@/model/propertytaxrate-master";
import { useToast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { CalendarIcon, Loader2 } from "lucide-react";
import AsyncSelect from "@/components/ui/react-select";

import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { ReactselectInterface } from "@/model/global-master";
import { useZoneMasterController } from "@/controller/master/ZoneMasterController";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { format } from "date-fns";

interface PropertyTaxFormInterface {
  btnTitle: string;
  editData?: PropertyTaxObjectInterface;
}

const PropertyTaxRateForm = ({
  btnTitle,
  editData,
}: PropertyTaxFormInterface) => {
  const { t } = useTranslation();

  const schema = z.object({
    fromDate: z.date({
      required_error: t("errorsRequiredField"),
    }),
    toDate: z.date({
      required_error: t("errorsRequiredField"),
    }),
    zone: z.string().trim().min(1, t("errorsRequiredField")),
    ward: z.string().trim().min(1, t("errorsRequiredField")),
    usage: z.string().trim().min(1, t("errorsRequiredField")),
    usageSub: z.string().trim().min(1, t("errorsRequiredField")),
    serveNo: z.number().min(1, t("errorsRequiredField")),
    readyRecokner: z.string().min(1, t("errorsRequiredField")),
    valueRate: z.string().min(1, t("errorsRequiredField")),
  });

  const { toast } = useToast();
  const [loader, setLoader] = useState(false);
  const {
    setIsCollapseOpen,
    refreshPropertyTaxList,
    setRefreshPropertyTaxList,
  } = useContext(GlobalContext);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      zone: editData?.zoneName || "",
      ward: editData?.ward?.ward_id || "",
      usage: editData?.usage || "",
      usageSub: editData?.usageSub || "",
      serveNo: editData?.serveNo || 0,
      readyRecokner: editData?.readyRecokner || "",
      valueRate: editData?.valueRate || "",
    },
  });

  const {
    formState: { errors },
    reset,
  } = form;
  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();
    // Handle form submission
  };

  const [selectedWard, setselectedWard] = useState<ReactselectInterface | null>(
    null,
  );

  const { wardList } = useWardMasterController();
  const wardOptions: ReactselectInterface[] =
    wardList?.map((ward: any) => ({
      value: ward.ward_id,
      label: ward.ward_name,
    })) || [];

  const { zoneList } = useZoneMasterController();
  const filteredZones = zoneList?.filter(
    (zone: any) => zone.ward?.ward_id === selectedWard?.value,
  );

  const zoneOptions: ReactselectInterface[] =
    filteredZones?.map((zone: any) => ({
      value: zone.zone_id,
      label: zone.zoneName,
    })) || [];

  const loadWardOptions = (
    inputValue: string,
    callback: (options: ReactselectInterface[]) => void,
  ) => {
    setTimeout(() => {
      callback(
        wardOptions.filter((option) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase()),
        ),
      );
    }, 1000);
  };

  const loadZoneOptions = (
    inputValue: string,
    callback: (options: ReactselectInterface[]) => void,
  ) => {
    setTimeout(() => {
      callback(
        zoneOptions.filter((option) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase()),
        ),
      );
    }, 1000);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className=" ">
        <div className="form-flex">
          <div className="form-element">
            <FormField
              control={form.control}
              name="fromDate"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>{t("propertytaxrate.fromDate")}</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !field.value && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>{t("propertytaxrate.pickaDate")}</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Calendar
                        className="dashboard-calender"
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </FormItem>
              )}
            />
          </div>
          <div className="form-element">
            <FormField
              control={form.control}
              name="toDate"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>{t("propertytaxrate.toDate")}</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !field.value && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>{t("propertytaxrate.pickaDate")}</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Calendar
                        className="dashboard-calender"
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="form-flex">
          <div className="form-element">
            <FormField
              control={form.control}
              name="ward"
              render={({ field }) => (
                <FormItem className=" ">
                  <FormLabel>{t("ward.wardLabel")}</FormLabel>
                  <FormControl>
                    <AsyncSelect
                      placeholder={t("ward.wardLabel")}
                      loadOptions={loadWardOptions}
                      defaultOptions={wardOptions}
                      value={wardOptions.find(
                        (option) => option.value === field.value,
                      )}
                      onChange={(selectedOption) => {
                        setselectedWard(selectedOption);
                        field.onChange(selectedOption?.value || "");
                      }}
                      colourOptions={wardOptions}
                    />
                  </FormControl>
                  {errors.ward && (
                    <FormMessage className="ml-1">
                      {t("errorsRequiredField")}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
          <div className="form-element">
            <FormField
              control={form.control}
              name="zone"
              render={({ field }) => (
                <FormItem className=" ">
                  <FormLabel>{t("zone.zoneLabel")}</FormLabel>
                  <FormControl>
                    <AsyncSelect
                      placeholder={t("zone.zoneLabel")}
                      loadOptions={loadZoneOptions}
                      defaultOptions={zoneOptions}
                      value={zoneOptions.find(
                        (option) => option.value === field.value,
                      )}
                      onChange={(selectedOption) =>
                        field.onChange(selectedOption?.value || "")
                      }
                      colourOptions={zoneOptions}
                    />
                  </FormControl>
                  {errors.zone && (
                    <FormMessage className="ml-1">
                      {t("errorsRequiredField")}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="form-flex">
          <div className="form-element">
            <FormField
              control={form.control}
              name="valueRate"
              render={({ field }) => (
                <FormItem className=" ">
                  <FormLabel>{t("propertytaxrate.taxMin")}</FormLabel>
                  <FormControl>
                    <Input
                      className="mt-1 block w-full"
                      type="text"
                      placeholder={t("propertytaxrate.taxMin")}
                      {...field}
                    />
                  </FormControl>
                  {errors.valueRate && (
                    <FormMessage className="ml-1">
                      {t("errorsRequiredField")}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
          <div className="form-element">
            <FormField
              control={form.control}
              name="valueRate"
              render={({ field }) => (
                <FormItem className=" ">
                  <FormLabel>{t("propertytaxrate.taxMax")}</FormLabel>
                  <FormControl>
                    <Input
                      className="mt-1 block w-full"
                      type="text"
                      placeholder={t("propertytaxrate.taxMax")}
                      {...field}
                    />
                  </FormControl>
                  {errors.valueRate && (
                    <FormMessage className="ml-1">
                      {t("errorsRequiredField")}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="form-flex">
          <div className="sm:w-[49%]">
            <FormField
              control={form.control}
              name="valueRate"
              render={({ field }) => (
                <FormItem className=" ">
                  <FormLabel>{t("propertytaxrate.municipalTax")} </FormLabel>
                  <FormControl>
                    <Input
                      className="mt-1 block w-full"
                      type="text"
                      placeholder={t("propertytaxrate.municipalTax")}
                      {...field}
                    />
                  </FormControl>
                  {errors.valueRate && (
                    <FormMessage className="ml-1">
                      {t("errorsRequiredField")}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="w-full flex justify-end mt-4">
          {loader ? (
            <Button disabled>
              <Loader2 className="mr-2 h-4 w-5 animate-spin " />
              {t("pleaseWait")}
            </Button>
          ) : (
            <Button type="submit" variant="submit">
              {t(btnTitle)}
            </Button>
          )}
        </div>
      </form>
    </Form>
  );
};

export default PropertyTaxRateForm;
