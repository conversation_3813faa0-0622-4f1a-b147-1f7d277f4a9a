import TaxList<PERSON><PERSON> from "../../services/TaxServices";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

const fetchTaxData = async () => {
  return new Promise((resolve, reject) => {
    TaxListApi.getTaxUserListByFY((response) => {
      if (response.status) {
        resolve(response.data.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const fetchYearData = async () => {
  return new Promise((resolve, reject) => {
    TaxListApi.getTaxUserListByYear((response) => {
      if (response.status) {
        resolve(response.data.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const changePublishStatus = async ({ publishId, publishStatus }) => {
  console.log("changePublishStatus", publishId, publishStatus);
  return new Promise((resolve, reject) => {
    TaxListApi.changePublishStatus(publishId, publishStatus, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(new Error("Error updating user"));
      }
    });
  });
};




export const useTaxController = () => {
  const queryClient = useQueryClient();

  const changePublishStatusMutation = useMutation({
    mutationFn: changePublishStatus,
    onMutate: async ({ publishId, publishStatus }) => {
      await queryClient.invalidateQueries({ queryKey: ["yearData"] });
      const previousStatus = queryClient.getQueryData(["yearData"]);
      queryClient.setQueryData(["yearData"], (old: { publish: any }[]) =>
        old.map((user: { publish: any }) =>
          user.publish === publishId ? { ...user, ...publishStatus } : user,
        ),
      );
      return { previousStatus };
    },
    onError: (err, { publishId, publishStatus }, context) => {
      queryClient.setQueryData(["yearData"], context.previousStatus);
    },

    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["yearData"] });
    },
  });

  const {
    data: taxData,
   
  } = useQuery({
    queryKey: ["taxData"],
    queryFn: fetchTaxData,
    staleTime: 0,
    refetchOnWindowFocus: true,
  });

  const { data: yearData, isLoading: yearDataLoading,refetch: refetchYearData } = useQuery({
    queryKey: ["yearData"],
    queryFn: fetchYearData,
    staleTime: 0,
    refetchOnWindowFocus: true,
  });
  return {
    taxData: taxData || [],
    yearData: yearData || [],
    yearDataLoading,
    changePublishStatus: changePublishStatusMutation.mutate,
     refetchYearData
  };

};

