import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
  } from 'typeorm';
  
  @Entity('tax_type_master')
  export class Tax_Type_Master extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    tax_type_id: string;
  
    @Column({ name: 'tax_type', nullable: false, type: String })
    tax_type: string;
  
    /*
     * Create and Update Date Columns
     */
  
    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;
  
    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;
  
    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
  }
  