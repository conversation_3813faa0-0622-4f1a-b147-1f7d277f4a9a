import {
  BadRequestException,
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';

import {
  Financial_yearRepository,
  Master_depreciationRepository,
  Master_rr_construction_rateRepository,
  Master_rr_rateRepository,
  Master_TaxValueRepository,
  Master_WeightingRepository,
  MasterGhanKachraRateRepository,
  WarshikKarRepository,
  ReassessmentRangeRepository,
  PropertyMasterRepository,
} from 'libs/database/repositories';
import { AnnualKarAkaraniService } from 'src/annual-kar-akarani/annual-kar-akarani.service';

@Injectable()
export class FinancialMasterService {
  constructor(
    private readonly financial_yearRepository: Financial_yearRepository,
    private readonly warShikKarAkarani: WarshikKarRepository,
    private readonly annualKarAkarniService: AnnualKarAkaraniService,
    private readonly propertyMasterRepository: PropertyMasterRepository,
    private readonly weightingRateRepo: Master_WeightingRepository,
    private readonly depreciationRepo: Master_depreciationRepository,
    private readonly rrConstrutionRepo: Master_rr_construction_rateRepository,
    private readonly rrRateRepo: Master_rr_rateRepository,
    private readonly masterTaxValueRepo: Master_TaxValueRepository,
    private readonly masterGhanKachraRepo: MasterGhanKachraRateRepository,
    private readonly reassessmentRangeRepo: ReassessmentRangeRepository,
  ) {}

  async findAll() {
    try {
      const getAllFyList = await this.financial_yearRepository.getAll();

      if (!getAllFyList) {
        throw new NotFoundException('Financial Year Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllFyList,
      };
    } catch (error) {
      throw error;
    }
  }

  async createFinancialYear() {
    const currentFinancialYear = await this.financial_yearRepository.findOne({
      where: { is_current: true },
    });
    if (!currentFinancialYear) {
      throw new BadRequestException(
        'No active financial year found. Please check the database.',
      );
    }
    const [startYear, endYear] = currentFinancialYear.financial_year_range
      .split('-')
      .map(Number);

    const financialYearRange = `${endYear}-${endYear + 1}`;

    const fromDate = new Date(Date.UTC(endYear, 3, 1))
      .toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      })
      .split('/')
      .join('-');
    const toDate = new Date(Date.UTC(endYear + 1, 2, 31))
      .toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      })
      .split('/')
      .join('-');

    const currentDate = Date.now();
    const last_toDate = currentFinancialYear.to_date + '';

    const [day, month, year] = last_toDate.split('-').map(Number);
    const date = new Date(year, month - 1, day);

    if (currentDate <= date.getTime()) {
      throw new BadRequestException(
        `Cannot create a new financial year until the current year (ending on ${date}) is completed.`,
      );
    }

    await this.financial_yearRepository.update(
      currentFinancialYear.financial_year_id,
      { is_current: false },
    );

    const newFinancialYear = this.financial_yearRepository.create({
      financial_year_range: financialYearRange,
      from_date: fromDate,
      to_date: toDate,
      is_current: true,
      is_active: '1',
      is_published: 'true',
    });

    const savedFinancialYear =
      await this.financial_yearRepository.save(newFinancialYear);

    // Find the specific property with property number SNP700064
    // const specificProperty =
    //   await this.propertyMasterRepository.findByPropertyNumber('SNP700064');

    // if (!specificProperty) {
    //   console.error('Property with number SNP700064 not found');
    //   return {
    //     message:
    //       'Successfully created new financial year but property SNP700064 not found',
    //   };
    // }

    // console.log(
    //   `Processing pending dues only for property SNP700064 from previous financial year: ${currentFinancialYear.financial_year_range}`,
    // );

    // // Find the warshik kar record for the specific property
    // const taxRecords = await this.warShikKarAkarani.find({
    //   where: {
    //     property: { property_id: specificProperty.property_id },
    //     financial_year: currentFinancialYear?.financial_year_range,
    //     status: 'active',
    //   },
    //   relations: ['property'],
    // });

    // console.log(
    //   `Found ${taxRecords.length} tax records to process for pending dues for property SNP700064`,
    // );

    // if (taxRecords.length > 0) {
    //   await this.annualKarAkarniService.calculateAndStorePendingDues(
    //     taxRecords[0],
    //     newFinancialYear,
    //     currentFinancialYear,
    //   );
    //   console.log(`Completed pending dues calculation for property SNP700064`);
    // } else {
    //   console.log(
    //     `No active warshik kar record found for property SNP700064 in financial year ${currentFinancialYear?.financial_year_range}`,
    //   );
    // }

    // // Process warshik kar for the specific property for the new financial year
    // try {
    //   console.log(
    //     `Starting Warshik Kar generation for property SNP700064 for new financial year: ${savedFinancialYear.financial_year_range}`,
    //   );

    //   // Get the current reassessment range
    //   const currentReassessmentRange =
    //     await this.reassessmentRangeRepo.getCurrentReassesmentRange();

    //   if (currentReassessmentRange) {
    //     // Process warshik kar for the specific property
    //     await this.annualKarAkarniService.processWarshikKarForSingleProperty(
    //       savedFinancialYear.financial_year_id,
    //       specificProperty.property_id,
    //     );

    //     console.log(`Warshik Kar generation completed for property SNP700064`);
    //   } else {
    //     console.warn(
    //       'No current reassessment range found. Skipping Warshik Kar generation.',
    //     );
    //   }
    // } catch (error) {
    //   console.error(
    //     'Error generating Warshik Kar for property SNP700064:',
    //     error,
    //   );
    //   // We don't throw the error here to ensure the financial year creation completes successfully
    //   // even if warshik kar generation fails
    // }

    return {
      message:
        'Successfully created new financial year and generated Warshik Kar data for property SNP700064',
    };
  }
}
