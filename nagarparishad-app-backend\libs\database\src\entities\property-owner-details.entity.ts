import {
  BaseEntity,
  ManyToOne,
  JoinColumn,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PropertyEntity } from './property.entity';
import { Owner_type_master } from './owner_type_master.entity';

// Assuming this enum is declared in a shared location or directly in this file
export enum Gender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER',
}

export enum MaritalStatus {
  YES = 'yes',
  NO = 'no',
  DIVORCED = 'divorced',
}

@Entity('property_owner_details')
export class Property_Owner_Details_Entity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  property_owner_details_id: string;

  @ManyToOne(() => PropertyEntity, (property) => property.property_id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

  @ManyToOne(() => Owner_type_master, (owner_type) => owner_type.owner_type_id)
  @JoinColumn({ name: 'owner_type_id' })
  owner_type: Owner_type_master;

  @Column({ type: 'varchar', nullable: true })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  mobile_number: string;

  @Column({ type: 'varchar', nullable: true })
  email_id: string;

  @Column({ type: 'varchar', nullable: true })
  aadhar_number: string;

  @Column({ type: 'varchar', nullable: true })
  pan_card: string;


  @Column({ type: 'enum', enum: Gender, nullable: true })
  gender: Gender;
  
  @Column({ type: 'enum', enum: MaritalStatus, nullable: true })
  marital_status: MaritalStatus;
  @Column({ type: 'varchar', nullable: true })
  partner_name: string;

  
  @Column({ type: 'varchar', nullable: true })
  remark: string;

  @Column({ type: 'varchar', nullable: true })
  last_action_done: string;

  @Column({ type: 'boolean',nullable: true })
  is_owner: boolean;

  @Column({ type: 'boolean',nullable: true })
  is_payer: boolean;

  
  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
