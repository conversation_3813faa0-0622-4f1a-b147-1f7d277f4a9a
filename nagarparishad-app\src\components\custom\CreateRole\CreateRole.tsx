import React, { useState, useEffect } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { useLocation, useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { useModuleController } from "@/controller/module/ModuleController";
import PermissionApi from "@/services/PermissionServices";
import { toast } from "@/components/ui/use-toast";
import { useTranslation } from "react-i18next";
import { usePermissionController } from "@/controller/permission/PermissonController";
import { Edit, ChevronDown, ChevronUp } from "lucide-react";

const CreateRole: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const { roleName, roleId, edit, viewOnly } = location.state || {};
  const { moduleList } = useModuleController();
  const { rolePermissions } = usePermissionController({ roleId: roleId });
  console.log("rolePermissionsrolePermissions",rolePermissions);

  const [permissions, setPermissions] = useState<{ [key: number]: any }>({});
  const [expandedModules, setExpandedModules] = useState<{
    [key: string]: boolean;
  }>({});
  const [editableModule, setEditableModule] = useState<string | null>(null);
console.log("location state",location.state,roleId);
  useEffect(() => {
    if (edit && rolePermissions && rolePermissions.length > 0) {
      const initialPermissions = {};
      rolePermissions.forEach((module) => {
        if (module?.forms) {
          module.forms.forEach((form) => {
            if (form?.permissions && form.permissions.length > 0) {
              const permission = form.permissions[0];
              initialPermissions[form.form_id] = {
                can_read: permission.can_read,
                can_write: permission.can_write,
                can_update: permission.can_update,
                can_delete: permission.can_delete,
                action_id: permission.action_id,
              };
            }
          });
        }
      });
      console.log("initialPermissionsinitialPermissionseditt",initialPermissions)

      setPermissions(initialPermissions);
    } else if (!edit && moduleList) {
      const initialPermissions = {};
      rolePermissions.forEach((module) => {
        if (module?.forms) {
          module.forms.forEach((form) => {
            if (form?.permissions && form.permissions.length > 0) {
              const permission = form.permissions[0];
              initialPermissions[form.form_id] = {
                can_read: permission.can_read,
                can_write: permission.can_write,
                can_update: permission.can_update,
                can_delete: permission.can_delete,
                action_id: permission.action_id,
              };
            }
          });
        }
      });
      console.log("initialPermissionsinitialPermissions",initialPermissions)
      console.log("initialPermissionsinitialPermissions moduleList",moduleList)

      setPermissions(initialPermissions);
    }
  }, [edit, moduleList, rolePermissions]);

  const handlePermissionChange = (
    formId: number,
    permissionType: string,
    value: boolean,
  ) => {
    console.log("handlePermissionChange",formId,permissionType,value,permissions,"permissions")
    setPermissions((prevPermissions) => ({
      ...prevPermissions,
      [formId]: {
        ...prevPermissions[formId],
        [permissionType]: value,
      },
    }));  
  };

  const handleModuleToggle = (moduleName: string) => {
    setExpandedModules((prevState) => ({
      ...prevState,
      [moduleName]: !prevState[moduleName],
    }));
  };

  const handleModuleSelectAllChange = (module, permissionType, value) => {
    const newPermissions = { ...permissions };
    module.forms.forEach((form) => {
      newPermissions[form.form_id][permissionType] = value;
    });
    setPermissions(newPermissions);
  };

  const handleSave = () => {
    if (viewOnly) return; // Prevent saving in view-only mode
  
  
    const formattedPermissions = Object.keys(permissions).map((formId) => {

      const basePermission = {
        form: parseInt(formId),
        can_read: permissions[formId].can_read ?? false ,
        can_write: permissions[formId].can_write ?? false ,
        can_update: permissions[formId].can_update ?? false,
        can_delete: permissions[formId].can_delete ?? false,
        is_valid: true,
        action_id: edit ? permissions : undefined

      };
console.log("basePermission",basePermission,permissions)
   

return {
  ...basePermission,
  action_id: permissions[formId].action_id,
};
    });
console.log("formattedPermissions",formattedPermissions)
    const requestBody = {
      role: roleId, // Include role ID for both edit and add
      forms: formattedPermissions,
    };

      PermissionApi.editPermission(requestBody, (response) => {
        if (response.status) {
          toast({
            title: t("permissionsSavedSuccess"),
            variant: "success",
          });
          setTimeout(() => {
            navigate("/dashboard/user-role");
          }, 2000);
        } else {
          toast({
            title: t("permissionsSavedFail"),
            variant: "destructive",
          });
        }
      });
    
  };
  

  const handleEdit = (moduleName) => {
    setEditableModule(moduleName);
  };

  const handleCancel = () => {
    navigate("/dashboard/user-role");
  };

  return (
    <div className="w-full h-fit p-6">
      <div className="bg-white shadow rounded-lg p-4 mb-6">
        <h2 className="text-2xl font-bold mb-4">
          {t("createRoleTitle")} {roleName}
        </h2>
        <div className="flex items-center mb-6">
          <Input
            value={roleName}
            placeholder={t("roleNamePlaceholder")}
            className="mr-4 w-1/3"
            readOnly
          />
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-4">
        <Table className="w-full text-sm text-left text-zinc-500">
          <TableHeader className="text-xs text-zinc-700 uppercase bg-zinc-50">
            <TableRow>
              <TableHead className="font-bold text-sm">
                {t("permission")}
              </TableHead>
              <TableHead className="text-center font-bold text-sm">
                {t("view")}
              </TableHead>
              <TableHead className="text-center font-bold text-sm">
                {t("add")}
              </TableHead>
              <TableHead className="text-center font-bold text-sm">
                {t("create")}
              </TableHead>
              <TableHead className="text-center font-bold text-sm">
                {t("delete")}
              </TableHead>
              {edit && !viewOnly && (
                <TableHead className="text-center font-bold text-sm">
                  {t("Actions")}
                </TableHead>
              )}
            </TableRow>
          </TableHeader>

          <TableBody>
            {moduleList &&
              moduleList.map(
                (module, moduleIndex) =>
                  module.forms.length > 0 && (
                    <React.Fragment key={moduleIndex}>
                      <TableRow
                        onClick={() => handleModuleToggle(module.moduleName)}
                        className="cursor-pointer"
                      >
                        <TableCell colSpan={1} className="font-bold py-4 flex items-center">
                          {module.moduleName}
                          {expandedModules[module.moduleName] ? <ChevronUp className="ml-4" /> : <ChevronDown className="mr-2" />}

                        </TableCell>
                        <TableCell className="text-center">
                          <Checkbox
                            className="w-6 h-6"
                            checked={module.forms.every(
                              (form) => permissions[form.form_id]?.can_read,
                            )}
                            onCheckedChange={(checked) =>
                              handleModuleSelectAllChange(
                                module,
                                "can_read",
                                checked,
                              )
                            }
                            onClick={(e) => e.stopPropagation()}
                            disabled={
                              viewOnly ||
                              (edit && editableModule !== module.moduleName)
                            }
                          />
                        </TableCell>
                        <TableCell className="text-center">
                          <Checkbox
                            className="w-6 h-6"
                            checked={module.forms.every(
                              (form) => permissions[form.form_id]?.can_write,
                            )}
                            onCheckedChange={(checked) =>
                              handleModuleSelectAllChange(
                                module,
                                "can_write",
                                checked,
                              )
                            }
                            onClick={(e) => e.stopPropagation()}
                            disabled={
                              viewOnly ||
                              (edit && editableModule !== module.moduleName)
                            }
                          />
                        </TableCell>
                        <TableCell className="text-center">
                          <Checkbox
                            className="w-6 h-6"
                            checked={module.forms.every(
                              (form) => permissions[form.form_id]?.can_update,
                            )}
                            onCheckedChange={(checked) =>
                              handleModuleSelectAllChange(
                                module,
                                "can_update",
                                checked,
                              )
                            }
                            onClick={(e) => e.stopPropagation()}
                            disabled={
                              viewOnly ||
                              (edit && editableModule !== module.moduleName)
                            }
                          />
                        </TableCell>
                        <TableCell className="text-center">
                          <Checkbox
                            className="w-6 h-6"
                            checked={module.forms.every(
                              (form) => permissions[form.form_id]?.can_delete,
                            )}
                            onCheckedChange={(checked) =>
                              handleModuleSelectAllChange(
                                module,
                                "can_delete",
                                checked,
                              )
                            }
                            onClick={(e) => e.stopPropagation()}
                            disabled={
                              viewOnly ||
                              (edit && editableModule !== module.moduleName)
                            }
                          />
                        </TableCell>
                        {edit && !viewOnly && (
                          <TableCell className="text-center">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEdit(module.moduleName);
                              }}
                              disabled={editableModule === module.moduleName}
                            >
                              <Edit className="text-blue-500" />
                            </button>
                          </TableCell>
                        )}
                      </TableRow>

                      {expandedModules[module.moduleName] &&
                        module.forms.map((form, formIndex) => (
                          <TableRow key={formIndex} className="bg-gray-50">
                            <TableCell className="pl-8 py-2">
                              {form?.formName}
                            </TableCell>
                            <TableCell className="text-center">
                              <Checkbox
                                className="w-6 h-6"
                                checked={permissions[form.form_id]?.can_read}
                                onCheckedChange={(checked) =>
                                  handlePermissionChange(
                                    form.form_id,
                                    "can_read",
                                    checked,
                                  )
                                }
                                disabled={
                                  viewOnly ||
                                  (edit && editableModule !== module.moduleName)
                                }
                              />
                            </TableCell>
                            <TableCell className="text-center">
                              <Checkbox
                                className="w-6 h-6"
                                checked={permissions[form.form_id]?.can_write}
                                onCheckedChange={(checked) =>
                                  handlePermissionChange(
                                    form.form_id,
                                    "can_write",
                                    checked,
                                  )
                                }
                                disabled={
                                  viewOnly ||
                                  (edit && editableModule !== module.moduleName)
                                }
                              />
                            </TableCell>
                            <TableCell className="text-center">
                              <Checkbox
                                className="w-6 h-6"
                                checked={permissions[form.form_id]?.can_update}
                                onCheckedChange={(checked) =>
                                  handlePermissionChange(
                                    form.form_id,
                                    "can_update",
                                    checked,
                                  )
                                }
                                disabled={
                                  viewOnly ||
                                  (edit && editableModule !== module.moduleName)
                                }
                              />
                            </TableCell>
                            <TableCell className="text-center">
                              <Checkbox
                                className="w-6 h-6"
                                checked={permissions[form.form_id]?.can_delete}
                                onCheckedChange={(checked) =>
                                  handlePermissionChange(
                                    form.form_id,
                                    "can_delete",
                                    checked,
                                  )
                                }
                                disabled={
                                  viewOnly ||
                                  (edit && editableModule !== module.moduleName)
                                }
                              />
                            </TableCell>
                            {edit &&
                              !viewOnly &&
                              editableModule === module.moduleName && (
                                <TableCell className="text-center"></TableCell>
                              )}
                          </TableRow>
                        ))}
                    </React.Fragment>
                  ),
              )}
          </TableBody>
        </Table>
      </div>
      <div className="flex justify-end align-middle mt-4">
        {!viewOnly && (
          <Button className="" onClick={handleSave}>
            {t("save")}
          </Button>
        )}
        <Button onClick={handleCancel} className="ml-3">
          {t("cancel")}
        </Button>
      </div>
    </div>
  );
};

export default CreateRole;  