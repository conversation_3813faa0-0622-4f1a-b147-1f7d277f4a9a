import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportPropertyupdatecoladdded1724757083425 implements MigrationInterface {
    name = 'ImportPropertyupdatecoladdded1724757083425'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ADD "missing_desc" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "is_merge" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "status" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "status"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "is_merge"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "missing_desc"`);
    }

}
