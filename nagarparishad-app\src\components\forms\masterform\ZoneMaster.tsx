import React, { useContext, useRef, useState } from "react";
import WhiteContainer from "@/components/custom/WhiteContainer";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { useZoneMasterController } from "@/controller/master/ZoneMasterController";
import { GlobalContext } from "@/context/GlobalContext";
import { ZoneObject } from "../../../model/zone-master";
import { toast } from "@/components/ui/use-toast";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import ZoneMasterForm from "@/components/forms/ZoneMasterForm";
import AddNewBtn from "../../globalcomponent/AddNewForm";
import { ResponseData } from "@/model/auth/authServices";
import { MASTER } from "@/constant/config/api.config";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";
//change in file name

const ZoneMaster = () => {
  const { setOpen, toggleCollapse, setMasterComponent } =
    useContext(GlobalContext);
  const userRef = useRef(null);
  const { t } = useTranslation();
  const { deleteZone, propertyLoading } = useZoneMasterController();

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Master, FormName.Zone, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Master, FormName.Zone, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Master, FormName.Zone, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Master, FormName.Zone, Action.CanDelete);

  const dynamicValues = {
    name: t("zone.zoneLabel"),
  };

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "zone",
      header: `${t("SrNo")}`,
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: "zoneName",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("zone.zoneName")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
     cell: ({ row }: { row: any })  => (
        <div className="capitalize">{row.original.zoneName}</div>
      ),
    },
 
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: `${t("Actions")}`,
            enableHiding: false,
            cell: ({ row }: { row: any }) => (
              <div className="flex space-x-2">
                {canUpdate && (
                  <Button
                    variant="submit"
                    className="h-8 w-8 p-0 justify-center"
                    onClick={() => handleEdit(row?.original)}
                  >
                    {t("table.edit")}
                  </Button>
                )}
                {/* {CanDelete && (
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0 justify-center ml-2  !border-BlueText !text-BlueText"
                    onClick={() => handleDelete(row?.original)}
                  >
                    {t("table.delete")}
                  </Button>
                )} */}
              </div>
            ),
          },
        ]
      : []),
  ];

  function handleEdit(item: any): void {
    setOpen(true);
    setMasterComponent(
      <ZoneMasterForm btnTitle={"zone.updateBtn"} editData={item && item} />
    );
    toggleCollapse();

    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<ZoneObject | null>(null);

  function handleDelete(item: any): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }
  const handleConfirmDelete = () => {
    if (selectedItem) {
      deleteZone(selectedItem.zone_id, {
        onSuccess: (response: ResponseData) => {
          if (response?.statusCode && response?.statusCode === 200) {
            toast({
              title: t("api.formdelete", dynamicValues),
              variant: "success",
            });
          } else {
            toast({
              title: response?.message,
              variant: "destructive",
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }

    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  //List API for Zone
  const { zoneList } = useZoneMasterController();
  const MasterType: string = MASTER.ZONE;

  return (
    <>
      <div className="flex h-fit  ">
        <div
          className=" w-full mx-auto px-4 sm:px-6 lg:px-5 py-6  "
          ref={userRef && userRef}
        >
          <p className="w-full flex items-center justify-between ml-2  text-2xl font-semibold mb-2">
            {t("zone.formTitle")}
          </p>
          {CanCreate && <WhiteContainer>
            {MasterType && <AddNewBtn masterType={MASTER.ZONE} />}
          </WhiteContainer>}
          <WhiteContainer>
            <TanStackTable
              columns={columns}
              data={zoneList}
              masterType={MASTER.ZONE}
              searchKey={"searchZone"}
              searchColumn={"zoneName"}
              loader={propertyLoading ? true : false}
            />
          </WhiteContainer>
        </div>
      </div>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={""}
          onDelete={handleConfirmDelete}
        />
      )}
    </>
  );
};

export default ZoneMaster;
