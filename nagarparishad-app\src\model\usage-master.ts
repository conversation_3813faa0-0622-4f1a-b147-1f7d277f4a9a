export interface UsageObjectInterface {
  usage_id: string;
  usage_type_id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface UsageMasterApiResponse {
  statusCode?: number;
  message: string;
  data?: UsageObjectInterface[] | null;
}

export interface UsageObject {
  usageId: string;
  usage: string;
}

export interface UsageCreateObject {
  usage: string;
}

export interface UsageCreateResObject {
  statusCode: number;
  message: string;
}

export interface UsageUpdateObject {
  usage: string;
}

export interface UsageUpdateApiResponse {
  statusCode: number;
  message: string;
}
