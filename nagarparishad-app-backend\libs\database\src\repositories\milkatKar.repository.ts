import {
  paginate,
  PaginationOptions,
} from './../../../helpers/src/Pagination/paginate';
import { Brackets, Repository } from 'typeorm';
import { MilkatKarEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class MilkatKareRepository extends Repository<MilkatKarEntity> {
  constructor(
    @InjectRepository(MilkatKarEntity)
    private readonly milkatKarRepository: Repository<MilkatKarEntity>,
  ) {
    super(
      milkatKarRepository.target,
      milkatKarRepository.manager,
      milkatKarRepository.queryRunner,
    );
  }

  async getMilkatKar(milkatKar_id) {
    const queryBuilder = this.milkatKarRepository
      .createQueryBuilder('milkatKar')
      .leftJoinAndSelect('milkatKar.property', 'property')
      .select(['milkatKar', 'property.property_id']);
    if (milkatKar_id) {
      queryBuilder.andWhere(`milkatKar.milkatKar_id = :milkatKar_id`, {
        milkatKar_id: milkatKar_id,
      });
    }
    // .where('milkatKar.status = :status', { status: 'active' })

    return await queryBuilder.orderBy('property.updated_at', 'DESC').getMany();
  }

  async getMilkatKarByWard(ward: string, reassementId?: string) {
    return await this.milkatKarRepository
      .createQueryBuilder('milkatKar')
      .leftJoinAndSelect('milkatKar.property', 'property')
      .leftJoin('property.ward', 'ward')
      .leftJoin('milkatKar.reassessmentRange', 'reassessmentRange')

      .select(['milkatKar', 'property.property_id'])
      .where(
        'ward.ward_name = :ward AND milkatKar.status = :status AND reassessmentRange.reassessment_range_id = :reassementId ',
        {
          ward,
          status: 'active',
          reassementId,
        },
      )
      .orderBy('property.updated_at', 'DESC')
      .getMany();
  }

  async getMilkatKarByWardWithoutWarshikKar(ward: string, reassementId: string, financialYear: string) {
    return await this.milkatKarRepository
      .createQueryBuilder('milkatKar')
      .leftJoinAndSelect('milkatKar.property', 'property')
      .leftJoin('property.ward', 'ward')
      .leftJoin('milkatKar.reassessmentRange', 'reassessmentRange')
      .leftJoin('property.warshikKar', 'warshikKar',
        'warshikKar.financial_year = :financialYear AND warshikKar.status = :warshikKarStatus',
        { financialYear, warshikKarStatus: 'active' }
      )
      .select(['milkatKar', 'property.property_id'])
      .where(
        'ward.ward_name = :ward AND milkatKar.status = :status AND reassessmentRange.reassessment_range_id = :reassementId AND warshikKar.warshik_karId IS NULL',
        {
          ward,
          status: 'active',
          reassementId,
        },
      )
      .orderBy('property.updated_at', 'DESC')
      .getMany();
  }

  async getMilkatKarSingle() {
    return await this.milkatKarRepository
      .createQueryBuilder('milkatKar')
      .leftJoinAndSelect('milkatKar.property', 'property')
      .select(['milkatKar', 'property.property_id'])
      // .where('milkatKar.status = :status', { status: 'active' })
      .getMany();
  }

  async getCountOfMilkatKar(value: string, searchOn: string) {
    try {
      // Determine the field to search on based on the searchOn parameter
      // const searchField = searchOn === 'old_propertyNumber' ? 'property.old_propertyNumber' : 'property.propertyNumber';
      const searchField = 'property.' + searchOn;
      return await this.milkatKarRepository
        .createQueryBuilder('milkatKar')
        .leftJoinAndSelect('milkatKar.property', 'property')
        .select(['milkatKar', 'property.property_id'])
        .where(`${searchField} = :value AND milkatKar.status = 'active'`, {
          value,
        })

        .getOne();
    } catch (error) {
      throw error;
    }
  }

  async get_data_for_nanuma8(milkatKar_id: string) {
    try {
      return await this.milkatKarRepository
        .createQueryBuilder('milkatKar')
        .select([
          'milkatKar.milkatKar_id',

          'ward.ward_name',
          'property.address',
          'property.gat_no',
          'property.house_or_apartment_name',
          'property.old_propertyNumber',
          'property.propertyNumber',
          'property.city_survey_number',
          'property.note',
          'street.street_name',
          'zone.zoneName',
          'milkatKar.all_property_tax_sum', // sankalit kar (gharpatti)
          'milkatKar.other_tax_sum_tax', //other //0
          'milkatKar.total_tax', //total_tax

          'milkatKar.tax_type_1',
          'milkatKar.tax_type_2',
          'milkatKar.tax_type_3',
          'milkatKar.tax_type_4',
          'milkatKar.tax_type_5',
          'milkatKar.tax_type_6',
          'milkatKar.tax_type_7',
          'milkatKar.tax_type_8',

          'milkatKarTax.sq_ft_meter',
          'milkatKarTax.rr_rate',
          'milkatKarTax.rr_construction_rate',
          'milkatKarTax.depreciation_rate',
          'milkatKarTax.weighting',
          'milkatKarTax.capital_value',
          'milkatKarTax.tax_value',
          'milkatKarTax.tax',

          'property_usage_Details.construction_end_date',
          'property_usage_Details.are_sq_ft',
          'property_usage_Details.are_sq_meter',
          'property_usage_Details.construction_area',
          'property_usage_Details.construction_start_year',

          'ownerDetails.name',
          'ownerDetails.owner_type_id',
          'ownerDetails.property_owner_details_id',
          'owner_type.owner_type',
          'propertyType.propertyType',
        ])
        .leftJoin('milkatKar.property', 'property')
        .leftJoin('milkatKar.milkatKarTax', 'milkatKarTax')
        .leftJoin(
          'milkatKarTax.property_usage_details',
          'property_usage_Details',
        )
        .leftJoin('property_usage_Details.propertyType', 'propertyType')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.street', 'street')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('ownerDetails.owner_type', 'owner_type')

        .where('milkatKar.milkatKar_id= :milkatKar_id', { milkatKar_id })
        .orderBy('milkatKar.updated_at', 'DESC')
        .getOne();
    } catch (error) {
      throw error;
    }
  }
async getAssesmentData(value: string, searchOn: string, fy: string, reassessmentRangeId: string) {
  try {
    const queryBuilder = this.milkatKarRepository
      .createQueryBuilder('milkatKar')
      .select([
        'milkatKar.milkatKar_id',
        'ward.ward_name',
        'property.property_id',
        'property.address',
        'property.gat_no',
        'property.property_remark',
        'property.house_or_apartment_name',
        'property.old_propertyNumber',
        'property.propertyNumber',
        'property.city_survey_number',
        'property.ferfarRemark',
        'street.street_name',
        'zone.zoneName',
        'milkatKar.all_property_tax_sum',
        'milkatKar.other_tax_sum_tax',
        'milkatKar.total_tax',
        'milkatKar.tax_type_1',
        'milkatKar.tax_type_2',
        'milkatKar.tax_type_3',
        'milkatKar.tax_type_4',
        'milkatKar.tax_type_5',
        'milkatKar.tax_type_6',
        'milkatKar.tax_type_7',
        'milkatKar.tax_type_8',
        'milkatKarTax.sq_ft_meter',
        'milkatKarTax.rr_rate',
        'milkatKarTax.rr_construction_rate',
        'milkatKarTax.depreciation_rate',
        'milkatKarTax.weighting',
        'milkatKarTax.capital_value',
        'milkatKarTax.tax_value',
        'milkatKarTax.tax',
        'milkatKarTax.shasti_fee',
        'milkatKarTax.property_type_discount',

        'property_usage_Details.construction_end_date',
        'property_usage_Details.are_sq_ft',
        'property_usage_Details.are_sq_meter',
        'property_usage_Details.construction_area',
        'property_usage_Details.construction_start_year',
        'property_usage_Details.orderIndex',
        'property_usage_Details.length',
        'property_usage_Details.width',
        'property_usage_Details.createdAt',

        'floorType.floor_name',

        'ownerDetails.name',
        'ownerDetails.owner_type_id',
        'ownerDetails.property_owner_details_id',
        'ownerDetails.createdAt',
        'owner_type.owner_type',
        'propertyType.propertyType',
        'previousOwner.name as previousOwnerName',
        'previousOwner.mobile_number as previousOwnerMobile',
        'previousOwner.email_id as previousOwnerEmail',
        'previousOwner.aadhar_number as previousOwnerAadhar',
        'previousOwner.pan_card as previousOwnerPan',
                'previousOwner.recordCreatedTime as recordCreatedTime',

        'previousOwnerType.owner_type as previousOwnerType',
        'reassessmentRange.reassessment_range_id',
        'reassessmentRange.start_range',
        'reassessmentRange.end_range',
      ])
      .leftJoin('milkatKar.property', 'property')
      .leftJoin('milkatKar.milkatKarTax', 'milkatKarTax')
      .leftJoin('milkatKarTax.property_usage_details', 'property_usage_Details')
      .leftJoin('property_usage_Details.propertyType', 'propertyType')
      .leftJoin('property_usage_Details.floorType', 'floorType')
      .leftJoin('property.zone', 'zone')
      .leftJoin('property.ward', 'ward')
      .leftJoin('property.street', 'street')
      .leftJoin('property.property_owner_details', 'ownerDetails')
      .leftJoin('ownerDetails.owner_type', 'owner_type')
      .leftJoin('property.previousOwners', 'previousOwner')
      .leftJoin('previousOwner.owner_type', 'previousOwnerType')
      .leftJoin('milkatKar.reassessmentRange', 'reassessmentRange')
      .where(
        new Brackets((qb) => {
          if (searchOn === 'old_propertyNumber') {
            qb.where('property.old_propertyNumber = :value', { value });
          } else if (searchOn === 'propertyNumber') {
            qb.where('property.propertyNumber = :value', { value });
          } else if (searchOn === 'name') {
            qb.where('ownerDetails.name = :value', { value });
          } else if (searchOn === 'ward') {
            qb.where('ward.ward_name = :value', { value });
          }
        }),
      )
      .andWhere('milkatKar.status = :status', { status: 'active' })
      .andWhere('reassessmentRange.reassessment_range_id = :reassessmentRangeId', {
        reassessmentRangeId,
      });

    return await queryBuilder.orderBy('property_usage_Details.orderIndex', 'ASC').getMany();
  } catch (error) {
    throw error;
  }
}


  async getDemandDetails(value: string, searchOn: string, fy: string, reassessmentRangeId: string) {
    try {
      const queryBuilder = this.milkatKarRepository
        .createQueryBuilder('milkatKar')
        .select([
          'milkatKar.milkatKar_id',
          'ward.ward_name',
          'property.property_id',
          'property.address',
          'property.gat_no',
          'property.house_or_apartment_name',
          'property.old_propertyNumber',
          'property.propertyNumber',
          'property.city_survey_number',
          'property.ferfarRemark',
          'street.street_name',
          'zone.zoneName',
          'milkatKar.all_property_tax_sum',
          'milkatKar.other_tax_sum_tax',
          'milkatKar.total_tax',
          'milkatKar.tax_type_1',
          'milkatKar.tax_type_2',
          'milkatKar.tax_type_3',
          'milkatKar.tax_type_4',
          'milkatKar.tax_type_5',
          'milkatKar.tax_type_6',
          'milkatKar.tax_type_7',
          'milkatKar.tax_type_8',
          'milkatKarTax.sq_ft_meter',
          'milkatKarTax.rr_rate',
          'milkatKarTax.rr_construction_rate',
          'milkatKarTax.depreciation_rate',
          'milkatKarTax.weighting',
          'milkatKarTax.capital_value',
          'milkatKarTax.tax_value',
          'milkatKarTax.tax',
          'property_usage_Details.construction_end_date',
          'property_usage_Details.are_sq_ft',
          'property_usage_Details.are_sq_meter',
          'property_usage_Details.construction_area',
          'property_usage_Details.construction_start_year',
          'property_usage_Details.length',
          'property_usage_Details.width',
          'ownerDetails.name',
          'ownerDetails.owner_type_id',
          'ownerDetails.property_owner_details_id',
          'owner_type.owner_type',
          'propertyType.propertyType',
          'previousOwner.name as previousOwnerName',
          'previousOwner.mobile_number as previousOwnerMobile',
          'previousOwner.email_id as previousOwnerEmail',
          'previousOwner.aadhar_number as previousOwnerAadhar',
          'previousOwner.pan_card as previousOwnerPan',
                          'previousOwner.recordCreatedTime as recordCreatedTime',

          'previousOwnerType.owner_type as previousOwnerType',
          'reassessmentRange.reassessment_range_id',
          'reassessmentRange.start_range',
          'reassessmentRange.end_range',
        ])
        .leftJoin('milkatKar.property', 'property')
        .leftJoin('milkatKar.milkatKarTax', 'milkatKarTax')
        .leftJoin(
          'milkatKarTax.property_usage_details',
          'property_usage_Details',
        )
        .leftJoin('property_usage_Details.propertyType', 'propertyType')
        .leftJoin('property.zone', 'zone')
        .leftJoin('property.ward', 'ward')
        .leftJoin('property.street', 'street')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('ownerDetails.owner_type', 'owner_type')
        .leftJoin('property.previousOwners', 'previousOwner') // Correct relation name
        .leftJoin('previousOwner.owner_type', 'previousOwnerType')
        .leftJoin('milkatKar.reassessmentRange', 'reassessmentRange')
        .where(
          new Brackets((qb) => {
            if (searchOn === 'old_propertyNumber') {
              qb.where('property.old_propertyNumber = :value', { value });
            } else if (searchOn === 'propertyNumber') {
              qb.where('property.propertyNumber = :value', { value });
            }
          }),
        )
        .andWhere('milkatKar.status = :status', { status: 'active' });

      // Use reassessmentRangeId for filtering
      queryBuilder.andWhere('reassessmentRange.reassessment_range_id = :reassessmentRangeId', {
        reassessmentRangeId,
      });

      return await queryBuilder
        .orderBy('milkatKarTax.updated_at', 'DESC')
        .getMany();
    } catch (error) {
      throw error;
    }
  }

  async getMilkatKarByReassessmentRange(reassessmentYearId: string) {
    try {
      return await this.milkatKarRepository
        .createQueryBuilder('milkatKar')
        .leftJoinAndSelect('milkatKar.property', 'property')
        .leftJoinAndSelect('milkatKar.reassessmentRange', 'reassessmentRange')
        .select([
          'milkatKar',
          'property.property_id',
          'reassessmentRange.reassessment_range_id',
          'reassessmentRange.start_range',
          'reassessmentRange.end_range',
        ])
        .where(
          'reassessmentRange.reassessment_range_id = :reassessmentYearId AND milkatKar.status = :status',
          {
            reassessmentYearId,
            status: 'active',
          },
        )
        .orderBy('property.updated_at', 'DESC')
        .getMany();
    } catch (error) {
      throw error;
    }
  }

  async getLatestMilkatKarByPropertyId(propertyId: string,reassementId?:string) {
    try {
      return await this.milkatKarRepository
        .createQueryBuilder('milkatKar')
        .leftJoinAndSelect('milkatKar.property', 'property')
        .leftJoinAndSelect('milkatKar.reassessmentRange', 'reassessmentRange')
        .select([
          'milkatKar',
          'property.property_id',
          'reassessmentRange.reassessment_range_id',
          'reassessmentRange.start_range',
          'reassessmentRange.end_range',
        ])
        .where(
          'property.property_id = :propertyId AND milkatKar.status = :status AND reassessmentRange.reassessment_range_id = :reassementId',
          {
            propertyId,
            status: 'active',
            reassementId
          },
        )
        .orderBy('milkatKar.created_at', 'DESC')
        .getOne();
    } catch (error) {
      throw error;
    }
  }

  async getMilkatKarBySearchCriteria(value: string, searchOn: string, reassessmentRangeId: string) {
    try {
      const queryBuilder = this.milkatKarRepository
        .createQueryBuilder('milkatKar')
        .leftJoinAndSelect('milkatKar.property', 'property')
        .leftJoin('property.property_owner_details', 'ownerDetails')
        .leftJoin('milkatKar.reassessmentRange', 'reassessmentRange')
        .select(['milkatKar', 'property.property_id'])
        .where('milkatKar.status = :status', { status: 'active' })
        .andWhere('reassessmentRange.reassessment_range_id = :reassessmentRangeId', {
          reassessmentRangeId,
        });

      // Add search criteria based on searchOn parameter
      if (searchOn === 'propertyNumber') {
        queryBuilder.andWhere('property.propertyNumber = :value', { value });
      } else if (searchOn === 'old_propertyNumber') {
        queryBuilder.andWhere('property.old_propertyNumber = :value', { value });
      } else if (searchOn === 'name') {
        queryBuilder.andWhere('ownerDetails.name LIKE :value', { value: `%${value}%` });
      }

      return await queryBuilder
        .orderBy('milkatKar.updated_at', 'DESC')
        .getMany();
    } catch (error) {
      throw error;
    }
  }
}
