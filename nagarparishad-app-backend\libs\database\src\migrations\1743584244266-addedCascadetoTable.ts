import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddedCascadetoTable1743584244266 implements MigrationInterface {
  name = 'AddedCascadetoTable1743584244266';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "demand_report_data" DROP CONSTRAINT "FK_acee47cdb6ce978a5883c61e24e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "receipt" DROP CONSTRAINT "FK_daf897aedb589b656c2d3cb8f61"`,
    );
    await queryRunner.query(
      `ALTER TABLE "demand_report_data" ADD CONSTRAINT "FK_acee47cdb6ce978a5883c61e24e" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "receipt" ADD CONSTRAINT "FK_daf897aedb589b656c2d3cb8f61" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "receipt" DROP CONSTRAINT "FK_daf897aedb589b656c2d3cb8f61"`,
    );
    await queryRunner.query(
      `ALTER TABLE "demand_report_data" DROP CONSTRAINT "FK_acee47cdb6ce978a5883c61e24e"`,
    );

    await queryRunner.query(
      `ALTER TABLE "receipt" ADD CONSTRAINT "FK_daf897aedb589b656c2d3cb8f61" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "demand_report_data" ADD CONSTRAINT "FK_acee47cdb6ce978a5883c61e24e" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
