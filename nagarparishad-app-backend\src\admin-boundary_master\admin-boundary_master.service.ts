import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';

import {
  AdminBoundaryMasterDto,
  CreateAdminBoundaryMasterDto,
  UpdateAdminBoundaryMasterDto,
} from './dto/admin-boundary_master.dto';
import { AdminstrativeBoundaryMasterRepository } from 'libs/database/repositories';

@Injectable()
export class AdminBoundaryMasterService {
  constructor(
    private readonly adminBoundaryMasterRepository: AdminstrativeBoundaryMasterRepository,
  ) {}

  async create(createAdminBoundaryMasterDto: CreateAdminBoundaryMasterDto) {
    try {
      const saveData = await this.adminBoundaryMasterRepository.saveData(
        createAdminBoundaryMasterDto,
      );

      return {
        message: 'Data Saved Successfully',
        data: saveData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllData = await this.adminBoundaryMasterRepository.findAllData();

      if (!getAllData) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(adminMasterDto: AdminBoundaryMasterDto) {
    try {
      const { adminstrativeBoundary_id } = adminMasterDto;
      const checkData = await this.adminBoundaryMasterRepository.findById(
        adminstrativeBoundary_id,
      );
      if (!checkData) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Data Found Success',
        data: checkData,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    adminMasterDto: AdminBoundaryMasterDto,
    updateAdminBoundaryMasterDto: UpdateAdminBoundaryMasterDto,
  ) {
    try {
      const { adminstrativeBoundary_id } = adminMasterDto;
      const checkData = await this.adminBoundaryMasterRepository.findById(
        adminstrativeBoundary_id,
      );
      if (!checkData) {
        throw new NotFoundException('Data Not Found');
      }

      const updateData = await this.adminBoundaryMasterRepository.updateData(
        adminstrativeBoundary_id,
        updateAdminBoundaryMasterDto,
      );

      if (updateData.affected === 0) {
        throw new NotAcceptableException('Failed To Update');
      }
      return {
        message: 'Data Updated Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(adminMasterDto: AdminBoundaryMasterDto) {
    try {
      const { adminstrativeBoundary_id } = adminMasterDto;
      const checkData = await this.adminBoundaryMasterRepository.findById(
        adminstrativeBoundary_id,
      );
      if (!checkData) {
        throw new NotFoundException('Data Not Found');
      }
      const deleteData = await this.adminBoundaryMasterRepository.deleteData(
        adminstrativeBoundary_id,
      );
      if (deleteData.affected === 0) {
        throw new NotAcceptableException('Failed To Delete');
      }
      return {
        message: 'Data Deleted Success',
      };
    } catch (error) {
      throw error;
    }
  }
}
