import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedColumneInrecipt1740120709754 implements MigrationInterface {
    name = 'AddedColumneInrecipt1740120709754'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "receipt" ADD "book_receipt_number" character varying  NULL`);

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      
        await queryRunner.query(`ALTER TABLE "receipt" DROP COLUMN "book_receipt_number"`);
    }

}
