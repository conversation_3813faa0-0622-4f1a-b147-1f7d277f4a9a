import { Repository } from 'typeorm';
import { GIS_data_Entity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class GIS_data_Repository extends Repository<GIS_data_Entity> {
  constructor(
    @InjectRepository(GIS_data_Entity)
    private readonly gis_data_Repository: Repository<GIS_data_Entity>,
  ) {
    super(
        gis_data_Repository.target,
        gis_data_Repository.manager,
        gis_data_Repository.queryRunner,
    );
  }



 
}
