import { Controller, Get, Post, Put, Delete, Body, Query } from '@nestjs/common';
import { PropertyTypeClassMasterService } from './propertyTypeClassMaster.service';
import { CreatePropertyTypeClassDto } from './dto/create-property-type-class.dto';
import { UpdatePropertyTypeClassDto } from './dto/update-property-type-class.dto';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@Controller('property-type-class')
export class PropertyTypeClassMasterController {
  constructor(private readonly propertyTypeClassMasterService: PropertyTypeClassMasterService) {}

  // Create a new PropertyTypeClass
  
  @Form('Property Class Master')
  @Permissions('can_write')
  @Post('create')
  async create(@Body() createPropertyTypeClassDto: CreatePropertyTypeClassDto) {
    const data = await this.propertyTypeClassMasterService.create(createPropertyTypeClassDto);
    return {
      message: 'Property type class created successfully',
      data,
    };
  }

  // Retrieve all PropertyTypeClasses

  @Form('Property Class Master')
  @Permissions('can_read')
  @Get()
  async findAll() {
    const data = await this.propertyTypeClassMasterService.findAll();
    return {
      message: 'All property type classes retrieved successfully',
      data,
    };
  }

  // Retrieve a single PropertyTypeClass by ID using a query parameter

  @Form('Property Class Master')
  @Permissions('can_read')
  @Get('findOne')
  async findOne(@Query('id') id: string) {
    const data = await this.propertyTypeClassMasterService.findOne(id);
    return {
      message: 'Property type class retrieved successfully',
      data,
    };
  }

  // Update a PropertyTypeClass by ID using a query parameter
  
  @Form('Property Class Master')
  @Permissions('can_update')
  @Put('update')
  async update(@Query('id') id: string, @Body() updatePropertyTypeClassDto: UpdatePropertyTypeClassDto) {
    const data = await this.propertyTypeClassMasterService.update(id, updatePropertyTypeClassDto);
    return {
      message: 'Property type class updated successfully',
      data,
    };
  }

  // Delete a PropertyTypeClass by ID using a query parameter

  @Form('Property Class Master')
  @Permissions('can_delete')
  @Delete('delete')
  async delete(@Query('id') id: string) {
    await this.propertyTypeClassMasterService.delete(id);
    return {
      message: 'Property type class deleted successfully',
    };
  }
}
