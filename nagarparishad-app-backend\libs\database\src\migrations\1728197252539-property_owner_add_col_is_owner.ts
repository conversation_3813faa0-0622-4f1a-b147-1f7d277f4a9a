import { MigrationInterface, QueryRunner } from "typeorm";

export class PropertyOwnerAddColIsOwner1728197252539 implements MigrationInterface {
    name = 'PropertyOwnerAddColIsOwner1728197252539'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property_owner_details" ADD "is_owner" boolean`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP COLUMN "is_owner"`);
    }

}
