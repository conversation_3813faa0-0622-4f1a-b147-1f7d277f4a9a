import { MigrationInterface, QueryRunner } from "typeorm";

export class AlterBookNumberEntity1744022409071 implements MigrationInterface {
    name = 'AlterBookNumberEntity1744022409071'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "book_number_master" DROP CONSTRAINT "FK_64e56e24e0ad3a65aa04d0131d0"`);
        await queryRunner.query(`ALTER TABLE "book_number_master" RENAME COLUMN "collector_id" TO "collectorId"`);
        // await queryRunner.query(`CREATE TABLE "deleted_property_usage_details" ("deleted_property_usage_details_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "property_id" uuid NOT NULL, "property_usage_details_id" uuid NOT NULL, CONSTRAINT "REL_3704e9416b2d21bee9eeed76e8" UNIQUE ("property_usage_details_id"), CONSTRAINT "PK_676fb1fb09519f2a427f80ec915" PRIMARY KEY ("deleted_property_usage_details_id"))`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c" UNIQUE ("billNo")`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP DEFAULT`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(50) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" DROP NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "property_owner_details" ADD CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_df5e676c36082c5c46778bc844c" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "milkatKarTax" ADD CONSTRAINT "FK_48589f448b7fc0e33f3ca4cb356" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "milkatKar" ADD CONSTRAINT "FK_3e1f3fe6a693b75466f63a957a4" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "warshik_kar" ADD CONSTRAINT "FK_dc23330aada107590eed796d7c0" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "FK_df738d1108e8291026728e28c55" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "book_number_master" ADD CONSTRAINT "FK_f931b5830ca900381aba781a5a6" FOREIGN KEY ("collectorId") REFERENCES "collector_master"("collectorId") ON DELETE SET NULL ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "demand_report_data" ADD CONSTRAINT "FK_acee47cdb6ce978a5883c61e24e" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "receipt" ADD CONSTRAINT "FK_daf897aedb589b656c2d3cb8f61" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "paid_data" ADD CONSTRAINT "FK_8ca6a648c2ccf657fa24519b077" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "payment_info" ADD CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "common_fields_of_property" ADD CONSTRAINT "FK_a4dee4e94aee71ce01ff0919c39" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "warshikKarTax" ADD CONSTRAINT "FK_782f8b60c2e42b03b9dc97ca1de" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "deleted_property_usage_details" ADD CONSTRAINT "FK_537835d3810d522523a6f9a3e5e" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "deleted_property_usage_details" ADD CONSTRAINT "FK_3704e9416b2d21bee9eeed76e8d" FOREIGN KEY ("property_usage_details_id") REFERENCES "property_usage_details"("property_usage_details_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "deleted_property_usage_details" DROP CONSTRAINT "FK_3704e9416b2d21bee9eeed76e8d"`);
        // await queryRunner.query(`ALTER TABLE "deleted_property_usage_details" DROP CONSTRAINT "FK_537835d3810d522523a6f9a3e5e"`);
        // await queryRunner.query(`ALTER TABLE "warshikKarTax" DROP CONSTRAINT "FK_782f8b60c2e42b03b9dc97ca1de"`);
        // await queryRunner.query(`ALTER TABLE "common_fields_of_property" DROP CONSTRAINT "FK_a4dee4e94aee71ce01ff0919c39"`);
        // await queryRunner.query(`ALTER TABLE "payment_info" DROP CONSTRAINT "FK_0a996a3cd8b7f035b815d3d84a9"`);
        // await queryRunner.query(`ALTER TABLE "paid_data" DROP CONSTRAINT "FK_8ca6a648c2ccf657fa24519b077"`);
        // await queryRunner.query(`ALTER TABLE "receipt" DROP CONSTRAINT "FK_daf897aedb589b656c2d3cb8f61"`);
        // await queryRunner.query(`ALTER TABLE "demand_report_data" DROP CONSTRAINT "FK_acee47cdb6ce978a5883c61e24e"`);
        await queryRunner.query(`ALTER TABLE "book_number_master" DROP CONSTRAINT "FK_f931b5830ca900381aba781a5a6"`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "FK_df738d1108e8291026728e28c55"`);
        // await queryRunner.query(`ALTER TABLE "warshik_kar" DROP CONSTRAINT "FK_dc23330aada107590eed796d7c0"`);
        // await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0"`);
        // await queryRunner.query(`ALTER TABLE "milkatKar" DROP CONSTRAINT "FK_3e1f3fe6a693b75466f63a957a4"`);
        // await queryRunner.query(`ALTER TABLE "milkatKarTax" DROP CONSTRAINT "FK_48589f448b7fc0e33f3ca4cb356"`);
        // await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_df5e676c36082c5c46778bc844c"`);
        // await queryRunner.query(`ALTER TABLE "property_owner_details" DROP CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(100) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET DEFAULT now()`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c"`);
        // await queryRunner.query(`DROP TABLE "deleted_property_usage_details"`);
        await queryRunner.query(`ALTER TABLE "book_number_master" RENAME COLUMN "collectorId" TO "collector_id"`);
        await queryRunner.query(`ALTER TABLE "book_number_master" ADD CONSTRAINT "FK_64e56e24e0ad3a65aa04d0131d0" FOREIGN KEY ("collector_id") REFERENCES "collector_master"("collectorId") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

}
