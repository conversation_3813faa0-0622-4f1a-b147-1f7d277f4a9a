import Api from "../../services/ApiServices";
import { useQuery } from "@tanstack/react-query";

// Global search function
const performGlobalSearch = async (
  queryParam: string,
  page?: number,
  limit?: number
) => {
  try {
    const response = await Api.globalSearch(queryParam, page, limit);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Advanced property search function
const performAdvancedPropertySearch = async (
  name: string,
  zone: string,
  ward: string,
  page: number,
  limit: number
) => {
  const response = await Api.advancedPropertySearch(name, zone, ward, page, limit);
  if (response.status) {
    return response.data;
  } else {
    throw new Error(response.data);
  }
};

export const useSearchController = () => {
  // Function to perform global search (used manually, not as a query)
  const globalSearch = async (
    queryParam: string,
    page?: number,
    limit?: number
  ) => {
    return await performGlobalSearch(queryParam, page, limit);
  };

  // Function to perform advanced property search (used manually, not as a query)
  const advancedPropertySearch = async (
    name: string,
    zone: string,
    ward: string,
    page: number,
    limit: number
  ) => {
    return await performAdvancedPropertySearch(name, zone, ward, page, limit);
  };

  return {
    globalSearch,
    advancedPropertySearch,
  };
};
