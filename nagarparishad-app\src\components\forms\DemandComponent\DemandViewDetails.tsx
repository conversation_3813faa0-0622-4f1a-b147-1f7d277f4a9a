import React, { useEffect, useRef, useState } from "react";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { ColumnDef } from "@tanstack/react-table";
import { FormControl } from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useZoneMasterController } from "@/controller/master/ZoneMasterController";
import { useTaxController } from "@/controller/tax/TaxListController";
import { TaxUserByFYInterface } from "@/model/tax/taxUserDetailsByFYInterfaces";
import { useTaxCalculateController } from "@/controller/tax/TaxCalculateController";
import axios from "axios";
import WhiteContainer from "@/components/custom/WhiteContainer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useLocation, useNavigate } from "react-router-dom";
import { Printer } from "lucide-react";
import { Input } from "@/components/ui/input";
import { FileText } from 'lucide-react';
import { Checkbox } from "@/components/ui/checkbox";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { Loader } from "@/components/globalcomponent/Loader";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import AsyncSelect from "@/components/ui/react-select";



const DemandViewDetails = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const yearRange = location.state?.financial_year?.financial_year_range;
  const [printloading, setPrintLoading] = useState(false);
  const [printNamunaloading, setPrintNamunaLoading] = useState(false);
  const [oldPropertyNumber, setOldPropertyNumber] = useState('');
  const [propertyNumber, setPropertyNumber] = useState('');
  const [wardName, setWardName] = useState(null);
  const { wardList } = useWardMasterController();
  const { getBillsList } = useTaxCalculateController();


  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchOn,setsearchOn]= useState("")

  const [taxData, setTaxData] = useState<any[]>([]); // Define state for tax data


  const financial_year: string = location?.state
  
  
  const wardOptions: any = wardList?.map((ward: any) => ({
    value: ward.ward_id,
    label: ward.ward_name,
  }));
  const loadWardOptions = (
    inputValue: string,
    callback: (options: any[]) => void
  ) => {
    setTimeout(() => {
      callback(
        wardOptions.filter((option: any) =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      );
    }, 1000);
  };
console.log("financial_year",financial_year)
const bill = async () => {
  // Reset state
  setOpen(false);
   const searchParams: any = {}; // Use the defined type for searchParams

    if (wardName) {
        searchParams.ward = wardName?.label;
    }

    if (propertyNumber) {
        searchParams.searchOn = "propertyNumber";
        searchParams.value = propertyNumber;
    } else if (oldPropertyNumber) {
        searchParams.searchOn = "old_propertyNumber";
        searchParams.value = oldPropertyNumber;
    } else {
        searchParams.searchOn = "all";
    }

  const urlSearchParams = new URLSearchParams(searchParams);
  setLoading(true);
  
  try {
    // Your existing API call logic
    // ...
        const response = await getBillsList(urlSearchParams, financial_year);
        console.log("response===>", response);

    // Only set open to true if data is successfully fetched
    if (response.statusCode===200 && response?.data?.data) {
      setTaxData(response?.data?.data);
      setOpen(true);
    } else {
      toast({
        title: t("No data found"),
        variant: "destructive",
      });
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    toast({
      title: t("Error fetching data"),
      variant: "destructive",
    });
  } finally {
    setLoading(false);
  }
};

   
    useEffect(() => {
      bill()
    },[])
 
  console.log("Taxdata>>>>",taxData)
  const handleOldNumberChange = (e) => {
    setOldPropertyNumber(e.target.value.trim().toUpperCase());
    setPropertyNumber(() => ""); 
    setSelectedValue(() => "");
    setWardName(null)
 
    setsearchOn("oldPropertyNumber");
  };

  const handlePropertyNumberChange = (e) => {
    setPropertyNumber(e.target.value.trim().toUpperCase());
    setOldPropertyNumber(() => ""); 
    setSelectedValue(() => "");
    setWardName(null)
    setsearchOn("propertyNumber");
  };

  const handleWardChange = (selectedOption) => {
    setWardName(selectedOption);
    setPropertyNumber(() => ""); // Clear the other field
    setOldPropertyNumber(() => ""); 


  }
  
  const handlePrintTax = async (item) => {
    setPrintLoading(true);
    console.log("item0000", item);
  
    const { billNo } = item; // Assuming item contains the billNo
  
    try {
      const response = await axios.get(
        `${apiBaseUrl}/v1/billing/get-bill`,
        {
          responseType: "blob",
          params: { billNo } // Include billNo as a query parameter
        }
      );
  
      const pdfBlob = new Blob([response.data], { type: "application/pdf" });
      const pdfUrl = URL.createObjectURL(pdfBlob);
      setPrintLoading(false);
  
      window.open(pdfUrl);
    } catch (error) {
      setPrintLoading(false);
      console.error("Error fetching the PDF:", error);
    }
  };
  


  
  

 
 
 
  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "srNo",
      header: `${t("SrNo")}`,
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.index + 1}</div>
      ),
    },
    {
      accessorKey: "billNo",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.billNo")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original?.billNo}</div>
      ),
    },
    {
      accessorKey: "propertyNumber",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.propertyNo")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original?.property?.propertyNumber}</div>
      ),
    },
    {
      accessorKey: "ownerName",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.ownerName")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => {
        const owners = row.original?.property?.property_owner_details;
        const primaryOwners = owners?.filter(
          (owner) => owner?.owner_type?.owner_type 
        );
  
        return (
          <div className="capitalize relative">
            {primaryOwners?.length > 0 ? (
              <>
                {primaryOwners.slice(0, 1).map((owner, index) => (
                  <p key={owner?.property_owner_details_id} className="text-nowrap">
                    {owner.name}
                    {index < primaryOwners.length - 1 && ", "}
                  </p>
                ))}
                {primaryOwners.length > 1 && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="min-w-4 h-5 border rounded-full ml-2 px-[7px] py-[11px] text-[12px] bg-[#f0eded]"
                      >
                        +{primaryOwners.length - 1}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-fit px-3 py-1">
                      {primaryOwners.slice(1).map((owner) => (
                        <DropdownMenuItem key={owner.property_owner_details_id}>
                          {owner.name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </>
            ) : (
              "--"
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "zone",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.zone")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original?.property?.zone?.zoneName}</div>
      ),
    },
    {
      accessorKey: "street",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.street")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original?.property?.street?.street_name}</div>
      ),
    },
    {
      accessorKey: "totalAmount",
      header: () => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
        >
          {t("taxView.totalAmount")}
        </Button>
      ),
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">₹{row.original?.total_tax}</div>
      ),
    },
    // {
    //   accessorKey: "actions",
    //   header: () => `${t("Actions")}`,
    //   enableHiding: false,
    //   cell: ({ row }: { row: any }) => (
    //     <div className="text-nowrap">
    //       <Button
    //         key={row.index}
    //         className={`bg-LightBlue text-BlueText border border-Secondary hover:bg-LightBlue px-4 py-1 min-w-fit h-fit ${
    //           printloading ? "cursor-not-allowed opacity-50" : ""
    //         }`}
    //         onClick={() => handlePrintTax(row?.original)}
    //         disabled={printloading}
    //       >
    //         <Printer className={`w-4 ${printloading ? "animate-bounce" : ""}`} />
    //       </Button>
    //     </div>
    //   ),
    // },
    // {
    //   accessorKey: "status",
    //   header: () => (
    //     <Button
    //       className="px-0 justify-start text-base font-semibold"
    //       variant="ghost"
    //     >
    //       स्थिती
    //     </Button>
    //   ),
    //   cell: ({ row }: { row: any }) => (
    //     <div className="capitalize">Failed</div>
    //   ),
    // },
  ];
  const [selectedValue, setSelectedValue] = useState(""); // Initialize state for selected value

  const handleValueChange = (value: any) => {
    setSelectedValue(value); // Update state when value changes
    setPropertyNumber(() => ""); // Clear the property number field
    setOldPropertyNumber(() => ""); // Clear the old property number field
    setsearchOn("all"); // Adjust the searchOn value as per your logic for ward
  };
  // const { taxData } = useTaxController();
  const navigate = useNavigate();
  const handleNavigate = () => {
    navigate("/property/property-demand");
  };

  
  return (
    <div className=" flex justify-center h-full overflow-x-auto w-full  ">
      <div className=" w-full h-fit sm:p-6 p-3">
        
        <p className="w-full flex items-center justify-between ml-2  text-[22px] font-semibold mb-2">
      {t("taxDemand.taxDemand")}({financial_year})

        </p>
        <WhiteContainer>
          <div>
           
            <div className="mb-5">
              <div className="grid md:grid-cols-4 gap-x-3 gap-y-3">
               

                <div className="text-nowrap w-full">            
                  {" "}
                  <Label>{t("propertyView.ward")}

                  </Label>
                  <AsyncSelect
                placeholder={t("propertyLocationDetailsForm.ward")}
                className="mt-1 h-[2.5rem]"
                loadOptions={loadWardOptions}
                defaultOptions={wardOptions}
                colourOptions={wardOptions}
                value={wardName} // Bind to wardName state
                onChange={handleWardChange} // 
                onKeyDown={(e) => e.key === 'Enter' && bill()}

              />
                </div>

                <div className="grid-cols-subgrid">
                <Label>{t("property.propertyNumberColumn")}</Label>

                  <Input
                    className="mt-1 block w-full"
                    type="text"
                    onChange={handlePropertyNumberChange}
                    transliterate={false}
                    value={propertyNumber}
                    placeholder={t("मालमत्ता क्रमांक")}
                    onKeyDown={(e) => e.key === 'Enter' && bill()}

                  />
                </div>
                <div className="grid-cols-subgrid">
                <Label>{t("property.oldPropertyNumber")}</Label>

                  <Input
                    className="mt-1 block w-full"
                    transliterate={false}
                    onChange={handleOldNumberChange}
                    value={oldPropertyNumber}
                    placeholder={t("property.oldPropertyNumber")}
                    onKeyDown={(e) => e.key === 'Enter' && bill()}

                  />
                </div>

                <div className=" grid-cols-subgrid flex items-end">
                  <Button
                    variant="submit"
                    className="mt-1"
                  onClick={bill}
>
                    शोधा
                  </Button>
                </div>
              </div>
            </div>
            <p className="text-xs italic font-semibold mb-0 text-[#3c3c3c]"> {t('selectAnyFieldNote')} </p>
          </div>
        </WhiteContainer>

        {" "}

        {loading && (
          <div className="flex justify-center items-center my-4">
            <Loader />

          </div>
        )}

        {open && !loading && (
        <WhiteContainer>
          {" "}
          {/* Add a relative positioned container */}
          <div className="text-nowrap flex justify-end">            {" "}
            {/* Use absolute positioning */}

            {/* <Button className="ml-4 mt-1">
              <Printer
                className={`w-4 mr-2  ${printloading ? "animate-bounce" : ""}`}
              />
              Print All</Button> */}

          </div>
          <div className="">
            {" "}
            {/* Add margin-top to make space for the absolute positioned element */}
       
            <TanStackTable
              columns={columns}
              data={taxData || []}
              searchKey={undefined}
              searchColumn={"ownerName"}
            />
          </div>
        </WhiteContainer>
        )}
      </div>
    </div>
  );
};

export default DemandViewDetails;
