import { ZoneObjectInterface } from "@/model/zone-master";
import Api from "../../services/ApiServices";
import {
  useQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import TaxListApi from "@/services/TaxServices";

interface ZoneData {
  zoneName: string; // Only zoneName now
}

const fetchReAssesment = async () => {
    return new Promise((resolve, reject) => {
      TaxListApi.getReAssesmentMaster((response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      });
    });
  };
  

const createReAssesment = async (DepreciationRateData: any) => {
  return new Promise((resolve, reject) => {
    TaxListApi.createReAssesment(DepreciationRateData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};




export const useReAssesmentController = () => {
  const queryClient = useQueryClient();

  const { data: DepreciationRateData, isLoading: ReAssesmentLoading } = useQuery({
    queryKey: ["ReAssesmentmaster"],
    queryFn: fetchReAssesment,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
  });

  const createReAssesmentMutation = useMutation({
    mutationFn: createReAssesment,
    onMutate: async (newDepreciationRates) => {
      await queryClient.cancelQueries({ queryKey: ["ReAssesmentmaster"] });
      const previousDepreciationRates = queryClient.getQueryData(["ReAssesmentmaster"]);

      queryClient.setQueryData(["ReAssesmentmaster"], (old: any) => {
             console.log(" construtionold",old)
        const updatedData = [newDepreciationRates, ...old.data];
        console.log("updatedData",updatedData)
        return updatedData;
      });

      return { previousDepreciationRates };
    },
    onError: (err, newDepreciationRates, context) => {
      queryClient.setQueryData(["ReAssesmentmaster"], context.previousDepreciationRates);
      console.error("Error creating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["ReAssesmentmaster"] });
    },
  });

 
  return {
    ReAssesmentList: DepreciationRateData?.data || [],
    ReAssesmentLoading,
    createReAssesment: createReAssesmentMutation.mutate,
  };

  
};


