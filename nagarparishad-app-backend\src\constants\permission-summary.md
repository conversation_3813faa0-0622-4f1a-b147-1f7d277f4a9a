# Permission and Forms Summary

## Overview
This document provides a comprehensive overview of all permissions, forms, and modules used in the Shirol Nagarparishad application.

## Statistics
- **Total Modules**: 6
- **Total Forms**: 47
- **Total Actions**: 4

## Module Breakdown

### 1. Setting Module (9 Forms)
- Deprecation Rate
- RR Construction Rate
- RR Rate
- Tax Rate
- Weighting Rate
- Ghan Kachra Rate
- Book Master
- Financial Year Master
- Reassessment Master

### 2. Master Module (18 Forms)
- Zone
- Ward
- Road
- PropertyType
- PropertyType Department
- Floor
- Usage
- Usage Sub
- Location Master
- Street Master
- Election Boundry Master
- Area Master
- Property Sub Type Master
- Property Class Master
- Construction Class Master
- Collector Master
- Owner Type Master
- Admin Boundary Master

### 3. Property Module (1 Form)
- Property

### 4. Payment Module (2 Forms)
- Payment Bill
- Payment Logs

### 5. Register Module (2 Forms)
- Assessment Report
- Register Report

### 6. Role and User Management Module (3 Forms)
- Role
- User
- Update Roles

### Additional Forms (12 Forms)
- Tax Creation
- Namuna 10
- Dashboard
- Import Export
- Tax Calculation
- Bill Generation
- Property Division
- Penalty Management
- Cron Jobs
- Global Search
- Logs
- Backup Migration

## Actions/Permissions
1. **can_read** - Read permission
2. **can_write** - Create permission
3. **can_update** - Update permission
4. **can_delete** - Delete permission

## Usage in Controllers
The following controllers use form permissions:

### Tax Masters
- `MasterDepreciationController` - Uses "Deprecation Rate" form
- `Master_tax_valueController` - Uses "Tax Rate" form
- `MasterRRRateController` - Uses "RR Rate" form
- `Master_rr_construction_rateController` - Uses "RR Construction Rate" form
- `Master_weightingController` - Uses "Weighting Rate" form
- `MasterGhanKachraRateController` - Uses "Ghan Kachra Rate" form

### Master Controllers
- `ZoneMasterController` - Uses "Zone" form
- `WardMasterController` - Uses "Ward" form
- `UsageMasterController` - Uses "Usage" form
- `AreaMasterController` - Uses "Area Master" form
- `LocationMasterController` - Uses "Location Master" form
- `StreetMasterController` - Uses "Street Master" form
- `ElectionBoundaryMasterController` - Uses "Election Boundry Master" form
- `PropertyTypeMasterController` - Uses "PropertyType" form
- `PropertySubTypeMasterController` - Uses "Property Sub Type Master" form
- `ConstructionClassController` - Uses "Construction Class Master" form
- `FloorMasterController` - Uses "Floor" form
- `CollectorMasterController` - Uses "Collector Master" form

### Other Controllers
- `BillingController` - Uses "Payment Bill" form
- `PropertyMasterController` - Uses "Property" form
- `RoleMasterController` - Uses "Role" form
- `UserController` - Uses "User" form

## Permission Guard Implementation
The application uses:
- `@Form()` decorator to specify which form the endpoint belongs to
- `@Permissions()` decorator to specify required permissions
- `RolesGuard` to enforce permission checks
- `AccessTokenGuard` for authentication

## Files Location
- **Backend Constants**: `nagarparishad-app-backend/src/constants/permission.constants.ts`
- **Frontend Constants**: `nagarparishad-app/src/constant/enums/permissionEnum.ts`
- **Permission Service**: `nagarparishad-app-backend/src/permission/permission.service.ts`
- **Permission Guard**: `nagarparishad-app-backend/libs/helpers/src/role-based-access/permission.guard.ts`
- **Permission Decorators**: `nagarparishad-app-backend/libs/helpers/src/role-based-access/permission.decorator.ts`

## Notes
- Some controllers have permissions commented out (like DashboardController)
- The permission system is currently bypassed in some guards (return true statements)
- All forms support the standard CRUD operations (Create, Read, Update, Delete)
- The system supports role-based access control with multiple roles (Admin, Supervisor, etc.)
