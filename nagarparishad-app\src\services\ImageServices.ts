// Image handling utilities
class ImageServices {
  // View image in new tab by converting to blob
  static viewImage = async (imageSrc: string): Promise<void> => {
    try {
      const response = await fetch(imageSrc);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      window.open(url, "_blank");
    } catch (error) {
      console.error("Error viewing image:", error);
      throw error;
    }
  };

  // Download image
  static downloadImage = async (imageSrc: string, filename?: string): Promise<void> => {
    try {
      const response = await fetch(imageSrc);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = filename || 'image';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading image:", error);
      throw error;
    }
  };
}

export default ImageServices;
