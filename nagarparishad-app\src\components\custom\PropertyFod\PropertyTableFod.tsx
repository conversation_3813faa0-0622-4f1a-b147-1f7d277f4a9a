import React, { useState } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import WhiteContainer from "../WhiteContainer";
import { Trash2, Edit, Check, X, UploadCloudIcon } from "lucide-react";
import cloneDeep from "lodash/cloneDeep";
import PropertyApi from "@/services/PropertyServices";
import DocumentUpload from "@/components/globalcomponent/DocumentUpload";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import WebcamCapture from "@/components/globalcomponent/WebcamCapture";
import { Textarea } from "@/components/ui/textarea";
import { t } from "i18next";
import {
  useForm,
  FormProvider,
  useFieldArray,
  useForm<PERSON>ontext,
} from "react-hook-form";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast"; // Import your custom toast hook
import AddOwnerForm from "./OwnerForm"; // Import the AddOwnerForm component
import { useOwnerTypeController } from "@/controller/master/OwnerTypeController";

const PropertyTable = ({
  actualPropertyId,
  validNumbers,
  ownerDetails,
  propertyUsageDetails,
  deletedOwnerIds, // Receive deletedOwnerIds as a prop
}) => {
  const methods = useForm();
  const { control, handleSubmit } = methods;
  const { toast } = useToast(); // Initialize the toast hook

  const [currentSelectedNumberForOwner, setCurrentSelectedNumberForOwner] =
    useState(null);
  const [currentSelectedOwner, setCurrentSelectedOwner] = useState(null);
  const [ownerMap, setOwnerMap] = useState({}); // { propertyNumber: [owners] }
  const [ownerOptions, setOwnerOptions] = useState(
    Array.isArray(ownerDetails)
      ? ownerDetails.map((owner) => ({
          label: owner.name,
          value: owner.property_owner_details_id,
        }))
      : []
  );

  const [currentSelectedNumberForUsage, setCurrentSelectedNumberForUsage] =
    useState(null);
  const [currentSelectedUsage, setCurrentSelectedUsage] = useState(null);

  const [usageMap, setUsageMap] = useState({}); // { propertyNumber: [usages] }
  const [usageOptions, setUsageOptions] = useState(
    Array.isArray(propertyUsageDetails)
      ? propertyUsageDetails.map((usage) => ({
          label: `${usage.propertyType?.propertyType || ""} - ${usage.are_sq_ft} sq ft`,
          value: usage.property_usage_details_id,
          isNew: false,
          ...usage,
        }))
      : []
  );

  const [editingUsage, setEditingUsage] = useState(null);
  const [tempUsage, setTempUsage] = useState(null);
  const [loading, setLoading] = useState(false); // Loading state
  const [isAddOwnerFormOpen, setIsAddOwnerFormOpen] = useState(false); // State for AddOwnerForm popup
  const [capturedPhotos, setCapturedPhotos] = useState<string[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState([]);

  const handleCapture = (photo: string) => {
    setCapturedPhotos((prev) => [...prev, photo]);
  };




    const handleUploadComplete = (fileDetails: any) => {
      console.log("fileDetailsfileDetails",fileDetails)
      setUploadedFiles((prevFiles) => {
        const newFiles = [...prevFiles];
        fileDetails.forEach((newFile) => {
          if (!newFiles.some((existingFile) => existingFile.name === newFile.name)) {
            newFiles.push(newFile);
          }
        });
        return newFiles;
      });
          console.log("fileDetailsfileDetails",uploadedFiles)
  
    };

  const handleAddOwner = () => {
    if (!currentSelectedNumberForOwner || !currentSelectedOwner) return;

    setOwnerMap((prev) => {
      const existing = prev[currentSelectedNumberForOwner] || [];

      if (
        existing.find(
          (o) => o.property_owner_details_id === currentSelectedOwner
        )
      ) {
        return prev; // Avoid duplicate
      }

      const ownerData = ownerDetails.find(
        (o) => o.property_owner_details_id === currentSelectedOwner
      );

      return {
        ...prev,
        [currentSelectedNumberForOwner]: [...existing, ownerData],
      };
    });

    setOwnerOptions((prev) =>
      prev.filter((o) => o.value !== currentSelectedOwner)
    );

    setCurrentSelectedOwner(null); // Reset owner selection
    setCurrentSelectedNumberForOwner(null); // Reset property number selection
  };

  const handleDeleteOwner = (propertyNumber, ownerId) => {
    setOwnerMap((prev) => {
      const updated = { ...prev };
      const removedOwner = updated[propertyNumber].find(
        (o) => o.property_owner_details_id === ownerId
      );

      updated[propertyNumber] = updated[propertyNumber].filter(
        (o) => o.property_owner_details_id !== ownerId
      );

      // Re-add to dropdown
      if (removedOwner) {
        setOwnerOptions((prev) => [
          ...prev,
          {
            label: removedOwner.name,
            value: removedOwner.property_owner_details_id,
          },
        ]);
      }

      return updated;
    });
  };

  const handleAddUsage = () => {
    if (!currentSelectedNumberForUsage || !currentSelectedUsage) return;

    setUsageMap((prev) => {
      const existing = prev[currentSelectedNumberForUsage] || [];

      if (
        existing.find(
          (u) => u.property_usage_details_id === currentSelectedUsage
        )
      ) {
        return prev;
      }

      const usageData = cloneDeep(
        usageOptions.find(
          (u) => u.property_usage_details_id === currentSelectedUsage
        )
      );

      return {
        ...prev,
        [currentSelectedNumberForUsage]: [...existing, usageData],
      };
    });

    setUsageOptions((prev) =>
      prev.filter((u) => u.value !== currentSelectedUsage)
    );

    setCurrentSelectedUsage(null); // Reset usage selection
    setCurrentSelectedNumberForUsage(null); // Reset property number selection
  };

  const handleDeleteUsage = (propertyNumber, usageId) => {
    setUsageMap((prev) => {
      const updated = { ...prev };
      const removedUsage = updated[propertyNumber].find(
        (u) => u.property_usage_details_id === usageId
      );

      updated[propertyNumber] = updated[propertyNumber].filter(
        (u) => u.property_usage_details_id !== usageId
      );

      // Re-add to dropdown with updated area
      if (removedUsage) {
        setUsageOptions((prev) => [
          ...prev,
          {
            label: `${removedUsage?.propertyType?.propertyType || ""} - ${removedUsage.are_sq_ft} sq ft`,
            value: removedUsage.property_usage_details_id,
            ...removedUsage,
          },
        ]);
      }

      return updated;
    });
  };

  const handleEditUsage = (propertyNumber, usageId) => {
    const usageToEdit = usageMap[propertyNumber].find(
      (u) => u.property_usage_details_id === usageId
    );
    setTempUsage(cloneDeep(usageToEdit));

    setEditingUsage({ propertyNumber, usageId });
  };
  const handleConfirmEdit = (propertyNumber, usageId) => {
    const BeforeEditUsageMap = cloneDeep(
      usageMap[propertyNumber]?.find(
        (u) => u.property_usage_details_id === usageId
      )
    );

    setUsageMap((prev) => {
      const updated = { ...prev };
      const usageToEdit = updated[propertyNumber].find(
        (u) => u.property_usage_details_id === usageId
      );

      if (usageToEdit) {
        const originalArea = parseFloat(BeforeEditUsageMap?.are_sq_ft);
        const newLength = parseFloat(tempUsage.length);
        const newWidth = parseFloat(tempUsage.width);
        const newArea = parseFloat(tempUsage.are_sq_ft);

        const remainingArea = (originalArea - newArea).toFixed(2);

        if (parseFloat(remainingArea) > 0) {
          usageToEdit.length = newLength;
          usageToEdit.width = newWidth;
          usageToEdit.are_sq_ft = newArea;
          const newUsage = {
            ...BeforeEditUsageMap,
            property_usage_details_id: randomUUID(),
            length: BeforeEditUsageMap.length - newLength,
            width: BeforeEditUsageMap.width - newWidth,
            are_sq_ft: remainingArea,
            are_sq_meter: (parseFloat(remainingArea) * 0.092903).toFixed(2),
          };

          setUsageOptions((prevOptions) => [
            ...prevOptions,
            {
              ...newUsage,
              label: `${newUsage?.propertyType?.propertyType || ""} - ${newUsage.are_sq_ft} sq ft`,
              value: newUsage.property_usage_details_id,
              isNew: true,
            },
          ]);
        }
      }

      return updated;
    });

    setEditingUsage(null);
    setTempUsage(null);
  };

  const handleDiscardEdit = () => {
    setEditingUsage(null);
    setTempUsage(null);
  };

  const handleInputChange = (field, value) => {
    setTempUsage((prev) => {
      const newValue = parseFloat(value);
      const originalArea = parseFloat(
        usageMap[editingUsage.propertyNumber].find(
          (u) => u.property_usage_details_id === editingUsage.usageId
        )?.are_sq_ft || 0
      );

      if (field === "length" || field === "width") {
        const newArea = parseFloat(
          (
            newValue * parseFloat(prev[field === "length" ? "width" : "length"])
          ).toFixed(2)
        );
        return { ...prev, [field]: value, are_sq_ft: newArea };
      } else if (field === "are_sq_ft") {
        return { ...prev, [field]: value, length: null, width: null };
      }

      return prev;
    });
  };

  const handleSunmitProperties = async () => {
    setLoading(true); // Set loading to true
    const propertiesData = validNumbers.map((propertyId) => ({
      propertyNumber: propertyId,
      owners: ownerMap[propertyId] || [],
      usages: usageMap[propertyId] || [],
    }));

    const properties = {
      actualPropertyId: actualPropertyId,
      properties: propertiesData,
      deletedOwnerIds: deletedOwnerIds, // Include deletedOwnerIds in the submission payload,
      photos: capturedPhotos, // Photos for all properties
      documents: uploadedFiles, // Documents for all properties
    };

    try {
      const response = await PropertyApi.divideProperties(properties);
      console.log("Submitted Properties:", response);

      // Show success toast
      toast({
        title: t("Properties submitted successfully!"),
        variant: "success",
      });

      // Reset form state
      setOwnerMap({});
      setUsageMap({});
      setOwnerOptions(
        Array.isArray(ownerDetails)
        ? ownerDetails.map((owner) => ({
          label: owner.name,
          value: owner.property_owner_details_id,
        }))
        : []
      );
      setUsageOptions(
        Array.isArray(propertyUsageDetails)
        ? propertyUsageDetails.map((usage) => ({
          label: `${usage.propertyType?.propertyType || ""} - ${usage.are_sq_ft} sq ft`,
          value: usage.property_usage_details_id,
          isNew: false,
          ...usage,
        }))
        : []
      );
      setCurrentSelectedNumberForOwner(null);
      setCurrentSelectedOwner(null);
      setCurrentSelectedNumberForUsage(null);
      setCurrentSelectedUsage(null);
      setEditingUsage(null);
      setTempUsage(null);
      setCapturedPhotos([]);
      setUploadedFiles([]);
    } catch (error) {
      console.error("Error submitting properties:", error);
      // Show error toast
      toast({
        title: t("Failed to submit properties. Please try again."),
        variant: "destructive",
      });
    } finally {
      setLoading(false); // Set loading to false
    }
  };

  const handleResetFod = () => {
    setUsageOptions(() =>
      propertyUsageDetails.map((usage) => ({
        label: `${usage.propertyType?.propertyType || ""} - ${usage.are_sq_ft} sq ft`,
        value: usage.property_usage_details_id,
        isNew: false,
        ...usage,
      }))
    );
    setUsageMap({});
    setTempUsage(null);
  };

  const openAddOwnerForm = () => {
    setIsAddOwnerFormOpen(true);
  };

  const closeAddOwnerForm = () => {
    setIsAddOwnerFormOpen(false);
  };

  const handleAddNewOwner = (ownerData) => {
    if (!currentSelectedNumberForOwner) {
      toast({
        title: t("Please select a property number first."),
        variant: "destructive",
      });
      return;
    }

    console.log("Adding new owner:", ownerData); // Debugging statement

    // Add isNew flag to the owner data
    const newOwnerData = {
      ...ownerData,
      property_owner_details_id:null,
      isNew: true,
    };

    setOwnerMap((prev) => {
      const existing = prev[currentSelectedNumberForOwner] || [];
      return {
        ...prev,
        [currentSelectedNumberForOwner]: [...existing, newOwnerData],
      };
    });

    closeAddOwnerForm();
    setCurrentSelectedNumberForOwner(null); // Reset property number selection
  };

  const { ownerTypeList } = useOwnerTypeController();

  const ownerTypeMap = Object.fromEntries(
    ownerTypeList.map((ownerType) => [
      ownerType.owner_type_id,
      ownerType.owner_type,
    ])
  );
  console.log("Owner Details:", ownerDetails);
  console.log("Owner Type Map:", ownerTypeMap);

 return (
    <FormProvider {...methods}>
      <WhiteContainer>
        <div className="space-y-6">
          <Tabs defaultValue="owner">
            <TabsList className="w-[100%] md:w-fit !h-12 ">
              <TabsTrigger
                className="p-[10px]  rounded text-[15px]"
                value="owner"
              >
                मालक जोडा
              </TabsTrigger>
              <TabsTrigger
                className="p-[10px]  rounded text-[15px]"
                value="usage"
              >
                वापर तपशील जोडा
              </TabsTrigger>
            </TabsList>
            <TabsContent value="owner">
              <div className="grid grid-cols-4 gap-3 mt-4">
                <Select
                  value={currentSelectedNumberForOwner}
                  onValueChange={(value) => {
                    setCurrentSelectedNumberForOwner(value);
                    setCurrentSelectedOwner(null);
                  }}
                >
                  <SelectTrigger>
                    {currentSelectedNumberForOwner || "मालमत्ता क्रमांक"}
                  </SelectTrigger>
                  <SelectContent>
                    {validNumbers.map((number) => (
                      <SelectItem key={number} value={number}>
                        {number}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={currentSelectedOwner}
                  onValueChange={setCurrentSelectedOwner}
                >
                  <SelectTrigger>
                    {ownerOptions.find(
                      (owner) => owner.value === currentSelectedOwner
                    )?.label || "मालक निवडा"}
                  </SelectTrigger>
                  <SelectContent>
                    {ownerOptions?.map((owner) => (
                      <SelectItem key={owner.value} value={owner.value}>
                        {owner.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="flex gap-2 pt-[2px]">
                  <Button onClick={handleAddOwner}>जोडा</Button>
                  <Button
  onClick={openAddOwnerForm}
  className="min-w-fit w-auto px-4"
>
  {t("malmattaFerfar.addPropertyOwner")}
</Button>
{" "}
                </div>
              </div>

              {/* Owner table */}
              {Object.entries(ownerMap).map(([propertyNumber, owners]) => (
                <div
                  key={propertyNumber}
                  className="mt-8 border border-gray-200 rounded-xl p-6 shadow-sm bg-white"
                >
                  <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">
                    मालमत्ता क्रमांक:{" "}
                    <span className="text-primary">{propertyNumber}</span>
                  </h3>

                  <Table className="border border-gray-200 rounded-20">
                    <TableHeader>
                      <TableRow className="bg-gray-100">
                        <TableHead className="text-sm font-medium text-gray-700 w-1/3">
                          नाव
                        </TableHead>
                        <TableHead className="text-sm font-medium text-gray-700 w-1/3">
                          मालक प्रकार
                        </TableHead>
                        <TableHead className="text-sm font-medium text-gray-700 text-left w-1/3">
                          कृती
                        </TableHead>
                      </TableRow>
                    </TableHeader>

                    <TableBody>
                      {owners.map((owner) => (
                        <TableRow
                          key={owner.property_owner_details_id}
                          className="align-top"
                        >
                          <TableCell className="w-1/3">
                            {owner.name || owner.owner_name}
                          </TableCell>{" "}
                          {/* Debugging statement */}
                          <TableCell className="w-1/3">
                            {typeof owner.owner_type === "object"
                              ? owner.owner_type?.owner_type
                              : ownerTypeMap[owner.owner_type]}
                          </TableCell>
                          <TableCell className="w-1/3 flex items-start justify-start">
                            <button
                              className="h-8 w-8 p-0"
                              onClick={() =>
                                handleDeleteOwner(
                                  propertyNumber,
                                  owner.property_owner_details_id
                                )
                              }
                            >
                              <Trash2 className="text-red-500" />
                            </button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ))}
            </TabsContent>
            <TabsContent value="usage">
              <div className="grid grid-cols-3 gap-3 mt-4">
                {/* Property Number for Usage */}
                <Select
                  value={currentSelectedNumberForUsage}
                  onValueChange={setCurrentSelectedNumberForUsage}
                >
                  <SelectTrigger>
                    {currentSelectedNumberForUsage || "मालमत्ता क्रमांक"}
                  </SelectTrigger>
                  <SelectContent>
                    {validNumbers.map((number) => (
                      <SelectItem key={number} value={number}>
                        {number}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Property Usage */}
                <Select
                  value={currentSelectedUsage}
                  onValueChange={setCurrentSelectedUsage}
                >
                  <SelectTrigger>
                    {usageOptions?.find(
                      (usage) => usage.value === currentSelectedUsage
                    )?.label || "वापर तपशील निवडा"}
                  </SelectTrigger>
                  <SelectContent>
                    {usageOptions?.map((usage) => (
                      <SelectItem key={usage.value} value={usage.value}>
                        {usage.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="flex gap-2">
                  <Button onClick={handleAddUsage}>जोडा</Button>
                  <Button onClick={handleResetFod}>Reset Usage</Button>
                </div>
              </div>

              {/* Property Usage table */}
              {Object.entries(usageMap).map(([propertyNumber, usages]) => (
                <div key={propertyNumber} className="mt-6 border rounded p-4">
                  <h3 className="font-semibold mb-2">
                    मालमत्ता क्रमांक : {propertyNumber}
                  </h3>
                  <Table className="border border-gray-200">
                    <TableHeader>
                      <TableRow className="bg-gray-100">
                        <TableHead>प्रकार</TableHead>
                        <TableHead>लांबी</TableHead>
                        <TableHead>रुंदी</TableHead>
                        <TableHead>क्षेत्रफळ (चौ. फूट)</TableHead>
                        <TableHead>कृती</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {usages?.map((usage) => (
                        <TableRow key={usage.property_usage_details_id}>
                          <TableCell>
                            {usage?.propertyType?.propertyType || ""}
                          </TableCell>
                          <TableCell>
                            {editingUsage &&
                            editingUsage.usageId ===
                              usage.property_usage_details_id ? (
                              <Input
                                type="number"
                                value={tempUsage.length}
                                onChange={(e) =>
                                  handleInputChange("length", e.target.value)
                                }
                              />
                            ) : (
                              usage.length
                            )}
                          </TableCell>
                          <TableCell>
                            {editingUsage &&
                            editingUsage.usageId ===
                              usage.property_usage_details_id ? (
                              <Input
                                type="number"
                                value={tempUsage.width}
                                onChange={(e) =>
                                  handleInputChange("width", e.target.value)
                                }
                              />
                            ) : (
                              usage.width
                            )}
                          </TableCell>
                          <TableCell>
                            {editingUsage &&
                            editingUsage.usageId ===
                              usage.property_usage_details_id ? (
                              <Input
                                type="number"
                                value={tempUsage.are_sq_ft}
                                onChange={(e) =>
                                  handleInputChange("are_sq_ft", e.target.value)
                                }
                              />
                            ) : (
                              usage.are_sq_ft
                            )}
                          </TableCell>
                          <TableCell>
                            {editingUsage &&
                            editingUsage.usageId ===
                              usage.property_usage_details_id ? (
                              <>
                                <button
                                  className="h-8 w-8 p-0"
                                  onClick={() =>
                                    handleConfirmEdit(
                                      propertyNumber,
                                      usage.property_usage_details_id
                                    )
                                  }
                                >
                                  <Check className="text-green-500" />
                                </button>
                                <button
                                  className="h-8 w-8 p-0"
                                  onClick={handleDiscardEdit}
                                >
                                  <X className="text-red-500" />
                                </button>
                              </>
                            ) : (
                              <>
                                <button
                                  className="h-8 w-8 p-0"
                                  onClick={() =>
                                    handleEditUsage(
                                      propertyNumber,
                                      usage.property_usage_details_id
                                    )
                                  }
                                >
                                  <Edit className="text-blue-500" />
                                </button>
                                <button
                                  className="h-8 w-8 p-0 ml-2"
                                  onClick={() =>
                                    handleDeleteUsage(
                                      propertyNumber,
                                      usage.property_usage_details_id
                                    )
                                  }
                                >
                                  <Trash2 className="text-red-500" />
                                </button>
                              </>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ))}
            </TabsContent>
          </Tabs>
        </div>
      </WhiteContainer>
      <WhiteContainer>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-5 px-3">
          <div className="grid-cols-subgrid">
            <FormField
              control={control}
              name="photo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>फोटो अपलोड करा </FormLabel>
                  <FormControl>
                    <WebcamCapture onCapture={(photo) => handleCapture(photo)} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <div className="grid-cols-subgrid">
            {/* Document Upload Field */}
            <FormField
              control={control}
              name="documents"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <DocumentUpload
                                          onUploadComplete={handleUploadComplete}

                      title={"Click to upload"}
                      btnText={t("propertyLocationDetailsForm.uploadFile")}
                      uploadIcon={
                        <UploadCloudIcon className="w-9 h-9 m-auto text-BlueText"  />
                      }
                      
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className="grid-cols-subgrid">
            <FormField
              control={control}
              name="remark"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>शेरा</FormLabel>
                  <FormControl>
                    <Textarea
                      className="mt-1 block w-full"
                      placeholder="शेरा"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className="mt-4 flex justify-end space-x-2 items-end">
            {/* Cancel button on the left */}
            <Button
              className="mt-5 "
              onClick={handleSubmit(handleSunmitProperties)}
              disabled={loading}
            >
              {loading ? "Submitting..." : "विभाजित मालमत्ता सबमिट करा"}
            </Button>
          </div>
        </div>
      </WhiteContainer>
      {isAddOwnerFormOpen && (
        <AddOwnerForm
          onClose={closeAddOwnerForm}
          onSubmit={handleAddNewOwner}
          ownerDetails={ownerDetails}
        />
      )}
    </FormProvider>
  );
};

export default PropertyTable;

function randomUUID() {
  // Generates a RFC4122 version 4 UUID
  return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
    (
      Number(c) ^
      (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (Number(c) / 4)))
    ).toString(16)
  );
}
