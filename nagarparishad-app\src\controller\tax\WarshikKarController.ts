import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import PropertyA<PERSON> from "@/services/PropertyServices";

// Fetch financial years for WarshikKar
const fetchFinancialYears = async () => {
  return new Promise((resolve, reject) => {
    PropertyApi.getFinancialYears((response) => {
      if (response.status && response.data.statusCode === 200) {
        // Format the data to include the financial year display
        const formattedData = response.data.data.map(item => ({
          ...item,
          financial_year_id: item.financial_year_id,
          financial_year_range: item.financial_year_range
        }));
        resolve(formattedData);
      } else {
        reject(response.data);
      }
    });
  });
};

// Fetch WarshikKar data
const fetchWarshikKarData = async () => {
  return new Promise((resolve, reject) => {
    // This would be replaced with an actual API call to get WarshikKar data
    // For now, we're using the financial years data as a placeholder
    PropertyApi.getFinancialYears((response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(response.data);
      }
    });
  });
};

// Process WarshikKar generation
const processWarshikKar = async ({ financialYearId }: { financialYearId: string }) => {
  return new Promise((resolve, reject) => {
    PropertyApi.processWarshikKar(financialYearId, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data);
      } else {
        reject(new Error(response.data.message || "Failed to generate Warshik Kar"));
      }
    });
  });
};

// Fetch ward-wise Warshik Kar status
const fetchWardWiseWarshikKarStatus = async (financialYearId: string) => {
  return new Promise((resolve, reject) => {
    PropertyApi.getWardWiseWarshikKarStatus(financialYearId, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(response.data);
      }
    });
  });
};

// Process Warshik Kar for specific ward
const processWarshikKarByWard = async ({ wardNumber, financialYearId }: { wardNumber: string, financialYearId: string }) => {
  return new Promise((resolve, reject) => {
    PropertyApi.processWarshikKarByWard(wardNumber, financialYearId, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data);
      } else {
        reject(new Error(response.data.message || "Failed to generate Warshik Kar for ward"));
      }
    });
  });
};

// Change publish status
const changePublishStatus = async ({ publishId, publishStatus }: { publishId: string, publishStatus: boolean }) => {
  return new Promise((resolve, reject) => {
    // This would be replaced with an actual API call to change publish status
    // For now, we're using a placeholder
    setTimeout(() => {
      resolve({ success: true, message: "Status changed successfully" });
    }, 500);
  });
};

export const useWarshikKarController = () => {
  const queryClient = useQueryClient();

  // Mutation for processing WarshikKar
  const processWarshikKarMutation = useMutation({
    mutationFn: processWarshikKar,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["warshikKarData"] });
    },
  });

  // Mutation for processing WarshikKar by ward
  const processWarshikKarByWardMutation = useMutation({
    mutationFn: processWarshikKarByWard,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["warshikKarData"] });
      queryClient.invalidateQueries({ queryKey: ["wardWiseWarshikKarStatus"] });
    },
  });

  // Mutation for changing publish status
  const changePublishStatusMutation = useMutation({
    mutationFn: changePublishStatus,
    onMutate: async ({ publishId, publishStatus }) => {
      await queryClient.invalidateQueries({ queryKey: ["warshikKarData"] });
      const previousStatus = queryClient.getQueryData(["warshikKarData"]);
      queryClient.setQueryData(["warshikKarData"], (old: { publish: any }[]) =>
        old.map((item: { publish: any }) =>
          item.publish === publishId ? { ...item, ...publishStatus } : item,
        ),
      );
      return { previousStatus };
    },
    onError: (err, { publishId, publishStatus }, context) => {
      queryClient.setQueryData(["warshikKarData"], context.previousStatus);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["warshikKarData"] });
    },
  });

  // Query for financial years
  const {
    data: financialYears,
    isLoading: financialYearsLoading,
    refetch: refetchFinancialYears
  } = useQuery({
    queryKey: ["financialYears"],
    queryFn: fetchFinancialYears,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
  });

  // Query for WarshikKar data
  const {
    data: warshikKarData,
    isLoading: warshikKarDataLoading,
    refetch: refetchWarshikKarData
  } = useQuery({
    queryKey: ["warshikKarData"],
    queryFn: fetchWarshikKarData,
    staleTime: 0, // Always refetch
    refetchOnWindowFocus: true,
  });

  return {
    financialYears: financialYears || [],
    financialYearsLoading,
    warshikKarData: warshikKarData || [],
    warshikKarDataLoading,
    processWarshikKar: processWarshikKarMutation.mutate,
    processWarshikKarByWard: processWarshikKarByWardMutation.mutate,
    fetchWardWiseWarshikKarStatus,
    refetchFinancialYears,
    refetchWarshikKarData
  };
};
