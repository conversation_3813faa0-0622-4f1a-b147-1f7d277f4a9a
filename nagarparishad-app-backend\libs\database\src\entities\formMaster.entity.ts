import {
  BaseEntity,
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ModuleMasterEntity } from './module-master.entity';

@Entity('form_master')
export class FormMasterEntity extends BaseEntity {
  @PrimaryGeneratedColumn()
  form_id: number;

  @Column({ name: 'form_name', type: 'varchar', nullable: false })
  formName: string;

  @ManyToOne(() => ModuleMasterEntity, (module) => module.forms)
  @JoinColumn({ name: 'module_id' })
  module: ModuleMasterEntity;
  /*
   * Create and Update Date Columns
   */
  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
