import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Master_tax_rateEntity } from 'libs/database/entities';
import { Master_TaxValueRepository ,PropertyTypeMasterRepository} from 'libs/database/repositories';

@Injectable()
export class Master_tax_valueService {
  constructor(
    @InjectRepository(Master_TaxValueRepository)
    private readonly taxValueRepository: Master_TaxValueRepository,
    private readonly PropertyTypeMasterRepository: PropertyTypeMasterRepository,

  ) {}

  async create(data: any): Promise<{ message: string; data: Master_tax_rateEntity[] }> {

    const propertyType = await this.PropertyTypeMasterRepository.findById(
      data.propertyType_id,
    );


    if (!propertyType) {
      throw new NotFoundException('propertyType not found');
    }

    // Handle reassessment range if provided
    let reassessmentRange = null;
    if (data.reassessment_range_id) {
      const { ReassessmentRange } = await import('libs/database/entities');
      reassessmentRange = await this.taxValueRepository.manager
        .getRepository(ReassessmentRange)
        .findOne({
          where: { reassessment_range_id: data.reassessment_range_id }
        });
      if (!reassessmentRange) {
        throw new NotFoundException('Reassessment range not found');
      }
    }

    // Create a new instance of Master_rr_construction_rate
    const newRate = this.taxValueRepository.create({
      ...data,
      property_type: propertyType, // Set the actual zone entity here
      reassessmentRange: reassessmentRange, // Set the reassessment range entity
    });
    const savedTaxValue = await this.taxValueRepository.save(newRate);
    return {
      message: 'Tax value created successfully',
      data: savedTaxValue,
    };
  }

  async findAll(): Promise<{ message: string; data: Master_tax_rateEntity[] }> {
    const allTaxValues = await this.taxValueRepository.getWithPropertyType();
    return {
      message: 'Tax values fetched successfully',
      data: allTaxValues,
    };
  }

  // async findOne(id: string): Promise<{ message: string; data: Master_tax_rateEntity | undefined }> {
  //   const taxValue = await this.taxValueRepository.findOne(id);
  //   if (!taxValue) {
  //     return {
  //       message: 'Tax value not found',
  //       data: undefined,
  //     };
  //   }
  //   return {
  //     message: 'Tax value fetched successfully',
  //     data: taxValue,
  //   };
  // }

  async update(id: string, data: any): Promise<{ message: string; data: Master_tax_rateEntity | undefined }> {
    const updatedTaxValue =  await this.taxValueRepository.updateTaxValue(id, data);
    return {
      message: 'Tax value updated successfully',
      data: updatedTaxValue.data,
    };
  }

  async delete(id: string): Promise<{ message: string }> {
    await this.taxValueRepository.softDelete(id);
    return {
      message: 'Tax value deleted successfully',
    };
  }

  
  
}
