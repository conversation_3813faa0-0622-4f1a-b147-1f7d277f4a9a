
import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { DeletedPropertyEntity, PropertyEntity } from '../entities';


@Injectable()
export class DeletedPropertyRepository extends Repository<DeletedPropertyEntity>{
    constructor(
        @InjectRepository(DeletedPropertyEntity)
        private readonly deletedPropertyRepo: Repository<DeletedPropertyEntity>,
    ) {      
    super(
        deletedPropertyRepo.target,
        deletedPropertyRepo.manager,
        deletedPropertyRepo.queryRunner,
      );
    }
    async findById(property_id : string): Promise<DeletedPropertyEntity | null> {
        return await this.deletedPropertyRepo.findOne({ where: {property_id: property_id } });
      }

}