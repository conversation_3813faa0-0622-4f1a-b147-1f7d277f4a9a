import { ChevronDown } from 'lucide-react';
import React, { useState } from 'react';
import { FaChevronDown } from 'react-icons/fa'; // Import the down arrow icon

interface CollapseProps {
  title: string;
  isOpen: boolean; // Controls whether the collapse is open or closed
  onToggle: () => void; // Function to handle open/close toggle
  children: React.ReactNode; // Content inside the collapse
}

const Collapse: React.FC<CollapseProps> = ({ title, isOpen, onToggle, children }) => {

  return (
    <div className="my-5 px-4 py-2 pt-4 border rounded-xl overflow-auto ">
      <button
        onClick={onToggle}
        className="text-xl font-semibold font-Noto flex items-center justify-between w-full focus:outline-none"
      >
        {title}
        <ChevronDown
          className={`w-6 h-6 transform transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>
      <div className={` transition-max-height duration-300 ${isOpen ? 'max-h-screen' : 'max-h-0 overflow-hidden'}`}>
        <div>
            <hr className='my-3' />
          {children}
        </div>
      </div>
    </div>
  );
};

export default Collapse;
