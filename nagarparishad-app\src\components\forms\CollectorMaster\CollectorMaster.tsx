import React, { useContext, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import { Edit, Trash } from "lucide-react";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { useNavigate, useParams } from "react-router-dom";
import { GlobalContext } from "@/context/GlobalContext";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import AsyncSelect from "@/components/ui/react-select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useBookController } from "@/controller/tax/BookeMasterController";
import { MultiSelect } from "@/components/ui/multi-select";
import UserListApi from "@/services/UserListServices";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

const CollectorMaster = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { collectorId } = useParams();
  const { setrefreshRoleList } = useContext(GlobalContext);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [selectedWard, setSelectedWard] = useState(null);
  const [localCollectorId, setLocalCollectorId] = useState(null);
  const [selectedBookIds, setSelectedBookIds] = useState([]);
  const { BookList, bookLoading } = useBookController();
  const [collectorList, setCollectorList] = useState([]);
  const [filterBookOptions, setFilterBookOptions] = useState([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const schema = z.object({
    firstname: z.string().trim().min(1, t("errorsRequiredField")),
    ward: z.object({
      ward_id: z.string().min(1, t("errorsRequiredField")),
      ward_name: z.string().optional(),
    }),
    books: z.array(z.string()).optional(),
  });

  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      firstname: "",
      ward: {
        ward_id: "",
        ward_name: "",
      },
      books: [],
    },
  });

  const { formState: { errors }, setValue, reset, control, watch } = form;

  const wardList = useWardMasterController();
  const wardOptions = wardList?.wardList?.map((ward) => ({
    value: ward?.ward_id,
    label: ward?.ward_name,
  })) || [];

  const updateFilterOptions = (includeCollectorId = null) => {
    setFilterBookOptions(() => {
      return BookList.filter((book) => book.collector === null || book.collector.collectorId === includeCollectorId)
        .sort((a, b) => a.book_number - b.book_number)
        .map((book) => ({
          value: book.id,
          label: book.book_number.toString(),
        }));
    });
  };

  useEffect(() => {
    fetchCollectors();
    updateFilterOptions();
  }, []);

  useEffect(() => {
    if (collectorId) {
      setIsEditMode(true);
      setLocalCollectorId(collectorId);
      fetchCollectorDetails(collectorId);
      setIsDialogOpen(true);
      updateFilterOptions(collectorId);
    }
  }, [collectorId]);

  const fetchCollectors = async () => {
    try {
      const collectors = await UserListApi.getAllCollectors();
      if (collectors.status === true) {
        setCollectorList(collectors.data);
      } else {
        setCollectorList([]);
        toast({
          title: t("failedToFetchCollectors"),
          description: t("errorFetchingData"),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching collectors:", error);
      toast({
        title: t("failedToFetchCollectors"),
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const fetchCollectorDetails = async (id) => {
    try {
      const collectors = await UserListApi.getAllCollectors();
      const collector = collectors.data.find((col) => col.collectorId === id);
      if (collector) {
        setValue("firstname", collector.user.firstname);
        setValue("ward.ward_id", collector.ward?.ward_id || "");
        setValue("ward.ward_name", collector.ward?.ward_name || "");
        setSelectedWard({
          value: collector.ward?.ward_id || "",
          label: collector.ward?.ward_name || "",
        });

        if (collector.books && collector.books.length > 0) {
          const bookIds = collector.books.map((book) => book.id);
          setValue("books", bookIds);
          setSelectedBookIds(bookIds);
        }
      }
    } catch (error) {
      console.error("Error fetching collector data:", error);
      toast({
        title: t("failedToFetchCollector"),
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const onSubmit = (data) => {
    const idToUpdate = collectorId || localCollectorId || (selectedItem && selectedItem.collectorId);

    if (!idToUpdate) {
      console.error("No collector ID found. Selected item:", selectedItem);
      toast({
        title: t("updateError"),
        description: t("noCollectorIdFound"),
        variant: "destructive",
      });
      return;
    }

    const updateData = {
      wardId: data.ward.ward_id,
      bookIds: selectedBookIds,
    };

    updateCollector(idToUpdate, updateData);
  };

  const updateCollector = async (id, updateData) => {
    try {
      await UserListApi.updateCollector(id, updateData);
      toast({
        title: t("collectorUpdatedSuccessfully"),
        variant: "success",
      });

      reset();
      setIsEditMode(false);
      setLocalCollectorId(null);
      setSelectedItem(null);
      setSelectedWard(null);
      setSelectedBookIds([]);
      setIsDialogOpen(false);

      fetchCollectors();
    } catch (error) {
      console.error("Update error:", error);
      toast({
        title: t("failedToUpdateCollector"),
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const columns = [
    {
      accessorKey: "SrNo",
      header: t("SrNo"),
      cell: ({ row }) => <div>{row.index + 1}</div>,
    },
    {
      accessorKey: "firstname",
      header: t("user.firstName"),
      cell: ({ row }) => <div>{row.original.user?.firstname || "N/A"}</div>,
    },
    {
      accessorKey: "lastname",
      header: t("user.lastName"),
      cell: ({ row }) => <div>{row.original.user?.lastname || "N/A"}</div>,
    },
    {
      accessorKey: "email",
      header: t("user.email"),
      cell: ({ row }) => <div>{row.original.user?.email || "N/A"}</div>,
    },
    {
      accessorKey: "mobileNumber",
      header: t("ownerDetails.mobileNumber"),
      cell: ({ row }) => <div>{row.original.user?.mobileNumber || "N/A"}</div>,
    },
    {
      accessorKey: "ward",
      header: t("ward.wardLabel"),
      cell: ({ row }) => <div>{row.original.ward?.ward_name || "N/A"}</div>,
    },
    {
      accessorKey: "books",
      header: t("setting.bookNumber"),
      cell: ({ row }) => {
        const books = row.original.books || [];
        return (
          <div>
            {books.length > 0 ? (
              <p>
                {books.map((book) => book.book_number || "Untitled Book").join(", ")}
              </p>
            ) : (
              "No Books"
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "actions",
      header: t("Actions"),
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <button
            className="h-8 w-8 p-0"
            onClick={() => handleEdit(row.original)}
            title={t("edit")}
          >
            <Edit className="text-blue-500" />
          </button>
        </div>
      ),
    },
  ];

  const handleEdit = (collectorData) => {
    const collectorId = collectorData.collectorId;

    if (!collectorId) {
      console.error("Could not find collector ID in data:", collectorData);
      toast({
        title: t("editError"),
        description: t("collectorIdNotFound"),
        variant: "destructive",
      });
      return;
    }

    setSelectedItem(collectorData);
    setIsEditMode(true);
    setLocalCollectorId(collectorId);

    setValue("firstname", collectorData.user?.firstname || "");
    setValue("ward.ward_id", collectorData.ward?.ward_id || "");
    setValue("ward.ward_name", collectorData.ward?.ward_name || "");

    setSelectedWard({
      value: collectorData.ward?.ward_id || "",
      label: collectorData.ward?.ward_name || "",
    });

    if (collectorData.books && collectorData.books.length > 0) {
      const bookIds = collectorData.books.map((book) => book.id);
      setValue("books", bookIds);
      setSelectedBookIds(bookIds);
    } else {
      setSelectedBookIds([]);
    }

    updateFilterOptions(collectorId);
    setIsDialogOpen(true);
  };

  const handleCancelEdit = () => {
    setIsEditMode(false);
    setSelectedItem(null);
    setLocalCollectorId(null);
    setSelectedBookIds([]);
    setIsDialogOpen(false);
    reset();
    updateFilterOptions();
  };

  const handleValueChange = (selectedValues) => {
    setSelectedBookIds(selectedValues);
    return selectedValues;
  };

  const onError = (error) => {
    console.error("Form submission error:", error);
  };

  return (
    <div className="w-full h-fit p-6">
      <h1 className="text-2xl font-semibold font-Poppins w-full mb-4 ml-2">
        {t("master.collector")}
      </h1>

      <div className="bg-white shadow rounded-lg p-4">
        <TanStackTable
          columns={columns}
          data={collectorList || []}
          // searchKey={"searchrole"}
          searchColumn={"roleName"}
        />
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px] bg-white border-2">
          <DialogHeader className="bg-gray-50 p-4 -mt-8 -mx-8 mb-4 rounded-t-lg">
            <DialogTitle className="text-Primary text-xl">{t("editCollector")}</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form
              className="flex flex-col gap-4"
              onSubmit={form.handleSubmit(onSubmit, onError)}
            >
              <div className="grid md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <FormField
                    control={form.control}
                    name="firstname"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("user.firstName")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("user.fullname")}
                            className="w-full"
                            value={selectedItem?.user?.firstname + " " + selectedItem?.user?.lastname}
                            disabled
                          />
                        </FormControl>
                        {errors.firstname && (
                          <FormMessage className="ml-1">
                            {errors.firstname.message}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
                <div className="md:col-span-2">
                  <FormField
                    control={form.control}
                    name="ward.ward_id"
                    render={() => (
                      <FormItem>
                        <FormLabel>
                          {t("propertyLocationDetailsForm.ward")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Controller
                            control={form.control}
                            name="ward.ward_id"
                            render={({ field: controllerField }) => (
                              <AsyncSelect
                                placeholder={t("propertyLocationDetailsForm.ward")}
                                options={wardOptions}
                                value={wardOptions?.find((option) => option.value === controllerField.value) || selectedWard}
                                onChange={(selectedOption) => {
                                  if (selectedOption) {
                                    controllerField.onChange(selectedOption.value);
                                    form.setValue("ward.ward_name", selectedOption.label);
                                    setSelectedWard(selectedOption);
                                  }
                                }}
                                colourOptions={wardOptions}
                              />
                            )}
                          />
                        </FormControl>
                        {errors.ward?.ward_id?.message && (
                          <FormMessage className="ml-1">
                            {errors.ward.ward_id.message}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
                <div className="md:col-span-2">
                  <FormField
                    control={control}
                    name="books"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("Select Books")}
                          <span className="ml-1 text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Controller
                            control={form.control}
                            name="books"
                            render={({ field: controllerField }) => (
                              <MultiSelect
                                options={filterBookOptions}
                                value={controllerField.value}
                                defaultValue={selectedBookIds}
                                onValueChange={(selectedValues) => {
                                  controllerField.onChange(handleValueChange(selectedValues));
                                }}
                                placeholder={t("Select Books")}
                                variant="default"
                                maxCount={15}
                                className="text-black mt-1"
                                asChild={true}
                              />
                            )}
                          />
                        </FormControl>
                        {errors.books && (
                          <FormMessage className="ml-1">
                            {errors.books.message}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-3 mt-4 justify-end">
                <Button variant="submit" type="submit" className="w-fit">
                  {t("updateCollector")}
                </Button>
                <Button variant="outline" type="button" onClick={handleCancelEdit}>
                  {t("cancel")}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={selectedItem.user?.firstname || ""}
        />
      )}
    </div>
  );
};

export default CollectorMaster;
