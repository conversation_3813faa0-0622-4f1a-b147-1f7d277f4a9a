
import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    JoinColumn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    ManyToOne,
    OneToMany,
  } from 'typeorm';
  import { PropertyEntity } from './property.entity';
import { WarshilKarEntity } from './warshik_kar.entity';

import { Property_Usage_Details_Entity } from './property-usage-details.entity';

@Entity('warshikKarTax')
export class WarshilKarTaxEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  warshik_karTaxId: string;

  @ManyToOne(() => PropertyEntity, (property) => property.property_id, {onDelete : 'CASCADE'})
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

  @ManyToOne(() => WarshilKarEntity, (WarshilKar) => WarshilKar.warshik_karId)
  @JoinColumn({ name: 'warshik_karId' })
  WarShikKar: WarshilKarEntity;


  @ManyToOne(() => Property_Usage_Details_Entity, (property_usage_details) => property_usage_details.property_usage_details_id)
  @JoinColumn({ name: 'property_usage_details_id' })
  property_usage_details: Property_Usage_Details_Entity;

  @Column({
    type: String,
    name: 'financial_year',
    nullable: true,
  })
  financial_year: string;

  @Column({ type: 'float', nullable: false })
  sq_ft_meter: number;

  //ready reckner rate
  @Column({ type: 'float', nullable: false })
  rr_rate: number;

  //ready reckner construction rate
  @Column({ type: 'float', nullable: false })
  rr_construction_rate: number;

  
  @Column({ type: 'float', nullable: false })
  depreciation_rate: number;

  //bharank 
  @Column({ type: 'float', nullable: false })
  weighting: number;

  
  @Column({ type: 'float', nullable: false })
  capital_value: number;

  @Column({ type: 'float', nullable: false })
  tax_value: number;

  @Column({ type: 'float', nullable: false })
  tax: number;

  @Column({ type: String, nullable: true })
  tax_data: JSON;


  @Column({ type: 'date', nullable: false })
  bill_generation_date: Date;

  

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}