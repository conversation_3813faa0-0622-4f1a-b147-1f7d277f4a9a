import { Repository } from 'typeorm';
import { ReassessmentRange } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class ReassessmentRangeRepository extends Repository<ReassessmentRange> {
  constructor(
    @InjectRepository(ReassessmentRange)
    private readonly reassessmentRangeRepository: Repository<ReassessmentRange>,
  ) {
    super(
      reassessmentRangeRepository.target,
      reassessmentRangeRepository.manager,
      reassessmentRangeRepository.queryRunner,
    );
  }

  async saveData(data: Partial<ReassessmentRange>) {
    return await this.reassessmentRangeRepository.save(data);
  }

  async findAllReassessmentRanges() {
    return await this.reassessmentRangeRepository
      .createQueryBuilder('reassessment_range')
      .orderBy('reassessment_range.created_at', 'DESC')
      .getMany();
  }

  async findById(reassessmentRangeId: string) {
    return await this.reassessmentRangeRepository
      .createQueryBuilder('reassessment_range')
      .where(
        'reassessment_range.reassessment_range_id = :reassessmentRangeId',
        { reassessmentRangeId },
      )
      .getOne();
  }

  async updateReassessmentRange(
    reassessmentRangeId: string,
    input: Partial<ReassessmentRange>,
  ) {
    return await this.reassessmentRangeRepository
      .createQueryBuilder('reassessment_range')
      .update(ReassessmentRange)
      .set(input)
      .where('reassessment_range_id = :reassessmentRangeId', {
        reassessmentRangeId,
      })
      .execute();
  }

  async deleteReassessmentRange(reassessmentRangeId: string) {
    return await this.reassessmentRangeRepository
      .createQueryBuilder('reassessment_range')
      .softDelete()
      .where('reassessment_range_id = :reassessmentRangeId', {
        reassessmentRangeId,
      })
      .execute();
  }

  async getCurrentReassesmentRange(): Promise<ReassessmentRange | null> {
    return await this.reassessmentRangeRepository.findOne({
      where: { is_current: true },
    });
  }

  async getReassessmentRangeByYear(
    yearRange: string,
  ): Promise<ReassessmentRange | null> {
    if (
      !yearRange ||
      typeof yearRange !== 'string' ||
      !yearRange.includes('-')
    ) {
      console.error('Invalid yearRange format:', yearRange);
      return null;
    }

    const parts = yearRange.split('-');
    const startYear = parseInt(parts[0], 10);
    const endYear = parseInt(parts[1], 10);

    if (isNaN(startYear) || isNaN(endYear)) {
      console.error('Failed to parse years from:', yearRange);
      return null;
    }

    // Query using PostgreSQL split_part to extract year integers from string
    return await this.reassessmentRangeRepository
      .createQueryBuilder('reassessment_range')
      .where(
        `CAST(split_part(reassessment_range.start_range, '-', 1) AS INTEGER) <= :startYear`,
        { startYear },
      )
      .andWhere(
        `CAST(split_part(reassessment_range.end_range, '-', 2) AS INTEGER) >= :endYear`,
        { endYear },
      )
      .andWhere('reassessment_range.deleted_at IS NULL')
      .getOne();
  }
}
