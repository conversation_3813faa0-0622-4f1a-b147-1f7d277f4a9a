// src/usage-sub-type-master/dto/update-usage-sub-type.dto.ts
import {
  IsString,
  IsNumber,
  IsUUID,
  IsNotEmpty,
  IsOptional,
  IsIn,
} from 'class-validator';

export class UpdateUsageSubTypeDto {
  @IsOptional()
  @IsString()
  financial_year?: string; // Example: "2024-2025", kept for backward compatibility

  @IsOptional()
  @IsString()
  reassessment_range_id?: string; // Reassessment range ID

  @IsNumber()
  value: number; // New value, e.g., changing from 10 to 15

  @IsString()
  @IsIn(['Active', 'Inactive'])
  status: string; // Status must be either 'Active' or 'Inactive'

  @IsOptional()
  @IsString()
  usage_sub_type_master_id?: string; // Existing usage subtype ID
}
