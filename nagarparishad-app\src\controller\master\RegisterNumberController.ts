import { useQuery } from "@tanstack/react-query";
import Api from "../../services/ApiServices";

// API call wrapper (convert callback → Promise)
const fetchRegisterNumbers = async () => {
  return new Promise((resolve, reject) => {
    Api.getAllRegisterNumbers((response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useRegisterNumberController = () => {
  const { data: registerNumberData, isLoading: registerNumberLoading } = useQuery({
    queryKey: ["registernumbermaster"],
    queryFn: fetchRegisterNumbers,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  return {
    registerNumberList: registerNumberData || [],
    registerNumberLoading,
  };
};
