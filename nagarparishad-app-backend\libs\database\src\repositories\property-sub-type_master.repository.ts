import { Repository } from 'typeorm';
import {
  PropertySubTypeMasterEntity,
  PropertyTypeMasterEntity,
} from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class PropertySubTypeMasterRepository extends Repository<PropertySubTypeMasterEntity> {
  constructor(
    @InjectRepository(PropertySubTypeMasterEntity)
    private readonly propertySubTypeMasterRepository: Repository<PropertySubTypeMasterEntity>,
  ) {
    super(
      propertySubTypeMasterRepository.target,
      propertySubTypeMasterRepository.manager,
      propertySubTypeMasterRepository.queryRunner,
    );
  }

  async saveData(input: any) {
    let data = this.propertySubTypeMasterRepository.create(input);
    data = await this.propertySubTypeMasterRepository.save(data);
    return data;
  }

  async findAllData() {
    return await this.propertySubTypeMasterRepository
      .createQueryBuilder('property_sub_type_master')
      .cache(true)
      .select([
        'property_sub_type_master.propertySub_id',
        'property_sub_type_master.propertySub_name',
        'property_type_master.propertyType_id',
        'property_type_master.propertyType',
      ])
      .leftJoin('property_sub_type_master.propertyType', 'property_type_master')
      .orderBy('property_sub_type_master.updated_at', 'DESC')
      .getMany();
  }

  async findById(id: string) {
    return await this.propertySubTypeMasterRepository
      .createQueryBuilder('property_sub_type_master')
      .select([
        'property_sub_type_master.propertySub_id',
        'property_sub_type_master.propertySub_name',
        'property_type_master.propertyType_id',
        'property_type_master.propertyType',
      ])
      .leftJoin('property_sub_type_master.propertyType', 'property_type_master')
      .where('property_sub_type_master.propertySub_id = :propertySub_id', {
        propertySub_id: id,
      })
      .getOne();
  }

  async updateData(id: string, input: any) {
    return await this.propertySubTypeMasterRepository
      .createQueryBuilder('property_sub_type_master')
      .update(PropertySubTypeMasterEntity)
      .set(input)
      .where('propertySub_id = :propertySub_id', { propertySub_id: id })
      .execute();
  }

  async deleteData(id: string) {
    return await this.propertySubTypeMasterRepository
      .createQueryBuilder('property_sub_type_master')
      .softDelete()
      .where('propertySub_id = :propertySub_id', { propertySub_id: id })
      .execute();
  }

  async findData(id: string) {
    return await this.propertySubTypeMasterRepository
      .createQueryBuilder('property_sub_type_master')
      .select([
        'property_sub_type_master.propertySub_id',
        'property_sub_type_master.propertySub_name',
      ])
      .leftJoin('property_sub_type_master.propertyType', 'property_type_master')
      .where('property_type_master.propertyType_id = :propertyType_id', {
        propertyType_id: id,
      })
      .getMany();
  }
}
