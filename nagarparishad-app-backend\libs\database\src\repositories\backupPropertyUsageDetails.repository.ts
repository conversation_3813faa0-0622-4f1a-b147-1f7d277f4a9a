import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { BackupPropertyUsageDetailsEntity, DeletedPropertyUsageEntity, Property_Usage_Details_Entity } from '../entities';
import { Injectable } from '@nestjs/common';

@Injectable()
export class BackupPropertyUsageDetailsRepository extends Repository<BackupPropertyUsageDetailsEntity> {
  constructor(
    @InjectRepository(BackupPropertyUsageDetailsEntity)
    private readonly backupPropertyUsageRepo: Repository<BackupPropertyUsageDetailsEntity>,
  ) {
    super(
        backupPropertyUsageRepo.target,
        backupPropertyUsageRepo.manager,
        backupPropertyUsageRepo.queryRunner,
    );
  }
  async findById(id: string) {
    return await this.backupPropertyUsageRepo
      .createQueryBuilder('backup_property_usage_details')
      .where('backup_property_usage_details.property_usage_details_id = :id', { id }) 
      .getOne();
  }
  
}
