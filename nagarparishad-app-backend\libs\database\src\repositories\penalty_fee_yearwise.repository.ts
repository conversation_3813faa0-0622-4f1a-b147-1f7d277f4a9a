import { Repository } from 'typeorm';
import { PenaltyFeeYearWiseEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from '@nestjs/common';

export class PenaltyFeeYearWiseRepository extends Repository<PenaltyFeeYearWiseEntity> {
  private readonly logger = new Logger(PenaltyFeeYearWiseRepository.name);

  constructor(
    @InjectRepository(PenaltyFeeYearWiseEntity)
    private readonly penaltyFeeYearWiseRepository: Repository<PenaltyFeeYearWiseEntity>,
  ) {
    super(
      penaltyFeeYearWiseRepository.target,
      penaltyFeeYearWiseRepository.manager,
      penaltyFeeYearWiseRepository.queryRunner,
    );
  }

  async savePenaltyFee(input: {
    property: string;
    financial_year: string;
    actual_value: number;
    tax_percentage: number;
  }): Promise<PenaltyFeeYearWiseEntity> {
    // Calculate penalty_value as actual_value * tax_percentage / 100
    const penaltyValue =
      Number(input.actual_value) * (Number(input.tax_percentage) / 100);

    // Create entity with property relation
    const penaltyFee = this.penaltyFeeYearWiseRepository.create({
      financial_year: input.financial_year,
      actual_value: input.actual_value,
      penalty_value: penaltyValue,
      tax_percentage: input.tax_percentage,
      property: { property_id: input.property },
    });

    return await this.penaltyFeeYearWiseRepository.save(penaltyFee);
  }

  async findAllPenaltyFees() {
    return await this.penaltyFeeYearWiseRepository
      .createQueryBuilder('penalty_fee_year_wise')
      .leftJoinAndSelect('penalty_fee_year_wise.property', 'property')
      .orderBy('penalty_fee_year_wise.updatedAt', 'DESC')
      .getMany();
  }

  async findPenaltyFeeById(id: string) {
    return await this.penaltyFeeYearWiseRepository
      .createQueryBuilder('penalty_fee_year_wise')
      .leftJoinAndSelect('penalty_fee_year_wise.property', 'property')
      .where('penalty_fee_year_wise.penalty_fee_id = :penalty_fee_id', {
        penalty_fee_id: id,
      })
      .getOne();
  }

  async updatePenaltyFee(
    id: string,
    input: {
      property?: string;
      financial_year?: string;
      actual_value?: number;
      tax_percentage?: number;
      penalty_value?: number;
    },
    calculatePenaltyValue: boolean = true,
  ) {
    // Always retrieve the existing penalty record to get current values
    const existingPenalty = await this.penaltyFeeYearWiseRepository.findOne({
      where: { penalty_fee_id: id },
    });

    if (!existingPenalty) {
      throw new Error(`Penalty fee with ID ${id} not found`);
    }

    // Determine the actual_value to use for penalty calculation
    // If actual_value is provided in input, use it; otherwise use existing value
    const actualValueForCalculation =
      input.actual_value !== undefined
        ? input.actual_value
        : existingPenalty.actual_value;

    // Determine the tax_percentage to use for penalty calculation
    // If tax_percentage is provided in input, use it; otherwise use existing value
    const taxPercentageForCalculation =
      input.tax_percentage !== undefined
        ? input.tax_percentage
        : existingPenalty.tax_percentage;

    // Only recalculate penalty_value if calculatePenaltyValue is true and penalty_value is not explicitly provided
    if (calculatePenaltyValue && input.penalty_value === undefined) {
      input.penalty_value =
        Number(actualValueForCalculation) *
        (Number(taxPercentageForCalculation) / 100);
    }

    // Handle property as a relation
    let updateData: any = { ...input };
    if (input.property) {
      updateData = {
        ...updateData,
        property: { property_id: input.property },
      };
      delete updateData.property;
    }
    console.log('updatedData', updateData);
    return await this.penaltyFeeYearWiseRepository
      .createQueryBuilder('penalty_fee_year_wise')
      .update(PenaltyFeeYearWiseEntity)
      .set(updateData)
      .where('penalty_fee_id = :penalty_fee_id', { penalty_fee_id: id })
      .execute();
  }

  async updatePenaltyWithPayment(
  id: string,
  paymentAmount: number,
) {
  // Retrieve the existing penalty record
  const existingPenalty = await this.penaltyFeeYearWiseRepository.findOne({
    where: { penalty_fee_id: id },
  });

  if (!existingPenalty) {
    throw new Error(`Penalty fee with ID ${id} not found`);
  }

  // Get current values
  const actualValue = Number(existingPenalty.actual_value);
  const penaltyValue = Number(existingPenalty.penalty_value);
  const taxPercentage = Number(existingPenalty.tax_percentage);

  // Calculate how much of the payment can be deducted from the penalty value
  const paymentFromPenalty = Math.min(paymentAmount, penaltyValue);
  let remainingPayment = paymentAmount - paymentFromPenalty;

  // Calculate new penalty value
  const newPenaltyValue = Math.max(0, penaltyValue - paymentFromPenalty);

  // If there's remaining payment, deduct it from the actual value
  const paymentFromActual = Math.min(remainingPayment, actualValue);
  const newActualValue = Math.max(0, actualValue - paymentFromActual);

  // Update the penalty record
  const updateData = {
    actual_value: newActualValue,
    penalty_value: newPenaltyValue,
  };

  return await this.penaltyFeeYearWiseRepository
    .createQueryBuilder('penalty_fee_year_wise')
    .update(PenaltyFeeYearWiseEntity)
    .set(updateData)
    .where('penalty_fee_id = :penalty_fee_id', { penalty_fee_id: id })
    .execute();
}

  async deletePenaltyFee(id: string) {
    return await this.penaltyFeeYearWiseRepository
      .createQueryBuilder('penalty_fee_year_wise')
      .softDelete()
      .where('penalty_fee_id = :penalty_fee_id', { penalty_fee_id: id })
      .execute();
  }

  async handlePercentageIncrement() {
    const penaltyFees = await this.findAllPenaltyFees();

    for (const penaltyFee of penaltyFees) {
      // Fix the calculation - add 2% to the percentage
      penaltyFee.tax_percentage += 2;

      // Note: We don't increase the actual_value, only the tax_percentage changes
      // The actual_value remains the same

      // Recalculate penalty_value based on actual_value and new tax_percentage
      // This ensures penalty_value is always actual_value * tax_percentage / 100
      penaltyFee.penalty_value =
        Number(penaltyFee.actual_value) *
        (Number(penaltyFee.tax_percentage) / 100);

      await this.penaltyFeeYearWiseRepository.save(penaltyFee);
    }

    return penaltyFees;
  }

  async findPenaltyFeesByPropertyId(
    propertyId: string,
    financialYear?: string,
  ) {
    let query = this.penaltyFeeYearWiseRepository
      .createQueryBuilder('penalty_fee_year_wise')
      .leftJoinAndSelect('penalty_fee_year_wise.property', 'property')
      .where('property.property_id = :propertyId', { propertyId });

    if (financialYear) {
      query = query.andWhere(
        'penalty_fee_year_wise.financial_year = :financialYear',
        { financialYear },
      );
    }

    return query.getMany();
  }
}
