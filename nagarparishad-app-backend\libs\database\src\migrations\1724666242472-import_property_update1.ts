import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportPropertyUpdate11724666242472 implements MigrationInterface {
    name = 'ImportPropertyUpdate11724666242472'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "ward_number" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "owner_name" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "owner_type" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "bhogawat_owner_name" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "usage_type" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "usage_desc" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "construction_year" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "length" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "width" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "sq_ft" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "sq_meter" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "sq_meter" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "sq_ft" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "width" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "length" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "construction_year" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "usage_desc" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "usage_type" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "bhogawat_owner_name" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "owner_type" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "owner_name" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "import_property" ALTER COLUMN "ward_number" SET NOT NULL`);
    }

}
