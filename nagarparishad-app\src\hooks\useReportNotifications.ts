import { useCallback } from 'react';
import { useNotifications } from '@/context/NotificationContext';
import { toast } from '@/hooks/use-toast';

export const useReportNotifications = () => {
  const { addNotification } = useNotifications();

  const createReportNotification = useCallback((
    reportType: 'namuna_eight' | 'namuna_nine',
    totalRecords?: number
  ) => {
    const title = reportType === 'namuna_eight' 
      ? 'Namuna Eight Report Generation' 
      : 'Namuna Nine Report Generation';

    const notificationId = addNotification({
      title,
      message: 'Your ZIP file is being generated...',
      type: 'progress',
      status: 'pending',
      persistent: true,
      metadata: {
        reportType,
        totalRecords,
        processedRecords: 0
      }
    });

    // Show toast notification
    toast({
      title: "Report Generation Started",
      description: "Your ZIP file is being generated. You'll be notified when it's ready.",
      duration: 3000,
    });

    return notificationId;
  }, [addNotification]);

  const handleReportGeneration = useCallback(async (
    reportType: 'namuna_eight' | 'namuna_nine',
    reportFunction: (params: any, onNotificationCreated?: (id: string) => void) => Promise<any>,
    params: any,
    totalRecords?: number
  ) => {
    let notificationId: string | null = null;

    try {
      // Create notification and get the ID
      const onNotificationCreated = (id: string) => {
        notificationId = id;
      };

      // If we expect a large dataset, create notification first
      if (totalRecords && totalRecords >= (reportType === 'namuna_eight' ? 10 : 20)) {
        notificationId = createReportNotification(reportType, totalRecords);
      }

      // Call the report function
      const result = await reportFunction(params, onNotificationCreated);

      // For small datasets that return immediately, handle the download
      if (result.status && result.data) {
        // If no notification was created (small dataset), handle download directly
        if (!notificationId) {
          return result;
        }
      }

      return result;
    } catch (error) {
      console.error('Error generating report:', error);
      
      // Show error toast
      toast({
        title: "Report Generation Failed",
        description: "There was an error generating your report. Please try again.",
        variant: "destructive",
        duration: 5000,
      });

      throw error;
    }
  }, [createReportNotification]);

  return {
    createReportNotification,
    handleReportGeneration
  };
};
