import { Controller, Get, Post, Put, Delete, Body, Query, HttpException, HttpStatus } from '@nestjs/common';
import { FloorMasterService } from './floor_master.service';
import { CreateFloorMasterDto } from './dto/create-floor-master.dto';
import { UpdatePropertyTypeClassDto } from './dto/update-floor-master.dto';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@Controller('floor-master')
export class FloorMasterController {
  constructor(private readonly floorMasterService: FloorMasterService) {}

  // Create a new Floor
  
  @Form('Floor')
  @Permissions('can_write')
  @Post('create')
  async create(@Body() createFloorDto: CreateFloorMasterDto) {
    const data = await this.floorMasterService.create(createFloorDto);
    return {
      message: 'Floor created successfully',
      data,
    };
  }

  // Retrieve all Floors
  
  @Form('Floor')
  @Permissions('can_read')
  @Get()
  async findAll() {
    const data = await this.floorMasterService.getAllFloors();
    return {
      message: 'All floors retrieved successfully',
      data:data.data,
    };
  }

  // Retrieve a single Floor by ID using a query parameter
  
  @Form('Floor')
  @Permissions('can_read')
  @Get('findOne')
  async findOne(@Query('id') id: string) {
    const data = await this.floorMasterService.getFloorById(id);
    if (!data) {
      throw new HttpException('Floor not found', HttpStatus.NOT_FOUND);
    }
    return {
      message: 'Floor retrieved successfully',
      data,
    };
  }

  // Update a Floor by ID using a query parameter
  
  @Form('Floor')
  @Permissions('can_update')
  @Put('update')
  async update(@Query('id') id: string, @Body() updateFloorDto: UpdatePropertyTypeClassDto) {
    const data = await this.floorMasterService.updateFloor(id, updateFloorDto);
    return {
      message: 'Floor updated successfully',
      data,
    };
  }

  // Delete a Floor by ID using a query parameter

  @Form('Floor')
  @Permissions('can_delete')
  @Delete('delete')
  async delete(@Query('id') id: string) {
    await this.floorMasterService.deleteFloor(id);
    return {
      message: 'Floor deleted successfully',
    };
  }
}
