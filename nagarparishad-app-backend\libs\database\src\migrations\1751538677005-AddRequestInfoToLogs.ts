import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddRequestInfoToLogs1751538677005 implements MigrationInterface {
    name = 'AddRequestInfoToLogs1751538677005'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.addColumn("logs", new TableColumn({
            name: "ip_address",
            type: "varchar",
            length: "45",
            isNullable: true,
        }));
        await queryRunner.addColumn("logs", new TableColumn({
            name: "user_agent",
            type: "text",
            isNullable: true,
        }));
        await queryRunner.addColumn("logs", new TableColumn({
            name: "request_url",
            type: "varchar",
            length: "255",
            isNullable: true,
        }));
        await queryRunner.addColumn("logs", new TableColumn({
            name: "request_method",
            type: "varchar",
            length: "10",
            isNullable: true,
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn("logs", "request_method");
        await queryRunner.dropColumn("logs", "request_url");
        await queryRunner.dropColumn("logs", "user_agent");
        await queryRunner.dropColumn("logs", "ip_address");
    }
}
