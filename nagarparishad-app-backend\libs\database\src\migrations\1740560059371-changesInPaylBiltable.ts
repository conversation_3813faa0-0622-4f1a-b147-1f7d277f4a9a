import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangesInPaylBiltable1740560059371 implements MigrationInterface {
    name = 'ChangesInPaylBiltable1740560059371'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "all_property_tax_sum_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "all_property_tax_sum_curr" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_1_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_1_curr" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_2_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_2_curr" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_3_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_3_curr" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_4_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_4_curr" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_5_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_5_curr" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_6_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_6_curr" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_7_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_7_curr" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_8_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_8_curr" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_9_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_9_curr" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_10_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "tax_type_10_tax_type_9_" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "other_tax_sum_tax_prev" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD "other_tax_sum_tax_curr" double precision DEFAULT '0'`);
     
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
   
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "other_tax_sum_tax_curr"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "other_tax_sum_tax_prev"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_10_tax_type_9_"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_10_prev"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_9_curr"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_9_prev"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_8_curr"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_8_prev"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_7_curr"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_7_prev"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_6_curr"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_6_prev"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_5_curr"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_5_prev"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_4_curr"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_4_prev"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_3_curr"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_3_prev"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_2_curr"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_2_prev"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_1_curr"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "tax_type_1_prev"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "all_property_tax_sum_curr"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP COLUMN "all_property_tax_sum_prev"`);
    }

}
