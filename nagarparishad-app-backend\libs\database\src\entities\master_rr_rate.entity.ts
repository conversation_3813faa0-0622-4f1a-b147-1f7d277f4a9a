import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    JoinColumn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    ManyToOne,
    OneToMany,
  } from 'typeorm';
  import { ZoneMaster } from './zoneMaster.entity';
  import { ReassessmentRange } from './reassesment_range.entity';


  @Entity('master_rr_rate')
  export class Master_rr_rateEntity extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    rr_rate_id: string;

    @ManyToOne(() => ZoneMaster, (zone) => zone.zone_id)
    @JoinColumn({ name: 'zone_id' })
    zone: ZoneMaster;


    @Column({ type: String, nullable: true })
    financial_year: string;

    @Column({ type: 'float', nullable: false , default: 0})
    value: number;


    @Column({ type: String, nullable: true, default: 'Active' })
    status: string;

    @ManyToOne(() => ReassessmentRange, (reassessmentRange) => reassessmentRange.reassessment_range_id, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn({ name: 'reassessment_range_id' })
    reassessmentRange: ReassessmentRange;


    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;

    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;

    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
  }

