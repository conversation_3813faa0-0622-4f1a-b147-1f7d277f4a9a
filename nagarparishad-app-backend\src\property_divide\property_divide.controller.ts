import { Controller, Get, Post, Body, Param, Query, ValidationPipe } from '@nestjs/common';
import { PropertyDivideService } from './property_divide.service';
import { PropertyEntity } from 'libs/database/entities/';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Property Divide')
@Controller('property-divide')
export class PropertyDivideController {
  constructor(private readonly propertyDivideService: PropertyDivideService) {}

  @ApiOperation({ summary: 'Check if a property number is unique' })
  @ApiResponse({ status: 200, description: 'Returns whether the property number is unique' })
  @Post('check-numbers')
  async checkNewPropertyNumbers(@Body('propertyNumbers') propertyNumbers: []) {
    return await this.propertyDivideService.checkNewPropertyNumbers(propertyNumbers);
  }

  @ApiOperation({ summary: 'Divide a property' })
  @ApiResponse({ status: 201, description: 'New property created successfully' })
  @ApiResponse({ status: 400, description: 'Property number already exists' })
  @ApiResponse({ status: 404, description: 'Original property not found' })
  @Post('divide/:originalPropertyId')
  async divideProperty(
    @Param('originalPropertyId') originalPropertyId: string,
    @Body(new ValidationPipe()) newPropertyData: Partial<PropertyEntity>
  ) {
    return await this.propertyDivideService.divideProperty(originalPropertyId, newPropertyData);
  }


  @ApiOperation({ summary: 'Divide a property' })
  @ApiResponse({ status: 201, description: 'New property created successfully' })
  @ApiResponse({ status: 400, description: 'Property number already exists' })
  @ApiResponse({ status: 404, description: 'Original property not found' })
  @Get('divideAndGenrateNumber')
  async genratePropertyNumbers(
    @Query() query: any,
  ) {
    return await this.propertyDivideService.genratePropertyNumbers(query.number, query.propertyNumber);
  }
  
  @ApiOperation({ summary: 'Create new food properties' })
  @ApiResponse({ status: 201, description: 'Food properties created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @Post('create-divide-properties')
  async createDivideProperties(@Body(new ValidationPipe()) foodPropertyData: any) {
    return await this.propertyDivideService.createFodProperties(foodPropertyData);
  }
}
