export interface AreaMasterObject {
  area_id: string;
  areaName: string;
  createdAt?: string;
  updatedAt?: string;
  deleteAt?: string | null;
}

export interface AreaListAllApi {
  statusCode: number;
  message: string;
  data: AreaMasterObject[];
}

export interface AreaCreateApi {
  statusCode: number;
  message: string;
  data: AreaMasterObject;
}
export interface AreaUpdateApi {
  statusCode: number;
  message: string;
}

export interface AreaSendApiObj {
  areaName: string;
}
