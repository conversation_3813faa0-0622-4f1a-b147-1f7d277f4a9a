import WhiteContainer from "@/components/custom/WhiteContainer";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { MASTER } from "@/constant/config/api.config";
import { ColumnDef } from "@tanstack/react-table";
import { t } from "i18next";
import { ArrowUpDown, CalendarIcon } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import DialogDemo from "@/components/globalcomponent/GlobalDialog";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/components/ui/use-toast";
import { z } from "zod";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import DemandViewDetails from "./DemandViewDetails";
import { useTaxController } from "@/controller/tax/TaxListController";
import { TaxUserByFYInterface } from "@/model/tax/taxUserDetailsByFYInterfaces";
import { useTranslation } from "react-i18next";
import PublisPopUpScreen from "@/components/custom/PublisPopUpScreen";
import { Tooltip } from "recharts";
import { useNavigate } from "react-router-dom";
import Api from "@/services/ApiServices";
import { useTaxCalculateController } from "@/controller/tax/TaxCalculateController";

const FormSchema = z.object({
  yearRange: z.string({
    required_error: "A financial year range is required.",
  }),
});

// year range peacker

const DemandComponent = () => {
  const demandRef = useRef(null);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const getLast10YearRanges = () => {
    const currentYear = new Date().getFullYear();
    let yearRanges = [];
    for (let i = 0; i < 2; i++) {
      const startYear = currentYear - i;
      const endYear = startYear + 1;
      yearRanges.push(`${startYear}-${endYear}`);
    }
    return yearRanges;
  };
  const yearRanges = getLast10YearRanges();

  const [selectedYearRange, setSelectedYearRange] = useState(null);
  const [financialYears, setFinancialYears] = useState([]);

  useEffect(() => {
    fetchFinancialYears();
  }, []);

  const fetchFinancialYears = async () => {
    try {
      const response = await Api.fyYears();
      if (response.data.statusCode === 200 && response.data.data) {
        setFinancialYears(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching financial years:", error);
      toast({
        title: "Failed to fetch financial years.",
        variant: "destructive",
      });
    }
  };
  // const data = [
  //   { year: "2023-2024", progress: "50", count: "5000" },
  //   { year: "2022-2023", progress: "100", count: "9000" },
  //   { year: "2021-2022", progress: "100", count: "9000" },
  //   { year: "2020-2021", progress: "100", count: "9000" },
  //   { year: "2019-2020", progress: "100", count: "9000" },
  //   { year: "2018-2019", progress: "100", count: "9000" },
  //   { year: "2017-2018", progress: "100", count: "9000" },
  //   { year: "2016-2017", progress: "100", count: "9000" },
  //   { year: "2015-2016", progress: "100", count: "9000" },
  //   { year: "2014-2015", progress: "100", count: "9000" },
  //   { year: "2013-2014", progress: "100", count: "9000" },
  // ];

  const [isPublished, setIsPublished] = useState(true); // Assuming it starts as published
  const [indexNo, setIndexNo] = useState<number>(100); // Assuming it starts as published
  const [isPublishOpen, setisPublishOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any | null>(null);
  const [publishId, setPublishId] = useState<any | null>(null);
  const [publishStatus, setPublishStatus] = useState<any | null>(null);
  const [selectedFinancialYear, setSelectedFinancialYear] = useState("");
  const [generatingRecords, setGeneratingRecords] = useState(false);
  const { changePublishStatus } = useTaxController();
  const { generateBill: generateBillMutation, isGeneratingBill } = useTaxCalculateController();
  const handleToggle = (item: any) => {
    setPublishId(item?.tax_fy_records_id);

    setPublishStatus(item?.is_published);

    if (item?.tax_fy_records_id) {
      console.log(
        "Clicked item ID:",
        item.tax_fy_records_id,
        "is_published:",
        item.is_published
      );
    } else {
      console.log("ID not found on the clicked item");
    }
    setSelectedItem(item?.is_published);
    setisPublishOpen(true);
  };

  const handleCancelPublish = () => {
    setisPublishOpen(false);
    setSelectedItem(null);
  };
  const handleConfirmSubmit = () => {
    const newPublishStatus = !publishStatus;

    changePublishStatus({
      publishId: publishId,
      publishStatus: newPublishStatus,
    });
    console.log("publishId:", publishId, "publishStatus:", newPublishStatus);
    setisPublishOpen(false);
    setIsPublished((prevState) => !prevState);
  };
  const { yearData, yearDataLoading,refetchYearData } = useTaxController()

  const generateBill = async () => {
    if (!selectedFinancialYear) {
      toast({
        title: `${t("selectFinancialYear")}`,
        variant: "destructive", // Shows red toast for missing financial year
      });
      return;
    }

    try {
      const data = await generateBillMutation(selectedFinancialYear);
      if (data.statusCode === 201) {
        toast({
          title: `${t("billGenerate")}`,
          variant: "success",
        });
        await refetchYearData();
      } else {
        throw new Error(data.message || "Failed to generate bill");
      }
    } catch (error) {
      console.error("Error generating bill:", error);
      toast({
        title: "Failed to generate bill",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      handleCloseNew();
    }
  };

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    toast({
      title: "You submitted the following values:",
      description: (
        <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
          <code className="text-white">{JSON.stringify(data, null, 2)}</code>
        </pre>
      ),
    });
    handleCloseNew();
  }

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "zone",
      header: `${t("SrNo")}`,
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.index + 1}</div>
      ),
    },
    {
      accessorKey: "financial_year",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
          >
            {t("taxDemand.year")}
          </Button>
        );
      },

      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.original?.financial_year}</div>
      ),
    },
    {
      accessorKey: "Progress",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("taxDemand.progress")}
            {/* <ArrowUpDown className="ml-2 h-4 w-4" /> */}
          </Button>
        );
      },
      cell: ({ row }: { row: any }) => {
        const totalProperty = row?.original?.total_property || 1;
        const totalPropertyProcessed =
          row?.original?.total_property_processed || 1;
        const per = (totalPropertyProcessed * 100) / totalProperty;
        console.log("percentage", per);
        return (
          <div className="w-full bg-zinc-200 rounded-full h-2.5">
            {row?.index === 0 ? (
              <Skeleton className={`h-2.5 bg-[#238c41] w-[60%]`} />
            ) : (
              <div
                className="bg-[#238c41] h-2.5 rounded-full"
                style={{ width: `${per}%` }}
              ></div>
            )}
          </div>
        );
      },
    },

    {
      accessorKey: "Count",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
          >
            {t("taxDemand.propertyCount")}
          </Button>
        );
      },

      cell: ({ row }: { row: any }) => (
        <div className="capitalize">
          {row.original?.total_properties}
        </div>
      ),
    },
    {
      accessorKey: "warshiK_car_count",
      header: t("taxDemand.warshiKCarCount"),
      cell: ({ row }) => <div>{row.original.warshiK_car_count}</div>,
    },
    {
      accessorKey: "bills_generated",
      header: t("taxDemand.billsGenerated"),
      cell: ({ row }) => <div>{row.original.bills_generated}</div>,
    },
    {
      accessorKey: `${t("Actions")}`,
      enableHiding: false,
      cell: ({ row }: { row: any }) => (
        <div className="flex space-x-2">
          <Button
            variant="submit"
            className="h-8 w-fit p-2 justify-center"
            onClick={() => handleViewOpen(row?.original)}
          >
            {t("viewBill")}
            {/* <SquarePen className="h-4 w-4" /> */}
          </Button>

          <Button
            onClick={() => {
              handleToggle(row?.original);
            }}
            className="h-8 w-8 p-0 justify-center ml-2  !border-BlueText "
          >
            {row?.original.is_published
              ? `${t("taxDemand.unPublish")}`
              : `${t("taxDemand.publish")}`}
          </Button>
        </div>
      ),
    },
  ];

  const [newDialogOpen, setNewDialogOpen] = useState(false);
  function handleOpenNew(item: any) {
    setNewDialogOpen(true);
  }

  function handleCloseNew() {
    setNewDialogOpen(false);
  }

  const [demandView, setDemandView] = useState(false);
  const [propertyYear, setPropertyYear] = useState<any[]>([]);
  function handleViewOpen(item: any[]) {
    // setDemandView(true);
    // setPropertyYear(item?.financial_year);
    navigate("/property/property-demand-view", {
      state: item?.financial_year,
    });
  }

  function handleCloseView() {
    setDemandView(false);
  }

  console.log("year data", yearData);

  return (
    <div className="flex h-fit ">
      <div
        className=" w-full mx-auto px-4 sm:px-6 lg:px-5 py-6  "
        ref={demandRef && demandRef}
      >
        <h1 className="text-2xl font-semibold font-Poppins   w-full ml-3 ">
          {t("taxDemand.taxDemand")}
        </h1>
        <div>
          <WhiteContainer>
            <div className="flex justify-end">
              <Button onClick={handleOpenNew}>
                {" "}
                {t("taxDemand.generateBill")}
              </Button>
            </div>
            <TanStackTable
              columns={columns}
              data={yearData}
              masterType={MASTER.ZONE}
              loader={yearDataLoading ? true : false}
            />
          </WhiteContainer>
        </div>
      </div>
      {isPublishOpen && (
        <PublisPopUpScreen
          isOpen={isPublishOpen}
          toggle={handleCancelPublish}
          itemName={selectedItem}
          onSubmit={handleConfirmSubmit}
        />
      )}
      {newDialogOpen && (
        <DialogDemo
          title={"Generate Bill"}
          isOpen={newDialogOpen}
          toggle={handleCloseNew}
          classname="w-[550px]"
        >
          <div>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(generateBill)}
                className="space-y-2"
              >
                <FormLabel> {t("selectFinancialYear")}</FormLabel>

                {/* <div className="flex md:justify-end justify-end items-center flex-wrap">
                  <div className="">
                    <FormField
                      control={form.control}
                      name="fromDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <Popover>
                            <PopoverTrigger asChild className="mt-1">
                              <FormControl className="bg-white">
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "w-[240px] pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "MM-dd-yyyy")
                                  ) : (
                                    <span>{"MM-DD-YYYY"}</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <Calendar
                                className="dashboard-calender"
                                mode="single"
                                selected={field.value}
                                onSelect={(date) => {
                                  field.onChange(date);
                                  setselectedDate(date);
                                }}
                                disabled={(date) =>
                                  date > new Date() ||
                                  date < new Date("1900-01-01")
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="ml-5">
                    <FormField
                      control={form.control}
                      name="toDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <Popover>
                            <PopoverTrigger asChild className="mt-1">
                              <FormControl className="bg-white">
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "w-[240px] pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "MM-dd-yyyy")
                                  ) : (
                                    <span>{"MM-DD-YYYY"}</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <Calendar
                                className="dashboard-calender"
                                mode="single"
                                selected={field.value}
                                onSelect={(date) => {
                                  field.onChange(date);
                                }}
                                disabled={(date) =>
                                  date > new Date() ||
                                  date < new Date("1900-01-01") ||
                                  (selectedDate && date < selectedDate)
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </FormItem>
                      )}
                    />
                  </div>
    
                  

                </div> */}

                <div className="flex md:justify-end justify-end items-center flex-wrap">
                  <FormField
                    control={form.control}
                    name="yearRange"
                    render={({ field }) => (
                      <Select
                        onValueChange={(value) => {
                          setSelectedFinancialYear(value);
                          field.onChange(value);
                        }}
                        value={selectedFinancialYear}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder={t("selectYear")} />
                        </SelectTrigger>
                        <SelectContent>
                          {financialYears.map((year) => (
                            <SelectItem
                              key={year.financial_year_range}
                              value={year.financial_year_range}
                            >
                              {year.financial_year_range}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>

                {/* <div>
                  <div className="flex flex-col w-full px-2 max-md:w-full">
                    <h2 className="text-center text-xl font-semibold mb-1 pt-1">
                      {t("paymentform.title")}
                      Please Confirm Rates
                    </h2>
                    <div className="">
                      <Table className="min-w-full border-collapse border border-zinc-400 dark:border-zinc-600">
                        <TableHeader>
                          <TableRow className="bg-zinc-200 dark:bg-zinc-700">
                            <TableHead className="border border-zinc-400 dark:border-zinc-600 px-4 py-2 text-black font-bold">
                              {t("paymentform.taxDetailsHeader")}
                            </TableHead>
                            <TableHead className="border border-zinc-400 dark:border-zinc-600 px-4 py-2 text-black font-bold">
                              {t("paymentform.amountHeader")}
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              {t("paymentform.streetLightTax")}
                            </TableCell>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              60.00
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              {t("paymentform.healthTax")}
                            </TableCell>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              120.00
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              {t("paymentform.drainageTax")}
                            </TableCell>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              46.00
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              {t("paymentform.treeTax")}
                            </TableCell>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              50.00
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              {t("paymentform.educationTax")}
                            </TableCell>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              80.00
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              {t("paymentform.employmentGuaranteeTax")}
                            </TableCell>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              30.00
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              {t("paymentform.fireTax")}
                            </TableCell>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              40.00
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              {t("paymentform.waterTax")}
                            </TableCell>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              50.00
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              {t("paymentform.solidWasteTax")}
                            </TableCell>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              20.00
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              {t("paymentform.penaltyTax")}
                            </TableCell>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              30.00
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              {t("paymentform.penalty")}
                            </TableCell>
                            <TableCell className="border border-zinc-400 dark:border-zinc-600 px-4 py-2">
                              20.00
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </div> */}
                <div className=" flex justify-end">
                  <Button className="ml-2" disabled={isGeneratingBill}>
                    {" "}
                    {/* {t("taxDemand.generateBill")} */}
                    {isGeneratingBill
                      ? t("Generating")
                      : t("taxDemand.generateBill")}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </DialogDemo>
      )}
      {demandView && (
        <DialogDemo
          isOpen={demandView}
          toggle={handleCloseView}
          classname=" min-w-fit h-full"
        >
          <DemandViewDetails year={financialYears} />
        </DialogDemo>
      )}
    </div>
  );
};

export default DemandComponent;
