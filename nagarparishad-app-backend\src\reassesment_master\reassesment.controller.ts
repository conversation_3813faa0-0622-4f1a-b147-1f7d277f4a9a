import {
    Controller,
    Get,
    Post,
    Body,
    Put,
    Patch,
    Param,
    Delete,
    Query,
    ValidationPipe,
  } from '@nestjs/common';

  import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
  import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';
import { ReassessmentRangeService } from './reassesment.service';
import { CreateReassessmentRangeDto, ReassessmentRangeIdDto, UpdateReassessmentRangeDto } from './dto/reassessment-range.dto';
  
  @ApiTags('Reassessment Range')
  @Controller('reassessment-range')
  export class ReassessmentRangeController {
    constructor(
      private readonly reassessmentRangeService: ReassessmentRangeService,
    ) {}
  
    @Form('ReassessmentRange')
    @Permissions('can_write')
    @ApiOperation({ summary: 'Create a new Reassessment Range' })
    @ApiResponse({
      status: 201,
      description: 'The Reassessment Range has been successfully created',
    })
    @Post()
    create(@Body() createReassessmentRangeDto: CreateReassessmentRangeDto) {
      console.log(createReassessmentRangeDto);

      return this.reassessmentRangeService.create(createReassessmentRangeDto);
    }
  
  
    @ApiOperation({ summary: 'Get all Reassessment Ranges' })
    @ApiResponse({ status: 200, description: 'Returns all Reassessment Ranges' })
    @ApiResponse({ status: 404, description: 'Not found' })
    @Get()
    findAll() {
      return this.reassessmentRangeService.findAll();
    }
  
  
    @ApiOperation({ summary: 'Get one Reassessment Range' })
    @ApiResponse({ status: 200, description: 'Returns Single Reassessment Range' })
    @ApiResponse({ status: 404, description: 'Not found' })
    @Get('getOne')
    findOne(@Query() reassessmentRange: ReassessmentRangeIdDto) {
      return this.reassessmentRangeService.findOne(reassessmentRange);
    }
  
    @Form('ReassessmentRange')
    @Permissions('can_update')
    @ApiOperation({ summary: 'Update a Reassessment Range by ID' })
    @ApiResponse({
      status: 200,
      description: 'The Reassessment Range has been successfully updated',
    })
    @ApiResponse({ status: 404, description: 'Reassessment Range not found' })
    @Patch()
    update(
      @Query() reassessmentRange: ReassessmentRangeIdDto,
      @Body() updateReassessmentRangeDto: UpdateReassessmentRangeDto,
    ) {
      return this.reassessmentRangeService.update(
        reassessmentRange,
        updateReassessmentRangeDto,
      );
    }
  
    @Form('ReassessmentRange')
    @Permissions('can_delete')
    @ApiOperation({ summary: 'Delete a Reassessment Range by ID' })
    @ApiResponse({
      status: 200,
      description: 'The Reassessment Range has been successfully deleted',
    })
    @ApiResponse({ status: 404, description: 'Reassessment Range not found' })
    @Delete()
    remove(@Query() reassessmentRange: ReassessmentRangeIdDto) {
      return this.reassessmentRangeService.remove(reassessmentRange);
    }
  }
  