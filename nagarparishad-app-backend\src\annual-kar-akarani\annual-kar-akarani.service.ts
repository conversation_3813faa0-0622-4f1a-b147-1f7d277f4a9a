import { Injectable, NotFoundException } from '@nestjs/common';
import {
  Financial_yearRepository,
  MilkatKareRepository,
  MilkatKarTaxeRepository,
  PaidDataRepository,
  Property_Owner_DetailsRepository,
  DemandRecordDataRepository,
  PropertyMasterRepository,
  TaxPendingDuesRepository,
  PenaltyFeeYearWiseRepository,
  ReassessmentRangeRepository,
  WardMasterRepository,
} from 'libs/database/repositories';
import { WarshikKarTaxRepository } from 'libs/database/repositories/warshik-kar-tax.entity';
import { WarshikKarRepository } from 'libs/database/repositories/warshikKar.repository';
import { Property_Usage_DetailsRepository } from 'libs/database/repositories';
import { UpdateTaxDetailsDto } from './dto/update-tax-types.dto';
import {
  DemandReportData,
  Financial_year,
  TaxPendingDuesEntity,
  WarshilKarEntity,
} from 'libs/database/entities';
import { TAX_TYPES } from '@helper/helpers/tax-types.helper';
import { previousDay } from 'date-fns';
import { retry } from 'rxjs';

enum TaxTypeEnum {
  all_property_tax_sum_total = 'house_tax',
  tax_type_6 = 'electricity_bill',
  tax_type_7 = 'health_tax',
  tax_type_8 = 'padsar',
  tax_type_9 = 'penalty_amount',
  tax_type_5 = 'shasti_fee',
  tax_type_4 = 'ghan_kachara', // (Ghan Kachara)
  TOTAL = 'total',
}

@Injectable()
export class AnnualKarAkaraniService {
  constructor(
    private readonly milkatKarAkarani: MilkatKareRepository,
    private readonly warShikKarAkarani: WarshikKarRepository,
    private readonly warshikarTaxAkarani: WarshikKarTaxRepository,
    private readonly milkatKarTaxAkarani: MilkatKarTaxeRepository,
    private readonly propertyMasterRepository: PropertyMasterRepository,
    private readonly taxPendingDuesRepository: TaxPendingDuesRepository,
    private readonly propertyUsageDetailsRepository: Property_Usage_DetailsRepository,
    private readonly paidDataRepo: PaidDataRepository,
    private readonly demandRecordRepo: DemandRecordDataRepository,
    private readonly financialYearRepository: Financial_yearRepository,
    private readonly propertyOwnerRepo: Property_Owner_DetailsRepository,
    private readonly penaltyFeeYearWiseRepository: PenaltyFeeYearWiseRepository,
    private readonly reAssesmentRepo: ReassessmentRangeRepository,
    private readonly wardMasterRepository: WardMasterRepository,
  ) {}
private static sanitizeNumbers(obj: any) {
  for (const key in obj) {
    if (typeof obj[key] === 'number' && isNaN(obj[key])) {
      obj[key] = 0;
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      AnnualKarAkaraniService.sanitizeNumbers(obj[key]);
    }
  }
  return obj;
}

  async processWarshikKarAkarani(params: any) {
    try {
      //Fetch All MilkatKar
      console.log("paramss--->", params)
      const { fy, milkatKar_id, ward, propertyNumber, oldPropertyNumber, ownerName } = params;
      //const getData = await this.milkatKarAkarani.getMilkatKar(milkatKar_id);
      const currentReassesmentRange =
        await this.reAssesmentRepo.getCurrentReassesmentRange();
      const currentFinancialYear =
        await this.financialYearRepository.getCurrentFinancialYear();
      let getData: any[];

      if (ward) {
        // Fetch data for the specified ward
        getData = await this.milkatKarAkarani.getMilkatKarByWard(
          ward,
          currentReassesmentRange?.reassessment_range_id,
        );
      } else if (milkatKar_id) {
        // Fetch data for the specific milkatKar_id
        getData = await this.milkatKarAkarani.getMilkatKar(milkatKar_id);
      } else if (propertyNumber) {
        // Fetch data for the specific property number
        getData = await this.milkatKarAkarani.getMilkatKarBySearchCriteria(
          propertyNumber,
          'propertyNumber',
          currentReassesmentRange?.reassessment_range_id,
        );
      } else if (oldPropertyNumber) {
        // Fetch data for the specific old property number
        getData = await this.milkatKarAkarani.getMilkatKarBySearchCriteria(
          oldPropertyNumber,
          'old_propertyNumber',
          currentReassesmentRange?.reassessment_range_id,
        );
      } else if (ownerName) {
        // Fetch data for the specific owner name
        getData = await this.milkatKarAkarani.getMilkatKarBySearchCriteria(
          ownerName,
          'name',
          currentReassesmentRange?.reassessment_range_id,
        );
      } else {
        throw new Error("Either 'ward', 'milkatKar_id', 'propertyNumber', 'oldPropertyNumber', or 'ownerName' must be provided.");
      }

      if (!getData || getData.length === 0) {
        return {
          statusCode: 200,
          message: 'No Milkat Kar data found for the given parameters. Please ensure Milkat Kar is generated for the property first.',
          data: {
            totalProperties: 0,
            processedProperties: 0,
            currentWard: '',
            percentage: 0,
          }
        };
      }

      // return {
      //   data: getData,
      // };

      const insertData = getData.map((item: any) =>
        this.insertWarshikKar(item, currentFinancialYear),
      );

      await Promise.all(insertData);

      return {
        statusCode: 200,
        message: 'Warshik Kar Akarni Process Completed for all properties',
        data: {
          totalProperties: getData.length,
          processedProperties: getData.length,
          currentWard: ward || 'Selected Properties',
          percentage: 100,
        }
      };
    } catch (error) {
      throw error;
    }
  }

  async processWarshikKarAkarniByReassessmentRange(reassessmentYearId: string) {
    try {
      // Step 1: Get the reassessment range
      const reassessmentRange =
        await this.propertyMasterRepository.getReassessmentRangeById(
          reassessmentYearId,
        );
      if (!reassessmentRange) {
        throw new Error('Reassessment range not found');
      }

      // Step 2: Get the financial year
      const currentFinancialYear =
        await this.financialYearRepository.getCurrentFinancialYear();
      if (!currentFinancialYear) {
        throw new Error('Current financial year not found');
      }

      // Step 3: Get all Milkat Kar data for the reassessment range
      const milkatKarData =
        await this.milkatKarAkarani.getMilkatKarByReassessmentRange(
          reassessmentYearId,
        );
      if (!milkatKarData || milkatKarData.length === 0) {
        return {
          message: 'No Milkat Kar data found for the given reassessment range',
          processedCount: 0,
          totalCount: 0,
        };
      }

      // Step 4: Process each Milkat Kar record
      let processedCount = 0;
      for (const item of milkatKarData) {
        try {
          await this.insertWarshikKar(item, currentFinancialYear);
          processedCount++;
        } catch (error) {
          console.error(
            `Error processing Warshik Kar for Milkat Kar ID ${item.milkatKar_id}:`,
            error,
          );
          // Continue with the next item even if one fails
        }
      }

      return {
        message: `Warshik Kar Akarni Process Completed for ${processedCount} out of ${milkatKarData.length} properties`,
        processedCount,
        totalCount: milkatKarData.length,
      };
    } catch (error) {
      console.error(
        'Error processing Warshik Kar Akarni by reassessment range:',
        error,
      );
      throw error;
    }
  }

  async processWarshikKarAkaraniByFinancialYear(financialYearId: string) {
    // Use the batch processing method with default batch size of 100
    return this.processWarshikKarAkaraniByFinancialYearBatch(financialYearId);
  }

  async processWarshikKarAkaraniByFinancialYearBatch(
    financialYearId: string,
    batchSize: number = 100,
  ) {
    try {
      // Step 1: Get the financial year
      const financialYear =
        await this.financialYearRepository.getFinancialYearById(
          financialYearId,
        );
      if (!financialYear) {
        throw new Error('Financial year not found');
      }

      // Step 2: Get all properties
      const properties = await this.propertyMasterRepository.find({
        order: { sr_no: 'ASC' },
      });

      const totalProperties = properties.length;
      let processedCount = 0;
      let currentBatch = 0;

      // Get current reassessment range
      const currentReassesmentRange =
        await this.reAssesmentRepo.getCurrentReassesmentRange();

      // Process properties in batches
      while (currentBatch * batchSize < totalProperties) {
        const startIndex = currentBatch * batchSize;
        const endIndex = Math.min(
          (currentBatch + 1) * batchSize,
          totalProperties,
        );
        const propertyBatch = properties.slice(startIndex, endIndex);

        console.log(
          `Processing batch ${currentBatch + 1}: Properties ${startIndex + 1} to ${endIndex} of ${totalProperties}`,
        );

        // Process each property in the current batch
        const batchPromises = propertyBatch.map(async (property) => {
          try {
            // Get the latest Milkat Kar for this property
            const milkatKar =
              await this.milkatKarAkarani.getLatestMilkatKarByPropertyId(
                property.property_id,
                currentReassesmentRange?.reassessment_range_id,
              );
            if (milkatKar) {
              await this.insertWarshikKar(milkatKar, financialYear);
              return true; // Successfully processed
            }
            return false; // No milkat kar found
          } catch (error) {
            console.error(
              `Error processing Warshik Kar for property ID ${property.property_id}:`,
              error,
            );
            return false; // Failed to process
          }
        });

        // Wait for all properties in the current batch to be processed
        const batchResults = await Promise.all(batchPromises);
        const batchProcessedCount = batchResults.filter(
          (result) => result,
        ).length;
        processedCount += batchProcessedCount;

        console.log(
          `Batch ${currentBatch + 1} completed: ${batchProcessedCount} properties processed successfully`,
        );

        // Move to the next batch
        currentBatch++;
      }

      return {
        message: `Warshik Kar Akarni Process Completed for ${processedCount} out of ${totalProperties} properties`,
        processedCount,
        totalCount: totalProperties,
      };
    } catch (error) {
      console.error(
        'Error processing Warshik Kar Akarni by financial year in batches:',
        error,
      );
      throw error;
    }
  }

  /**
   * Process Warshik Kar for a single property
   * @param financialYearId The ID of the financial year
   * @param propertyId The ID of the property to process
   * @returns Result of the processing
   */
  async processWarshikKarForSingleProperty(
    financialYearId: string,
    propertyId: string,
  ) {
    try {
      console.log(`Processing Warshik Kar for single property ID: ${propertyId}`);

      // Step 1: Get the financial year
      const financialYear =
        await this.financialYearRepository.getFinancialYearById(
          financialYearId,
        );
      if (!financialYear) {
        throw new Error('Financial year not found');
      }

      // Step 2: Get the property
      const property = await this.propertyMasterRepository.findOne({
        where: { property_id: propertyId },
      });

      if (!property) {
        throw new Error(`Property with ID ${propertyId} not found`);
      }

      // Get current reassessment range
      const currentReassesmentRange =
        await this.reAssesmentRepo.getCurrentReassesmentRange();

      if (!currentReassesmentRange) {
        throw new Error('Current reassessment range not found');
      }

      // Get the latest Milkat Kar for this property
      const milkatKar =
        await this.milkatKarAkarani.getLatestMilkatKarByPropertyId(
          propertyId,
          currentReassesmentRange?.reassessment_range_id,
        );

      if (!milkatKar) {
        return {
          message: `No Milkat Kar data found for property ID ${propertyId}`,
          success: false,
        };
      }

      // Process the Warshik Kar for this property
      await this.insertWarshikKar(milkatKar, financialYear);

      return {
        message: `Warshik Kar Akarni Process Completed for property ${property.propertyNumber}`,
        success: true,
      };
    } catch (error) {
      console.error(
        `Error processing Warshik Kar for single property ID ${propertyId}:`,
        error,
      );
      throw error;
    }
  }

  async getWarshikKarDataByReassessmentRange(reassessmentYearId: string) {
    try {
      // Check if the reassessment range exists
      const reassessmentRange =
        await this.propertyMasterRepository.getReassessmentRangeById(
          reassessmentYearId,
        );
      if (!reassessmentRange) {
        throw new Error('Reassessment range not found');
      }

      // Get Warshik Kar data for the specified reassessment range
      // Since Warshik Kar doesn't directly have a reassessment range relation,
      // we need to get it through the property's Milkat Kar
      const milkatKarData =
        await this.milkatKarAkarani.getMilkatKarByReassessmentRange(
          reassessmentYearId,
        );
      if (!milkatKarData || milkatKarData.length === 0) {
        return {
          statusCode: 200,
          message: 'No Milkat Kar data found for the given reassessment range',
          data: {
            reassessment_range_id: reassessmentRange.reassessment_range_id,
            start_range: reassessmentRange.start_range,
            end_range: reassessmentRange.end_range,
            reassessment_range: `${reassessmentRange.start_range} to ${reassessmentRange.end_range}`,
            total_properties: 0,
            total_property_processed: 0,
            warshik_kar_count: 0,
            data: [],
          },
        };
      }

      // Get property IDs from Milkat Kar data
      const propertyIds = milkatKarData.map(
        (item) => item.property.property_id,
      );

      // Get Warshik Kar data for these properties
      const warshikKarData =
        await this.warShikKarAkarani.findByPropertyIds(propertyIds);

      // Format the result
      const formattedResult = {
        reassessment_range_id: reassessmentRange.reassessment_range_id,
        start_range: reassessmentRange.start_range,
        end_range: reassessmentRange.end_range,
        reassessment_range: `${reassessmentRange.start_range} to ${reassessmentRange.end_range}`,
        total_properties: propertyIds.length,
        total_property_processed: warshikKarData.length,
        warshik_kar_count: warshikKarData.filter(
          (item) => item.status === 'active',
        ).length,
        data: warshikKarData,
      };

      return {
        statusCode: 200,
        message: 'Warshik Kar data retrieved successfully',
        data: formattedResult,
      };
    } catch (error) {
      console.error(
        'Error retrieving Warshik Kar data by reassessment range:',
        error,
      );
      throw error;
    }
  }

  async getWarshikKarAkarni(value: string, searchOn: string, fy: string) {
    try {
      const getData = await this.propertyMasterRepository.getWarshikKar(
        value,
        searchOn,
        fy,
      );

      console.log('Fetched getData:', getData);

      const getTaxData = await this.warshikarTaxAkarani.find({
        where: {
          WarShikKar: {
            warshik_karId: getData?.warshikKar[0].warshik_karId,
          },
        },
      });

      const get__milkat_kar = await this.milkatKarAkarani.getCountOfMilkatKar(
        value,
        searchOn,
      );

      let count_get__milkat_kar = 0;
      let milkat_kar_id = '';
      if (get__milkat_kar) {
        count_get__milkat_kar = 1;
        milkat_kar_id = get__milkat_kar.milkatKar_id;
      }

      if (!getData) {
        return {
          message: 'Data Not found',
          data: {
            count_get__milkat_kar,
            milkat_kar_id,
          },
        };
      }

      // Get latest penalty data for the property
      if (getData.warshikKar && getData.warshikKar.length > 0) {
        const propertyId = getData.property_id;

        const penaltyFees =
          await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
            propertyId,
          );

        if (penaltyFees && penaltyFees.length > 0) {
          let currentPenaltyValue = 0;
          let previousPenaltySum = 0;
          let currentActualValue = 0;
          let previousActualValue = 0;

          // Loop through all penalties
          penaltyFees.forEach((penalty) => {
            if (penalty.financial_year === fy) {
              currentPenaltyValue = Number(penalty.penalty_value || 0);
              currentActualValue = Number(penalty.actual_value || 0);
            } else {
              previousPenaltySum += Number(penalty.penalty_value || 0);
              previousActualValue += Number(penalty.actual_value || 0);
            }
          });

          // Round values
          currentPenaltyValue = Math.round(currentPenaltyValue);
          previousPenaltySum = Math.round(previousPenaltySum);
          currentActualValue = Math.round(currentActualValue);
          previousActualValue = Math.round(previousActualValue);
          

          console.log('Penalty Breakdown:');
          console.log('Current Penalty Value:', currentPenaltyValue);
          console.log('Previous Penalty Sum:', previousPenaltySum);
          console.log('Current Actual Value:', currentActualValue);
          console.log('Previous Actual Value:', previousActualValue);

          const warshikKar = getData.warshikKar[0];

          // Assign tax_type_9
          warshikKar.tax_type_9_current = currentPenaltyValue;
          warshikKar.tax_type_9_previous = previousPenaltySum;
          warshikKar.tax_type_9 = currentPenaltyValue + previousPenaltySum;

          console.log('Before adding penalties:');
          console.log('total_tax_current:', warshikKar.total_tax_current);
          console.log('total_tax_previous:', warshikKar.total_tax_previous);
          console.log('total_tax:', warshikKar.total_tax);

          // Update totals
          warshikKar.total_tax_current = Math.max(
            0,
            Math.round(
              Number(warshikKar.total_tax_current || 0) + currentPenaltyValue,
            ),
          );
          warshikKar.total_tax_previous = Math.max(
            0,
            Math.round(
              Number(warshikKar.total_tax_previous || 0) + previousPenaltySum+previousActualValue,
            ),
          );
          warshikKar.total_tax =
            warshikKar.total_tax_current + warshikKar.total_tax_previous;

          console.log('After adding penalties:');
          console.log(
            'Updated total_tax_current:',
            warshikKar.total_tax_current,
          );
          console.log(
            'Updated total_tax_previous:',
            warshikKar.total_tax_previous,
          );
          console.log('Updated total_tax:', warshikKar.total_tax);

        }
      }

      const tax_types = {
        tax_type_1: TAX_TYPES.tax_type_1,
        tax_type_2: TAX_TYPES.tax_type_2,
        tax_type_3: TAX_TYPES.tax_type_3,
        tax_type_4: TAX_TYPES.tax_type_4,
        tax_type_5: TAX_TYPES.tax_type_5,
        tax_type_6: TAX_TYPES.tax_type_6,
        tax_type_7: TAX_TYPES.tax_type_7,
        tax_type_8: TAX_TYPES.tax_type_8,
        tax_type_9: TAX_TYPES.tax_type_9,
        tax_type_10: TAX_TYPES.tax_type_10,
      };

      return {
        message: 'Data found Success',
        data: {
          ...getData,
          getTaxData,
          tax_types,
          milkat_kar_id,
        },
      };
    } catch (error) {
      console.error('Error in getWarshikKarAkarni:', error);
      throw error;
    }
  }

  async getWarshikKarAkarniForBillData(
    value: string,
    searchOn: string,
    fy: string,
  ) {
    try {
      const getData = await this.propertyMasterRepository.getWarshikKar(
        value,
        searchOn,
        fy,
      );

      if (!getData) {
        return {
          message: 'Data Not found',
          data: [],
        };
      }

      // Check if warshikKar data exists and has at least one entry
      if (!getData.warshikKar || getData.warshikKar.length === 0) {
        console.log(
          `No warshikKar data found for property ${value} (${searchOn}) for financial year ${fy}`,
        );
        return {
          message: 'No warshikKar data found for the specified financial year',
          data: {
            ...getData,
            warshikKar: [], // Return empty array for warshikKar
          },
        };
      }

      const tax_types = {
        tax_type_6: TAX_TYPES.tax_type_6, // दिवाबत्ती कर
        tax_type_7: TAX_TYPES.tax_type_7, // आरोग्य कर
        tax_type_8: TAX_TYPES.tax_type_8, // पडसर कर
        tax_type_1: TAX_TYPES.tax_type_1, // वृक्ष उपकर
        tax_type_2: TAX_TYPES.tax_type_2, // शिक्षण उपकर
        tax_type_3: TAX_TYPES.tax_type_3, // रोजगार हमी कर
        tax_type_10: TAX_TYPES.tax_type_10, // अग्निशमन फी
        tax_type_4: TAX_TYPES.tax_type_4, // घनकचरा शुल्क
        tax_type_5: TAX_TYPES.tax_type_5, // अनधिकृत शास्ती कर
        tax_type_9: TAX_TYPES.tax_type_9, // दंड
      };

      // Check if property data exists in warshikKar

      // Get the latest penalty data for the property
      const propertyId = getData.property_id;

      // Get all penalty fees for this property
      const penaltyFees =
        await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
          propertyId,
        );

      if (penaltyFees && penaltyFees.length > 0) {
        let currentPenaltyValue = 0;
        let previousPenaltySum = 0;
        let currentActualValue = 0;
        let previousActualValue = 0;

        // Calculate current and previous penalties
        penaltyFees.forEach((penalty) => {
          if (penalty.financial_year === fy) {
            // Current year penalty - use the latest calculated penalty value
            currentPenaltyValue = Number(penalty.penalty_value || 0);
            currentActualValue = Number(penalty.actual_value || 0);
          } else {
            // Previous years penalties - sum them up
            previousPenaltySum += Number(penalty.penalty_value || 0);
            previousActualValue += Number(penalty.actual_value || 0);
          }
        });

        // Round the values
        currentPenaltyValue = Math.round(currentPenaltyValue);
        previousPenaltySum = Math.round(previousPenaltySum);
        currentActualValue = Math.round(currentActualValue);
        previousActualValue = Math.round(previousActualValue);

        // Update tax_type_9 values with the latest penalty data
        getData.warshikKar[0].tax_type_9_current = currentPenaltyValue;
        getData.warshikKar[0].tax_type_9_previous = previousPenaltySum;
        getData.warshikKar[0].tax_type_9 =
          currentPenaltyValue + previousPenaltySum;

        // First, update the current and previous tax totals
        getData.warshikKar[0].total_tax_current = Math.max(
          0,
          Math.round(
            (Number(getData.warshikKar[0].total_tax_current) || 0) + (currentPenaltyValue),

          ),
        );

        getData.warshikKar[0].total_tax_previous = Math.max(
          0,
          Math.round(

              previousActualValue + previousPenaltySum,
          ),
        );

        // Then, calculate the total tax as the sum of current and previous
        getData.warshikKar[0].total_tax =
          getData.warshikKar[0].total_tax_current +
          getData.warshikKar[0].total_tax_previous;

        console.log(`Updated penalty for bill data - property ${propertyId}:
          Penalty Values - Current=${currentPenaltyValue}, Previous=${previousPenaltySum}, Total=${currentPenaltyValue + previousPenaltySum}
          Actual Values - Current=${currentActualValue}, Previous=${previousActualValue}, Total=${currentActualValue + previousActualValue}`);
      }

      console.log('here adding taxtypeee');
      return {
        message: 'Data found Success',
        data: {
          ...getData,
          tax_types,
        },
      };
    } catch (error) {
      console.error('Error in getWarshikKarAkarniForBillData:', error);
      throw error;
    }
  }

  /**
   * Get ward-wise Warshik Kar generation status
   * @param financialYearId The ID of the financial year
   * @returns Ward-wise status data
   */
  async getWardWiseWarshikKarStatus(financialYearId: string) {
    try {
      // Check if the financial year exists
          const financialYear = await this.financialYearRepository.findOne({
      where: { is_current: true },
    });

      console.log("financialYear---------------->",financialYear,financialYearId)
      if (!financialYear) {
        throw new NotFoundException('Financial year not found');
      }

      // Get all wards
      const wards = await this.wardMasterRepository.findAllWard();

      // Get ward-wise status
      const wardWiseStatus = await Promise.all(
        wards.map(async (ward) => {
          // Get total properties in this ward
          const totalProperties = await this.propertyMasterRepository.count({
            where: { ward: { ward_id: ward.ward_id } }
          });

          // Get generated Warshik Kar records for this ward and financial year
          const generatedProperties = await this.warShikKarAkarani.count({
            where: {
              property: { ward: { ward_id: ward.ward_id } },
              financial_year: financialYear.financial_year_range,
              status: 'active'
            }
          });

          const remainingProperties = totalProperties - generatedProperties;

          return {
            ward_id: ward.ward_id,
            ward_name: ward.ward_name,
            total_properties: totalProperties,
            generated_count: generatedProperties,
            remaining_count: remainingProperties,
            progress_percentage: totalProperties > 0 ? Math.round((generatedProperties / totalProperties) * 100) : 0
          };
        })
      );

      return {
        statusCode: 200,
        message: 'Ward-wise Warshik Kar status retrieved successfully',
        data: wardWiseStatus
      };
    } catch (error) {
      console.error('Error retrieving ward-wise Warshik Kar status:', error);
      throw error;
    }
  }

  /**
   * Process Warshik Kar for a specific ward
   * @param wardNumber The ward number/name
   * @param financialYearId The ID of the financial year
   * @returns Result of the processing
   */
  /**
   * Get count of properties that need Warshik Kar generation for a specific ward
   * @param wardNumber The ward number/name
   * @param financialYear The financial year
   * @returns Count of remaining properties
   */
  async getWarshikKarRemainingCount(wardNumber: string, financialYear: string) {
    try {
      // Get the current reassessment range
      const currentReassessmentRange = await this.reAssesmentRepo.getCurrentReassesmentRange();
      if (!currentReassessmentRange) {
        return {
          statusCode: 200,
          message: 'No current reassessment range found',
          data: {
            totalProperties: 0,
            remainingProperties: 0,
            completedProperties: 0,
          }
        };
      }

      // Get all Milkat Kar data for the specific ward
      const allMilkatKarData = await this.milkatKarAkarani.getMilkatKarByWard(
        wardNumber,
        currentReassessmentRange.reassessment_range_id,
      );

      if (!allMilkatKarData || allMilkatKarData.length === 0) {
        return {
          statusCode: 200,
          message: `No Milkat Kar data found for ward ${wardNumber}`,
          data: {
            totalProperties: 0,
            remainingProperties: 0,
            completedProperties: 0,
          }
        };
      }

      // Get properties that don't have Warshik Kar yet
      const pendingMilkatKarData = await this.milkatKarAkarani.getMilkatKarByWardWithoutWarshikKar(
        wardNumber,
        currentReassessmentRange.reassessment_range_id,
        financialYear
      );

      return {
        statusCode: 200,
        message: 'Ward statistics retrieved successfully',
        data: {
          wardName: wardNumber,
          totalProperties: allMilkatKarData.length,
          remainingProperties: pendingMilkatKarData.length,
          completedProperties: allMilkatKarData.length - pendingMilkatKarData.length,
        }
      };
    } catch (error) {
      console.error(`Error getting Warshik Kar remaining count for ward ${wardNumber}:`, error);
      throw error;
    }
  }

  async processWarshikKarAkaraniByWard(wardNumber: string, financialYearId: string) {
    try {
      console.log(`Processing Warshik Kar for ward: ${wardNumber}, financial year: ${financialYearId}`);

      // Step 1: Get the financial year
      const financialYear = await this.financialYearRepository.getFinancialYearById(financialYearId);
      if (!financialYear) {
        throw new Error('Financial year not found');
      }

      // Step 2: Get the current reassessment range
      const currentReassessmentRange = await this.reAssesmentRepo.getCurrentReassesmentRange();
      if (!currentReassessmentRange) {
        throw new Error('No current reassessment range found');
      }

      // Step 3: Get all Milkat Kar data for the specific ward (for counting total)
      const allMilkatKarData = await this.milkatKarAkarani.getMilkatKarByWard(
        wardNumber,
        currentReassessmentRange.reassessment_range_id,
      );

      if (!allMilkatKarData || allMilkatKarData.length === 0) {
        return {
          statusCode: 200,
          message: `No Milkat Kar data found for ward ${wardNumber}`
        };
      }

      // Step 4: Get only Milkat Kar data that doesn't have Warshik Kar yet
      const pendingMilkatKarData = await this.milkatKarAkarani.getMilkatKarByWardWithoutWarshikKar(
        wardNumber,
        currentReassessmentRange.reassessment_range_id,
        financialYear.financial_year_range
      );

      if (pendingMilkatKarData.length === 0) {
        return {
          statusCode: 200,
          message: `All properties in ward ${wardNumber} already have Warshik Kar generated`,
          data: {
            totalProperties: allMilkatKarData.length,
            processedProperties: 0,
            alreadyProcessed: allMilkatKarData.length,
            currentWard: wardNumber,
            percentage: 100,
          }
        };
      }

      // Step 5: Process only the pending properties
      const insertData = pendingMilkatKarData.map((item: any) =>
        this.insertWarshikKar(item, financialYear),
      );

      await Promise.all(insertData);

      return {
        statusCode: 200,
        message: `Warshik Kar processing completed for ${pendingMilkatKarData.length} remaining properties in ward ${wardNumber}`,
        data: {
          totalProperties: allMilkatKarData.length,
          processedProperties: pendingMilkatKarData.length,
          alreadyProcessed: allMilkatKarData.length - pendingMilkatKarData.length,
          currentWard: wardNumber,
          percentage: 100,
        }
      };
    } catch (error) {
      console.error(`Error processing Warshik Kar for ward ${wardNumber}:`, error);
      throw error;
    }
  }
  async getWarshikKarAkarniAll(params: any) {
    try {
      const { ward, value, searchOn, fy, ...options } = params;

      // Fetch data using the repository method
      const checkData: any =
        await this.propertyMasterRepository.getWarshikKarAll(
          ward,
          value,
          searchOn,
          fy,
          options,
        );

      // Fetch additional data for warshikKar
      console.log("ward", ward,"checkData",checkData,value,searchOn);
      const checkWarshikKarGenerated =
        await this.propertyMasterRepository.getWarshikKarAllcount(fy);

      // If searching by ward, get additional ward statistics
      let wardStatistics = null;
      if (searchOn === 'ward' && ward) {
        const currentReassessmentRange = await this.reAssesmentRepo.getCurrentReassesmentRange();
        if (currentReassessmentRange) {
          // Get total Milkat Kar properties in the ward
          const totalMilkatKarInWard = await this.milkatKarAkarani.getMilkatKarByWard(
            ward,
            currentReassessmentRange.reassessment_range_id,
          );

          // Get properties that don't have Warshik Kar yet
          const pendingMilkatKarInWard = await this.milkatKarAkarani.getMilkatKarByWardWithoutWarshikKar(
            ward,
            currentReassessmentRange.reassessment_range_id,
            fy
          );

          wardStatistics = {
            wardName: ward,
            totalPropertiesWithMilkatKar: totalMilkatKarInWard?.length || 0,
            propertiesWithWarshikKar: (totalMilkatKarInWard?.length || 0) - (pendingMilkatKarInWard?.length || 0),
            remainingPropertiesForWarshikKar: pendingMilkatKarInWard?.length || 0,
          };
        }
      }

      // Check if checkData is empty
      if (!checkData || checkData.data.length === 0) {
        return {
          message: 'Data Not found',
          data: checkWarshikKarGenerated,
          wardStatistics: wardStatistics,
        };
      }

      // Process each item in checkData
      for (const item of checkData.data) {
        const getPaidData =
          await this.paidDataRepo.findAndSumPaidDataByFinancialYearAndPropertyTotalSum(
            fy,
            item.propertyNumber,
          );

        item.TotalTax = getPaidData?.total_amount ?? 0; // Add tax to each property

        // Get property ID for this item
        if (item.property_id) {
          // Get all penalty fees for this property
          const penaltyFees =
            await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
              item.property_id,
            );

          if (penaltyFees && penaltyFees.length > 0) {
            let currentPenaltyValue = 0;
            let previousPenaltySum = 0;
            let currentActualValue = 0;
            let previousActualValue = 0;

            // Calculate current and previous penalties
            penaltyFees.forEach((penalty) => {
              if (penalty.financial_year === fy) {
                // Current year penalty - use the latest calculated penalty value
                currentPenaltyValue = Number(penalty.penalty_value || 0);
                currentActualValue = Number(penalty.actual_value || 0);
              } else {
                // Previous years penalties - sum them up
                previousPenaltySum += Number(penalty.penalty_value || 0);
                previousActualValue += Number(penalty.actual_value || 0);
              }
            });

            // Round the values
            currentPenaltyValue = Math.round(currentPenaltyValue);
            previousPenaltySum = Math.round(previousPenaltySum);
            currentActualValue = Math.round(currentActualValue);
            previousActualValue = Math.round(previousActualValue);

            // Add penalty information to the item
            item.tax_type_9 = currentPenaltyValue + previousPenaltySum;
            item.tax_type_9_current = currentPenaltyValue;
            item.tax_type_9_previous = previousPenaltySum;

            // Update total tax to include penalty
            if (item.total_tax !== undefined) {
              // First, update the current and previous tax totals if they exist
              if (item.total_tax_current !== undefined) {
                item.total_tax_current = Math.max(
                  0,
                  Math.round(
                    (Number(item.total_tax_current) || 0) + currentActualValue,
                  ),
                );
              }

              if (item.total_tax_previous !== undefined) {
                item.total_tax_previous = Math.max(
                  0,
                  Math.round(
                    (Number(item.total_tax_previous) || 0) +
                      previousActualValue,
                  ),
                );
              }

              // Then, calculate the total tax
              if (
                item.total_tax_current !== undefined &&
                item.total_tax_previous !== undefined
              ) {
                // If we have both current and previous, sum them
                item.total_tax =
                  item.total_tax_current + item.total_tax_previous;
              } else {
                // Otherwise, just add the actual values to the existing total
                item.total_tax = Math.max(
                  0,
                  Math.round(
                    (Number(item.total_tax) || 0) +
                      currentActualValue +
                      previousActualValue,
                  ),
                );
              }
            }

            console.log(`Updated penalty for list item - property ${item.property_id}:
              Penalty Values - Current=${currentPenaltyValue}, Previous=${previousPenaltySum}, Total=${currentPenaltyValue + previousPenaltySum}
              Actual Values - Current=${currentActualValue}, Previous=${previousActualValue}, Total=${currentActualValue + previousActualValue}`);
          }
        }
      }

      const tax_types = {
        tax_type_6: TAX_TYPES.tax_type_6, // दिवाबत्ती कर
        tax_type_7: TAX_TYPES.tax_type_7, // आरोग्य कर (corrected from 'आरोग्य क')
        tax_type_8: TAX_TYPES.tax_type_8, // पडसर कर
        tax_type_1: TAX_TYPES.tax_type_1, // वृक्ष उपकर
        tax_type_2: TAX_TYPES.tax_type_2, // शिक्षण उपकर
        tax_type_3: TAX_TYPES.tax_type_3, // रोजगार हमी कर
        tax_type_10: TAX_TYPES.tax_type_10, // अग्निशमन फी
        tax_type_4: TAX_TYPES.tax_type_4, // घनकचरा शुल्क
        tax_type_5: TAX_TYPES.tax_type_5, // अनधिकृत शास्ती कर
        tax_type_9: TAX_TYPES.tax_type_9, // दंड
      };

      const { data: checkWarshikKarGeneratedData, ...pagiantioData } =
        checkData;

      return {
        data: {
          checkData: checkWarshikKarGeneratedData,
          checkWarshikKarGenerated,
          tax_types,
          wardStatistics,
          ...pagiantioData,
        },
        message: 'Data found successfully',
        count: checkWarshikKarGenerated,
      };
    } catch (error) {
      console.error('Error in getWarshikKarAkarniAll:', error);
      throw error;
    }
  }

  async insertWarshikKar(data: any, financialYear: any) {
    //Get All MiltakarTax Data for the Milkat Id
    const getMilkatValues: any = await this.milkatKarTaxAkarani.getDataById(
      data.milkatKar_id,
    );

    let fy = financialYear?.financial_year_range;
console.log("financialYear")
    // Step 1: Update property status to "inactive" before inserting into warshik_kar
    await this.warShikKarAkarani.update(
      {
        property: data.property.property_id,
        status: 'active',
        financial_year: fy,
      },
      {
        status: 'inactive',
      },
    );

    const property = await this.propertyMasterRepository.findOne({
      where: { property_id: data.property.property_id },
    });

    // Get tax dues from tax_pending_dues table
    const taxDues = await this.taxPendingDuesRepository
      .createQueryBuilder('taxDue')
      .where(
        'taxDue.old_propertyNumber = :oldNumber AND taxDue.financial_year = :fy',
        {
          oldNumber: property.old_propertyNumber?.trim(),
          fy: financialYear?.financial_year_id,
        },
      )
      .getMany();
      console.log("taxDuestaxDues",taxDues)

    // Get current financial year - needed for context
    await this.financialYearRepository.getCurrentFinancialYear();

   const safeNumber = (val: any): number =>
  Number.isFinite(Number(val)) ? Number(val) : 0;

const prepareData = {
  financial_year: fy,

  all_property_tax_sum_total: Math.max(
    0,
    Math.round(
      safeNumber(data.all_property_tax_sum) +
        safeNumber(taxDues[0]?.all_property_tax_sum) -
        safeNumber(data?.property_type_discount),
    ),
  ),

  all_property_tax_sum_current: Math.max(
    0,
    Math.round(
      safeNumber(data.all_property_tax_sum) -
        safeNumber(data?.property_type_discount),
    ),
  ),

  all_property_tax_sum: Math.round(safeNumber(taxDues[0]?.all_property_tax_sum)),

  tax_type_1:
    safeNumber(data.tax_type_1) + Math.round(safeNumber(taxDues[0]?.tax_type_1)),
  tax_type_1_current: safeNumber(data.tax_type_1),
  tax_type_1_previous: Math.round(safeNumber(taxDues[0]?.tax_type_1)),

  tax_type_2:
    safeNumber(data.tax_type_2) + Math.round(safeNumber(taxDues[0]?.tax_type_2)),
  tax_type_2_current: safeNumber(data.tax_type_2),
  tax_type_2_previous: Math.round(safeNumber(taxDues[0]?.tax_type_2)),

  tax_type_3:
    safeNumber(data.tax_type_3) + Math.round(safeNumber(taxDues[0]?.tax_type_3)),
  tax_type_3_current: safeNumber(data.tax_type_3),
  tax_type_3_previous: Math.round(safeNumber(taxDues[0]?.tax_type_3)),

  tax_type_4:
    safeNumber(data.tax_type_4) + Math.round(safeNumber(taxDues[0]?.tax_type_4)),
  tax_type_4_current: safeNumber(data.tax_type_4),
  tax_type_4_previous: Math.round(safeNumber(taxDues[0]?.tax_type_4)),

  tax_type_5:
    safeNumber(data.tax_type_5) + Math.round(safeNumber(taxDues[0]?.tax_type_5)),
  tax_type_5_current: safeNumber(data.tax_type_5),
  tax_type_5_previous: Math.round(safeNumber(taxDues[0]?.tax_type_5)),

  tax_type_6:
    safeNumber(data.tax_type_6) + Math.round(safeNumber(taxDues[0]?.tax_type_6)),
  tax_type_6_current: safeNumber(data.tax_type_6),
  tax_type_6_previous: Math.round(safeNumber(taxDues[0]?.tax_type_6)),

  tax_type_7:
    safeNumber(data.tax_type_7) + Math.round(safeNumber(taxDues[0]?.tax_type_7)),
  tax_type_7_current: safeNumber(data.tax_type_7),
  tax_type_7_previous: Math.round(safeNumber(taxDues[0]?.tax_type_7)),

  tax_type_8:
    safeNumber(data.tax_type_8) + Math.round(safeNumber(taxDues[0]?.tax_type_8)),
  tax_type_8_current: safeNumber(data.tax_type_8),
  tax_type_8_previous: Math.round(safeNumber(taxDues[0]?.tax_type_8)),

  tax_type_9: 0,
  tax_type_9_current: 0,
  tax_type_9_previous: 0,

  tax_type_10:
    safeNumber(data.tax_type_10) + Math.round(safeNumber(taxDues[0]?.tax_type_10)),
  tax_type_10_current: safeNumber(data.tax_type_10),
  tax_type_10_previous: Math.round(safeNumber(taxDues[0]?.tax_type_10)),

  other_tax_sum_tax:
    safeNumber(data.other_tax_sum_tax) +
    Math.round(safeNumber(data?.other_tax_sum_tax_previous)),
  other_tax_sum_tax_current: safeNumber(data.other_tax_sum_tax),
  other_tax_sum_tax_previous: Math.round(safeNumber(data?.other_tax_sum_tax_previous)),

  total_tax: Math.max(
    0,
    Math.round(
      safeNumber(data.total_tax) -
        safeNumber(data?.property_type_discount) +
        Math.round(
          safeNumber(taxDues[0]?.tax_type_1) +
            safeNumber(taxDues[0]?.tax_type_2) +
            safeNumber(taxDues[0]?.tax_type_3) +
            safeNumber(taxDues[0]?.tax_type_4) +
            safeNumber(taxDues[0]?.tax_type_5) +
            safeNumber(taxDues[0]?.tax_type_6) +
            safeNumber(taxDues[0]?.tax_type_7) +
            safeNumber(taxDues[0]?.tax_type_8) +
            // no tax_type_9
            safeNumber(taxDues[0]?.tax_type_10) +
            safeNumber(taxDues[0]?.all_property_tax_sum),
        ),
    ),
  ),

  total_tax_current: Math.max(
    0,
    Math.round(safeNumber(data.total_tax) - safeNumber(data?.property_type_discount)),
  ),

  total_tax_previous: Math.round(
    safeNumber(taxDues[0]?.tax_type_1) +
      safeNumber(taxDues[0]?.tax_type_2) +
      safeNumber(taxDues[0]?.tax_type_3) +
      safeNumber(taxDues[0]?.tax_type_4) +
      safeNumber(taxDues[0]?.tax_type_5) +
      safeNumber(taxDues[0]?.tax_type_6) +
      safeNumber(taxDues[0]?.tax_type_7) +
      safeNumber(taxDues[0]?.tax_type_8) +
      safeNumber(taxDues[0]?.tax_type_10) +
      safeNumber(taxDues[0]?.all_property_tax_sum),
  ),

  property_type_discount: safeNumber(data?.property_type_discount),
  status: data.status,
  property: data.property?.property_id || null, // prevent UUID errors
};



    //Save MilkatKar in Warshikar
    const saveMaster: any = await this.warShikKarAkarani.saveData(prepareData);

    // Extract the warshikarId
    const warshikKarId = saveMaster.warshik_karId;

    // Iterate over the values to insert according to the warshikarId
    for (const milkatValue of getMilkatValues) {
      // Add key-value to milkat obj
      milkatValue.WarShikKar = warshikKarId;

      // Save Data in warshikKar
      await this.warshikarTaxAkarani.saveData(milkatValue);
    }

    // Step 4: Update property status to null after insertion
    await this.propertyMasterRepository.update(
      { property_id: data.property.property_id },
      { updateStatus: null },
    );
  }

 async updatetaxtypes({
  warshikKar_id,
  property_id,
  updateTaxTypes,
}: {
  warshikKar_id: string;
  property_id: string;
  updateTaxTypes: UpdateTaxDetailsDto;
}) {
  try {
    const { owner_id } = updateTaxTypes;
    const property = await this.propertyMasterRepository.findById(property_id);
    if (!property) {
      return { message: 'Given property Not found' };
    }

    // Update tax payer information
    await this.propertyOwnerRepo.updateTaxPayer(owner_id, property_id);

    const warshikKar = await this.warShikKarAkarani.findById(warshikKar_id);
    if (!warshikKar) {
      return { message: 'Given annual tax Not found' };
    }

    Object.assign(warshikKar, updateTaxTypes);

    for (let i = 1; i <= 10; i++) {
      const currentKey = `tax_type_${i}_current`;
      const previousKey = `tax_type_${i}_previous`;
      const totalKey = `tax_type_${i}`;
      warshikKar[totalKey] = (Number(warshikKar[currentKey]) || 0) + (Number(warshikKar[previousKey]) || 0);
    }

    warshikKar.all_property_tax_sum_total = (Number(warshikKar.all_property_tax_sum_current) || 0) + (Number(warshikKar.all_property_tax_sum) || 0);

    warshikKar.other_tax_sum_tax = (Number(warshikKar.other_tax_sum_tax_current) || 0) + (Number(warshikKar.other_tax_sum_tax_previous) || 0);

    // Include tax_type_9 (penalty) in the total tax calculation
    warshikKar.total_tax = (Number(warshikKar.total_tax_current) || 0) + (Number(warshikKar.total_tax_previous) || 0) + (Number(warshikKar.tax_type_9) || 0);

    const updatedWarshikKar = await this.warShikKarAkarani.save(warshikKar);

    const taxPendingDues = await this.taxPendingDuesRepository.findOne({
      where: { old_propertyNumber: property.old_propertyNumber?.trim() },
    });

    if (!taxPendingDues) {
      return { message: 'No pending dues found for the given property' };
    }

    // Update taxPendingDues fields with values from updateTaxTypes if they are not zero
    taxPendingDues.all_property_tax_sum = updateTaxTypes.all_property_tax_sum !== undefined ? updateTaxTypes.all_property_tax_sum : 0;
    taxPendingDues.tax_type_8 = updateTaxTypes.tax_type_8_previous !== undefined ? updateTaxTypes.tax_type_8_previous : 0;
    taxPendingDues.tax_type_7 = updateTaxTypes.tax_type_7_previous !== undefined ? updateTaxTypes.tax_type_7_previous : 0;
    taxPendingDues.tax_type_6 = updateTaxTypes.tax_type_6_previous !== undefined ? updateTaxTypes.tax_type_6_previous : 0;
    taxPendingDues.tax_type_4 = updateTaxTypes.tax_type_4_previous !== undefined ? updateTaxTypes.tax_type_4_previous : 0;
    taxPendingDues.tax_type_9 = updateTaxTypes.tax_type_9_previous !== undefined ? updateTaxTypes.tax_type_9_previous : 0;
    taxPendingDues.tax_type_5 = updateTaxTypes.tax_type_5_previous !== undefined ? updateTaxTypes.tax_type_5_previous : 0;

    // If tax_type_9_current is provided, update or create a penalty fee for the current financial year
    if (updateTaxTypes.tax_type_9_current) {
      const currentFinancialYear = await this.financialYearRepository.getCurrentFinancialYear();
      const currentFY = currentFinancialYear.financial_year_range;

      // Check if a penalty fee already exists for this property and financial year
      const existingPenalty = await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(property_id, currentFY);

      if (existingPenalty && existingPenalty.length > 0) {
        // Update existing penalty
        await this.penaltyFeeYearWiseRepository.updatePenaltyFee(
          existingPenalty[0].penalty_fee_id,
          {
            actual_value: updateTaxTypes.tax_type_9_current,
          },
          true,
        );
      } else {
        // Create new penalty with default tax percentage of 2%
        await this.penaltyFeeYearWiseRepository.savePenaltyFee({
          property: property_id,
          financial_year: currentFY,
          actual_value: updateTaxTypes.tax_type_9_current,
          tax_percentage: 2, // Default tax percentage
        });
      }
    }

    // Recalculate total to ensure it's accurate
    const recalculatedTotal = Object.keys(taxPendingDues).reduce((sum, key) => {
      if (key.startsWith('tax_type_') || key === 'all_property_tax_sum') {
        return sum + (Number(taxPendingDues[key]) || 0);
      }
      return sum;
    }, 0);

    taxPendingDues.total = recalculatedTotal;

    // Only save if there are pending dues (total > 0)
    let updatedTaxPendingDues = null;
    if (recalculatedTotal > 0) {
      updatedTaxPendingDues = await this.taxPendingDuesRepository.save(taxPendingDues);
    } else {
      // If no pending dues, delete the record
      if (taxPendingDues.dues_id) {
        await this.taxPendingDuesRepository.deleteData(taxPendingDues.dues_id);
      }
    }

    return {
      message: 'Tax details updated successfully',
      data: {
        updatedWarshikKar,
        updatedTaxPendingDues,
      },
    };
  } catch (error) {
    throw error;
  }
}

  async updateTaxData(warshiktax_id: string) {
    try {
      const warshiktax =
        await this.warshikarTaxAkarani.getDataInfoByTaxId(warshiktax_id);
      const propertyUsage_id =
        warshiktax.property_usage_details.property_usage_details_id;
      const floor_name =
        await this.propertyUsageDetailsRepository.getFloorTypeName(
          propertyUsage_id,
        );

      if (floor_name && floor_name != 'तळ मजला') {
        let taxData = JSON.parse(warshiktax.tax_data as any);
        taxData.capital_value_formula = `=(${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}`;
        taxData.emarat_tax_formula = `=((${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}) * ${taxData.taxCalculationDetails.taxValue})`;
        (taxData.emarat_tax_formula_fields = `'( are_sq_meter * rr_construction_rate * depreciation_rate)) * weighting ) * tax_rate '`),
          (warshiktax.tax_data = JSON.stringify(taxData) as unknown as JSON);

        await this.warshikarTaxAkarani.save(warshiktax);
        return {
          message: 'Updated Tax Data',
          data: warshiktax,
        };
      } else {
        let taxData = JSON.parse(warshiktax.tax_data as any);

        taxData.capital_value_formula = `=(${taxData.areaSqMeter}*${taxData.taxCalculationDetails.rrRate} )+(${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}`;
        taxData.emarat_tax_formula = `=((${taxData.areaSqMeter}*${taxData.rr_rate} )+(${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}) * ${taxData.taxCalculationDetails.taxValue})`;
        taxData.emarat_tax_formula_fields = `'=((( are_sq_meter * rr_rate) + ( are_sq_meter * rr_construction_rate * depreciation_rate)) * weighting ) * tax_rate ',
`;
        warshiktax.tax_data = JSON.stringify(taxData) as unknown as JSON;

        await this.warshikarTaxAkarani.save(warshiktax);
        return {
          message: 'Updated Tax Data',
          data: warshiktax,
        };
      }
      // This code is unreachable, but kept for safety
      // return {
      //   message: 'No updates to Tax Data needed',
      // };
    } catch (error) {
      throw error;
    }
  }

  async updateAllTaxData() {
    try {
      const warshiktaxes = await this.warshikarTaxAkarani.getAll();
      let updatedtax_Ids: string[] = [];

      const updatePromises = warshiktaxes.map(async (warshiktax) => {
        const propertyUsage_id =
          warshiktax.property_usage_details.property_usage_details_id;
        const floor_name =
          await this.propertyUsageDetailsRepository.getFloorTypeName(
            propertyUsage_id,
          );
        if (floor_name && floor_name != 'तळ मजला') {
          let taxData = JSON.parse(warshiktax.tax_data as any);
          taxData.capital_value_formula = `=(${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}`;
          taxData.emarat_tax_formula = `=((${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}) * ${taxData.taxCalculationDetails.taxValue})`;
          (taxData.emarat_tax_formula_fields = `'( are_sq_meter * rr_construction_rate * depreciation_rate)) * weighting ) * tax_rate '`),
            (warshiktax.tax_data = JSON.stringify(taxData) as unknown as JSON);

          await this.warshikarTaxAkarani.save(warshiktax);
          updatedtax_Ids.push(warshiktax.warshik_karTaxId);
        } else {
          let taxData = JSON.parse(warshiktax.tax_data as any);
          taxData.capital_value_formula = `=(${taxData.areaSqMeter}*${taxData.taxCalculationDetails.rrRate} )+(${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}`;
          taxData.emarat_tax_formula = `=((${taxData.areaSqMeter}*${taxData.rr_rate} )+(${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}) * ${taxData.taxCalculationDetails.taxValue})`;
          taxData.emarat_tax_formula_fields = `'=((( are_sq_meter * rr_rate) + ( are_sq_meter * rr_construction_rate * depreciation_rate)) * weighting ) * tax_rate ',
`;
          warshiktax.tax_data = JSON.stringify(taxData) as unknown as JSON;

          await this.warshikarTaxAkarani.save(warshiktax);
          updatedtax_Ids.push(warshiktax.warshik_karTaxId);
        }
      });

      await Promise.all(updatePromises);

      if (updatedtax_Ids.length === 0) {
        return { message: 'No updates needed' };
      }
      return {
        message: 'Updated All Tax Data',
        data: updatedtax_Ids,
      };
    } catch (error) {
      throw error;
    }
  }

  async calculateAndStorePendingDues(
    warshilKar: WarshilKarEntity,
    financialYear: Financial_year,
    previousYear: Financial_year,
  ) {
    console.log(
      `DEBUG: Starting calculateAndStorePendingDues for property ID: ${warshilKar?.property?.property_id}`,
    );

    if (!warshilKar) {
      console.log(`ERROR: No warshikKar data found for property`);
      return `No warshikKar data found for property`;
    }

    console.log(
      `DEBUG: WarshikKar data found: ${JSON.stringify({
        warshik_karId: warshilKar.warshik_karId,
        property_id: warshilKar.property.property_id,
        financial_year: warshilKar.financial_year,
        status: warshilKar.status,
      })}`,
    );

    const property = await this.propertyMasterRepository.getDataFromID(
      warshilKar.property.property_id,
    );

    console.log(
      `DEBUG: Property data found: ${JSON.stringify({
        property_id: property.property_id,
        propertyNumber: property.propertyNumber,
        old_propertyNumber: property.old_propertyNumber,
      })}`,
    );

    console.log(
      `DEBUG: Looking for paid data for property ${property.propertyNumber} in financial year ${previousYear.financial_year_range}`,
    );

    const paidDatas = await this.paidDataRepo.find({
      where: {
        property: property,
        financial_year: previousYear.financial_year_range,
      },
    });

    console.log(`DEBUG: Found ${paidDatas.length} paid data records`);
    console.log(`DEBUG: Paid data details:`, paidDatas);

    let paidAmount = {
      [TaxTypeEnum.all_property_tax_sum_total]: 0,
      [TaxTypeEnum.tax_type_6]: 0,
      [TaxTypeEnum.tax_type_7]: 0,
      [TaxTypeEnum.tax_type_8]: 0,
      [TaxTypeEnum.tax_type_9]: 0,
      [TaxTypeEnum.tax_type_5]: 0,
      [TaxTypeEnum.tax_type_4]: 0,
      [TaxTypeEnum.TOTAL]: 0,
    };

    paidDatas.forEach((paidData, index) => {
      console.log(`DEBUG: Processing paid data record ${index + 1}:`);
      console.log(
        `  - all_property_tax_sum: ${paidData?.all_property_tax_sum || 0}`,
      );
      console.log(`  - tax_type_6: ${paidData?.tax_type_6 || 0}`);
      console.log(`  - tax_type_7: ${paidData?.tax_type_7 || 0}`);
      console.log(`  - tax_type_8: ${paidData?.tax_type_8 || 0}`);
      console.log(`  - tax_type_9: ${paidData?.tax_type_9 || 0}`);
      console.log(`  - tax_type_5: ${paidData?.tax_type_5 || 0}`);
      console.log(`  - tax_type_4: ${paidData?.tax_type_4 || 0}`);

      paidAmount[TaxTypeEnum.all_property_tax_sum_total] +=
        paidData?.all_property_tax_sum || 0;
      paidAmount[TaxTypeEnum.tax_type_6] += paidData?.tax_type_6 || 0;
      paidAmount[TaxTypeEnum.tax_type_7] += paidData?.tax_type_7 || 0;
      paidAmount[TaxTypeEnum.tax_type_8] += paidData?.tax_type_8 || 0;
      paidAmount[TaxTypeEnum.tax_type_9] += paidData?.tax_type_9 || 0;
      paidAmount[TaxTypeEnum.tax_type_5] += paidData?.tax_type_5 || 0;
      paidAmount[TaxTypeEnum.tax_type_4] += paidData?.tax_type_4 || 0;
    });

    console.log(`DEBUG: Total paid amounts:`);
    console.log(
      `  - HOUSE_TAX: ${paidAmount[TaxTypeEnum.all_property_tax_sum_total]}`,
    );
    console.log(`  - ELECTRICITY_BILL: ${paidAmount[TaxTypeEnum.tax_type_6]}`);
    console.log(`  - HEALTH_TAX: ${paidAmount[TaxTypeEnum.tax_type_7]}`);
    console.log(`  - PADSAR: ${paidAmount[TaxTypeEnum.tax_type_8]}`);
    console.log(`  - PENALTY_AMOUNT: ${paidAmount[TaxTypeEnum.tax_type_9]}`);
    console.log(`  - SHASTI_FEE: ${paidAmount[TaxTypeEnum.tax_type_5]}`);
    console.log(`  - GHAN_KACHARA: ${paidAmount[TaxTypeEnum.tax_type_4]}`);

    console.log(`DEBUG: WarshikKar tax values:`);
    console.log(`  - tax_type_1: ${warshilKar.tax_type_1 || 0}`);
    console.log(`  - tax_type_6: ${warshilKar.tax_type_6 || 0}`);
    console.log(`  - tax_type_7: ${warshilKar.tax_type_7 || 0}`);
    console.log(`  - tax_type_8: ${warshilKar.tax_type_8 || 0}`);
    console.log(`  - tax_type_9: ${warshilKar.tax_type_9 || 0}`);
    console.log(`  - tax_type_5: ${warshilKar.tax_type_5 || 0}`);
    console.log(`  - tax_type_4: ${warshilKar.tax_type_4 || 0}`);

    const pendingDues = {
      all_property_tax_sum: Math.max(
        0,
        (warshilKar.all_property_tax_sum_total || 0) -
          (paidAmount[TaxTypeEnum.all_property_tax_sum_total] || 0),
      ),
      tax_type_6: Math.max(
        0,
        (warshilKar.tax_type_6 || 0) -
          (paidAmount[TaxTypeEnum.tax_type_6] || 0),
      ),
      tax_type_7: Math.max(
        0,
        (warshilKar.tax_type_7 || 0) -
          (paidAmount[TaxTypeEnum.tax_type_7] || 0),
      ),
      tax_type_8: Math.max(
        0,
        (warshilKar.tax_type_8 || 0) -
          (paidAmount[TaxTypeEnum.tax_type_8] || 0),
      ),
      tax_type_9: Math.max(
        0,
        (warshilKar.tax_type_9 || 0) -
          (paidAmount[TaxTypeEnum.tax_type_9] || 0),
      ),
      tax_type_5: Math.max(
        0,
        (warshilKar.tax_type_5 || 0) -
          (paidAmount[TaxTypeEnum.tax_type_5] || 0),
      ),
      tax_type_4: Math.max(
        0,
        (warshilKar.tax_type_4 || 0) -
          (paidAmount[TaxTypeEnum.tax_type_4] || 0),
      ),
      tax_type_1: 0,

      tax_type_2: 0,
      tax_type_3: 0,
      tax_type_10: 0,
      total: 0, // Will calculate below
    };

    console.log(`DEBUG: Calculated pending dues:`);
    console.log(`  - tax_type_1: ${pendingDues.tax_type_1}`);
    console.log(`  - tax_type_6: ${pendingDues.tax_type_6}`);
    console.log(`  - tax_type_7: ${pendingDues.tax_type_7}`);
    console.log(`  - tax_type_8: ${pendingDues.tax_type_8}`);
    console.log(`  - tax_type_9: ${pendingDues.tax_type_9}`);
    console.log(`  - tax_type_5: ${pendingDues.tax_type_5}`);
    console.log(`  - tax_type_4: ${pendingDues.tax_type_4}`);

    // Make sure to include penalty amount in the total calculation
    pendingDues.total =
      pendingDues.all_property_tax_sum +
      pendingDues.tax_type_1 +
      pendingDues.tax_type_2 +
      pendingDues.tax_type_3 +
      pendingDues.tax_type_4 +
      pendingDues.tax_type_5 +
      pendingDues.tax_type_6 +
      pendingDues.tax_type_7 +
      pendingDues.tax_type_8 +
      // pendingDues.tax_type_9 + // Explicitly include penalty amount
      pendingDues.tax_type_10;

    console.log(`DEBUG: Total pending dues: ${pendingDues.total}`);

    // Only add to tax_pending_dues if there are actual pending dues (total > 0)
    if (pendingDues.total <= 0) {
      console.log(
        `DEBUG: No pending dues for property ${property.propertyNumber || property.old_propertyNumber}, skipping tax_pending_dues entry`,
      );
      return;
    }

    let propertyHolderName = '-';
    let possessionHolderName = '-';

    console.log(
      `DEBUG: Processing ${property.property_owner_details.length} property owner details`,
    );

    property.property_owner_details.forEach((owner, index) => {
      console.log(
        `DEBUG: Owner ${index + 1} - name: ${owner.name}, type: ${owner.owner_type.owner_type}`,
      );
      if (owner.owner_type.owner_type === 'स्वत:') {
        propertyHolderName = owner.name;
      } else {
        possessionHolderName = owner.name;
      }
    });

    console.log(`DEBUG: Property holder name: ${propertyHolderName}`);
    console.log(`DEBUG: Possession holder name: ${possessionHolderName}`);

    const taxPendingDues = new TaxPendingDuesEntity();
    taxPendingDues.all_property_tax_sum = pendingDues.all_property_tax_sum;
    taxPendingDues.tax_type_1 = pendingDues.tax_type_1; // Initialize tax_type_1
    taxPendingDues.tax_type_2 = pendingDues.tax_type_2;
    taxPendingDues.tax_type_3 = pendingDues.tax_type_3;
    taxPendingDues.tax_type_4 = pendingDues.tax_type_4;
    taxPendingDues.tax_type_5 = pendingDues.tax_type_5;
    taxPendingDues.tax_type_6 = pendingDues.tax_type_6;
    taxPendingDues.tax_type_7 = pendingDues.tax_type_7;
    taxPendingDues.tax_type_8 = pendingDues.tax_type_8;
    taxPendingDues.tax_type_9 = pendingDues.tax_type_9;
    taxPendingDues.tax_type_10 = pendingDues.tax_type_10;
    taxPendingDues.total = pendingDues.total;
    taxPendingDues.financial_year = financialYear;
    taxPendingDues.old_propertyNumber = property.old_propertyNumber || '-';
    taxPendingDues.streetName = property.street.street_name || '-';
    taxPendingDues.surveyNumber = property.city_survey_number || '-';
    taxPendingDues.ward = property.ward.ward_name;
    taxPendingDues.propertyHolderName = propertyHolderName;
    taxPendingDues.sheetIndex = '-';
    taxPendingDues.possessionHolderName = possessionHolderName;
    taxPendingDues.property_number = property.propertyNumber || null;

    console.log(`DEBUG: Saving tax pending dues with data:`, {
      financial_year_id: financialYear.financial_year_id,
      financial_year_range: financialYear.financial_year_range,
      old_propertyNumber: taxPendingDues.old_propertyNumber,
      property_number: taxPendingDues.property_number,
      total: taxPendingDues.total,
    });

    try {
      const savedTaxPendingDues =
        await this.taxPendingDuesRepository.save(taxPendingDues);
      console.log(
        `DEBUG: Successfully saved tax pending dues with ID: ${savedTaxPendingDues.dues_id}`,
      );
      console.log(
        `Added pending dues for property ${property.propertyNumber || property.old_propertyNumber} with total amount ${pendingDues.total}`,
      );
    } catch (error) {
      console.error(`ERROR: Failed to save tax pending dues:`, error);
      throw error;
    }
  }

  async updateDemandReport() {
    try {
      const currentFinancialYear =
        await this.financialYearRepository.getCurrentFinancialYear();

      const warshikarTaxes =
        await this.warShikKarAkarani.getActiveWarshikKarByPropertyId(
          currentFinancialYear?.financial_year_range,
        );
      const payments = await this.paidDataRepo.getAllPaidData(
        currentFinancialYear?.financial_year_range,
      );
// console.log("paymetnss",JSON.stringify(payments.slice(0,10)));

      //  return { message: 'Demand record updated successfully',
      //   data:payments.slice(0,10)
      //   };

      const mapTaxes = new Map<string, WarshilKarEntity[]>();

      for (const tax of warshikarTaxes) {
        const propertyId = tax.property?.property_id;
        if (!propertyId) {
          console.warn('Skipping tax with null property:', tax);
          continue;
        }

        if (!mapTaxes.has(propertyId)) {
          mapTaxes.set(propertyId, []);
        }

        mapTaxes.get(propertyId)!.push(tax);
      }

      for (const pay of payments) {
        const propertyId = pay.property?.property_id;
        if (!propertyId) {
          console.warn(
            'Skipping payment with null property:',
            pay.paid_data_id,
          );
          continue;
        }

        const taxData = mapTaxes.get(propertyId);
        if (!taxData || taxData.length === 0) {
          console.warn('No tax data for property:', propertyId);
          continue;
        }

        for (const tax of taxData) {
          const existingDemand = await this.demandRecordRepo.findOne({
            where: {
              property: { property_id: propertyId },
              financial_year: pay.financial_year,
            },
            relations: ['property'],
          });

          const report = new DemandReportData();
          report.property = pay.property;
          report.financial_year = pay.financial_year;
          report.property_number = pay.property_number;
          // Assign the first receipt if available, or null
          let reciptObj:any=pay.paymentInfo.receipts
          report.ReciptInfo  =reciptObj;
          

          const taxTypes = Array.from({ length: 10 }, (_, i) => i + 1);

          if (existingDemand) {
            for (const type of taxTypes) {
              const prevKey = `tax_type_${type}_prev_remaining`;
              const currKey = `tax_type_${type}_curr_remaining`;
              const paidPrevKey = `tax_type_${type}_prev_paid`;
              const paidCurrKey = `tax_type_${type}_curr_paid`;

              report[prevKey] =
                (existingDemand[prevKey] ?? 0) -
                (existingDemand[paidPrevKey] ?? 0);
              report[currKey] =
                (existingDemand[currKey] ?? 0) -
                (existingDemand[paidCurrKey] ?? 0);
            }

            report.other_tax_sum_tax_prev_remaining =
              (existingDemand.other_tax_sum_tax_prev_remaining ?? 0) -
              (existingDemand.other_tax_sum_tax_prev_paid ?? 0);

            report.other_tax_sum_tax_curr_remaining =
              (existingDemand.other_tax_sum_tax_curr_remaining ?? 0) -
              (existingDemand.other_tax_sum_tax_curr_paid ?? 0);

            report.all_property_tax_sum_remaining =
              (existingDemand.all_property_tax_sum_remaining ?? 0) -
              (existingDemand.all_property_tax_sum_paid ?? 0);
          } else {
            for (const type of taxTypes) {
              report[`tax_type_${type}_prev_remaining`] =
                tax[`tax_type_${type}_previous`] ?? 0;
              report[`tax_type_${type}_curr_remaining`] =
                tax[`tax_type_${type}_current`] ?? 0;
              report[`tax_type_${type}_remaining`] =
                tax[`tax_type_${type}`] ?? 0;
            }

            report.other_tax_sum_tax_prev_remaining =
              tax.other_tax_sum_tax_previous ?? 0;
            report.other_tax_sum_tax_curr_remaining =
              tax.other_tax_sum_tax_current ?? 0;
            report.all_property_tax_sum_prev_remaining =
              tax.all_property_tax_sum ?? 0;
            report.all_property_tax_sum_curr_remaining =
              tax.all_property_tax_sum_current ?? 0;
            report.all_property_tax_sum_remaining =
              tax.all_property_tax_sum_total ?? 0;
          }
          for (const type of taxTypes) {
            report[`tax_type_${type}_paid`] = pay[`tax_type_${type}`] ?? 0;
            report[`tax_type_${type}_prev_paid`] =
              pay[`tax_type_${type}_prev`] ?? 0;
            report[`tax_type_${type}_curr_paid`] =
              pay[`tax_type_${type}_curr`] ?? 0;
          }

          report.other_tax_sum_tax_paid = pay.other_tax_sum_tax ?? 0;
          report.other_tax_sum_tax_prev_paid = pay.other_tax_sum_tax_prev ?? 0;
          report.other_tax_sum_tax_curr_paid = pay.other_tax_sum_tax_curr ?? 0;
          report.all_property_tax_sum_paid = pay.all_property_tax_sum ?? 0;
          report.all_property_tax_sum_prev_paid =
            pay.all_property_tax_sum_prev ?? 0;
          report.all_property_tax_sum_curr_paid =
            pay.all_property_tax_sum_curr ?? 0;

          let totalPaid = 0;
          let totalRemaining = 0;

          for (const type of taxTypes) {
            totalPaid += report[`tax_type_${type}_paid`] ?? 0;
            totalRemaining += report[`tax_type_${type}_remaining`] ?? 0;

            // Ensure tax_type_9 (penalty) is properly included in the total
            if (type === 9) {
              console.log(
                `Including penalty in total: ${report[`tax_type_${type}_remaining`] ?? 0}`,
              );
            }
          }

          totalPaid += report.other_tax_sum_tax_paid ?? 0;
          totalPaid += report.all_property_tax_sum_paid ?? 0;

          totalRemaining += report.other_tax_sum_tax_remaining ?? 0;
          totalRemaining += report.all_property_tax_sum_remaining ?? 0;

          if (existingDemand) {
            totalRemaining =
              (existingDemand.total_amount_remaining ?? 0) -
              (existingDemand.total_amount_paid ?? 0);
          }

          report.total_amount_paid = totalPaid;
          report.total_amount_remaining = totalRemaining;

          await this.demandRecordRepo.save(report);
        }
      }

      return { message: 'Demand record updated successfully' };
    } catch (error) {
      console.error('Error updating demand record:', error);
      throw new Error('Failed to update demand record');
    }
  }
}
