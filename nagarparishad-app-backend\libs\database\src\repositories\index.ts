import { AdminstrativeBoundaryMasterRepository } from './adminstrative-boundaryMaster.repository';
import { AreaLocalityMasterRepository } from './area-localityMaster.repository';
import { ConstructionClassRepository } from './constructionClass.repository';
import { ElectionBoundaryMasterRepository } from './election-boundaryMaster.repository';
import { FormMasterRepository } from './form-master.repository';
import { LocationRepository } from './location-master.repository';
import { ModuleMasterRepository } from './module-master.repository';
import { PropertyMasterRepository } from './property-master.repository';
import { PropertySubTypeMasterRepository } from './property-sub-type_master.repository';
import { PropertyTypeMasterRepository } from './property-typeMaster.repository';
import { RoleMasterRepository } from './role-master.repository';
import { RolewiseFormPermissionRepository } from './rolewise-permission.repository';
import { StreetMasterRepository } from './street-master.repository';
import { UsageMasterRepository } from './usage-master.repository';
import { UsageSubMasterRepository } from './usage-sub-master.repository';
import { UserOtpRepository } from './user-otp.repository';
import { UserRepository } from './user.repository';
import { WardMasterRepository } from './ward_master.repository';
import { ZoneMasterRepository } from './zone-master.repository';
import { Tax_PropertyWiseRepository } from './tax_propertywise.repository';
import { Tax_PropertyRepository } from './tax_property.repository';
import { Tax_FY_RecordsRepository } from './tax_fy_records.repository';
import { PropertyTypeRepository } from './property_type.repository';
import { ImportPropertyMasterRepository } from './import-property-master.repository'
import { ImportPropertyStatsMasterRepository } from './import-property-stats-master.repository'
import { OwnerTypeRepository } from './owner_type.repository'
import { Property_Owner_DetailsRepository } from  './property_owner_details.repository'
import { Property_Usage_DetailsRepository } from './property_usage_details.repository'
import  { GIS_data_Repository } from './gis_data.repository'
import { LogsRepository } from './logs.repository'
import { MilkatKareRepository } from './milkatKar.repository'
import { MilkatKarTaxeRepository } from './milkatKarTax.repository';
import { Master_rr_rateRepository } from './masterRrRate.repository';
import { Master_depreciationRepository } from './masterdepreciation.repository';
import { Master_rr_construction_rateRepository } from './masterRR_construction_rate.repository';
import { Master_WeightingRepository } from  './masterWeighting.repository';
import { Master_TaxValueRepository } from './masterTaxValue.repository';
import { CommonFiledsOfPropertyRepository } from './commonFieldProperty.repository';

import exp from 'constants';
import { WarshikKarRepository } from './warshikKar.repository';
import { WarshikKarTaxRepository } from './warshik-kar-tax.entity';
import { Financial_yearRepository } from './financialYear.repository'
import {WrongPropertyTypeMasterRepository} from './wrong_property_type.repository'
import { FloorMasterRepository } from './floorMaster.repository';
import { MasterGhanKachraRateRepository } from './masterGhanKachra.repository';
import { PropertyTypeClassMasterRepository } from './property_type_classMaster.repository';
import {TaxPendingDuesRepository} from './tax_pending_dues.repository';
import { BillDataRepository} from './bill.repository'
import { ReceiptRepository } from './recipt_Master_repository';
import { PaymentInfoRepository } from './paymentInfo.repository';
import { PaidDataRepository } from './paid_data_Master.repository';
import { PaidAndNonPaidDataRepository } from './paid-and-non-paid-data.repository';
import { BookNumberMasterRepository } from './book_numberMaster.repository';
import { DeletedPropertyUsageRepository } from './deletedPropertyUsageDetails.repository';
import { DeletedPropertyRepository } from './deleted_property.repository';
import { BackupPropertyUsageDetailsRepository } from './backupPropertyUsageDetails.repository';
import { BackupPaymentDetailsRepository } from './backupPaymentDetails.repository';
import { PreviousOwnerRepository } from './previousPropertyOwners.repository';
import { CollectorMasterRepository } from './collectorMaster.repository';
import { DemandRecordDataRepository } from './demand-report-data.repository';
import { ReassessmentRangeRepository } from './reassesment.repository';
import { PenaltyFeeYearWiseRepository } from './penalty_fee_yearwise.repository';
import { PropertyFerfarDetailsRepository } from './property_ferfar_detail.repository';
import { CronJobFailureRepository } from './cron-job-failure.repository';
import { OfflineNotificationRepository } from './offline-notification.repository';
import { RegisterNumberRepository } from './register_number.repository';

export const repositories = [
  WardMasterRepository,
  ZoneMasterRepository,
  LocationRepository,
  StreetMasterRepository,
  AreaLocalityMasterRepository,
  AdminstrativeBoundaryMasterRepository,
  ElectionBoundaryMasterRepository,
  PropertyTypeMasterRepository,
  PropertySubTypeMasterRepository,
  UsageMasterRepository,
  UsageSubMasterRepository,
  ConstructionClassRepository,
  PropertyMasterRepository,
  RoleMasterRepository,
  UserRepository,
  UserOtpRepository,
  RolewiseFormPermissionRepository,
  FormMasterRepository,
  ModuleMasterRepository,
  Tax_PropertyWiseRepository,
  Tax_PropertyRepository,
  Tax_FY_RecordsRepository,
  PropertyTypeRepository,
  ImportPropertyMasterRepository,
  ImportPropertyStatsMasterRepository,
  GIS_data_Repository,
  OwnerTypeRepository,
  Property_Owner_DetailsRepository,
  Property_Usage_DetailsRepository,
  LogsRepository,
  MilkatKareRepository,
  MilkatKarTaxeRepository,
  Master_rr_rateRepository,
  WarshikKarRepository,
  MilkatKarTaxeRepository,
  WarshikKarTaxRepository,
  Master_depreciationRepository,
  Master_rr_construction_rateRepository,
  Master_WeightingRepository,
  Master_TaxValueRepository,
  CommonFiledsOfPropertyRepository,
  Financial_yearRepository,
  WrongPropertyTypeMasterRepository,
  FloorMasterRepository,
  MasterGhanKachraRateRepository,
  PropertyTypeClassMasterRepository,
  TaxPendingDuesRepository,
  BillDataRepository,
  BookNumberMasterRepository,

  ReceiptRepository,
  PaymentInfoRepository,
  PaidDataRepository,
  PaidAndNonPaidDataRepository,
  BookNumberMasterRepository,
  DeletedPropertyUsageRepository,
  DeletedPropertyRepository,
  BackupPropertyUsageDetailsRepository,
  BackupPaymentDetailsRepository,
  PreviousOwnerRepository,
  CollectorMasterRepository,
  DemandRecordDataRepository,
  ReassessmentRangeRepository,
  PenaltyFeeYearWiseRepository,
  PropertyFerfarDetailsRepository,
  CronJobFailureRepository,
  OfflineNotificationRepository,
  RegisterNumberRepository
];

export * from './ward_master.repository';
export * from './zone-master.repository';
export * from './location-master.repository';
export * from './street-master.repository';
export * from './area-localityMaster.repository';
export * from './adminstrative-boundaryMaster.repository';
export * from './election-boundaryMaster.repository';
export * from './property-typeMaster.repository';
export * from './property-sub-type_master.repository';
export * from './usage-master.repository';
export * from './usage-sub-master.repository';
export * from './constructionClass.repository';
export * from './property-master.repository';
export * from './role-master.repository';
export * from './user.repository';
export * from './user-otp.repository';
export * from './rolewise-permission.repository';
export * from './form-master.repository';
export * from './module-master.repository';
export * from './tax_property.repository';
export * from './tax_propertywise.repository';
export * from './tax_fy_records.repository';
export * from './property_type.repository';
export * from './import-property-master.repository';
export * from './import-property-stats-master.repository';
export * from './gis_data.repository';
export * from './owner_type.repository';
export * from './property_owner_details.repository';
export * from './property_usage_details.repository';
export * from './logs.repository';
export * from './milkatKar.repository';
export * from './milkatKarTax.repository';
export * from './masterRrRate.repository';
export * from './warshikKar.repository';
export * from './milkatKarTax.repository';
export * from './warshik-kar-tax.entity';
export * from './masterdepreciation.repository';
export * from './masterRR_construction_rate.repository';
export * from './masterWeighting.repository';
export * from './masterTaxValue.repository';
export * from './commonFieldProperty.repository';
export * from './financialYear.repository';
export * from './wrong_property_type.repository'
export * from './floorMaster.repository';
export * from './masterGhanKachra.repository';
export * from './property_type_classMaster.repository';
export * from './tax_pending_dues.repository';
export * from './bill.repository';
export *  from './recipt_Master_repository';
export *  from './paymentInfo.repository';
export *  from './paid_data_Master.repository';
export * from './paid-and-non-paid-data.repository';
export * from './book_numberMaster.repository';
export * from './deletedPropertyUsageDetails.repository';
export * from './deleted_property.repository';
export * from './backupPropertyUsageDetails.repository';
export * from './backupPaymentDetails.repository';
export * from './previousPropertyOwners.repository';
export * from './collectorMaster.repository';
export * from './demand-report-data.repository'
export * from './reassesment.repository';
export * from './penalty_fee_yearwise.repository';
export * from './property_ferfar_detail.repository';
export * from './cron-job-failure.repository';
export * from './offline-notification.repository';
export * from './register_number.repository';
