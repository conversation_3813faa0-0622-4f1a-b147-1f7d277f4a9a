import React, { useEffect, useState } from "react";
import axios from "axios";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import WhiteContainer from "../WhiteContainer";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { PlusIcon } from "lucide-react";
import { Loader } from "@/components/globalcomponent/Loader";

const GisDataTable = () => {
  const [selectedWard, setSelectedWard] = useState("");
  const [selectedWardAftersearch, setselectedWardAftersearch] = useState("");
  const apiBaseUrl = import.meta.env.VITE_APP_BASE_URL;

  const [data, setData] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [sortOrder, setSortOrder] = useState(null); // Sort order state
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleSearch = async () => {
    setLoading(true);

    try {
      const response = await axios.get(
        `${apiBaseUrl}/v1/import-module/getCountMissingFields?ward_number=${selectedWard}`
      );
      setData(response.data?.data);
      setselectedWardAftersearch(selectedWard);
    } catch (error) {
      console.error("Error fetching data:", error);
      setData(null);
    } finally {
      setLoading(false);
    }
  };
  const handleDownload = async () => {
    if (selectedWard) {
      setDownloadLoading(true);
      const downloadUrl = `${apiBaseUrl}/v1/import-module/export-csv?ward_number=${selectedWard}`;
      try {
        const response = await axios.get(downloadUrl, {
          responseType: "blob",
        });

        const currentDate = new Date();
        const formattedDate = `${currentDate.getFullYear()}-${(
          currentDate.getMonth() + 1
        )
          .toString()
          .padStart(
            2,
            "0"
          )}-${currentDate.getDate().toString().padStart(2, "0")}`;
        const formattedTime = `${currentDate.getHours().toString().padStart(2, "0")}-${currentDate
          .getMinutes()
          .toString()
          .padStart(
            2,
            "0"
          )}-${currentDate.getSeconds().toString().padStart(2, "0")}`;

        const filename = `ward_${selectedWard}_${formattedDate}_${formattedTime}.xlsx`;

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        link.remove();
      } catch (error) {
        alert("Error downloading file");
      } finally {
        setDownloadLoading(false);
      }
    } else {
      alert("Please select a ward before downloading.");
    }
  };

  const handleUpload = async () => {
    if (selectedFile) {
      setUploadLoading(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append("file", selectedFile);

      try {
        const response = await axios.post(
          `${apiBaseUrl}/v1/import-module/upload-and-update`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
            onUploadProgress: (progressEvent) => {
              const percentCompleted = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              );
              setUploadProgress(percentCompleted);
            },
          }
        );

        alert(`File uploaded successfully: ${selectedFile.name}`);
        setSelectedFile(null);
        setUploadProgress(0);

        console.log("Upload response:", response.data);
      } catch (error) {
        alert("Error uploading file");
        console.error("Upload error:", error);
      } finally {
        setUploadLoading(false);
      }
    } else {
      alert("Please select a CSV file to upload.");
    }
  };

  const handleFileChange = (event) => {
    console.log("file chnages",event)

    const file = event.target.files[0];
    if (
      file &&
      file.type ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ) {
      console.log("file chnages",file)
      setSelectedFile(file);
    } else {
      alert("Please select a valid .xlsx file.");
    }
  };
  const importDate =
    data?.last_syncDate?.import_date || "2022-08-27T07:50:29.829Z";
  const dateObject = new Date(importDate);
  console.log("data", data);

  console.log("Date Object:", dateObject);

  const formattedDate = dateObject.toLocaleString("en-US", {
    month: "long",
    day: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
  const responseInMarathi = {
    blankStreetCount: "रस्त्याचे नाव/ गल्लीचे नाव",
    blankZoneNameCount: "झोन ",
    blankPropertyNumberCount: "मालमत्ता क्रमांक",
    blankOldPropertyNumberCount: "जुना मालमत्ता क्रमांक",
    blankOwnerNameCount: "मालकाचे नाव",
    blankOwnerTypeCount: "वापरकर्ता",
    blankUsageTypeCount: "वापर",
    blankUsageDescCount: "मालमत्तेचे वर्णन",
    blankConstructionYearCount: "मिळकत बांधकामाचे वर्ष",
    blankLengthCount: "लांबी",
    blankWidthCount: "रुंदी",
    blankSqftCount: "क्षेत्रफळ (चौ. फूट )",
    blankSqmeterCount: "क्षेत्रफळ (चौ. मी.)",
    okCount: " बरोबर मालमत्ता",
    totalCount: "एकूण मालमत्ता",
  };

  const handleSort = () => {
    if (sortOrder === "asc") {
      setSortOrder("desc");
    } else {
      setSortOrder("asc");
    }
  };

  const sortedData = data
  ? Object.entries(data)
      .slice(0, -3)
      .sort(([keyA, valueA], [keyB, valueB]) => {
        const numA = Number(valueA);
        const numB = Number(valueB);

        if (isNaN(numA) || isNaN(numB)) {
          return 0; // Or another default value or behavior
        }

        if (sortOrder === "asc") {
          return numA - numB;
        } else if (sortOrder === "desc") {
          return numB - numA;
        } else {
          return 0;
        }
      })
  : [];


  return (
    <div className="flex h-fit">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <h1 className="text-2xl font-semibold font-Poppins   w-full ml-3 ">
      GIS Data
        </h1>
        <WhiteContainer>
          <div className="mb-3">
            <div className="grid md:grid-cols-4 gap-x-3 gap-y-3">
              <div className="grid-cols-subgrid">
                <Select onValueChange={setSelectedWard} value={selectedWard}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select Ward" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value="0">All</SelectItem>

                      <SelectItem value="1">Ward 1</SelectItem>
                      <SelectItem value="2">Ward 2</SelectItem>
                      <SelectItem value="3">Ward 3</SelectItem>
                      <SelectItem value="4">Ward 4</SelectItem>
                      <SelectItem value="5">Ward 5</SelectItem>
                      <SelectItem value="6">Ward 6</SelectItem>
                      <SelectItem value="7">Ward 7</SelectItem>
                      <SelectItem value="8">Ward 8</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid-cols-subgrid mt-1">
                <Button
                  // className="w-1/2"
                  variant="submit"
                  disabled={!selectedWard}
                  onClick={handleSearch}
                >
                  शोधा
                </Button>
              </div>
            </div>
          </div>
        </WhiteContainer>

        {loading && (
          <div className="flex justify-center items-center my-4">
                      <Loader/>

          </div>
        )}

        {data && !loading && (
          <WhiteContainer className="pt-2 relative">
            <p className="text-sm italic text-gray-500 text-end absolute right-4">
              *शेवटचे अद्यतनित केले: {formattedDate}
            </p>

            <div className="my-3 mb-5 grid grid-cols-4 gap-3">
              <div className="grid-cols-subgrid">
                <Label className="font-medium">{`एकूण मालमत्ता`}</Label>
                <Input disabled className="mt-1" value={data.totalCount || 0} />
              </div>
              <div className="grid-cols-subgrid">
                <Label className="font-medium">{`योग्य मालमत्ता`}</Label>
                <Input disabled className="mt-1" value={data.okCount || 0} />
              </div>
            </div>

            <div>
              <Table className="w-full border border-gray-500 rounded-20">
                <TableHeader>
                  <TableRow>
                    <TableHead className="border border-gray-500 bg-gray-200 font-semibold text-black  a">
                      Field name
                    </TableHead>
                    <TableHead
                      onClick={handleSort}
                      className="border bg-gray-200 border-gray-500 cursor-pointer font-semibold text-black"
                    >
                      Total values
                      {sortOrder === "asc" && <span> ⇅</span>}
                      {sortOrder === "desc" && <span> ⇅</span>}
                      {sortOrder === null && <span> ⇅</span>}{" "}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedData.map(([key, value]) => (
                    <TableRow key={key}>
                      <TableCell className="border border-gray-500 ">
                        {responseInMarathi[key]}
                      </TableCell>
                      <TableCell className="border border-gray-500 ">
                        {value}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            <div className="flex items-center justify-between w-full mt-5">
              <div className=" justify-end ">
                <Button disabled={downloadLoading} onClick={handleDownload}>
                  Download Sheet
                </Button>
                <p className="text-sm text-gray-500 mt-1">
                  *Download Imported sheet
                </p>
              </div>

              <div className="">
                <div className="relative flex">
                  <Label className="sr-only">Upload Files</Label>
                  <Input
                    type="file"
                    accept=".xlsx"
                    onChange={handleFileChange}
                    className="sr-only"
                    id="file-input"
                  />
                  <Label htmlFor="file-input" className="cursor-pointer">
                    <div
                      className={`flex items-center justify-center h-full border-2 border-dashed rounded-md border-gray-300 p-2 w-[170px] mr-3 overflow-hidden bg`}
                      style={{
                        background: `linear-gradient(to right, #3b82f6 ${uploadProgress}%, transparent ${uploadProgress}%)`,
                      }}
                    >
                      {!selectedFile && (
                        <PlusIcon className="h-5 w-5 text-gray-400" />
                      )}
                      <span className={`ml-2 text-gray-700 ${selectedFile?"text-[10px]":"text-nowrap"}`}>
                        {selectedFile ? selectedFile.name : "Select a file"}
                      </span>
                    </div>
                  </Label>
                  <Button
                    className="  h-8 sm:h-10 bg-blue-500 text-white"
                    onClick={handleUpload}
                    disabled={!selectedFile || uploadLoading}
                  >
                    {uploadLoading ? "Uploading..." : "Upload"}
                  </Button>
                </div>

                {/* {uploadLoading && (
                  <div className="mt-2 w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 border-2">
                    <div
                      className="bg-blue-600 h-2.5 rounded-full"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                )} */}

                <p className="text-sm text-gray-500 mt-1 pl-1">
                  *Upload Corrected sheet here
                </p>
              </div>
            </div>
          </WhiteContainer>
        )}
      </div>
    </div>
  );
};

export default GisDataTable;
