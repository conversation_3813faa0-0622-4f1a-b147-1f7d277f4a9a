import { Repository } from 'typeorm';
import { ModuleMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class ModuleMasterRepository extends Repository<ModuleMasterEntity> {
  constructor(
    @InjectRepository(ModuleMasterEntity)
    private readonly moduleMasterRepository: Repository<ModuleMasterEntity>,
  ) {
    super(
      moduleMasterRepository.target,
      moduleMasterRepository.manager,
      moduleMasterRepository.queryRunner,
    );
  }

  async saveData(input: any) {
    try {
      let data = this.moduleMasterRepository.create(input);
      data = await this.moduleMasterRepository.save(data);
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getAll() {
    return await this.moduleMasterRepository
      .createQueryBuilder('module_master')
      .leftJoinAndSelect('module_master.forms', 'form_master')
      .select([
        'module_master.module_id',
        'module_master.moduleName',
        'form_master.form_id',
        'form_master.formName',
      ])
      .getMany();
  }

  async getOne() {}

  async updateData() {}

  async deleteData() {}
}
