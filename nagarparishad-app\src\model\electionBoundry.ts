export interface ElectionBoundaryMasterObject {
  electionBoundary_id: string;
  electionBoundaryName: string;
  createdAt?: string;
  updatedAt?: string;
  deleteAt?: string | null;
}

export interface ElectionBoundaryListAllApi {
  statusCode: number;
  message: string;
  data: ElectionBoundaryMasterObject[];
}
export interface ElectionBoundaryGetOne {
  electionBoundary_id: string;
}

export interface ElectionBoundaryCreateApi {
  statusCode: number;
  message: string;
  data: ElectionBoundaryMasterObject;
}

export interface ElectionBoundaryUpdateApi {
  statusCode: number;
  message: string;
}

export interface ElectionBoundarySendApiObj {
  electionBoundaryName: string;
}
