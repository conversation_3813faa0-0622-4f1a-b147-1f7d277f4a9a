import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTranslation } from "react-i18next";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useZoneMasterController } from "@/controller/master/ZoneMasterController";
import {
  WardObjectInterface,
  ZoneMasterAddApiResp,
  ZoneObject,
} from "../../../model/zone-master";
import Api from "@/services/ApiServices";
import { useToast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { Loader2 } from "lucide-react";
import { Calendar as CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import AsyncSelect from "@/components/ui/react-select";
import { useWardMasterController } from "@/controller/master/WardMasterController";
import { ReactselectInterface } from "@/model/global-master";
import { useUsageMasterController } from "@/controller/master/UsageMasterController";
import { useUsageSubController } from "@/controller/master/UsageSubController";
import { UsageObjectInterface } from "@/model/usage-master";
import { UsageSubObjectInterface } from "@/model/usagesub-master";

interface ZoneMasterInterface {
  btnTitle: string;
  editData?: ZoneObject;
}

const ReadyRecknerRateForm = ({ btnTitle, editData }: ZoneMasterInterface) => {
  const { t } = useTranslation();

  const schema = z.object({
    fromDate: z.date({
      required_error: "A date of birth is required.",
    }),
    toDate: z.date({
      required_error: "A date of birth is required.",
    }),
    zone: z.string().trim().min(1, t("errorsRequiredField")),
    ward: z.string().trim().min(1, t("errorsRequiredField")),
    usage: z.string().trim().min(1, t("errorsRequiredField")),
    usageSub: z.string().trim().min(1, t("errorsRequiredField")),
    serveNo: z.number().min(1, t("errorsRequiredField")),
    readyRecokner: z.string().min(1, t("errorsRequiredField")),
    valueRate: z.string().min(1, t("errorsRequiredField")),
  });
  const { toast } = useToast();
  const [loader, setLoader] = useState(false);
  const { setIsCollapseOpen, refreshZoneList, setRefreshZoneList } =
    useContext(GlobalContext);
  //console.log("edita data",editData)

  const dynamicValues = {
    name: t("zone.zoneLabel"),
  };

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      zone: editData?.zoneName || "",
      ward: editData?.ward?.ward_id || "",
      usage: editData?.ward?.ward_id || "",
      usageSub: editData?.ward?.ward_id || "",
      serveNo: editData?.ward?.ward_id || 0,
      readyRecokner: editData?.ward?.ward_id || "",
      valueRate: editData?.ward?.ward_id || "",
    },
  });
  const {
    formState: { errors },
    reset,
  } = form;

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();
    const DataResponse = {
      zoneName: data.zone,
      ward: data.ward,
    };
    if (editData?.zone_id !== undefined && editData?.zone_id !== null) {
      setLoader(true);
      Api.updateZone(
        editData?.zone_id,
        DataResponse,
        (response: { status: boolean; data: ZoneMasterAddApiResp }) => {
          if (response.status && response.data.statusCode === 200) {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            form.reset({
              zone: "",
              ward: "",
            });
            setIsCollapseOpen(false);
            setRefreshZoneList(!refreshZoneList);
            setLoader(false);
          } else {
            toast({
              title: response.data.message,
              variant: "destructive",
            });
            setLoader(false);
          }
        },
      );
    } else {
      setLoader(true);

      Api.createZone(
        DataResponse,
        (response: { status: boolean; data: ZoneMasterAddApiResp }) => {
          if (response.status && response.data.statusCode === 201) {
            toast({
              title: t("api.formcreate", dynamicValues),
              variant: "success",
            });

            reset();
            setIsCollapseOpen(false);
            setRefreshZoneList(!refreshZoneList);
            setLoader(false);
            form.reset({
              zone: "",
              ward: "",
            });
          } else {
            toast({
              title: response.data.message,
            });
            setLoader(false);
          }
        },
      );
    }
  };

  useEffect(() => {
    if (editData) {
      form.reset({
        zone: editData.zoneName || "",
        ward: editData.ward?.ward_id || "",
      });
    } else {
      form.reset({
        zone: "",
        ward: "",
      });
    }
  }, [editData]);

  const [selectedWard, setselectedWard] = useState({});

  const wardList: any = useWardMasterController();
  const wardOptions: ReactselectInterface = wardList?.wardList?.map(
    (ward: WardObjectInterface) => ({
      value: ward.ward_id,
      label: ward.ward_name,
    }),
  );

  const zoneList: any = useZoneMasterController();
  const filteredZones = zoneList?.zoneList?.filter(
    (zone: any) => zone.ward?.ward_id === selectedWard?.value,
  );

  const zoneOptions: ReactselectInterface = filteredZones?.map(
    (zone: ZoneObject) => ({
      value: zone.zone_id,
      label: zone.zoneName,
    }),
  );

  const usageList: any = useUsageMasterController();
  const usageOptions: ReactselectInterface = usageList.usageList?.map(
    (usagetype: UsageObjectInterface) => ({
      value: usagetype.usage_id,
      label: usagetype.usage,
    }),
  );

  const usageSubList: any = useUsageSubController();
  const usagesubOptions: ReactselectInterface = usageSubList.usageSubList?.map(
    (usagesubtype: UsageSubObjectInterface) => ({
      value: usagesubtype.usageSub_id,
      label: usagesubtype.subUsage,
    }),
  );

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className=" ">
          <div className="form-flex">
            <div className="form-element">
              <FormField
                control={form.control}
                name="fromDate"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("readyreckonerrate.fromDate")}</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !field.value && "text-muted-foreground",
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>{t("readyreckonerrate.pickaDate")}</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Calendar
                          className="dashboard-calender"
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </FormItem>
                )}
              />
            </div>
            <div className="form-element">
              <FormField
                control={form.control}
                name="toDate"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("readyreckonerrate.toDate")}</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !field.value && "text-muted-foreground",
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>{t("readyreckonerrate.pickaDate")}</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Calendar
                          className="dashboard-calender"
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="form-flex">
            <div className="form-element">
              <FormField
                control={form.control}
                name="ward"
                render={({ field }) => (
                  <FormItem className=" ">
                    <FormLabel>
                      {t("propertyLocationDetailsForm.ward")}
                    </FormLabel>
                    <FormControl>
                      <AsyncSelect
                        placeholder={t("propertyLocationDetailsForm.ward")}
                        options={wardOptions}
                        value={
                          wardOptions &&
                          wardOptions?.find(
                            (option: any) => option.value === field.value,
                          )
                        }
                        onChange={(selectedOption: any) => {
                          setselectedWard(selectedOption);
                          field.onChange(selectedOption.value);
                        }} // Use selectedOption.value
                        onValue
                        colourOptions={wardOptions}
                      />
                    </FormControl>
                    {errors.ward && (
                      <FormMessage className="ml-1">
                        {" "}
                        {t("errorsRequiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="form-element">
              <FormField
                control={form.control}
                name="zone"
                render={({ field }) => (
                  <FormItem className=" ">
                    <FormLabel>
                      {t("propertyLocationDetailsForm.zone")}
                    </FormLabel>
                    <FormControl>
                      <AsyncSelect
                        placeholder={t("propertyLocationDetailsForm.zone")}
                        options={zoneOptions}
                        value={
                          zoneOptions &&
                          zoneOptions?.find(
                            (option: any) => option.value === field.value,
                          )
                        }
                        onChange={(selectedOption: any) =>
                          field.onChange(selectedOption.value)
                        } // Use selectedOption.value
                        colourOptions={zoneOptions}
                      />
                    </FormControl>
                    {errors.zone && (
                      <FormMessage className="ml-1">
                        {" "}
                        {t("errorsRequiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="form-flex">
            <div className="form-element">
              <FormField
                control={form.control}
                name="usage"
                render={({ field }) => (
                  <FormItem className=" ">
                    <FormLabel>
                      {t("propertyAssessmentDetailsForm.propertyUsageType")}
                    </FormLabel>
                    <FormControl>
                      <AsyncSelect
                        placeholder={t(
                          "propertyAssessmentDetailsForm.propertyUsageType",
                        )}
                        options={usageOptions}
                        value={
                          usageOptions &&
                          usageOptions?.find(
                            (option: any) => option.value === field.value,
                          )
                        }
                        onChange={(selectedOption: any) =>
                          field.onChange(selectedOption.value)
                        } // Use selectedOption.value
                        colourOptions={usageOptions}
                      />
                    </FormControl>
                    {errors.usage && (
                      <FormMessage className="ml-1">
                        {" "}
                        {t("errorsRequiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="form-element">
              <FormField
                control={form.control}
                name="usageSub"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t("propertyAssessmentDetailsForm.propertyUsageSubType")}
                    </FormLabel>
                    <FormControl>
                      <AsyncSelect
                        placeholder={t(
                          "propertyAssessmentDetailsForm.propertyUsageSubType",
                        )}
                        options={usagesubOptions}
                        value={
                          usagesubOptions &&
                          usagesubOptions?.find(
                            (option: any) => option.value === field.value,
                          )
                        }
                        onChange={(selectedOption: any) =>
                          field.onChange(selectedOption.value)
                        } // Use selectedOption.value
                        colourOptions={usagesubOptions}
                      />
                    </FormControl>
                    {errors.usageSub && (
                      <FormMessage className="ml-1"></FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="form-flex">
            <div className="form-element">
              <FormField
                control={form.control}
                name="serveNo"
                render={({ field }) => (
                  <FormItem className=" ">
                    <FormLabel>{t("readyreckonerrate.surveyNumber")}</FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1 block w-full"
                        type="text"
                        placeholder={t("readyreckonerrate.surveyNumber")}
                        {...field}
                      />
                    </FormControl>
                    {errors.serveNo && (
                      <FormMessage className="ml-1">
                        {t("errorsRequiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="form-element">
              <FormField
                control={form.control}
                name="readyRecokner"
                render={({ field }) => (
                  <FormItem className=" ">
                    <FormLabel>
                      {t("readyreckonerrate.readyreckoner")}
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1 block w-full"
                        type="text"
                        placeholder={t("readyreckonerrate.readyreckoner")}
                        {...field}
                      />
                    </FormControl>
                    {errors.readyRecokner && (
                      <FormMessage className="ml-1">
                        {t("errorsRequiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="form-flex">
            <div
              className="sm:w-[49%]
            "
            >
              <FormField
                control={form.control}
                name="valueRate"
                render={({ field }) => (
                  <FormItem className=" ">
                    <FormLabel>{t("readyreckonerrate.valueRate")}</FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1 block w-full"
                        type="text"
                        placeholder={t("readyreckonerrate.valueRate")}
                        {...field}
                      />
                    </FormControl>
                    {errors.valueRate && (
                      <FormMessage className="ml-1">
                        {t("errorsRequiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="w-full flex justify-end mt-4">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-5 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
        </form>
      </Form>
    </>
  );
};

export default ReadyRecknerRateForm;
