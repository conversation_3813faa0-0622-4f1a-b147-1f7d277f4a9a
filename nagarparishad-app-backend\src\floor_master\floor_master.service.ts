import { Injectable, NotFoundException } from '@nestjs/common';
import { Floor_Master } from 'libs/database/entities';
import { FloorMasterRepository } from 'libs/database/repositories/floorMaster.repository';
import { CreateFloorMasterDto } from './dto/create-floor-master.dto';
import { UpdatePropertyTypeClassDto } from './dto/update-floor-master.dto';
  // Adjust import path

@Injectable()
export class FloorMasterService {
  constructor(private readonly floorMasterRepository: FloorMasterRepository) {}

  // Retrieve all floors
  async getAllFloors(): Promise<{ message: string; data: Floor_Master[] }> {
    try {
      const allFloors = await this.floorMasterRepository.getAll();
      if (!allFloors.length) {
        throw new NotFoundException('No floors found');
      }
      return {
        message: 'Floors fetched successfully',
        data: allFloors,
      };
    } catch (error) {
      throw new NotFoundException('Failed to fetch floors');
    }
  }

  // Retrieve a single floor by ID
  async getFloorById(id: string): Promise<Floor_Master> {
    try {
      const floor = await this.floorMasterRepository.findById(id);
      if (!floor) {
        throw new NotFoundException(`Floor with ID ${id} not found`);
      }
      return floor;
    } catch (error) {
      throw new NotFoundException('Failed to fetch floor');
    }
  }

  // Create a new floor
  async create(createFloorDto: CreateFloorMasterDto): Promise<Floor_Master> {
    try {
      const newFloor = await this.floorMasterRepository.save(createFloorDto);
      return newFloor;
    } catch (error) {
      throw new NotFoundException('Failed to create floor');
    }
  }

  // Update a floor by ID
  async updateFloor(id: string, updateFloorDto: UpdatePropertyTypeClassDto): Promise<Floor_Master> {
    try {
      const existingFloor = await this.floorMasterRepository.findById(id);
      if (!existingFloor) {
        throw new NotFoundException(`Floor with ID ${id} not found`);
      }

      // Update the existing floor data
      Object.assign(existingFloor, updateFloorDto);

      return await this.floorMasterRepository.save(existingFloor);
    } catch (error) {
      throw new NotFoundException('Failed to update floor');
    }
  }

  // Delete a floor by ID
  async deleteFloor(id: string): Promise<void> {
    try {
      // const existingFloor = await this.floorMasterRepository.f(id);
      // if (!existingFloor) {
      //   throw new NotFoundException(`Floor with ID ${id} not found`);
      // }

      await this.floorMasterRepository.softDelete(id);
    } catch (error) {
      throw new NotFoundException('Failed to delete floor');
    }
  }
}
