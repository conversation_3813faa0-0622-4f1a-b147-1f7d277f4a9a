import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import {
  CreatePropertyTypeMasterDto,
  PropertyTypeMasterIdDto,
  UpdatePropertyTypeMasterDto,
} from './dto/property-type_master.dto';
import { PropertyTypeClassMasterRepository, PropertyTypeMasterRepository } from 'libs/database/repositories';
import { ChangePropertyTypeClassDto } from './dto/property-type_class.dto';

@Injectable()
export class PropertyTypeMasterService {
  constructor(
    private readonly propertyTypeMasterRepository: PropertyTypeMasterRepository,
    private readonly propertyTypeClassRepository: PropertyTypeClassMasterRepository,
  ) {}

  async create(createPropertyTypeMasterDto: CreatePropertyTypeMasterDto) {
    try {
      const saveData = await this.propertyTypeMasterRepository.saveData(
        createPropertyTypeMasterDto,
      );

      return {
        message: 'Record Saved SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllData =
        await this.propertyTypeMasterRepository.findAllLocation();

      if (!getAllData) {
        throw new NotFoundException('Record Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(propertyTypeMaster: PropertyTypeMasterIdDto) {
    try {
      const { propertyType_id } = propertyTypeMaster;
      const checkData =
        await this.propertyTypeMasterRepository.findById(propertyType_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      return {
        message: 'Data found Sucess',
        data: checkData,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    propertyTypeMaster: PropertyTypeMasterIdDto,
    updatePropertyTypeMasterDto: UpdatePropertyTypeMasterDto,
  ) {
    try {
      const { propertyType_id } = propertyTypeMaster;
      const checkData =
        await this.propertyTypeMasterRepository.findById(propertyType_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      const updateData = await this.propertyTypeMasterRepository.updateData(
        propertyType_id,
        updatePropertyTypeMasterDto,
      );

      if (updateData.affected === 0) {
        throw new NotAcceptableException('Failed to update Data');
      }

      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(propertyTypeMaster: PropertyTypeMasterIdDto) {
    try {
      const { propertyType_id } = propertyTypeMaster;
      const checkData =
        await this.propertyTypeMasterRepository.findById(propertyType_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      const deleteData =
        await this.propertyTypeMasterRepository.deleteData(propertyType_id);

      if (deleteData.affected === 0) {
        throw new NotAcceptableException('Failed to delete data');
      }

      return {
        message: 'data Delete Successfully',
        data: deleteData,
      };
    } catch (error) {
      throw error;
    }
  }
  
  async updatePropertyTypeClass(ChangePropertyTypeClass: ChangePropertyTypeClassDto) {
    try{
      const checkData =
      await this.propertyTypeMasterRepository.findById(ChangePropertyTypeClass.propertyType_id);

      const updateType =
      await this.propertyTypeClassRepository.findById(ChangePropertyTypeClass.propertyTypeClass_id)

      if (!checkData || !updateType ) {
        throw new NotFoundException('Data Not found');
      }
      checkData.property_type_class = updateType; 
  
      await this.propertyTypeMasterRepository.save(checkData);
  
      return {
        message: `Property Type Class changed successfully`,
        data: checkData,
      };
    }catch(error) {
      throw error;
    }
  }

  async getPropertyTypeClass(propertyType_id: string) {
    try{

      const checkData = await this.propertyTypeMasterRepository.findOne_class(propertyType_id);
      
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }  
            return {
        message: `Property Type Class retrieved successfully`,
       data: checkData.property_type_class.property_type_class
      }
      }catch(error) {
      throw error;
    }
  }
  async findAllGroupedByClass() {
    try{

      const properties = await this.propertyTypeMasterRepository.find({
        relations: ['property_type_class'],
        where: { deletedAt: null },
      });
      
      const groupedData = properties.reduce((acc, property) => {
        const className = property.property_type_class?.property_type_class || 'Uncategorized';
  
        if (!acc[className]) {
          acc[className] = [];
        }
  
        acc[className].push({
          propertyType_id: property.propertyType_id,
          propertyType: property.propertyType,
        });
        
        return acc;
      }, {} as Record<string, { propertyType_id: string; propertyType: string }[]>);
      
      return {
        message : "All propertyType classes and their respective propertyTypes",
       data: groupedData
      }
    } catch (error) {
      throw(error);
    }
    }
  
  }
