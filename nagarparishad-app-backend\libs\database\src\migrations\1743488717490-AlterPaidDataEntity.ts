import { MigrationInterface, QueryRunner } from "typeorm";

export class AlterPaidDataEntity1743488717490 implements MigrationInterface {
    name = 'AlterPaidDataEntity1743488717490'

  public async up(queryRunner: QueryRunner): Promise<void> {
      
    // Rename the old property_id column to preserve existing data
    await queryRunner.query(
        `ALTER TABLE "paid_data" RENAME COLUMN "property_id" TO "old_property_id";`
    );

    // Ensure old_property_id is casted to UUID properly
    await queryRunner.query(
        `ALTER TABLE "paid_data" ALTER COLUMN "old_property_id" TYPE UUID USING "old_property_id"::UUID;`
    );

    // Add new property_id column as UUID with foreign key constraint
    await queryRunner.query(
        `ALTER TABLE "paid_data" ADD COLUMN "property_id" UUID;`
    );
    
    // Add foreign key constraint
    await queryRunner.query(`ALTER TABLE "paid_data" ADD CONSTRAINT "FK_8ca6a648c2ccf657fa24519b077" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE SET NULL ON UPDATE NO ACTION`);

    // Migrate existing data: Match the property table correctly
    await queryRunner.query(`
        UPDATE "paid_data" 
        SET "property_id" = (
          SELECT "property_id" FROM "property" 
          WHERE "property"."property_id" = "paid_data"."old_property_id"
        )::UUID
        WHERE "old_property_id" IS NOT NULL;
    `);     


    // Drop the old property_id column after verifying data migration
    await queryRunner.query(
      `ALTER TABLE "paid_data" DROP COLUMN "old_property_id";`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back old property_id column
    await queryRunner.query(
      `ALTER TABLE "paid_data" ADD COLUMN "old_property_id" UUID;`
    );

    // Restore old property_id values from property_id
    
    // Drop the new foreign key constraint and column
    await queryRunner.query(
        `ALTER TABLE "paid_data" DROP CONSTRAINT "FK_8ca6a648c2ccf657fa24519b077";`
    );
    
    await queryRunner.query(
      `UPDATE "paid_data" SET "old_property_id" = "property_id";`
    );
    await queryRunner.query(
      `ALTER TABLE "paid_data" DROP COLUMN "property_id";`
    );

    // Rename back old_property_id to property_id
    await queryRunner.query(
      `ALTER TABLE "paid_data" RENAME COLUMN "old_property_id" TO "property_id";`
    );
  }
}


