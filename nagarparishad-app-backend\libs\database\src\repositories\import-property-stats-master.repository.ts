import { Repository } from 'typeorm';
import { Import_PropertyStatsEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import {
  PaginatedResult,
  PaginationOptions,
  paginate,
} from '@helper/helpers/Pagination';
import { Logger } from '@nestjs/common';

export class ImportPropertyStatsMasterRepository extends Repository<Import_PropertyStatsEntity> {
  constructor(
    @InjectRepository(Import_PropertyStatsEntity)
    private readonly import_PropertyStatsRepository: Repository<Import_PropertyStatsEntity>,
  ) {
    super(
      import_PropertyStatsRepository.target,
      import_PropertyStatsRepository.manager,
      import_PropertyStatsRepository.queryRunner,
    );
  }

  async saveData(input: any) {
    let data = this.import_PropertyStatsRepository.create(input);
    data = await this.import_PropertyStatsRepository.save(data);
    return data;
  }



  async findAllData(): Promise<Import_PropertyStatsEntity[]> {
    return await this.import_PropertyStatsRepository
      .createQueryBuilder('import_property_stats')
      .orderBy('import_property_stats.updated_at', 'DESC')
      .getMany();
  }

  async getLastStatData_ofWard(ward_name) {

    return await this.import_PropertyStatsRepository
      .createQueryBuilder('import_property_stats')
      .select([
        'import_property_stats.blankStreetCount',
        'import_property_stats.blankZoneNameCount',
        'import_property_stats.blankPropertyNumberCount',
        'import_property_stats.blankOldPropertyNumberCount',
        'import_property_stats.blankOwnerNameCount',
        'import_property_stats.blankOwnerTypeCount',
        'import_property_stats.blankUsageTypeCount',
        'import_property_stats.blankUsageDescCount',
        'import_property_stats.blankConstructionYearCount',
        'import_property_stats.blankLengthCount',
        'import_property_stats.blankWidthCount',
        'import_property_stats.blankSqftCount',
        'import_property_stats.blankSqmeterCount',
        'import_property_stats.okCount',
        'import_property_stats.totalCount',
        'import_property_stats.ward_name',
        'import_property_stats.import_date',
       
       
      ])
      .where(
        '( import_property_stats.ward_name = :ward_name',
        { ward_name}
      )
      .orderBy('import_property_stats.created_at', 'DESC')
      .getOne();
    }

    async getLastSyncDate(ward_name) {
      return await this.import_PropertyStatsRepository
        .createQueryBuilder('import_property_stats')
        .select(['import_property_stats.import_date'])
        .where(
          '( import_property_stats.ward_name = :ward_name)',
          { ward_name}
        )
        .orderBy('import_property_stats.created_at', 'DESC')
        .getOne();
  
     
  
    }








}
