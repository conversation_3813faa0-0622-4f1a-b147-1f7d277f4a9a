import { Controller, Get, Param, Post, Query } from "@nestjs/common";
import { DashboardService } from "./dashboard_stat.service";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Form, Permissions } from "@helper/helpers/role-based-access/permission.decorator";

@ApiTags('dashboard-stats')
@Controller('dashboard')
export class DashboardController{

    constructor(
        private readonly dashboardService: DashboardService
    ){}



    // @Form('Property')
    // @Permissions('can_read')
    @ApiOperation({ summary: 'Get all Total Property CounStats' })
    @ApiResponse({ status: 200, description: 'Returns all Total Property CounStats' })
    @Get('total-property-stats')
    findAllTotalPropertyStats(@Query('year') year: string) {
        return this.dashboardService.findAllTotalPropertyStats();
    }


    // @Form('Property')
    // @Permissions('can_read')
    @ApiOperation({ summary: 'Get Ward-wise Property Stats' })
    @ApiResponse({ status: 200, description: 'Returns Ward-wise Property Stats' })
    @Get('ward-property-stats')
    async findWardPropertyStats(@Query('ward') ward?: string) {
        if (ward) {
            // Fetch data for the specific ward
            return this.dashboardService.findWardPropertyStats(ward);
        } else {
            // Fetch data for all wards
            return this.dashboardService.findAllWardPropertyStats();
        }
    }

    // @Form('Property')
    // @Permissions('can_read')
    @ApiOperation({ summary: 'Get all Total Property CounStats' })
    @ApiResponse({ status: 200, description: 'Returns all Total Property CounStats' })
    @Get('total-property-type-stats')
    paidTaxesForPropertyTypes
    () {
        return this.dashboardService.propertyTypeStats();
    }

    // @Form('Property')
    // @Permissions('can_read')
    @ApiOperation({ summary: 'Get Usage Type-wise Property Stats' })
    @ApiResponse({ status: 200, description: 'Returns Usage Type-wise Property Stats' })
    @Get('usage-type-property-stats')
    async findUsageTypePropertyStats(@Query('usageType') usageType?: string) {
        if (usageType) {
            return this.dashboardService.findUsageTypePropertyStats(usageType);
        } else {
            return this.dashboardService.findAllUsageTypePropertyStats();
        }
    }


    
  @Get('recent-transactions')
  getRecentTransactions() {
    return this.dashboardService.getRecentTransactions();
  }

  @Get('tax-collection-trend')
  getTaxCollectionTrend(@Query('financialYear') financialYear: string) {
    return this.dashboardService.getTaxCollectionTrend(financialYear);
  }

  @Get('ward-performance')
  getWardPerformance() {
    return this.dashboardService.getWardPerformance();
  }

  @Get('defaulter-analysis')
  getDefaulterAnalysis() {
    return this.dashboardService.getDefaulterAnalysis();
  }

}