import {
  BaseEntity,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>um<PERSON>,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PropertyEntity } from './property.entity';
import { Tax_Type_Master } from './tax_type_master.entity';
import { Tax_PropertyEntity } from './tax_property.entity';
@Entity('tax_property_other_taxes')
export class Tax_PropertyEntity_Other_Taxes extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  tax_property_other_taxes_id: string;


  @ManyToOne(() => PropertyEntity, (property) => property.property_id)
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

 
  @ManyToOne(() => Tax_PropertyEntity, (tax_Property) => tax_Property.tax_property_id)
  @JoinColumn({ name: 'tax_property_id' })
  tax_Property: Tax_PropertyEntity;

  @ManyToOne(() => Tax_Type_Master, (tax_type_master) => tax_type_master.tax_type_id)
  @JoinColumn({ name: 'tax_type_id' })
  tax_type_master: PropertyEntity;



  @Column({ type: String, nullable: false })
  tax_type: string;
  
  @Column({ type: 'float', nullable: false })
  amount: number;


  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
