import { Repository } from 'typeorm';
import { PropertyTypeMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class PropertyTypeRepository extends Repository<PropertyTypeMasterEntity> {
  constructor(
    @InjectRepository(PropertyTypeMasterEntity)
    private readonly propertyTypeRepository: Repository<PropertyTypeMasterEntity>,
  ) {
    super(
      propertyTypeRepository.target,
      propertyTypeRepository.manager,
      propertyTypeRepository.queryRunner,
    );
  }

 

//   async findAllLocation() {
//     return await this.locationRepository
//       .createQueryBuilder('location_master')
//       .orderBy('location_master.updated_at', 'DESC')
//       .getMany();
//   }

//   async findById(location_id: string) {
//     return await this.locationRepository
//       .createQueryBuilder('location_master')
//       .where('location_master.location_id = :location_id', { location_id })
//       .getOne();
//   }

//   async updateLocation(location_id: string, input: { locationName?: string }) {
//     return await this.locationRepository
//       .createQueryBuilder('location_master')
//       .update(LocationEntity)
//       .set(input)
//       .where('location_id = :location_id', { location_id })
//       .execute();
//   }

//   async deleteLocation(location_id: string) {
//     return await this.locationRepository
//       .createQueryBuilder('location_master')
//       .softDelete()
//       .where('location_id = :location_id', { location_id })
//       .execute();
//   }
 }
