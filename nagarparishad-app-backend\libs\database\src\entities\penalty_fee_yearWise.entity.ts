import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
  } from 'typeorm';
  import { Financial_year } from './financial_year.entity';
  import { PropertyEntity } from './property.entity';

  @Entity('penalty_fee_yearWise')
  export class PenaltyFeeYearWiseEntity extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    penalty_fee_id: string;

    @ManyToOne(() => PropertyEntity, (property) => property.property_id)
    @JoinColumn({ name: 'property_id' })
    property: PropertyEntity;

    @Column({ type: String, nullable: false })
    financial_year: string;

    @Column({ name: 'actual_value', nullable: false, type: 'decimal' })
    actual_value: number;

    @Column({ name: 'penalty_value', nullable: false, type: 'decimal' })
    penalty_value: number;

    @Column({ name: 'tax_percentage', nullable: false })
    tax_percentage: number;



    /*
     * <PERSON>reate and Update Date Columns
     */
    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;

    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;

    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
  }
