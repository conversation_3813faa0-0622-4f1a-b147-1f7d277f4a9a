import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaidAndNonPaidDataEntity } from '../entities/paid-and-non-paid-data.entity';
import {
  PaginatedResult,
  PaginationOptions,
  paginate,
} from '@helper/helpers/Pagination';

export class PaidAndNonPaidDataRepository extends Repository<PaidAndNonPaidDataEntity> {
  constructor(
    @InjectRepository(PaidAndNonPaidDataEntity)
    private readonly paidAndNonPaidDataRepository: Repository<PaidAndNonPaidDataEntity>,
  ) {
    super(
      paidAndNonPaidDataRepository.target,
      paidAndNonPaidDataRepository.manager,
      paidAndNonPaidDataRepository.queryRunner,
    );
  }

  // Save a new paid and non-paid data record
  async saveData(input: {
    property_id: string;
    financial_year_id: string;
    financial_year_range: string;
    name: string;
    property_number: string;
    old_property_number?: string;
    total_tax: number;
    paid: number;
    remaining: number;
    is_paid: boolean;
  }): Promise<PaidAndNonPaidDataEntity> {
    const data = this.paidAndNonPaidDataRepository.create({
      property: { property_id: input.property_id },
      financial_year: { financial_year_id: input.financial_year_id },
      financial_year_range: input.financial_year_range,
      name: input.name,
      property_number: input.property_number,
      old_property_number: input.old_property_number,
      total_tax: input.total_tax,
      paid: input.paid,
      remaining: input.remaining,
      is_paid: input.is_paid,
    });
    return await this.paidAndNonPaidDataRepository.save(data);
  }

  // Find all data with pagination
  async findAllData(options: PaginationOptions): Promise<PaginatedResult<PaidAndNonPaidDataEntity>> {
    const queryBuilder = this.paidAndNonPaidDataRepository
      .createQueryBuilder('paid_and_non_paid_data')
      .leftJoinAndSelect('paid_and_non_paid_data.property', 'property')
      .leftJoinAndSelect('paid_and_non_paid_data.financial_year', 'financial_year');

    options.sortBy = options.sortBy || 'updatedAt';
    options.sortOrder = options.sortOrder || 'DESC';

    return await paginate(queryBuilder, options, 'paid_and_non_paid_data');
  }

  // Find data by financial year
  async findByFinancialYear(financialYearRange: string, options: PaginationOptions): Promise<PaginatedResult<PaidAndNonPaidDataEntity>> {
    const queryBuilder = this.paidAndNonPaidDataRepository
      .createQueryBuilder('paid_and_non_paid_data')
      .leftJoinAndSelect('paid_and_non_paid_data.property', 'property')
      .leftJoinAndSelect('paid_and_non_paid_data.financial_year', 'financial_year')
      .where('paid_and_non_paid_data.financial_year_range = :financialYearRange', { financialYearRange });

    options.sortBy = options.sortBy || 'updatedAt';
    options.sortOrder = options.sortOrder || 'DESC';

    return await paginate(queryBuilder, options, 'paid_and_non_paid_data');
  }

  // Find paid users
  async findPaidUsers(financialYearRange?: string, options: PaginationOptions = {},value?: string,searchOn?: string): Promise<PaginatedResult<PaidAndNonPaidDataEntity>> {
    const queryBuilder = this.paidAndNonPaidDataRepository
      .createQueryBuilder('paid_and_non_paid_data')
      .leftJoinAndSelect('paid_and_non_paid_data.property', 'property')
      .leftJoinAndSelect('paid_and_non_paid_data.financial_year', 'financial_year')
      .where('paid_and_non_paid_data.is_paid = :isPaid', { isPaid: true });

    if (financialYearRange) {
      queryBuilder.andWhere('paid_and_non_paid_data.financial_year_range = :financialYearRange', { financialYearRange });
    }
        if (value && searchOn) {
      switch (searchOn) {
        case 'property_number':
          queryBuilder.andWhere('paid_and_non_paid_data.property_number = :value', { value });
          break;
        case 'old_property_number':
          queryBuilder.andWhere('paid_and_non_paid_data.old_property_number = :value', { value });
          break;
        case 'owner_details_name':
          queryBuilder.andWhere('LOWER(paid_and_non_paid_data.name) LIKE LOWER(:value)', { value: `%${value}%` });
          break;
        default:
          // For other fields, try to search in the main table
          queryBuilder.andWhere(`LOWER(paid_and_non_paid_data.${searchOn}) LIKE LOWER(:value)`, { value: `%${value}%` });
          break;
      }
    }

    options.sortBy = options.sortBy || 'updatedAt';
    options.sortOrder = options.sortOrder || 'DESC';

    return await paginate(queryBuilder, options, 'paid_and_non_paid_data');
  }

  // Find non-paid users
  async findNonPaidUsers(financialYearRange?: string, options: PaginationOptions = {},value?: string,searchOn?: string): Promise<PaginatedResult<PaidAndNonPaidDataEntity>> {
    const queryBuilder = this.paidAndNonPaidDataRepository
      .createQueryBuilder('paid_and_non_paid_data')
      .leftJoinAndSelect('paid_and_non_paid_data.property', 'property')
      .leftJoinAndSelect('paid_and_non_paid_data.financial_year', 'financial_year')
      .where('paid_and_non_paid_data.is_paid = :isPaid', { isPaid: false });

    if (financialYearRange) {
      queryBuilder.andWhere('paid_and_non_paid_data.financial_year_range = :financialYearRange', { financialYearRange });
    }
    // Add search functionality
    if (value && searchOn) {
      switch (searchOn) {
        case 'property_number':
          queryBuilder.andWhere('paid_and_non_paid_data.property_number = :value', { value });
          break;
        case 'old_property_number':
          queryBuilder.andWhere('paid_and_non_paid_data.old_property_number = :value', { value });
          break;
        case 'owner_details_name':
          queryBuilder.andWhere('LOWER(paid_and_non_paid_data.name) LIKE LOWER(:value)', { value: `%${value}%` });
          break;
        default:
          // For other fields, try to search in the main table
          queryBuilder.andWhere(`LOWER(paid_and_non_paid_data.${searchOn}) LIKE LOWER(:value)`, { value: `%${value}%` });
          break;
      }
    }

    options.sortBy = options.sortBy || 'updatedAt';
    options.sortOrder = options.sortOrder || 'DESC';

    return await paginate(queryBuilder, options, 'paid_and_non_paid_data');
  }

  // Update existing record
  async updateData(
    id: string,
    input: {
      name?: string;
      total_tax?: number;
      paid?: number;
      remaining?: number;
      is_paid?: boolean;
    },
  ): Promise<PaidAndNonPaidDataEntity> {
    await this.paidAndNonPaidDataRepository.update(id, input);
    return await this.paidAndNonPaidDataRepository.findOne({
      where: { paid_and_non_paid_data_id: id },
      relations: ['property', 'financial_year'],
    });
  }

  // Check if record exists for property and financial year
  async findByPropertyAndFinancialYear(propertyId: string, financialYearRange: string): Promise<PaidAndNonPaidDataEntity> {
    return await this.paidAndNonPaidDataRepository.findOne({
      where: {
        property: { property_id: propertyId },
        financial_year_range: financialYearRange,
      },
      relations: ['property', 'financial_year'],
    });
  }

  // Delete all records for a financial year (for regeneration)
  async deleteByFinancialYear(financialYearRange: string): Promise<void> {
    await this.paidAndNonPaidDataRepository.delete({
      financial_year_range: financialYearRange,
    });
  }
}
