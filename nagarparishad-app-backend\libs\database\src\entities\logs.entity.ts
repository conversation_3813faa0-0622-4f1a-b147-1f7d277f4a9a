import {
  <PERSON>tity,
  Column,
  PrimaryColumn,
  BaseEntity,PrimaryGeneratedColumn, CreateDateColumn
} from 'typeorm';

@Entity('logs')
export class Logs  extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50,nullable: true })
  logType: string;  // Type of log: SUCCESS, ERROR, INFO

  @Column({ type: 'varchar', length: 50,nullable: true })
  logSubType: string;  // Type of log: SAVE,UPDATE,DELETE,FUNCTION_ERROR_CATCH_ERROR,ERROR

  @Column({ type: 'varchar', length: 100,nullable: true })
  file: string;  

  @Column({ type: 'varchar', length: 100 ,nullable: true})
  api: string;  

  @Column({ type: 'text', nullable: true })
  message: string;  // Message or additional information about the log

  @Column({ type: 'json', nullable: true })
  Prev_data: any; 

  @Column({ type: 'json', nullable: true })
  data: any;  // Relevant data, such as the input data or error details

  @Column({ type: 'json', nullable: true })
  user_id: any;  // Relevant data, such as the input data or error details

  @Column({ type: 'json', nullable: true })
  extra: any;  // Relevant data, such as the input data or error details

  // need to commen out when it done

  // @Column({ type: 'varchar', length: 45, nullable: true })
  // ip_address: string;

  // @Column({ type: 'text', nullable: true })
  // user_agent: string;

  // @Column({ type: 'varchar', length: 255, nullable: true })
  // request_url: string;

  // @Column({ type: 'varchar', length: 10, nullable: true })
  // request_method: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;  // Timestamp of when the log was created
}
