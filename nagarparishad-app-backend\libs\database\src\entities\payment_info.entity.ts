import {
  BaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PropertyEntity } from './property.entity';
import { ReceiptEntity } from './receipt.entity';
import { PaidDataEntity } from './paid_data.entity';

// Define Payment Status Enum
export enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
}

@Entity('payment_info')
export class PaymentInfoEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  payment_id: string;

  @ManyToOne(() => PropertyEntity, (property) => property.payments, {
    cascade: true,
    onDelete: 'CASCADE', // This will delete related payment_info records when a property is deleted
  })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;
  

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: false })
  amount: number;

  @Column({ type: String, nullable: true })
  tax_payer_name: string;

    @Column({ type: 'varchar', nullable: true })
  financial_year_range: string;
  
  @Column({ type: 'varchar', length: 50, nullable: false })
  payment_mode: string; // e.g., 'Online', 'Cash', 'Cheque'

  @Column({ type: 'varchar', length: 50, nullable: true })
  transaction_id: string; // Stores online payment transaction reference

  @Column({ type: 'enum', enum: PaymentStatus, default: PaymentStatus.PENDING })
  payment_status: PaymentStatus; // Uses enum instead of boolean for better flexibility

  @Column({ type: 'date', nullable: true })
  payment_date: Date;


  @OneToOne(() => ReceiptEntity, (receipt) => receipt.paymentInfo, {onDelete: 'CASCADE' })
  receipts: ReceiptEntity[];

  @OneToOne(() => PaidDataEntity, (paidData) => paidData.paymentInfo, { onDelete : "CASCADE"})
  paidData: PaidDataEntity;
  /*
   * Create and Update Date Columns
   */
  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
