<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice List</title>
    <style>
        /* Add your styles here */
        .invoice {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        .invoice-header {
            border-bottom: 1px solid #ccc;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        .invoice-details {
            margin-bottom: 20px;
        }
        .item-row {
            border-bottom: 1px solid #eee;
            padding: 5px 0;
        }
        .item-row:last-child {
            border-bottom: none;
        }
        .item-row div {
            display: inline-block;
            width: 25%;
        }
    </style>
</head>
<body>
    <% invoices.forEach(function(invoice) { %>
        <div class="invoice">
            <div class="invoice-header">
                <h2>Invoice Number: <%= invoice.invoiceNumber %></h2>
                <p>Date: <%= invoice.date %></p>
            </div>
            <div class="invoice-details">
                <p><strong>Customer Name:</strong> <%= invoice.customerName.firstName %> <%= invoice.customerName.lastName %></p>
                <p><strong>Customer Address:</strong> <%= invoice.customerAddress.streetAddress %>, <%= invoice.customerAddress.city %>, <%= invoice.customerAddress.state %> <%= invoice.customerAddress.zipCode %></p>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Quantity</th>
                        <th>Unit Price</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <% invoice.items.forEach(function(item) { %>
                        <tr class="item-row">
                            <td><%= item.description %></td>
                            <td><%= item.quantity %></td>
                            <td><%= item.unitPrice.toFixed(2) %></td>
                            <td><%= (item.quantity * item.unitPrice).toFixed(2) %></td>
                        </tr>
                    <% }); %>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3"><strong>Subtotal:</strong></td>
                        <td><%= invoice.subtotal.toFixed(2) %></td>
                    </tr>
                    <tr>
                        <td colspan="3"><strong>Tax (10%):</strong></td>
                        <td><%= invoice.tax.toFixed(2) %></td>
                    </tr>
                    <tr>
                        <td colspan="3"><strong>Total:</strong></td>
                        <td><%= invoice.total.toFixed(2) %></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    <% }); %>
</body>
</html>
