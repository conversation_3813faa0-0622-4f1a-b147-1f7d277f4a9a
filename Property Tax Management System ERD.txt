// Property Tax Management System ERD
// Copy and paste this into https://dbdiagram.io/d

Table billdata {
  id string [pk]
  billNo string
  bill_generation_date date
  property_number string
  fyear string
  property_id string [ref: > property.property_id]
}

Table book_number_master {
  id string [pk]
  book_number int
  availableReceipts json
  receiptsInUse json
  collectorId string [ref: > collector_master.collectorId]
}

Table collector_master {
  collectorId string [pk]
  isActive boolean
  user_id string [ref: - user_master.user_id]
  ward_id string [ref: > ward_master.ward_id]
  role_id string [ref: > role_master.role_id]
}

Table common_fields_of_property {
  id string [pk]
  GISID string
  propertyDescription string
  completionCertificate string
  accessRoad string
  individualToilet string
  toiletType string
  totalNumber int
  lightingFacility string
  tapConnection string
  totalConnections int
  solarProject string
  rainWaterHarvesting string
  sewageSystem string
  groundFloorArea int
  remainingGroundFloorArea int
  property_id string [ref: - property.property_id]
}

Table deleted_property_usage_details {
  deleted_property_usage_details_id string [pk]
  property_id string [ref: > property.property_id]
  property_usage_details_id string [ref: - property_usage_details.property_usage_details_id]
}

Table demand_report_data {
  demand_report_data_id string [pk]
  all_property_tax_sum_remaining float
  all_property_tax_sum_prev_remaining float
  all_property_tax_sum_curr_remaining float
  all_property_tax_sum_paid float
  all_property_tax_sum_prev_paid float
  all_property_tax_sum_curr_paid float
  total_amount_paid float
  total_amount_remaining float
  tax_type_1_remaining float
  tax_type_1_prev_remaining float
  tax_type_1_curr_remaining float
  tax_type_1_paid float
  tax_type_1_prev_paid float
  tax_type_1_curr_paid float
  tax_type_2_remaining float
  tax_type_2_prev_remaining float
  tax_type_2_curr_remaining float
  tax_type_2_paid float
  tax_type_2_prev_paid float
  tax_type_2_curr_paid float
  tax_type_3_remaining float
  tax_type_3_prev_remaining float
  tax_type_3_curr_remaining float
  tax_type_3_paid float
  tax_type_3_prev_paid float
  tax_type_3_curr_paid float
  tax_type_4_remaining float
  tax_type_4_prev_remaining float
  tax_type_4_curr_remaining float
  tax_type_4_paid float
  tax_type_4_prev_paid float
  tax_type_4_curr_paid float
  tax_type_5_remaining float
  tax_type_5_prev_remaining float
  tax_type_5_curr_remaining float
  tax_type_5_paid float
  tax_type_5_prev_paid float
  tax_type_5_curr_paid float
  tax_type_6_remaining float
  tax_type_6_prev_remaining float
  tax_type_6_curr_remaining float
  tax_type_6_paid float
  tax_type_6_prev_paid float
  tax_type_6_curr_paid float
  tax_type_7_remaining float
  tax_type_7_prev_remaining float
  tax_type_7_curr_remaining float
  tax_type_7_paid float
  tax_type_7_prev_paid float
  tax_type_7_curr_paid float
  tax_type_8_remaining float
  tax_type_8_prev_remaining float
  tax_type_8_curr_remaining float
  tax_type_8_paid float
  tax_type_8_prev_paid float
  tax_type_8_curr_paid float
  tax_type_9_remaining float
  tax_type_9_prev_remaining float
  tax_type_9_curr_remaining float
  tax_type_9_paid float
  tax_type_9_prev_paid float
  tax_type_9_curr_paid float
  tax_type_10_remaining float
  tax_type_10_prev_remaining float
  tax_type_10_curr_remaining float
  tax_type_10_paid float
  tax_type_10_prev_paid float
  tax_type_10_curr_paid float
  other_tax_sum_tax_remaining float
  other_tax_sum_tax_prev_remaining float
  other_tax_sum_tax_curr_remaining float
  other_tax_sum_tax_paid float
  other_tax_sum_tax_prev_paid float
  other_tax_sum_tax_curr_paid float
  status string
  financial_year string
  remaining_amount string
  property_type_discount string
  other_discount string
  property_number string
  property_id string [ref: > property.property_id]
  receipt_id string [ref: - receipt.receipt_id]
}

Table form_master {
  form_id int [pk, increment]
  formName string
  module_id int [ref: > module_master.module_id]
}

Table master_depreciation_rate {
  depreciation_rate_id string [pk]
  from_age float
  to_age float
  financial_year string
  value float
  status string
  property_type_class_id string [ref: > property_type_class.property_type_class_id]
  reassessment_range_id string [ref: > reassessment_range.reassessment_range_id]
}

Table master_ghanKachra_rate {
  ghanKachra_rate_id string [pk]
  financial_year string
  value float
  status string
  usage_sub_type_id string [ref: > usage_sub_type_master.usage_sub_type_id]
  usage_type_id string [ref: > usage_type_master.usage_type_id]
  reassessment_range_id string [ref: > reassessment_range.reassessment_range_id]
}

Table master_rr_construction_rate {
  rr_construction_rate_id string [pk]
  financial_year string
  value float
  status string
  property_type_class_id string [ref: > property_type_class.property_type_class_id]
  reassessment_range_id string [ref: > reassessment_range.reassessment_range_id]
}

Table master_rr_rate {
  rr_rate_id string [pk]
  financial_year string
  value float
  status string
  zone_id string [ref: > zone_master.zone_id]
  reassessment_range_id string [ref: > reassessment_range.reassessment_range_id]
}

Table master_tax_rate {
  rr_tax_id string [pk]
  financial_year string
  value float
  status string
  property_type_id string [ref: > property_type_master.propertyType_id]
  reassessment_range_id string [ref: > reassessment_range.reassessment_range_id]
}

Table master_weighting_rate {
  weighting_rate_id string [pk]
  financial_year string
  value float
  status string
  usage_type_id string [ref: > usage_type_master.usage_type_id]
  reassessment_range_id string [ref: > reassessment_range.reassessment_range_id]
}

Table milkatKar {
  milkatKar_id string [pk]
  financial_year string
  all_property_tax_sum float
  tax_type_1 float
  tax_type_2 float
  tax_type_3 float
  tax_type_4 float
  tax_type_5 float
  tax_type_6 float
  tax_type_7 float
  tax_type_8 float
  tax_type_9 float
  tax_type_10 float
  property_type_discount float
  other_tax_sum_tax float
  total_tax float
  status string
  property_id string [ref: > property.property_id]
  reassessment_range_id string [ref: > reassessment_range.reassessment_range_id]
}

Table milkatKarTax {
  milkatKartax_id string [pk]
  financial_year string
  sq_ft_meter float
  length float
  width float
  rr_rate float
  rr_construction_rate float
  depreciation_rate float
  weighting float
  capital_value float
  tax_value float
  tax float
  shasti_fee float
  property_type_discount float
  padsar_kar_tax float
  bill_generation_date date
  tax_data json
  property_id string [ref: > property.property_id]
  milkatKar_id string [ref: > milkatKar.milkatKar_id]
  property_usage_details_id string [ref: > property_usage_details.property_usage_details_id]
}

Table module_master {
  module_id int [pk, increment]
  moduleName string
  createdBy string
  createdBy1 string
}

Table offline_notifications {
  id string [pk]
  userId string [ref: > user_master.user_id]
  notificationId string
  title string
  message string
  type string
  status string
  progress int
  filePath string
  fileName string
  fileSize bigint
  downloadUrl string
  isDownloaded boolean
  downloadCount int
  isRead boolean
  expiresAt timestamp
  metadata json
  webhookReceivedAt timestamp
  deliveredAt timestamp
}

Table paid_and_non_paid_data {
  paid_and_non_paid_data_id string [pk]
  financial_year_range string
  name string
  property_number string
  old_property_number string
  total_tax decimal
  paid decimal
  remaining decimal
  is_paid boolean
  property_id string [ref: > property.property_id]
  financial_year_id string [ref: > financial_year.financial_year_id]
}

Table paid_data {
  paid_data_id string [pk]
  property_number string
  all_property_tax_sum float
  all_property_tax_sum_prev float
  all_property_tax_sum_curr float
  total_amount float
  tax_type_1 float
  tax_type_1_prev float
  tax_type_1_curr float
  tax_type_2 float
  tax_type_2_prev float
  tax_type_2_curr float
  tax_type_3 float
  tax_type_3_prev float
  tax_type_3_curr float
  tax_type_4 float
  tax_type_4_prev float
  tax_type_4_curr float
  tax_type_5 float
  tax_type_5_prev float
  tax_type_5_curr float
  tax_type_6 float
  tax_type_6_prev float
  tax_type_6_curr float
  tax_type_7 float
  tax_type_7_prev float
  tax_type_7_curr float
  tax_type_8 float
  tax_type_8_prev float
  tax_type_8_curr float
  tax_type_9 float
  tax_type_9_prev float
  tax_type_9_curr float
  tax_type_10 float
  tax_type_10_prev float
  tax_type_10_curr float
  other_tax_sum_tax float
  other_tax_sum_tax_prev float
  other_tax_sum_tax_curr float
  other_discount string
  status string
  financial_year string
  remaining_amount string
  year_wise_penalty_data json
  property_id string [ref: > property.property_id]
  payment_id string [ref: - payment_info.payment_id]
}

Table payment_info {
  payment_id string [pk]
  amount decimal
  tax_payer_name string
  financial_year_range string
  payment_mode string
  transaction_id string
  payment_status string [note: 'enum: pending, completed, failed']
  payment_date date
  property_id string [ref: > property.property_id]
  receipt_id string [ref: - receipt.receipt_id]
}

Table penalty_fee_yearWise {
  penalty_fee_id string [pk]
  financial_year string
  actual_value decimal
  penalty_value decimal
  tax_percentage int
  property_id string [ref: > property.property_id]
}

Table previous_property_owners {
  previous_owner_id string [pk]
  name string
  mobile_number string
  email_id string
  aadhar_number string
  pan_card string
  record_created_time timestamp
  property_id string [ref: > property.property_id]
  owner_type_id string [ref: > owner_type_master.owner_type_id]
  reassessment_range_id string [ref: > reassessment_range.reassessment_range_id]
}

Table property_owner_details {
  property_owner_details_id string [pk]
  name string
  mobile_number string
  email_id string
  aadhar_number string
  pan_card string
  gender string [note: 'enum: Male, Female, Other']
  marital_status string [note: 'enum: Single, Married, Divorced, Widowed']
  partner_name string
  remark string
  last_action_done string
  is_owner boolean
  is_payer boolean
  property_id string [ref: > property.property_id]
  owner_type_id string [ref: > owner_type_master.owner_type_id]
}

Table property_sub_type_master {
  property_sub_type_id string [pk]
  propertySubType_name string
  propertyType_id string [ref: > property_type_master.propertyType_id]
}

Table property_type_master {
  propertyType_id string [pk]
  propertyType string
  property_type_class_id string [ref: > property_type_class.property_type_class_id]
}

Table property_usage_details {
  property_usage_details_id string [pk]
  property_type_desc string
  construction_area float
  length decimal
  width decimal
  are_sq_ft decimal
  are_sq_meter decimal
  construction_start_date timestamp
  construction_end_date timestamp
  Building_age float
  annual_rent float
  floor string
  flat_no string
  authorized boolean
  construction_start_year string
  remark string
  tapshil string
  orderIndex int
  property_id string [ref: > property.property_id]
  propertyType_id string [ref: > property_type_master.propertyType_id]
  floor_id string [ref: > floor_master.floor_id]
  property_sub_type_id string [ref: > property_sub_type_master.property_sub_type_id]
  usage_type_id string [ref: > usage_type_master.usage_type_id]
  usage_sub_type_id string [ref: > usage_sub_type_master.usage_sub_type_id]
}

Table property {
  property_id string [pk]
  newPropertyNumber string
  propertyNumber string
  old_propertyNumber string
  parent_propertyNumber string
  city_survey_number string
  address string
  house_or_apartment_name string
  latitude string
  longitude string
  mobile_number string
  email_id string
  uploaded_files json
  property_desc string
  plot_area float
  Plot_construction_area float
  Plot_empty_area float
  construction_area float
  carpet_area float
  exempted_area float
  assessable_area float
  land_cost float
  standard_rate float
  annual_rent float
  capital_value float
  property_remark string
  landmark string
  snp_ward string
  zone_code string
  gat_no string
  gis_number string
  note string
  ferfarRemark string
  fodRemark string
  parent_propertyId string
  survey_person_code string
  survey_date string
  property_photographs text
  sr_no int
  import_property_id string
  updateStatus string
  city string
  plot_number string
  block_number string
  house_number string
  street_id string [ref: > street_master.street_id]
  ward_id string [ref: > ward_master.ward_id]
  register_number_id string [ref: > register_number.register_number_id]
  zone_id string [ref: > zone_master.zone_id]
}

Table property_ferfar_details {
  property_ferfar_detail_id string [pk]
  previous_owner_names text
  reason text
  photo_image_paths text
  document_image_path text
  user_email_id text
  year string
  property_id string [ref: > property.property_id]
  reassessment_range_id string [ref: > reassessment_range.reassessment_range_id]
}

Table property_fod_details {
  property_fod_details_id string [pk]
  new_property_numbers text
  reason text
  photo_image_paths text
  document_image_path text
  user_email_id text
  year string
  property_id string [ref: > property.property_id]
  reassessment_range_id string [ref: > reassessment_range.reassessment_range_id]
}

Table receipt {
  receipt_id string [pk]
  book_number string
  book_receipt_number string
  financial_year string
  receipt_date timestamp
  remark string
  additional_notes string
  property_id string [ref: > property.property_id]
  book_number_master_id string [ref: > book_number_master.id]
}

Table rolewise_form_permission {
  action_id int [pk, increment]
  can_read boolean
  can_write boolean
  can_update boolean
  can_delete boolean
  is_valid boolean
  role_id string [ref: > role_master.role_id]
  form_id int [ref: > form_master.form_id]
}

Table tax_property {
  tax_property_id string [pk]
  bill_no string
  financial_year string
  all_property_tax_sum float
  other_tax_sum_tax float
  total_tax float
  bill_generation_date date
  property_id string [ref: > property.property_id]
}

Table tax_property_other_taxes {
  tax_property_other_taxes_id string [pk]
  tax_type string
  amount float
  property_id string [ref: > property.property_id]
  tax_property_id string [ref: > tax_property.tax_property_id]
  tax_type_master_id string [ref: > tax_type_master.tax_type_master_id]
}

Table tax_propertywise {
  tax_propertywise_id string [pk]
  financial_year string
  bill_no string
  sq_ft_meter float
  rr_rate float
  rr_construction_rate float
  depreciation_rate float
  weighting float
  capital_value float
  tax_value float
  tax float
  bill_generation_date date
  property_id string [ref: > property.property_id]
  tax_property_id string [ref: > tax_property.tax_property_id]
  property_usage_details_id string [ref: > property_usage_details.property_usage_details_id]
}

Table user_otp {
  otpId string [pk]
  otp string
  expiresAt timestamp
  status int
  mobile_number string
  verificationId string
  additionalInfo json
  user_id string [ref: > user_master.user_id]
}

Table user_master {
  user_id string [pk]
  firstname string
  lastname string
  email string
  password string
  isActive boolean
  address string
  mobileNumber string
  profilePic string
  refreshToken text
  resetToken text
  lastLoginAt timestamp
  role_id string [ref: > role_master.role_id]
}

Table warshik_kar {
  warshik_karId string [pk]
  financial_year string
  all_property_tax_sum_total float
  all_property_tax_sum float
  all_property_tax_sum_current float
  tax_type_1 float
  tax_type_1_current float
  tax_type_1_previous float
  tax_type_2 float
  tax_type_2_current float
  tax_type_2_previous float
  tax_type_3 float
  tax_type_3_current float
  tax_type_3_previous float
  tax_type_4 float
  tax_type_4_current float
  tax_type_4_previous float
  tax_type_5 float
  tax_type_5_current float
  tax_type_5_previous float
  tax_type_6 float
  tax_type_6_current float
  tax_type_6_previous float
  tax_type_7 float
  tax_type_7_current float
  tax_type_7_previous float
  tax_type_8 float
  tax_type_8_current float
  tax_type_8_previous float
  tax_type_9 float
  tax_type_9_current float
  tax_type_9_previous float
  tax_type_10 float
  tax_type_10_current float
  tax_type_10_previous float
  other_tax_sum_tax float
  other_tax_sum_tax_current float
  other_tax_sum_tax_previous float
  total_tax float
  total_tax_current float
  total_tax_previous float
  status string
  property_type_discount string
  billNo string
  bill_generation_date date
  property_id string [ref: > property.property_id]
}

Table warshikKarTax {
  warshik_karTaxId string [pk]
  financial_year string
  sq_ft_meter float
  rr_rate float
  rr_construction_rate float
  depreciation_rate float
  weighting float
  capital_value float
  tax_value float
  tax float
  tax_data json
  bill_generation_date date
  property_id string [ref: > property.property_id]
  warshik_karId string [ref: > warshik_kar.warshik_karId]
  property_usage_details_id string [ref: > property_usage_details.property_usage_details_id]
}

// Master Tables (Referenced but not defined in your ERD)
Table ward_master {
  ward_id string [pk]
  ward_name string
  zone_id string [ref: > zone_master.zone_id]
}

Table zone_master {
  zone_id string [pk]
  zone_name string
}

Table street_master {
  street_id string [pk]
  street_name string
}

Table register_number {
  register_number_id string [pk]
  register_number string
}

Table role_master {
  role_id string [pk]
  role_name string
}

Table owner_type_master {
  owner_type_id string [pk]
  owner_type_name string
}

Table property_type_class {
  property_type_class_id string [pk]
  class_name string
}

Table reassessment_range {
  reassessment_range_id string [pk]
  range_name string
}

Table usage_type_master {
  usage_type_id string [pk]
  usage_type_name string
}

Table usage_sub_type_master {
  usage_sub_type_id string [pk]
  usage_sub_type_name string
  usage_type_id string [ref: > usage_type_master.usage_type_id]
}

Table floor_master {
  floor_id string [pk]
  floor_name string
}

Table tax_type_master {
  tax_type_master_id string [pk]
  tax_type_name string
}

Table financial_year {
  financial_year_id string [pk]
  year_name string
}

Table tax_pendingDues {
  tax_pendingDues_id string [pk]
  financial_year_id string [ref: > financial_year.financial_year_id]
}