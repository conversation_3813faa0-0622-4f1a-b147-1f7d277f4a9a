import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportPropertyupdatecoladdded21724829253259 implements MigrationInterface {
    name = 'ImportPropertyupdatecoladdded21724829253259'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" RENAME COLUMN "sr_no_system" TO "sr_no_excel"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "import_property" RENAME COLUMN "sr_no_excel" TO "sr_no_system"`);
    }

}
