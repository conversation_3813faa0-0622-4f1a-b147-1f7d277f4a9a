import { Repository } from 'typeorm';
import { BookNumberMasterEntity, ReceiptEntity } from '../entities'; // Assuming the entity is in the `entities` folder
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { BookNumberMasterRepository } from './book_numberMaster.repository';

export class ReceiptRepository extends Repository<ReceiptEntity> {
  constructor(
    @InjectRepository(ReceiptEntity)
    private readonly receiptRepository: Repository<ReceiptEntity>,
    private readonly bookNumberRepository: BookNumberMasterRepository,
  ) {
    super(
      receiptRepository.target,
      receiptRepository.manager,
      receiptRepository.queryRunner,
    );
  }

  // Save a new receipt
  async saveReceipt(input: {
    book_number: string;
    financial_year: string;
    receipt_date: Date;
    additional_notes?: string;
    property_id: string; // Assuming property_id is needed to associate with PropertyEntity
    payment_id: string; // Assuming payment_id is needed to associate with PaymentInfoEntity
  }): Promise<ReceiptEntity> {
    const receipt = this.receiptRepository.create(input);
    return await this.receiptRepository.save(receipt);
  }

  // Find all receipts
  async findAllReceipts() {
    return await this.receiptRepository
      .createQueryBuilder('receipt')
      .orderBy('receipt.receipt_date', 'DESC')
      .getMany();
  }

  // Find receipt by ID with related entities
async findReceiptById(id: string) {
  return await this.receiptRepository
    .createQueryBuilder('receipt')
    .leftJoinAndSelect('receipt.paymentInfo', 'paymentInfo')
    .leftJoinAndSelect('receipt.bookNumber', 'bookNumber')
    .leftJoinAndSelect('receipt.property', 'property')
        .leftJoinAndSelect('paymentInfo.paidData', 'paidData')

    .where('receipt.receipt_id = :id', { id })
    .getOne();
}


  // Find receipts by property ID
  async findReceiptsByPropertyId(property_id: string) {
    return await this.receiptRepository
      .createQueryBuilder('receipt')
      .where('receipt.property_id = :property_id', { property_id })
      .getMany();
  }

  // Update receipt by ID
  async updateReceipt(
    id: string,
    input: {
      book_number?: string;
      book_receipt_number?: string;
      financial_year?: string;
      receipt_date?: Date;
      additional_notes?: string;
    },
  ) {
    try {
      // Fetch the existing receipt
      const receipt = await this.receiptRepository.findOne({
        where: { receipt_id: id },
        relations: ['bookNumber'],
      });
  
      if (!receipt) {
        throw new Error('Receipt not found');
      }
  
      // Update general receipt fields
      if (input.financial_year !== undefined)
        receipt.financial_year = input.financial_year;
      if (input.receipt_date !== undefined)
        receipt.receipt_date = input.receipt_date;
      if (input.additional_notes !== undefined)
        receipt.additional_notes = input.additional_notes;
  
              
      // Condition: If both book_number and book_receipt_number are empty in receipt
      if (
        (!receipt.book_number || receipt.book_number === '') &&
        (!receipt.book_receipt_number || receipt.book_receipt_number === '') &&
        !receipt.bookNumber
      ) {
          
        if (!input.book_number || !input.book_receipt_number) {
          throw new Error('Both book_number and book_receipt_number are required for initial update');
        }
  
        // Fetch new book entity
        const newBookEntity = await this.bookNumberRepository.findOneBook(Number(input.book_number));
  
        if (!newBookEntity) {
          throw new Error('New book number not found');
        }
  
        // Update the book entity by adding the new receipt number
        newBookEntity.receiptsInUse = [...new Set([...newBookEntity.receiptsInUse, Number(input.book_receipt_number)])];
        newBookEntity.availableReceipts = newBookEntity.availableReceipts.filter(r => r !== Number(input.book_receipt_number));
  
        await this.bookNumberRepository.saveBookEntity(newBookEntity);
  
        // Update the receipt's bookNumber relation
        receipt.bookNumber = newBookEntity;
        receipt.book_number = input.book_number;
        receipt.book_receipt_number = input.book_receipt_number;
      } 
      // Condition: If book_number has changed
      else if (
        input.book_number &&
        (!receipt.book_number || Number(input.book_number) !== Number(receipt.book_number))
      ) {
          
        // Fetch the new book entity
        const newBookEntity = await this.bookNumberRepository.findOneBook(Number(input.book_number));
  
        if (!newBookEntity) {
          throw new Error('New book number not found');
        }
  
        // Remove the old book receipt from the previous book entity
        if (receipt.bookNumber) {
          const oldBookEntity = receipt.bookNumber;
          oldBookEntity.receiptsInUse = oldBookEntity.receiptsInUse.filter(r => r !== Number(receipt.book_receipt_number));
          oldBookEntity.availableReceipts = [...new Set([...oldBookEntity.availableReceipts, Number(receipt.book_receipt_number)])];
  
          await this.bookNumberRepository.saveBookEntity(oldBookEntity);
        }
  
        // Update the new book entity
        newBookEntity.receiptsInUse = [...new Set([...newBookEntity.receiptsInUse, Number(input.book_receipt_number)])];
        newBookEntity.availableReceipts = newBookEntity.availableReceipts.filter(r => r !== Number(input.book_receipt_number));
  
        await this.bookNumberRepository.saveBookEntity(newBookEntity);
  
        // Update the receipt's bookNumber relation
        receipt.bookNumber = newBookEntity;
        receipt.book_number = input.book_number;
      } 
      // Condition: If only the receipt number has changed
      else if (
        input.book_receipt_number &&
        (!receipt.book_receipt_number || input.book_receipt_number !== receipt.book_receipt_number)
      ) {
          
        if (!receipt.bookNumber) {
          throw new Error('Book entity not found for the receipt');
        }
  
        // Remove the old receipt number from the book entity
        const bookEntity = receipt.bookNumber;
        bookEntity.receiptsInUse = bookEntity.receiptsInUse.filter(r => r !== Number(receipt.book_receipt_number));
        bookEntity.availableReceipts = [...new Set([...bookEntity.availableReceipts, Number(receipt.book_receipt_number)])];
  
        // Add the new receipt number
        bookEntity.receiptsInUse = [...new Set([...bookEntity.receiptsInUse, Number(input.book_receipt_number)])];
        bookEntity.availableReceipts = bookEntity.availableReceipts.filter(r => r !== Number(input.book_receipt_number));
  
        await this.bookNumberRepository.saveBookEntity(bookEntity);
  
        // Update the receipt's book_receipt_number
        receipt.book_receipt_number = input.book_receipt_number;
      }
  
      // Save the updated receipt
      await this.receiptRepository.save(receipt);
  
      return { message: 'Receipt successfully updated!',data:receipt };
    } catch (error) {
      return { message: 'Receipt update failed!', error: error.message };
    }
  }
  

  // Delete (soft delete) a receipt by ID
  async deleteReceipt(id: string) {
    return await this.receiptRepository
      .createQueryBuilder('receipt')
      .softDelete()
      .where('receipt_id = :id', { id })
      .execute();
  }
}
