import {
  BaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
} from 'typeorm';

@Entity('financial_year')
@Unique(['financial_year_range']) 
export class Financial_year extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  financial_year_id: string;

  @Column({ name: 'financial_year_range', type: String, nullable: false })
  financial_year_range: string;


  @Column({ name: 'from_date', type: String, nullable: false })
  from_date: Date;

  @Column({ name: 'to_date', type: String, nullable: false })
  to_date: Date;

  @Column({ name: 'is_active', type: String, nullable: false })
  is_active: string;

  @Column({ name: 'is_current', type: Boolean, default : false })
  is_current: boolean;

  @Column({ name: 'is_published', type: String, nullable: false, default: 'true' })
  is_published: string;
  /*
   * Create and Update Date Columns
   */

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
