import {
  <PERSON><PERSON>ntity,
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PropertyTypeMasterEntity } from './property-type-master.entity';

@Entity('property_sub_type_master')
export class PropertySubTypeMasterEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  property_sub_type_id: string;

  @Column({ name: 'propertySub_name', type: String, nullable: false })
  propertySubType_name: string;

  @ManyToOne(
    () => PropertyTypeMasterEntity,
    (propertyType) => propertyType.propertyType_id,
  )
  @JoinColumn({ name: 'propertyType_id' })
  propertyType: PropertyTypeMasterEntity;

  /*
   * Create and Update Date Columns
   */
  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
