import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class ColAddedNewPropertyNumberInProperty1728471037127 implements MigrationInterface {
    name = 'ColAddedNewPropertyNumberInProperty1728471037127'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the "newPropertyNumber" column already exists
        const table = await queryRunner.getTable("property");
        const newPropertyNumberColumn = table?.findColumnByName("newPropertyNumber");

        if (!newPropertyNumberColumn) {
            await queryRunner.query(`ALTER TABLE "property" ADD "newPropertyNumber" character varying`);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the "newPropertyNumber" column
        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "newPropertyNumber"`);
    }
}
