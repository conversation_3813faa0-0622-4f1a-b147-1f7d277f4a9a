import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useToast } from "@/components/ui/use-toast";
import WhiteContainer from "../WhiteContainer";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Download, Eye, Loader, Trash, Trash2 } from "lucide-react";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { usePropertyRegistrationController } from "@/controller/property-registration/PropertyRegistrationController";
import { formatAdharNo } from "@/controller/hepler/formatAdhar";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import PropertyApi from "@/services/PropertyServices";
import Api from "@/services/ApiServices";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import PropertyTable from "./PropertyTableFod";
import AddOwnerForm from "./AddOwnerForm"; // Import the AddOwnerForm component
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

interface Property {
  propertyNumber: string;
  ownerName: string;
  address: string;
  area: number;
  usageType: string;
  length?: number;
  breadth?: number;
}

interface PropertyDivision {
  originalNumber: string;
  newNumbers: string[];
}

interface PropertyDimension {
  propertyNumber: string;
  ownerName: string;
  length: number;
  breadth: number;
  area: number;
}

const DivisionDialog: React.FC<any> = ({
  isOpen,
  onClose,
  onDivide,
  isLoading,
}) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [divisions, setDivisions] = useState<string>("");

  const handleSubmit = () => {
    const divisionNumber = Number(divisions);
    if (
      !divisions.trim() ||
      isNaN(divisionNumber) ||
      divisionNumber < 2
    ) {
      toast({
        title: t("Please enter a valid number of divisions"),
        description: `Number must be greater than 1`,
        variant: "destructive",
      });
      return;
    }
    onDivide(divisionNumber);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px] bg-white">
        <DialogHeader>
          <DialogTitle>{t("मालमत्ता विभाजन")}</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="divisions">{t("विभागांची संख्या")}</Label>
              <Input
                id="divisions"
                type="number"
                value={divisions}
                onChange={(e) => setDivisions(e.target.value)}
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            {t("cancel")}
          </Button>
          <Button variant="submit" onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? "..." : t("submit")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

let allIsValid = false;

const PropertySplit = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { usePropertyDetails } = usePropertyRegistrationController();
  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Property, FormName.PropertyDivision, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Property, FormName.PropertyDivision, Action.CanUpdate);
  const canCreate = canPerformAction(ModuleName.Property, FormName.PropertyDivision, Action.CanCreate);
  const canDelete = canPerformAction(ModuleName.Property, FormName.PropertyDivision, Action.CanDelete);

  const [propertyNumber, setPropertyNumber] = useState("");
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const [oldPropertyNumber, setOldPropertyNumber] = useState("");
  const [propertyId, setPropertyId] = useState("");
  const [searchParam, setSearchParam] = useState<
    "propertyNumber" | "old_propertyNumber"
  >("propertyNumber");
  const [property, setProperty] = useState<any | null>(null);
  const [isDivisionDialogOpen, setIsDivisionDialogOpen] = useState(false);
  const [isDividing, setIsDividing] = useState(false);
  const [propertyDivision, setPropertyDivision] =
    useState<PropertyDivision | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [isAssigning, setIsAssigning] = useState(false);
  const [isValidNumbers, setIsValidNumbers] = useState<boolean | null>(null);
  const [propertyDimensions, setPropertyDimensions] = useState<
    PropertyDimension[]
  >([]);
  const [showDimensionsForm, setShowDimensionsForm] = useState(false);
  const [selectedNumber, setSelectedNumber] = useState<string | null>(null);

  const [dividedNumber, setDividedNumber] = useState<any[] | null>(null);
  const [validNumber, setValidNumber] = useState<string[] | null>(null);
  const [error, setError] = useState(false);
  const [actualPropertyId, setActualPropertyId] = useState<string | null>(null);
  const [searchOnParameter, setSearchOnParameter] = useState("");
  const [deletedOwnerIds, setDeletedOwnerIds] = useState<string[]>([]);

  console.log("allIsValid 1", allIsValid);

  const handleSearch = async () => {
    const selectedPropertyNumber = oldPropertyNumber || propertyNumber;
    if (!selectedPropertyNumber) {
      toast({
        title: t("Enter Property Number"),
        variant: "destructive",
      });
      return;
    }

    setPropertyId(selectedPropertyNumber);
    const searchParam = oldPropertyNumber
      ? "old_propertyNumber"
      : "propertyNumber";
    setSearchParam(searchParam);

    // Directly call the API after setting the search parameter
    const propertydata = await PropertyApi.getSingleProperty(
      selectedPropertyNumber,
      (response) => {
        if (response.status) {
          console.log("repsonesss", response.data);
          setProperty(response.data);
          setActualPropertyId(response.data.data.property_id);
        } else {
          toast({
            title: t("Error"),
            description: response.data.message,
            variant: "destructive",
          });
        }
      },
      searchParam
    );
  };

  const openPopup = () => {
    setIsDivisionDialogOpen(true);
  };

  const closePopup = () => {
    setIsDivisionDialogOpen(false);
  };

  const handleNumberSubmit = async (number) => {
    setSelectedNumber(number);
    console.log("property", property);
    try {
      const response = await Api.divideAndGenerateNumber(
        number,
        property?.data?.propertyNumber
      );
      if (!response.status) {
        throw new Error("Network response was not ok");
      }
      console.log("response number data", response.data.data);
      setDividedNumber(response.data.data);
      closePopup();
      toast({
        title: "Success",
        description: ` यशस्वीरित्या मालमत्ता क्रमांक तयार केले...`,
        variant: "success"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to fetch property details: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const handleValidateNumbers = async (numbers: any[]) => {
    setIsValidating(true);
    const propertyNumbers = numbers.map((number) => number.propertyNumber);

    try {
      const response = await Api.checkNumberAreUnique(propertyNumbers);
      setDividedNumber(response.data.data);
      console.log("response.data.data", response.data.data);
      console.log("isValid--", "");

      allIsValid = response.data.data.every(
        (number) => number.isUnique === true
      );
      if (!allIsValid) {
        setError(true);
      } else {
        setError(false);
      }
    } catch (error) {}
    // Mock validation
    const isValid = true;
    setIsValidNumbers(isValid);

    setIsValidating(false);
  };

  const handleAssignNumbers = (numbers: string[]) => {
    if (!property) return;

    setIsAssigning(true);
    // Mock assignment success
    const success = true;

    if (success) {
      toast({
        title: t("New property numbers assigned successfully"),
      });
      setProperty(null);
      setPropertyDivision(null);
      setIsValidNumbers(null);
      setPropertyDimensions([]);
      setShowDimensionsForm(false);
    }
    setIsAssigning(false);
  };

  const handleDimensionsSubmit = (dimensions: PropertyDimension[]) => {
    setPropertyDimensions(dimensions);
    if (propertyDivision) {
      handleAssignNumbers(propertyDivision.newNumbers);
    }
  };
  const handleInputChange = (index, value) => {
    const newDividedNumber = [...dividedNumber];
    newDividedNumber[index].propertyNumber = value.trim();
    setDividedNumber(newDividedNumber);
  };
  const ownerColumn = [
    {
      accessorKey: "index",
      header: `${t("ownerDetails.SrNo")}`,
      cell: ({ row }: { row: any }) => <div>{row.index + 1}</div>,
    },
    {
      accessorKey: "name",
      header: `${t("ownerDetails.name")}`,
      cell: ({ row }: { row: any }) => <div>{row.original?.name}</div>,
    },
    {
      accessorKey: "owner_type",
      header: `${t("ownerDetails.type")}`,
      cell: ({ row }: { row: any }) => (
        <div>{row.original.owner_type?.owner_type}</div>
      ),
    },
    {
      accessorKey: "mobile_number",
      header: `${t("ownerDetails.mobileNumber")}`,
      cell: ({ row }: { row: any }) => <div>{row.original?.mobile_number}</div>,
    },
    {
      accessorKey: "email_id",
      header: `${t("ownerDetails.emailId")}`,
      cell: ({ row }: { row: any }) => <div>{row.original?.email_id}</div>,
    },
    {
      accessorKey: "aadhar_number",
      header: `${t("ownerDetails.adharNumber")}`,
      cell: ({ row }: { row: any }) => (
        <div>{formatAdharNo(row.original?.aadhar_number)}</div>
      ),
    },
    {
      accessorKey: "actions",
      header: `${t("action")}`,
      cell: ({ row }: { row: any }) => (
        <>
        {canDelete && <button
        // variant="submit"
        className="h-8 w-8 p-0 ml-2"
        onClick={() => handleDeleteOwner(row.original.property_owner_details_id)}
        >
        <Trash className="text-red-500" />
      </button>}
      </>
      
      ),
    },
  ];

  const handleDeleteOwner = (ownerId) => {
    setDeletedOwnerIds((prev) => [...prev, ownerId]);
    setProperty((prev) => ({
      ...prev,
      data: {
        ...prev.data,
        property_owner_details: prev.data.property_owner_details.filter(
          (owner) => owner.property_owner_details_id !== ownerId
        ),
      },
    }));
  };

  const usageColumns = [
    {
      accessorKey: "index",
      header: `${t("propertyDetail.SrNo")}`,
      cell: ({ row }: { row: any }) => <div>{row.index + 1}</div>,
    },
    {
      accessorKey: "propertyType",
      header: `${t("propertyDetail.propertyType")}`,
      cell: ({ row }: { row: any }) => (
        <div>{row.original?.propertyType?.propertyType}</div>
      ),
    },
    {
      accessorKey: "construction_start_year",
      header: `${t("propertyDetail.constructionDate")}`,
      cell: ({ row }: { row: any }) => (
        <div>{row.original?.construction_start_year}</div>
      ),
    },
    {
      accessorKey: "usageType",
      header: `${t("propertyDetail.usageType")}`,
      cell: ({ row }: { row: any }) => (
        <div>{row.original.usageType?.usage_type}</div>
      ),
    },
    
        {
      accessorKey: "floorType",
      header: `${t("मजला")}`,
     cell: ({ row }: { row: any })  => <div>{row.original.floorType?.floor_name || ""}</div>,
    },
    {
      accessorKey: "usageSubType",
      header: `${t("propertyDetail.usageSubType")}`,
      cell: ({ row }: { row: any }) => (
        <div>{row.original.usageSubType?.usage_sub_type}</div>
      ),
    },
    {
      accessorKey: "length",
      header: `${t("propertyDetail.length")}`,
      cell: ({ row }: { row: any }) => <div>{row.original?.length}</div>,
    },
    {
      accessorKey: "width",
      header: `${t("propertyDetail.width")}`,
      cell: ({ row }: { row: any }) => <div>{row.original?.width}</div>,
    },
    {
      accessorKey: "are_sq_ft",
      header: `${t("propertyDetail.areaSqFt")}`,
      cell: ({ row }: { row: any }) => <div>{row.original?.are_sq_ft}</div>,
    },
    {
      accessorKey: "are_sq_meter",
      header: `${t("propertyDetail.areaSqMt")}`,
      cell: ({ row }: { row: any }) => <div>{row.original?.are_sq_meter}</div>,
    },
    {
      accessorKey: "authorized",
      header: `${t("propertyDetail.propertyStatus")}`,
      cell: ({ row }: { row: any }) => (
        <div>{row.original.authorized ? "अधिकृत" : "अनधिकृत"}</div>
      ),
    },
  ];

  const singlePropertyDetails = property?.data;
  const ownerDetails = singlePropertyDetails?.property_owner_details;
  const propertyUsageDetails = singlePropertyDetails?.property_usage_details;

  const handleGenrationProcess = async (numbers: any[]) => {
    let propertyNumbers = numbers.map((number) => number.propertyNumber);
    setValidNumber(propertyNumbers);

    console.log(
      "Numbers",
      numbers,
      "ownerDetails",
      ownerDetails,
      "propertyUsageDetails",
      propertyUsageDetails
    );
    setShowDimensionsForm(true);
  };
  const handlePropertyNumberChange = (e) => {
    setPropertyNumber(e.target.value.trim().toUpperCase());
    setOldPropertyNumber("");
  };

  const handleOldPropertyNumberChange = (e) => {
    setOldPropertyNumber(e.target.value.trim().toUpperCase());
    setPropertyNumber("");
  };

  if (!canRead) {
    return <div>You do not have permission to view this page.</div>;
  }

  return (
    <div className="flex h-fit">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 className="text-2xl font-semibold">{t("मालमता फोड")}</h1>

        <WhiteContainer>
          <div className="space-y-6">
            <div className="grid md:grid-cols-4 gap-x-3 gap-y-3">
              <div className="grid-cols-subgrid">
                <Label>{t("property.propertyNumberColumn")}</Label>
                <Input
                  className="mt-1 block w-full"
                  transliterate={false}
                  placeholder={t("property.propertyNumberColumn")}
                  value={propertyNumber}
                  onChange={handlePropertyNumberChange}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                />
              </div>
              <div className="grid-cols-subgrid">
                <Label>{t("property.oldPropertyNumber")}</Label>
                <Input
                  className="mt-1 block w-full"
                  transliterate={false}
                  placeholder={t("property.oldPropertyNumber")}
                  value={oldPropertyNumber}
                  onChange={handleOldPropertyNumberChange}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                />
              </div>

              <div className="grid-cols-subgrid mt-1 flex items-end">
                <Button variant="submit" onClick={handleSearch}>
                  {t("search")}
                </Button>
              </div>
            </div>
          </div>
        </WhiteContainer>

        {loading && (
          <div className="flex justify-center my-8">
            <Loader />
          </div>
        )}

        {property && (
          <WhiteContainer className="overflow-x-auto">
            <Tabs defaultValue="owner" className="w-full ">
              <TabsList className="w-[100%] md:w-fit !h-12 ">
                <TabsTrigger
                  className=" p-[10px]  rounded text-[15px] "
                  value="owner"
                >
                  {t("propertyView.ownerDetails")}{" "}
                </TabsTrigger>
                <TabsTrigger
                  className=" p-[10px]  rounded text-[15px] "
                  value="property"
                >
                  {t("propertyView.propertyDetails")}{" "}
                </TabsTrigger>
              </TabsList>
              <TabsContent value="owner">
                <TanStackTable
                  columns={ownerColumn}
                  data={ownerDetails || []}
                />
              </TabsContent>
              <TabsContent value="property">
                <TanStackTable
                  columns={usageColumns}
                  data={propertyUsageDetails || []}
                />
              </TabsContent>
            </Tabs>

            {canCreate && <div className="flex items-end  justify-end">
              <Button
                variant="submit"
                onClick={openPopup}
              >
                {t("विभागणी करा")}
              </Button>
            </div>}
          </WhiteContainer>
        )}

        {dividedNumber && (
          <WhiteContainer>
            <div>
              <h2 className="mb-4">नवीन मालमत्ता क्रमांक</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-3 gap-4">
                {dividedNumber.map((number, index) => {
                  return (
                    <div key={index} className="grid-cols-subgrid">
                      <Input
                        key={index}
                        type="text"
                        value={number.propertyNumber}
                        onChange={(e) =>
                          handleInputChange(index, e.target.value)
                        }
                        className={`${
                          number.isUnique ? " border" : "border-red-700 border-2"
                        }`}
                      />
                    </div>
                  );
                })}
              </div>

              <div className="flex items-end  justify-between text-red-400">
                <div className="error-message">
                  {`${error ? "Duplicate property numbers found." : ""}`}
                </div>

                <div className="flex justify-between mt-5">
                  {canCreate && <Button
                    className="mr-5"
                    onClick={() => handleValidateNumbers(dividedNumber)}
                  >
                    मालमत्ता क्रमांक तपासा
                  </Button>}

                  {canCreate && <Button
                    disabled={!allIsValid}
                    onClick={() => handleGenrationProcess(dividedNumber)}
                  >
                    मालमत्ता क्रमांक तयार करा
                  </Button>}
                </div>
              </div>
            </div>
          </WhiteContainer>
        )}

        {showDimensionsForm && (
          <div>
            <PropertyTable
              actualPropertyId={actualPropertyId}
              validNumbers={validNumber}
              ownerDetails={ownerDetails}
              propertyUsageDetails={propertyUsageDetails}
              deletedOwnerIds={deletedOwnerIds} // Pass deletedOwnerIds as a prop
            />
          </div>
        )}

        <DivisionDialog
          isOpen={isDivisionDialogOpen}
          onClose={() => setIsDivisionDialogOpen(false)}
          onDivide={handleNumberSubmit}
          isLoading={isDividing}
        />
      </div>
    </div>
  );
};

export default PropertySplit;
