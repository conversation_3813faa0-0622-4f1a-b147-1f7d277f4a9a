import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableCommonTableProperty1728025663372 implements MigrationInterface {
    name = 'CreateTableCommonTableProperty1728025663372'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create ENUM types for various fields
        await queryRunner.query(`CREATE TYPE "public"."common_fields_of_property_individualtoilet_enum" AS ENUM('yes', 'no')`);
        await queryRunner.query(`CREATE TYPE "public"."common_fields_of_property_solarproject_enum" AS ENUM('yes', 'no')`);
        await queryRunner.query(`CREATE TYPE "public"."common_fields_of_property_rainwaterharvesting_enum" AS ENUM('yes', 'no')`);
        await queryRunner.query(`CREATE TYPE "public"."common_fields_of_property_sewagesystem_enum" AS ENUM('yes', 'no')`);

        // Create the common_fields_of_property table
        await queryRunner.query(`
            CREATE TABLE "common_fields_of_property" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "GISID" character varying,
                "propertyDescription" character varying,
                "completionCertificate" character varying,
                "accessRoad" character varying,
                "individualToilet" "public"."common_fields_of_property_individualtoilet_enum",
                "toiletType" character varying,
                "totalNumber" integer,
                "lightingFacility" character varying,
                "tapConnection" character varying,
                "totalConnections" integer,
                "solarProject" "public"."common_fields_of_property_solarproject_enum",
                "rainWaterHarvesting" "public"."common_fields_of_property_rainwaterharvesting_enum",
                "sewageSystem" "public"."common_fields_of_property_sewagesystem_enum",
                "groundFloorArea" integer,
                "remainingGroundFloorArea" integer,
                CONSTRAINT "PK_558b4bc836dfe4941534fe675f1" PRIMARY KEY ("id")
            )
        `);

        // Modify property_owner_details table to add new fields
        await queryRunner.query(`ALTER TABLE "property_owner_details" ADD "pan_card" character varying`);
        
        // Create ENUM types for gender and marital status
        await queryRunner.query(`CREATE TYPE "public"."property_owner_details_gender_enum" AS ENUM('MALE', 'FEMALE', 'OTHER')`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" ADD "gender" "public"."property_owner_details_gender_enum"`);

        await queryRunner.query(`CREATE TYPE "public"."property_owner_details_marital_status_enum" AS ENUM('yes', 'no', 'divorced')`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" ADD "marital_status" "public"."property_owner_details_marital_status_enum"`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" ADD "partner_name" character varying`);

        // Modify property table to add foreign key reference to common_fields_of_property
        await queryRunner.query(`ALTER TABLE "property" ADD "common_fields_id" uuid`);
        
        // Modify import_property table to add new fields
        await queryRunner.query(`ALTER TABLE "import_property" ADD "emarat_kar" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "owner_table_id" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "usage_details_table_id" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "property_table_id" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "capital_value" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "total_tax" character varying`);
        await queryRunner.query(`ALTER TABLE "import_property" ADD "mobile" character varying`);

        // Create foreign key constraint
        await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_2ec55a615abf24e021be0904df5" FOREIGN KEY ("common_fields_id") REFERENCES "common_fields_of_property"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraint
        await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_2ec55a615abf24e021be0904df5"`);

        // Remove added columns from import_property
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "mobile"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "total_tax"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "capital_value"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "property_table_id"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "usage_details_table_id"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "owner_table_id"`);
        await queryRunner.query(`ALTER TABLE "import_property" DROP COLUMN "emarat_kar"`);

        // Remove added columns from property table
        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "common_fields_id"`);

        // Remove added columns from property_owner_details
        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP COLUMN "partner_name"`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP COLUMN "marital_status"`);
        await queryRunner.query(`DROP TYPE "public"."property_owner_details_marital_status_enum"`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP COLUMN "gender"`);
        await queryRunner.query(`DROP TYPE "public"."property_owner_details_gender_enum"`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP COLUMN "pan_card"`);

        // Drop the common_fields_of_property table
        await queryRunner.query(`DROP TABLE "common_fields_of_property"`);

        // Drop ENUM types
        await queryRunner.query(`DROP TYPE "public"."common_fields_of_property_sewagesystem_enum"`);
        await queryRunner.query(`DROP TYPE "public"."common_fields_of_property_rainwaterharvesting_enum"`);
        await queryRunner.query(`DROP TYPE "public"."common_fields_of_property_solarproject_enum"`);
        await queryRunner.query(`DROP TYPE "public"."common_fields_of_property_individualtoilet_enum"`);
    }
}
