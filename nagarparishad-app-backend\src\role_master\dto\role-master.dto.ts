import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Transform, plainToClass } from 'class-transformer';
import { IsInt, IsNotEmpty, IsString } from 'class-validator';

export class CreateRoleMasterDto {
  @ApiProperty({ name: 'roleName', type: 'string' })
  @IsNotEmpty()
  @IsString()
  roleName: string;

  @ApiProperty({ name: 'createdBy', type: 'string' })
  @IsNotEmpty()
  @IsString() //After user map change to uuid
  createdBy: string;
}

export class UpdateRoleMasterDto extends PartialType(CreateRoleMasterDto) {}

export class RoleIdMasterDto {
  @ApiProperty({ name: 'role_id', type: 'number' })
  @IsNotEmpty()
  @IsInt()
  // role_id: number;
  @Transform(({ value }) => parseInt(value, 10))
  role_id: number;
}
