import WhiteContainer from "@/components/custom/WhiteContainer";
import React, { useContext, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ArrowUpDown, Edit, Trash } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";
import { useUserRegisterController } from "@/controller/user-register/UserRegisterController";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { MASTER } from "@/constant/config/api.config";
import { GlobalContext } from "@/context/GlobalContext";
import UserRegisterForm from "../UserRegisterForm/UserRegisterForm";
import { useTranslation } from "react-i18next";
import Tippy from "@tippyjs/react";
import "tippy.js/dist/tippy.css";
import { ResponseData } from "@/model/auth/authServices";
import { toast } from "@/components/ui/use-toast";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

const UserRegister = () => {
  const { setOpen, toggleCollapse, setMasterComponent } =
    useContext(GlobalContext);
  const { t } = useTranslation();
  const [editData, setEditData] = useState(null);
  const userRef = useRef(null);
  const { userList, deleteUser, userLoading } = useUserRegisterController();
  const { canPerformAction } = usePermissions();

  const canUpdate = canPerformAction(
    ModuleName.RoleAndUserMangement,
    FormName.User,
    Action.CanUpdate
  );
  const canDelete = canPerformAction(
    ModuleName.RoleAndUserMangement,
    FormName.User,
    Action.CanDelete
  );
  const canCreate = canPerformAction(
    ModuleName.RoleAndUserMangement,
    FormName.User,
    Action.CanCreate
  );

  const dynamicValues = {
    name: t("user.userTitle"),
  };
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  function handleEdit(item: any): void {
    setEditData(item);
    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }

  function handleDelete(item: any): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  const handleConfirmDelete = () => {
    deleteUser(selectedItem.userId, {
      onSuccess: (response: ResponseData) => {
        if (response?.statusCode && response?.statusCode === 200) {
          toast({
            title: t("api.formdelete", dynamicValues),
            variant: "success",
          });
        } else {
          toast({
            title: response?.message,
            variant: "destructive",
          });
        }
      },
      onError: (error) => {
        toast({
          title: error.message,
          variant: "destructive",
        });
      },
    });

    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: `${t("SrNo")}`,
      header: `${t("SrNo")}`,
      cell: ({ row }: { row: any }) => (
        <div className="capitalize">{row.index + 1}</div>
      ),
    },
    {
      accessorKey: "firstname",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("user.firstName")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }: { row: any }) => (
        <div className="">{row.original?.firstname}</div>
      ),
    },
    {
      accessorKey: "lastName",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
          >
            {t("user.lastName")}
          </Button>
        );
      },
      cell: ({ row }: { row: any }) => (
        <div className="">{row.original?.lastname}</div>
      ),
    },
    {
      accessorKey: "email",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
          >
            {t("user.email")}
          </Button>
        );
      },
      cell: ({ row }: { row: any }) => (
        <div className="">{row.original?.email}</div>
      ),
    },
    {
      accessorKey: "role",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
          >
            {t("user.role")}
          </Button>
        );
      },
      cell: ({ row }: { row: any }) => {
        const roleName = row.original?.role?.roleName || "";
        const truncatedRoleName =
          roleName.length > 20 ? roleName.slice(0, 20) + "..." : roleName;
        return (
          <Tippy content={roleName}>
            <div className="cursor-pointer">{truncatedRoleName}</div>
          </Tippy>
        );
      },
    },
    ...(canUpdate || canDelete
      ? [
          {
            accessorKey: `${t("Actions")}`,
            enableHiding: false,
            cell: ({ row }: { row: any }) => (
              <div className="flex space-x-2">
                {canUpdate && (
                  <button
                    className="h-8 w-8 p-0 justify-center"
                    onClick={() => handleEdit(row?.original)}
                  >
                    <Edit className="text-blue-500" />
                  </button>
                )}
                {canDelete && (
                  <button
                    className="h-8 w-8 p-0 justify-center ml-2 !border-BlueText !text-BlueText"
                    onClick={() => handleDelete(row?.original)}
                  >
                    <Trash className="text-red-500" />
                  </button>
                )}
              </div>
            ),
          },
        ]
      : []),
  ];

  const MasterType: string = MASTER.USERLIST;
  const newUserList = userList.map((user: any) => {
    return {
      userId: user.user_id,
      firstname: user.firstname,
      lastname: user.lastname,
      email: user.email,
      mobileNumber: user.mobileNumber,
      address: user.address,
      role: user.role,
      isActive: user.isActive,
    };
  });

  return (
    <div className="w-full h-fit sm:p-6 p-3" ref={userRef && userRef}>
      <h1 className="text-2xl font-semibold font-Poppins w-full ml-3">
        {t("user.userTitle")}
      </h1>

      {canCreate && (
        <WhiteContainer>
          <UserRegisterForm btnTitle={"user"} editData={editData && editData} />
        </WhiteContainer>
      )}
      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={newUserList}
          masterType={MASTER.USERLIST}
          searchKey={"searchFirstName"}
          searchColumn={"firstname"}
          loader={userLoading ? true : false}
        />
      </WhiteContainer>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={selectedItem.first_name}
          onDelete={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default UserRegister;
