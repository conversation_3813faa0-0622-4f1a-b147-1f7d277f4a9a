import React, { useContext, useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  PropertysubtypeSendApiObj,
  PropertysubtypeMasterObject,
} from "../../../model/propertysubtype-master";
import { useToast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { usePropertysubtypeMasterController } from "@/controller/master/PropertySubtypeMasterController";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { PropertytypeMasterObject } from "../../../model/propertytype-master";
import { Loader2 } from "lucide-react";
import { usePropertytypeMasterController } from "@/controller/master/PropertyMasterController";

interface PropertysubtypeMasterInterface {
  btnTitle: string;
  editData?: PropertysubtypeMasterObject;
}

const PropertysubtypeMasterForm = ({
  btnTitle,
  editData,
}: PropertysubtypeMasterInterface) => {
  const { t } = useTranslation();
  const schema = z.object({
    propertySub_name: z.string().trim().min(1, t("errorsRequiredField")),
    propertyType: z.string().trim().min(1, t("errorsRequiredField")),
  });
  const { createPropertysubtype, updatePropertysubtype } =
    usePropertysubtypeMasterController();
  const dynamicValues = {
    name: t("propertysubtype.propertysubtypeLabel"),
  };
  const { setRefreshPropertysubtypeList, refreshPropertysubtypeList } =
    useContext(GlobalContext);
  const { toast } = useToast();
  const [loader, setLoader] = useState(false);
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      propertySub_name: editData?.propertySub_name || "",
      propertyType: editData?.propertyType?.propertyType_id || "",
    },
  });
  const {
    formState: { errors },

  } = form;

  const onSubmit = (
    data: z.infer<typeof schema>,
    event: React.FormEvent<HTMLFormElement>,
  ) => {
    event.preventDefault();

    const DataResponse: PropertysubtypeSendApiObj = {
      propertySub_name: data?.propertySub_name,
      propertyType: data?.propertyType,
    };
    if (
      editData?.propertyType !== undefined &&
      editData?.propertyType !== null
    ) {
      setLoader(true);
      updatePropertysubtype(
        {
          propertysubtypeId: editData?.propertySub_id,
          propertysubtypeData: DataResponse,
        },
        {
          onSuccess: () => {
            toast({
              title: t("api.formupdate", dynamicValues),
              variant: "success",
            });
            form.reset({
              propertySub_name: "",
              propertyType: "",
            });
            setLoader(false);
            setRefreshPropertysubtypeList(!refreshPropertysubtypeList);
          },
          onError: (error: any) => {
            toast({
              title: error.message,
              variant: "destructive",
            });
            setLoader(false);
          },
        },
      );
    } else {
      setLoader(true);
      createPropertysubtype(DataResponse, {
        onSuccess: () => {
          toast({
            title: t("api.formcreate", dynamicValues),
            variant: "success",
          });
          form.reset({
            propertySub_name: "",
            propertyType: "",
          });
          setLoader(false);
        },
        onError: (error: any) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
          setLoader(false);
        },
      });
    }
  };
  const { propertytypeList } = usePropertytypeMasterController();
  useEffect(() => {
    if (editData) {
      form.reset({
        propertySub_name: editData?.propertySub_name || "",
        propertyType: editData?.propertyType?.propertyType_id || "",
      });
    } else {
      form.reset({
        propertySub_name: "",
        propertyType: "",
      });
    }
  }, [editData]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full md:w-full"
        >
          <div className="form-flex">
            <div className="form-element">
              <FormField
                control={form.control}
                name="propertyType"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>{t("propertytype.propertytypeLabel")}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {(propertytypeList &&
                            propertytypeList?.length > 0 &&
                            propertytypeList.filter(
                              (property: PropertytypeMasterObject) =>
                                property.propertyType_id === field.value,
                            )[0]?.propertyType) ||
                            "Select Property Type"}
                        </SelectTrigger>
                      </FormControl>

                      <SelectContent>
                        {propertytypeList && propertytypeList?.length > 0 && (
                          <>
                            {propertytypeList.map(
                              (property: PropertytypeMasterObject) => (
                                <SelectItem
                                  value={property.propertyType_id}
                                  key={property.propertyType_id}
                                >
                                  {property.propertyType}
                                </SelectItem>
                              ),
                            )}
                          </>
                        )}
                      </SelectContent>
                    </Select>
                    {errors.propertyType && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div className="form-element ">
              <FormField
                control={form.control}
                name="propertySub_name"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {t("propertysubtype.propertysubtypeLabel")}
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="mt-1"
                        placeholder={t("propertysubtype.propertysubtypeLabel")}
                        {...field}
                      />
                    </FormControl>
                    {errors.propertySub_name && (
                      <FormMessage className="ml-1">
                        {t("errors.requiredField")}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="w-full flex justify-end mt-4">
            {loader ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 !w-10 animate-spin " />
                {t("pleaseWait")}
              </Button>
            ) : (
              <Button type="submit" variant="submit">
                {t(btnTitle)}
              </Button>
            )}
          </div>
        </form>
      </Form>
    </>
  );
};

export default PropertysubtypeMasterForm;
