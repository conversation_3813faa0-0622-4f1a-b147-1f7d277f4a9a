import { Repository } from 'typeorm';
import { Floor_Master } from '../entities'; // Adjust the path according to your project structure
import { InjectRepository } from '@nestjs/typeorm';

export class FloorMasterRepository extends Repository<Floor_Master> {
  constructor(
    @InjectRepository(Floor_Master)
    private readonly floorMasterRepository: Repository<Floor_Master>,
  ) {
    super(
      floorMasterRepository.target,
      floorMasterRepository.manager,
      floorMasterRepository.queryRunner,
    );
  }  

  // Fetch all floor records
  async getAll(): Promise<Floor_Master[]> {
    return await this.floorMasterRepository.find();
  }

  // Fetch floor records by their name (optional utility method)
  async getFloorKeywords(): Promise<string[]> {
    const floors = await this.floorMasterRepository.find();
    return floors.map(floor => floor.floor_name); 
  }

  // Fetch a single floor by ID
  async findById(id: string): Promise<Floor_Master | undefined> {
    const floor = await this.floorMasterRepository
      .createQueryBuilder('floor')
      .where('floor.floor_id = :id', { id })  // Adjust this to match your actual primary key column
      .getOne(); // Fetches a single record
    return floor;
  }
}
