import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeUniquConstrint1731567696570 implements MigrationInterface {
    name = 'ChangeUniquConstrint1731567696570'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" DROP CONSTRAINT "FK_89469a4ad05a7ed3f78d2df185a"`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" DROP CONSTRAINT "UQ_89469a4ad05a7ed3f78d2df185a"`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" ADD CONSTRAINT "FK_89469a4ad05a7ed3f78d2df185a" FOREIGN KEY ("usage_sub_type_master_id") REFERENCES "usage_sub_type_master"("usage_sub_type_master_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" DROP CONSTRAINT "FK_89469a4ad05a7ed3f78d2df185a"`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" ADD CONSTRAINT "UQ_89469a4ad05a7ed3f78d2df185a" UNIQUE ("usage_sub_type_master_id")`);
        await queryRunner.query(`ALTER TABLE "master_ghanKachra_rate" ADD CONSTRAINT "FK_89469a4ad05a7ed3f78d2df185a" FOREIGN KEY ("usage_sub_type_master_id") REFERENCES "usage_sub_type_master"("usage_sub_type_master_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
