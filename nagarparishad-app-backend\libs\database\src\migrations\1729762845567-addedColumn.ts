import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedColumn1729762845567 implements MigrationInterface {
    name = 'AddedColumn1729762845567'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property" ADD "landmark" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "landmark"`);
    }

}
