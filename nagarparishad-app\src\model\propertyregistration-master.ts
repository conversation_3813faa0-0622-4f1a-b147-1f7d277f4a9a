export interface PropertyLocationDetails {
  gat_number: string;
  property_number: string;
  property_old_number?:string;
  property_master_id?: string;
  building_permission_number: string;
  city_survey_number: string;
  property_status: string;
  plot_number: string;
  block_number: string;
  house_number: string;
  location: string;
  house_or_apartment_name: string;
  street: streetObject;
  landmark: string;
  country: string;
  city: string;
  latitude: string;
  longitude: string;
  ward: wardObject;
  register: registerObject | null;
  zone: zoneObject;
  adminstrativeBoundary: string;
  electionBoundary: string;
  uploaded_files: string[];
  sr_no: number;
  property_desc: string;
  area: string;
  property_remark:string;
}

export interface wardObject {
 ward_id:string;
 ward_name:string
}
export interface registerObject {
  register_id:string;
  register_name:string
 }
export interface zoneObject {
 zone_id:string;
 zoneName:string
}
export interface streetObject {
 street_id:string;
 street_name:string
}

export interface property_owner_details {
  marriage_flag?: boolean;
  name?: string;
  firstname?: string;
  middlename?: string;
  lastname?: string;
  organization?: string;
  mobile_number?: string;
  email_id?: string;
  aadhar_number?: string;
  pan_card?: string;
  gender?: string;
  owner_type?: string;
}

export interface AssessmentDetailsForm {
  email_id: string;
  aadhar_number(aadhar_number: any): string;
  pan_card: string;
  gender: any;
  marriage_flag: boolean;
  mobile_number: string;
  name: string;
  property_owner_details: property_owner_details[]; // Only keeping property_usage_details
}

export interface PropertyUsageDetail {
  property_usage_details_id: string; // Required, should not be null
  construction_area: number | null; // Allowing null
  length: number; // Required, should not be null
  width: number; // Required, should not be null
  are_sq_ft: number; // Required, should not be null
  are_sq_meter: number; // Required, should not be null
  construction_end_date: string | null; // Allowing null
  Building_age: number | null; // Allowing null
  floor: string | null; // Allowing null
  flat_no: string | null; // Allowing null
  authorized: boolean; // Required, should not be null
  propertyType: {
    propertyType_id: string; // Required, should not be null
    propertyType: string; // Required, should not be null
  };
  usageType: {
    usage_type_id: string; // Required, should not be null
    usage_type: string; // Required, should not be null
  };
  usageSubType?: { // Making usageSubType optional
    usage_sub_type_id: string; // Required, should not be null
    usage_sub_type: string; // Required, should not be null
  } | null; // Allowing null for usageSubType
  construction_year: number; // Required, should not be null
}


export interface PropertyAssessmentDetailsForm {
  property_usage_details: PropertyUsageDetail[]; // Only keeping property_usage_details
}

export interface PlotDetailsForm {
  GISID: string; // Geographic Information System ID
  propertyDescription: string; // Description of the property
  completionCertificate: string | null; // Completion certificate information (nullable)
  accessRoad: string; // Information about the access road
  individualToilet: "yes" | "no"; // Indicates whether there is an individual toilet
  toiletType: string; // Type of toilet (e.g., Indian, Western)
  totalNumber: string; // Total string of something (e.g., flats, units)
  lightingFacility: string; // Details about lighting facilities
  tapConnection: string; // Tap connection information (nullable)
  totalConnections: string; // Total string of connections (e.g., water, electricity)
  solarProject: "yes" | "no"; // Indicates whether there is a solar project
  rainWaterHarvesting: "yes" | "no"; // Indicates whether rainwater harvesting is implemented
  sewageSystem: "yes" | "no"; // Indicates whether there is a sewage system
  groundFloorArea: string; // Area of the ground floor
  remainingGroundFloorArea: string; // Remaining area of the ground floor
}


export interface PropertyRegistrationInterface {
  PropertyLocationDetails: PropertyLocationDetails;
  AssessmentDetailsForm: AssessmentDetailsForm;
  PropertyAssessmentDetailsForm: PropertyAssessmentDetailsForm;
  PlotDetailsForm: PlotDetailsForm;
}

interface WardCreateObject {
  ward_name: string;
}

interface ZoneCreateObject {
  zoneName: string;
}

export interface PropertyList {
  property_master_id: string;
  firstname: string;
  lastname: string;
  mobile_number: string;
  ward: WardCreateObject;
  zone: ZoneCreateObject;
}
export interface PropertyListAllApi {
  statusCode: number;
  message: string;
  data: PropertyList[];
}

export interface GetSingleRecord {
  property_owner_details(arg0: string, property_owner_details: any): unknown;
  // Property Location Details
  property_id: string;
  building_permission_number: string;
  city_survey_number: string;
  property_status: number;
  plot_number: string;
  block_number: string;
  house_number: string;
  location: any;
  house_or_apartment_name: string;
  street: any;
  landmark: string;
  country: string;
  city: string;
  latitude: string;
  longitude: string;
  ward: {
    ward_name: string; // Updated to include ward_name from the response
    ward_id:string;
  };
  register: registerObject | null;
  zone: {
    zoneName: string; // Updated to include zoneName from the response
    zone_id:string;
  };
  adminstrativeBoundary: any;
  electionBoundary: any;
  uploaded_files: string[];
  sequence_no: string;
  property_desc: string | null; // Changed to reflect potential null
  area: any;
  mobile_number: string;

  // Assessment Details
  middlename: string;
  firstname: string;
  lastname: string;
  organization: string;
  email_id: string;
  aadhar_number: string;
  pan_card: string;
  gender: string;
  owner_type: string;

  // Property Assessment Details
  plot_area: number;
  propertyType: any;
  propertySubType: any;
  usage: any;
  usageSub: any;
  bill_type: number;
  delivery_type: number;
  current_assessment: string;
  first_assessment: string;
  construction_start_date: string;
  construction_end_date: string;

  // Plot Details
  flat_status: number;
  floor: string;
  flat_no: string;
  occupancy_type: string;
  constructionClass: any;
  tax_start_date: string;
  room_detail_info_flag: boolean;
  room_detail: string;
  manual_area_flag: boolean;
  construction_area: number;
  carpet_area: number | null; // Changed to reflect potential null
  exempted_area: number | null; // Changed to reflect potential null
  assessable_area: number | null; // Changed to reflect potential null
  land_cost: number | null; // Changed to reflect potential null
  standard_rate: number | null; // Changed to reflect potential null
  annual_rent: number | null; // Changed to reflect potential null
  capital_value: number | null; // Changed to reflect potential null
  remark: string | null; // Changed to reflect potential null
  property_photographs: string;

  // New Fields from Latest Response
  propertyNumber: string; // New field
  Plot_construction_area: number; // New field
  Plot_empty_area: number; // New field
  snp_ward: string | null; // New field from response
  zone_code: string; // New field from response
  gat_number: string; // New field from response
  gis_number: string; // New field from response
  note: string; // New field from response
  survey_person_code: string; // New field from response
  survey_date: string; // New field from response
  sr_no: number; // New field from response
  property_owner_details: Array<{
    property_owner_details_id: string;
    name: string;
    mobile_number: string;
    email_id: string;
    aadhar_number: string;
    owner_type: {
      owner_type: string;
    };
  }>; // Updated based on the response
  property_usage_details: Array<{
    property_usage_details_id: string;
    construction_area: number | null; // Allowing null
    length: number;
    width: number;
    are_sq_ft: number;
    are_sq_meter: number;
    construction_end_date: string | null; // Allowing null
    Building_age: number | null; // Allowing null
    floor: string | null; // Allowing null
    flat_no: string | null; // Allowing null
    authorized: boolean;
    propertyType: {
      propertyType_id: string; // Added propertyType_id
      propertyType: string;
    };
    usageType: {
      usage_type_id: string; // Added usage_type_id
      usage_type: string;
    };
    usageSubType: { // Making usageSubType optional
      usage_sub_type_id:string;
      usage_sub_type: string;
    } | null; // Allowing null for usageSubType
    construction_year: number;
  }>;
  
  waste_tax: number; // New field from response
}


export interface GetSingleRecordApi {
  statusCode: number;
  message: string;
  data: GetSingleRecord;
}
