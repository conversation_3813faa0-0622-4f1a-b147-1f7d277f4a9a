import { MigrationInterface, QueryRunner } from "typeorm";

export class TaxFyReocrdColUpdate1728475625695 implements MigrationInterface {
    name = 'TaxFyReocrdColUpdate1728475625695'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tax_fy_records" DROP CONSTRAINT "FK_c9ce89ec90dc8f02667b203ab8c"`);
        await queryRunner.query(`ALTER TABLE "tax_fy_records" RENAME COLUMN "financial_year_id" TO "financial_year"`);
        await queryRunner.query(`ALTER TABLE "tax_fy_records" DROP COLUMN "financial_year"`);
        await queryRunner.query(`ALTER TABLE "tax_fy_records" ADD "financial_year" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tax_fy_records" DROP COLUMN "financial_year"`);
        await queryRunner.query(`ALTER TABLE "tax_fy_records" ADD "financial_year" uuid`);
        await queryRunner.query(`ALTER TABLE "tax_fy_records" RENAME COLUMN "financial_year" TO "financial_year_id"`);
        await queryRunner.query(`ALTER TABLE "tax_fy_records" ADD CONSTRAINT "FK_c9ce89ec90dc8f02667b203ab8c" FOREIGN KEY ("financial_year_id") REFERENCES "financial_year"("financial_year_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
