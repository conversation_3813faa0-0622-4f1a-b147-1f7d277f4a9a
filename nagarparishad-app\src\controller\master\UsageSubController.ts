import { useContext } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import Api from "../../services/ApiServices";
import {
  UsageSubMasterApiResponse,
  UsageSubCreateObject,
} from "@/model/usagesub-master";
import { GlobalContext } from "@/context/GlobalContext";

const fetchUsageSubList = () => {
  return new Promise((resolve, reject) => {
    Api.getAllUsageSubList((response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(new Error("Error fetching usage sub-list"));
      }
    });
  });
};

const createUsageSub = (newUsageSub) => {
  return new Promise((resolve, reject) => {
    Api.createUsageSub(newUsageSub, (response) => {
      if (response.status && response.data.statusCode === 201) {
        resolve(response.data.data);
      } else {
        reject(new Error("Error creating usage sub"));
      }
    });
  });
};

const updateUsageSub = ({ subUsageId, subUsageData }) => {
  return new Promise((resolve, reject) => {
    Api.updateUsageSub(subUsageId, subUsageData, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(response.data.data);
      } else {
        reject(new Error("Error updating usage sub"));
      }
    });
  });
};

const deleteUsageSub = (subUsageId) => {
  return new Promise((resolve, reject) => {
    Api.deleteUsageSub(subUsageId, (response) => {
      if (response.status && response.data.statusCode === 200) {
        resolve(subUsageId);
      } else {
        reject(new Error("Error deleting usage sub"));
      }
    });
  });
};

export const useUsageSubController = () => {
  const queryClient = useQueryClient();

  const { data: usageSubList, isLoading:usageSubLoading } = useQuery({
    queryKey: ["usageSubMaster"],
    queryFn: fetchUsageSubList,
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  const createUsageSubMutation = useMutation({
    mutationFn: createUsageSub,
    onMutate: async (newUsageSub) => {
      await queryClient.invalidateQueries({ queryKey: ["usageSubMaster"] });

      const previousUsageSubs = queryClient.getQueryData(["usageSubMaster"]);
      queryClient.setQueryData(["usageSubMaster"], (old) => [
        newUsageSub,
        ...old,
      ]);

      return { previousUsageSubs };
    },
    onError: (err, newUsageSub, context) => {
      queryClient.setQueryData(["usageSubMaster"], context.previousUsageSubs);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["usageSubMaster"] });
    },
  });

  const updateUsageSubMutation = useMutation({
    mutationFn: updateUsageSub,
    onMutate: async ({ subUsageId, subUsageData }) => {
      await queryClient.invalidateQueries({ queryKey: ["usageSubMaster"] });

      const previousUsageSubs = queryClient.getQueryData(["usageSubMaster"]);
      console.log("sub uage id", subUsageId, "&", subUsageData);
      queryClient.setQueryData(["usageSubMaster"], (old) =>
        old.map((usage) =>
          usage.usageSub_id === subUsageId
            ? { ...usage, ...subUsageData }
            : usage,
        ),
      );

      return { previousUsageSubs };
    },
    onError: (err, { subUsageId, subUsageData }, context) => {
      queryClient.setQueryData(["usageSubMaster"], context.previousUsageSubs);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["usageSubMaster"] });
    },
  });

  const deleteUsageSubMutation = useMutation({
    mutationFn: deleteUsageSub,
    onMutate: async (subUsageId) => {
      await queryClient.invalidateQueries({ queryKey: ["usageSubMaster"] });

      const previousUsageSubs = queryClient.getQueryData(["usageSubMaster"]);

      queryClient.setQueryData(["usageSubMaster"], (old) =>
        old.filter((usage) => usage.usageSub_id !== subUsageId),
      );

      return { previousUsageSubs };
    },
    onError: (err, subUsageId, context) => {
      queryClient.setQueryData(["usageSubMaster"], context.previousUsageSubs);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["usageSubMaster"] });
    },
  });

  return {
    usageSubList: usageSubList || [],
    usageSubLoading,
    createUsageSub: createUsageSubMutation.mutate,
    updateUsageSub: updateUsageSubMutation.mutate,
    deleteUsageSub: deleteUsageSubMutation.mutate,
  };
};
