import { FloorMasterRepository } from 'libs/database/repositories/floorMaster.repository';
import { CommonFiledsOfPropertyRepository } from './../../libs/database/src/repositories/commonFieldProperty.repository';
import {
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import { parseCsv } from '../utils/helper';
import { CreatePropertyMasterDto } from './dto/create-property_master.dto';
import { UpdatePropertyMasterDto } from './dto/update-property_master.dto';
import { PropertyOwnerDetailsDto } from './dto/update-property_master.dto';
import {
  PropertyMasterRepository,
  WardMasterRepository,
  ZoneMasterRepository,
  Property_Usage_DetailsRepository,
  Property_Owner_DetailsRepository,
  StreetMasterRepository,
  OwnerTypeRepository,
  PropertyTypeMasterRepository,
  UsageMasterRepository,
  UsageSubMasterRepository,
  DeletedPropertyUsageRepository,
  BackupPropertyUsageDetailsRepository,
  ImportPropertyMasterRepository,
  MilkatKarTaxeRepository,
  PaymentInfoRepository,
  RegisterNumberRepository, // Added RegisterNumberRepository
} from 'libs/database/repositories';
import { DataSource } from 'typeorm';

import { PropertyMasterIdDto } from './dto/property_master.dto';
import { SearchPropertyDto } from './dto/search-property_master.dto';
import { AuthHelper } from '@helper/helpers';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { CSVDTO } from './dto/csv.validator.dto';
import * as csv from 'csv-parse';
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { UpdatePropertyDto } from './dto/update-property_master.dto';
import {
  DeletedPropertyEntity,
  Floor_Master,
  Gender,
  MaritalStatus,
  UsageSubTypeMasterEntity,
  RegisterNumberEntity, // Added RegisterNumberEntity entity
} from 'libs/database/entities';
import { KarAkaraniService } from 'src/kar-akarani/karAkarani.service';
import { EntityManager } from 'typeorm';
import {
  StreetMasterEntity,
  Ward_Master,
  ZoneMaster,
  PropertyEntity,
  Owner_type_master,
  UsageTypeMasterEntity,
  PropertyTypeMasterEntity,
  Property_Owner_Details_Entity,
  Property_Usage_Details_Entity,
  CommonFiledsOfPropertyEntity,
} from 'libs/database/entities';
import { DeletedPropertyRepository } from 'libs/database/repositories/deleted_property.repository';
import { InjectDataSource } from '@nestjs/typeorm';
@Injectable()
export class PropertyMasterService {
  entityManager: any;
  constructor(
    @InjectDataSource() private readonly dataSource: DataSource,

    private readonly propertyMasterRepository: PropertyMasterRepository,
    private readonly wardRepository: WardMasterRepository,
    private readonly zoneRepository: ZoneMasterRepository,
    private readonly authHelper: AuthHelper,
    private readonly property_Usage_DetailsRepository: Property_Usage_DetailsRepository,
    private readonly property_Owner_DetailsRepository: Property_Owner_DetailsRepository,
    private readonly streetMasterRepository: StreetMasterRepository,
    private readonly ownerTypeRepository: OwnerTypeRepository,
    private readonly propertyTypeMasterRepository: PropertyTypeMasterRepository,
    private readonly usageTypeMasterRepository: UsageMasterRepository,
    private readonly CommonFiledsOfPropertyRepository: CommonFiledsOfPropertyRepository,
    private readonly wardMasterRepository: WardMasterRepository,
    private readonly usageSubMasterRepository: UsageSubMasterRepository,
    private readonly KarService: KarAkaraniService,
    private readonly FloorMasterRepository: FloorMasterRepository,
    private readonly deletedPropertyRepo: DeletedPropertyRepository,
    private readonly backupPropertyUsageDetailsRepo: BackupPropertyUsageDetailsRepository,
    private readonly importProperty: ImportPropertyMasterRepository,
    private readonly milkatKarTaxRepository: MilkatKarTaxeRepository,
    private readonly paymentInfoRepository: PaymentInfoRepository,
    private readonly registerNumberRepository: RegisterNumberRepository, // Added RegisterNumberRepository
  ) {}

  /// for generating next property number while creating
  private async getNextNewPropertyNumber(ward_number: string) {
    const ward = await this.wardMasterRepository.findOne({
      where: {
        ward_name: ward_number,
      },
    });
console.log("added-->")
    const lastProperty = await this.propertyMasterRepository.findOne({
      where: { ward: { ward_id: ward.ward_id } },
      order: { propertyNumber: 'DESC' },
    });

    const lastPropertyByNewPropertyNumber =
      await this.propertyMasterRepository.findOne({
        where: { ward: { ward_id: ward.ward_id } },
        order: { newPropertyNumber: 'DESC' },
      });

    let nextNumber = 1;
    let nextNewPropertyNumber = 1;

    if (lastProperty && lastProperty.propertyNumber) {
      const lastNumber = parseInt(lastProperty.propertyNumber.slice(-5));
      nextNumber = lastNumber + 1;
    }

    if (
      lastPropertyByNewPropertyNumber &&
      lastPropertyByNewPropertyNumber.newPropertyNumber
    ) {
      const lastNewNumber = parseInt(
        lastPropertyByNewPropertyNumber.newPropertyNumber.slice(-5),
      );
      nextNewPropertyNumber = lastNewNumber + 1;
    }

    // Function to check if a property number exists in the database
    const propertyNumberExists = async (propertyNumber: string) => {
      const existingProperty = await this.propertyMasterRepository.findOne({
        where: { propertyNumber },
      });
      return !!existingProperty;
    };

    // Generate unique property numbers
    let propertyNumber = `SNP${ward_number}${nextNumber.toString().padStart(5, '0')}`;
    let newPropertyNumber = `SNP${ward_number}${nextNewPropertyNumber.toString().padStart(5, '0')}`;

    // Check for uniqueness and increment if necessary
    while (await propertyNumberExists(propertyNumber)) {
      nextNumber++;
      propertyNumber = `SNP${ward_number}${nextNumber.toString().padStart(5, '0')}`;
    }

    while (await propertyNumberExists(newPropertyNumber)) {
      nextNewPropertyNumber++;
      newPropertyNumber = `SNP${ward_number}${nextNewPropertyNumber.toString().padStart(5, '0')}`;
    }

    return {
      propertyNumber,
      newPropertyNumber,
    };
  }

  // async create(createPropertyMasterDto: CreatePropertyMasterDto) {
  //   try {
  //     let { propertyNumber } = createPropertyMasterDto;

  //     if (!propertyNumber) {
  //       propertyNumber =
  //         await this.authHelper.generateRandomNumberWithPrefix('SN');
  //       createPropertyMasterDto.propertyNumber = propertyNumber;
  //     }
  //     const saveData = await this.propertyMasterRepository.saveData(
  //       createPropertyMasterDto,
  //     );

  //     return {
  //       message: 'Data Saved SuccessFully',
  //     };
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  async deleteDuplicates() {
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.startTransaction();
    try {
      // Step 1: Identify duplicates
      const duplicates = await queryRunner.manager
        .createQueryBuilder()
        .select('p.*')
        .from((subQuery) => {
          return subQuery
            .select('p.*')
            .addSelect(
              'ROW_NUMBER() OVER (PARTITION BY p.propertyNumber, p.old_propertyNumber, p.import_property_id ORDER BY p.property_id) as row_num',
            )
            .from(PropertyEntity, 'p');
        }, 'p')
        .where('p.row_num > 1')
        .getRawMany();

      console.log('Raw duplicates data:', duplicates);

      const duplicateIds = duplicates
        .filter((dup) => dup.row_num > 1)
        .map((dup) => dup.property_id);

      console.log('Filtered duplicate IDs:', duplicateIds);

      if (duplicateIds.length === 0) {
        console.log('No duplicates found.');
        return {
          message: 'No duplicates found.',
          data: [],
        };
      }

      // Step 2: Delete related records from property_usage_details
      const usageDetailsResult = await queryRunner.manager
        .createQueryBuilder()
        .delete()
        .from(Property_Usage_Details_Entity)
        .where('property_id IN (:...ids)', { ids: duplicateIds })
        .execute();

      console.log(
        'Deleted from property_usage_details:',
        usageDetailsResult.affected ?? 0,
      );

      // Step 3: Delete related records from property_owner_details
      const ownerDetailsResult = await queryRunner.manager
        .createQueryBuilder()
        .delete()
        .from(Property_Owner_Details_Entity)
        .where('property_id IN (:...ids)', { ids: duplicateIds })
        .execute();

      console.log(
        'Deleted from property_owner_details:',
        ownerDetailsResult.affected ?? 0,
      );

      // Step 4: Delete duplicate records from property
      const propertyResult = await queryRunner.manager
        .createQueryBuilder()
        .delete()
        .from(PropertyEntity)
        .where('property_id IN (:...ids)', { ids: duplicateIds })
        .execute();

      console.log('Deleted from property:', propertyResult.affected ?? 0);

      await queryRunner.commitTransaction();
      return {
        message: 'Deleted successfully',
        data: duplicateIds,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('Transaction failed:', error);
      throw new NotAcceptableException('Failed to delete duplicates');
    } finally {
      await queryRunner.release();
    }
  }

  async createProperty(createPropertyDto: CreatePropertyMasterDto) {
    const {
      property_owner_details,
      property_usage_details,
      street_id,
      ward_id,
      zone_id,
      register_id, // Added register_id
      old_propertyNumber,

      ...propertyData
    } = createPropertyDto;

    return await this.propertyMasterRepository.manager.transaction(
      async (transactionManager: EntityManager) => {
        // Find the related entities for street, ward, and zone using transaction manager
        const street = await transactionManager.findOne(StreetMasterEntity, {
          where: { street_id },
        });

        const ward = await transactionManager.findOne(Ward_Master, {
          where: { ward_id },
        });
        const zone = await transactionManager.findOne(ZoneMaster, {
          where: { zone_id },
        });
        
        const register_number = register_id ? await transactionManager.findOne(RegisterNumberEntity, { // Changed to register_number
          where: { register_id },
        }) : null;

        if (!street || !ward || !zone || (register_id && !register_number)) { // Changed to register_number validation
          throw new Error('Invalid street, ward, zone, or register data.');
        }

        const { propertyNumber, newPropertyNumber } =
          await this.getNextNewPropertyNumber(ward.ward_name);
        let oldNumber = old_propertyNumber?.trim();
        const property = transactionManager.create(PropertyEntity, {
          ...propertyData,
          propertyNumber,
          old_propertyNumber: oldNumber,
          newPropertyNumber,
          street,
          ward,
          zone,
          register_number, // Changed to register_number
        
        });
        const savedProperty = await transactionManager.save(property);

        const totalProperties = await transactionManager.count(PropertyEntity);
        savedProperty.sr_no = totalProperties;

        await transactionManager.save(savedProperty);

        // Insert Property Owner Details
        for (const ownerDetails of property_owner_details) {
          const owner_type = await transactionManager.findOne(
            Owner_type_master,
            {
              where: { owner_type_id: ownerDetails.owner_type_id },
            },
          );

          if (!owner_type) {
            throw new Error(
              `Invalid owner type: ${ownerDetails.owner_type_id}`,
            );
          }

          const ownerEntity = transactionManager.create(
            Property_Owner_Details_Entity,
            {
              ...ownerDetails,
              owner_type,
              property: savedProperty,
              gender: ownerDetails.gender as Gender,
              marital_status: ownerDetails?.marital_status as MaritalStatus,
            },
          );

          await transactionManager.save(ownerEntity);
        }

        // Insert Property Usage Details
        for (const [index, usageDetails] of property_usage_details.entries()) {
          const propertyType = await transactionManager.findOne(
            PropertyTypeMasterEntity,
            {
              where: { propertyType_id: usageDetails.propertyType_id },
            },
          );
          const usageType = await transactionManager.findOne(
            UsageTypeMasterEntity,
            {
              where: { usage_type_id: usageDetails.usage_type_id },
            },
          );
          const floor = usageDetails.floor_id
            ? await transactionManager.findOne(Floor_Master, {
                where: { floor_id: usageDetails.floor_id },
              })
            : null;
          let usageSubType = null;
          if (usageDetails.usage_sub_type_master_id) {
            usageSubType = await transactionManager.findOne(
              UsageSubTypeMasterEntity,
              {
                where: {
                  usage_sub_type_master_id:
                    usageDetails.usage_sub_type_master_id,
                },
              },
            );
          }
          console.log('usageSubType777', usageSubType);
          console.log(
            'usageSubType777*888',
            usageDetails.usage_sub_type_master_id,
          );

          const usageEntity = transactionManager.create(
            Property_Usage_Details_Entity,
            {
              ...usageDetails,
              orderIndex: index + 1,
              propertyType,
              usageType,
              usageSubType,
              floorType: floor,
              property: savedProperty,
            },
          );

          await transactionManager.save(usageEntity);
        }

        const commonField = transactionManager.create(
          CommonFiledsOfPropertyEntity,
          {
            ...propertyData.commonFields,
            property: savedProperty,
          },
        );

        await transactionManager.save(commonField);

        // Call calculateTaxForProperty with transactionManager

        // Use below code to gernate milkar akarni after property creation

        // await this.KarService.calculateTaxForProperty(savedProperty.property_id, transactionManager);

        return savedProperty;
      },
    );
  }

  async findAll(
    value: string,
    searchOn: string,
    page?: number,
    limit?: number,
  ) {
    try {
      console.log(searchOn, value);
      limit = limit ?? 200;
      const getAllData = await this.propertyMasterRepository.findAllData(
        value,
        searchOn,
        { page, limit },
      );
      if (!getAllData.data || getAllData.data.length === 0) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }
  async findOne_old(propertyMaster: PropertyMasterIdDto) {
    // try {
    //   const { property_master_id } = propertyMaster;
    //   const checkData = await this.propertyMasterRepository.findById(property_master_id);
    //   if (!checkData) {
    //     throw new NotFoundException('Data Not found');
    //   }
    //   // Format and add construction_year
    //   const updatedData = JSON.parse(JSON.stringify(checkData)); // Deep clone to modify
    //   // Adding construction_year to each property_usage_details
    //   if (updatedData.property_usage_details) {
    //     updatedData.property_usage_details = updatedData.property_usage_details.map((detail: any) => {
    //       const constructionEndDate = new Date(detail.construction_end_date);
    //       const constructionYear = constructionEndDate.getFullYear();
    //       return {
    //         ...detail,
    //         construction_year: constructionYear
    //       };
    //     });
    //   }
    //   return {
    //     message: 'Data found Success',
    //     data: {
    //       ...updatedData,
    //       waste_tax: 5 // Adding waste_tax
    //     },
    //   };
    // } catch (error) {
    //   throw error;
    // }
  }

  async findOne(value: string, searchOn: string) {
    try {
      const checkData = await this.propertyMasterRepository.getData(
        value,
        searchOn,
      );
const getAllPayments=await this.paymentInfoRepository.getPaymentsByPropertyNumber(   value,
        searchOn,)
      // Format and add construction_year
      const updatedData = JSON.parse(JSON.stringify(checkData)); // Deep clone to modify

      // Adding construction_year to each property_usage_details
      console.log('dataa------------>', updatedData.property_usage_details);
      if (updatedData.property_usage_details) {
        updatedData.property_usage_details =
          updatedData.property_usage_details.map((detail: any) => {
            let constructionYear;
            let constructionStartDate;

            if (detail.construction_end_date) {
              constructionStartDate = new Date(detail.construction_start_date);
              constructionYear =
                detail.construction_start_year ||
                new Date(detail.construction_end_date).getFullYear();
            } else {
              // If construction_end_date is null, set constructionYear to 0 if construction_start_year is not provided
              constructionYear = detail.construction_start_year || 0;
              constructionStartDate = detail.construction_start_date
                ? new Date(detail.construction_start_date)
                : null;
            }

            console.log(
              'dataa------------>11',
              constructionYear,
              constructionStartDate,
            );

            return {
              ...detail,
              construction_start_year: constructionYear,
              construction_start_date: constructionStartDate,
            };
          });
      }

      return {
        message: 'Data found Success',
        data: {
          ...updatedData,
          getAllPayments,
          waste_tax: 5, // Adding waste_tax
        },
      };
    } catch (error) {
      throw error;
    }
  }
  // async update(
  //   propertyMaster: PropertyMasterIdDto,
  //   updatePropertyMasterDto: UpdatePropertyMasterDto,
  // ) {
  //   try {
  //     const { property_master_id } = propertyMaster;
  //     const checkData =
  //       await this.propertyMasterRepository.findById(property_master_id);
  //     if (!checkData) {
  //       throw new NotFoundException('Data Not found');
  //     }

  //     const updateData = await this.propertyMasterRepository.updateData(
  //       property_master_id,
  //       updatePropertyMasterDto,
  //     );
  //     if (updateData.affected === 0) {
  //       throw new NotAcceptableException('Failed to update Data');
  //     }
  //     return {
  //       message: 'Update found Sucess',
  //       data: updateData,
  //     };
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  async updateProperty(
    propertyId: string,
    updatePropertyDto: UpdatePropertyDto,
  ) {
    const property = await this.propertyMasterRepository.findOne({
      where: { property_id: propertyId },
      relations: [
        'property_usage_details',
        'property_owner_details',
        'commonFields',
      ], // Ensure common fields are also loaded
    });
    if (!property) {
      throw new NotFoundException('Property not found');
    }

    const street = await this.streetMasterRepository.findOne({
      where: { street_id: updatePropertyDto.street_id },
    });
    if (!street) {
      throw new NotFoundException('Street not found');
    }

    const ward = await this.wardMasterRepository.findOne({
      where: { ward_id: updatePropertyDto.ward_id },
    });
    if (!ward) {
      throw new NotFoundException('Ward not found');
    }

    const zone = await this.zoneRepository.findOne({
      where: { zone_id: updatePropertyDto.zone_id },
    });
    if (!zone) {
      throw new NotFoundException('Zone not found');
    }

    let register_number = null;
    if (updatePropertyDto.register_id) {
      register_number = await this.registerNumberRepository.findOne({
        where: { register_id: updatePropertyDto.register_id },
      });
      if (!register_number) {
        throw new NotFoundException('Register number not found');
      }
    }

    const newUsageEntities: any[] = [];
    const newOwnerEntities: any[] = [];

    if (
      updatePropertyDto.property_usage_details &&
      updatePropertyDto.property_usage_details.length > 0
    ) {
      let lastOrderIndex = property.property_usage_details.length > 0 ? Math.max(...property.property_usage_details.map(d => d.orderIndex)) : 0;
      for (const usageDetailDto of updatePropertyDto.property_usage_details) {
        let usageDetail;
        const propertyType = await this.propertyTypeMasterRepository.findOne({
          where: { propertyType_id: usageDetailDto.propertyType_id },
        });

        const usageType = await this.usageTypeMasterRepository.findOne({
          where: { usage_type_id: usageDetailDto.usage_type_id },
        });
        let floor = null;
        if (usageDetailDto.floor_id) {
          floor = await this.FloorMasterRepository.findOne({
            where: {
              floor_id: usageDetailDto.floor_id ?? null, // If floor_id is undefined, it will explicitly query with null
            },
          });
        }
        let usageSubType = null;
        if (usageDetailDto.usage_sub_type_master_id) {
          usageSubType = await this.usageSubMasterRepository.findOne({
            where: {
              usage_sub_type_master_id:
                usageDetailDto.usage_sub_type_master_id || null,
            },
          });
        }

        if (
          usageDetailDto.property_usage_details_id &&
          usageDetailDto.property_usage_details_id.trim()
        ) {
          usageDetail = await this.property_Usage_DetailsRepository.findOne({
            where: {
              property_usage_details_id:
                usageDetailDto.property_usage_details_id,
            },
          });

          if (!usageDetail) {
            throw new NotFoundException(
              `Property usage details not found for ID: ${usageDetailDto.property_usage_details_id}`,
            );
          }

          usageDetail.property = property; // Associate the property entity
          usageDetail.propertyType = propertyType;
          usageDetail.usageType = usageType;
          usageDetail.usageSubType = usageSubType;
          usageDetail.floorType = floor;

          Object.assign(usageDetail, usageDetailDto);

          await this.property_Usage_DetailsRepository.save(usageDetail);
        } else {
          const { property_usage_details_id, ...remainingFields } =
            usageDetailDto;

          const usageEntity = this.property_Usage_DetailsRepository.create({
            ...remainingFields,
            orderIndex: ++lastOrderIndex,
            property: { property_id: property.property_id }, // Associate using property_id
            propertyType,
            usageType,
            usageSubType,
            floorType: floor,
          });

          newUsageEntities.push(usageEntity);
        }
      }
    }

    // Handle updating property_owner_details
    if (
      updatePropertyDto.property_owner_details &&
      updatePropertyDto.property_owner_details.length > 0
    ) {
      for (const ownerDetailDto of updatePropertyDto.property_owner_details) {
        let ownerDetail;

        if (
          ownerDetailDto.property_owner_details_id &&
          ownerDetailDto.property_owner_details_id.trim()
        ) {
          // Existing owner update
          ownerDetail = await this.property_Owner_DetailsRepository.findOne({
            where: {
              property_owner_details_id:
                ownerDetailDto.property_owner_details_id,
            },
          });

          if (!ownerDetail) {
            throw new NotFoundException(
              `Property owner details not found for ID: ${ownerDetailDto.property_owner_details_id}`,
            );
          }

          const ownerTypeMaster = await this.ownerTypeRepository.findOne({
            where: { owner_type_id: ownerDetailDto.owner_type_id },
          });

          if (!ownerTypeMaster) {
            throw new NotFoundException(
              `Owner type not found for type: ${ownerDetailDto.owner_type_id}`,
            );
          }
          if (
            ownerTypeMaster.owner_type == 'भोगवटादार' ||
            ownerTypeMaster.owner_type == 'भाडेकरू'
          ) {
            const importdata = await this.importProperty.updateBhogwatadharName(
              property.import_property_id,
              ownerDetailDto.name,
            );
          }

          ownerDetail.owner_type = ownerTypeMaster;
          Object.assign(ownerDetail, ownerDetailDto);

          // Ensure gender is assigned correctly
          if (ownerDetailDto.gender) {
            ownerDetail.gender = ownerDetailDto.gender as Gender;
          }

          await this.property_Owner_DetailsRepository.save(ownerDetail);
        } else {
          // New owner creation
          const { property_owner_details_id, ...remainingFields } =
            ownerDetailDto;

          const ownerTypeMaster = await this.ownerTypeRepository.findOne({
            where: { owner_type_id: ownerDetailDto.owner_type_id },
          });

          if (!ownerTypeMaster) {
            throw new NotFoundException(
              `Owner type not found for type: ${ownerDetailDto.owner_type_id}`,
            );
          }
          if (
            ownerTypeMaster.owner_type == 'भोगवटादार' ||
            ownerTypeMaster.owner_type == 'भाडेकरू'
          ) {
            const importdata = await this.importProperty.updateBhogwatadharName(
              property.import_property_id,
              ownerDetailDto.name,
            );
          }

          const ownerEntity = this.property_Owner_DetailsRepository.create({
            ...remainingFields,
            property: { property_id: property.property_id }, // Directly associate property entity
            owner_type: ownerTypeMaster, // Assign the owner type entity
            gender: ownerDetailDto.gender as Gender, // Ensure gender is assigned correctly
            marital_status: ownerDetailDto.marital_status
              ? MaritalStatus.YES
              : MaritalStatus.NO, // Ensure marital status is assigned correctly
          });

          newOwnerEntities.push(ownerEntity);
        }
      }
    }

    // Handle updating commonFields
    if (
      updatePropertyDto.commonFields &&
      updatePropertyDto.commonFields.commonFieldId
    ) {
      const commonFieldId = updatePropertyDto.commonFields.commonFieldId; // Get the commonFieldId from the DTO

      // Find the specific common field by its ID
      const commonFields = await this.CommonFiledsOfPropertyRepository.findOne({
        where: { id: commonFieldId }, // Assuming 'id' is the identifier for common fields
      });

      if (!commonFields) {
        throw new NotFoundException(
          `Common fields with id ${commonFieldId} not found`,
        );
      }

      // Update the found common fields
      Object.assign(commonFields, updatePropertyDto.commonFields);
      // commonFields.is_imarat_kar_full_discount = updatePropertyDto.is_imarat_kar_full_discount;

      // Save the updated common fields
      await this.CommonFiledsOfPropertyRepository.save(commonFields);
    }

    if (updatePropertyDto.deleted_property_usage_details_id) {
      for (const propertyUsageId of updatePropertyDto.deleted_property_usage_details_id) {
        if (!propertyUsageId) continue;

        const propertyUsage =
          await this.property_Usage_DetailsRepository.findOneById(
            propertyUsageId,
          );

        if (propertyUsage) {
          const existingLog =
            await this.backupPropertyUsageDetailsRepo.findById(propertyUsageId);

          if (existingLog) {
            console.log(propertyUsage, '/n already exists in deleted table');
          } else {
            const logEntry = this.backupPropertyUsageDetailsRepo.create({
              property_id: property.property_id,
              property_usage_details_id: propertyUsageId,
              property_usage_details: propertyUsage,
            });
            await this.backupPropertyUsageDetailsRepo.save(logEntry);
          }
        } else {
          console.log(
            `Property usage details not found for ID: ${propertyUsageId}`,
          );
        }
      }
      // Re-order the remaining usage details
      const remainingUsageDetails = await this.property_Usage_DetailsRepository.find({ where: { property: { property_id: property.property_id } }, order: { orderIndex: 'ASC' } });
      for (const [index, detail] of remainingUsageDetails.entries()) {
        detail.orderIndex = index + 1;
        await this.property_Usage_DetailsRepository.save(detail);
      }
    }
    // Set the property "updateStatus" to "Updated"
    property.updateStatus = 'Updated';
    property.street = street;
    property.ward = ward;
    property.zone = zone;
    property.register_number = register_number; // Assign register_number
    Object.assign(property, updatePropertyDto);

    // Save updated property
    await this.propertyMasterRepository.save(property);

    // Save all new usage entities
    for (const usageEntity of newUsageEntities) {
      await this.property_Usage_DetailsRepository.save(usageEntity);
    }

    // Save all new owner entities
    for (const ownerEntity of newOwnerEntities) {
      await this.property_Owner_DetailsRepository.save(ownerEntity);
    }

    return await this.propertyMasterRepository.findOne({
      where: { property_id: property.property_id },
    });
  }

  // async testingDeletion(propertyId: string, deleted_property_usage_details_ids : UpdatePropertyDto["deleted_property_usage_details_id"]){
  //   const property = await this.propertyMasterRepository.findOne({
  //     where: { property_id: propertyId },
  //     relations: [
  //       'property_usage_details',
  //       'property_owner_details',
  //     ],
  //   });
  //   if (deleted_property_usage_details_ids) {
  //     for (const propertyUsageId of deleted_property_usage_details_ids) { // Change 'in' to 'of'
  //       if (!propertyUsageId) continue; // Skip if null or undefined

  //       const propertyUsage = await this.property_Usage_DetailsRepository.findOneById(propertyUsageId);

  //       if (propertyUsage) {
  //         const existingLog = await this.deletedPropertyUsageRepo.findOneById(propertyUsageId);

  //         if (existingLog) {
  //           console.log(propertyUsage, "/n already exists in deleted table");
  //         } else {
  //           const logEntry = this.deletedPropertyUsageRepo.create({
  //             property,
  //             property_usage: propertyUsage,
  //           });
  //           await this.deletedPropertyUsageRepo.save(logEntry);
  //         }
  //       } else {
  //         console.log(`Property usage details not found for ID: ${propertyUsageId}`);
  //       }
  //     }
  //   }
  // }

  async deleteProperty(propertyId: string) {
    try {
      console.log('propertyIdpropertyIdpropertyId', propertyId);

      // Find the property in the main repository
      const property = await this.propertyMasterRepository.findById(propertyId);
      if (!property) throw new Error('Property not found');

      // Check if a deleted property record already exists
      let deletedProperty = await this.deletedPropertyRepo.findById(propertyId);

      if (deletedProperty) {
        // If it exists, update the existing record
        deletedProperty.property = property;
      } else {
        // If it doesn't exist, create a new record
        deletedProperty = new DeletedPropertyEntity();
        deletedProperty.property = property;
        deletedProperty.property_id = propertyId;
      }

      // Save the deleted property record
      await this.deletedPropertyRepo.save(deletedProperty);

      // Delete the property from the main repository
      await this.propertyMasterRepository.delete(propertyId);

      console.log(`Property ${propertyId} backed up and deleted successfully.`);
      return {
        message: `Deleted Property with propertyId : ${propertyId}`,
        data: deletedProperty,
      };
    } catch (error) {
      throw new Error(`Error in backupAndDeleteProperty: ${error.message}`);
    }
  }

  async backupProperty(propertyId: string): Promise<void> {}

  async remove(propertyMaster: PropertyMasterIdDto) {
    // try {
    //   const { property_master_id } = propertyMaster;
    //   const checkData =
    //     await this.propertyMasterRepository.getData(property_master_id);
    //   if (!checkData) {
    //     throw new NotFoundException('Data Not found');
    //   }
    //   const deleteData =
    //     await this.propertyMasterRepository.deleteData(property_master_id);
    //   if (deleteData.affected === 0) {
    //     throw new NotAcceptableException('Failed to delete data');
    //   }
    //   return {
    //     message: 'data Delete Successfully',
    //     data: deleteData,
    //   };
    // } catch (error) {
    //   throw error;
    // }
  }

  async searchProperty(searchPropertyDto: SearchPropertyDto) {
    try {
      // const { search } = searchPropertyDto;
      if (searchPropertyDto.search) {
        return await this.filterBysearch(searchPropertyDto);
      }

      const { ward, zone, fullName, ...paginatedOptions } = searchPropertyDto;
      const [firstName, ...lastNameParts] = fullName.split(' ');
      const lastName = lastNameParts.join(' ');

      //Check Ward

      const checkWard = await this.wardRepository.findWardById(ward);
      if (!checkWard) {
        throw new NotFoundException('Ward Not Found');
      }

      const checkZone = await this.zoneRepository.findById(zone);
      if (!checkZone) {
        throw new NotFoundException('Zone Not Found');
      }

      //Search

      const getData = await this.propertyMasterRepository.findByName(
        {
          ward,
          zone,
          firstName,
          lastName,
        },
        paginatedOptions,
      );

      // Logger.log('getData', JSON.stringify(getData));

      if (!getData || getData.total === 0) {
        throw new NotFoundException('Records Not Found');
      }

      Logger.log(
        `firstName: ${firstName} lastName: ${lastName} ward: ${ward} zone: ${zone}`,
      );

      return {
        message: 'Records Fetched ',
        data: getData,
      };
    } catch (error) {
      throw error;
    }
  }

  private async filterBysearch(searchPropertyDto: SearchPropertyDto) {
    const getData =
      await this.propertyMasterRepository.getSearch(searchPropertyDto);

    if (!getData || getData.total === 0) {
      throw new NotFoundException('Data Not Found');
    }
    return {
      message: 'Success',
      data: getData,
    };
  }

  async processCSVData(file: Express.Multer.File) {
    //console.log("filename");
    try {
      // const fetchData = await parseCsv(file);
      let rowNum = 0;

      // for (const dataObj of fetchData) {
      //   // console.log('sr_no', dataObj['sr_no']);
      //   // console.log('name', dataObj['name']);
      //   rowNum++;
      // }
      // return {
      //   message: 'Success',
      //   data: {Number_of_rows:rowNum},

      // };

      return {
        message: 'Success',
        data: {},
      };
    } catch (error) {
      console.log('error', error);
      throw error;
    }
  }

  async validateCsvData(file): Promise<any> {
    const csvContent = file.buffer;
    const parsedData: any = await new Promise((resolve, reject) => {
      csv.parse(
        csvContent,
        {
          columns: true,
          relax_quotes: true,
          skip_empty_lines: true,
          cast: true,
        },
        (err, records) => {
          if (err) {
            reject(err);
            return { error: true, message: 'Unable to parse file' };
          }
          resolve(records);
        },
      );
    });
    const errors: string[] = [];
    if (!parsedData.length) {
      errors.push('Empty File Provided');
      return {
        error: true,
        message: 'File Validation Failed',
        errorsArray: errors,
      };
    }
    //validate All Rows
    for await (const [index, rowData] of parsedData.entries()) {
      const validationErrors = await this.validateFileRow(rowData);
      if (validationErrors.length) {
        return {
          error: true,
          message: `File Rows Validation Failed at row: ${index + 1}`,
          errorsArray: validationErrors,
        };
      }
    }
    return { error: false };
  }

  async validateFileRow(rowData) {
    // console.log("---------------------------------------------------------------------rowData",rowData);
    const errors: string[] = [];
    const csvDto = plainToInstance(CSVDTO, rowData);
    //console.log("---------------------------------------------------------------------csvDto",csvDto);
    const validationErrors = await validate(csvDto);
    if (validationErrors.length > 0) {
      validationErrors.forEach((error) => {
        const { property, constraints } = error;
        const errorMessage = `${property}: ${Object.values(constraints).join(', ')}`;
        errors.push(errorMessage);
        console.log(
          '-----------------------------------------------------------------------errors',
          errors,
        );
      });
    }
    return errors;
  }

  // async processFile(file){
  //   //return {message: "file validation successfully done. you can do your processing with your validated file data"}
  //   //validate All Rows
  //   const csvContent = file.buffer
  //   const parsedData: any = await new Promise((resolve, reject) => {
  //     csv.parse(csvContent, { columns: true, relax_quotes: true, skip_empty_lines: true, cast: true }, (err, records) => {
  //       if (err) {
  //         reject(err);
  //         return { error: true, message: "Unable to parse file" }
  //       }
  //       resolve(records);
  //     });
  //   });
  //   const errors: string[] = [];
  //   if (!parsedData.length) {
  //     errors.push('Empty File Provided')
  //     return { error: true, message: "File Validation Failed", errorsArray: errors }
  //   }
  //   for await (const [index, rowData] of parsedData.entries()) {
  //     // const validationErrors = await this.validateFileRow(rowData)
  //     // if (validationErrors.length) {
  //     //   return { error: true, message: `File Rows Validation Failed at row: ${index + 1}`, errorsArray: validationErrors }
  //     // }

  //     console.log("row",rowData);
  //   }
  // }

  async processFile(file) {
    const workbook = XLSX.read(file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const parsedData = XLSX.utils.sheet_to_json(worksheet, { defval: '' });

    const errors: string[] = [];
    if (!parsedData.length) {
      errors.push('Empty File Provided');
      return {
        error: true,
        message: 'File Validation Failed',
        errorsArray: errors,
      };
    }

    for await (const [index, rowData] of parsedData.entries()) {
      console.log('row', rowData);
      await this.savePropertyData_(rowData);
    }

    return { message: 'File processed successfully', data: parsedData };
  }

  async exportPropertyDataToExcel(financial_year?: string): Promise<Buffer> {
    try {
      // Get property data with related information
      const properties =
        await this.propertyMasterRepository.getPropertyDataForExport(
          financial_year,
        );

      if (!properties || properties.length === 0) {
        throw new NotFoundException('No property data found for export');
      }
      // console.log("propertites",properties[10].property_usage_details);

      // Group properties by ward
      const wardWiseData = new Map<string, any[]>();

      for (const property of properties) {
        const wardName = property.ward?.ward_name || 'Unknown Ward';

        if (!wardWiseData.has(wardName)) {
          wardWiseData.set(wardName, []);
        }

        // Get the first owner name (primary owner)
        const ownerName =
          property.property_owner_details &&
          property.property_owner_details.length > 0
            ? property.property_owner_details[0].name
            : '';

        // Get the latest warshik kar data
        const warshikKar =
          property.warshikKar && property.warshikKar.length > 0
            ? property.warshikKar[0]
            : null;
        // Check if property has usage details
        if (
          property.property_usage_details &&
          property.property_usage_details.length > 0
        ) {
          // Create a row for each usage detail
          property.property_usage_details.forEach((usageDetail, index) => {
            wardWiseData.get(wardName).push({
              // Show property details only for the first usage detail (index 0)
              property_number: index === 0 ? property.propertyNumber || '' : '',
              old_property_number:
                index === 0 ? property.old_propertyNumber || '' : '',
              owner_name: index === 0 ? ownerName : '',
              ward: index === 0 ? wardName : '',
              zone: index === 0 ? property.zone?.zoneName || '' : '',
              total_tax:
                index === 0
                  ? warshikKar?.total_tax || warshikKar?.total_tax_current || 0
                  : '',
              // Include tax types 1 to 9
              tax_type_1: index === 0 ? warshikKar?.tax_type_1 || 0 : '',
              tax_type_2: index === 0 ? warshikKar?.tax_type_2 || 0 : '',
              tax_type_3: index === 0 ? warshikKar?.tax_type_3 || 0 : '',
              tax_type_4: index === 0 ? warshikKar?.tax_type_4 || 0 : '',
              tax_type_5: index === 0 ? warshikKar?.tax_type_5 || 0 : '',
              tax_type_6: index === 0 ? warshikKar?.tax_type_6 || 0 : '',
              tax_type_7: index === 0 ? warshikKar?.tax_type_7 || 0 : '',
              tax_type_8: index === 0 ? warshikKar?.tax_type_8 || 0 : '',
              tax_type_9: index === 0 ? warshikKar?.tax_type_9 || 0 : '',
              all_property_tax_sum_total:
                index === 0 ? warshikKar?.all_property_tax_sum_total || 0 : '',
              // Always show usage-specific details
              length: usageDetail?.length || 0,
              breadth: usageDetail?.width || 0,
              area_sq_ft: usageDetail?.are_sq_ft || 0,
              area_sq_meter: usageDetail?.are_sq_meter || 0,
              usage_detail_number: index + 1, // To identify which usage detail this is
              property_type: usageDetail?.propertyType?.propertyType || '',
              usage_type: usageDetail?.usageType?.usage_type || '',
              floor: usageDetail?.floorType?.floor_name || '',
              construction_area: usageDetail?.construction_area || 0,
              // Store property ID for grouping purposes
              property_id: property.property_id,
              is_first_usage: index === 0, // Flag to identify first usage detail
            });
          });
        } else {
          // If no usage details, create one row with empty usage data
          wardWiseData.get(wardName).push({
            property_number: property.propertyNumber || '',
            old_property_number: property.old_propertyNumber || '',
            owner_name: ownerName,
            ward: wardName,
            zone: property.zone?.zoneName || '',
            total_tax:
              warshikKar?.total_tax || warshikKar?.total_tax_current || 0,
            length: 0,
            breadth: 0,
            area_sq_ft: 0,
            area_sq_meter: 0,
            usage_detail_number: 0,
            property_type: '',
            usage_type: '',
            floor: '',
            construction_area: 0,
            property_id: property.property_id,
            is_first_usage: true,
          });
        }
      }

      // Create Excel workbook
      const workbook = new ExcelJS.Workbook();

      // Sort wards alphabetically
      const sortedWards = Array.from(wardWiseData.keys()).sort();

      // Create a worksheet for each ward
      for (const wardName of sortedWards) {
        const wardData = wardWiseData.get(wardName);

        // Create worksheet with ward name (sanitize for Excel sheet name)
        const sanitizedWardName = wardName
          .replace(/[\\\/\?\*\[\]]/g, '_')
          .substring(0, 31);
        const worksheet = workbook.addWorksheet(sanitizedWardName);

        // Define columns
        // Define columns
        worksheet.columns = [
          { header: 'अ.क्र. (Sr No)', key: 'sr_no', width: 10 },
          {
            header: 'मालमत्ता क्रमांक (Property Number)',
            key: 'property_number',
            width: 20,
          },
          {
            header: 'जुना मालमत्ता क्रमांक (Old Property Number)',
            key: 'old_property_number',
            width: 25,
          },
          { header: 'मालकाचे नाव (Owner Name)', key: 'owner_name', width: 30 },
          { header: 'प्रभाग (Ward)', key: 'ward', width: 15 },
          { header: 'क्षेत्र (Zone)', key: 'zone', width: 15 },
          { header: 'एकूण कर (Total Tax)', key: 'total_tax', width: 15 },
          { header: 'वृक्ष उपकर', key: 'tax_type_1', width: 15 },
          { header: 'शिक्षण उपकर', key: 'tax_type_2', width: 15 },
          { header: 'रोजगार हमी कर', key: 'tax_type_3', width: 20 },
          { header: 'घनकचरा शुल्क', key: 'tax_type_4', width: 15 },
          { header: 'अनधिकृत शास्ती कर', key: 'tax_type_5', width: 20 },
          { header: 'दिवाबत्ती कर', key: 'tax_type_6', width: 15 },
          { header: 'आरोग्य कर', key: 'tax_type_7', width: 15 },
          { header: 'पडसर कर', key: 'tax_type_8', width: 15 },
          { header: 'दंड', key: 'tax_type_9', width: 15 },
          { header: 'इमारत कर', key: 'all_property_tax_sum_total', width: 15 },
          {
            header: 'वापर तपशील क्र. (Usage Detail No)',
            key: 'usage_detail_number',
            width: 20,
          },
          {
            header: 'मालमत्ता प्रकार (Property Type)',
            key: 'property_type',
            width: 20,
          },
          { header: 'वापर प्रकार (Usage Type)', key: 'usage_type', width: 20 },
          { header: 'मजला (Floor)', key: 'floor', width: 15 },
          {
            header: 'बांधकाम क्षेत्र (Construction Area)',
            key: 'construction_area',
            width: 20,
          },
          { header: 'लांबी (Length)', key: 'length', width: 15 },
          { header: 'रुंदी (Breadth)', key: 'breadth', width: 15 },
          {
            header: 'क्षेत्रफळ (चौ.फूट) (Area Sq Ft)',
            key: 'area_sq_ft',
            width: 20,
          },
          {
            header: 'क्षेत्रफळ (चौ.मीटर) (Area Sq Meter)',
            key: 'area_sq_meter',
            width: 20,
          },
        ];

        // Add serial numbers to ward data - group by property and assign same serial number
        let currentSerialNumber = 1;
        let lastPropertyId = null;

        const dataWithSerialNumbers = wardData.map((item) => {
          // If this is a new property (first usage detail), increment serial number
          if (item.is_first_usage) {
            if (lastPropertyId !== null) {
              currentSerialNumber++;
            }
            lastPropertyId = item.property_id;
          }

          return {
            ...item,
            sr_no: currentSerialNumber,
          };
        });

        // Add data rows
        worksheet.addRows(dataWithSerialNumbers);

        // Style the header row
        worksheet.getRow(1).eachCell((cell) => {
          cell.font = { bold: true, color: { argb: 'FFFFFF' } };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '366092' },
          };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        });

        // Style data rows
        worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
          if (rowNumber > 1) {
            row.eachCell((cell) => {
              cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
              };
              cell.alignment = { horizontal: 'center', vertical: 'middle' };
            });

            // Alternate row coloring
            if (rowNumber % 2 === 0) {
              row.eachCell((cell) => {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'F2F2F2' },
                };
              });
            }
          }
        });

        // Add summary row at the end
        const summaryRowNumber = wardData.length + 2;
        const summaryRow = worksheet.getRow(summaryRowNumber);
        summaryRow.getCell(1).value = 'एकूण (Total)';
        // Calculate total tax only from first usage details (unique properties)
        const uniquePropertiesInWard = wardData.filter(
          (item) => item.is_first_usage,
        );
        summaryRow.getCell(7).value = uniquePropertiesInWard.reduce(
          (sum, item) => sum + (item.total_tax || 0),
          0,
        );

        // Style summary row
        summaryRow.eachCell((cell, colNumber) => {
          cell.font = { bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFF99' },
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      }

      // Create a summary worksheet
      const summaryWorksheet = workbook.addWorksheet('Summary');
      summaryWorksheet.columns = [
        { header: 'प्रभाग (Ward)', key: 'ward_name', width: 25 },
        {
          header: 'मालमत्ता संख्या (Property Count)',
          key: 'property_count',
          width: 20,
        },
        { header: 'एकूण कर (Total Tax)', key: 'total_tax', width: 20 },
      ];

      const summaryData = [];
      let grandTotalProperties = 0;
      let grandTotalTax = 0;

      for (const [wardName, wardData] of wardWiseData) {
        // Count unique properties (only first usage details)
        const uniqueProperties = wardData.filter((item) => item.is_first_usage);
        const propertyCount = uniqueProperties.length;
        const totalTax = uniqueProperties.reduce(
          (sum, item) => sum + (item.total_tax || 0),
          0,
        );

        summaryData.push({
          ward_name: wardName,
          property_count: propertyCount,
          total_tax: totalTax,
        });
        grandTotalProperties += propertyCount;
        grandTotalTax += totalTax;
      }

      summaryWorksheet.addRows(summaryData);

      // Add grand total row
      const grandTotalRow = summaryWorksheet.getRow(summaryData.length + 2);
      grandTotalRow.getCell(1).value = 'महाएकूण (Grand Total)';
      grandTotalRow.getCell(2).value = grandTotalProperties;
      grandTotalRow.getCell(3).value = grandTotalTax;

      // Style summary worksheet
      summaryWorksheet.getRow(1).eachCell((cell) => {
        cell.font = { bold: true, color: { argb: 'FFFFFF' } };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '366092' },
        };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });

      // Style summary data rows
      summaryWorksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
        if (rowNumber > 1) {
          row.eachCell((cell) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
          });

          // Style grand total row
          if (rowNumber === summaryData.length + 2) {
            row.eachCell((cell) => {
              cell.font = { bold: true };
              cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFD700' },
              };
            });
          }
        }
      });

      // Generate Excel buffer
      const buffer = await workbook.xlsx.writeBuffer();
      return buffer as Buffer;
    } catch (error) {
      throw error;
    }
  }

  async savePropertyData_(row: any) {
    try {
      let { propertyNumber } = row;
      console.log(
        '-------------------------------------propertyNumber',
        propertyNumber,
      );
      let basePropertyNumber = '';
      if (propertyNumber) {
        const match = propertyNumber.match(/^(SNP\d+)/);
        if (match) {
          basePropertyNumber = match[1];
        } else {
          // Handle case where propertyNumber does not match expected pattern
          throw new Error('Invalid property number format');
        }
      } else {
      }
      console.log(
        '--------------------------------------basePropertyNumber',
        basePropertyNumber,
      );

      // Check if the base property number already exists
      const existingProperty = await this.propertyMasterRepository.findOne({
        where: { propertyNumber: basePropertyNumber },
      });
      if (existingProperty) {
        // return {
        //   message: `Property number ${basePropertyNumber} already exists. Record not saved.`,
        // };
        console.log('property number already exists', basePropertyNumber);
      } else {
        // let basePropertyNumber;
        const saveData = await this.propertyMasterRepository.saveData(row);
        return {
          message: 'Data Saved SuccessFully',
        };
      }
    } catch (error) {
      throw error;
    }
  }

  async savePropertyData(row: any) {
    try {
      let { propertyNumber } = row;
      let basePropertyNumber;

      console.log(
        '-----------------------------------------propertyNumber',
        propertyNumber,
      );
      // Extract base property number
      // if (propertyNumber) {
      //   const match = propertyNumber.match(/^(SNP\d+)/);
      //   if (match) {
      //     basePropertyNumber = match[1];
      //   } else {
      //     // Handle case where propertyNumber does not match expected pattern
      //     throw new Error('Invalid property number format');
      //   }
      // } else {
      //   basePropertyNumber = await this.authHelper.generateRandomNumberWithPrefix('SN');
      //   row.propertyNumber = basePropertyNumber;
      // }

      // // Check if the base property number already exists
      // const existingProperty = await this.propertyMasterRepository.findOne({ where: { propertyNumber: basePropertyNumber } });
      // if (existingProperty) {
      //   return {
      //     message: `Property number ${basePropertyNumber} already exists. Record not saved.`,
      //   };
      // }

      // // Save the record if base property number does not exist
      // const saveData = await this.propertyMasterRepository.saveData(row);
      return {
        message: 'Data Saved Successfully',
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update length and width in all MilkatKar tax records from property usage details
   */
  async updateAllMilkatKarLengthWidth() {
    const logger = new Logger('PropertyMasterService');
    logger.log(
      'Starting update of length and width for all MilkatKar tax records',
    );

    try {
      // Get all MilkatKar tax records with property usage details
      const milkatKarTaxRecords = await this.milkatKarTaxRepository
        .createQueryBuilder('milkatKarTax')
        .leftJoinAndSelect(
          'milkatKarTax.property_usage_details',
          'propertyUsage',
        )
        .leftJoinAndSelect('milkatKarTax.property', 'property')
        .getMany();

      if (milkatKarTaxRecords.length === 0) {
        return {
          success: true,
          message: 'No MilkatKar tax records found',
          data: {
            totalRecordsFound: 0,
            recordsUpdated: 0,
            recordsSkipped: 0,
            errors: [],
          },
        };
      }

      logger.log(
        `Found ${milkatKarTaxRecords.length} MilkatKar tax records to process`,
      );

      const updateResults = {
        totalRecordsFound: milkatKarTaxRecords.length,
        recordsUpdated: 0,
        recordsSkipped: 0,
        errors: [],
      };

      // Process each record
      for (const taxRecord of milkatKarTaxRecords) {
        try {
          // Skip if no property usage details
          if (!taxRecord.property_usage_details) {
            updateResults.recordsSkipped++;
            continue;
          }

          const propertyUsage = taxRecord.property_usage_details;
          const currentLength = taxRecord.length || 0;
          const currentWidth = taxRecord.width || 0;
          const newLength = propertyUsage.length || 0;
          const newWidth = propertyUsage.width || 0;

          // Skip if values are already the same
          if (currentLength === newLength && currentWidth === newWidth) {
            updateResults.recordsSkipped++;
            continue;
          }

          // Update the record
          await this.milkatKarTaxRepository.update(
            { milkatKartax_id: taxRecord.milkatKartax_id },
            {
              length: newLength,
              width: newWidth,
              updatedAt: new Date(),
            },
          );

          updateResults.recordsUpdated++;
        } catch (error) {
          updateResults.errors.push({
            milkat_kar_tax_id: taxRecord.milkatKartax_id,
            property_number: taxRecord.property?.propertyNumber || 'Unknown',
            error: error.message,
          });
        }
      }

      logger.log(
        `Update completed. Total: ${updateResults.totalRecordsFound}, Updated: ${updateResults.recordsUpdated}, Skipped: ${updateResults.recordsSkipped}, Errors: ${updateResults.errors.length}`,
      );

      return {
        success: true,
        message: 'MilkatKar tax length and width update completed',
        data: updateResults,
      };
    } catch (error) {
      logger.error(
        `Error in updateAllMilkatKarLengthWidth: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update tax payer name in all payment_info records from property owner details
   */
  async updateAllPaymentInfoTaxPayerName() {
    const logger = new Logger('PropertyMasterService');
    logger.log('Starting update of tax payer names in payment_info records');

    const TARGET_OWNER_TYPE_ID = '98e191a7-4254-49bc-86d9-c746b61ddc7a';
    try {
      // Get all payment_info records with property and owner details
      const paymentInfoRecords = await this.paymentInfoRepository
        .createQueryBuilder('payment_info')
        .leftJoinAndSelect('payment_info.property', 'property')
        .leftJoinAndSelect('property.property_owner_details', 'ownerDetails')
        .leftJoinAndSelect('ownerDetails.owner_type', 'ownerType')
        .getMany();

      if (paymentInfoRecords.length === 0) {
        return {
          success: true,
          message: 'No payment_info records found',
          data: {
            totalRecordsFound: 0,
            recordsUpdated: 0,
            recordsSkipped: 0,
            errors: [],
          },
        };
      }

      logger.log(
        `Found ${paymentInfoRecords.length} payment_info records to process`,
      );

      const updateResults = {
        totalRecordsFound: paymentInfoRecords.length,
        recordsUpdated: 0,
        recordsSkipped: 0,
        errors: [],
      };
      console.log(
        'paymentInfoRecords',
        paymentInfoRecords[0].property.property_owner_details,
      );
      // Process each payment record
      for (const paymentRecord of paymentInfoRecords) {
        try {
          // Skip if no property or owner details
          if (
            !paymentRecord.property ||
            !paymentRecord.property.property_owner_details ||
            paymentRecord.property.property_owner_details.length === 0
          ) {
            updateResults.recordsSkipped++;
            continue;
          }

          const ownerDetails = paymentRecord.property.property_owner_details;
          let selectedOwner = null;

          // First priority: Find owner with specific owner_type_id
          selectedOwner = ownerDetails.find(
            (owner) =>
              owner.owner_type &&
              owner.owner_type.owner_type_id === TARGET_OWNER_TYPE_ID,
          );

          // Second priority: Take the first available owner if target not found
          if (!selectedOwner) {
            selectedOwner = ownerDetails[0];
          }

          if (!selectedOwner || !selectedOwner.name) {
            updateResults.recordsSkipped++;
            continue;
          }

          const currentTaxPayerName = paymentRecord.tax_payer_name;
          const newTaxPayerName = selectedOwner.name;

          // Skip if tax payer name is already the same
          if (currentTaxPayerName === newTaxPayerName) {
            updateResults.recordsSkipped++;
            continue;
          }

          // Update the payment record
          await this.paymentInfoRepository.update(
            { payment_id: paymentRecord.payment_id },
            {
              tax_payer_name: newTaxPayerName,
              updatedAt: new Date(),
            },
          );

          updateResults.recordsUpdated++;
        } catch (error) {
          updateResults.errors.push({
            payment_id: paymentRecord.payment_id,
            property_number:
              paymentRecord.property?.propertyNumber || 'Unknown',
            error: error.message,
          });
        }
      }

      logger.log(
        `Update completed. Total: ${updateResults.totalRecordsFound}, Updated: ${updateResults.recordsUpdated}, Skipped: ${updateResults.recordsSkipped}, Errors: ${updateResults.errors.length}`,
      );

      return {
        success: true,
        message: 'Payment info tax payer name update completed',
        data: updateResults,
      };
    } catch (error) {
      logger.error(
        `Error in updateAllPaymentInfoTaxPayerName: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
  async updateImaratKarDiscount(propertyId: string, discountPercentage: number) {
    const property = await this.propertyMasterRepository.findOne({
      where: { property_id: propertyId },
      relations: ['commonFields'],
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    if (!property.commonFields) {
      throw new NotFoundException('Common fields not found for this property');
    }

    // property.commonFields.is_imarat_kar_full_discount = discountPercentage;
    await this.CommonFiledsOfPropertyRepository.save(property.commonFields);

    return { message: 'Imarat Kar discount updated successfully' };
  }

  async updateIsPaymentPayerName() {
    const logger = new Logger('PropertyMasterService');
    logger.log('Starting update of tax payer names in payment_info records');

    const TARGET_OWNER_TYPE_ID = '98e191a7-4254-49bc-86d9-c746b61ddc7a';

    try {
      const paymentInfoRecords = await this.propertyMasterRepository.find({
        relations: [
          'property_owner_details',
          'property_owner_details.owner_type',
        ],
      });

      if (paymentInfoRecords.length === 0) {
        return {
          success: true,
          message: 'No property records found',
          data: {
            totalRecordsFound: 0,
            recordsUpdated: 0,
            recordsSkipped: 0,
            errors: [],
          },
        };
      }

      logger.log(
        `Found ${paymentInfoRecords.length} payment_info records to process`,
      );

      const updateResults = {
        totalRecordsFound: paymentInfoRecords.length,
        recordsUpdated: 0,
        recordsSkipped: 0,
        errors: [],
      };
      console.log(
        'paymentInfoRecords',
        paymentInfoRecords[0].property_owner_details,
      );

      for (const paymentRecord of paymentInfoRecords) {
        try {
          if (
            !paymentRecord ||
            !paymentRecord.property_owner_details ||
            paymentRecord.property_owner_details.length === 0
          ) {
            updateResults.recordsSkipped++;
            continue;
          }

          const ownerDetails = paymentRecord.property_owner_details;
          let selectedOwner = ownerDetails.find(
            (owner) =>
              owner.owner_type &&
              owner.owner_type.owner_type_id === TARGET_OWNER_TYPE_ID,
          );

          if (!selectedOwner) {
            selectedOwner = ownerDetails[0];
          }

          if (!selectedOwner || !selectedOwner.name) {
            updateResults.recordsSkipped++;
            continue;
          }

          await this.property_Owner_DetailsRepository.update(
            {
              property_owner_details_id:
                selectedOwner.property_owner_details_id,
            },
            {
              is_payer: true,
              updatedAt: new Date(),
            },
          );

          updateResults.recordsUpdated++;
        } catch (error) {
          updateResults.errors.push({
            recordId: paymentRecord?.property_id || 'unknown',
            error: error.message,
          });
        }
      }

      logger.log(
        `Update completed. Total: ${updateResults.totalRecordsFound}, Updated: ${updateResults.recordsUpdated}, Skipped: ${updateResults.recordsSkipped}, Errors: ${updateResults.errors.length}`,
      );

      return {
        success: true,
        message: 'Payment info tax payer name update completed',
        data: updateResults,
      };
    } catch (error) {
      logger.error(
        `Error in updateIsPaymentPayerName: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
