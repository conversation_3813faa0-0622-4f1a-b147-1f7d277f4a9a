import { PaginationDto } from '@helper/helpers/Pagination';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class SearchPropertyDto extends PaginationDto {
  @ApiProperty({ name: 'search', type: 'string' })
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsUUID()
  ward: string;

  @IsOptional()
  @IsUUID()
  zone: string;

  @IsOptional()
  @IsString()
  fullName: string;
}
