
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from '@nestjs/common';
import { RegisterNumberEntity } from '../entities';

export class RegisterNumberRepository extends Repository<RegisterNumberEntity> {
  constructor(
    @InjectRepository(RegisterNumberEntity)
    private readonly registerNumberRepository: Repository<RegisterNumberEntity>,
  ) {
    super(
      registerNumberRepository.target,
      registerNumberRepository.manager,
      registerNumberRepository.queryRunner,
    );
  }

  async saveRegisterNumber(input: { register_name: string }): Promise<RegisterNumberEntity> {
    let registerNumber = await this.registerNumberRepository.create(input);
    registerNumber = await this.registerNumberRepository.save(registerNumber);
    return registerNumber;
  }

  async findAllRegisterNumber() {
    return await this.registerNumberRepository
      .createQueryBuilder('register-number')
      .orderBy('register-number.updated_at', 'DESC')
      .getMany();
  }

  async findRegisterNumberById(id: string) {
    return await this.registerNumberRepository
      .createQueryBuilder('register-number')
      .where('register-number.register_id = :register_id', { register_id: id })
      .getOne();
  }

  async updateRegisterNumber(id: string, input: { register_name?: string }) {
    return await this.registerNumberRepository
      .createQueryBuilder('register-number')
      .update(RegisterNumberEntity)
      .set(input)
      .where('register_id = :register_id', { register_id: id })
      .execute();
  }

  async deleteRegisterNumber(id: string) {
    return await this.registerNumberRepository
      .createQueryBuilder('register-number')
      .softDelete()
      .where('register_id = :register_id', { register_id: id })
      .execute();
  }
}
