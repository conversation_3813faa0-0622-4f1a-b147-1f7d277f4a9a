import { Repository } from 'typeorm';
import { FormMasterEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class FormMasterRepository extends Repository<FormMasterEntity> {
  constructor(
    @InjectRepository(FormMasterEntity)
    private readonly formMasterRepository: Repository<FormMasterEntity>,
  ) {
    super(
      formMasterRepository.target,
      formMasterRepository.manager,
      formMasterRepository.queryRunner,
    );
  }

  async saveData(input: { formName: string }): Promise<FormMasterEntity> {
    let data = this.formMasterRepository.create(input);
    data = await this.formMasterRepository.save(data);
    return data;
  }

  async findAllData(): Promise<FormMasterEntity[]> {
    const formData = await this.formMasterRepository
      .createQueryBuilder('form_master')
      .leftJoinAndSelect('form_master.module', 'module') // Corrected property path
      .orderBy('form_master.updated_at', 'ASC')
      .getMany();

    const groupedData = formData.reduce((acc: any, curr: any) => {
      const { module } = curr;
      const key = `${module.module_id}-${module.moduleName}`;
      if (!acc[key]) {
        acc[key] = {
          module_id: module.module_id,
          moduleName: module.moduleName,
          forms: [],
        };
      }
      acc[key].forms.push({
        form_id: curr.form_id,
        formName: curr.formName,
        createdAt: curr.createdAt,
        updatedAt: curr.updatedAt,
        deletedAt: curr.deletedAt,
      });
      return acc;
    }, {});

    const result: FormMasterEntity[] = Object.values(groupedData);

    return result;
  }

  async findById(id: number): Promise<FormMasterEntity> {
    return await this.formMasterRepository
      .createQueryBuilder('form_master')
      .where('form_master.form_id = :form_id', {
        form_id: id,
      })
      .getOne();
  }

  async updateData(id: number, input: { formName?: string }) {
    return await this.formMasterRepository
      .createQueryBuilder('form_master')
      .update(FormMasterEntity)
      .set(input)
      .where('form_id = :form_id', {
        form_id: id,
      })
      .execute();
  }

  async deleteData(id: number) {
    return await this.formMasterRepository
      .createQueryBuilder('form_master')
      .softDelete()
      .where('form_id = :form_id', {
        form_id: id,
      })
      .execute();
  }
}
