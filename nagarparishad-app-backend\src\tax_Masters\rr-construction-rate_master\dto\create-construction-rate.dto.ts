// src/construction-rate-master/dto/create-construction-rate.dto.ts
import { IsString, IsNumber, IsUUID, IsNotEmpty, IsIn, IsOptional } from 'class-validator';

export class CreateConstructionRateDto {
  @IsUUID()
  @IsNotEmpty()
  property_type_class_id: string; // Required property type class ID

  @IsString()
  @IsOptional()
  financial_year?: string; // Optional for backward compatibility

  @IsString()
  @IsOptional()
  reassessment_range_id?: string; // Reassessment range ID

  @IsNumber()
  @IsNotEmpty()
  value: number; // Required rate value

  @IsString()
  @IsIn(['Active', 'Inactive'])
  status: string; // Status must be either 'Active' or 'Inactive'
}
