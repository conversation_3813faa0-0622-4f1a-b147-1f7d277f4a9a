import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useTranslation } from "react-i18next";
import publish from "../../../assets/img/publish.png";
import { useTaxController } from "@/controller/tax/TaxListController";

export interface PublishPopScreenProps {
  isOpen: boolean;
  toggle: () => void;
  itemName: any;
  onSubmit: () => void;
}

const PublisPopUpScreen: React.FC<PublishPopScreenProps> = ({
  isOpen,
  toggle,
  itemName,
  onSubmit,
}) => {
  const { t } = useTranslation();

  const { changePublishStatus } = useTaxController()

  const handleClose = () => {
    console.log("close call");
    toggle();
  };

  const handleSubmit = () => {
    onSubmit(); 
    handleClose(); 
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px] border-2 bg-white">
        <DialogHeader>
          <DialogTitle className="leading-6">
            {itemName  ? t("unpublish") : t("publish")}
          </DialogTitle>
          <DialogDescription className="flex justify-center !my-3">
            {/* <img src={publish} className="w-[50px] h-[50px]" /> */}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className=" !justify-center">
          <Button onClick={handleClose}>{t("deletescreen.no")}</Button>
          <Button onClick={handleSubmit}>{t("deletescreen.yes")}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PublisPopUpScreen;
