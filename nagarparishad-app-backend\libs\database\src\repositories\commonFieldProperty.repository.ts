import { Repository } from 'typeorm';
import { CommonFiledsOfPropertyEntity } from '../entities'; // Adjust the import path as necessary
import { InjectRepository } from '@nestjs/typeorm';

export class CommonFiledsOfPropertyRepository extends Repository<CommonFiledsOfPropertyEntity> {
  constructor(
    @InjectRepository(CommonFiledsOfPropertyEntity)
    private readonly commonFiledsOfPropertyRepository: Repository<CommonFiledsOfPropertyEntity>,
  ) {
    super(
      commonFiledsOfPropertyRepository.target,
      commonFiledsOfPropertyRepository.manager,
      commonFiledsOfPropertyRepository.queryRunner,
    );
  }

  // You can add custom methods for this repository here if needed
}
