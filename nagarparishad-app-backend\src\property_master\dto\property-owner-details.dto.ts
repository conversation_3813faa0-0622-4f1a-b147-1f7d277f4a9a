import { IsString, IsOptional, IsBoolean, ValidateIf, IsEnum, IS_BOOLEAN ,IsUUID,} from 'class-validator';
import { Gender } from 'libs/database/entities';


export class PropertyOwnerDetailsDto {
  @IsString()
  @IsOptional()
  owner_type_id: string;

  @IsString()
  @IsOptional()
  owner_type: string;

  @IsString()
  @IsOptional()
  name: string;

  @IsString()
  @IsOptional()
  mobile_number: string;

  @IsString()
  @IsOptional()
  email_id: string;

  @IsString()
  @IsOptional()
  aadhar_number: string;

  @IsString()
  @IsOptional()
  pan_card: string;

  @IsEnum(['MALE','FEMALE','OTHER'])
  @IsOptional()
  gender: 'MALE'|'FEMALE'|'OTHER' | null; 

  @IsEnum(['yes','no','divorced'])
  @IsOptional()
  marital_status: 'yes' | 'no' | 'divorced';

  @ValidateIf((o) => o.marital_status === true) 
  @IsString()
  @IsOptional()
  partner_name: string; 

  @IsString()
  @IsOptional()
  remark: string;

  @IsBoolean()
  @IsOptional()
  is_owner: boolean;
}

// DTO for adding or updating owner details
export class UpdateOwnerDetailsDto {
  @IsUUID()
  @IsOptional()
  owner_type_id?: string;

  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  mobile_number?: string;

  @IsString()
  @IsOptional()
  email_id?: string;

  @IsString()
  @IsOptional()
  aadhar_number?: string;

  @IsString()
  @IsOptional()
  pan_card?: string;

  @IsString()
  @IsOptional()
  gender?: Gender;

  @IsString()
  @IsOptional()
  marital_status?: MaritalStatus;

  @IsString()
  @IsOptional()
  partner_name?: string;
  
  @IsString()
  @IsOptional()
  remark?: string;

  @IsBoolean()
  @IsOptional()
  is_owner?: boolean;
}

// DTO for change ownership flag
export class ChangeOwnershipDto {
  @IsString()
  @IsOptional()
  property_owner_details_id: string;

  @IsBoolean()
  @IsOptional()
  is_owner: boolean;
}

// DTO for delete owner (soft delete)
export class DeleteOwnerDto {
  property_owner_details_id: string;
}


export enum MaritalStatus {
  YES = 'yes',
  NO = 'no',
  DIVORCED = 'divorced',
}