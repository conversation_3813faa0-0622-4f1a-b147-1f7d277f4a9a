import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import Api from "../../services/ApiServices";
import { AreaListAllApi, AreaMasterObject } from "../../model/area-master";
import { GlobalContext } from "@/context/GlobalContext";
import { useContext } from "react";

export const useAreaMasterController = () => {
  const queryClient = useQueryClient();

  const fetchAreas = async () => {
    return new Promise((resolve, reject) => {
      Api.getAllArea((response) => {
        if (response.status && response.data.statusCode === 200) {
          resolve(response.data.data);
        } else {
          reject(new Error("Error fetching locations"));
        }
      });
    });
  };

  const createArea = async (areaData: any) => {
    return new Promise((resolve, reject) => {
      Api.createArea(areaData, (response) => {
        if (response.status && response.data.statusCode === 201) {
          resolve(response.data.data);
        } else {
          reject(new Error("Error fetching locations"));
        }
      });
    });
  };

  const updateArea = async ({
    areaId,
    areaData,
  }: {
    areaId: string;
    areaData: any;
  }) => {
    return new Promise((resolve, reject) => {
      Api.updateArea(areaId, areaData, (response) => {
        if (response.status && response.data.statusCode === 200) {
          resolve(response.data);
        } else {
          reject(new Error("Error fetching locations"));
        }
      });
    });
  };

  const deleteArea = async (areaId: string) => {
    return new Promise((resolve, reject) => {
      Api.deleteArea(areaId, (response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      });
    });
  };

  const {
    data: areaList,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["areamaster"],
    queryFn: fetchAreas,
    staleTime: 2 * 60 * 1000, // Cache for 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
  });

  const createAreaMutation = useMutation({
    mutationFn: createArea,
    onMutate: async (newArea) => {
      await queryClient.cancelQueries({ queryKey: ["areamaster"] });

      const previousAreas = queryClient.getQueryData(["areamaster"]);
      console.log("previousAreas", previousAreas);

      queryClient.setQueryData(["areamaster"], (old) => [newArea, ...old]);

      return { previousAreas };
    },
    onError: (err, newArea, context) => {
      queryClient.setQueryData(["areamaster"], context.previousAreas);
      console.error("Error creating area:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["areamaster"] });
    },
  });

  const updateAreaMutation = useMutation({
    mutationFn: updateArea,
    onMutate: async ({ areaId, areaData }) => {
      await queryClient.cancelQueries({ queryKey: ["areamaster"] });

      const previousAreas = queryClient.getQueryData(["areamaster"]);

      queryClient.setQueryData(["areamaster"], (old) =>
        old.map((area) =>
          area.area_id === areaId ? { ...area, ...areaData } : area,
        ),
      );

      return { previousAreas };
    },
    onError: (err, { areaId, areaData }, context) => {
      queryClient.setQueryData(["areamaster"], context.previousAreas);
      console.error("Error updating area:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["areamaster"] });
    },
  });

  const deleteAreaMutation = useMutation({
    mutationFn: deleteArea,
    onMutate: async (areaId) => {
      await queryClient.cancelQueries({ queryKey: ["areamaster"] });

      const previousAreas = queryClient.getQueryData(["areamaster"]);

      queryClient.setQueryData(["areamaster"], (old) =>
        old.filter((area) => area.area_id !== areaId),
      );
      return { previousAreas };
    },
    onError: (err, areaId, context) => {
      queryClient.setQueryData(["areamaster"], context.previousAreas);
      console.error("Error deleting area:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["areamaster"] });
    },
  });

  return {
    areaList: areaList || [],
    isLoading,
    error,
    createArea: createAreaMutation.mutate,
    updateArea: updateAreaMutation.mutate,
    deleteArea: deleteAreaMutation.mutate,
  };
};
