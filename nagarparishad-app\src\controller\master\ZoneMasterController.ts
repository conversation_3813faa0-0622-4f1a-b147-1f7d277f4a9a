import { ZoneObjectInterface } from "@/model/zone-master";
import Api from "../../services/ApiServices";
import {
  useQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";

interface ZoneData {
  zoneName: string; // Only zoneName now
}
const fetchZones = async () => {
  const response = await Api.getAllZone();
  return response.data.sort((a, b) => a.zoneName.localeCompare(b.zoneName));
};

const createZone = async (zoneData: ZoneData) => {
  return new Promise((resolve, reject) => {
    Api.createZone(zoneData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const updateZone = async ({ zoneId, zoneData }: { zoneId: string; zoneData: ZoneData }) => {
  return new Promise((resolve, reject) => {
    Api.updateZone(zoneId, zoneData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteZone = async (zoneId: string) => {
  return new Promise((resolve, reject) => {
    Api.deleteZone(zoneId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const useZoneMasterController = () => {
  const queryClient = useQueryClient();

  const { data: zoneData ,isLoading: propertyLoading } = useQuery({
    queryKey: ["zonemaster"],
    queryFn: fetchZones,
    staleTime: 10 * 60 * 2, // 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
    
  });

  // Mutation for creating a zone
  const createZoneMutation = useMutation({
    mutationFn: createZone,
    onMutate: async (newZone) => {
      await queryClient.cancelQueries({ queryKey: ["zonemaster"] });
      const previousZones = queryClient.getQueryData<ZoneObjectInterface[]>(["zonemaster"]);
      queryClient.setQueryData(["zonemaster"], (old) => {
        const updatedData = [newZone, ...(old || [])]; // Updated to use old if it exists
        return updatedData;
      });

      return { previousZones };
    },
    onError: (err, newZone, context) => {
      queryClient.setQueryData(["zonemaster"], context.previousZones);
      console.error("Error creating zone:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["zonemaster"] });
    },
  });

  // Mutation for updating a zone
  const updateZoneMutation = useMutation({
    mutationFn: updateZone,
    onMutate: async ({ zoneId, zoneData }) => {
      await queryClient.cancelQueries({ queryKey: ["zonemaster"] });

      const previousZones = queryClient.getQueryData<ZoneObjectInterface[]>(["zonemaster"]);

      queryClient.setQueryData(["zonemaster"], (old) => {
        const updatedZones = old?.map((zone) =>
          zone.zone_id === zoneId ? { ...zone, ...zoneData } : zone
        );
        return updatedZones;
      });

      return { previousZones };
    },
    onError: (err, { zoneId, zoneData }, context) => {
      queryClient.setQueryData(["zonemaster"], context.previousZones);
      console.error("Error updating zone:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["zonemaster"] });
    },
  });

  const deleteZoneMutation = useMutation({
    mutationFn: deleteZone,
    onMutate: async (zoneId) => {
      await queryClient.cancelQueries({ queryKey: ["zonemaster"] });

      const previousZones = queryClient.getQueryData<ZoneObjectInterface[]>(["zonemaster"]);

      queryClient.setQueryData(["zonemaster"], (old) => {
        const updatedZones = old?.filter((zone) => zone.zone_id !== zoneId);
        return updatedZones;
      });
      return { previousZones };
    },
    onError: (err, zoneId, context) => {
      queryClient.setQueryData(["zonemaster"], context.previousZones);
      console.error("Error deleting zone:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["zonemaster"] });
    },
  });

  return {
    zoneList: zoneData || [],
    propertyLoading,
    createZone: createZoneMutation.mutate,
    updateZone: updateZoneMutation.mutate,
    deleteZone: deleteZoneMutation.mutate,
  };
};
