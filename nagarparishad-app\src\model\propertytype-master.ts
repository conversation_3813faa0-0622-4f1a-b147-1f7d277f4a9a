export interface PropertytypeMasterObject {
  propertyType_id: string;
  propertyType: string;
  createdAt?: string;
  updatedAt?: string;
  deleteAt?: string | null;
}

export interface PropertytypeListAllApi {
  statusCode: number;
  message: string;
  data: PropertytypeMasterObject[];
}

export interface PropertyListResponse {
  status: boolean;
  data: PropertytypeListAllApi;
}

export interface PropertytypeCreateApi {
  statusCode: number;
  message: string;
  data: PropertytypeMasterObject;
}
export interface PropertytypeUpdateApi {
  statusCode: number;
  message: string;
}

export interface PropertytypeSendApiObj {
  propertyType: string;
}
