import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { UserPropertyService } from './user-property.service';
import { PublicUserDto, ValidateOtp } from './dto/user-property.dto';
import { PropertyAnnualTax } from './dto/property-annual-tax.dto';
import { Public } from '@jwt/jwt-auth/decorators/public.decorator';


@Public()
@Controller('user-property')
export class UserPropertyController {
  constructor(private readonly userPropertyService: UserPropertyService) {}


  @Get('checkProperty')
  checkPropertyDetailsReturnAuth(@Query() publicUserDto: PublicUserDto){
    return this.userPropertyService.checkPropertyDetailsReturnOtp(publicUserDto)
  }
//for gloabl serch
  

  @Post('search')
  searchPropertyValidate(@Body() publicUserDto: PublicUserDto){
    return this.userPropertyService.searchPropertyValidate(publicUserDto);
  }

  @Post('validate-otp')
  validateOtp(@Body() validateOtp: ValidateOtp ){
    return this.userPropertyService.validateOtp(validateOtp);
  }


  @Get('getPropertyTax')
  getPropertyAnnualTax(@Query() propertyAnnualTax: PropertyAnnualTax){
    return this.userPropertyService.getPropertyAnnualTax(propertyAnnualTax);
  }
}
