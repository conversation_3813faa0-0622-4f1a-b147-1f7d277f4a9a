# Stage 1: Build the React app
FROM node:18-alpine as build

ARG ENVIRONMENT
ENV NODE_ENV=development
ENV FRONTEND_ENV=$ENVIRONMENT

# Set your working directory
WORKDIR /

ENV PATH /node_modules/.bin:$PATH

# Copy package.json and package-lock.json
COPY package*.json ./

# Install specific npm version and dependencies
# RUN npm install --force -g npm@9.7.1 
# RUN npm install vite@latest @vitejs/plugin-react@latest
RUN npm install -g npm@9.7.1

RUN npm install vite@latest --legacy-peer-deps
RUN npm install @vitejs/plugin-react@latest --legacy-peer-deps

RUN npm install --legacy-peer-deps


# Copy the rest of the application code
COPY . ./

# Build the application
RUN npm run build

# Stage 2: Serve the app with Nginx
FROM nginx:1.21-alpine

# Copy the built app from the build stage to the nginx web root directory
COPY --from=build /dist /usr/share/nginx/html

# Add your nginx configurations
COPY ./default.conf /etc/nginx/conf.d/default.conf

# Expose port 80 for the application
EXPOSE 80

# Start the nginx server
CMD ["nginx", "-g", "daemon off;"]