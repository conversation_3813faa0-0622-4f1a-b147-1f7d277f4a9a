import { Injectable } from '@nestjs/common';
import { CreateItemDto } from './dto/create-item.dto';
import { UpdateItemDto } from './dto/update-item.dto';
const fs = require('fs');
const path = require('path');
const ejs = require('ejs');
import { Response } from 'express';
@Injectable()
export class ItemService {
  create(createItemDto: CreateItemDto) {
    return 'This action adds a new item';
  }

  async findAll(res: Response) {
   return res.send({});
  }

  findOne(id: number) {
    return {
      message: 'Area Saved SuccessFully',
      data: 'ii',
    };
    return `This action returns a #${id} item`;
  }

  update(id: number, updateItemDto: UpdateItemDto) {
    return `This action updates a #${id} item`;
  }

  remove(id: number) {
    return `This action removes a #${id} item`;
  }
}
