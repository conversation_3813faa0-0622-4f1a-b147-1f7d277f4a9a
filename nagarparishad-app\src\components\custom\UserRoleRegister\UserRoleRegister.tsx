import React, { useContext, useState } from "react";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import { Eye, Edit, Trash, ArrowUpDown } from "lucide-react";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "@/context/GlobalContext";
import { useRoleController } from "@/controller/roles/RolesController";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { ColumnDef } from "@tanstack/react-table";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

const UserRoleRegister = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { setrefreshRoleList } = useContext(GlobalContext);
  const { roleList, addRole, deleteRole, userRoleList } = useRoleController();
  const { canPerformAction } = usePermissions();

  const canRead = canPerformAction(ModuleName.RoleAndUserMangement, FormName.Role, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.RoleAndUserMangement, FormName.Role, Action.CanUpdate);
  const canCreate = canPerformAction(ModuleName.RoleAndUserMangement, FormName.Role, Action.CanCreate);
  const canDelete = canPerformAction(ModuleName.RoleAndUserMangement, FormName.Role, Action.CanDelete);

  const schema = z.object({
    role: z.string().trim().min(1, t("errorsRequiredField")),
  });

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      role: "",
    },
  });

  const {
    formState: { errors },
    setError,
  } = form;

  const onSubmit = (data) => {
    if (roleList.some((role) => role.roleName === data.role)) {
      setError("role", {
        type: "manual",
        message: t("alreadyexist"),
      });
      return;
    }

    const roleData = {
      roleName: data.role,
      createdBy: "123",
    };

    addRole(roleData, {
      onSuccess: (response) => {
        const roleId = response.saveData?.role_id;
        console.log("user-roleuser-role", response, roleId);
        navigate("/dashboard/create-role", {
          state: {
            roleName: data.role,
            roleId: roleId,
            edit: false,
          },
        });
      },
      onError: (error) => {
        toast({
          title: error.message || t("failedToCreateRole"),
          variant: "destructive",
        });
      },
    });
  };

  const handleViewDetails = (index) => {
    navigate("/dashboard/create-role", {
      state: {
        roleName: roleList[index].roleName,
        roleId: roleList[index].role_id,
        edit: false,
        viewOnly: true,
      },
    });
  };

  const handleOpenDelete = (index) => {
    setSelectedItem({ ...roleList[index], index });
    setIsDeleteOpen(true);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleConfirmDelete = () => {
    if (selectedItem) {
      deleteRole(selectedItem.role_id, {
        onSuccess: () => {
          setrefreshRoleList((prev) => !prev);
          toast({ title: t("roleDeletedSuccessfully"), variant: "success" });
        },
        onError: (error) => {
          toast({
            title: error.message || t("failedToDeleteRole"),
            variant: "destructive",
          });
        },
        onSettled: () => {
          setIsDeleteOpen(false);
          setSelectedItem(null);
        },
      });
    }
  };

  const handleEdit = (index) => {
    setSelectedItem({ ...roleList[index], index });
    navigate("/dashboard/create-role", {
      state: {
        roleName: roleList[index].roleName,
        roleId: roleList[index].role_id,
        edit: true,
      },
    });
  };

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "SrNo",
      header: t("SrNo"),
      cell: ({ row }) => <div>{row.index + 1}</div>,
    },
    {
      accessorKey: "roleName",
      header: ({ column }) => {
        return (
          <Button
            className="px-0 justify-start text-base font-semibold"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            {t("roleName")}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div>{row.original.roleName}</div>,
    },
    ...(canUpdate || canDelete
      ? [
          {
            accessorKey: "actions",
            header: t("Actions"),
            cell: ({ row }) => (
              <div className="flex space-x-2">
                {canRead && (
                  <button
                    className="h-8 w-8 p-0"
                    onClick={() => handleViewDetails(row.index)}
                  >
                    <Eye className="text-gray-500" />
                  </button>
                )}
                {canUpdate && (
                  <button
                    className="h-8 w-8 p-0 ml-2"
                    onClick={() => handleEdit(row.index)}
                  >
                    <Edit className="text-blue-500" />
                  </button>
                )}
                {canDelete && (
                  <button
                    className="h-8 w-8 p-0 ml-2"
                    onClick={() => handleOpenDelete(row.index)}
                  >
                    <Trash className="text-red-500" />
                  </button>
                )}
              </div>
            ),
          },
        ]
      : []),
  ];

  return (
    <div className="w-full h-fit p-6">
      <h1 className="text-2xl font-semibold font-Poppins w-full mb-4 ml-2">
        {t("addRole")}
      </h1>
      {canCreate && (
        <div className="bg-white shadow rounded-lg p-4 mb-6">
          <p className="text-md font-semibold mb-1">{t("addRole")}</p>
          <Form {...form}>
            <form
              className="flex items-center"
              onSubmit={form.handleSubmit(onSubmit)}
            >
              <div className="grid md:grid-cols-4 gap-x-3 gap-y-3">
                <div className="grid-cols-subgrid col-span-3">
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem className=" ">
                        <FormControl>
                          <Input
                            placeholder={t("enterRoleName")}
                            className="w-full"
                            {...field}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.preventDefault();
                                form.handleSubmit(onSubmit)();
                              }
                            }}
                          />
                        </FormControl>
                        {errors.role && (
                          <FormMessage className="ml-1">
                            {errors.role.message}
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex items-start justify-start h-full pt-[5px] grid-cols-subgrid">
                  <Button variant="submit" type="submit">
                    {t("createRole")}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </div>
      )}
      <div className="bg-white shadow rounded-lg p-4">
        <TanStackTable
          columns={columns}
          data={roleList}
          loader={userRoleList ? true : false}
          searchKey={"searchrole"}
          searchColumn={"roleName"}
        />
      </div>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={selectedItem.roleName}
          onDelete={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default UserRoleRegister;
