import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  Matches,
} from 'class-validator';

export class CreateUserDto {
  @ApiProperty({ description: 'First name of the user', example: '<PERSON>' })
  @IsNotEmpty()
  @IsString()
  @Length(1, 50)
  firstname: string;

  @ApiProperty({ description: 'Last name of the user', example: '<PERSON><PERSON>' })
  @IsNotEmpty()
  @IsString()
  @Length(1, 50)
  lastname: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Password for the user account',
    example: 'Password123!',
  })
  @IsNotEmpty()
  @IsString()
  @Length(6, 50)
  // Use a regex pattern to enforce strong passwords if needed
  @Matches(/(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[\W_]).{6,}/, {
    message:
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
  })
  password: string;

  @ApiProperty({
    description: 'User active status',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Address of the user',
    example: '123 Main St, Anytown, USA',
  })
  @IsNotEmpty()
  @IsString()
  address: string;

  @ApiProperty({
    description: 'Role ID associated with the user',
    example: '1',
  })
  @IsNotEmpty()
  role_id: number; // Assuming role_id is a string, adjust accordingly if it's a different type

  @ApiProperty({ description: 'Ward if the role assigned is of a collector', example: 'ward_id' })
  @IsOptional()
  @IsUUID()
  ward_id?: string;

  @ApiProperty({
    description: 'Mobile number of the user',
    example: '+1234567890',
  })
  @IsNotEmpty()
  @IsString()
  @Matches(/^[0-9]{10,15}$/, {
    message: 'Mobile number must be between 10 to 15 digits',
  })
  mobileNumber: string;

  @ApiProperty({
    description: 'Profile picture URL of the user',
    example: 'http://example.com/profile-pic.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  profilePic?: string;
}
