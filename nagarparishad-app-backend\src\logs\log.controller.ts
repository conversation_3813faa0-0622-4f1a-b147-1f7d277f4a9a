import { Controller, Get, Query, HttpStatus, Res } from '@nestjs/common';
import { LogsService } from './log.service';
import { Logs } from 'libs/database/entities';
import { Response } from 'express';

@Controller('logs')
export class LogsController {
    constructor(private readonly logsService: LogsService) {}
  
    @Get()
    async getLogs(@Query() params, @Res() res: Response) {
      try {
        const logs = await this.logsService.getLogs({
          logType: params.logType,
          logSubType: params.logSubType,
          fromDate: params.fromDate,
          toDate: params.toDate,
        });
        return res.status(HttpStatus.OK).json({
          statusCode: HttpStatus.OK,
          data: logs,
        });
      } catch (error) {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'An error occurred while fetching logs.',
        });
      }
    }
  }
