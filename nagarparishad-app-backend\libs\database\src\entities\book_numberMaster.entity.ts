import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    ManyToOne,
    OneToMany,
    PrimaryColumn,
    JoinColumn,
    UpdateDateColumn,
} from 'typeorm';
import { ReceiptEntity } from './receipt.entity';
import { CollectorMaster } from './collectorMaster.entity';

@Entity('book_number_master')
export class BookNumberMasterEntity extends BaseEntity {

    @PrimaryColumn({ type: 'uuid' })
    id: string

    @Column({ type: 'int', unique: true })
    book_number: number;

    @Column({ type: 'json', name: 'available_receipts', nullable: false })
    availableReceipts: number[];

    @Column({ type: 'json', name: 'receipts_in_use', nullable: false })
    receiptsInUse: number[];

    @ManyToOne(() => CollectorMaster ,(collector) => collector.collectorId, { eager: true, onDelete: "SET NULL" })
    @JoinColumn({ name: 'collectorId' }) 
    collector: CollectorMaster;

    @Column({ nullable: true })
    collectorId: string;

    
  @OneToMany(() => ReceiptEntity, (receipt) => receipt.bookNumber)
  receipts: ReceiptEntity[];

    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;

    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;

    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
}
