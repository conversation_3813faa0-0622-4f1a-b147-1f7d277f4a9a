import { MigrationInterface, QueryRunner } from "typeorm";

export class FirstMigration1723178247276 implements MigrationInterface {
    name = 'FirstMigration1723178247276'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "adminstrative_boundaryMaster" ("adminstrativeBoundary_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "adminstrative_boundary_name" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_bfd9a2f46515930c59cef7c16cb" PRIMARY KEY ("adminstrativeBoundary_id"))`);
        await queryRunner.query(`CREATE TABLE "area_master" ("area_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "area_name" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_0e41116553f3d6d57cfe7686913" PRIMARY KEY ("area_id"))`);
        await queryRunner.query(`CREATE TABLE "construction_class" ("constructionClass_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "construction_class_name" character varying NOT NULL, "construction_class_marathi" character varying NOT NULL, "values" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_1ac25bdfea18ead10770b96be34" PRIMARY KEY ("constructionClass_id"))`);
        await queryRunner.query(`CREATE TABLE "election_boundary_master" ("electionBoundary_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "election_boundary_name" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_1f2bd70f330a8c8d97e07027111" PRIMARY KEY ("electionBoundary_id"))`);
        await queryRunner.query(`CREATE TABLE "module_master" ("module_id" SERIAL NOT NULL, "module_name" character varying NOT NULL, "created_by" character varying, "created_by_1" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_82cbd0346edf7203226f5954f3e" PRIMARY KEY ("module_id"))`);
        await queryRunner.query(`CREATE TABLE "form_master" ("form_id" SERIAL NOT NULL, "form_name" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "module_id" integer, CONSTRAINT "PK_d3a53e3093c7bef38723ad21806" PRIMARY KEY ("form_id"))`);
        await queryRunner.query(`CREATE TABLE "location_master" ("location_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "location_name" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_d9fb893ab489e80dad2ddce625b" PRIMARY KEY ("location_id"))`);
        await queryRunner.query(`CREATE TABLE "property_type_master" ("propertyType_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "property_type" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_90968ea94d908efdc94ae176ad5" PRIMARY KEY ("propertyType_id"))`);
        await queryRunner.query(`CREATE TABLE "property_sub_type_master" ("property_sub_type_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "propertySub_name" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "propertyType_id" uuid, CONSTRAINT "PK_ea3b6d4e79849079d2f5ec183f8" PRIMARY KEY ("property_sub_type_id"))`);
        await queryRunner.query(`CREATE TABLE "street_master" ("street_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "street_name" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_214a52a7ac26d87e1d3964600ca" PRIMARY KEY ("street_id"))`);
        await queryRunner.query(`CREATE TABLE "ward_master" ("ward_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "ward_name" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_fa6173802f56c36430593fa98ca" PRIMARY KEY ("ward_id"))`);
        await queryRunner.query(`CREATE TABLE "zone_master" ("zone_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "zone_name" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "ward_id" uuid, CONSTRAINT "PK_ce64f88c15979a17e00162e6613" PRIMARY KEY ("zone_id"))`);
        await queryRunner.query(`CREATE TABLE "owner_type_master" ("owner_type_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "owner_type" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_691ef94f0d0bd8ddd8d7314a868" PRIMARY KEY ("owner_type_id"))`);
        await queryRunner.query(`CREATE TABLE "property_owner_details" ("property_owner_details_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying, "mobile_number" character varying, "email_id" character varying, "aadhar_number" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, "owner_type_id" uuid, CONSTRAINT "PK_4caced9bfde81221dd576d34e60" PRIMARY KEY ("property_owner_details_id"))`);
        await queryRunner.query(`CREATE TABLE "usage_type_master" ("usage_type_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "usage_type" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_7786783a18f41c5959586f89f87" PRIMARY KEY ("usage_type_id"))`);
        await queryRunner.query(`CREATE TABLE "usage_sub_type_master" ("usage_sub_type_master_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "usage_sub_type_master" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "usage_type_id" uuid, CONSTRAINT "PK_ec539063a2309b212af6518b53e" PRIMARY KEY ("usage_sub_type_master_id"))`);
        await queryRunner.query(`CREATE TABLE "property_usage_details" ("property_usage_details_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "property_type_desc" character varying, "construction_area" double precision, "length" double precision, "width" double precision, "are_sq_ft" double precision, "are_sq_meter" double precision, "construction_start_date" TIMESTAMP, "construction_end_date" TIMESTAMP, "Building_age" double precision, "annual_rent" double precision, "floor" character varying, "flat_no" character varying, "authorized" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, "propertyType_id" uuid, "propertySub_id" uuid, "usage_type_id" uuid, "usage_sub_type_master_id" uuid, CONSTRAINT "PK_0fa4d6c8575198d364ee14b5d54" PRIMARY KEY ("property_usage_details_id"))`);
        await queryRunner.query(`CREATE TABLE "tax_propertywise" ("tax_propertywise_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year" character varying NOT NULL, "bill_no" character varying NOT NULL, "sq_ft_meter" double precision NOT NULL, "rr_rate" double precision NOT NULL, "rr_construction_rate" double precision NOT NULL, "depreciation_rate" double precision NOT NULL, "weighting" double precision NOT NULL, "capital_value" double precision NOT NULL, "tax_value" double precision NOT NULL, "tax" double precision NOT NULL, "bill_generation_date" date NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, "tax_property_id" uuid, "property_usage_details_id" uuid, CONSTRAINT "PK_dd546ded7c03c4b92caa6f75433" PRIMARY KEY ("tax_propertywise_id"))`);
        await queryRunner.query(`CREATE TABLE "tax_type_master" ("tax_type_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "tax_type" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_ea73a6b7ede6083009ddab52bbe" PRIMARY KEY ("tax_type_id"))`);
        await queryRunner.query(`CREATE TABLE "tax_property_other_taxes" ("tax_property_other_taxes_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "tax_type" character varying NOT NULL, "amount" double precision NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, "tax_property_id" uuid, "tax_type_id" uuid, CONSTRAINT "PK_6e16ef4818786e3969790a434ca" PRIMARY KEY ("tax_property_other_taxes_id"))`);
        await queryRunner.query(`CREATE TABLE "tax_property" ("tax_property_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "bill_no" character varying NOT NULL, "financial_year" character varying NOT NULL, "all_property_tax_sum" double precision NOT NULL DEFAULT '0', "other_tax_sum_tax" double precision NOT NULL, "total_tax" double precision NOT NULL, "bill_generation_date" date NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, CONSTRAINT "PK_fa79fead1140058397388f5d2e1" PRIMARY KEY ("tax_property_id"))`);
        await queryRunner.query(`CREATE TABLE "property" ("property_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "propertyNumber" character varying, "old_propertyNumber" character varying, "city_survey_number" character varying, "address" character varying, "house_or_apartment_name" character varying, "latitude" character varying, "longitude" character varying, "mobile_number" character varying, "email_id" character varying, "uploaded_files" json, "property_desc" character varying, "plot_area" double precision, "Plot_construction_area" double precision, "Plot_empty_area" double precision, "construction_area" double precision, "carpet_area" double precision, "exempted_area" double precision, "assessable_area" double precision, "land_cost" double precision, "standard_rate" double precision, "annual_rent" double precision, "capital_value" double precision, "remark" character varying, "snp_ward" character varying, "zone_code" character varying, "gat_no" character varying, "gis_number" character varying, "note" character varying, "survey_person_code" character varying, "survey_date" character varying, "property_photographs" text, "sr_no" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "street_id" uuid, "ward_id" uuid, "zone_id" uuid, CONSTRAINT "PK_5dedb31d883f351fc101febc7c1" PRIMARY KEY ("property_id"))`);
        await queryRunner.query(`CREATE TABLE "role_master" ("role_id" SERIAL NOT NULL, "role_name" character varying NOT NULL, "created_by" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_70640920c8ac0ddde941f05b32e" PRIMARY KEY ("role_id"))`);
        await queryRunner.query(`CREATE TABLE "rolewise_form_permission" ("action_id" SERIAL NOT NULL, "can_read" boolean NOT NULL DEFAULT false, "can_write" boolean NOT NULL DEFAULT false, "can_update" boolean NOT NULL DEFAULT false, "can_delete" boolean NOT NULL DEFAULT false, "is_valid" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "role_id" integer, "form_id" integer, CONSTRAINT "PK_70c0ceb00229770b87bacc63b3c" PRIMARY KEY ("action_id"))`);
        await queryRunner.query(`CREATE TABLE "user_master" ("user_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "first_name" character varying NOT NULL, "last_name" character varying NOT NULL, "email" character varying NOT NULL, "password" character varying NOT NULL, "is_Active" boolean NOT NULL DEFAULT false, "address" character varying NOT NULL, "mobile_number" character varying NOT NULL, "profile_pic" character varying, "refresh_token" text, "reset_token" text, "last_login_at" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "role_id" integer, CONSTRAINT "PK_dc2fd281084013caf643e90092c" PRIMARY KEY ("user_id"))`);
        await queryRunner.query(`CREATE TABLE "user_otp" ("otpId" uuid NOT NULL DEFAULT uuid_generate_v4(), "otp" character varying NOT NULL, "expires_at" TIMESTAMP NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "user_id" uuid, CONSTRAINT "PK_e71700919bab76bded97049e996" PRIMARY KEY ("otpId"))`);
        await queryRunner.query(`CREATE TABLE "financial_year" ("financial_year_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "financial_year_range" character varying NOT NULL, "from_date" character varying NOT NULL, "to_date" character varying NOT NULL, "is_active" character varying NOT NULL, "is_published" character varying NOT NULL DEFAULT 'true', "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_89ea62aaf86dcb9f08c962e1534" PRIMARY KEY ("financial_year_id"))`);
        await queryRunner.query(`CREATE TABLE "tax_fy_records" ("tax_fy_records_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" character varying NOT NULL, "total_property" double precision NOT NULL, "total_property_processed" double precision NOT NULL, "is_published" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "financial_year_id" uuid, CONSTRAINT "PK_e60ffa9cefd6d944ffd8ace8440" PRIMARY KEY ("tax_fy_records_id"))`);
        await queryRunner.query(`ALTER TABLE "form_master" ADD CONSTRAINT "FK_446d9b07b5cce300c2545651aec" FOREIGN KEY ("module_id") REFERENCES "module_master"("module_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property_sub_type_master" ADD CONSTRAINT "FK_f57d1e0e7e045220e0b05e5d3f7" FOREIGN KEY ("propertyType_id") REFERENCES "property_type_master"("propertyType_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "zone_master" ADD CONSTRAINT "FK_efd275f461cedf170a8c6eab88f" FOREIGN KEY ("ward_id") REFERENCES "ward_master"("ward_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" ADD CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" ADD CONSTRAINT "FK_41f1d78e372bcf67a674ddc7c12" FOREIGN KEY ("owner_type_id") REFERENCES "owner_type_master"("owner_type_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "usage_sub_type_master" ADD CONSTRAINT "FK_47f9107980376de64568006ff66" FOREIGN KEY ("usage_type_id") REFERENCES "usage_type_master"("usage_type_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_df5e676c36082c5c46778bc844c" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_90614cac26fb49598421ebfed20" FOREIGN KEY ("propertyType_id") REFERENCES "property_type_master"("propertyType_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_c5c90bc818dde0d2f3fc3a0019a" FOREIGN KEY ("propertySub_id") REFERENCES "property_sub_type_master"("property_sub_type_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_4f0223350514a6a272405b98dc0" FOREIGN KEY ("usage_type_id") REFERENCES "usage_type_master"("usage_type_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_db39c94a4d3c4b0abfda93fca11" FOREIGN KEY ("usage_sub_type_master_id") REFERENCES "usage_sub_type_master"("usage_sub_type_master_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_199a29a48f8231c75c791acdfbe" FOREIGN KEY ("tax_property_id") REFERENCES "tax_property"("tax_property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_propertywise" ADD CONSTRAINT "FK_1790af28e759558cd2116e9d953" FOREIGN KEY ("property_usage_details_id") REFERENCES "property_usage_details"("property_usage_details_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_property_other_taxes" ADD CONSTRAINT "FK_693fd31b799618c70fccf7e9752" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_property_other_taxes" ADD CONSTRAINT "FK_6acd97c4114fa7d71e1edd7018a" FOREIGN KEY ("tax_property_id") REFERENCES "tax_property"("tax_property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_property_other_taxes" ADD CONSTRAINT "FK_1af28303bbff71508964482060f" FOREIGN KEY ("tax_type_id") REFERENCES "tax_type_master"("tax_type_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_property" ADD CONSTRAINT "FK_d7a73eaab514342e3364090ccba" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_2b3f8b3d5bea0587920b3020eac" FOREIGN KEY ("street_id") REFERENCES "street_master"("street_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_63859fe9b799dc463dd2e14ad72" FOREIGN KEY ("ward_id") REFERENCES "ward_master"("ward_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "property" ADD CONSTRAINT "FK_ed2960763a10694e2d01f2f041c" FOREIGN KEY ("zone_id") REFERENCES "zone_master"("zone_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "rolewise_form_permission" ADD CONSTRAINT "FK_e0bc9aeebac5d83438653744c42" FOREIGN KEY ("role_id") REFERENCES "role_master"("role_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "rolewise_form_permission" ADD CONSTRAINT "FK_6ddad189717af040c0d3a28b345" FOREIGN KEY ("form_id") REFERENCES "form_master"("form_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_master" ADD CONSTRAINT "FK_fb2e0710e1b9af4bdfbc1853339" FOREIGN KEY ("role_id") REFERENCES "role_master"("role_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_otp" ADD CONSTRAINT "FK_7c4b83e0619128a0b57da32228c" FOREIGN KEY ("user_id") REFERENCES "user_master"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_fy_records" ADD CONSTRAINT "FK_c9ce89ec90dc8f02667b203ab8c" FOREIGN KEY ("financial_year_id") REFERENCES "financial_year"("financial_year_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tax_fy_records" DROP CONSTRAINT "FK_c9ce89ec90dc8f02667b203ab8c"`);
        await queryRunner.query(`ALTER TABLE "user_otp" DROP CONSTRAINT "FK_7c4b83e0619128a0b57da32228c"`);
        await queryRunner.query(`ALTER TABLE "user_master" DROP CONSTRAINT "FK_fb2e0710e1b9af4bdfbc1853339"`);
        await queryRunner.query(`ALTER TABLE "rolewise_form_permission" DROP CONSTRAINT "FK_6ddad189717af040c0d3a28b345"`);
        await queryRunner.query(`ALTER TABLE "rolewise_form_permission" DROP CONSTRAINT "FK_e0bc9aeebac5d83438653744c42"`);
        await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_ed2960763a10694e2d01f2f041c"`);
        await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_63859fe9b799dc463dd2e14ad72"`);
        await queryRunner.query(`ALTER TABLE "property" DROP CONSTRAINT "FK_2b3f8b3d5bea0587920b3020eac"`);
        await queryRunner.query(`ALTER TABLE "tax_property" DROP CONSTRAINT "FK_d7a73eaab514342e3364090ccba"`);
        await queryRunner.query(`ALTER TABLE "tax_property_other_taxes" DROP CONSTRAINT "FK_1af28303bbff71508964482060f"`);
        await queryRunner.query(`ALTER TABLE "tax_property_other_taxes" DROP CONSTRAINT "FK_6acd97c4114fa7d71e1edd7018a"`);
        await queryRunner.query(`ALTER TABLE "tax_property_other_taxes" DROP CONSTRAINT "FK_693fd31b799618c70fccf7e9752"`);
        await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_1790af28e759558cd2116e9d953"`);
        await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_199a29a48f8231c75c791acdfbe"`);
        await queryRunner.query(`ALTER TABLE "tax_propertywise" DROP CONSTRAINT "FK_bc04fc091149eaffa8a0220e8f0"`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_db39c94a4d3c4b0abfda93fca11"`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_4f0223350514a6a272405b98dc0"`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_c5c90bc818dde0d2f3fc3a0019a"`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_90614cac26fb49598421ebfed20"`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_df5e676c36082c5c46778bc844c"`);
        await queryRunner.query(`ALTER TABLE "usage_sub_type_master" DROP CONSTRAINT "FK_47f9107980376de64568006ff66"`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP CONSTRAINT "FK_41f1d78e372bcf67a674ddc7c12"`);
        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP CONSTRAINT "FK_bb2e60b7331aeb5322489d70ec2"`);
        await queryRunner.query(`ALTER TABLE "zone_master" DROP CONSTRAINT "FK_efd275f461cedf170a8c6eab88f"`);
        await queryRunner.query(`ALTER TABLE "property_sub_type_master" DROP CONSTRAINT "FK_f57d1e0e7e045220e0b05e5d3f7"`);
        await queryRunner.query(`ALTER TABLE "form_master" DROP CONSTRAINT "FK_446d9b07b5cce300c2545651aec"`);
        await queryRunner.query(`DROP TABLE "tax_fy_records"`);
        await queryRunner.query(`DROP TABLE "financial_year"`);
        await queryRunner.query(`DROP TABLE "user_otp"`);
        await queryRunner.query(`DROP TABLE "user_master"`);
        await queryRunner.query(`DROP TABLE "rolewise_form_permission"`);
        await queryRunner.query(`DROP TABLE "role_master"`);
        await queryRunner.query(`DROP TABLE "property"`);
        await queryRunner.query(`DROP TABLE "tax_property"`);
        await queryRunner.query(`DROP TABLE "tax_property_other_taxes"`);
        await queryRunner.query(`DROP TABLE "tax_type_master"`);
        await queryRunner.query(`DROP TABLE "tax_propertywise"`);
        await queryRunner.query(`DROP TABLE "property_usage_details"`);
        await queryRunner.query(`DROP TABLE "usage_sub_type_master"`);
        await queryRunner.query(`DROP TABLE "usage_type_master"`);
        await queryRunner.query(`DROP TABLE "property_owner_details"`);
        await queryRunner.query(`DROP TABLE "owner_type_master"`);
        await queryRunner.query(`DROP TABLE "zone_master"`);
        await queryRunner.query(`DROP TABLE "ward_master"`);
        await queryRunner.query(`DROP TABLE "street_master"`);
        await queryRunner.query(`DROP TABLE "property_sub_type_master"`);
        await queryRunner.query(`DROP TABLE "property_type_master"`);
        await queryRunner.query(`DROP TABLE "location_master"`);
        await queryRunner.query(`DROP TABLE "form_master"`);
        await queryRunner.query(`DROP TABLE "module_master"`);
        await queryRunner.query(`DROP TABLE "election_boundary_master"`);
        await queryRunner.query(`DROP TABLE "construction_class"`);
        await queryRunner.query(`DROP TABLE "area_master"`);
        await queryRunner.query(`DROP TABLE "adminstrative_boundaryMaster"`);
    }

}
