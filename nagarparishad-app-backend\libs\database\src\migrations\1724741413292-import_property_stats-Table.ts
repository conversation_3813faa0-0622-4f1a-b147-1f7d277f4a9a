import { MigrationInterface, QueryRunner } from "typeorm";

export class ImportPropertyStatsTable1724741413292 implements MigrationInterface {
    name = 'ImportPropertyStatsTable1724741413292'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "import_property_stats" ("dstats_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "blankStreetCount" integer, "blankZoneNameCount" integer, "blankPropertyNumberCount" integer, "blankOldPropertyNumberCount" integer, "blankOwnerNameCount" integer, "blankOwnerTypeCount" integer, "blankUsageTypeCount" integer, "blankUsageDescCount" integer, "blankConstructionYearCount" integer, "blankLengthCount" integer, "blankWidthCount" integer, "blankSqftCount" integer, "blankSqmeterCount" integer, "okCount" integer, "totalCount" integer, "ward_name" character varying, "import_date" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_b1dd8548376e0b5af4f769feb84" PRIMARY KEY ("dstats_id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "import_property_stats"`);
    }

}
