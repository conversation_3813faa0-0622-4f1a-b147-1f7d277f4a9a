import {
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateUsageSubMasterDto,
  UpdateUsageSubMasterDto,
  UsageIdDto,
  UsageSubMasterDto,
} from './dto/usage_sub__master.dto';
import {
  UsageMasterRepository,
  UsageSubMasterRepository,
} from 'libs/database/repositories';

@Injectable()
export class UsageSubMasterService {
  constructor(
    private readonly usageMasterReposiotry: UsageMasterRepository,
    private readonly usageSubMasterRepository: UsageSubMasterRepository,
  ) {}
  async create(createUsageSubMasterDto: CreateUsageSubMasterDto) {
    try {
      const { usage } = createUsageSubMasterDto;
      //check if exists
      const checkUsage = await this.usageMasterReposiotry.findById(usage);

      if (!checkUsage) {
        throw new NotFoundException('Usage  Not Found');
      }
      // const saveData = await this.usageSubMasterRepository.saveData(
      //   createUsageSubMasterDto,
      // );
      const saveData={};
      return {
        message: 'Data Saved SuccessFully',
        data: saveData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllData = await this.usageSubMasterRepository.findAllData();

      if (!getAllData) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(usageSubMasterDto: UsageSubMasterDto) {
    try {
      const { usageSub_id } = usageSubMasterDto;
      const checkData =
        await this.usageSubMasterRepository.findById(usageSub_id);

      if (!checkData) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Data Found Success',
        data: checkData,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    usageSubMasterDto: UsageSubMasterDto,
    updateUsageSubMasterDto: UpdateUsageSubMasterDto,
  ) {
    try {
      const { usageSub_id } = usageSubMasterDto;
      const checkData =
        await this.usageSubMasterRepository.findById(usageSub_id);

      if (!checkData) {
        throw new NotFoundException('Data Not Found');
      }
      const updateData = await this.usageSubMasterRepository.updateData(
        usageSub_id,
        updateUsageSubMasterDto,
      );

      if (updateData.affected === 0) {
        throw new NotAcceptableException('Failed to update ');
      }
      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(usageSubMasterDto: UsageSubMasterDto) {
    try {
      const { usageSub_id } = usageSubMasterDto;
      const checkData =
        await this.usageSubMasterRepository.findById(usageSub_id);

      if (!checkData) {
        throw new NotFoundException('Data Not Found');
      }
      const deleteData =
        await this.usageSubMasterRepository.deleteData(usageSub_id);

      if (deleteData.affected === 0) {
        throw new NotAcceptableException('Failed To Delete data');
      }

      return {
        message: 'Data Deleted SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }

  async getByUsage(usageIdDto: UsageIdDto) {
    try {
      const { usage } = usageIdDto;
      const checkData = await this.usageMasterReposiotry.findById(usage);
      if (!checkData) {
        throw new NotFoundException(' Usage Not Found');
      }

      const getData = await this.usageSubMasterRepository.findData(usage);

      if (!getData || getData.length === 0) {
        throw new NotFoundException('Data Not Found');
      }
      return {
        message: 'Records Fetched Success',
        data: getData,
      };
    } catch (error) {
      throw error;
    }
  }
}
