import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Res } from '@nestjs/common';
import { ItemService } from './item.service';
import { CreateItemDto } from './dto/create-item.dto';
import { UpdateItemDto } from './dto/update-item.dto';
import { Response } from 'express';

@Controller('item')
export class ItemController {
  constructor(private readonly itemService: ItemService) {}

  @Post()
  create(@Body() createItemDto: CreateItemDto) {
    return this.itemService.create(createItemDto);
  }

  @Get()
  async findAll(@Res() res: Response) {
    await this.itemService.findAll(res);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return {
      message: 'Area Saved SuccessFully',
      data: 'ii',
    };
    return this.itemService.findOne(+id);
  }

  @Get('generate-invoices')
  generateInvoices() {
    return {
      message: 'Invoice data',
      data: 'Invoice'
    };
    
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateItemDto: UpdateItemDto) {
    return this.itemService.update(+id, updateItemDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.itemService.remove(+id);
  }
}
