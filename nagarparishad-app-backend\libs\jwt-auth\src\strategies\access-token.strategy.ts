import { PassportStrategy } from '@nestjs/passport';
import { UserRepository } from 'libs/database/repositories';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { JwtPayload } from '../types';
import { Injectable, UnauthorizedException } from '@nestjs/common';

@Injectable()
export class AccessTokenStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(private readonly userRepository: UserRepository) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: process.env.JWT_ACCESS_SECRET,
    });
  }

  async validate(payload: JwtPayload) {
    const { sub: user_id } = payload;

    const user = await this.userRepository.findById(user_id);

    if (!user) {
      throw new UnauthorizedException();
    }
    return payload;
  }
}
