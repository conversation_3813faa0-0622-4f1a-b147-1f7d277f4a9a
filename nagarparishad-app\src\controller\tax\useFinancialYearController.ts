import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import TaxListApi from "@/services/TaxServices";
import Api from "@/services/ApiServices";

interface FinancialYear {
  id: string;
  year: string;
  start_date: string;
  end_date: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

// Function to fetch financial years
const fetchFinancialYears = async () => {
    const response = await Api.fyYears();
console.log("repsonsss",response)
    return response.data.data;

};

// Function to create a new financial year
const createFinancialYear = async (financialYearData: any) => {
  // return new Promise((resolve, reject) => {
  //   TaxListApi.createFinancialYear(financialYearData, (response) => {
  //     if (response.status) {
  //       resolve(response.data);
  //     } else {
  //       reject(response.data);
  //     }
  //   });
  // });
};

export const useFinancialYearController = () => {
  const queryClient = useQueryClient();

  // Query to fetch financial years
  const { data: financialYearData, isLoading: financialYearLoading } = useQuery({
    queryKey: ["financialYears"],
    queryFn: fetchFinancialYears,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true, // Refetch on window focus
  });

  // Mutation to create a new financial year
  const createFinancialYearMutation = useMutation({
    mutationFn: createFinancialYear,
    onMutate: async (newFinancialYear) => {
      await queryClient.cancelQueries({ queryKey: ["financialYears"] });
      const previousFinancialYears = queryClient.getQueryData(["financialYears"]);

      queryClient.setQueryData(["financialYears"], (old: any) => {
        const updatedData = [newFinancialYear, ...old.data];
        return updatedData;
      });

      return { previousFinancialYears };
    },
    onError: (err, newFinancialYear, context) => {
      queryClient.setQueryData(["financialYears"], context.previousFinancialYears);
      console.error("Error creating financial year:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["financialYears"] });
    },
  });

  return {
    FinancialYearList: financialYearData || [],
    financialYearLoading,
    createFinancialYear: createFinancialYearMutation.mutate,
  };
};
