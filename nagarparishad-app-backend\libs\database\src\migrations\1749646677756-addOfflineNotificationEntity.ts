import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOfflineNotificationEntity1749646677756 implements MigrationInterface {
  name = 'AddOfflineNotificationEntity1749646677756';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TYPE "public"."offline_notifications_type_enum" AS ENUM('info', 'success', 'warning', 'error', 'progress')
    `);
    
    await queryRunner.query(`
      CREATE TYPE "public"."offline_notifications_status_enum" AS ENUM('pending', 'processing', 'completed', 'failed', 'delivered')
    `);

    await queryRunner.query(`
      CREATE TABLE "offline_notifications" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "notification_id" character varying(255) NOT NULL,
        "title" character varying(255) NOT NULL,
        "message" text NOT NULL,
        "type" "public"."offline_notifications_type_enum" NOT NULL DEFAULT 'info',
        "status" "public"."offline_notifications_status_enum" NOT NULL DEFAULT 'pending',
        "progress" integer,
        "file_path" character varying(500),
        "file_name" character varying(255),
        "file_size" bigint,
        "download_url" character varying(500),
        "is_downloaded" boolean NOT NULL DEFAULT false,
        "download_count" integer NOT NULL DEFAULT '0',
        "expires_at" TIMESTAMP,
        "metadata" jsonb,
        "webhook_received_at" TIMESTAMP,
        "delivered_at" TIMESTAMP,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_offline_notifications_id" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_offline_notifications_notification_id" UNIQUE ("notification_id")
      )
    `);

    await queryRunner.query(`
      ALTER TABLE "offline_notifications" 
      ADD CONSTRAINT "FK_offline_notifications_user_id" 
      FOREIGN KEY ("user_id") REFERENCES "user_master"("user_id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_offline_notifications_user_id" ON "offline_notifications" ("user_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_offline_notifications_status" ON "offline_notifications" ("status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_offline_notifications_expires_at" ON "offline_notifications" ("expires_at")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_offline_notifications_expires_at"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_offline_notifications_status"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_offline_notifications_user_id"`);
    await queryRunner.query(`ALTER TABLE "offline_notifications" DROP CONSTRAINT "FK_offline_notifications_user_id"`);
    await queryRunner.query(`DROP TABLE "offline_notifications"`);
    await queryRunner.query(`DROP TYPE "public"."offline_notifications_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."offline_notifications_type_enum"`);
  }
}
