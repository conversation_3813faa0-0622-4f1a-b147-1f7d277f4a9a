import {
    BaseEntity,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
  } from 'typeorm';
import { Financial_year } from './financial_year.entity';

  @Entity('tax_pending_dues')
  export class TaxPendingDuesEntity extends BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    dues_id: string;
    @Column({ name: 'sheetIndex', nullable: false, type: String })
    sheetIndex: string;

    @Column({ name: 'propertyNumber', nullable: true, type: String })
    property_number: string;

    @Column({ name: 'old_propertyNumber', nullable: false, type: String })
    old_propertyNumber: string;

    @Column({ name: 'survey_number', nullable: false, type: String })
    surveyNumber: string;

    @Column({ name: 'streetName', nullable: false, type: String })
    streetName: string;

    @Column({ name: 'property_holder_name', nullable: false, type: String })
    propertyHolderName: string;

    @Column({ name: 'possession_holder_name', nullable: false, type: String })
    possessionHolderName: string;

    @Column({ name: 'ward', nullable: false, type: String })
    ward: string;

    @Column({ name: 'all_property_tax_sum', nullable: false, type: 'decimal' })
    all_property_tax_sum: number;

    @Column({ name: 'tax_type_1', nullable: true, type: 'decimal', default: 0 })
    tax_type_1: number;

    @Column({ name: 'tax_type_2', nullable: true, type: 'decimal', default: 0 })
    tax_type_2: number;

    @Column({ name: 'tax_type_3', nullable: true, type: 'decimal', default: 0 })
    tax_type_3: number;

    @Column({ name: 'tax_type_4', nullable: false, type: 'decimal' })
    tax_type_4: number;

    @Column({ name: 'tax_type_5', nullable: true, type: 'decimal', default: 0 })
    tax_type_5: number;

    @Column({ name: 'tax_type_6', nullable: false, type: 'decimal' })
    tax_type_6: number;

    @Column({ name: 'tax_type_7', nullable: false, type: 'decimal' })
    tax_type_7: number;

    @Column({ name: 'tax_type_8', nullable: false, type: 'decimal' })
    tax_type_8: number;

    @Column({ name: 'tax_type_9', nullable: false, type: 'decimal' })
    tax_type_9: number;

    @Column({ name: 'tax_type_10', nullable: true, type: 'decimal', default: 0 })
    tax_type_10: number;

    @Column({ name: 'total', nullable: false, type: 'decimal' })
    total: number;

    @ManyToOne(() => Financial_year, (financial_year) => financial_year.financial_year_id)
    @JoinColumn({ name: 'financial_year' })
    financial_year: Financial_year;

    /*
     * Create and Update Date Columns
     */

    @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
    public createdAt!: Date;

    @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
    public updatedAt!: Date;

    @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
    public deletedAt!: Date;
  }
