import { MigrationInterface, QueryRunner } from "typeorm";

export class CreattableClassMaster1729153065904 implements MigrationInterface {
    name = 'CreattableClassMaster1729153065904'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" DROP CONSTRAINT "FK_909c3828ef12af861ea7fd2f8f7"`);
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" RENAME COLUMN "propertyType_id" TO "property_type_class_id"`);
        await queryRunner.query(`CREATE TABLE "property_type_class" ("property_type_class_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "property_type_class" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_32813f697922bab74eced656225" PRIMARY KEY ("property_type_class_id"))`);
        await queryRunner.query(`CREATE TABLE "master_depreciation_rate" ("depreciation_rate_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "from_age" double precision NOT NULL DEFAULT '0', "to_age" double precision NOT NULL DEFAULT '0', "financial_year" character varying NOT NULL, "value" double precision NOT NULL DEFAULT '0', "status" character varying DEFAULT 'Active', "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_type_class_id" uuid, CONSTRAINT "PK_754dde7ea25943fdd348cd50e05" PRIMARY KEY ("depreciation_rate_id"))`);
        await queryRunner.query(`ALTER TABLE "property_type_master" ADD "property_type_class_id" uuid`);
        await queryRunner.query(`ALTER TABLE "property_type_master" ADD CONSTRAINT "FK_a81fb4c2157c7c368b532d0835e" FOREIGN KEY ("property_type_class_id") REFERENCES "property_type_class"("property_type_class_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" ADD CONSTRAINT "FK_51026b0c62d7fae067f63f5db6a" FOREIGN KEY ("property_type_class_id") REFERENCES "property_type_class"("property_type_class_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "master_depreciation_rate" ADD CONSTRAINT "FK_3a50bbe702257695cfb8af11434" FOREIGN KEY ("property_type_class_id") REFERENCES "property_type_class"("property_type_class_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "master_depreciation_rate" DROP CONSTRAINT "FK_3a50bbe702257695cfb8af11434"`);
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" DROP CONSTRAINT "FK_51026b0c62d7fae067f63f5db6a"`);
        await queryRunner.query(`ALTER TABLE "property_type_master" DROP CONSTRAINT "FK_a81fb4c2157c7c368b532d0835e"`);
        await queryRunner.query(`ALTER TABLE "property_type_master" DROP COLUMN "property_type_class_id"`);
        await queryRunner.query(`DROP TABLE "master_depreciation_rate"`);
        await queryRunner.query(`DROP TABLE "property_type_class"`);
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" RENAME COLUMN "property_type_class_id" TO "propertyType_id"`);
        await queryRunner.query(`ALTER TABLE "master_rr_construction_rate" ADD CONSTRAINT "FK_909c3828ef12af861ea7fd2f8f7" FOREIGN KEY ("propertyType_id") REFERENCES "property_type_master"("propertyType_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
