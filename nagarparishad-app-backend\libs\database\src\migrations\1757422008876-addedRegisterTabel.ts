import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedRegisterTabel1757422008876 implements MigrationInterface {
    name = 'AddedRegisterTabel1757422008876'

    public async up(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`CREATE TABLE "register_number" ("register_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "register_name" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_8ff969ce02365def8eb3ebcc329" PRIMARY KEY ("register_id"))`);
        await queryRunner.query(`ALTER TABLE "property" ADD "register_number" uuid`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE "property" DROP COLUMN "register_number"`);
        await queryRunner.query(`DROP TABLE "register_number"`);
        }

}
