import { Controller, Get, Post, Body, Param, Put, Delete, Query } from '@nestjs/common';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';
import { PenaltyFeeYearWiseService } from './penalty_fee.services';
import { CreatePenaltyFeeDto, UpdatePenaltyFeeDto } from './dto/penalty-fee-year-wise.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Penalty Fee')
@Controller('penalty-fee-year-wise')
export class PenaltyFeeYearWiseController {
  constructor(private readonly penaltyFeeYearWiseService: PenaltyFeeYearWiseService) {}

  // Create a new PenaltyFeeYearWise
  // Retrieve all PenaltyFeeYearWise records
  @Form('PenaltyFee')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all penalty fees' })
  @ApiResponse({ status: 200, description: 'Returns all penalty fees' })
  @Get()
  async getAllPenaltyFees() {
    const data = await this.penaltyFeeYearWiseService.getAllPenaltyFees();
    return {
      message: 'All penalty fees retrieved successfully',
      data,
    };
  }

  @Form('PenaltyFee')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get penalties by property ID' })
  @ApiResponse({ status: 200, description: 'Returns penalties for the specified property' })
  @Get('by-property')
  async getPenaltiesByPropertyId(
    @Query('propertyId') propertyId: string,
    @Query('financialYear') financialYear?: string
  ) {
    if (!propertyId) {
      return {
        message: 'Property ID is required',
        data: [],
      };
    }

    const data = await this.penaltyFeeYearWiseService.getPenaltyFeesByPropertyId(propertyId, financialYear);
    return {
      message: 'Penalties retrieved successfully for the property',
      data,
    };
  }

  // Retrieve a single PenaltyFeeYearWise record by ID using a query parameter

  // Update a PenaltyFeeYearWise record by ID using a query parameter
  // @Form('PenaltyFee')
  // @Permissions('can_update')
  // @Put('update')
  // async updatePenaltyFee(@Query('id') id: string, @Body() updatePenaltyFeeDto: UpdatePenaltyFeeDto) {
  //   const data = await this.penaltyFeeYearWiseService.updatePenaltyFee(id, updatePenaltyFeeDto);
  //   return {
  //     message: 'Penalty fee updated successfully',
  //     data,
  //   };
  // }

  // // Delete a PenaltyFeeYearWise record by ID using a query parameter
  // @Form('PenaltyFee')
  // @Permissions('can_delete')
  // @Delete('delete')
  // async deletePenaltyFee(@Query('id') id: string) {
  //   await this.penaltyFeeYearWiseService.removePenaltyFee(id);
  //   return {
  //     message: 'Penalty fee deleted successfully',
  //   };
  // }

  /**
   * Manually trigger the monthly 2% penalty increment
   * This endpoint allows testing the cron job functionality that runs on the first day of each month
   */
  @Form('PenaltyFee')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Manually trigger monthly 2% penalty increment' })
  @ApiResponse({ status: 200, description: 'Returns the result of the penalty increment operation' })
  @Post('trigger-monthly-increment')
  async triggerMonthlyIncrement() {
    return await this.penaltyFeeYearWiseService.triggerMonthlyIncrement();
  }

  /**
   * Manually trigger the January 1st penalty calculation
   * This endpoint allows testing the cron job functionality that runs on January 1st
   */
  @Form('PenaltyFee')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Manually trigger January 1st penalty calculation' })
  @ApiResponse({ status: 200, description: 'Returns the result of the January 1st penalty calculation' })
  @Post('trigger-january-calculation')
  async triggerJanuaryCalculation() {
    return await this.penaltyFeeYearWiseService.calculatePenaltiesOnRemainingAmounts();
  }

  /**
   * Test API for January 1st penalty calculation
   * This endpoint simulates what would happen on January 1st:
   * 1. Takes the current year's remaining total tax
   * 2. Calculates 2% of it
   * 3. Adds that to the penalty table organized by financial year
   */
  @Form('PenaltyFee')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Test January 1st penalty calculation with detailed logs' })
  @ApiResponse({ status: 200, description: 'Returns detailed results of the January 1st penalty calculation' })
  @Post('test-january-first-penalty')
  async testJanuaryFirstPenalty() {
    return await this.penaltyFeeYearWiseService.testJanuaryFirstPenalty();
  }

  /**
   * Test specific property (0722fe33-1362-4a55-829f-68a6f636bc76) for January 1st penalty calculation
   * This endpoint tests the penalty calculation for the specific property with detailed console logs
   */
  @Form('PenaltyFee')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Test January 1st penalty calculation for specific property 0722fe33-1362-4a55-829f-68a6f636bc76 with detailed console logs' })
  @ApiResponse({ status: 200, description: 'Returns detailed results of the January 1st penalty calculation for the specific property' })
  @Post('test-specific-property')
  async testSpecificProperty() {
    return await this.penaltyFeeYearWiseService.testJanuaryFirstPenaltyForSpecificProperty();
  }
}
