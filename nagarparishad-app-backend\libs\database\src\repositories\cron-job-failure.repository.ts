import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import { CronJobFailureEntity } from '../entities/cron-job-failure.entity';

@Injectable()
export class CronJobFailureRepository extends Repository<CronJobFailureEntity> {
  constructor(
    @InjectRepository(CronJobFailureEntity)
    private readonly cronJobFailureRepository: Repository<CronJobFailureEntity>,
  ) {
    super(
      cronJobFailureRepository.target,
      cronJobFailureRepository.manager,
      cronJobFailureRepository.queryRunner,
    );
  }

  /**
   * Record a failed job
   */
  async recordFailure(
    jobId: string,
    jobName: string,
    errorMessage: string,
    jobData?: any
  ): Promise<CronJobFailureEntity> {
    // Check if this job already has a failure record
    const existingFailure = await this.cronJobFailureRepository.findOne({
      where: { job_id: jobId }
    });

    if (existingFailure) {
      // Update existing record
      existingFailure.error_message = errorMessage;
      existingFailure.attempt_count += 1;
      existingFailure.last_attempt = new Date();
      existingFailure.job_data = jobData;
      existingFailure.status = 'failed';
      
      return await this.cronJobFailureRepository.save(existingFailure);
    } else {
      // Create new failure record
      const newFailure = this.cronJobFailureRepository.create({
        job_id: jobId,
        job_name: jobName,
        error_message: errorMessage,
        attempt_count: 1,
        last_attempt: new Date(),
        first_failure: new Date(),
        job_data: jobData,
        status: 'failed'
      });
      
      return await this.cronJobFailureRepository.save(newFailure);
    }
  }

  /**
   * Mark a job as resolved
   */
  async markAsResolved(jobId: string, resolutionNotes?: string): Promise<void> {
    await this.cronJobFailureRepository.update(
      { job_id: jobId },
      { 
        status: 'resolved',
        resolution_notes: resolutionNotes,
        updated_at: new Date()
      }
    );
  }

  /**
   * Mark a job as retrying
   */
  async markAsRetrying(jobId: string): Promise<void> {
    await this.cronJobFailureRepository.update(
      { job_id: jobId },
      { 
        status: 'retrying',
        updated_at: new Date()
      }
    );
  }

  /**
   * Get all failed jobs
   */
  async getFailedJobs(): Promise<CronJobFailureEntity[]> {
    return await this.cronJobFailureRepository.find({
      where: { status: 'failed' },
      order: { last_attempt: 'DESC' }
    });
  }

  /**
   * Get failed jobs by job name
   */
  async getFailedJobsByName(jobName: string): Promise<CronJobFailureEntity[]> {
    return await this.cronJobFailureRepository.find({
      where: { 
        job_name: jobName,
        status: 'failed'
      },
      order: { last_attempt: 'DESC' }
    });
  }

  /**
   * Get a specific failed job by ID
   */
  async getFailedJobById(jobId: string): Promise<CronJobFailureEntity | null> {
    return await this.cronJobFailureRepository.findOne({
      where: { job_id: jobId }
    });
  }

  /**
   * Clean up old resolved failures (older than specified days)
   */
  async cleanupOldResolvedFailures(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const result = await this.cronJobFailureRepository.delete({
      status: 'resolved',
      updated_at: LessThan(cutoffDate)
    });

    return result.affected || 0;
  }

  /**
   * Get failure statistics
   */
  async getFailureStatistics(): Promise<{
    totalFailed: number;
    totalRetrying: number;
    totalResolved: number;
    failuresByJobName: Array<{ jobName: string; count: number }>;
  }> {
    const [totalFailed, totalRetrying, totalResolved] = await Promise.all([
      this.cronJobFailureRepository.count({ where: { status: 'failed' } }),
      this.cronJobFailureRepository.count({ where: { status: 'retrying' } }),
      this.cronJobFailureRepository.count({ where: { status: 'resolved' } })
    ]);

    const failuresByJobName = await this.cronJobFailureRepository
      .createQueryBuilder('failure')
      .select('failure.job_name', 'jobName')
      .addSelect('COUNT(*)', 'count')
      .where('failure.status = :status', { status: 'failed' })
      .groupBy('failure.job_name')
      .getRawMany();

    return {
      totalFailed,
      totalRetrying,
      totalResolved,
      failuresByJobName: failuresByJobName.map(item => ({
        jobName: item.jobName,
        count: parseInt(item.count)
      }))
    };
  }

  /**
   * Get critical failures (jobs that have failed multiple times)
   */
  async getCriticalFailures(minAttempts: number = 3): Promise<CronJobFailureEntity[]> {
    return await this.cronJobFailureRepository.find({
      where: { 
        status: 'failed',
      },
      order: { attempt_count: 'DESC', last_attempt: 'DESC' }
    }).then(failures => 
      failures.filter(failure => failure.attempt_count >= minAttempts)
    );
  }
}
