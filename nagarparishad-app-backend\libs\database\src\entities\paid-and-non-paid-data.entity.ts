import {
  BaseEntity,
  Column,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PropertyEntity, Financial_year } from './index';

@Entity('paid_and_non_paid_data')
export class PaidAndNonPaidDataEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  paid_and_non_paid_data_id: string;

  @ManyToOne(() => PropertyEntity, (property) => property.property_id, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

  @ManyToOne(() => Financial_year, (financialYear) => financialYear.financial_year_id, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'financial_year_id' })
  financial_year: Financial_year;

  @Column({ type: 'varchar', nullable: false })
  financial_year_range: string;

  @Column({ type: 'varchar', nullable: true })
  name: string;

  @Column({ type: 'varchar', nullable: false })
  property_number: string;

  @Column({ type: 'varchar', nullable: true })
  old_property_number: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: false, default: 0 })
  total_tax: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: false, default: 0 })
  paid: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: false, default: 0 })
  remaining: number;

  @Column({ type: 'boolean', nullable: false, default: false })
  is_paid: boolean;

  /*
   * Create and Update Date Columns
   */
  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
