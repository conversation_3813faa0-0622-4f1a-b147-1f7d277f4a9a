import { SelectQueryBuilder, EntityMetadata } from 'typeorm';

export interface PaginationOptions {
  page?: number;
  limit?: number;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  showAll?: boolean;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  nextPageAvailable: boolean;
  prevPageAvailable: boolean;
}


export async function paginate<T>(
  queryBuilder: SelectQueryBuilder<T>,
  options: PaginationOptions,
  entityAlias: string,
): Promise<PaginatedResult<T>> {
  const {
    page = 1,
    limit = 10,
    filters = {},
    sortBy,
    sortOrder = 'DESC',
    showAll = false,
  } = options;

  // Apply filters if provided
  if (Array.isArray(filters) && filters.length > 0) {
    filters.forEach((filter) => {
      Object.keys(filter).forEach((key) => {
        if (filter[key]) {
          queryBuilder.andWhere(
            `LOWER(${entityAlias}.${key}) LIKE LOWER(:${key})`,
            {
              [key]: `%${filter[key]}%`.toLowerCase(),
            },
          );
        }
      });
    });
  }

  // 🛠️ Validate and apply sorting
  if (sortBy && sortOrder) {
    const metadata: EntityMetadata = queryBuilder.connection.getMetadata(queryBuilder.alias);

    const validFields = metadata.columns.map((col) => col.propertyName);

    if (validFields.includes(sortBy)) {
      queryBuilder.addOrderBy(`${entityAlias}.${sortBy}`, sortOrder);
    } else {
      console.warn(`Invalid sort field: ${sortBy},${validFields}`);
    }
  }

  let result: T[];
  let total: number;

  if (showAll) {
    result = await queryBuilder.getMany();
    total = result.length;
  } else {
    [result, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();
  }

  const totalPages = Math.ceil(total / limit);

  return {
    data: result,
    total,
    page: showAll ? 1 : page,
    limit: showAll ? total : limit,
    totalPages,
    nextPageAvailable: !showAll && page < totalPages,
    prevPageAvailable: !showAll && page > 1,
  };
}
