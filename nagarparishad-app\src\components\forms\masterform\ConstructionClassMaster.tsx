import WhiteContainer from "@/components/custom/WhiteContainer";
import React, { useContext, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ArrowUpDown } from "lucide-react";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen";
import { toast } from "@/components/ui/use-toast";
import { GlobalContext } from "@/context/GlobalContext";
import { ColumnDef } from "@tanstack/react-table";
import { MasterObjectColumnDef } from "@/model/zone-master";
import { MASTER } from "@/constant/config/api.config";
import { useConstructionClassController } from "@/controller/master/ConstructionController";
import ConstructionMasterForm from "../ConstructionMasterForm";
import AddNewBtn from "../../globalcomponent/AddNewForm";
import { ResponseData } from "@/model/auth/authServices";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

const ConstructionClassMaster = () => {
  const { t } = useTranslation();
  const userRef = useRef(null);
  const { constructionClassList, deleteConstructionClass } =
    useConstructionClassController();

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Master, FormName.ConstructionClassMaster, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Master, FormName.ConstructionClassMaster, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Master, FormName.ConstructionClassMaster, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Master, FormName.ConstructionClassMaster, Action.CanDelete);

  const dynamicValues = {
    name: t("construction.classNameLabel"),
  };

  const { setMasterComponent, setOpen } = useContext(GlobalContext);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any | null>(null);

  function handleEdit(item: any): void {
    setOpen(true);
    setMasterComponent(
      <ConstructionMasterForm
        btnTitle={"construction.updateBtn"}
        editData={item && item}
      />,
    );
    if (userRef.current) {
      userRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }

  function handleDelete(item: any): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  const handleConfirmDelete = () => {
    if (selectedItem) {
      deleteConstructionClass(selectedItem.constructionClass_id, {
        onSuccess: (response: ResponseData) => {
          if (response.statusCode && response.statusCode === 200) {
            toast({
              title: t("api.formdelete", dynamicValues),
              variant: "success",
            });
          } else {
            toast({
              title: response.message,
            });
          }
        },
        onError: (error) => {
          toast({
            title: error.message,
            variant: "destructive",
          });
        },
      });
    }
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const columns: ColumnDef<MasterObjectColumnDef>[] = [
    {
      accessorKey: `${t("SrNo")}`,
      header: `${t("SrNo")}`,
     cell: ({ row }: { row: any })  => <div className="capitalize">{row.index + 1}</div>,
    },
    {
      accessorKey: "constructionClassName",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("construction.constructionLabel")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
     cell: ({ row }: { row: any })  => (
        <div className="">{row.original?.constructionClassName}</div>
      ),
    },
    {
      accessorKey: "constructionClassMarathi",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("construction.classNameMarathiLabel")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
     cell: ({ row }: { row: any })  => (
        <div className="">{row.original?.constructionClassMarathi}</div>
      ),
    },
    {
      accessorKey: "values",
      header: ({ column }) => (
        <Button
          className="px-0 justify-start text-base font-semibold"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          {t("construction.valueLabel")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
     cell: ({ row }: { row: any })  => <div className="">{row.original?.values}</div>,
    },
    ...(canUpdate || CanDelete
      ? [
          {
            accessorKey: `${t("Actions")}`,
            enableHiding: true,
            cell: ({ row }: { row: any }) => (
              <>
                {canUpdate && (
                  <Button
                    variant="submit"
                    className="h-8 w-8 p-0 justify-center"
                    onClick={() => handleEdit(row?.original)}
                  >
                    {t("table.edit")}
                  </Button>
                )}
                {CanDelete && (
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0 justify-center ml-2  !border-BlueText !text-BlueText"
                    onClick={() => handleDelete(row?.original)}
                  >
                    {t("table.delete")}
                  </Button>
                )}
              </>
            ),
          },
        ]
      : []),
  ];

  const MasterType: string = MASTER.CONSTRUCTION;

  return (
    <div
      className="bg-Secondary w-full  min-h-screen h-full p-6"
      ref={userRef && userRef}
    >
      <p className="w-full flex items-center justify-between ml-2  text-[18px] font-semibold mb-2">
        {t("construction.formTitle")}
      </p>

      {CanCreate && <WhiteContainer>
        {MasterType && <AddNewBtn masterType={MASTER.CONSTRUCTION} />}
      </WhiteContainer>}
      <WhiteContainer className="mt-5">
        <TanStackTable
          columns={columns}
          data={constructionClassList}
          masterType={MASTER.CONSTRUCTION}
          searchColumn="Values"
          searchKey="searchConstructionClass"
        />
      </WhiteContainer>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={selectedItem.constructionClassName}
          onDelete={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default ConstructionClassMaster;
