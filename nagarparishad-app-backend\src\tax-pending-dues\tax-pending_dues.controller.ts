import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Query,
    Delete,
    UseInterceptors,
    BadRequestException,
    UploadedFile,
    HttpStatus,
    InternalServerErrorException,
    Res,
  } from '@nestjs/common';
  import {
    CreateTaxPendingDuesDto,
    TaxPendingDuesDto,
    UpdateTaxPendingDuesDto,
  } from './dto/TaxPendingDues.dto';
  import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import { TaxPendingDuesService } from './tax-pending_dues.service';
import { Response } from 'express';

  
  @ApiTags('Tax Pending Dues')
  @Controller('tax-pending-dues')
  export class TaxPendingDuesController {
    constructor(private readonly taxPendingDuesService: TaxPendingDuesService) {}
  
    @ApiOperation({ summary: 'Upload CSV file' })
    @ApiResponse({ status: 200, description: 'CSV file uploaded and processed successfully' })
    @ApiResponse({ status: 400, description: 'Invalid file format' })
    @Post('upload-file')
    @UseInterceptors(
      FileInterceptor('file', {
        storage: memoryStorage(),
        limits: { files: 1, fileSize: 1024 * 1024 * 5 }, // 5 MB
        fileFilter: (req, file, cb) => {
          const allowedMimeTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.ms-excel', // .xls
            'text/csv', // .csv
          ];
          if (!allowedMimeTypes.includes(file.mimetype)) {
            cb(new BadRequestException('Invalid file type'), false);
          } else if (file?.size > 1024 * 1024 * 5) {
            // 5MB
            cb(
              new BadRequestException('Max File Size Reached. Max Allowed: 5MB'),
              false,
            );
          }
          cb(null, true);
        },
      }),
    )
    async uploadCsvFile(@UploadedFile() file: Express.Multer.File): Promise<any> {
      try {
        // console.log("fileeeein in")
        const results = await this.taxPendingDuesService.processFile(file);
        return {
          error: false,
          statusCode: HttpStatus.OK,
          message: 'File uploaded and processed successfully',
          data: results,
        };
      } catch (e) {
        throw new InternalServerErrorException(
          e?.message || 'Internal Server Error',
        );
      }
    }
      @Get('/compare-csv')
      async exportCsvFile(
        @Query('ward_number') wardNumber: string,
        @Res() res: Response,
      ) {
        const csvData = await this.taxPendingDuesService.exportCsvData(wardNumber);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader(
          'Content-Disposition',
          `attachment; filename=compare.csv`,
        );
        res.status(HttpStatus.OK).send(csvData);
      }
  
  }
  