import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Patch,
  Param,
  Delete,
  Query,
  ValidationPipe,
} from '@nestjs/common';
import { PropertyTypeMasterService } from './property-type_master.service';
import {
  CreatePropertyTypeMasterDto,
  PropertyTypeMasterIdDto,
  UpdatePropertyTypeMasterDto,
} from './dto/property-type_master.dto';
import { ChangePropertyTypeClassDto } from './dto/property-type_class.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@ApiTags('Property Type Master')
@Controller('property-type-master')
export class PropertyTypeMasterController {
  constructor(
    private readonly propertyTypeMasterService: PropertyTypeMasterService,
  ) {}

  
  @Form('PropertyType')
  @Permissions('can_write')
  @ApiOperation({ summary: 'Create a new Property Type' })
  @ApiResponse({
    status: 201,
    description: 'The Property Type has been successfully created',
  })
  @Post()
  create(@Body() createPropertyTypeMasterDto: CreatePropertyTypeMasterDto) {
    return this.propertyTypeMasterService.create(createPropertyTypeMasterDto);
  }

  
  // @Form('Property')
  // @Permissions('can_read')
  @ApiOperation({ summary: 'Get all Property Type' })
  @ApiResponse({ status: 200, description: 'Returns all Property Type' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.propertyTypeMasterService.findAll();
  }

  
  @Form('PropertyType')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one Property Type' })
  @ApiResponse({ status: 200, description: 'Returns Single Property Type' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() propertyTypeMaster: PropertyTypeMasterIdDto) {
    return this.propertyTypeMasterService.findOne(propertyTypeMaster);
  }

  
  @Form('PropertyType')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Update a Property Type by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Property Type has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Property Type not found' })
  @Patch()
  update(
    @Query() propertyTypeMaster: PropertyTypeMasterIdDto,
    @Body() updatePropertyTypeMasterDto: UpdatePropertyTypeMasterDto,
  ) {
    return this.propertyTypeMasterService.update(
      propertyTypeMaster,
      updatePropertyTypeMasterDto,
    );
  }

  
  @Form('PropertyType')
  @Permissions('can_delete')
  @ApiOperation({ summary: 'Delete a Property Type by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Property Type has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Property Type not found' })
  @Delete()
  remove(@Query() propertyTypeMaster: PropertyTypeMasterIdDto) {
    return this.propertyTypeMasterService.remove(propertyTypeMaster);
  }

  
  @Form('PropertyType')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Change Property Type Class' })
  @ApiResponse({
    status: 200,
    description: 'The Property Type  class has been successfully updated',
  })
  @Put('change-type')
  updateTypeClass(@Query(new ValidationPipe({ transform: true })) ChangePropertyTypeClass: ChangePropertyTypeClassDto) {
    return this.propertyTypeMasterService.updatePropertyTypeClass(ChangePropertyTypeClass);
  }

  
  // @Form('PropertyType')
  // @Permissions('can_read')
  @ApiOperation({ summary: 'Get Property Type Class' })
  @ApiResponse({
    status: 200,
    description: 'retrieved Property Type class successfully',
  })
  @Get('type-class')
  getTypeClass(@Query('propertyType_id') propertyType_id: string) {
    return this.propertyTypeMasterService.getPropertyTypeClass(propertyType_id);
  }

  
  @Form('PropertyType')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all Property Type Class' })
  @ApiResponse({
    status: 200,
    description: 'retrieved all Property Type class successfully',
  })
  @Get('all-classes')
  getAllTypeClass() {
    return this.propertyTypeMasterService.findAllGroupedByClass();
  }

}
