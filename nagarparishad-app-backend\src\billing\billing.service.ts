import { PaginatedResult } from './../../libs/helpers/src/Pagination/paginate';
import {
  Financial_yearRepository,
  ImportPropertyMasterRepository,
  PaidDataRepository,
  repositories,
  PenaltyFeeYearWiseRepository
} from './../../libs/database/src/repositories/index';
import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';

import { Response } from 'express';
import {
  Tax_PropertyWiseRepository,
  PropertyMasterRepository,
} from 'libs/database/repositories';
import { Tax_PropertyRepository } from 'libs/database/repositories';
import { WarshikKarRepository } from 'libs/database/repositories';
import { MilkatKareRepository } from 'libs/database/repositories';
import { Tax_billGenerateService } from 'src/tax-calculate/tax-billGenarete.service';

import {
  ClientProxyFactory,
  Transport,
  ClientProxy,
} from '@nestjs/microservices';
import { S3 } from 'aws-sdk';
import * as AWS from 'aws-sdk';
import path from 'path';
import fs from 'fs';
import ejs from 'ejs';
import axios from 'axios';
import FormData from 'form-data';
import archiver from 'archiver';
import * as rimraf from 'rimraf';

const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
import { TAX_TYPES } from '@helper/helpers/tax-types.helper';
import { AnnualKarAkaraniService } from 'src/annual-kar-akarani/annual-kar-akarani.service';
import { json } from 'stream/consumers';

AWS.config.update({ region: process.env.AWS_REGION });
@Injectable()
export class BillingService {
  // create(createBillingDto: CreateBillingDto) {
  //   return 'This action adds a new billing';
  // }
  constructor(
    private readonly tax_PropertyWiseRepository: Tax_PropertyWiseRepository,
    private readonly tax_PropertyRepository: Tax_PropertyRepository,
    private readonly warshikKarRepository: WarshikKarRepository,
    private readonly milkatKareRepository: MilkatKareRepository,
    private readonly importRepository: ImportPropertyMasterRepository,
    private readonly tax_billGenerateService: Tax_billGenerateService,
    private readonly propertyMasterRepository: PropertyMasterRepository,
    private readonly financialYearRepo: Financial_yearRepository,
        private readonly penaltyFeeYearWiseRepository: PenaltyFeeYearWiseRepository,

    private readonly paidDataRepo: PaidDataRepository,
        private readonly annualKarAkaraniService: AnnualKarAkaraniService,
    
  ) {}
  // Function to convert English digits to Marathi digits
  convertToMarathiDigits(value: string | number): string {
    const marathiDigits = {
      '0': '०',
      '1': '१',
      '2': '२',
      '3': '३',
      '4': '४',
      '5': '५',
      '6': '६',
      '7': '७',
      '8': '८',
      '9': '९',
    };
    return String(value)
      .split('')
      .map((char) => marathiDigits[char] || char)
      .join('');
  }

  // Function to convert dates to Marathi format
  convertDateToMarathi(dateString: string): string {
    const [year, month, day] = dateString.split('-');
    return `${this.convertToMarathiDigits(day)}-${this.convertToMarathiDigits(month)}-${this.convertToMarathiDigits(year)}`;
  }

  // Function to convert dates to Marathi format
  convertDateToMarathi_andReturnYYYY(dateString: string): string {
    const year = dateString.split('-')[0];
    return this.convertToMarathiDigits(year);
  }

  // Recursive function to convert all numeric values in the JSON
  async convertJson(jsonObj: any): Promise<any> {
    if (typeof jsonObj === 'object' && jsonObj !== null) {
      for (let key in jsonObj) {

         if (key === 'createdAt') {
        continue;
      }
            if (key === 'recordCreatedTime') {
        continue;
      }
        // Add a new key for total_tax_in_marathi
        if (key.toLowerCase() === 'total_tax' && !isNaN(jsonObj[key])) {
          jsonObj['total_tax_in_marathi'] =
            await this.convertNumberToMarathiWords(parseFloat(jsonObj[key]));
        }
        if (typeof jsonObj[key] === 'number' || !isNaN(jsonObj[key])) {
          jsonObj[key] = this.convertToMarathiDigits(jsonObj[key]);
        } else if (key.toLowerCase().includes('construction_end_date')) {
          jsonObj[key] = this.convertDateToMarathi_andReturnYYYY(jsonObj[key]);
        } else if (key.toLowerCase().includes('date')) {
          jsonObj[key] = this.convertDateToMarathi(jsonObj[key]);
        } else if (typeof jsonObj[key] === 'object') {
          jsonObj[key] = await this.convertJson(jsonObj[key]); // Ensure recursive processing
        }
      }
    }
    return jsonObj;
  }

  async get_bill_pdf_via_aws(res: Response) {
    //  res.send('This action returns all billing');
    try {
      let billData = await this.getBillData('66');
      billData = await this.convertJson(billData); // Convert numeric values to Marathi digits

      const s3 = new S3();
      const bucketName = process.env.S3_PRIVATE_BUCKET_NAME || 'spms-ms';
      const fileName = `bill-data-${Date.now()}.json`;
      const params = {
        Bucket: bucketName,
        Key: fileName,
        Body: JSON.stringify(billData),
        ContentType: 'application/json',
      };

      await s3.upload(params).promise();

      return res.status(200).send(billData);

      const client: ClientProxy = ClientProxyFactory.create({
        transport: Transport.TCP,
        options: {
          host: '127.0.0.1',
          port: 3002,
        },
      });

      const pattern = { cmd: 'generate_pdf' };
      const payload = {
        htmlTemplate: '<html><body><h1>Sample PDF</h1></body></html>',
        template_fileName: 'sample',
        data_filename: fileName,
      };

      const result = await client.send<Buffer>(pattern, payload).toPromise();

      // Set the content type to PDF
      res.contentType('application/pdf');
      // Send the PDF buffer as a response
      res.send(Buffer.from(result));
    } catch (error) {
      console.error('Error fetching PDF:', error);
      res.status(500).send('Error generating PDF');
    }
  }
  async get_bill_pdf(params: any, res: Response) {
    try {
      let { billNo, ward, limit, page } = params;

      // Set default values for limit and page if they are not provided
      const defaultLimit = Number.MAX_SAFE_INTEGER; // A large number to fetch all records
      const defaultPage = 1;

      limit = limit ? parseInt(limit) : defaultLimit;
      page = page ? parseInt(page) : defaultPage;

      let billData = null;

      if (billNo) {
        billData = await this.getBillData(billNo);
        billData = await this.convertJson(billData); // Convert numeric values to Marathi digits
      } else if (ward) {
        billData = await this.getBillDataByward(ward, limit, page);
        billData = await this.convertJson(billData); // Convert numeric values to Marathi digits
      }

      //       // return res.status(200).send(billData);
      // res.send(billData)
      const client: ClientProxy = ClientProxyFactory.create({
        transport: Transport.TCP,
        options: {
          host: '127.0.0.1',
          port: 3002,
        },
      });

      const pattern = { cmd: 'save_pdf' };
      const payload = {
        htmlTemplate: 'bill-template_6.ejs',
        fileData: billData,
        apiSecretKey: process.env.API_SECRET_KEY,
        ward: ward, // Pass the ward parameter
        page: page, // Pass the page parameter
        limit: limit, // Pass the limit parameter
      };

      const result = await client.send<Buffer>(pattern, payload).toPromise();

      // Set the content type to PDF
      res.contentType('application/pdf');
      // Send the PDF buffer as a response
      res.send(Buffer.from(result));
    } catch (error) {
      console.error('Error fetching PDF:', error);
      res.status(500).send('Error generating PDF');
    }
  }

  async get_bill_pdf_ByNumber(propertyNumbers: any, res: Response) {
    //  res.send('This action returns all billing');
    try {
      let billData = null;
      if (!propertyNumbers || propertyNumbers.length === 0) {
        return res
          .status(400)
          .send({ message: 'No property numbers provided' });
      }

      // Fetch bill data for each property number
      billData = await this.getBillDataByPropertyNUmber(propertyNumbers);

      billData = await this.convertJson(billData); // Convert numeric values to Marathi digits

      return res.status(200).send(billData);

      const client: ClientProxy = ClientProxyFactory.create({
        transport: Transport.TCP,
        options: {
          host: '127.0.0.1',
          port: 3002,
        },
      });

      const pattern = { cmd: 'save_pdf' };
      const payload = {
        htmlTemplate: 'bill-template_6.ejs',
        fileData: billData,
        apiSecretKey: process.env.API_SECRET_KEY,
      };

      const result = await client.send<Buffer>(pattern, payload).toPromise();

      res.contentType('application/pdf');
      res.send(Buffer.from(result));
    } catch (error) {
      console.error('Error fetching PDF:', error);
      res.status(500).send('Error generating PDF');
    }
  }
  async get_bill_pdf_ByPropertyNumber(
    params: { propertyNumber: string },
    res: Response,
  ) {
    const { propertyNumber } = params;

    try {
      // Determine if the provided number is a propertyNumber or oldPropertyNumber
      let property = await this.propertyMasterRepository.findOne({
        where: { propertyNumber: propertyNumber },
      });

      if (!property) {
        // If not found, check if it's an oldPropertyNumber
        property = await this.propertyMasterRepository.findOne({
          where: { old_propertyNumber: propertyNumber },
        });
      }

      if (!property) {
        return res.status(404).json({ message: 'Property not found' });
      }

      const currentFinancialYear =
        await this.financialYearRepo.getCurrentFinancialYear();
      const actualPropertyNumber = property.propertyNumber;
      let financial_year = {
        financial_year: currentFinancialYear.financial_year_range,
        propertyNumber: actualPropertyNumber,
      }; // Assuming Tax_propertyFYDto has a 'year' property
      const billData =
        await this.tax_billGenerateService.generate_billsForPropertyNumber(
          financial_year,
        );

    //  const latestBillData: any =
    //     await this.annualKarAkaraniService.getWarshikKarAkarniForBillData(
    //       actualPropertyNumber,
    //       'propertyNumber',
    //       currentFinancialYear.financial_year_range,
    //     );    
        
        
        const data =
        await this.warshikKarRepository.get_data_for_bill_By_PropertyNUmber(
          actualPropertyNumber,
        );


  const penaltyFees =
        await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(
          property.property_id,
        );

      if (penaltyFees && penaltyFees.length > 0) {
        let currentPenaltyValue = 0;
        let previousPenaltySum = 0;
        let currentActualValue = 0;
        let previousActualValue = 0;

        // Calculate current and previous penalties
        penaltyFees.forEach((penalty) => {
          if (penalty.financial_year === currentFinancialYear.financial_year_range) {
            // Current year penalty - use the latest calculated penalty value
            currentPenaltyValue = Number(penalty.penalty_value || 0);
            currentActualValue = Number(penalty.actual_value || 0);
          } else {
            // Previous years penalties - sum them up
            previousPenaltySum += Number(penalty.penalty_value || 0);
            previousActualValue += Number(penalty.actual_value || 0);
          }
        });

        // Round the values
        currentPenaltyValue = Math.round(currentPenaltyValue);
        previousPenaltySum = Math.round(previousPenaltySum);
        currentActualValue = Math.round(currentActualValue);
        previousActualValue = Math.round(previousActualValue);

        // Update tax_type_9 values with the latest penalty data
        data[0].tax_type_9_current = currentPenaltyValue;
          data[0].tax_type_9_previous = previousPenaltySum;
         data[0].tax_type_9 =
          currentPenaltyValue + previousPenaltySum;

        // First, update the current and previous tax totals
          data[0].total_tax_current = Math.max(
          0,
          Math.round(
            (Number(    data[0].total_tax_current) || 0) + (currentPenaltyValue),

          ),
        );

            data[0].total_tax_previous = Math.max(
          0,
          Math.round(

              previousActualValue + previousPenaltySum,
          ),
        );

        // Then, calculate the total tax as the sum of current and previous
           data[0].total_tax =
              data[0].total_tax_current +
              data[0].total_tax_previous;

        console.log(`Updated penalty for bill data - property ${ property.property_id}:
          Penalty Values - Current=${currentPenaltyValue}, Previous=${previousPenaltySum}, Total=${currentPenaltyValue + previousPenaltySum}
          Actual Values - Current=${currentActualValue}, Previous=${previousActualValue}, Total=${currentActualValue + previousActualValue}`);
      }


              console.log("datadatadata-->  updated data",data)





      const ownerDetail = await this.importRepository.find({
        where: { propertyNumber: actualPropertyNumber },
      });


      const updatedData = data.map((item) => ({
        ...item,
        ownerDetail,
      }));

      const getPaidData =
        await this.paidDataRepo.findAndSumPaidDataByFinancialYearAndProperty(
          currentFinancialYear.financial_year_range,
          actualPropertyNumber,
        );
console.log("datadatadata--> getPaidData",getPaidData)

      if (updatedData && getPaidData) {
        // Iterate over the warshikKar array in getData
        updatedData.forEach((warshikKar) => {
          // Subtract the corresponding values from getPaidData, ensuring no negative values
          warshikKar.all_property_tax_sum_total = Math.max(
            0,
            warshikKar.all_property_tax_sum_total -
              (getPaidData.all_property_tax_sum || 0),
          );

          warshikKar.all_property_tax_sum = Math.max(
            0,
            warshikKar.all_property_tax_sum -
              (getPaidData.all_property_tax_sum || 0),
          );

          warshikKar.all_property_tax_sum_current = Math.max(
            0,
            warshikKar.all_property_tax_sum_current -
              (getPaidData.all_property_tax_sum_curr || 0),
          );

          // Subtract tax_type values
          let totalTaxTypeCurr = 0;
          let totalTaxTypePrev = 0;

          for (let i = 1; i <= 10; i++) {
            warshikKar[`tax_type_${i}`] = Math.max(
              0,
              warshikKar[`tax_type_${i}`] - (getPaidData[`tax_type_${i}`] || 0),
            );

            warshikKar[`tax_type_${i}_current`] = Math.max(
              0,
              warshikKar[`tax_type_${i}_current`] -
                (getPaidData[`tax_type_${i}_curr`] || 0),
            );

            warshikKar[`tax_type_${i}_previous`] = Math.max(
              0,
              warshikKar[`tax_type_${i}_previous`] -
                (getPaidData[`tax_type_${i}_prev`] || 0),
            );

            // Accumulate the sums for total_tax_current and total_tax_previous
            totalTaxTypeCurr += getPaidData[`tax_type_${i}_curr`] || 0;
            totalTaxTypePrev += getPaidData[`tax_type_${i}_prev`] || 0;
          }

          // Subtract other_tax_sum_tax values
          warshikKar.other_tax_sum_tax = Math.max(
            0,
            warshikKar.other_tax_sum_tax -
              (getPaidData.other_tax_sum_tax_curr || 0),
          );

          warshikKar.other_tax_sum_tax_current = Math.max(
            0,
            warshikKar.other_tax_sum_tax_current -
              (getPaidData.other_tax_sum_tax_curr || 0),
          );

          warshikKar.other_tax_sum_tax_previous = Math.max(
            0,
            warshikKar.other_tax_sum_tax_previous -
              (getPaidData.other_tax_sum_tax_prev || 0),
          );

          // Subtract total_tax values

          warshikKar.total_tax = Math.max(
            0,
            warshikKar.total_tax - (getPaidData.total_amount || 0)-(getPaidData.other_discount || 0),
          );
          // Calculate total_tax_current and total_tax_previous using the accumulated sums
          warshikKar.total_tax_current = Math.max(
            0,
            warshikKar.total_tax_current -
              (totalTaxTypeCurr + (getPaidData.all_property_tax_sum_curr || 0)),
          );

          warshikKar.total_tax_previous = Math.max(
            0,
            warshikKar.total_tax_previous -
              (totalTaxTypePrev + (getPaidData.all_property_tax_sum_prev || 0)),
          );
        });
      }


      const tax_types = {
        tax_type_6: TAX_TYPES.tax_type_6, // दिवाबत्ती कर
        tax_type_7: TAX_TYPES.tax_type_7, // आरोग्य कर (corrected from 'आरोग्य क')
        tax_type_8: TAX_TYPES.tax_type_8, // पडसर कर
        tax_type_1: TAX_TYPES.tax_type_1, // वृक्ष उपकर
        tax_type_2: TAX_TYPES.tax_type_2, // शिक्षण उपकर
        tax_type_3: TAX_TYPES.tax_type_3, // रोजगार हमी कर
        tax_type_10: TAX_TYPES.tax_type_10, // अग्निशमन फी
        tax_type_4: TAX_TYPES.tax_type_4, // घनकचरा शुल्क
        tax_type_5: TAX_TYPES.tax_type_5, // अनधिकृत शास्ती कर
        tax_type_9: TAX_TYPES.tax_type_9, // दंड
      };
      console.log("updatedData[0]",updatedData[0].property.property_owner_details)

      let marathiData = await this.convertJson(updatedData[0]); // Convert numeric values to Marathi digits
      const templatePath = path.join(
        __dirname,
        'templates',
        'bill-template_6.ejs',
      );
      const template = fs.readFileSync(templatePath, 'utf-8');
      const htmlOutput = ejs.render(template, {
        fileData: marathiData,
        tax_types: tax_types,
      });

      const generatedHtmlDir = path.join(
        __dirname,
        'templates',
        'generatedhtml',
      );
      if (!fs.existsSync(generatedHtmlDir)) {
        fs.mkdirSync(generatedHtmlDir, { recursive: true });
      }

      const filePath = path.join(generatedHtmlDir, `index.html`);
      fs.writeFileSync(filePath, htmlOutput); // Ensure the HTML file is saved

      const formData = new FormData();
      formData.append('html_file', fs.createReadStream(filePath), 'index.html');

      // Add the additional parameters
      // formData.append('paper_width', '27');
      // formData.append('paper_height', '34');
      // formData.append('scale', '1.0');
      // formData.append('margin_top', '0.4');
      // formData.append('margin_bottom', '0.4');
      // formData.append('margin_left', '0.4');
      // formData.append('margin_right', '0.4');
      // formData.append('singlePage', 'true');

      formData.append('paperWidth	', '10'); // A4 width in mm
      formData.append('paperHeight', '12'); // A4 height in mm (or increase if needed)
      formData.append('scale', '1.0'); // Scale down content to fit
      formData.append('marginTop', '0.3');
      formData.append('marginBottom', '0.3');
      formData.append('marginLeft', '0.3');
      formData.append('marginRight', '0.3');

      // Optional: to prevent page breaks
      formData.append('prefer_css_page_size', 'false');
      formData.append('landscape', 'false');

      const apiUrl = process.env.CONVERT_SINGLEHTML_TO_PDF;

      const response = await axios.post(apiUrl, formData, {
        headers: {
          ...formData.getHeaders(),
        },
        responseType: 'arraybuffer',
      });

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader(
        'Content-Disposition',
        'inline; filename="NamunaEightReport.pdf"',
      );
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      return res.status(200).send(response.data);
    } catch (error) {
      console.error('Error:', error);
      res
        .status(500)
        .json({ message: 'Internal Server Error', error: error.message });
    }
  }

  // async get_bill_pdf_ByPropertyNumber(
  //   params: { propertyNumber: string },
  //   res: Response,
  // ) {
  //   const { propertyNumber } = params;

  //   try {
  //     // Determine if the provided number is a propertyNumber or oldPropertyNumber
  //     let property = await this.propertyMasterRepository.findOne({
  //       where: { propertyNumber: propertyNumber },
  //     });

  //     if (!property) {
  //       // If not found, check if it's an oldPropertyNumber
  //       property = await this.propertyMasterRepository.findOne({
  //         where: { old_propertyNumber: propertyNumber },
  //       });
  //     }

  //     if (!property) {
  //       return res.status(404).json({ message: 'Property not found' });
  //     }

  //     const currentFinancialYear =
  //       await this.financialYearRepo.getCurrentFinancialYear();
  //     const actualPropertyNumber = property.propertyNumber;
  //     let financial_year = {
  //       financial_year: currentFinancialYear.financial_year_range,
  //       propertyNumber: actualPropertyNumber,
  //     }; // Assuming Tax_propertyFYDto has a 'year' property
  //     const billData =
  //       await this.tax_billGenerateService.generate_billsForPropertyNumber(
  //         financial_year,
  //       );
  //     const data =
  //       await this.warshikKarRepository.get_data_for_bill_By_PropertyNUmber(
  //         actualPropertyNumber,
  //       );

  //     const ownerDetail = await this.importRepository.find({
  //       where: { propertyNumber: actualPropertyNumber },
  //     });

  //     const updatedData = data.map((item) => ({
  //       ...item,
  //       ownerDetail,
  //     }));

  //     const getPaidData =
  //       await this.paidDataRepo.findAndSumPaidDataByFinancialYearAndProperty(
  //         currentFinancialYear.financial_year_range,
  //         actualPropertyNumber,
  //       );

  //     console.log('ownerDetail updatedData tttt', updatedData[0], getPaidData);

  //     if (updatedData && getPaidData) {
  //       // Iterate over the warshikKar array in getData
  //       updatedData.forEach((warshikKar) => {
  //         // Subtract the corresponding values from getPaidData, ensuring no negative values
  //         warshikKar.all_property_tax_sum_total = Math.max(
  //           0,
  //           warshikKar.all_property_tax_sum_total -
  //             (getPaidData.all_property_tax_sum || 0),
  //         );

  //         warshikKar.all_property_tax_sum = Math.max(
  //           0,
  //           warshikKar.all_property_tax_sum -
  //             (getPaidData.all_property_tax_sum || 0),
  //         );

  //         warshikKar.all_property_tax_sum_current = Math.max(
  //           0,
  //           warshikKar.all_property_tax_sum_current -
  //             (getPaidData.all_property_tax_sum_curr || 0),
  //         );

  //         // Subtract tax_type values
  //         let totalTaxTypeCurr = 0;
  //         let totalTaxTypePrev = 0;

  //         for (let i = 1; i <= 10; i++) {
  //           warshikKar[`tax_type_${i}`] = Math.max(
  //             0,
  //             warshikKar[`tax_type_${i}`] - (getPaidData[`tax_type_${i}`] || 0),
  //           );

  //           warshikKar[`tax_type_${i}_current`] = Math.max(
  //             0,
  //             warshikKar[`tax_type_${i}_current`] -
  //               (getPaidData[`tax_type_${i}_curr`] || 0),
  //           );

  //           warshikKar[`tax_type_${i}_previous`] = Math.max(
  //             0,
  //             warshikKar[`tax_type_${i}_previous`] -
  //               (getPaidData[`tax_type_${i}_prev`] || 0),
  //           );

  //           // Accumulate the sums for total_tax_current and total_tax_previous
  //           totalTaxTypeCurr += getPaidData[`tax_type_${i}_curr`] || 0;
  //           totalTaxTypePrev += getPaidData[`tax_type_${i}_prev`] || 0;
  //         }

  //         // Subtract other_tax_sum_tax values
  //         warshikKar.other_tax_sum_tax = Math.max(
  //           0,
  //           warshikKar.other_tax_sum_tax -
  //             (getPaidData.other_tax_sum_tax_curr || 0),
  //         );

  //         warshikKar.other_tax_sum_tax_current = Math.max(
  //           0,
  //           warshikKar.other_tax_sum_tax_current -
  //             (getPaidData.other_tax_sum_tax_curr || 0),
  //         );

  //         warshikKar.other_tax_sum_tax_previous = Math.max(
  //           0,
  //           warshikKar.other_tax_sum_tax_previous -
  //             (getPaidData.other_tax_sum_tax_prev || 0),
  //         );

  //         // Subtract total_tax values
  //         warshikKar.total_tax = Math.max(
  //           0,
  //           warshikKar.total_tax - (getPaidData.total_amount || 0),
  //         );

  //         // Calculate total_tax_current and total_tax_previous using the accumulated sums
  //         warshikKar.total_tax_current = Math.max(
  //           0,
  //           warshikKar.total_tax_current -
  //             (totalTaxTypeCurr + (getPaidData.all_property_tax_sum_curr || 0)),
  //         );

  //         warshikKar.total_tax_previous = Math.max(
  //           0,
  //           warshikKar.total_tax_previous -
  //             (totalTaxTypePrev + (getPaidData.all_property_tax_sum_prev || 0)),
  //         );
  //       });
  //     } else {
  //     }

  //     console.log('ownerDetail', ownerDetail);
  //     console.log(
  //       'ownerDetail updatedData',
  //       updatedData[0].property.property_owner_details,
  //     );

  //     const tax_types = {
  //       tax_type_6: 'दिवाबत्ती कर',
  //       tax_type_7: 'आरोग्य कर',
  //       tax_type_8: 'पडसर कर',
  //       tax_type_1: 'वृक्ष उपकर',
  //       tax_type_2: 'शिक्षण उपकर',
  //       tax_type_3: 'रोजगार हमी कर',
  //       tax_type_10: 'अग्निशमन फी',
  //       tax_type_4: 'घनकचरा शुल्क',
  //       tax_type_5: 'अनधिकृत शास्ती कर',
  //       tax_type_9: 'दंड',
  //     };
  //     let marathiData = await this.convertJson(updatedData[0]); // Convert numeric values to Marathi digits

  //     const templatePath = path.join(
  //       __dirname,
  //       'templates',
  //       'bill-template_6.ejs',
  //     );
  //     const template = fs.readFileSync(templatePath, 'utf-8');

  //     const htmlOutput = ejs.render(template, {
  //       fileData: marathiData,
  //       tax_types: tax_types,
  //     });
  //     // res.send(htmlOutput);
  //     const generatedHtmlDir = path.join(
  //       __dirname,
  //       'templates',
  //       'generatedhtml',
  //     );
  //     if (!fs.existsSync(generatedHtmlDir)) {
  //       fs.mkdirSync(generatedHtmlDir, { recursive: true });
  //     }

  //     const filePath = path.join(generatedHtmlDir, 'index.html');

  //     fs.writeFileSync(filePath, htmlOutput);

  //     const formData = new FormData();
  //     formData.append('file', fs.createReadStream(filePath), 'index.html');

  //     const apiUrl = process.env.PDF_URL;

  //     const response = await axios.post(apiUrl, formData, {
  //       headers: {
  //         ...formData.getHeaders(),
  //       },
  //     });

  //     res
  //       .status(200)
  //       .json({ message: 'File sent to API', apiResponse: htmlOutput });
  //   } catch (error) {
  //     console.error('Error:', error);
  //     res
  //       .status(500)
  //       .json({ message: 'Internal Server Error', error: error.message });
  //   }
  // }

  async getBillDataByPropertyNUmber(propertyNumbers: any[]) {
    let getData = []; // Initialize as an empty array
    for (const propertyNumber of propertyNumbers) {
      const data =
        await this.warshikKarRepository.get_data_for_bill_By_PropertyNUmber(
          propertyNumber,
        );
      const ownerDetail = await this.importRepository.find({
        where: { propertyNumber: propertyNumber },
      });
      const updatedData = data.map((item) => ({
        ...item,
        ownerDetail,
      }));

      getData.push(...updatedData);
    }
    if (!getData) {
      throw new NotFoundException('Data Not Found');
    }

    //order as per pdf layout changes
    const tax_types = {
      tax_type_6: TAX_TYPES.tax_type_6, // दिवाबत्ती कर
      tax_type_7: TAX_TYPES.tax_type_7, // आरोग्य कर (corrected from 'आरोग्य क')
      tax_type_8: TAX_TYPES.tax_type_8, // पडसर कर
      tax_type_1: TAX_TYPES.tax_type_1, // वृक्ष उपकर
      tax_type_2: TAX_TYPES.tax_type_2, // शिक्षण उपकर
      tax_type_3: TAX_TYPES.tax_type_3, // रोजगार हमी कर
      tax_type_10: TAX_TYPES.tax_type_10, // अग्निशमन फी
      tax_type_4: TAX_TYPES.tax_type_4, // घनकचरा शुल्क
      tax_type_5: TAX_TYPES.tax_type_5, // अनधिकृत शास्ती कर
      tax_type_9: TAX_TYPES.tax_type_9, // दंड
    };

    const result = getData?.flat();
    return {
      getData: result,
      tax_types,
    };
  }

  async get_namuna8_pdf(params: any, res: Response) {
    try {
      const { milkatKar_id } = params;
      let data = await this.getBillDataNanuma8(milkatKar_id);
      let data_json_formatted = await this.convertJson(data); // Convert numeric values to Marathi digits
      //return res.status(200).send(data_json_formatted);

      const client: ClientProxy = ClientProxyFactory.create({
        transport: Transport.TCP,
        options: {
          host: '127.0.0.1',
          port: 3002,
        },
      });

      const pattern = { cmd: 'generate_pdf' };
      const payload = {
        htmlTemplate: 'namunaeight.ejs',
        fileData: data_json_formatted,
        apiSecretKey: process.env.API_SECRET_KEY,
      };

      const result = await client.send<Buffer>(pattern, payload).toPromise();

      // Set the content type to PDF
      res.contentType('application/pdf');
      // Send the PDF buffer as a response
      res.send(Buffer.from(result));
    } catch (err) {
      console.error('Error generating PDF:', err);
      res.status(500).send('Error generating PDF');
    }
  }

  async get_namuna9_pdf(params: any, res: Response) {
    try {
      const { warshik_karId } = params;
      let data = await this.getBillDataNanuma9(warshik_karId);
      let data_json_formatted = await this.convertJson(data); // Convert numeric values to Marathi digits
      return res.status(200).send(data_json_formatted);
      const client: ClientProxy = ClientProxyFactory.create({
        transport: Transport.TCP,
        options: {
          host: '127.0.0.1',
          port: 3002,
        },
      });

      const pattern = { cmd: 'generate_pdf' };
      const payload = {
        htmlTemplate: 'namunanine.ejs',
        fileData: data_json_formatted,
        apiSecretKey: process.env.API_SECRET_KEY,
      };

      const result = await client.send<Buffer>(pattern, payload).toPromise();

      // Set the content type to PDF
      res.contentType('application/pdf');
      // Send the PDF buffer as a response
      res.send(Buffer.from(result));
    } catch (err) {
      console.error('Error generating PDF:', err);
      res.status(500).send('Error generating PDF');
    }
  }

  findOne(id: number) {
    return `This action returns a #${id} billing`;
  }

  async getBillData(billNo: string) {
    const getData = await this.warshikKarRepository.get_data_for_bill(billNo);

    if (!getData) {
      throw new NotFoundException('Data Not Found');
    }

    //order as per pdf layout changes
    const tax_types = {
      tax_type_6: TAX_TYPES.tax_type_6, // दिवाबत्ती कर
      tax_type_7: TAX_TYPES.tax_type_7, // आरोग्य कर (corrected from 'आरोग्य क')
      tax_type_8: TAX_TYPES.tax_type_8, // पडसर कर
      tax_type_1: TAX_TYPES.tax_type_1, // वृक्ष उपकर
      tax_type_2: TAX_TYPES.tax_type_2, // शिक्षण उपकर
      tax_type_3: TAX_TYPES.tax_type_3, // रोजगार हमी कर
      tax_type_10: TAX_TYPES.tax_type_10, // अग्निशमन फी
      tax_type_4: TAX_TYPES.tax_type_4, // घनकचरा शुल्क
      tax_type_5: TAX_TYPES.tax_type_5, // अनधिकृत शास्ती कर
      tax_type_9: TAX_TYPES.tax_type_9, // दंड
    };

    return {
      getData,
      tax_types,
    };
  }

  async getBillDataByward(ward: string, limit?: number, page?: number) {
    const getData: any =
      await this.warshikKarRepository.get_data_for_bill_by_ward(
        ward,
        limit,
        page,
      );

    if (!getData) {
      throw new NotFoundException('Data Not Found');
    }
    const tax_types = {
      tax_type_6: TAX_TYPES.tax_type_6, // दिवाबत्ती कर
      tax_type_7: TAX_TYPES.tax_type_7, // आरोग्य कर (corrected from 'आरोग्य क')
      tax_type_8: TAX_TYPES.tax_type_8, // पडसर कर
      tax_type_1: TAX_TYPES.tax_type_1, // वृक्ष उपकर
      tax_type_2: TAX_TYPES.tax_type_2, // शिक्षण उपकर
      tax_type_3: TAX_TYPES.tax_type_3, // रोजगार हमी कर
      tax_type_10: TAX_TYPES.tax_type_10, // अग्निशमन फी
      tax_type_4: TAX_TYPES.tax_type_4, // घनकचरा शुल्क
      tax_type_5: TAX_TYPES.tax_type_5, // अनधिकृत शास्ती कर
      tax_type_9: TAX_TYPES.tax_type_9, // दंड
    };

    for (const record of getData) {
      if (record.property?.propertyNumber) {
        const propertyNumber = record.property.propertyNumber;

        // Fetch bhogawatdar details from import_property
        const ownerDetail = await this.importRepository.findOne({
          where: { propertyNumber: propertyNumber },
        });

        if (ownerDetail) {
          // Ensure property_owner_details exists as an array
          let details = {
            name: ownerDetail.bhogawat_owner_name,
          };
          record.property.property_owner_details.push(details);
        }
      }
    }

    return {
      getData,
      tax_types,
    };
  }

  async getBillDataNanuma8(milkatKar_id: string) {
    const getData =
      await this.milkatKareRepository.get_data_for_nanuma8(milkatKar_id);

    if (!getData) {
      throw new NotFoundException('Data Not Found');
    }

    const tax_types = {
      tax_type_1: TAX_TYPES.tax_type_1, // वृक्ष उपकर (corrected from 'वृक्ष उपकार')
      tax_type_2: TAX_TYPES.tax_type_2, // शिक्षण उपकर
      tax_type_3: TAX_TYPES.tax_type_3, // रोजगार हमी कर (corrected from 'रो. हमी उपकर')
      tax_type_4: TAX_TYPES.tax_type_4, // घनकचरा शुल्क (corrected from 'घनकचरा सेवा कर')
      tax_type_5: TAX_TYPES.tax_type_5, // अनधिकृत शास्ती कर (corrected from 'शास्ती फी')
    };
    return {
      getData,
      tax_types,
    };
  }

  async getBillDataNanuma9(warshik_karId: string) {
    const getData =
      await this.warshikKarRepository.get_data_for_nanuma9(warshik_karId);

    if (!getData) {
      throw new NotFoundException('Data Not Found');
    }

    const tax_types = {
      tax_type_1: TAX_TYPES.tax_type_1, // वृक्ष उपकर (corrected from 'वृक्ष उपकार')
      tax_type_2: TAX_TYPES.tax_type_2, // शिक्षण उपकर
      tax_type_3: TAX_TYPES.tax_type_3, // रोजगार हमी कर (corrected from 'रो. हमी उपकर')
      tax_type_4: TAX_TYPES.tax_type_4, // घनकचरा शुल्क (corrected from 'घनकचरा सेवा कर')
      tax_type_5: TAX_TYPES.tax_type_5, // अनधिकृत शास्ती कर (corrected from 'शास्ती फी')
    };
    return {
      getData,
      tax_types,
    };
  }

async  getBillPdfByPropertyNumbers(propertyNumbers, res) {
  try {
    const allBillsData = [];
    const currentFinancialYear = await this.financialYearRepo.getCurrentFinancialYear();

    for (const propertyNumber of propertyNumbers) {
      let property = await this.propertyMasterRepository.findOne({ where: { propertyNumber } });
      if (!property) {
        property = await this.propertyMasterRepository.findOne({ where: { old_propertyNumber: propertyNumber } });
      }

      if (property) {
        const actualPropertyNumber = property.propertyNumber;
        const financialYear = {
          financial_year: currentFinancialYear.financial_year_range,
          propertyNumber: actualPropertyNumber
        };
        await this.tax_billGenerateService.generate_billsForPropertyNumber(financialYear);
        const data = await this.warshikKarRepository.get_data_for_bill_By_PropertyNUmber(actualPropertyNumber);
        const penaltyFees = await this.penaltyFeeYearWiseRepository.findPenaltyFeesByPropertyId(property.property_id);

        if (penaltyFees && penaltyFees.length > 0) {
          let currentPenaltyValue = 0, previousPenaltySum = 0, currentActualValue = 0, previousActualValue = 0;
          penaltyFees.forEach(penalty => {
            if (penalty.financial_year === currentFinancialYear.financial_year_range) {
              currentPenaltyValue = Number(penalty.penalty_value || 0);
              currentActualValue = Number(penalty.actual_value || 0);
            } else {
              previousPenaltySum += Number(penalty.penalty_value || 0);
              previousActualValue += Number(penalty.actual_value || 0);
            }
          });

          currentPenaltyValue = Math.round(currentPenaltyValue);
          previousPenaltySum = Math.round(previousPenaltySum);
          currentActualValue = Math.round(currentActualValue);
          previousActualValue = Math.round(previousActualValue);

          data[0].tax_type_9_current = currentPenaltyValue;
          data[0].tax_type_9_previous = previousPenaltySum;
          data[0].tax_type_9 = currentPenaltyValue + previousPenaltySum;
          data[0].total_tax_current = Math.max(0, Math.round((Number(data[0].total_tax_current) || 0) + currentPenaltyValue));
          data[0].total_tax_previous = Math.max(0, Math.round(previousActualValue + previousPenaltySum));
          data[0].total_tax = data[0].total_tax_current + data[0].total_tax_previous;
        }

        const ownerDetail = await this.importRepository.find({ where: { propertyNumber: actualPropertyNumber } });
        const updatedData = data.map(item => ({ ...item, ownerDetail }));
        allBillsData.push(updatedData[0]);
      }
    }

    if (allBillsData.length === 0) {
      return res.status(404).json({ message: 'No properties found' });
    }

    const taxTypes = {
      tax_type_6: TAX_TYPES.tax_type_6,
      tax_type_7: TAX_TYPES.tax_type_7,
      tax_type_8: TAX_TYPES.tax_type_8,
      tax_type_1: TAX_TYPES.tax_type_1,
      tax_type_2: TAX_TYPES.tax_type_2,
      tax_type_3: TAX_TYPES.tax_type_3,
      tax_type_10: TAX_TYPES.tax_type_10,
      tax_type_4: TAX_TYPES.tax_type_4,
      tax_type_5: TAX_TYPES.tax_type_5,
      tax_type_9: TAX_TYPES.tax_type_9,
    };

    const tempPdfDir = path.join(process.cwd(), 'temp_pdfs', `bills_${Date.now()}`);
    if (!fs.existsSync(tempPdfDir)) {
      fs.mkdirSync(tempPdfDir, { recursive: true });
    }

    const pdfPromises = allBillsData.map(async (billData, index) => {
      try {
        console.log('Processing bill data:', billData);
        const marathiData = await this.convertJson(billData);
        const templatePath = path.join(__dirname, 'templates', 'bill-template_6.ejs');
        const template = fs.readFileSync(templatePath, 'utf-8');
        const htmlOutput = ejs.render(template, { fileData: marathiData, tax_types: taxTypes });

        const formData = new FormData();
        formData.append('html_file', Buffer.from(htmlOutput), {
          filename: `index.html`,
       
        });


    // template_name: 'NamunaEightReport',
    //       data: sendingDataToPdf,
    //       output_filename: 'property_reports.pdf',
    //       orientation: 'landscape',
    //       page_width: 11.0,
    //       page_height: 8.5,
    //       margin_top: 0.5,
    //       margin_bottom: 0.5,
    //       margin_left: 0.5,
    //       margin_right: 0.5,

      formData.append('paperWidth	', '10'); // A4 width in mm
      formData.append('paperHeight', '12'); // A4 height in mm (or increase if needed)
      formData.append('scale', '1.0'); // Scale down content to fit
      formData.append('marginTop', '0.3');
      formData.append('marginBottom', '0.3');
      formData.append('marginLeft', '0.3');
      formData.append('marginRight', '0.3');

      // Optional: to prevent page breaks
      formData.append('prefer_css_page_size', 'false');
      formData.append('landscape', 'false');

      const apiUrl = process.env.CONVERT_SINGLEHTML_TO_PDF;


        



        const response = await axios.post(apiUrl, formData, {
          headers: { ...formData.getHeaders() },
             responseType: 'arraybuffer',
        });

 

        // Assuming the API returns a JSON object with a base64 encoded PDF
        const pdfBase64 = response.data;
        const pdfBuffer = Buffer.from(pdfBase64, 'base64');

        const sanitizedPropertyNumber = (billData.property.propertyNumber || index).replace(/\//g, '_');
        const pdfFileName = `bill_${sanitizedPropertyNumber}.pdf`;
        const pdfFilePath = path.join(tempPdfDir, pdfFileName);
        // Ensure the directory exists for the PDF file
        const pdfFileDir = path.dirname(pdfFilePath);
        if (!fs.existsSync(pdfFileDir)) {
          fs.mkdirSync(pdfFileDir, { recursive: true });
        }
        fs.writeFileSync(pdfFilePath, pdfBuffer);
        await sleep(2000); // Add a 1-second delay
        return pdfFilePath;
      } catch (error) {
        console.error(`Error generating PDF for bill ${index}:`, error.message);
        throw error;
      }
    });

    await Promise.all(pdfPromises);
console.log("pdfPromises",pdfPromises)
    const zipFileName = `Bills_${Date.now()}.zip`;
      const zipFilePath = path.join(process.cwd(), 'temp_pdfs', zipFileName);

      await new Promise<void>((resolve, reject) => {
        const output = fs.createWriteStream(zipFilePath);
        const archive = archiver('zip', {
          zlib: { level: 9 }, // Sets the compression level.
        });

        output.on('close', async () => {
          res.setHeader('Content-Type', 'application/zip');
          res.setHeader('Content-Disposition', `attachment; filename="${zipFileName}"`);
          res.download(zipFilePath, async (err) => {
            if (err) {
              console.error('Error sending zip file:', err);
              reject(err);
            } else {
              await rimraf.sync(tempPdfDir); // Clean up the temporary PDF directory
              await rimraf.sync(zipFilePath); // Clean up the temporary zip file
              resolve();
            }
          });
        });

        archive.on('error', (err) => {
          console.error('Archiver error:', err);
          reject(err);
        });

        archive.pipe(output);
        archive.directory(tempPdfDir, false);
        archive.finalize();
      });
  } catch (error) {
    console.error('Error:', error);
    res.status(500).json({ message: 'Internal Server Error', error: error.message });
  }
}


  async getForm8Data() {
    try {
      const getData = await this.tax_PropertyRepository.milkat_kar();

      if (!getData) {
        throw new NotFoundException('Data Not Found');
      }

      return getData;

      //  const dt= {
      //     bill_no: '12345677999',
      //     bill_date: '01/04/2024',
      //     // Add other necessary fields here
      //   };
      //   return
    } catch (err) {
      console.error('Error generating getForm8Data:', err);
      //res.status(500).send('Error generating PDF');
    }
  }

  readonly marathiNumbers: string[] = [
    '',
    'एक',
    'दोन',
    'तीन',
    'चार',
    'पाच',
    'सहा',
    'सात',
    'आठ',
    'नऊ',
    'दहा',
    'अकरा',
    'बारा',
    'तेरा',
    'चौदा',
    'पंधरा',
    'सोळा',
    'सतरा',
    'अठरा',
    'एकोणीस',
    'वीस',
    'एकवीस',
    'बावीस',
    'तेवीस',
    'चोवीस',
    'पंचवीस',
    'सव्वीस',
    'सत्तावीस',
    'अठ्ठावीस',
    'एकोणतीस',
    'तीस',
    'एकतीस',
    'बत्तीस',
    'तेहतीस',
    'चौतीस',
    'पस्तीस',
    'छत्तीस',
    'सदतीस',
    'अडतीस',
    'एकोणचाळीस',
    'चाळीस',
    'एक्केचाळीस',
    'बेचाळीस',
    'तेहेचाळीस',
    'चव्वेचाळीस',
    'पंचेचाळीस',
    'सहाचाळीस',
    'सत्तेचाळीस',
    'अठ्ठेचाळीस',
    'एकोणपन्नास',
    'पन्नास',
    'एक्कावन्न',
    'बावन्न',
    'त्रेपन्न',
    'चोपन्न',
    'पंचावन्न',
    'छप्पन्न',
    'सत्तावन्न',
    'अठ्ठावन्न',
    'एकोणसाठ',
    'साठ',
    'एकसष्ट',
    'बासष्ट',
    'त्रेसष्ट',
    'चौसष्ट',
    'पासष्ट',
    'सहासष्ट',
    'सत्तेसष्ट',
    'अठ्ठेसष्ट',
    'एकोणसत्तर',
    'सत्तर',
    'एक्काहत्तर',
    'बाहत्तर',
    'त्र्याहत्तर',
    'चौर्‍याहत्तर',
    'पंच्याहत्तर',
    'शहात्तर',
    'सत्त्याहत्तर',
    'अठ्ठ्याहत्तर',
    'एकोणऐंशी',
    'ऐंशी',
    'एक्क्याऐंशी',
    'ब्याऐंशी',
    'त्र्याऐंशी',
    'चौर्‍याऐंशी',
    'पंच्याऐंशी',
    'शहाऐंशी',
    'सत्त्याऐंशी',
    'अठ्ठ्याऐंशी',
    'एकोणनव्वद',
    'नव्वद',
    'एक्क्याण्णव',
    'ब्याण्णव',
    'त्र्याण्णव',
    'चौर्‍याण्णव',
    'पंच्याण्णव',
    'शहाण्णव',
    'सत्त्याण्णव',
    'अठ्ठ्याण्णव',
    'शंभर',
  ];

  async convertNumberToMarathiWords(num: number): Promise<string> {
    if (num === 0) return 'शून्य';

    let result = '';

    if (num >= 100000) {
      const lakhs = Math.floor(num / 100000);
      result += `${this.marathiNumbers[lakhs]} लाख `;
      num %= 100000;
    }
    if (num >= 1000) {
      const thousands = Math.floor(num / 1000);
      result += `${this.marathiNumbers[thousands]} हजार `;
      num %= 1000;
    }

    if (num >= 100) {
      const hundreds = Math.floor(num / 100);
      result += `${this.marathiNumbers[hundreds]}शे `;
      num %= 100;
    }

    if (num > 0) {
      result += `${this.marathiNumbers[num]} `;
    }

    return result.trim();
  }
}
