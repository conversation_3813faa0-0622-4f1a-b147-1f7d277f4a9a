
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateRegisterNumberDto {
  @ApiProperty({
    description: 'Name of the register',
    example: 'General Register',
  })
  @IsNotEmpty()
  @IsString()
  register_name: string;
}

export class UpdateRegisterNumberDto {
  @ApiProperty({
    description: 'Name of the register',
    example: 'General Register',
    required: false,
  })
  @IsOptional()
  @IsString()
  register_name?: string;
}
