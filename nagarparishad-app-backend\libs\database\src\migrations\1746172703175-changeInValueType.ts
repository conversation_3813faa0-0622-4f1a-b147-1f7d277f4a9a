import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeInValueType1746172703175 implements MigrationInterface {
  name = 'ChangeInValueType1746172703175';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "penalty_fee_yearWise" DROP COLUMN "tax_percentage"`,
    );
    await queryRunner.query(
      `ALTER TABLE "penalty_fee_yearWise" ADD "tax_percentage" integer NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "penalty_fee_yearWise" DROP COLUMN "tax_percentage"`,
    );
    await queryRunner.query(
      `ALTER TABLE "penalty_fee_yearWise" ADD "tax_percentage" numeric NOT NULL`,
    );
  }
}
