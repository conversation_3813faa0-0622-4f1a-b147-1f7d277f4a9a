import { PaginationDto } from '@helper/helpers/Pagination';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class GetUsersDto extends PaginationDto {
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  role: number;
}

export class GetSingleUserDto {
  @IsNotEmpty()
  @IsUUID()
  user_id: string;

  @IsOptional()
  @IsUUID()
  collector_id?: string

  @IsOptional()
  @IsUUID()
  ward_id?: string
}
