import { Module } from '@nestjs/common';
import { CronJobsController } from './cron-jobs.controller';
import { CronJobsService } from 'libs/helpers/src/cron-jobs/cron-jobs.service';
import { DatabaseModule } from 'libs/database/database.module';
@Module({
  imports: [DatabaseModule],
  controllers: [CronJobsController],
  providers: [CronJobsService],
  exports: [CronJobsService],
})
export class CronJobsModule {}
