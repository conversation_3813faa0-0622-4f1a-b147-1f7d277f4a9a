import { Injectable } from '@nestjs/common';
import { CollectorMasterRepository, PaidDataRepository, Property_Usage_DetailsRepository, PropertyTypeClassMasterRepository, PropertyMasterRepository, WarshikKarRepository, Financial_yearRepository, PaymentInfoRepository, TaxPendingDuesRepository } from 'libs/database/repositories';
import { IsNull, Not } from 'typeorm';

@Injectable()
export class DashboardService {
  constructor(
    private readonly propertyMaster: PropertyMasterRepository,
    private readonly paidTaxData: PaidDataRepository,
    private readonly warshikKar: WarshikKarRepository,
    private readonly propertyUsageDetails: Property_Usage_DetailsRepository,
    private readonly collectorRepo : CollectorMasterRepository,
    private readonly fyRepo: Financial_yearRepository,
    private readonly propertyTypeClassMasterRepository: PropertyTypeClassMasterRepository,
       private readonly paymentInfoRepository: PaymentInfoRepository,
        private readonly propertyMasterRepository: PropertyMasterRepository,
        private readonly taxPendingDuesRepository: TaxPendingDuesRepository,
        private readonly warshikKarRepository: WarshikKarRepository,
        private readonly paidDataRepository: PaidDataRepository
      
  ) {}

  async propertyTypeStats() {
    try{
      const allClasses = await this.propertyMaster.getPropertyCountByType();
            return {
        data: allClasses
      }
    } catch(e) {
      throw(e);
    }
  }
  async findAllTotalPropertyStats(year?: string) {
    try {
      // Get current financial year if not provided
      year = year || (await this.fyRepo.getCurrentFinancialYear()).financial_year_range; 
       
        
      const startYear = year.split('-')[0];
      console.log("year-->",year)
      console.log("startYear-->",startYear)
      // Fetch necessary data
    const qb = this.warshikKar
      .createQueryBuilder('warshikKar')
      .select('SUM(CAST(warshikKar.total_tax AS NUMERIC))', 'total')
      .where('warshikKar.financial_year = :year', { year })
      .andWhere('warshikKar.status = :status', { status: 'active' });

    // 🧾 Log full SQL query for debugging
    console.log("🔍 Generated SQL Query:", qb.getSql());

    // Fetch data
    const [totalPropertyCount, totalPaidTax, totalTaxResult] = await Promise.all([
      this.propertyMaster.findCount(),
      this.paidTaxData.findTotalTaxPaid(year),
      qb.getRawOne()
    ]);
      console.log("totalTaxResult-->",totalTaxResult)

      const totalTax = Number(totalTaxResult?.total) || 0;
  
      // Calculate remaining tax
      const remainingTax = totalTax - (totalPaidTax || 0);
  
        
      return {
        message: "Total Property Stats",
        data: {
          totalPropertyCount,
          totalPaidTax,
          remainingTax,
        },
      };
    } catch (error) {
      console.error("Error fetching total property stats:", error);
      throw new Error("Failed to fetch total property stats");
    }
  }
  
  
  // async paidTaxesForPropertyTypes(params: { year: string }) {
  //   const { year } = params;
    
  //   const allPaidDataThisYear = await this.paidTaxData.findAllPaidDataByYear(year);

  //   const propertyTypeMap = new Map<string, number>(); 

  //   // await Promise.all(
  //   //   allPaidDataThisYear.map(async (payment) => {
  //   //     const property = await this.propertyMaster.findById(payment.property_id);
    
  //   //     property.property_usage_details.forEach((usage) => {
  //   //       const propertyType = usage.propertyType?.propertyType || "UnknownType";
  //   //       const currentTotal = propertyTypeMap.get(propertyType) || 0;
  //   //       propertyTypeMap.set(propertyType, currentTotal + payment.total_amount);
  //   //     });
  //   //   })
  //   // );
    
  //   const groupedByPropertyType = Object.fromEntries(propertyTypeMap);
    
  //     
  //   return {
  //     message: "Total Property Stats",
  //     data: {
  //       groupedByPropertyType
  //     }
  //   };
  // }
  async findWardPropertyStats(wardId: string) {
    try {
        // Fetch property stats for a specific ward
        const wardData = await this.propertyMaster.findTotalForWard(wardId);

        // Fetch collector details for the given ward
        const collector = await this.collectorRepo.getByWard(wardId);

        // return {
        //     message: `Property Stats for Ward ${wardId}`,
        //     data: {
        //         propertyStats: wardData,
        //         collector: collector
        //             ? {
        //                   collectorId: collector.collectorId || "",
        //                   userName: collector.user?.firstname || "",
        //               }
        //             : null, 
        //     },
        // };
        
        return {
            message: `Property Stats for Ward ${wardId}`,
            data: {
                propertyStats: wardData,
                collector: collector
                    ? {
                          collectorId: collector.collectorId || "",
                          userName: collector.user?.firstname || "",
                      }
                    : null, 
            },
        };
    } catch (error) {
        console.error(`Error fetching property stats for ward ${wardId}:`, error);
        throw new Error(`Failed to fetch property stats for ward ${wardId}`);
    }
}

async findAllWardPropertyStats() {
  try {
      const collectors = await this.collectorRepo.findAllWithWards();
      const allPaidDataThisYear = await this.paidTaxData.findAllPaidData();
      
      const wardPaidDataMap = new Map<string, number>();

      await Promise.all(
        allPaidDataThisYear.map(async (payment) => {
          const property = payment.property
          const wardName = property?.ward?.ward_name || "NoWard";

          const currentTotal = wardPaidDataMap.get(wardName) || 0;
          wardPaidDataMap.set(wardName, currentTotal + payment.total_amount);
        })
      );

      const enrichedWardData = collectors.map((collector) => ({
          ...collector,
          tax_paid: wardPaidDataMap.get(collector.ward_name) || 0, 
      }));

      return {
          message: "Property Stats for All Wards",
          data: enrichedWardData,
      };
  } catch (error) {
      console.error("Error fetching property stats for all wards:", error);
      throw new Error("Failed to fetch property stats for all wards");
  }
}

  async findUsageTypePropertyStats(usageType: string) {
    try {
      const usageTypeData = await this.propertyUsageDetails.findTotalForUsageType(usageType);
      return {
        message: `Property Stats for Usage Type ${usageType}`,
        data: usageTypeData
      };
    } catch (error) {
      console.error(`Error fetching property stats for usage type ${usageType}:`, error);
      throw new Error(`Failed to fetch property stats for usage type ${usageType}`);
    }
  }

  async findAllUsageTypePropertyStats() {
    try {
      const allUsageTypeData = await this.propertyUsageDetails.findTotalUsageTypeWise();
      return {
        message: "Property Stats for All Usage Types",
        data: allUsageTypeData
      };
    } catch (error) {
      console.error("Error fetching property stats for all usage types:", error);
      throw new Error("Failed to fetch property stats for all usage types");
    }
  }
  
  async getRecentTransactions() {
    const transactions = await this.paymentInfoRepository.createQueryBuilder('payment')
      .select('payment.payment_id', 'id')
      .addSelect('property.property_id', 'propertyId')
        .addSelect('property.propertyNumber', 'propertyNumber') // ✅ new
    .addSelect('property.old_propertyNumber', 'oldPropertyNumber') // ✅ new
      .addSelect('payment.tax_payer_name', 'taxpayerName')
      .addSelect('payment.amount', 'amount')
      .addSelect('payment.payment_mode', 'paymentMethod')
      .addSelect('payment.payment_status', 'status')
      .addSelect('payment.payment_date', 'timestamp')
      .innerJoin('payment.property', 'property')
      .innerJoin('property.property_owner_details', 'ownerDetails')
      .orderBy('payment.payment_date', 'DESC')
      .offset(0) // Starts from the first record
    .limit(5) // Limits the results to 5
    .getRawMany();
    

    return {
      message: "Recent Transactions",
      data: transactions
    };
  }

  async getTaxCollectionTrend(financialYear: string) {
    const monthlyData = await this.paymentInfoRepository.createQueryBuilder('payment')
      .select("TO_CHAR(payment.payment_date, 'YYYY-MM') as month, SUM(payment.amount) as collected")
      .where("payment.financial_year_range = :financialYear", { financialYear })
      .groupBy('month')
      .orderBy('month')
      .getRawMany();

    return {
      message: "Tax Collection Trend",
      data: monthlyData.map(data => ({ ...data, target: 500000 }))
    };
  }

  async getWardPerformance() {
    const wardStats = await this.propertyMasterRepository.createQueryBuilder('property')
      .select('ward.ward_name as ward_name, COUNT(property.property_id) as count')
      .innerJoin('property.ward', 'ward')
      .groupBy('ward.ward_name')
      .getRawMany();

    const paymentStats = await this.paymentInfoRepository.createQueryBuilder('payment')
      .select('ward.ward_name as ward_name, SUM(payment.amount) as collected')
      .innerJoin('payment.property', 'property')
      .innerJoin('property.ward', 'ward')
      .groupBy('ward.ward_name')
      .getRawMany();

    const totalTaxByWard = await this.warshikKarRepository.createQueryBuilder('warshikKar')
      .select('ward.ward_name as ward_name, SUM(warshikKar.total_tax) as total')
      .innerJoin('warshikKar.property', 'property')
      .innerJoin('property.ward', 'ward')
      .groupBy('ward.ward_name')
      .getRawMany();

    const result = wardStats.map(ward => {
        const payment = paymentStats.find(p => p.ward_name === ward.ward_name);
        const totalTaxData = totalTaxByWard.find(t => t.ward_name === ward.ward_name);
        const collected = payment ? Number(payment.collected) : 0;
        const total = totalTaxData ? Number(totalTaxData.total) : 0;
        const pending = total - collected;
        const rate = total > 0 ? (collected * 100) / total : 0;

        return {
          ...ward,
          collected,
          total,
          pending,
          rate,
        };
      })

    return {
      message: "Ward Performance",
      data: result.sort((a, b) => a.ward_name.localeCompare(b.ward_name))
    };
  }

  async getDefaulterAnalysis() {
    const currentYear = new Date().getFullYear().toString();
    const warshikKarData = await this.warshikKarRepository.find({ where: { financial_year: currentYear } });
    const paidData = await this.paidDataRepository.find({ where: { financial_year: currentYear } });

    const propertyDues = {};

    for (const tax of warshikKarData) {
      propertyDues[tax.property.property_id] = {
        totalTax: tax.total_tax,
        paidAmount: 0,
      };
    }

    for (const payment of paidData) {
      if (propertyDues[payment.property.property_id]) {
        propertyDues[payment.property.property_id].paidAmount += payment.total_amount;
      }
    }

    let highValueDefaulters = 0;
    let mediumValueDefaulters = 0;
    let lowValueDefaulters = 0;
    let highValueAmount = 0;
    let mediumValueAmount = 0;
    let lowValueAmount = 0;

    for (const propertyId in propertyDues) {
      const due = propertyDues[propertyId].totalTax - propertyDues[propertyId].paidAmount;
      if (due > 50000) {
        highValueDefaulters++;
        highValueAmount += due;
      } else if (due >= 10000 && due <= 50000) {
        mediumValueDefaulters++;
        mediumValueAmount += due;
      } else if (due > 0 && due < 10000) {
        lowValueDefaulters++;
        lowValueAmount += due;
      }
    }

    return {
      message: "Defaulter Analysis",
      data: [
        { category: 'High Value (>50k)', count: highValueDefaulters, amount: highValueAmount },
        { category: 'Medium Value (10-50k)', count: mediumValueDefaulters, amount: mediumValueAmount },
        { category: 'Low Value (<10k)', count: lowValueDefaulters, amount: lowValueAmount },
      ]
    };
  }
}
