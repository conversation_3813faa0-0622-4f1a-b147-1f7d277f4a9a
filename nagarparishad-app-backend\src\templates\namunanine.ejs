<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; }
    .header img { width: 6rem; height: 6rem; }
    .header .text-center { text-align: center; }
    .header h1 { font-size: 1.25rem; font-weight: bold; }
    .header p { font-size: 0.875rem; }
    .header .bold { font-weight: bold; }
    .divider { height: 2px; background-color: black; margin-bottom: 1rem; }
    .info-section { margin-bottom: 1rem; }
    .info-section .flex { display: flex; justify-content: space-between; }
    .info-section p { margin: 0; }
    .info-section .font-semibold { font-weight: 600; }
    .table-container { overflow-x: auto; }
    .table { min-width: 100%; border-collapse: collapse; border: 1px solid #71717a; }
    .table th, .table td { border: 1px solid #71717a; padding: 0.5rem; }
    thead, tbody { vertical-align: baseline; }
    .table th { text-align: left; }
    .footer { margin-bottom: 1rem; }
    .flex { display: flex; }
    .border-zinc-400 { border-color: #dae1e7; }
    .h-2 { height: 2px; }
    .max-w-4xl { max-width: 768px; }
    .sign-box { display: flex; flex-direction: column; justify-content: center; align-items: end; margin-top: 30px; text-align: center; padding-right: 30px; font-size: 15px; }
    .mx-auto { margin-left: auto; margin-right: auto; }
    .p-4 { padding: 16px; }
    .border-2 { border-width: 2px; }
    .border-black { border-color: black; }
    .justify-between { justify-content: space-between; }
    .items-center { align-items: center; }
    .flex-direction-column { flex-direction: column; }
    .mb-4 { margin-bottom: 16px; }
    .w-24 { width: 96px; }
    .h-24 { height: 96px; }
    .text-center { text-align: center; }
    .text-xl { font-size: 1.25rem; }
    .font-bold { font-weight: bold; }
    .text-sm { font-size: 0.875rem; }
    .text-xs { font-size: 0.675rem; }
    .h-2 { height: 8px; }
    .overflow-x-auto { overflow-x: auto; }
    .min-w-full { min-width: fit-content; }
    .border-collapse { border-collapse: collapse; }
    .border { border: 1px solid #525252; }
    .border-zinc-400 { border-color: #525252; }
    .px-4 { padding-left: 8px; padding-right: 8px; }
    .px-1 { padding-left: 5px; padding-right: 5px; }
    .py-2 { padding-top: 2px; padding-bottom:  2px; }
    p { margin-top: 3px; margin-bottom: 3px; }
    .pb-0 { padding-bottom: 0; }
    .border-red { border: 1px solid red; }
    .gap15 { gap: 10px; }
    .th-subtitle { width: 20%; word-wrap: break-word; margin-right: 1px; }
    .th-subtitle1 { word-wrap: break-word; margin-right: 1px; }
    .th-title-sm { min-width: 82.7px; max-width: 70px; word-wrap: break-word; margin-right: 1px; font-size: 15px; }
    .th-title-md { width: 99px; min-width: 99px; max-width: 99px; word-wrap: break-word; margin-right: 1px; }
    .th-title-lg { width: 12%; word-wrap: break-word; margin-right: 1px; }
    .th-title-xl { min-width: 230px; max-width: fit-content; word-wrap: break-word; margin-right: 1px; }
    .margin-top-20 { margin-top: 20px; }
    .rotate-number { display: flex; gap: 10px; transform: rotate(90deg); word-wrap: break-word; }
    .border-left { border: 1px solid #525252; }
    .border-top { border-top: 1px solid #525252; }
    .grid-font { font-size: 12px; }
    div { margin-left: 0; margin-right: 0 !important; }
    .flex-align-justify-center { display: flex; justify-content: center; }
    .grid-font-numbers { font-size: 12px; }
    .th-title-height { min-height: 120px; }
    .th-subtitle-width { min-width: 98.7px; }
    .th-subtitle-width .flex { justify-content: space-between; padding: 0 2px; }
    .mb-5 { margin-bottom: 5px; }
    .mb-19 { margin-bottom: 19px; }
    .qh-sut-height { height: 61px; }
    .qh-sut-height p:first-child { margin-top: 0px; margin-bottom: 0px; border-right: 1px solid; }
    .py-0 { padding-top: 0; padding-bottom: 0; }
    .th-subtitle-width div:nth-child(2) p { margin-bottom: 10px; }
    .block{ display: block; }
    .border-right { border-right: 1px solid black; }
    .w-40 {width: 40%;}
    .w-60 {width: 60%;}
    .px-0{ padding-left: 0 !important; padding-right: 0 !important; }
    .h-32{ height: 32px; }
    .mb-1{margin-bottom: 1px !important;}
    .text-wrap{ word-wrap: break-word; }
  </style>
</head>
<body>
  <div class="container">
    <div class="flex justify-center items-center flex-direction-column">
      <h1 class="text-xl font-bold my-1">नगरपरिषद शिरोळ जि. कोल्हापूर</h1>
      <h1 class="text-xl font-bold">नमुना क्रमांक ९ कर मागणी रजिस्टर</h1>
      <p class="text-md font-bold">सन २०२४-२०२५ वर्षाच्या आकारणी केलेल्या मागणीचे नोंदणी पुस्तक</p>
    </div>
    <div class="mb-4 px-1 margin-top-20">
      <div class="flex justify-between font-bold">
        <div>
          <p><span class="font-semibold">नगरपरिषद</span>: नगरपरिषद शिरोळ</p>
        </div>
        <div>
          <p><span class="font-semibold">तालुका</span>: शिरोळ</p>
        </div>
        <div>
          <p><span class="font-semibold">जिल्हा:</span> कोल्हापूर</p>
        </div>
      </div>
    </div>
    <div class="min-w-full border-collapse grid-font">
      <div class="flex justify-center">
        <div class="border-left px-4 py-2 th-title-sm">अ क्र</div>
        <div class="border-left px-4 py-2 th-title-sm">मि क्र.</div>
        <div class="border-left px-4 py-2 th-title-sm">जुना क्र.</div>
        <div class="border-left px-4 py-2 th-title-sm">रस्त्याचे नाव</div>
        <div class="border-left px-4 py-2 th-title-sm">करदात्याचे नाव</div>
        <div class="border-left px-4 py-2 th-title-sm" style="min-width: 154px;">इमारतकर</div>
        <% Object.keys(tax_types).forEach(function(key) { %>
          <div class="border-left px-4 py-2 th-title-sm"><%= tax_types[key] %></div>
        <% }); %>
        <div class="border-left px-4 py-2 th-title-sm">शास्तीफी</div>
        <div class="border-left px-4 py-2 th-title-sm">दंड रक्कम</div>
        <div class="border-left px-4 py-2 th-title-sm">एकूण मागणी</div>
        <div class="border-left px-4 py-2 th-title-sm">बाकी</div>
      </div>
    </div>
    <% if (Array.isArray(fileData.payments)) { %>
      <% fileData.payments.forEach(function(receipt, index) { %>
        <div class="min-w-full border-collapse grid-font">
          <div class="flex justify-center">
            <% if (index === 0) { %>
              <div class="border-left px-4 py-2 th-title-sm"><%= index + 1 %></div>
              <div class="border-left px-4 py-2 th-title-sm"><%= fileData.propertyNumber %></div>
              <div class="border-left px-4 py-2 th-title-sm"><%= fileData.old_propertyNumber %></div>
              <div class="border-left px-4 py-2 th-title-sm"><%= fileData.street.street_name %></div>
            <% } else { %>
              <!-- Empty divs to maintain layout structure -->
              <div class="border-left px-4 py-2 th-title-sm"></div>
              <div class="border-left px-4 py-2 th-title-sm"></div>
              <div class="border-left px-4 py-2 th-title-sm"></div>
              <div class="border-left px-4 py-2 th-title-sm"></div>
            <% } %>
            <div class="border-left px-4 py-2 th-title-sm"><%= fileData.property_owner_details[0].name %></div>
            <div class="border-left py-2 th-title-sm th-subtitle-width" style="min-width: 170px;">
              <div class="flex justify-center">
                <p>मागील मागणी</p>
                <p><%= fileData.demandReportData[index].all_property_tax_sum_prev_remaining %></p>
              </div>
              <div class="flex">
                <p>चालु मागणी</p>
                <p><%= fileData.demandReportData[index].all_property_tax_sum_curr_remaining %></p>
              </div>
              <div class="flex border-top">
                <p>एकूण मागणी</p>
                <p><%= fileData.demandReportData[index].all_property_tax_sum_remaining %></p>
              </div>
            </div>
            <% Object.keys(tax_types).forEach(function(key) { %>
              <div class="border-left py-2 th-title-sm th-subtitle-width">
                <div class="flex">
                  <p><%= fileData.demandReportData[index][`${key}_prev_remaining`] %></p>
                </div>
                <div class="flex">
                  <p><%= fileData.demandReportData[index][`${key}_curr_remaining`] %></p>
                </div>
                <div class="flex border-top">
                  <p><%= fileData.demandReportData[index][`${key}_remaining`] %></p>
                </div>
              </div>
            <% }); %>
            <div class="border-left py-2 th-title-sm th-subtitle-width">
              <div class="flex">
                <p>०</p>
              </div>
              <div class="flex">
                <p>०</p>
              </div>
              <div class="flex border-top">
                <p>०</p>
              </div>
            </div>
            <div class="border-left py-2 th-title-sm th-subtitle-width">
              <div class="flex">
                <p>०</p>
              </div>
              <div class="flex">
                <p>०</p>
              </div>
              <div class="flex border-top">
                <p>०</p>
              </div>
            </div>
            <div class="border-left py-2 th-title-sm th-subtitle-width">
              <div class="flex">
                <p>०</p>
              </div>
              <div class="flex">
                <p><%= fileData.demandReportData[index].total_amount_remaining %></p>
              </div>
              <div class="flex border-top">
                <p><%= fileData.demandReportData[index].total_amount_remaining %></p>
              </div>
            </div>
            <div class="border-left py-2 th-title-sm th-subtitle-width">
              <div class="flex">
                <p>०</p>
              </div>
              <div class="flex">
                <p>0</p>
              </div>
              <div class="flex border-top">
                <p>0</p>
              </div>
            </div>
          </div>
        </div>
        <div class="min-w-full border-collapse grid-font">
          <div class="flex justify-center">
            <div class="border-left px-4 py-2 th-title-sm"></div>
            <div class="border-left px-4 py-2 th-title-sm"></div>
            <div class="border-left px-4 py-2 th-title-sm"></div>
            <div class="border-left px-4 py-2 th-title-sm">
              <p>बुक क्र :- </p>
              <p>पावती क्र :- </p>
              <p>पावती दिं :- </p>
            </div>
            <div class="border-left px-4 py-2 th-title-sm">
              <p><%= receipt.receipts.book_number || "" %></p>
              <p><%= receipt.receipts.book_receipt_number || "" %></p>
              <p><%= receipt.payment_date %></p>
            </div>
            <div class="border-left py-2 th-title-sm th-subtitle-width" style="min-width: 170px;">
              <div class="flex">
                <p>मागील वसूल</p>
                <p><%= fileData.demandReportData[index].all_property_tax_sum_prev_paid %></p>
              </div>
              <div class="flex">
                <p>चालु वसूल</p>
                <p><%= fileData.demandReportData[index].all_property_tax_sum_curr_paid %></p>
              </div>
              <div class="flex border-top">
                <p>एकूण वसूल</p>
                <p><%= fileData.demandReportData[index].all_property_tax_sum_paid %></p>
              </div>
            </div>
            <% Object.keys(tax_types).forEach(function(key) { %>
              <div class="border-left py-2 th-title-sm th-subtitle-width">
                <div class="flex">
                  <p><%= fileData.demandReportData[index][`${key}_prev_paid`] %></p>
                </div>
                <div class="flex">
                  <p><%= fileData.demandReportData[index][`${key}_curr_paid`] %></p>
                </div>
                <div class="flex border-top">
                  <p><%= fileData.demandReportData[index][`${key}_paid`] %></p>
                </div>
              </div>
            <% }); %>
            <div class="border-left py-2 th-title-sm th-subtitle-width">
              <div class="flex">
                <p>०</p>
              </div>
              <div class="flex">
                <p>०</p>
              </div>
              <div class="flex border-top">
                <p>०</p>
              </div>
            </div>
            <div class="border-left py-2 th-title-sm th-subtitle-width">
              <div class="flex">
                <p>०</p>
              </div>
              <div class="flex">
                <p>०</p>
              </div>
              <div class="flex border-top">
                <p>०</p>
              </div>
            </div>
            <div class="border-left py-2 th-title-sm th-subtitle-width flex">
              <div class="flex border-right flex-direction-column w-40 px-0 py-0">
                <div class="flex h-">
                  <p>सूट</p>
                </div>
                <div class="flex border-top h-32 text-wrap">
                  <p><%= fileData.demandReportData[index]?.property_type_discount %></p>
                
                </div>
              </div>
              <div class="flex flex-direction-column w-60 px-0 py-0">
                <div class="flex">
                  <p>०</p>
                </div>
                <div class="flex">
                  <p class="mb-1"><%= fileData.demandReportData[index].total_amount_paid %></p>
                </div>
                <div class="flex border-top">
                  <p><%= fileData.demandReportData[index].total_amount_paid %></p>
                </div>
              </div>
            </div>
            <div class="border-left py-2 th-title-sm th-subtitle-width">
              <div class="flex">
                <p>०</p>
              </div>
              <div class="flex">
                <p>0</p>
              </div>
              <div class="flex border-top">
                <p><%= fileData.demandReportData[index].remaining_amount %></p>
              </div>
            </div>
          </div>
        </div>
      <% }); %>
    <% } %>
  </div>
</body>
</html>
