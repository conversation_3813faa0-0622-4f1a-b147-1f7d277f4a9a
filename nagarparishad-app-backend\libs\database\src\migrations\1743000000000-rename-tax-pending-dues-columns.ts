import { MigrationInterface, QueryRunner } from "typeorm";

export class RenameTaxPendingDuesColumns1743000000000 implements MigrationInterface {
    name = 'RenameTaxPendingDuesColumns1743000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Rename existing columns to tax_type_X format
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "house_tax" TO "all_property_tax_sum"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "electricity_bill" TO "tax_type_6"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "health_tax" TO "tax_type_7"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "padsar" TO "tax_type_8"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "penalty_amount" TO "tax_type_9"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "ghan_kachara" TO "tax_type_4"`);

        // Add missing tax types with default values
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" ADD "tax_type_1" decimal DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" ADD "tax_type_2" decimal DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" ADD "tax_type_3" decimal DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" ADD "tax_type_10" decimal DEFAULT 0`);

        // Convert shasti_fee from string to decimal
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" ADD "tax_type_5_temp" decimal DEFAULT 0`);
        await queryRunner.query(`UPDATE "tax_pending_dues" SET "tax_type_5_temp" = CAST("shasti_fee" AS decimal) WHERE "shasti_fee" ~ '^[0-9]+(\.[0-9]+)?$'`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" DROP COLUMN "shasti_fee"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "tax_type_5_temp" TO "tax_type_5"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert the changes
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "tax_type_5" TO "shasti_fee"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" DROP COLUMN "tax_type_10"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" DROP COLUMN "tax_type_3"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" DROP COLUMN "tax_type_2"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" DROP COLUMN "tax_type_1"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "tax_type_4" TO "ghan_kachara"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "tax_type_9" TO "penaltyAmount"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "tax_type_8" TO "padsar"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "tax_type_7" TO "healthTax"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "tax_type_6" TO "electricityBill"`);
        await queryRunner.query(`ALTER TABLE "tax_pending_dues" RENAME COLUMN "all_property_tax_sum" TO "house_tax"`);
    }
}
