import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddedColumn1729682743836 implements MigrationInterface {
    name = 'AddedColumn1729682743836';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check and add "remark" column in "property_usage_details"
        const hasRemark = await queryRunner.hasColumn("property_usage_details", "remark");
        if (!hasRemark) {
            await queryRunner.query(`ALTER TABLE "property_usage_details" ADD "remark" character varying`);
        }

        // Check and add "tapshil" column in "property_usage_details"
        const hasTapshil = await queryRunner.hasColumn("property_usage_details", "tapshil");
        if (!hasTapshil) {
            await queryRunner.query(`ALTER TABLE "property_usage_details" ADD "tapshil" character varying`);
        }

        // Check and add "status" column in "user_otp"
        const hasStatus = await queryRunner.hasColumn("user_otp", "status");
        if (!hasStatus) {
            await queryRunner.query(`ALTER TABLE "user_otp" ADD "status" integer`);
        }

        // Check and add "mobile_number" column in "user_otp"
        const hasMobileNumber = await queryRunner.hasColumn("user_otp", "mobile_number");
        if (!hasMobileNumber) {
            await queryRunner.query(`ALTER TABLE "user_otp" ADD "mobile_number" character varying`);
        }

        // Check and add "verificationId" column in "user_otp"
        const hasVerificationId = await queryRunner.hasColumn("user_otp", "verificationId");
        if (!hasVerificationId) {
            await queryRunner.query(`ALTER TABLE "user_otp" ADD "verificationId" character varying`);
        }

        // Check and add "key_value" column in "user_otp"
        const hasKeyValue = await queryRunner.hasColumn("user_otp", "key_value");
        if (!hasKeyValue) {
            await queryRunner.query(`ALTER TABLE "user_otp" ADD "key_value" jsonb`);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the columns if they exist

        const hasKeyValue = await queryRunner.hasColumn("user_otp", "key_value");
        if (hasKeyValue) {
            await queryRunner.query(`ALTER TABLE "user_otp" DROP COLUMN "key_value"`);
        }

        const hasVerificationId = await queryRunner.hasColumn("user_otp", "verificationId");
        if (hasVerificationId) {
            await queryRunner.query(`ALTER TABLE "user_otp" DROP COLUMN "verificationId"`);
        }

        const hasMobileNumber = await queryRunner.hasColumn("user_otp", "mobile_number");
        if (hasMobileNumber) {
            await queryRunner.query(`ALTER TABLE "user_otp" DROP COLUMN "mobile_number"`);
        }

        const hasStatus = await queryRunner.hasColumn("user_otp", "status");
        if (hasStatus) {
            await queryRunner.query(`ALTER TABLE "user_otp" DROP COLUMN "status"`);
        }

        const hasTapshil = await queryRunner.hasColumn("property_usage_details", "tapshil");
        if (hasTapshil) {
            await queryRunner.query(`ALTER TABLE "property_usage_details" DROP COLUMN "tapshil"`);
        }

        const hasRemark = await queryRunner.hasColumn("property_usage_details", "remark");
        if (hasRemark) {
            await queryRunner.query(`ALTER TABLE "property_usage_details" DROP COLUMN "remark"`);
        }
    }
}
