import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeINRaltionOfReciptAndBookEntity1741067508283 implements MigrationInterface {
    name = 'ChangeINRaltionOfReciptAndBookEntity1741067508283'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`DROP INDEX "public"."UQ_book_number_not_deleted"`);
        await queryRunner.query(`ALTER TABLE "receipt" ADD "book_number_id" uuid`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c" UNIQUE ("billNo")`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP DEFAULT`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(50) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "book_number_master" ADD CONSTRAINT "UQ_08e9055ec8df29c3d59e09d2c50" UNIQUE ("book_number")`);
        await queryRunner.query(`ALTER TABLE "receipt" ALTER COLUMN "book_receipt_number" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "FK_df738d1108e8291026728e28c55" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "receipt" ADD CONSTRAINT "FK_c3bb7626262aa8454b0bdf0e1bd" FOREIGN KEY ("book_number_id") REFERENCES "book_number_master"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "receipt" DROP CONSTRAINT "FK_c3bb7626262aa8454b0bdf0e1bd"`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "FK_df738d1108e8291026728e28c55"`);
        await queryRunner.query(`ALTER TABLE "receipt" ALTER COLUMN "book_receipt_number" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "book_number_master" DROP CONSTRAINT "UQ_08e9055ec8df29c3d59e09d2c50"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(100) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET DEFAULT now()`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c"`);
        await queryRunner.query(`ALTER TABLE "receipt" DROP COLUMN "book_number_id"`);
        // await queryRunner.query(`CREATE UNIQUE INDEX "UQ_book_number_not_deleted" ON "book_number_master" ("book_number") WHERE (deleted_at IS NULL)`);
    }

}
