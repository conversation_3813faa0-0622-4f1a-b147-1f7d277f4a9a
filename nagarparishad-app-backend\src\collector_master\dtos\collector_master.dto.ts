import { PartialType } from '@nestjs/swagger';
import { IsString, IsUUID, IsBoolean, IsOptional, IsArray } from 'class-validator';
import { CollectorMaster } from 'libs/database/entities';

export class CreateCollectorDto {
    @IsUUID()
    userId: string;

    @IsUUID()
    wardId: string;

    @IsBoolean()
    @IsOptional()
    isActive?: boolean;
}

export class UpdateCollectorDto {
  @IsOptional()
  @IsUUID()
  wardId?: string;

  @IsOptional()
  @IsUUID()
  userId?: string;

  @IsOptional()
  @IsUUID()
  roleId?: string;

  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  bookIds?: string[];
  
}

