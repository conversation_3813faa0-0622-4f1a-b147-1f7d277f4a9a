<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
      /*  
      .container {
        margin: 0 auto;
        padding: 1rem 0;
        border: 2px solid black;
      }
        */
        .container {
          max-width: 50rem;
          max-height: 290mm; 
      margin: 0 auto;
      border: 2px solid black;
      box-sizing: border-box;
    }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
      }
      .header img {
        width: 6rem;
        height: 6rem;
      }
      .header .text-center {
        text-align: center;
      }
      .header h1 {
        font-size: 1.25rem;
        font-weight: bold;
      }
      .header p {
        font-size: 0.875rem;
      }
      .header .bold {
        font-weight: bold;
      }
      .divider {
        height: 2px;
        background-color: black;
        margin-bottom: 1rem;
      }
      .info-section {
        margin-bottom: 1rem;
      }
      .info-section .flex {
        display: flex;
        justify-content: space-between;
      }
      .info-section p {
        margin: 0;
      }
      .info-section .font-semibold {
        font-weight: 600;
      }
      .table-container {
        overflow-x: auto;
      }
      .table {
        min-width: 100%;
        border-collapse: collapse;
        border: 1px solid #71717a;
      }
      .table th,
      .table td {
        border: 1px solid #71717a;
        padding: 0.5rem;
      }
      .table th {
        text-align: left;
        font-weight: normal;
      }
      .footer {
        margin-bottom: 1rem;
      }
      .flex {
        display: flex;
      }
      .border-zinc-400 {
        border-color: #dae1e7;
      }
      .h-2 {
        height: 2px;
      }

      .max-w-4xl {
        max-width: 768px;
      }
      .sign-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: end;
        margin-top: 30px;
        text-align: center;
        padding-right: 30px;
        font-size: 15px;
      }
      .mx-auto {
        margin-left: auto;
        margin-right: auto;
      }

      .p-4 {
        padding: 16px;
      }

      .border-2 {
        border-width: 2px;
      }

      .border-black {
        border-color: black;
      }

      .flex {
        display: flex;
      }

      .justify-between {
        justify-content: space-between;
      }

      .items-center {
        align-items: center;
      }

      .mb-4 {
        margin-bottom: 16px;
      }

      .w-24 {
        width: 96px;
      }

      .h-24 {
        height: 96px;
      }

      .text-center {
        text-align: center;
      }

      .text-xl {
        font-size: 1.1rem;
      }

      .font-bold {
        font-weight: bold;
      }

      .text-sm {
        font-size: 0.700rem;
      }
      .text-xs {
        font-size: 0.60rem;
      }
      .h-2 {
        height: 8px;
      }

      .overflow-x-auto {
        overflow-x: auto;
      }

      .min-w-full {
        min-width: 100%;
      }

      .border-collapse {
        border-collapse: collapse;
      }

      .border {
        border: 1px solid #525252;
      }

      .border-zinc-400 {
        border-color: #525252;
      }

      .px-4 {
        padding-left: 8px;
        padding-right: 8px;
      }
      .px-1 {
        padding-left: 5px;
        padding-right: 5px;
      }
      .py-2 {
        padding-top: 2px;
        padding-bottom: 2px;
      }
      .border-2 {
        border: 2px solid black;
      }
      p {
        margin-top: 3px;
        margin-bottom: 3px;
      }
      .pb-0{
        padding-bottom: 0;

      }
      .bold{
        font-weight: 600;
      }
        .signature {
            text-align: right;
            margin: 0;
        }

        .stamp {
            display: flex;
            justify-content: end;
            align-items: center;
        }

        .sign {
            width: 150px;
            height: 40px;
            margin-right: 60px;
        }

        .signature p {
            margin: 1px 20px;
            font-size: 12px;
            margin-right: 80px;
        }

        .stamp_img{
            height: 100px;
            width: 100px;
        }
        .note{
            text-align: center;
        }
        @media print {
            body {
                padding: 0;
                max-width: 92%;
                margin-left: auto;
                margin-right: auto;
            }

            .document-container {
                padding: 10px;
            }

            .table-section {
                margin-bottom: 0;
            }

            .signature img {
                width: 120px;
                height: 40px;
                margin-right: 70px;
            }
            .note{
                text-align: center;
            }
        }

        @page {
            margin-top: 25px;
        }
    </style>
     <script>
      // Helper function to convert numbers to Marathi digits
      function toMarathiDigits(number) {
        const digitsMap = {
          '0': '०',
          '1': '१',
          '2': '२',
          '3': '३',
          '4': '४',
          '5': '५',
          '6': '६',
          '7': '७',
          '8': '८',
          '9': '९'
        };

        // Convert the number to a string and replace each digit with its Marathi equivalent
      return String(number).replace(/[0-9]/g, (digit) => digitsMap[digit]);
      }

      function numberToWords(num) {
          const units = ['zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
          const teens = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
          const tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];
          const scales = ['hundred', 'thousand', 'million', 'billion', 'trillion'];

          if (num < 10) return capitalize(units[num]);
          if (num < 20) return capitalize(teens[num - 10]);
          if (num < 100) return capitalize(tens[Math.floor(num / 10)] + (num % 10 ? '-' + units[num % 10] : ''));

          let word = '';
          let scale = 0;
          while (num > 0) {
              let n = num % 1000;
              if (n !== 0) {
                  let str = '';
                  if (n % 100 < 20) {
                      str = (n % 100 < 10 ? units[n % 100] : teens[n % 100 - 10]);
                  } else {
                      str = tens[Math.floor(n % 100 / 10)] + (n % 10 ? '-' + units[n % 10] : '');
                  }
                  if (Math.floor(n / 100) > 0) {
                      str = units[Math.floor(n / 100)] + ' ' + scales[0] + ' ' + str;
                  }
                  word = str + ' ' + (scale > 0 ? scales[scale] + ' ' : '') + word;
              }
              scale++;
              num = Math.floor(num / 1000);
          }
          return capitalize(word.trim());
      }
      
     
      function numberToWords_marathi(num) {
        const units = ['शून्य', 'एक', 'दोन', 'तीन', 'चार', 'पाच', 'सहा', 'सात', 'आठ', 'नऊ'];
        const teens = ['दहा', 'अकरा', 'बारा', 'तेरा', 'चौदा', 'पंधरा', 'सोळा', 'सतरा', 'अठरा', 'एकोणीस'];
        const tens = ['', '', 'वीस', 'तीस', 'चाळीस', 'पन्नास', 'साठ', 'सत्तर', 'ऐंशी', 'नव्वद'];
        const scales = ['शंभर', 'हजार', 'लाख', 'कोटी', 'अब्ज'];

          if (num < 10) return capitalize(units[num]);
          if (num < 20) return capitalize(teens[num - 10]);
          if (num < 100) return capitalize(tens[Math.floor(num / 10)] + (num % 10 ? '-' + units[num % 10] : ''));

          let word = '';
          let scale = 0;
          while (num > 0) {
              let n = num % 1000;
              if (n !== 0) {
                  let str = '';
                  if (n % 100 < 20) {
                      str = (n % 100 < 10 ? units[n % 100] : teens[n % 100 - 10]);
                  } else {
                      str = tens[Math.floor(n % 100 / 10)] + (n % 10 ? '-' + units[n % 10] : '');
                  }
                  if (Math.floor(n / 100) > 0) {
                      str = units[Math.floor(n / 100)] + ' ' + scales[0] + ' ' + str;
                  }
                  word = str + ' ' + (scale > 0 ? scales[scale] + ' ' : '') + word;
              }
              scale++;
              num = Math.floor(num / 1000);
          }
          return capitalize(word.trim());
      }


      function capitalize(word) {
          return word.charAt(0).toUpperCase() + word.slice(1);
      }

      document.addEventListener('DOMContentLoaded', function() {
          const number =// fileData.total_tax;
          //number=123;
          console.log(">>>>>", number)
          const numberInWords = numberToWords_marathi(number);
          console.log(">>>>>",numberInWords)
          document.getElementById('numberInWords').textContent = numberInWords;
          
      });





    </script>
  </head>
  <body>
    <div class="container p-2 pb-0">
        <div class="flex justify-between items-center">
            <img src="https://api-spms.onpointsoft.com/images/logo.png" alt="logo" class="w-24 h-24" style="margin-left: 15px;" />
            <div class="text-center px-1">
                <h1 class="text-xl font-bold">शिरोळ नगरपरिषद शिरोळ</h1>
                <p class="text-sm">दि.०१/०४/२०२४ ते ३१/०३/२०२५ अखेरचे</p>
                <p class="text-xl font-bold">करमागणी बिल</p>
                <p class="text-sm">
                    (महाराष्ट्र नगरपालिका,नगरपंचायती व औद्योगिक  नगरी अधिनियम १९६५ चे कलम १५० व अकौट कोड नमुना नं.५२ अन्वये)
                </p>
            </div>
            <img src="https://api-spms.onpointsoft.com/images/logo_two.png" alt="logo" class="w-24 h-24" style="margin-right: 15px;" />
        </div>
        <hr />
        <div class="mb-4 px-1">
            <div class="flex justify-between">
                <div>
                  <p><span class="font-semibold">बिल क्र.</span>: <%= fileData.property.billdata[0].billNo %></p>
                    <p><span class="font-semibold">बिल दिनांक:</span> <%= fileData.property.billdata[0].bill_generation_date %></p>
                    <p><span class="font-semibold">वॉर्ड:</span> <%= fileData.property.ward ? fileData.property.ward.ward_name : '' %></p>
                  <p>
  <span class="font-semibold">नाव:</span>
  <%
    const ownerDetailsName = fileData.property.property_owner_details || [];

    // Check for is_payer true
    const payerOwner = ownerDetailsName.find(owner => owner.is_payer === "true");

    // Fallback to owner_type === 'स्वत:'
    const fallbackOwner = ownerDetailsName.find(owner => owner.owner_type?.owner_type === 'स्वत:');

    // Decide which name to show
    const firstOwnerName = payerOwner?.name || fallbackOwner?.name || 'No owner details available';
  %>
  <%= firstOwnerName %>
</p>
                  <p><span class="font-semibold">पत्ता/भागाचे नाव:</span> <%= fileData.property.street.street_name %></p>
                </div>
                <div>
                    <p><span class="font-semibold">जुना मालमता क्र.:</span> <%= fileData.property.old_propertyNumber %></p>
                    <p><span class="font-semibold">नवीन मालमता क्र.:</span> <%= fileData.property.propertyNumber %></p>
                    <p><span class="font-semibold">वापराचा प्रकार :</span> <%= fileData.property.property_usage_details[0].usageType.usage_type %></p>
                 

                    <p>
                      <span class="font-semibold">भोगवटादार:</span>
                      <% 
                        let bhogawatName = null;
                    
                      
                        if (fileData.ownerDetail && fileData.ownerDetail[0] && fileData.ownerDetail[0].bhogawat_owner_name) {
                          bhogawatName = fileData.ownerDetail[0].bhogawat_owner_name;
                        } else if (fileData.property.property_owner_details) {
                          let bhogawatOwner = fileData.property.property_owner_details.find(owner => owner.owner_type.owner_type === "भोगवटादार");
                          if (bhogawatOwner) {
                            bhogawatName = bhogawatOwner.name;
                          }
                        }
                      %>
                      <%= bhogawatName ? bhogawatName : "" %>
                    </p>
                    
                    <p><span class="font-semibold">रजिस्टर/झोन:</span> <%= fileData.property.zone ? fileData.property.zone.zoneName : '' %></p>
                    <p>
                      <span class="font-semibold">मोबाईल क्र:</span>
                      <%
                      const ownerDetails = fileData.property.property_owner_details || [];
                      let mobileNumber = "--";
                      
                      for (const owner of ownerDetails) {
                        if (owner.owner_type.owner_type === 'स्वत:' && owner.mobile_number && owner.mobile_number !== "null") {
                          mobileNumber = owner.mobile_number;
                          break; // Stop searching once a valid mobile number is found
                        }
                      }
                      
                      if (mobileNumber === "--") {
                        for (const owner of ownerDetails) {
                          if (owner.owner_type.owner_type !== 'स्वत:' && owner.mobile_number && owner.mobile_number !== "null") {
                            mobileNumber = owner.mobile_number;
                            break; // Stop searching once a valid mobile number is found
                          }
                        }
                      }
                      %>
                      <%= mobileNumber %>
                      
                    </p>            </div>
            </div>
        </div>

        <div class="overflow-x-auto px-1">
            <table class="min-w-full border-collapse border border-zinc-400">
                <thead>
                    <tr>
                        <th class="border border-zinc-400 px-4 py-2">करांचे तपशील </th>
                        <th class="border border-zinc-400 px-4 py-2">थकबाकी रक्कम </th>
                        <th class="border border-zinc-400 px-4 py-2">चालू मागणी रक्कम</th>
                        <th class="border border-zinc-400 px-4 py-2">एकूण रक्कम</th>
                    </tr>
                    <tr>
                      <th class="border border-zinc-400 px-4 py-2">1</th>
                      <th class="border border-zinc-400 px-4 py-2">2</th>
                      <th class="border border-zinc-400 px-4 py-2">3</th>
                      <th class="border border-zinc-400 px-4 py-2">4</th>
                  </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="border border-zinc-400 px-4 py-2">संकलित कर(घरपट्टी)</td>
                        <td class="border border-zinc-400 px-4 py-2"><%= fileData.all_property_tax_sum %></td>
                        <td class="border border-zinc-400 px-4 py-2"><%= fileData.all_property_tax_sum_current %></td>

                        <td class="border border-zinc-400 px-4 py-2"><%= fileData.all_property_tax_sum_total %></td>
                    </tr>
                    <% Object.entries(tax_types).forEach(([key, value]) => {
                      const formattedValue = fileData[key] ||  0;
                      const currentValueKey = `${key}_current`;
                      const previousValueKey = `${key}_previous`;
                      const previousValue = fileData[previousValueKey] || 0;
                      const currentValue = fileData[currentValueKey] ||  0;
                      const total = fileData[key] ||  0;
                    
                      if (formattedValue !== "null") {
                    %>
                      <tr>
                          <td class="border border-zinc-400 px-4 py-2"><%= value %></td>
                          <td class="border border-zinc-400 px-4 py-2"><%= previousValue %></td>
                          <td class="border border-zinc-400 px-4 py-2"><%= currentValue %></td>
                          <td class="border border-zinc-400 px-4 py-2"><%= total %></td>
                      </tr>
                    <%
                      }
                    });
                    %>
                 
                    <tr>
                      <td class="border border-zinc-400 px-4 py-2">वारंट फी</td>
                      <td class="border border-zinc-400 px-4 py-2">०</td>
                      <td class="border border-zinc-400 px-4 py-2">०</td>
                      <td class="border border-zinc-400 px-4 py-2">०</td>
                  </tr>
                  <tr>
                    <td class="border border-zinc-400 px-4 py-2">इतर</td>
                    <td class="border border-zinc-400 px-4 py-2">०</td>
                    <td class="border border-zinc-400 px-4 py-2">०</td>
                    <td class="border border-zinc-400 px-4 py-2">०</td>
                </tr>
                    <tr>
                        <td class="border border-zinc-400 px-4 py-2">एकूण देय रक्कम</td>
                        <td class="border border-zinc-400 px-4 py-2"><%= fileData.total_tax_previous%></td>
                        <td class="border border-zinc-400 px-4 py-2"><%= fileData.total_tax_current%></td>
                        <td class="border border-zinc-400 px-4 py-2"><%= fileData.total_tax %></td>
                    </tr>
                    <tr>
                        <td class="border border-zinc-400 px-4 py-2">अक्षरी रुपये</td>
                        <td colspan="3"><span id="rrr"><%= fileData.total_tax_in_marathi %> रुपये फक्त /-</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mb-4 px-1">
            <p class="text-xs">
                टीप:- १) स्तंभ ४ मध्ये दर्शविण्यात आलेल्या करांची रक्कम आपणाकडून सन २०२४-२५ या आर्थिक वर्षातील येणेअसून, आपणा हे
                बिल प्राप्त होताच १५ दिवसाच्या आत कराची सर्व रक्कम नगरपरिषदेकडे जमा करावी.
              </p>
              <p class="text-xs">
                २) अ) या बिलात मागणी केलेली रक्कम आपणाकडून देण्यात न आल्यास किंवा ब) ती का देण्यात आली नाही याबद्दल
                मुख्याधिकारी यांची खात्री पटेल असे कोणतेही कारण दर्शविण्यात आले नाही तर किंवा क) महाराष्ट्र नगरपरिषद अधिनियम
                १९६५ चे कलम १६९ नुसार कोणतेही अपील दाखल करण्यात आले नसेल , तर उक्त रक्कम देण्यासंबधी आपल्यावर कलम १५२
                नुसार जप्तीची कार्यवाही करणायत येईल. ड) अनधिकृत बांधकामास नियमानुसार शास्ती आकारणी करण्यात येईल.
              </p>
              <p class="text-xs">
                ३) मालमत्ता कराचे देयक व नोटीस देवूनही विहित मुदतीत कर भरणा न केलेस सबंधित मिळकतधारकांचे पाण्याचे नळ
                कनेक्शन कोणतीही पूर्वसूचना न देता खंडित केले जाईल, याची नोंद घ्यावी. 
              </p>
              <p class="text-xs bold">
                ४) कलम १५० अ (१) अन्वये दिलेल्या कराची रक्कम विहित मुदतीत न भरलेस ,न भरलेल्या येणे असलेल्या रक्कमेवर
                दरमहा २% प्रमाणे रक्कम शास्ती (दंड) आकारण्यात येईल, आणि बिलाची पूर्ण रक्कम भरेपर्यंत अशी शास्ती(दंड) भरणे
                बंधनकारक राहील.
              </p>
              <p class="text-xs">
                ५ ) जर आपण नव्याने बांधकाम केलेस / बांधकामात बदल केलेस अथवा मालमत्तेच्या वापरात बदल केला असलेस सुधारित कर
                आकारणी करण्यात येईल याची नोंद घ्यावी.
              </p>
              <p class="text-xs bold">
                ६) सन २०२४-२५ या आर्थिक वर्षात कलम १२४ (२) अन्वये शिरोळ नगरपरिषद हद्दीतील सर्व मिळकतीचे करपात्र मूल्यांमध्ये
                सुधारणा करण्यात आली असून, चालू मागणी रक्कम नविन कर आकारणीप्रमाणे स्तंभ ३ मध्ये दर्शविण्यात आली आहे.ज्या
                मिळकतधारकांनी चालू वर्षाची रक्कम भरली असेल, त्यांनी केवळ फरकांची रक्कम भरावयाची आहे, याची नोंद घ्यावी.
              </p>      
        </div>

        <div class="stamp">
            <img src="https://api-spms.onpointsoft.com/images/stamp.jpg" alt="" class="stamp_img">
            <div class="signature">
                <img src="https://api-spms.onpointsoft.com/images/sign.jpg" alt="" class="sign" />
                <p style="margin-right: 110px;">मुख्याधिकारी,</p>
                <p>शिरोळ नगरपरिषद, शिरोळ</p>
            </div>
        </div>
        <div>
            <p class="note" >चूक भूल द्यावी घ्यावी</p>
        </div>
    </div>
</body>
</html>
