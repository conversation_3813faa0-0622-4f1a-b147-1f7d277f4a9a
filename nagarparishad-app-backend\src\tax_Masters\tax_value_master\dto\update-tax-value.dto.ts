// src/tax-value-master/dto/update-tax-value.dto.ts
import { IsString, IsNumber, IsUUID, IsOptional, IsIn } from 'class-validator';

export class UpdateTaxValueDto {
  @IsString()
  @IsOptional()
  financial_year?: string; // Optional, allows updating the year

  @IsString()
  @IsOptional()
  reassessment_range_id?: string; // Reassessment range ID

  @IsNumber()
  @IsOptional()
  value?: number; // Optional new tax value

  @IsString()
  @IsOptional()
  @IsIn(['Active', 'Inactive'])
  status?: string; // Status can be updated to 'active' or 'inactive'

  @IsUUID()
  @IsOptional()
  propertyType_id?: string; // Optional property type ID for updating
}
