import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class OwnerDataDto {
  @ApiProperty({ description: 'Owner ID (required for update)', example: 'uuid' })
  @IsUUID()
  @IsOptional()
  property_owner_details_id?: string;

  @ApiProperty({ description: 'Owner type ID', example: 'uuid' })
  @IsUUID()
  owner_type_id: string;

  @ApiProperty({ description: 'Owner name', example: '<PERSON>' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Mobile number', example: '9876543210' })
  @IsString()
  @IsOptional()
  mobile_number?: string;

  @ApiProperty({ description: 'Email ID', example: '<EMAIL>' })
  @IsString()
  @IsOptional()
  email_id?: string;

  @ApiProperty({ description: 'Aadhar number', example: '123456789012' })
  @IsString()
  @IsOptional()
  aadhar_number?: string;

  @ApiProperty({ description: 'Gender', example: 'MALE' })
  @IsString()
  @IsOptional()
  gender?: string;

  @ApiProperty({ description: 'Marital status', example: 'yes' })
  @IsString()
  @IsOptional()
  marital_status?: string;

  @ApiProperty({ description: 'PAN card number', example: '**********' })
  @IsString()
  @IsOptional()
  pan_card?: string;

  @ApiProperty({ description: 'Partner name', example: 'Jane Doe' })
  @IsString()
  @IsOptional()
  partner_name?: string;
}

export class PropertyFerfarAddUpdateDto {
  @ApiProperty({ description: 'Property ID', example: 'uuid' })
  @IsUUID()
  property_id: string;

  @ApiProperty({ description: 'Remark for the ferfar operation', example: 'Property transferred due to sale' })
  @IsString()
  @IsOptional()
  remark?: string;

  @ApiProperty({ description: 'Array of owners to add or update', type: [OwnerDataDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OwnerDataDto)
  owners: OwnerDataDto[];

  @ApiProperty({ description: 'Array of photo image paths', type: [String] })
  @IsArray()
  @IsOptional()
  photos?: string[];

  @ApiProperty({ description: 'Array of document image paths', type: [String] })
  @IsArray()
  @IsOptional()
  documents?: string[];
}
