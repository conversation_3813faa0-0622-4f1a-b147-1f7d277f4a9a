import { useRoutes } from "react-router-dom";
import router from "../routes";
import React, { useEffect } from "react";

const App = () => {
  const content = useRoutes(router);

  const currentAppVersion = "1.0.1"; // Update this version number with each release
  const storedAppVersion = localStorage.getItem("appVersion");

  useEffect(() => {
    // Only run version check once on app initialization
    const runVersionCheck = () => {
      if (storedAppVersion === null) {
        // First time user - just set the version
        localStorage.setItem("appVersion", currentAppVersion);
      } else if (storedAppVersion !== currentAppVersion) {
        // Version changed - preserve user authentication but clear other data
        const userData = localStorage.getItem("UserData");
        const permissions = localStorage.getItem("permissions");

        // Clear all localStorage
        localStorage.clear();

        // Restore important data
        if (userData) {
          localStorage.setItem("UserData", userData);
        }
        if (permissions) {
          localStorage.setItem("permissions", permissions);
        }

        // Update the stored version
        localStorage.setItem("appVersion", currentAppVersion);
      }
    };

    runVersionCheck();
  }, []); // Empty dependency array to run only once

  return <>{content}</>;
};

export default App;
