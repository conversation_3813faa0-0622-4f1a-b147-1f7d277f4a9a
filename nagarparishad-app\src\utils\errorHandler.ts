// HTTP Status Codes
export enum StatusCode {
  OK = 200,
  CREATED = 201,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503
}

// Error types
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  SERVER_ERROR = 'SERVER_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  DUPLICATE_ENTRY = 'DUPLICATE_ENTRY',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// Error response structure
export interface ErrorResponse {
  type: ErrorType;
  message: string;
  statusCode: StatusCode;
  details?: any;
}

// Map HTTP status codes to error types
export const mapStatusToErrorType = (statusCode: number): ErrorType => {
  switch (statusCode) {
    case StatusCode.BAD_REQUEST:
      return ErrorType.VALIDATION_ERROR;
    case StatusCode.UNAUTHORIZED:
      return ErrorType.AUTHENTICATION_ERROR;
    case StatusCode.FORBIDDEN:
      return ErrorType.AUTHORIZATION_ERROR;
    case StatusCode.NOT_FOUND:
      return ErrorType.RESOURCE_NOT_FOUND;
    case StatusCode.CONFLICT:
      return ErrorType.DUPLICATE_ENTRY;
    case StatusCode.INTERNAL_SERVER_ERROR:
    case StatusCode.SERVICE_UNAVAILABLE:
      return ErrorType.SERVER_ERROR;
    default:
      return ErrorType.UNKNOWN_ERROR;
      
       
  }
};

// Get localized error message key based on error type
export const getErrorMessageKey = (errorType: ErrorType): string => {
  switch (errorType) {
    case ErrorType.VALIDATION_ERROR:
      return 'errorsInvalidInput';
    case ErrorType.AUTHENTICATION_ERROR:
      return 'errorsUnauthorized';
    case ErrorType.AUTHORIZATION_ERROR:
      return 'errorsForbidden';
    case ErrorType.RESOURCE_NOT_FOUND:
      return 'errorsResourceNotFound';
    case ErrorType.SERVER_ERROR:
      return 'errorsInternalServerError';
    case ErrorType.NETWORK_ERROR:
      return 'errorsNetworkError';
    case ErrorType.DUPLICATE_ENTRY:
      return 'errorsDuplicateEntry';
    default:
      return 'errorsUnknownError';
  }
};

// Handle API errors
export const handleApiError = (error: any): ErrorResponse => {
  // Network error (no response)
  if (!error.response) {
    return {
      type: ErrorType.NETWORK_ERROR,
      message: 'Network error. Please check your connection.',
      statusCode: StatusCode.SERVICE_UNAVAILABLE
    };
  }

  const { status, data } = error.response;
  const errorType = mapStatusToErrorType(status);
  
  return {
    type: errorType,
    message: data?.message || 'An error occurred',
    statusCode: status,
    details: data
  };
};

// Custom hook for displaying error toasts
export const useErrorToast = (toast: any, t: any) => {
  return (error: any) => {
    const errorResponse = handleApiError(error);
    const messageKey = getErrorMessageKey(errorResponse.type);
    
    toast({
      title: t(messageKey),
      description: errorResponse.message,
      variant: "destructive",
    });
    
    return errorResponse;
  };
};