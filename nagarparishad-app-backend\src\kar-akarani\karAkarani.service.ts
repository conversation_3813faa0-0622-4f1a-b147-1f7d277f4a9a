import { format } from 'date-fns';
import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { Response } from 'express';
import { EntityManager, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { Buffer } from 'buffer';
import { In, LessThan } from 'typeorm';
import { validate as isUUID } from 'uuid'; // Import UUID validation function
import { log_type, log_sub_type, api } from 'src/utils/constants';
import * as path from 'path';
import {
  StreetMasterEntity,
  Ward_Master,
  ZoneMaster,
  Owner_type_master,
  UsageTypeMasterEntity,
  PropertyTypeMasterEntity,
  Property_Owner_Details_Entity,
  Property_Usage_Details_Entity,
  CommonFiledsOfPropertyEntity,
  MilkatKarTaxEntity,
  MilkatKarEntity,
} from 'libs/database/entities';

import {
  ImportPropertyMasterRepository,
  ImportPropertyStatsMasterRepository,
  GIS_data_Repository,
  StreetMasterRepository,
  ZoneMasterRepository,
  WardMasterRepository,
  PropertyMasterRepository,
  OwnerTypeRepository,
  Property_Owner_DetailsRepository,
  Property_Usage_DetailsRepository,
  UsageMasterRepository,
  PropertyTypeMasterRepository,
  LogsRepository,
  MilkatKareRepository,
  MilkatKarTaxeRepository,
  Master_rr_rateRepository,
  Master_depreciationRepository,
  Master_rr_construction_rateRepository,
  Master_WeightingRepository,
  Master_TaxValueRepository,
  FloorMasterRepository,
  UsageSubMasterRepository,
  MasterGhanKachraRateRepository,
  Financial_yearRepository,
  PaymentInfoRepository
} from 'libs/database/repositories';

import { Import_PropertyEntity, PropertyEntity } from 'libs/database/entities'; // Adjust the import path as needed
import { get } from 'http';
import { contains } from 'class-validator';
import { Json } from 'aws-sdk/clients/robomaker';
import { TAX_TYPES } from '@helper/helpers/tax-types.helper';
import { ReassessmentRangeRepository } from 'libs/database/repositories/reassesment.repository';

@Injectable()
export class KarAkaraniService {
  constructor(
    private readonly importPropertyMasterRepository: ImportPropertyMasterRepository,
    private readonly importPropertyStatsMasterRepository: ImportPropertyStatsMasterRepository,
    private readonly gis_data_Repository: GIS_data_Repository,
    private readonly streetMasterRepository: StreetMasterRepository,
    private readonly zoneMasterRepository: ZoneMasterRepository,
    private readonly wardMasterRepository: WardMasterRepository,
    private readonly propertyMasterRepository: PropertyMasterRepository,
    private readonly ownerTypeRepository: OwnerTypeRepository,
    private readonly property_Owner_DetailsRepository: Property_Owner_DetailsRepository,
    private readonly property_Usage_DetailsRepository: Property_Usage_DetailsRepository,
    private readonly usageMasterRepository: UsageMasterRepository,
    private readonly propertyTypeMasterRepository: PropertyTypeMasterRepository,
    private readonly logsRepository: LogsRepository,
    private readonly propertyUsageDetailsRepository: Property_Usage_DetailsRepository,
    private readonly milkatKareRepository: MilkatKareRepository,
    private readonly milkatKarTaxeRepository: MilkatKarTaxeRepository,
    private readonly master_rr_rateRepository: Master_rr_rateRepository,
    private readonly master_depreciationRepository: Master_depreciationRepository,
    private readonly master_rr_construction_rateRepository: Master_rr_construction_rateRepository,
    private readonly master_WeightingRepository: Master_WeightingRepository,
    private readonly master_TaxValueRepository: Master_TaxValueRepository,
    private readonly milkatKarAkarani: MilkatKareRepository,
    private readonly floorRepo: FloorMasterRepository,
    private readonly UsageSubTypeRepo: UsageSubMasterRepository,
    private readonly Master_Ghankachar: MasterGhanKachraRateRepository,
    private readonly financialYearRepo: Financial_yearRepository,
    private readonly reAssesmentRepo: ReassessmentRangeRepository,
    private readonly paymentInfoMasterRepository: PaymentInfoRepository,
  ) {}

  async findAll(params: any) {
    const { ward, value, searchOn, ...options } = params;
    const checkData = await this.propertyMasterRepository.getMilkatKarAll(
      ward,
      value,
      searchOn,
      options,
    );
    if (!checkData) {
      return {
        message: 'Data Not found',
      };
    }

    return {
      data: checkData,
      message: 'Data found Sucess',
    };
  }
  async processMilakatKarAkarni(wardNumber: string) {
    try {
      // Step 1: Fetch all properties for the given ward
      const properties = await this.propertyMasterRepository.find({
        where: { ward: { ward_name: wardNumber } },
        order: { sr_no: 'ASC' },
      });
      const currentReassesmentRange =
        await this.reAssesmentRepo.getCurrentReassesmentRange();

      // Step 2: Loop through each property and call the individual tax calculation function
      for (const property of properties) {
        let currentMilkatKar = await this.milkatKarAkarani.findOne({
          where: {
            property: { property_id: property.property_id },
            reassessmentRange: {
              reassessment_range_id:
                currentReassesmentRange.reassessment_range_id,
            },
            status: 'active',
          },
        });
        if (!currentMilkatKar || property.updateStatus === 'Updated') {
                    await this.calculateTaxForProperty(property.property_id);

        }
      }
      return {
        message: 'Milkat Kar Akarni Process Completed for all properties',
      };
    } catch (error) {
      console.error('Error processing Milkat Kar Akarni:', error);
      throw error;
    }
  }

  async processMilakatKarAkarniByReassessmentRange(reassessmentYearId: string) {
    try {
      // Step 1: Check if the reassessment range exists
      const reassessmentRange =
        await this.reAssesmentRepo.findById(reassessmentYearId);
      if (!reassessmentRange) {
        throw new NotFoundException('Reassessment range not found');
      }

      // Step 2: Fetch all properties
      const properties = await this.propertyMasterRepository.find({
        order: { sr_no: 'ASC' },
      });

      // Step 3: Loop through each property and call the individual tax calculation function
      let processedCount = 0;
      for (const property of properties) {
        try {
          // Calculate tax for the property with the specified reassessment range
          // await this.calculateTaxForPropertyWithReassessmentRange(property.property_id, reassessmentYearId);
          await this.calculateTaxForProperty(property.property_id);
          processedCount++;
        } catch (error) {
          console.error(
            `Error processing property ${property.property_id}:`,
            error,
          );
          // Continue with the next property even if one fails
        }
      }

      return {
        message: `Milkat Kar Akarni Process Completed for ${processedCount} out of ${properties.length} properties`,
        processedCount,
        totalCount: properties.length,
      };
    } catch (error) {
      console.error(
        'Error processing Milkat Kar Akarni by reassessment range:',
        error,
      );
      throw error;
    }
  }

  async getMilkatKarDataByReassessmentRange(reassessmentYearId: string) {
    try {
      // Check if the reassessment range exists
      const reassessmentRange =
        await this.reAssesmentRepo.findById(reassessmentYearId);
      if (!reassessmentRange) {
        throw new NotFoundException('Reassessment range not found');
      }

      // Get Milkat Kar data for the specified reassessment range
      const milkatKarData =
        await this.milkatKareRepository.getMilkatKarByReassessmentRange(
          reassessmentYearId,
        );

      // Format the result
      const formattedResult = {
        reassessment_range_id: reassessmentRange.reassessment_range_id,
        start_range: reassessmentRange.start_range,
        end_range: reassessmentRange.end_range,
        reassessment_range: `${reassessmentRange.start_range} to ${reassessmentRange.end_range}`,
        total_properties: milkatKarData.length,
        total_property_processed: milkatKarData.filter(
          (item) => item.status === 'active',
        ).length,
        milkat_kar_count: milkatKarData.filter(
          (item) => item.status === 'active',
        ).length,
        data: milkatKarData,
      };

      return {
        statusCode: 200,
        message: 'Milkat Kar data retrieved successfully',
        data: formattedResult,
      };
    } catch (error) {
      console.error(
        'Error retrieving Milkat Kar data by reassessment range:',
        error,
      );
      throw error;
    }
  }

  // New function to calculate tax for an individual property with a specific reassessment range

  async calculateShasteeFee(
    area: number,
    isvalid: boolean,
    tax: number,
    usageType: string,
  ): Promise<number> {
    if (isvalid) {
      return 0; // If isvalid is true, return zero
    }

    const validUsageType=['वाणिज्य','औद्योगिक']
      const isCommercial = validUsageType.includes(usageType);


    if (isCommercial) {
      return tax * 2; // For commercial buildings, the fee is always 2 times the building tax
    }

    
    // For residential buildings, calculate based on the area
    if (area <= 600) {
      return 0; // None
    } else if (area <= 1000) {
      return (tax*0.5); // 50% of the building tax
    } else {
      return tax * 2; // 2 times the building tax
    }
  }



  // New function to calculate tax for an individual property
  async calculateTaxForProperty(
    property_id: string,
    transactionManager?: EntityManager, // Optional transaction manager
  ) {
    try {
      const currentFinancialYear =
        await this.financialYearRepo.getCurrentFinancialYear();

      const paymentRecord = await this.paymentInfoMasterRepository.findOne({
        where: {
          property: { property_id: property_id },
          financial_year_range: currentFinancialYear.financial_year_range,
        },
      });

      // if (paymentRecord) {
      //   throw new ForbiddenException(
      //     `Payment already made for property  for the financial year ${currentFinancialYear.financial_year_range}`,
      //   );
      // }
      let totalPropertyTaxSum = 0;

      // Fetch the property details using transactionManager if available
      const propertyRepository = transactionManager
        ? transactionManager.getRepository(PropertyEntity)
        : this.propertyMasterRepository;

      const property = await propertyRepository.findOne({
        where: { property_id: property_id },
        relations: ['zone'],
      });

      if (!property) {
        throw new Error(`Property not found with ID: ${property_id}`);
      }

      // Fetch all usage details for the current property
      const usageDetails = await (
        transactionManager
          ? transactionManager.getRepository(Property_Usage_Details_Entity)
          : this.propertyUsageDetailsRepository
      ).find({
        where: { property: { property_id: property.property_id } },
        relations: ['propertyType', 'usageType', 'floorType', 'usageSubType'],
      });

      if (!usageDetails || usageDetails.length === 0) {
        console.warn(
          `No usage details found for property ID: ${property.property_id}. Skipping tax calculation.`,
        );
        return {
          message: `No usage details found for property ID: ${property.property_id}. Skipping tax calculation.`,
        };
      }
      console.log('usage detaislss--->', usageDetails);

      let capital_value_total = 0;
      let tax_value_total = 0;
      let rojagar_hami_kar_total = 0;
      let vruskh_kar_total = 0;
      let shikhan_kar_total = 0;
      let padsar_kar_total = 0;
      let property_type_discount = 0;
      let shastiFeeTotal = 0;

      const insertedMilkatKarTaxes = [];

      // Loop through each usage detail and calculate tax
      const ghanKachra = [];

     const unauthorizedUsages = usageDetails.filter(
  (usage) => !usage.authorized && usage.propertyType?.propertyType?.trim() !== 'पडसर'
);

// Calculate total unauthorized area
const totalUnauthorizedArea = unauthorizedUsages.reduce(
  (sum, usage) => sum + Number(usage.are_sq_ft),
  0
);

          console.log("unauthorizedUsages",unauthorizedUsages,totalUnauthorizedArea)

      for (const usage of usageDetails) {
        const {
          are_sq_meter,
          propertyType,
          construction_start_year,
          usageType,
          floorType,
          usageSubType,
        } = usage;
        let currentGhanKacharatax = 0;
        console.log("padsar_kar_total",padsar_kar_total)

        console.log("shastiifeetoatla",shastiFeeTotal)


        const usageSubTypeId = usageSubType
          ? usageSubType.usage_sub_type_master_id
          : null;
        const floorTypeId = floorType ? floorType.floor_id : null;
        const all_tax_values = await this.get_property_tax_values(
          property.zone.zone_id,
          propertyType.propertyType_id,
          construction_start_year,
          usageType.usage_type_id,
          are_sq_meter,
          propertyType.propertyType,
          usageSubTypeId,
          floorTypeId,
          usageType.usage_type,

        );

        const {
          rr_rate,
          rr_construction_rate,
          depreciation_rate,
          weighting,
          capital_value,
          tax_value,
          tax,
          GhanKachar,
          vruskh_kar,
          shikhan_kar,
          rojagar_hami_kar,
          padsar_kar,
          padsar_kar_tax,
          property_age,
        } = all_tax_values;
        capital_value_total += capital_value;
        tax_value_total += tax_value;
        rojagar_hami_kar_total += rojagar_hami_kar;
        vruskh_kar_total += vruskh_kar;
        shikhan_kar_total += shikhan_kar;
        padsar_kar_total += padsar_kar;

let shastiFee = 0; 
console.log("propertyType?.propertyType?.trim()",propertyType?.propertyType?.trim())
     if(propertyType?.propertyType?.trim()!=='पडसर'){   
  shastiFee= await this.calculateShasteeFee(
   totalUnauthorizedArea,
    Boolean(usage.authorized) || false,
    tax,
    usageType.usage_type
  );
  console.log("shastiFeeshastiFee",usage.authorized,shastiFee,totalUnauthorizedArea,"taxx",tax, usageType.usage_type,"tax_value",tax_value);

  shastiFeeTotal += shastiFee;
}



console.log("shastiifeetoatla",shastiFeeTotal)
let propertySpecificDiscount = 0;
        //
        // will connet out later
        if (usageType.usage_type === 'धार्मिक') {
          property_type_discount += tax;
          propertySpecificDiscount=tax;
          totalPropertyTaxSum +=tax;
        } else if (usageType.usage_type === 'शैक्षणिक') {
          const discount = tax * 0.5;
                    propertySpecificDiscount=discount;

          property_type_discount += discount;
          totalPropertyTaxSum += tax;
        } else if (propertyType?.propertyType?.trim()==='पडसर') {
          
        } else{
                    totalPropertyTaxSum += tax;

        }

        // const shastiFee = await this.calculateShasteeFee(usage.are_sq_ft, usage?.authorized || false, tax, usageType);

        const Floor_Type = floorTypeId
          ? await this.floorRepo.findOne({ where: { floor_id: floorTypeId } })
          : null;
        let includeRRRate = null;
        if (Floor_Type) {
          includeRRRate == Floor_Type;
        }

        const all_tax_values_json: Record<string, any> = {
          capital_value_formula: includeRRRate
            ? includeRRRate.floor_name?.trim() === 'तळ मजला'
              ? `=((${are_sq_meter} * ${rr_rate}) + (${are_sq_meter} * ${rr_construction_rate} * ${depreciation_rate})) * ${weighting}`
              : `=(${are_sq_meter} * ${rr_construction_rate} * ${depreciation_rate}) * ${weighting}`
            : `=((${are_sq_meter} * ${rr_rate}) + (${are_sq_meter} * ${rr_construction_rate} * ${depreciation_rate})) * ${weighting}`,
          emarat_tax_formula_fields:
            '=((( are_sq_meter * rr_rate) + ( are_sq_meter * rr_construction_rate * depreciation_rate)) * weighting ) * tax_rate ',
          zoneId: property.zone.zone_id,
          propertyTypeId: propertyType.propertyType_id,
          propertyType: propertyType.propertyType,
          constructionStartYear: construction_start_year,
          areaSqMeter: are_sq_meter,
          usageType: {
            usageTypeId: usageType.usage_type_id,
            usageType: usageType.usage_type,
          },
          usageSubType: {
            usageSubTypeId: usageSubType?.usage_sub_type_master_id,
            usageSubType: usageSubType?.usage_sub_type,
          },
          floorType: {
            floorTypeId: floorType?.floor_id,
            floorType: floorType?.floor_name,
          },
          // length: 15, // Example value, replace with actual
          // breadth: 10, // Example value, replace with actual
          taxCalculationDetails: {
            rrRate: rr_rate,
            rrConstructionRate: rr_construction_rate,
            depreciationRate: depreciation_rate,
            weighting: weighting,
            capitalValue: capital_value,
            taxValue: tax_value,
            tax: tax,
            ghanKachar: GhanKachar,
            vruskhKar: vruskh_kar,
            shikhanKar: shikhan_kar,
            rojagarHamiKar: rojagar_hami_kar,
            padsarKar: padsar_kar,
            property_age: property_age,
          },
        };

        //pushing ghnakacher in tax
        // Assuming GhanKachar and other variables are already defined
        ghanKachra.push(GhanKachar);

        console.log("shastiifeet",shastiFee)

        const milkatKarTax = (
          transactionManager
            ? transactionManager.getRepository(MilkatKarTaxEntity)
            : this.milkatKarTaxeRepository
        ).create({
          property,
          sq_ft_meter: are_sq_meter,
          length: usage.length || 0,
          width: usage.length || 0,
          rr_rate,
          rr_construction_rate,
          depreciation_rate,
          weighting,
          capital_value: Math.round(capital_value), // Round capital_value
          tax_value: tax_value, // Round tax_value
          shasti_fee: Math.round(shastiFee), // Round shasti_fee
          property_type_discount: Math.round(propertySpecificDiscount), // Round property_type_discount
          tax: Math.round(tax), // Round tax
          padsar_kar_tax: padsar_kar_tax,
          bill_generation_date: new Date(),
          property_usage_details: usage,
          tax_data: all_tax_values_json,
        });

        const savedMilkatKarTax = await (transactionManager
          ? transactionManager.save(milkatKarTax)
          : this.milkatKarTaxeRepository.save(milkatKarTax));

        insertedMilkatKarTaxes.push(savedMilkatKarTax);
      }

      // Additional tax types
      let tax_type_1 = Math.round(vruskh_kar_total);
      let tax_type_2 = Math.round(shikhan_kar_total);
      let tax_type_3 = Math.round(rojagar_hami_kar_total);
      let tax_type_8 = Math.round(padsar_kar_total);
      let tax_type_5 = Math.round(shastiFeeTotal);

      let tax_type_4 = Math.max(...ghanKachra);
      totalPropertyTaxSum = Math.round(totalPropertyTaxSum);
      let total_tax =
        totalPropertyTaxSum +
        tax_type_1 +
        tax_type_2 +
        tax_type_3 +
        tax_type_4 +
  tax_type_5+
       tax_type_8;
      total_tax = parseFloat(total_tax.toFixed(0));
      console.log('total_tax--->', total_tax);
      // const other_tax_sum_tax =
      //   tax_type_1 + tax_type_2 + tax_type_3 + tax_type_4 + tax_type_8;

      const other_tax_sum_tax = 0;

      // Mark previous MilkatKar records as inactive
      await (
        transactionManager
          ? transactionManager.getRepository(MilkatKarEntity)
          : this.milkatKareRepository
      ).update(
        { property: property, status: 'active' },
        { status: 'inactive' },
      );

      const currentReassesmentRange =
        await this.reAssesmentRepo.getCurrentReassesmentRange();
      const milkatKar = (
        transactionManager
          ? transactionManager.getRepository(MilkatKarEntity)
          : this.milkatKareRepository
      ).create({
        property,
        financial_year: currentFinancialYear.financial_year_range || '',
        all_property_tax_sum: totalPropertyTaxSum,
        tax_type_1: tax_type_1,
        tax_type_2: tax_type_2,
        tax_type_3: tax_type_3,
        tax_type_4: tax_type_4,
        tax_type_8: tax_type_8,
                tax_type_5: tax_type_5,

        total_tax: total_tax,
        other_tax_sum_tax: other_tax_sum_tax,
        property_type_discount: Math.round(property_type_discount || 0),
        reassessmentRange: currentReassesmentRange,
        status: 'active',
      });

      const savedMilkatKar = await (transactionManager
        ? transactionManager.save(milkatKar)
        : this.milkatKareRepository.save(milkatKar));

      for (const milkatKarTax of insertedMilkatKarTaxes) {
        await (
          transactionManager
            ? transactionManager.getRepository(MilkatKarTaxEntity)
            : this.milkatKarTaxeRepository
        ).update(
          { milkatKartax_id: milkatKarTax.milkatKartax_id },
          { MilkatKar: savedMilkatKar },
        );
      }

      await (
        transactionManager
          ? transactionManager.getRepository(PropertyEntity)
          : this.propertyMasterRepository
      ).update(
        { property_id: property.property_id },
        { updateStatus: 'milkatKarUpdated' },
      );

      return {
        message: `Milkat Kar tax calculation completed for property ID: ${property_id}`,
      };
    } catch (error) {
      console.error(
        `Error calculating tax for property ID+=: ${property_id}:`,
        error,
      );
      throw new Error(
        `Error calculating tax for property ID+: ${property_id}: ${error.message}`,
      );
    }
  }

  async getMilkatKarAkarni(value: string, searchOn: string, fy: string) {
    try {
      //const { propertyNumber } = propertyNumber;

      // Get the reassessment range for the given financial year
      const currentReassesmentRange =
        await this.reAssesmentRepo.getReassessmentRangeByYear(fy);
      console.log('currentReassesmentRange------>', currentReassesmentRange);
      if (!currentReassesmentRange) {
        return {
          message: 'Reassessment range not found for the given financial year',
          data: {
            property_count: 0,
            property_id: '',
          },
        };
      }

      const currentReassessmentRangeId =
        currentReassesmentRange.reassessment_range_id;

      // Use the reassessment_range_id to get the Milkat Kar data
      const checkData = await this.propertyMasterRepository.getMilkatKar(
        value,
        searchOn,
        fy,
        currentReassessmentRangeId,
      );

      const get_property_count =
        await this.propertyMasterRepository.getCountOfProperty(value, searchOn);
      let property_count = 0;
      let property_id = '';
      if (get_property_count) {
        property_count = 1;
        property_id = get_property_count.property_id;
      }
      if (!checkData) {
        return {
          message: 'Data Not found',
          data: {
            property_count,
            property_id,
          },
        };
      }

      // Format and add construction_year
      const updatedData = JSON.parse(JSON.stringify(checkData)); // Deep clone to modify

      // Adding construction_year to each property_usage_details
      if (updatedData.property_usage_details) {
        updatedData.property_usage_details =
          updatedData.property_usage_details.map((detail: any) => {
            const constructionEndDate = new Date(detail.construction_end_date);
            const constructionYear = constructionEndDate.getFullYear();

            return {
              ...detail,
              construction_year: constructionYear,
            };
          });
      }

      const tax_types = {
        tax_type_8: TAX_TYPES.tax_type_8, // पडसर कर
        tax_type_1: TAX_TYPES.tax_type_1, // वृक्ष उपकर
        tax_type_2: TAX_TYPES.tax_type_2, // शिक्षण उपकर
        tax_type_3: TAX_TYPES.tax_type_3, // रोजगार हमी कर
        tax_type_4: TAX_TYPES.tax_type_4, // घनकचरा शुल्क
        tax_type_5: TAX_TYPES.tax_type_5, // अनधिकृत शास्ती कर
        tax_type_6: TAX_TYPES.tax_type_6, // दिवाबत्ती कर
        tax_type_7: TAX_TYPES.tax_type_7, // आरोग्य कर
        tax_type_9: TAX_TYPES.tax_type_9, // दंड
        tax_type_10: TAX_TYPES.tax_type_10, // अग्निशमन फी
      };

      return {
        message: 'Data found Success',
        data: {
          ...updatedData,
          tax_types,
          // waste_tax: 5 // Adding waste_tax
        },
      };
    } catch (error) {
      throw error;
    }
  }

  //formula calculation get values
  async get_property_tax_values(
    zone_id: string,
    propertyType: string,
    construction_start_year: string | null,
    usage_type: string,
    sq_ft_meter: number,
    property_type_name: string,
    usageSubType: any,
    floorType: any,
    usage_type_name: string,
  ) {
    try {
      // Step 1: Fetch the RR rate from the master_rr_rate table
      const property_class = await this.propertyTypeMasterRepository.findOne({
        where: { propertyType_id: propertyType },
        relations: ['property_type_class'],
      });

      const Floor_Type = floorType
        ? await this.floorRepo.findOne({ where: { floor_id: floorType } })
        : null;

      const property_class_id = property_class?.property_type_class;
      if (!property_class_id) {
        throw new Error(
          `Property type class not found - ${property_class_id}-${propertyType}-${property_class?.property_type_class}-${property_class}`,
        );
      }
      const UsageSubType = usageSubType
        ? await this.UsageSubTypeRepo.findOne({
            where: { usage_sub_type_master_id: usageSubType },
          })
        : null;
      const currentFinancialYear =
        await this.financialYearRepo.getCurrentFinancialYear();
      const currentReassessmentRange =
        await this.reAssesmentRepo.getCurrentReassesmentRange();
        console.log("currentFinancialYear-->",currentFinancialYear)
                console.log("currentReassessmentRange-->",currentReassessmentRange)

      const rr_rate_record = await this.master_rr_rateRepository.findOne({
        where: {
          zone: { zone_id: zone_id },
          financial_year: currentFinancialYear.financial_year_range || '',
          status: 'Active',
          reassessmentRange: {reassessment_range_id: currentReassessmentRange.reassessment_range_id},
        },
      });

      if (!rr_rate_record) {
        throw new Error(`RR Rate not found for zone ID: ${zone_id}`);
      }

      // Step 2: Fetch the RR construction rate from the master_rr_construction_rate table
      const rr_construction_rate_record =
        await this.master_rr_construction_rateRepository
          .createQueryBuilder('Master_rr_construction_rate')
          .innerJoinAndSelect(
            'Master_rr_construction_rate.property_type_class_id',
            'Property_type_class_master',
          )
          .where(
            'Master_rr_construction_rate.property_type_class_id = :property_type_class_id',
            {
              property_type_class_id: property_class_id.property_type_class_id,
            },
          )
          .andWhere(
            'Master_rr_construction_rate.financial_year = :financial_year',
            {
              financial_year: currentFinancialYear.financial_year_range || '',
            },
          )
          .andWhere('Master_rr_construction_rate.status = :status', {
            status: 'Active',
          })
          .andWhere(
            'Master_rr_construction_rate.reassessment_range_id = :reassessmentRangeId',
            {
              reassessmentRangeId:
                currentReassessmentRange.reassessment_range_id,
            },
          )
          .getOne();

      if (!rr_construction_rate_record) {
        throw new Error(
          `RR Construction Rate not found for property type: ${propertyType}`,
        );
      }

      // Step 3: Calculate the property age and fetch depreciation rate from master_depreciation_rate
      const property_age =
        Number(construction_start_year) === 0 ||
        isNaN(Number(construction_start_year))
          ? 0
          : Math.max(
              new Date().getFullYear() - 1 - Number(construction_start_year),
              0,
            );

      let depreciation_rate_record = null;
      if (property_type_name?.trim() !== 'पडसर') {
        depreciation_rate_record = await this.master_depreciationRepository
          .createQueryBuilder('depreciationRate')
          .leftJoinAndSelect(
            'depreciationRate.property_type_class_id',
            'propertyTypeClass',
          )
          .where(
            'propertyTypeClass.property_type_class_id = :propertyTypeClassId',
            { propertyTypeClassId: property_class_id.property_type_class_id },
          )
          .andWhere('depreciationRate.from_age <= :propertyAge', {
            propertyAge: property_age,
          })
          .andWhere('depreciationRate.to_age >= :propertyAge', {
            propertyAge: property_age,
          })
          .andWhere('depreciationRate.financial_year = :financialYear', {
            financialYear: currentFinancialYear.financial_year_range || '',
          })
          .andWhere('depreciationRate.status = :status', { status: 'Active' })
          .andWhere('depreciationRate.deleted_at IS NULL')
          .andWhere(
            'depreciationRate.reassessment_range_id = :reassessmentRangeId',
            {
              reassessmentRangeId:
                currentReassessmentRange.reassessment_range_id,
            },
          )
          .getOne();

        if (!depreciation_rate_record) {
          throw new Error(
            `Depreciation rate not found for property type: ${propertyType} and age: ${property_age}`,
          );
        }
      }

      // Step 4: Fetch the weighting rate from the master_weighting_rate table
      const weighting_rate_record =
        await this.master_WeightingRepository.findOne({
          where: {
            usage_type: { usage_type_id: usage_type },
            financial_year: currentFinancialYear.financial_year_range || '',
            status: 'Active',
          reassessmentRange: {reassessment_range_id: currentReassessmentRange.reassessment_range_id},
          },
        });

      if (!weighting_rate_record) {
        throw new Error(
          `Weighting rate not found for usage type: ${usage_type}`,
        );
      }

      // Step 5: Fetch the tax rate from the master_tax_rate table
      const tax_rate_record = await this.master_TaxValueRepository.findOne({
        where: {
          property_type: { propertyType_id: propertyType },
          financial_year: currentFinancialYear.financial_year_range || '',
          status: 'Active',
          reassessmentRange: {reassessment_range_id: currentReassessmentRange.reassessment_range_id},
        },
      });

      if (!tax_rate_record) {
        throw new Error(
          `Tax rate not found for property type: ${propertyType}`,
        );
      }

      // Step 6: Calculate the capital value
      let capital_value = 0;
      let padsar_kar = 0;
      let padsar_kar_tax = 0;
      console.log('property_type_name', property_type_name);
      if (property_type_name?.trim() == 'पडसर') {
        console.log(
          'padsar_kar_cala calcualtion of padsar kar',
          rr_rate_record.value,
          weighting_rate_record.value,
        );
        padsar_kar =
          (sq_ft_meter * rr_rate_record.value + sq_ft_meter * 0 * 0) *
          weighting_rate_record.value;
        console.log('depreciation_rate_record', depreciation_rate_record);
        //depreciation_rate_record keep 0
        capital_value =
          (sq_ft_meter * rr_rate_record.value +
            sq_ft_meter * rr_construction_rate_record?.value * 0) *
          weighting_rate_record?.value;

        console.log(
          `capital_value in --> === (${sq_ft_meter} * ${rr_rate_record.value} + ${sq_ft_meter} * ${rr_construction_rate_record.value} * ${depreciation_rate_record?.value}) * ${weighting_rate_record.value} = ${capital_value}`,
        );
        console.log('CAPTIAL non floor ', capital_value);
      } else {
        //if floor 1- to 3 then no
        if (
          Floor_Type &&
          (Floor_Type.floor_name?.trim() === 'पहिला मजला' ||
            Floor_Type.floor_name?.trim() === 'दुसरा मजला' ||
            Floor_Type.floor_name?.trim() === 'तिसरा मजला')
        ) {
          console.log(
            'Floor_Type.floor_name.trim()',
            Floor_Type.floor_name?.trim(),
          );
          capital_value =
            (sq_ft_meter * 0 +
              sq_ft_meter *
                rr_construction_rate_record.value *
                depreciation_rate_record?.value) *
            weighting_rate_record.value;

          console.log(
            `capital_value in  === (${sq_ft_meter} * ${rr_rate_record.value} + ${sq_ft_meter} * ${rr_construction_rate_record.value} * ${depreciation_rate_record?.value}) * ${weighting_rate_record.value} = ${capital_value}`,
          );
          console.log('CAPTIAL non floor ', capital_value);
        } else {
          console.log(
            'Floor_Type.floor_name.trim() noo',
            Floor_Type?.floor_name?.trim(),
          );

          capital_value =
            (sq_ft_meter * rr_rate_record.value +
              sq_ft_meter *
                rr_construction_rate_record.value *
                depreciation_rate_record?.value) *
            weighting_rate_record.value;
          console.log(
            `capital_value in flortyoe === (${sq_ft_meter} * ${rr_rate_record.value} + ${sq_ft_meter} * ${rr_construction_rate_record.value} * ${depreciation_rate_record?.value}) * ${weighting_rate_record.value} = ${capital_value}`,
          );
          console.log('CAPTIAL ', capital_value);
        }
      }

      capital_value = parseFloat(capital_value.toFixed(2));
      padsar_kar = parseFloat(padsar_kar.toFixed(2));

      // Step 7: Calculate the tax value using the fetched tax rate
      let tax_value = 0;
      if (!padsar_kar) {
        tax_value = capital_value * tax_rate_record.value;
      }
      let padskar_value = 0;
      if (padsar_kar) {
        padskar_value = padsar_kar * tax_rate_record.value;
      }

      tax_value = parseFloat(tax_value?.toFixed(2));

      // Step 8: Apply depreciation to the tax value
      let tax = tax_value * (1 - depreciation_rate_record?.value);
      tax = parseFloat(tax.toFixed(0));

      // Step 9: Fetch GhanKachara value only if UsageSubType exists
      let ghankacharaTax = 0;
      if (UsageSubType) {
        const usage_type_id: any = usage_type;

        const ghankachrraMaster = await this.Master_Ghankachar.findOne({
          where: {
            UsageSubType: { usage_sub_type_master_id: usageSubType },
            financial_year: currentFinancialYear.financial_year_range || '',
            status: 'Active',
          reassessmentRange: {reassessment_range_id: currentReassessmentRange.reassessment_range_id},
          },
        });

        ghankacharaTax = ghankachrraMaster?.value || 0;
        ghankacharaTax = ghankacharaTax * 12;
      }

      //step 10 calculate vruskh kar, shikhan kar, rojagar hami kar,
      let vruskh_kar = 0;
      let shikhan_kar = 0;
      let rojagar_hami_kar = 0;

      if (
        usage_type_name?.trim().includes('खुली जागा') == true ||
        usage_type_name?.trim() == 'धार्मिक' ||
        property_type_name?.trim() == 'पडसर'
      ) {
        vruskh_kar = 0;
        shikhan_kar = 0;
        rojagar_hami_kar = 0;
      } else if (usage_type_name?.trim() == 'निवासी') {
        vruskh_kar = capital_value * 0.0001;
        shikhan_kar = capital_value * 0.0001;
        rojagar_hami_kar = 0;
      } else {
        vruskh_kar = capital_value * 0.0001;
        shikhan_kar = capital_value * 0.0001;
        rojagar_hami_kar = capital_value * 0.0001;
      }

      // Return all calculated values
      // return {
      //   rr_rate: rr_rate_record.value,
      //   rr_construction_rate: rr_construction_rate_record.value,
      //   depreciation_rate: depreciation_rate_record.value,
      //   weighting: weighting_rate_record.value,
      //   capital_value: capital_value,
      //   tax_value: tax_rate_record.value,
      //   tax: tax_value,
      //   GhanKachar: ghankacharaTax,
      // };
      // Return all calculated values with rounding

      return {
        rr_rate: rr_rate_record.value,
        rr_construction_rate: rr_construction_rate_record.value,
        depreciation_rate: depreciation_rate_record?.value || 0,
        weighting: weighting_rate_record.value,
        capital_value: Math.round(capital_value),
        tax_value: tax_rate_record.value,
        tax:Math.round(tax_value),

        GhanKachar: Math.round(ghankacharaTax),
        vruskh_kar: Math.round(vruskh_kar),
        shikhan_kar: Math.round(shikhan_kar),
        rojagar_hami_kar: Math.round(rojagar_hami_kar),
        padsar_kar: Math.round(padskar_value),
        padsar_kar_tax: Math.round(padskar_value),
        property_age,
      };
    } catch (error) {
      console.error(`Error fetching tax values:`, error);
      throw error;
    }
  }

  async updateTaxData(milkatKartax_id: string) {
    try {
      const milkatKartax =
        await this.milkatKarTaxeRepository.getDataInfoByTaxId(milkatKartax_id);
      const propertyUsage_id =
        milkatKartax.property_usage_details.property_usage_details_id;
      const floor_name =
        await this.propertyUsageDetailsRepository.getFloorTypeName(
          propertyUsage_id,
        );
      let taxData = JSON.parse(milkatKartax.tax_data as any);
      console.log('tax dataaa---', taxData);
      if (floor_name && floor_name != 'तळ मजला') {
        let taxData = JSON.parse(milkatKartax.tax_data as any);
        taxData.capital_value_formula = `=(${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}`;
        taxData.emarat_tax_formula = `=((${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}) * ${taxData.taxCalculationDetails.taxValue})`;
        taxData.emarat_tax_formula_fields = `=((are_sq_meter * rr_construction_rate * depreciation_rate * weighting ) * tax_rate)`;

        milkatKartax.tax_data = JSON.stringify(taxData) as unknown as JSON;

        await this.milkatKarTaxeRepository.save(milkatKartax);
        return {
          message: 'Updated Tax Data',
          data: milkatKartax,
        };
      }
      return {
        message: 'No updates to Tax Data needed',
      };
    } catch (error) {
      throw error;
    }
  }

  async updateAllTaxData() {
    try {
      const milkatKartaxes = await this.milkatKarTaxeRepository.getAll();
      let updatedtax_Ids: string[] = [];

      const updatePromises = milkatKartaxes.map(async (milkatKartax) => {
        const propertyUsage_id =
          milkatKartax.property_usage_details.property_usage_details_id;
        const floor_name =
          await this.propertyUsageDetailsRepository.getFloorTypeName(
            propertyUsage_id,
          );

        if (floor_name && floor_name !== 'तळ मजला') {
          let taxData = JSON.parse(milkatKartax.tax_data as any);
          taxData.capital_value_formula = `=(${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}`;
          taxData.emarat_tax_formula = `=((${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}) * ${taxData.taxCalculationDetails.taxValue})`;
          taxData.emarat_tax_formula_fields = `=((are_sq_meter * rr_construction_rate * depreciation_rate * weighting ) * tax_rate)`;

          milkatKartax.tax_data = JSON.stringify(taxData) as unknown as JSON;

          await this.milkatKarTaxeRepository.save(milkatKartax);
          updatedtax_Ids.push(milkatKartax.milkatKartax_id);
        } else {
          let taxData = JSON.parse(milkatKartax.tax_data as any);
          taxData.capital_value_formula = `=(${taxData.areaSqMeter}*${taxData.taxCalculationDetails.rrRate} )+(${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}`;
          taxData.emarat_tax_formula = `=((${taxData.areaSqMeter}*${taxData.rr_rate} )+(${taxData.areaSqMeter} * ${taxData.taxCalculationDetails.rrConstructionRate} * ${taxData.taxCalculationDetails.depreciationRate}) * ${taxData.taxCalculationDetails.weighting}) * ${taxData.taxCalculationDetails.taxValue})`;
          taxData.emarat_tax_formula_fields = `'=((( are_sq_meter * rr_rate) + ( are_sq_meter * rr_construction_rate * depreciation_rate)) * weighting ) * tax_rate ',
  `;
          milkatKartax.tax_data = JSON.stringify(taxData) as unknown as JSON;

          await this.milkatKarTaxeRepository.save(milkatKartax);
          updatedtax_Ids.push(milkatKartax.milkatKartax_id);
        }
      });

      await Promise.all(updatePromises);

      if (updatedtax_Ids.length === 0) {
        return { message: 'No updates needed' };
      }
      return { message: 'Updated all necessary Tax Data' };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get count of properties that need Milkat Kar generation for a specific ward
   * @param wardNumber The ward number/name
   * @param reassessmentRangeId The reassessment range ID
   * @returns Count of remaining properties
   */
  async getMilkatKarRemainingCount(
    wardNumber: string,
    reassessmentRangeId: string,
  ) {
    try {
      // Get all properties in the ward
      const allProperties = await this.propertyMasterRepository.find({
        where: { ward: { ward_name: wardNumber } },
        relations: ['ward'],
      });

      if (!allProperties || allProperties.length === 0) {
        return {
          statusCode: 200,
          message: `No properties found for ward ${wardNumber}`,
          data: {
            totalProperties: 0,
            remainingProperties: 0,
            completedProperties: 0,
          },
        };
      }

      // Get properties that already have Milkat Kar for this reassessment range
      const propertiesWithMilkatKar = await this.milkatKareRepository.find({
        where: {
          property: { ward: { ward_name: wardNumber } },
          reassessmentRange: { reassessment_range_id: reassessmentRangeId },
          status: 'active',
        },
        relations: ['property', 'property.ward', 'reassessmentRange'],
      });

      const completedCount = propertiesWithMilkatKar.length;
      const remainingCount = allProperties.length - completedCount;

      return {
        statusCode: 200,
        message: 'Ward statistics retrieved successfully',
        data: {
          wardName: wardNumber,
          totalProperties: allProperties.length,
          remainingProperties: remainingCount,
          completedProperties: completedCount,
        },
      };
    } catch (error) {
      console.error(
        `Error getting Milkat Kar remaining count for ward ${wardNumber}:`,
        error,
      );
      throw error;
    }
  }

  async getWardWiseMilkatKarStatus(reassessmentYearId: string) {
    try {
      // Check if the reassessment range exists
      const reassessmentRange =
        await this.reAssesmentRepo.findById(reassessmentYearId);
      if (!reassessmentRange) {
        throw new NotFoundException('Reassessment range not found');
      }

      // Get all wards
      const wards = await this.wardMasterRepository.findAllWard();

      // Get ward-wise status
      const wardWiseStatus = await Promise.all(
        wards.map(async (ward) => {
          // Get total properties in this ward
          const totalProperties = await this.propertyMasterRepository.count({
            where: { ward: { ward_id: ward.ward_id } },
          });

          // Get generated Milkat Kar records for this ward and reassessment range
          const generatedProperties = await this.milkatKareRepository.count({
            where: {
              property: { ward: { ward_id: ward.ward_id } },
              reassessmentRange: { reassessment_range_id: reassessmentYearId },
              status: 'active',
            },
          });

          const remainingProperties = totalProperties - generatedProperties;

          return {
            ward_id: ward.ward_id,
            ward_name: ward.ward_name,
            total_properties: totalProperties,
            generated_count: generatedProperties,
            remaining_count: remainingProperties,
            progress_percentage:
              totalProperties > 0
                ? Math.round((generatedProperties / totalProperties) * 100)
                : 0,
          };
        }),
      );

      return {
        statusCode: 200,
        message: 'Ward-wise Milkat Kar status retrieved successfully',
        data: wardWiseStatus,
      };
    } catch (error) {
      console.error('Error retrieving ward-wise Milkat Kar status:', error);
      throw error;
    }
  }

  async processMilakatKarAkarniByWardAndReassessment(
    wardNumber: string,
    reassessmentYearId: string,
  ) {
    try {
      // Step 1: Check if the reassessment range exists
      const reassessmentRange =
        await this.reAssesmentRepo.findById(reassessmentYearId);
      if (!reassessmentRange) {
        throw new NotFoundException('Reassessment range not found');
      }

      // Step 2: Fetch all properties for the given ward
      const properties = await this.propertyMasterRepository.find({
        where: { ward: { ward_name: wardNumber } },
        order: { sr_no: 'ASC' },
      });

      if (properties.length === 0) {
        return {
          statusCode: 200,
          message: `No properties found for ward: ${wardNumber}`,
          processedCount: 0,
          totalCount: 0,
        };
      }

      // Step 3: Loop through each property and call the individual tax calculation function
      let processedCount = 0;
      for (const property of properties) {
        try {
          await this.calculateTaxForProperty(property.property_id);
          processedCount++;
        } catch (error) {
          console.error(
            `Error processing property ${property.property_id}:`,
            error,
          );
          // Continue with the next property even if one fails
        }
      }

      return {
        statusCode: 200,
        message: `Milkat Kar Akarni Process Completed for Ward ${wardNumber}: ${processedCount} out of ${properties.length} properties processed`,
        processedCount,
        totalCount: properties.length,
        wardNumber,
        reassessmentYearId,
      };
    } catch (error) {
      console.error(
        'Error processing Milkat Kar Akarni by ward and reassessment:',
        error,
      );
      throw error;
    }
  }
}
