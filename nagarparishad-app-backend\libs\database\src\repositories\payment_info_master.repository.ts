import { Repository } from 'typeorm';
import { PaymentInfoEntity } from '../entities'; // Assuming the entity is in the `entities` folder
import { InjectRepository } from '@nestjs/typeorm';

export class PaymentInfoRepository extends Repository<PaymentInfoEntity> {
  constructor(
    @InjectRepository(PaymentInfoEntity)
    private readonly paymentInfoRepository: Repository<PaymentInfoEntity>,
  ) {
    super(
      paymentInfoRepository.target,
      paymentInfoRepository.manager,
      paymentInfoRepository.queryRunner,
    );
  }

  // Save a new payment info
  async savePaymentInfo(input: {
    // Add fields specific to PaymentInfoEntity
    payment_date: Date;
    status: string;
  }): Promise<PaymentInfoEntity> {
    const paymentInfo = this.paymentInfoRepository.create(input);
    return await this.paymentInfoRepository.save(paymentInfo);
  }

  // Find all payment info
  async findAllPaymentInfo() {
    return await this.paymentInfoRepository
      .createQueryBuilder('payment_info')
      .orderBy('payment_info.payment_date', 'DESC')
      .getMany();
  }

  // Find payment info by ID
  async findPaymentInfoById(id: string) {
    return await this.paymentInfoRepository
      .createQueryBuilder('payment_info')
      .where('payment_info.payment_info_id = :id', { id })
      .getOne();
  }

  // Update payment info by ID
  async updatePaymentInfo(
    id: string,
    input: {
      payment_date?: Date;
      status?: string;
    },
  ) {
    return await this.paymentInfoRepository
      .createQueryBuilder('payment_info')
      .update(PaymentInfoEntity)
      .set(input)
      .where('payment_info_id = :id', { id })
      .execute();
  }

  // Delete (soft delete) a payment info by ID
  async deletePaymentInfo(id: string) {
    return await this.paymentInfoRepository
      .createQueryBuilder('payment_info')
      .softDelete()
      .where('payment_info_id = :id', { id })
      .execute();
  }
}
