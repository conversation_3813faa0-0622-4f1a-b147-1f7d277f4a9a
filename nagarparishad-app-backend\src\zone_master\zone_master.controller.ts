import {
  Body,
  Controller,
  Delete,
  Get,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ZoneMasterService } from './zone_master.service';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CreateZoneMasterDto } from './dto/create-zone-master.dto';
import { GetWardMastereDto, ZoneMasterDto } from './dto/zone-master.dto';
import { UpdateZoneMasterDto } from './dto/update-zone-master.dto';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';
import { Public } from '@jwt/jwt-auth/decorators/public.decorator';

@ApiTags('Zone Master')
@Controller('zone-master')
export class ZoneMasterController {
  constructor(private readonly zoneMasterService: ZoneMasterService) {}

  @Form('Zone')
  @Permissions('can_write')
  @ApiOperation({ summary: 'Create a new zone' })
  @ApiResponse({
    status: 201,
    description: 'The zone has been successfully created',
  })
  @Post('create')
  create(@Body() createZoneMasterDto: CreateZoneMasterDto) {
    return this.zoneMasterService.create(createZoneMasterDto);
  }

  @Form('Zone')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all zones' })
  @ApiResponse({ status: 200, description: 'Returns all zones' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.zoneMasterService.findAllZone();
  }

  @Form('Zone')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one zone' })
  @ApiResponse({ status: 200, description: 'Returns Single Zone' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')
  findOne(@Query() zoneMasterDto: ZoneMasterDto) {
    return this.zoneMasterService.findOne(zoneMasterDto);
  }

  @Form('Zone')
  @Permissions('can_update')
  @ApiOperation({ summary: 'Update a zone by ID' })
  @ApiResponse({
    status: 200,
    description: 'The zone has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Zone not found' })
  @Patch('update')
  update(
    @Query() zoneMasterDto: ZoneMasterDto,
    @Body() updateZoneMasterDto: UpdateZoneMasterDto,
  ) {
    return this.zoneMasterService.update(zoneMasterDto, updateZoneMasterDto);
  }

  @Form('Zone')
  @Permissions('can_delete')
  @ApiOperation({ summary: 'Delete a zone by ID' })
  @ApiResponse({
    status: 200,
    description: 'The zone has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'zone not found' })
  @Delete('delete')
  remove(@Query() zoneMasterDto: ZoneMasterDto) {
    return this.zoneMasterService.remove(zoneMasterDto);
  }

  @ApiOperation({ summary: 'Get all zones by ward' })
  @ApiResponse({ status: 200, description: 'Returns all zones by ward' })
  @ApiResponse({ status: 404, description: 'Ward Not found' })
  @Get('byWard')
  getByWard(@Query() getWardMastereDto: GetWardMastereDto) {
    return this.zoneMasterService.getByWard(getWardMastereDto);
  }

  @Public()
  @ApiOperation({ summary: 'Get all zones (Public API for home page)' })
  @ApiResponse({ status: 200, description: 'Returns all zones without authentication' })
  @Get('public')
  findAllPublic() {
    return this.zoneMasterService.findAllZone();
  }
}
