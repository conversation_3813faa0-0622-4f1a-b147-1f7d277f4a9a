import {
  BaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PaymentInfoEntity } from './payment_info.entity';
import { PropertyEntity } from './property.entity';
import { BookNumberMasterEntity } from './book_numberMaster.entity';
import { DemandReportData } from './demandeReportData.entity';

@Entity('receipt')
export class ReceiptEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  receipt_id: string;

  @ManyToOne(() => PropertyEntity, (property) => property.receipt, {
    cascade: true,
    onDelete: 'CASCADE', // This will delete related payment_info records when a property is deleted
  })
  @JoinColumn({ name: 'property_id' })
  property: PropertyEntity;

  @OneToOne(() => PaymentInfoEntity, (paymentInfo) => paymentInfo.receipts, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'payment_id' })
  paymentInfo: PaymentInfoEntity;

  @OneToOne(() => DemandReportData, (paymentInfo) => paymentInfo.ReciptInfo, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  DemandReportData: DemandReportData;

  @Column({ type: 'varchar' })
  book_number: string;

  @Column({ type: 'varchar' })
  book_receipt_number: string;

  @Column({ type: String, nullable: false })
  financial_year: string;

  @Column({ type: 'timestamp', nullable: false })
  receipt_date: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  remark: string; // Optional field for any additional notes

  @Column({ type: 'varchar', length: 255, nullable: true })
  additional_notes: string; // Optional field for any additional notes

  @ManyToOne(() => BookNumberMasterEntity, (book) => book.receipts)
  @JoinColumn({ name: 'book_number_id' })
  bookNumber: BookNumberMasterEntity; /*
   * Create and Update Date Columns
   */
  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}
