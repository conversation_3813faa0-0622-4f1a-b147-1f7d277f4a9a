import { Repository } from 'typeorm';
import { Master_tax_rateEntity, ReassessmentRange } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { PropertyTypeMasterRepository } from './property-typeMaster.repository';


export class Master_TaxValueRepository extends Repository<Master_tax_rateEntity> {
  constructor(
    @InjectRepository(Master_tax_rateEntity)
    private readonly master_tax_rateEntity: Repository<Master_tax_rateEntity>,
    private readonly PropertyTypeMasterRepository: PropertyTypeMasterRepository,

  ) {
    super(
      master_tax_rateEntity.target,
      master_tax_rateEntity.manager,
      master_tax_rateEntity.queryRunner,
    );
  }
  async getWithPropertyType(): Promise<Master_tax_rateEntity[]> {
    return this.master_tax_rateEntity.find({
      relations: ['property_type','reassessmentRange'],  // Include property class relation
    });
}
async updateTaxValue(
  id: string,
  data: any,
): Promise<{ message: string; data: Master_tax_rateEntity }> {
  // Check if the record exists
  const existingRecord = await this.master_tax_rateEntity.findOne({
    where: { rr_tax_id: id }, // Ensure to match the correct ID field
    relations: ['property_type', 'reassessmentRange'], // Ensure the relation is fetched
  });

  if (!existingRecord) {
    return {
      message: 'No record found to update',
      data: undefined,
    };
  }

  // Check if the zone_id is provided in the data
  if (data.propertyType_id) {
    const property_type = await this.PropertyTypeMasterRepository.findById(data.propertyType_id); // Adjust to your zone entity structure
    if (property_type) {
      existingRecord.property_type = property_type; // Assign the fetched zone to the existing record
    } else {
      return {
        message: 'property_type not found',
        data: undefined,
      };
    }
  }

  // Handle reassessment range if provided
  if (data.reassessment_range_id) {
    const reassessmentRange = await this.master_tax_rateEntity.manager
      .getRepository(ReassessmentRange)
      .findOne({
        where: { reassessment_range_id: data.reassessment_range_id }
      });
    if (reassessmentRange) {
      existingRecord.reassessmentRange = reassessmentRange;
    }
  }

  // Update the properties of the existing record
  Object.assign(existingRecord, data);

  // Save the updated record back to the database
  await this.master_tax_rateEntity.save(existingRecord);

  return {
    message: 'Tax value updated successfully',
    data: existingRecord,
  };
}

async generateForNewFinancialYear(newFinancialYear: string, currentReassessmentRange: ReassessmentRange): Promise<{ message: string; data?: Master_tax_rateEntity[] }> {
  // Fetch all existing records
  const existingRecords = await this.getWithPropertyType();

  if (existingRecords.length === 0) {
    return {
      message: 'No records found to duplicate',
    };
  }

  const duplicatedRecords: Master_tax_rateEntity[] = [];

  for (const record of existingRecords) {
    // Create a new record based on the existing one
    const newRecord = new Master_tax_rateEntity();
    newRecord.financial_year = newFinancialYear; // Keep for backward compatibility
    newRecord.value = record.value;
    newRecord.status = record.status;
    newRecord.property_type = record.property_type;
    newRecord.reassessmentRange = currentReassessmentRange;

    // Save the new record to the database
    const savedRecord = await this.master_tax_rateEntity.save(newRecord);
    duplicatedRecords.push(savedRecord);
  }

  return {
    message: 'Records generated successfully for the new reassessment range',
    data: duplicatedRecords,
  };
}



}
