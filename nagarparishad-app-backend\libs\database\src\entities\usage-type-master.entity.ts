import {
  <PERSON><PERSON><PERSON>ty,
  Column,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UsageSubTypeMasterEntity } from './usage-sub-type-master.entity';
import { Master_ghanKachra_rateEntity } from './master_ghanKachra_rate.entity';

@Entity('usage_type_master')
export class UsageTypeMasterEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  usage_type_id: string;

  @Column({ name: 'usage_type', type: String, nullable: false })
  usage_type: string;

  /*
   * Create and Update Date Columns
   */
  @OneToMany(() => UsageSubTypeMasterEntity, (usageSubType) => usageSubType.usageType)
  usageSubTypes: UsageSubTypeMasterEntity[];


  
  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  public createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  public updatedAt!: Date;

  @DeleteDateColumn({ type: 'timestamp', name: 'deleted_at' })
  public deletedAt!: Date;
}


