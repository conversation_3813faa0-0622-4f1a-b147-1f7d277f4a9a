import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import { CollectorMasterRepository, FormMasterRepository, ModuleMasterRepository, RoleMasterRepository, RolewiseFormPermissionRepository } from 'libs/database/repositories';
import {
  CreateRoleMasterDto,
  RoleIdMasterDto,
  UpdateRoleMasterDto,
} from './dto/role-master.dto';
import { CollectorMaster } from 'libs/database/entities';

@Injectable()
export class RoleMasterService {
  constructor(private readonly roleMasterRepository: RoleMasterRepository,
    private readonly collectorRepo: CollectorMasterRepository,
        private readonly moduleMasterRepository: ModuleMasterRepository,
     private readonly formRepository: FormMasterRepository,
    private permissionRepository: RolewiseFormPermissionRepository
  ) {}
  async create(createRoleMasterDto: CreateRoleMasterDto) {
    try {
      // Save the new role
      const saveData = await this.roleMasterRepository.saveData(createRoleMasterDto);
  
      // If the role is 'Collector', create a collector entry
      let collector: CollectorMaster | undefined;
      if (createRoleMasterDto.roleName === 'Collector') {
        collector = await this.collectorRepo.createCollectorForRole({ isActive: true, role_id: saveData.role_id });
      }
  
      // Fetch all modules and their forms
      const modules = await this.moduleMasterRepository.find();

      // Iterate over each module and form to create permissions for the new role
      for (const module of modules) {
        const forms = await this.formRepository.find({ where: { module: { module_id: module.module_id } } });
        for (const form of forms) {
          // Create a permission entry with all permissions set to false
          const permission = this.permissionRepository.create({
            role: saveData,
            form,
            can_read: false,
            can_write: false,
            can_update: false,
            can_delete: false,
            is_valid: true,
          });
  
          // Save the permission entry
          await this.permissionRepository.save(permission);
        }
      }
  
      return {
        message: 'Record Saved Successfully',
        data: {
          saveData,
          collector,
        },
      };
    } catch (error) {
      throw error;
    }
  }
  

  async findAll() {
    try {
      const getAllData = await this.roleMasterRepository.findAllData();

      if (!getAllData) {
        throw new NotFoundException('Record Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(roleIdMasterDto: RoleIdMasterDto) {
    try {
      const { role_id } = roleIdMasterDto;
      const checkData = await this.roleMasterRepository.findById(role_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      return {
        message: 'Data found Sucess',
        data: checkData,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    roleIdMasterDto: RoleIdMasterDto,
    updateRoleMasterDto: UpdateRoleMasterDto,
  ) {
    try {
      const { role_id } = roleIdMasterDto;
      const checkData = await this.roleMasterRepository.findById(role_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }
      const updateData = await this.roleMasterRepository.updateData(
        role_id,
        updateRoleMasterDto,
      );

      if (updateData.affected === 0) {
        throw new NotAcceptableException('Failed to update Data');
      }

      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(roleIdMasterDto: RoleIdMasterDto) {
    try {
      const { role_id } = roleIdMasterDto;
      const checkData = await this.roleMasterRepository.findById(role_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      const deleteData = await this.roleMasterRepository.deleteData(role_id);

      if (deleteData.affected === 0) {
        throw new NotAcceptableException('Failed to delete data');
      }

      return {
        message: 'data Delete Successfully',
        data: deleteData,
      };
    } catch (error) {
      throw error;
    }
  }
}
