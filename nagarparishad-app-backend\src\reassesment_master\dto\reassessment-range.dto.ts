import { IsString, IsBoolean, IsOptional } from 'class-validator';

export class CreateReassessmentRangeDto {
  @IsString()
  start_range: string;

  @IsString()
  end_range: string;

  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @IsBoolean()
  @IsOptional()
  is_current?: boolean;

  @IsBoolean()
  @IsOptional()
  is_published?: boolean;
}

export class UpdateReassessmentRangeDto {
  @IsString()
  @IsOptional()
  start_range?: string;

  @IsString()
  @IsOptional()
  end_range?: string;

  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @IsBoolean()
  @IsOptional()
  is_current?: boolean;

  @IsBoolean()
  @IsOptional()
  is_published?: boolean;
}

export class ReassessmentRangeIdDto {
  @IsString()
  reassessmentRange_id: string;
}
  