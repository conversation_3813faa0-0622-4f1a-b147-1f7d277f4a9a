import PropertyApi from "../../services/PropertyServices";
import { PropertyRegistrationInterface } from "../../model/propertyregistration-master";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";


const fetchProperty = async ({ pageParam = { limit: 40, page: 1 } }) => {
  const skip = (pageParam.page - 1) * pageParam.limit;
  console.log("Fetching properties with skip:", skip, "and limit:", pageParam.limit);
  const response = await PropertyApi.getAllProperty(pageParam.limit, pageParam.page);
  return response;
};

const registerProperty = async (propertyData: PropertyRegistrationInterface) => {
  return new Promise((resolve, reject) => {
    PropertyApi.AddPropertyRegistration(propertyData, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const fetchSingleProperty = async (propertyId: string,search:'property_id' | 'propertyNumber' | 'old_propertyNumber') => {
  return new Promise((resolve, reject) => {
    PropertyApi.getSingleProperty(propertyId,(response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    },search);
  });
};

const updateproperty = async ({
  propertyId,
  propertyData,
}: {
  propertyId: any;
  propertyData: any;
}) => {
  return new Promise((resolve, reject) => {
    PropertyApi.UpdatePropertyRegistration(
      propertyId,
      propertyData,
      (response) => {
        if (response.status) {
          resolve(response.data);
        } else {
          reject(response.data);
        }
      }
    );
  });
};

const getPropertyDetails = async ({ propertyId }: { propertyId: any }) => {
  return new Promise((resolve, reject) => {
    PropertyApi.getSingleProperty(propertyId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

const deleteSingleProperty = async (propertyId: string) => {
  return new Promise((resolve, reject) => {
    PropertyApi.deleteSingleProperty(propertyId, (response) => {
      if (response.status) {
        resolve(response.data);
      } else {
        reject(response.data);
      }
    });
  });
};

export const usePropertyRegistrationController = () => {
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [searchValue, setSearchValue] = useState<string>(""); // New state for search value
  const [searchOn, setSearchOn] = useState<string>("property_number"); // Default search field

  const {
    data: paginatedPropertyData,
    isLoading: propertyLoading,
  } = useQuery({
    queryKey: ["propertyRegistration", page, limit, searchValue, searchOn], 
    queryFn: () =>
      PropertyApi.getAllProperty(limit, page, searchValue, searchOn), 
    staleTime: 1000 * 60 * 2, // 2 minutes
    onSuccess: (data) => {
      setPage(Number(data.page));
      setLimit(Number(data.limit));
    },
  });

  const propertyList = paginatedPropertyData?.data || [];
  const totalRecords = paginatedPropertyData?.total || 0;
  const totalPages = paginatedPropertyData?.totalPages || 0;

  const createPropertyMutation = useMutation({
    mutationFn: registerProperty,
    onMutate: async (newProperty) => {
      await queryClient.cancelQueries({ queryKey: ["propertyRegistration"] });
      const previousWards = queryClient.getQueryData(["propertyRegistration"]);
      queryClient.setQueryData(["propertyRegistration"], (old: any) => {
        const updatedData = [newProperty, ...old];
        // Log the updated data
        return updatedData;
      });

      return { previousWards };
    },
    onError: (err, newWard, context) => {
      queryClient.setQueryData(["propertyRegistration"], context.previousWards);
      console.error("Error creating ward:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["propertyRegistration"] });
    },
  });

  // Mutation for updating a zone
  const updatePropertyMutation = useMutation({
    mutationFn: updateproperty,
    onMutate: async ({ propertyId, propertyData }) => {
      await queryClient.cancelQueries({ queryKey: ["propertyRegistration"] });
      const previousProperty = queryClient.getQueryData([
        "propertyRegistration",
      ]);

      queryClient.setQueryData(["propertyRegistration"], (old) => {
        const updatedZones = old?.map((property) =>
          property.proper === propertyId
            ? { ...property, ...propertyData }
            : property
        );

        return updatedZones;
      });

      return { previousProperty };
    },
    onError: (err, { propertyId, propertyData }, context) => {
      queryClient.setQueryData(
        ["propertyRegistration"],
        context.previousProperty
      );
      console.error("Error updating zone:", err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["propertyRegistration"] });
    },
  });
  const deletePropertyMutation = useMutation({
    mutationFn: deleteSingleProperty,
    onMutate: async (propertyId) => {
      await queryClient.cancelQueries({ queryKey: ["propertyRegistration", page, limit, searchValue, searchOn] });
  
      const previousProperty = queryClient.getQueryData([
        "propertyRegistration", page, limit, searchValue, searchOn
      ]);
      console.log("previousPropertypreviousProperty",previousProperty)
  
      queryClient.setQueryData(["propertyRegistration", page, limit, searchValue, searchOn], (old = []) => {
        const updatedWards = old.data.filter(
          (property) => property.property_master_id !== propertyId
        );
        return updatedWards;
      });
  
      return { previousProperty };
    },
    onError: (err, propertyId, context) => {
      queryClient.setQueryData(
        ["propertyRegistration", page, limit, searchValue, searchOn],
        context.previousProperty
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["propertyRegistration", page, limit, searchValue, searchOn] });
    },
  });
  
  const usePropertyDetails = (propertyId: string,search:'property_id' | 'propertyNumber' | 'old_propertyNumber') => {
    return useQuery({
      queryKey: ["propertyDetails", propertyId],
      queryFn: () => fetchSingleProperty(propertyId,search),
      staleTime: 10 * 60 * 2, // 2 minutes
      refetchOnWindowFocus: true, // Refetch on window focus
    });
  };

  return {
    propertyList,
    propertyLoading,
    createProperty: createPropertyMutation.mutate,
    updateProperty: updatePropertyMutation.mutate,
    deleteProperty: deletePropertyMutation.mutate,
    usePropertyDetails,

    pagination: {
      page,
      setPage,
      limit,
      setLimit,
      totalPages,
      totalRecords,
    },
    searchValue,
    setSearchValue, // Expose search value setter
    searchOn,
    setSearchOn, // Expose search field setter
  };
};