import { MigrationInterface, QueryRunner } from "typeorm";

export class CreatedTableRelatedToPayments1739961971062 implements MigrationInterface {
    name = 'CreatedTableRelatedToPayments1739961971062'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "receipt" ("receipt_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "book_number" character varying NOT NULL, "financial_year" character varying NOT NULL, "receipt_date" TIMESTAMP NOT NULL, "additional_notes" character varying(255), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "property_id" uuid, "payment_id" uuid, CONSTRAINT "REL_1f5f66e68bf189af1466e3756a" UNIQUE ("payment_id"), CONSTRAINT "PK_aa7e2fd229261106074134676c4" PRIMARY KEY ("receipt_id"))`);
        await queryRunner.query(`CREATE TABLE "paid_data" ("paid_data_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "all_property_tax_sum" double precision DEFAULT '0', "total_amount" double precision DEFAULT '0', "tax_type_1" double precision DEFAULT '0', "tax_type_2" double precision DEFAULT '0', "tax_type_3" double precision DEFAULT '0', "tax_type_4" double precision DEFAULT '0', "tax_type_5" double precision DEFAULT '0', "tax_type_6" double precision DEFAULT '0', "tax_type_7" double precision DEFAULT '0', "tax_type_8" double precision DEFAULT '0', "tax_type_9" double precision DEFAULT '0', "tax_type_10" double precision DEFAULT '0', "other_tax_sum_tax" double precision DEFAULT '0', "status" character varying, "financial_year" character varying NOT NULL, "property_id" character varying, "property_number" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "payment_info_id" uuid, CONSTRAINT "REL_0a6d25ea3d453c65efb565599b" UNIQUE ("payment_info_id"), CONSTRAINT "PK_515f89e698ab7d1e7cb3a34f2d9" PRIMARY KEY ("paid_data_id"))`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c" UNIQUE ("billNo")`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" DROP DEFAULT`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(50) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" DROP NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_90614cac26fb49598421ebfed20" FOREIGN KEY ("propertyType_id") REFERENCES "property_type_master"("propertyType_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD CONSTRAINT "FK_df738d1108e8291026728e28c55" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "receipt" ADD CONSTRAINT "FK_daf897aedb589b656c2d3cb8f61" FOREIGN KEY ("property_id") REFERENCES "property"("property_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "receipt" ADD CONSTRAINT "FK_1f5f66e68bf189af1466e3756a3" FOREIGN KEY ("payment_id") REFERENCES "payment_info"("payment_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "paid_data" ADD CONSTRAINT "FK_0a6d25ea3d453c65efb565599ba" FOREIGN KEY ("payment_info_id") REFERENCES "payment_info"("payment_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // await queryRunner.query(`ALTER TABLE "master_tax_rate" ADD CONSTRAINT "FK_ba03f0f1cab042ed390aa18efda" FOREIGN KEY ("propertyType_id") REFERENCES "property_type_master"("propertyType_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE "master_tax_rate" DROP CONSTRAINT "FK_ba03f0f1cab042ed390aa18efda"`);
        await queryRunner.query(`ALTER TABLE "paid_data" DROP CONSTRAINT "FK_0a6d25ea3d453c65efb565599ba"`);
        await queryRunner.query(`ALTER TABLE "receipt" DROP CONSTRAINT "FK_1f5f66e68bf189af1466e3756a3"`);
        await queryRunner.query(`ALTER TABLE "receipt" DROP CONSTRAINT "FK_daf897aedb589b656c2d3cb8f61"`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "FK_df738d1108e8291026728e28c55"`);
        // await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_90614cac26fb49598421ebfed20"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "property_id" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP COLUMN "property_number"`);
        // await queryRunner.query(`ALTER TABLE "billdata" ADD "property_number" character varying(100) NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET DEFAULT now()`);
        // await queryRunner.query(`ALTER TABLE "billdata" ALTER COLUMN "bill_generation_date" SET NOT NULL`);
        // await queryRunner.query(`ALTER TABLE "billdata" DROP CONSTRAINT "UQ_8acdc60ca94d77832f81a66989c"`);
        await queryRunner.query(`DROP TABLE "paid_data"`);
        await queryRunner.query(`DROP TABLE "receipt"`);
    }

}
