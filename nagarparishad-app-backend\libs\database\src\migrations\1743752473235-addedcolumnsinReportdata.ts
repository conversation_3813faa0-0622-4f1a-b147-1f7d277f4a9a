import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedcolumnsinReportdata1743752473235 implements MigrationInterface {
    name = 'AddedcolumnsinReportdata1743752473235'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "total_amount"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "total_amount_paid" double precision DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "total_amount_remaining" double precision DEFAULT '0'`);
 }

    public async down(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "total_amount_remaining"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" DROP COLUMN "total_amount_paid"`);
        await queryRunner.query(`ALTER TABLE "demand_report_data" ADD "total_amount" double precision DEFAULT '0'`);
    }

}
