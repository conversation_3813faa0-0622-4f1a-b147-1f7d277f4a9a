import {
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import { CreateWardMasterDto } from './dto/create-ward_master.dto';
import { UpdateWardMasterDto } from './dto/update-ward_master.dto';
import { WardMasterRepository } from 'libs/database/repositories';
import { WardIdDto } from './dto/ward-id.dto';

@Injectable()
export class WardMasterService {
  constructor(private readonly wardMasterRepository: WardMasterRepository) {}

  async create(createWardMasterDto: CreateWardMasterDto) {
    try {
      const SaveWard =
        await this.wardMasterRepository.saveWard(createWardMasterDto);

      return {
        message: 'Ward Saved SuccessFully',
        data: SaveWard,
      };
    } catch (error) {
      throw error;
    }
  }

  async findAllWard() {
    try {
      const getAllWard = await this.wardMasterRepository.findAllWard();

      if (!getAllWard) {
        throw new NotFoundException('Ward Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllWard,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(wardIdDto: WardIdDto) {
    try {
      const { ward_id } = wardIdDto;
      const checkWard = await this.wardMasterRepository.findWardById(ward_id);

      // Logger.log(JSON.stringify(checkWard));
      if (!checkWard) {
        throw new NotFoundException('Ward Not Found');
      }

      return {
        message: 'Ward Found Success',
        data: checkWard,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(wardIdDto: WardIdDto, updateWardMasterDto: UpdateWardMasterDto) {
    try {
      const { ward_id } = wardIdDto;

      const checkWard = await this.wardMasterRepository.findWardById(ward_id);

      if (!checkWard) {
        throw new NotFoundException('Ward Not Found');
      }

      const updateWard = await this.wardMasterRepository.updateWard(
        ward_id,
        updateWardMasterDto,
      );

      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(wardIdDto: WardIdDto) {
    try {
      const { ward_id } = wardIdDto;

      const checkWard = await this.wardMasterRepository.findWardById(ward_id);

      if (!checkWard) {
        throw new NotFoundException('Ward Not Found');
      }

      const deleteWard = await this.wardMasterRepository.deleteWard(ward_id);

      if (deleteWard.affected === 0) {
        throw new NotAcceptableException('Failed To Delete ward');
      }

      return {
        message: 'Ward Deleted SuccessFully',
        data: deleteWard,
      };
    } catch (error) {
      throw error;
    }
  }
}
