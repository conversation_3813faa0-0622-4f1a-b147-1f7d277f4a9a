import { IsEnum, IsNotEmpty, Is<PERSON>umber, IsString, Min, ValidateIf } from "class-validator";


export class CSVDTO{   
    
    survey_person_code: string
    
    
    survey_date: string
    
   
    latitude: string
    
    
    longitude: string
    
    
    old_propertyNumber: string
    
    
    propertyNumber: string
    
   
    snp_ward: string
    
    
    zone_code: string
    
    
    house_or_apartment_name: string    
   
}