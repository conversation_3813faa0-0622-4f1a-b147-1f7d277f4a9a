import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { DeletedPropertyUsageEntity, Property_Usage_Details_Entity } from '../entities';
import { Injectable } from '@nestjs/common';

@Injectable()
export class DeletedPropertyUsageRepository extends Repository<DeletedPropertyUsageEntity> {
  constructor(
    @InjectRepository(DeletedPropertyUsageEntity)
    private readonly deletedPropertyUsageRepo: Repository<DeletedPropertyUsageEntity>,
  ) {
    super(
      deletedPropertyUsageRepo.target,
      deletedPropertyUsageRepo.manager,
      deletedPropertyUsageRepo.queryRunner,
    );
  }
  async deleteAll(): Promise<void> {
    await this.deletedPropertyUsageRepo.clear(); 
  }  

  async findAll(): Promise<DeletedPropertyUsageEntity[]> {
    return await this.deletedPropertyUsageRepo.find({
      relations: ['property', 'property_usage'], 
    });
  }
  
  async findOneById(id: string): Promise<DeletedPropertyUsageEntity | null> {
    return await this.deletedPropertyUsageRepo
      .createQueryBuilder('deleted_property_usage_details')
      .leftJoinAndSelect('deleted_property_usage_details.property', 'property')
      .leftJoinAndSelect('deleted_property_usage_details.property_usage', 'property_usage')
      .where('property_usage.property_usage_details_id = :id', { id }) 
      .getOne();
  } 
  
}
