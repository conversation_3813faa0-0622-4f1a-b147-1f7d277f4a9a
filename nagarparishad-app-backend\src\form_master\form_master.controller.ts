import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { FormMasterService } from './form_master.service';
import {
  CreateFormMasterDto,
  FormMasterIdDto,
  UpdateFormMasterDto,
} from './dto/form-master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Form Master')
@Controller('form-master')
export class FormMasterController {
  constructor(private readonly formMasterService: FormMasterService) {}

  @ApiOperation({ summary: 'Create a new Form Master' })
  @ApiResponse({
    status: 201,
    description: 'The Form Master has been successfully created',
  })
  @Post()
  create(@Body() createFormMasterDto: CreateFormMasterDto) {
    return this.formMasterService.create(createFormMasterDto);
  }

  @ApiOperation({ summary: 'Get all Form Masters By Module' })
  @ApiResponse({
    status: 200,
    description: 'Returns all Form Masters By Module',
  })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.formMasterService.findAll();
  }

  @ApiOperation({ summary: 'Get one Form Master' })
  @ApiResponse({ status: 200, description: 'Returns Single Form Master' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('detail')
  findOne(@Query() formaMasterId: FormMasterIdDto) {
    return this.formMasterService.findOne(formaMasterId);
  }

  @ApiOperation({ summary: 'Update a Form Master by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Form Master has been successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Form Master not found' })
  @Patch()
  update(
    @Query() formaMasterId: FormMasterIdDto,
    @Body() updateFormMasterDto: UpdateFormMasterDto,
  ) {
    return this.formMasterService.update(formaMasterId, updateFormMasterDto);
  }

  @ApiOperation({ summary: 'Delete a Form Master by ID' })
  @ApiResponse({
    status: 200,
    description: 'The Form Master has been successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Form Master not found' })
  @Delete()
  remove(@Query() formaMasterId: FormMasterIdDto) {
    return this.formMasterService.remove(formaMasterId);
  }
}
