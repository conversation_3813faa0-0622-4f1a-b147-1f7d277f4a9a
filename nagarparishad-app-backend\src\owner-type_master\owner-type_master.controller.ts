import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { OwnerTypeMasterService } from './owner-type_master.service';
import {
  CreateOwnerTypeMasterDto,
  OwnerTypeMasterIdDto,
  UpdateOwnerTypeMasterDto,
} from './dto/owner-type_master.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Form, Permissions } from '@helper/helpers/role-based-access/permission.decorator';

@ApiTags('Owner Type Master')
@Controller('owner-type-master')
export class OwnerTypeMasterController {
  constructor(
    private readonly ownerTypeMasterService: OwnerTypeMasterService,
  ) {}

  // @ApiOperation({ summary: 'Create a new Property Type' })
  // @ApiResponse({
  //   status: 201,
  //   description: 'The Property Type has been successfully created',
  // })
  // @Post()
  // create(@Body() createPropertyTypeMasterDto: CreateOwnerTypeMasterDto) {
  //   return this.ownerTypeMasterService.create(createPropertyTypeMasterDto);
  // }

  @Form('PropertyType')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get all Property Type' })
  @ApiResponse({ status: 200, description: 'Returns all Property Type' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get()
  findAll() {
    return this.ownerTypeMasterService.findAll();
  }

  
  @Form('PropertyType')
  @Permissions('can_read')
  @ApiOperation({ summary: 'Get one Property Type' })
  @ApiResponse({ status: 200, description: 'Returns Single Property Type' })
  @ApiResponse({ status: 404, description: 'Not found' })
  @Get('getOne')

  findOne(@Query() ownerTypeMaster: OwnerTypeMasterIdDto) {
    return this.ownerTypeMasterService.findOne(ownerTypeMaster);
  }ownerTypeMaster

  // @ApiOperation({ summary: 'Update a Property Type by ID' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'The Property Type has been successfully updated',
  // })
  // @ApiResponse({ status: 404, description: 'Property Type not found' })
  // @Patch()
  // update(
  //   @Query() propertyTypeMaster: PropertyTypeMasterIdDto,
  //   @Body() updatePropertyTypeMasterDto: UpdatePropertyTypeMasterDto,
  // ) {
  //   return this.ownerTypeMasterService.update(
  //     propertyTypeMaster,
  //     updatePropertyTypeMasterDto,
  //   );
  // }

  // @ApiOperation({ summary: 'Delete a Property Type by ID' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'The Property Type has been successfully deleted',
  // })
  // @ApiResponse({ status: 404, description: 'Property Type not found' })
  // @Delete()
  // remove(@Query() propertyTypeMaster: PropertyTypeMasterIdDto) {
  //   return this.propertyTypeMasterService.remove(propertyTypeMaster);
  // }
}
