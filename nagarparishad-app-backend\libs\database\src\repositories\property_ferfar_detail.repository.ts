import { Repository } from 'typeorm';
import { Property_Ferfar_Detail_Entity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class PropertyFerfarDetailsRepository extends Repository<Property_Ferfar_Detail_Entity> {
  constructor(
    @InjectRepository(Property_Ferfar_Detail_Entity)
    private readonly propertyFerfarDetailsRepository: Repository<Property_Ferfar_Detail_Entity>,
  ) {
    super(
      propertyFerfarDetailsRepository.target,
      propertyFerfarDetailsRepository.manager,
      propertyFerfarDetailsRepository.queryRunner,
    );
  }

  async findAllFerfarDetails() {
    return await this.propertyFerfarDetailsRepository
      .createQueryBuilder('property_ferfar_details')
      .orderBy('property_ferfar_details.created_at', 'DESC')
      .getMany();
  }

  async findById(ferfarDetailsId: string) {
    return await this.propertyFerfarDetailsRepository
      .createQueryBuilder('property_ferfar_details')
      .where('property_ferfar_details.property_ferfar_detail_id = :ferfarDetailsId', { ferfarDetailsId })
      .getOne();
  }

  async findByPropertyId(propertyId: string) {
    return await this.propertyFerfarDetailsRepository
      .createQueryBuilder('property_ferfar_details')
      .where('property_ferfar_details.property_id = :propertyId', { propertyId })
      .getMany();
  }

  async findByReassessmentRangeId(reassessmentRangeId: string) {
    return await this.propertyFerfarDetailsRepository
      .createQueryBuilder('property_ferfar_details')
      .where('property_ferfar_details.reassessment_range_id = :reassessmentRangeId', { reassessmentRangeId })
      .getMany();
  }

  async findByPropertyIdAndReassessmentRangeId(propertyId: string, reassessmentRangeId: string) {
    return await this.propertyFerfarDetailsRepository
      .createQueryBuilder('property_ferfar_details')
      .where('property_ferfar_details.property_id = :propertyId', { propertyId })
      .andWhere('property_ferfar_details.reassessment_range_id = :reassessmentRangeId', { reassessmentRangeId })
      .getMany();
  }

  async updateFerfarDetails(ferfarDetailsId: string, input: Partial<Property_Ferfar_Detail_Entity>) {
    return await this.propertyFerfarDetailsRepository
      .createQueryBuilder('property_ferfar_details')
      .update(Property_Ferfar_Detail_Entity)
      .set(input)
      .where('property_ferfar_detail_id = :ferfarDetailsId', { ferfarDetailsId })
      .execute();
  }

  async deleteFerfarDetails(ferfarDetailsId: string) {
    return await this.propertyFerfarDetailsRepository
      .createQueryBuilder('property_ferfar_details')
      .softDelete()
      .where('property_ferfar_detail_id = :ferfarDetailsId', { ferfarDetailsId })
      .execute();
  }
}
