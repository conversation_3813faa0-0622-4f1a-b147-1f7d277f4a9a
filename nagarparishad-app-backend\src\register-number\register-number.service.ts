import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateRegisterNumberDto, UpdateRegisterNumberDto } from './dto/register-number.dto';
import { RegisterNumberRepository } from 'libs/database/repositories/register_number.repository';

@Injectable()
export class RegisterNumberService {
  constructor(
    private readonly registerNumberRepository: RegisterNumberRepository,
  ) {}

  async createRegisterNumber(createRegisterNumberDto: CreateRegisterNumberDto) {
    try {
      const registerNumber = await this.registerNumberRepository.saveRegisterNumber(createRegisterNumberDto);
      return {
        message: 'Register Number created successfully',
        data: registerNumber,
      };
    } catch (error) {
      throw error;
    }
  }

  async findAllRegisterNumber() {
    try {
      const registerNumbers = await this.registerNumberRepository.findAllRegisterNumber();
      if (!registerNumbers || registerNumbers.length === 0) {
        throw new NotFoundException('No Register Numbers found');
      }
      return {
        message: 'Register Numbers fetched successfully',
        data: registerNumbers,
      };
    } catch (error) {
      throw error;
    }
  }

  async findRegisterNumberById(id: string) {
    try {
      const registerNumber = await this.registerNumberRepository.findRegisterNumberById(id);
      if (!registerNumber) {
        throw new NotFoundException(`Register Number with ID ${id} not found`);
      }
      return {
        message: 'Register Number fetched successfully',
        data: registerNumber,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateRegisterNumber(id: string, updateRegisterNumberDto: UpdateRegisterNumberDto) {
    try {
      await this.registerNumberRepository.updateRegisterNumber(id, updateRegisterNumberDto);
      return {
        message: `Register Number with ID ${id} updated successfully`,
      };
    } catch (error) {
      throw error;
    }
  }

  async removeRegisterNumber(id: string) {
    try {
      await this.registerNumberRepository.deleteRegisterNumber(id);
      return {
        message: `Register Number with ID ${id} deleted successfully`,
      };
    } catch (error) {
      throw error;
    }
  }
}
