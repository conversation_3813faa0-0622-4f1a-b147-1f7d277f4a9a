import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedColumnNameInInfoTable1743492919605 implements MigrationInterface {
    name = 'AddedColumnNameInInfoTable1743492919605'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "payment_info" ADD "tax_payer_name" character varying`);
 }

    public async down(queryRunner: QueryRunner): Promise<void> {
   
        await queryRunner.query(`ALTER TABLE "payment_info" DROP COLUMN "tax_payer_name"`);
    }

}
