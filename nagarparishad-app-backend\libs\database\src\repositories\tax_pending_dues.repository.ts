import { Repository } from 'typeorm';
import { TaxPendingDuesEntity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';
import { Financial_year } from '../entities/financial_year.entity';

export class TaxPendingDuesRepository extends Repository<TaxPendingDuesEntity> {
  constructor(
    @InjectRepository(TaxPendingDuesEntity)
    private readonly taxPendingDuesRepository: Repository<TaxPendingDuesEntity>,
    @InjectRepository(Financial_year)
    private readonly financialYearRepository: Repository<Financial_year>,
  ) {
    super(
      taxPendingDuesRepository.target,
      taxPendingDuesRepository.manager,
      taxPendingDuesRepository.queryRunner,
    );
  }

  async saveData(input: {
    sheetIndex: string;
    old_propertyNumber: string;
    surveyNumber: string;
    streetName: string;
    propertyHolderName: string;
    possessionHolderName: string;
    ward: string;
    all_property_tax_sum?: number;
    tax_type_1?: number;
    tax_type_2?: number;
    tax_type_3?: number;
    tax_type_4?: number;
    tax_type_5?: number;
    tax_type_6?: number;
    tax_type_7?: number;
    tax_type_8?: number;
    tax_type_9?: number;
    tax_type_10?: number;
    total: number;
  }): Promise<TaxPendingDuesEntity> {
    // Check if there are any pending dues (total > 0)
    if (input.total <= 0) {
      console.log(`Skipping tax_pending_dues entry for property ${input.old_propertyNumber} with no pending dues`);
      return null;
    }

    // Fetch the current financial year where is_current = true
    const currentFinancialYear = await this.financialYearRepository.findOne({
      where: { is_current: true },
    });

    if (!currentFinancialYear) {
      throw new Error('No current financial year found');
    }

    let taxPendingDues = this.taxPendingDuesRepository.create({
      ...input,
      financial_year: currentFinancialYear,
    });

    taxPendingDues = await this.taxPendingDuesRepository.save(taxPendingDues);
    console.log(`Added pending dues for property ${input.old_propertyNumber} with total amount ${input.total}`);
    return taxPendingDues;
  }

  async findAllData(): Promise<TaxPendingDuesEntity[]> {
    return await this.taxPendingDuesRepository
      .createQueryBuilder('tax_pending_dues')
      .leftJoinAndSelect('tax_pending_dues.financial_year', 'financial_year') // Include financial year details
      .orderBy('tax_pending_dues.updatedAt', 'DESC')
      .getMany();
  }

  async findById(id: string): Promise<TaxPendingDuesEntity> {
    return await this.taxPendingDuesRepository
      .createQueryBuilder('tax_pending_dues')
      .leftJoinAndSelect('tax_pending_dues.financial_year', 'financial_year') // Include financial year details
      .where('tax_pending_dues.dues_id = :dues_id', { dues_id: id })
      .getOne();
  }

  async updateData(id: string, input: {
    old_propertyNumber?: string;
    surveyNumber?: string;
    streetName?: string;
    propertyHolderName?: string;
    possessionHolderName?: string;
    ward?: string;
    all_property_tax_sum?: number;
    tax_type_1?: number;
    tax_type_2?: number;
    tax_type_3?: number;
    tax_type_4?: number;
    tax_type_5?: number;
    tax_type_6?: number;
    tax_type_7?: number;
    tax_type_8?: number;
    tax_type_9?: number;
    tax_type_10?: number;
    total?: number;
  }) {
    return await this.taxPendingDuesRepository
      .createQueryBuilder('tax_pending_dues')
      .update(TaxPendingDuesEntity)
      .set(input)
      .where('dues_id = :dues_id', { dues_id: id })
      .execute();
  }

  async deleteData(id: string) {
    return await this.taxPendingDuesRepository
      .createQueryBuilder('tax_pending_dues')
      .softDelete()
      .where('dues_id = :dues_id', { dues_id: id })
      .execute();
  }
}
