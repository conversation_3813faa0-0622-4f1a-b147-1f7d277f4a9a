import * as React from "react";
import { cn } from "@/lib/utils";
import { ReactTransliterate } from "react-transliterate";

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  transliterate?: boolean;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, transliterate = true, disabled, ...props }, ref) => {
    const valueAsString =
      props.value !== undefined ? props.value.toString() : "";

      const disabledClasses = "font-semibold cursor-not-allowed opacity-50 border-gray-300 bg-gray-100 bg-[#f6f7f8]";

    const defaultClass = disabled ? disabledClasses : "";

    return transliterate ? (
      <ReactTransliterate
        renderComponent={(innerProps) => <textarea {...innerProps} />}
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-[#0000006b] bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          defaultClass,
          className
        )}
        ref={ref}
        {...props}
        onChangeText={(text: string) => {
          // console.log("Transliterated text:", text);
        }}
        value={valueAsString}
        lang="mr"
      />
    ) : (
      <textarea
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-[#0000006b] bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          defaultClass,
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);

Textarea.displayName = "Textarea";

export { Textarea };
