import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateAuthDto } from './dto/update-auth.dto';
import {
  CollectorMasterRepository,
  LogsRepository,
  ModuleMasterRepository,
  RoleMasterRepository,
  RolewiseFormPermissionRepository,
  UserRepository,
} from 'libs/database/repositories';
import { api, log_sub_type, log_type } from 'src/utils/constants';
import { AuthHelper, OtpHelperService, UserField } from '@helper/helpers';
import { EmailLoginDto } from './dto/email-login.dto';
import { JwtAuthService } from '@jwt/jwt-auth';
import { OtpDto } from './dto/validate-otp.dto';
import { PermissionHelper } from 'src/permission/permission.helper';
import { EmailService } from 'src/utils/email.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly authHelper: AuthHelper,
    private readonly jwtAuthService: JwtAuthService,
    private readonly otpHelper: OtpHelperService,
    private readonly roleReposiotry: RoleMasterRepository,
    private readonly permissionRepository: RolewiseFormPermissionRepository,
    private readonly permissionHelper: PermissionHelper,
    private readonly moduleMasterRepository: ModuleMasterRepository,
    private readonly collectorRepo: CollectorMasterRepository,
    private readonly emailService: EmailService,
    private readonly logsRepository: LogsRepository,
  ) {}
  async signUp(input: CreateUserDto) {
    const currentFile = 'auth.service.ts';
    const currentApi = '/api/v1/auth/sign-up';
    try {
      const checkRole = await this.roleReposiotry.findById(input.role);

      if (!checkRole) {
        throw new NotFoundException('Role Not Found');
      }

      const emailExists = await this.userRepository.checkUserExists(
        UserField.EMAIL,
        input.email,
      );

      if (emailExists) {
        throw new HttpException('Email already exists', HttpStatus.CONFLICT);
      }

      const checkPhoneNumber = await this.userRepository.checkUserExists(
        UserField.MOBILE_NUMBER,
        input.mobileNumber,
      );

      if (checkPhoneNumber) {
        throw new HttpException(
          'Mobile number already exists',
          HttpStatus.CONFLICT,
        );
      }

      input.password = await this.authHelper.encodePassword(input.password);
      const savedUser = await this.userRepository.saveData(input);
      console.log('checkRole', checkRole);
      if (checkRole.roleName === 'Collector') {
        await this.collectorRepo.createCollectorForUser({
          user_id: savedUser.user_id,
          isActive: true, // Set default value or based on input
          ward_id: null, // Assuming ward_id is part of input
          role_id: checkRole.role_id,
        });
      }
      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.SAVE,
        file: currentFile,
        api: currentApi,
        message: 'User signed up successfully',
        data: { userId: savedUser.user_id, email: savedUser.email },
        user_id: savedUser.user_id, // The user who was created
      });
      return {
        message: 'User Registered Successfully',
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error during user sign-up',
        data: { error: error.message, stack: error.stack },
        user_id: null, // No user context if creation failed
      });
      console.log('error', error);
      throw error;
    }
  }

  async signIn(input: EmailLoginDto) {
    const currentFile = 'auth.service.ts';
    const currentApi = '/api/v1/auth/sign-in';
    let user;
    try {
      user = await this.userRepository.findByEmail(input.email);
      if (!user) {
        throw new NotFoundException('Invalid email or password');
      }

      if (!user.role) {
        throw new BadRequestException('User Role Not Found');
      }

      const isPasswordMatch = await this.authHelper.isPasswordValid(
        input.password,
        user.password,
      );

      if (!isPasswordMatch) {
        throw new ForbiddenException('Invalid user name or password');
      }

      const otp = await this.otpHelper.generateOtp(user.user_id);

      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.INFO,
        file: currentFile,
        api: currentApi,
        message: 'OTP generated for user sign-in',
        data: { userId: user.user_id, email: user.email },
        user_id: user.user_id,
      });

      return {
        message: `OTP sent to registered email ${otp}`,
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error during user sign-in',
        data: { error: error.message, stack: error.stack },
        user_id: user ? user.user_id : null,
      });
      throw error;
    }
  }

  async getFormattedData(role_id: number) {
    const moduleData = await this.moduleMasterRepository.getAll();

    const permissionsData =
      await this.permissionRepository.findAllData(role_id);

    if (!permissionsData || permissionsData.length == 0) {
      return null;
      // throw new NotFoundException('Data Not Found');
    }

    const formattedData = await this.permissionHelper.formatData(
      permissionsData,
      moduleData,
    );

    return formattedData || null;
  }

  async validateOtp(input: OtpDto) {
    try {
      const { email, otp } = input;

      const user = await this.userRepository.findByEmail(email);

      if (!user) {
        throw new UnauthorizedException();
      }

      //validate otp

      await this.otpHelper.validateOtp(user.user_id, otp);

      const accessToken = await this.jwtAuthService.generateAccessToken({
        user_id: user.user_id,
        email: user.email,
        role: user.role.role_id,
      });

      const refreshToken = await this.jwtAuthService.generateRefreshToken({
        user_id: user.user_id,
        email: user.email,
        role: user.role.role_id,
      });

      await this.updateRefreshToken(user.user_id, refreshToken);

      const formattedData = await this.getFormattedData(user.role.role_id);

      return {
        message: 'Logged in successfully',
        data: {
          accessToken,
          refreshToken,
          user: {
            firstName: user.firstname,
            lastName: user.lastname,
            email: user.email,
            role: user.role.role_id,
            Permissions: formattedData || null,
          },
        },
      };
    } catch (error) {
      throw error;
    }
  }

  async signOut(user_id: string) {
    const currentFile = 'auth.service.ts';
    const currentApi = '/api/v1/auth/sign-out';
    try {
      await this.userRepository.updateData(user_id, {
        refreshToken: null,
      });

      await this.logsRepository.logAction({
        logType: log_type.SUCCESS,
        logSubType: log_sub_type.INFO,
        file: currentFile,
        api: currentApi,
        message: 'User signed out successfully',
        data: { userId: user_id },
        user_id: user_id,
      });

      return {
        message: 'Signed out successfully',
      };
    } catch (error) {
      await this.logsRepository.logAction({
        logType: log_type.ERROR,
        logSubType: log_sub_type.CATCH_ERROR,
        file: currentFile,
        api: currentApi,
        message: 'Error during user sign-out',
        data: { error: error.message, stack: error.stack },
        user_id: user_id,
      });
      throw error;
    }
  }

  async refreshToken(user_id: string, token: string) {
    try {
      const user = await this.userRepository.findById(user_id);

      if (!user || !user.refreshToken) {
        throw new ForbiddenException('Access Denied');
      }

      const refreshTokenMatches = await this.authHelper.isPasswordValid(
        token,
        user.refreshToken,
      );

      if (!refreshTokenMatches) {
        throw new ForbiddenException('Access Invaid Denied');
      }
      const refreshToken = await this.jwtAuthService.generateRefreshToken({
        user_id: user.user_id,
        email: user.email,
        role: user.role.role_id,
      });

      await this.updateRefreshToken(user.user_id, refreshToken);

      const accessToken = await this.jwtAuthService.generateAccessToken({
        user_id: user.user_id,
        email: user.email,
        role: user.role.role_id,
      });

      return {
        messaage: 'Token refreshed successfully',
        data: {
          accessToken,
          refreshToken,
        },
      };
    } catch (error) {
      throw error;
    }
  }

  private async updateRefreshToken(
    user_id: string,
    refreshToken?: string | null,
  ) {
    const hashedRefreshToken = refreshToken
      ? await this.authHelper.encodePassword(refreshToken)
      : null;
    await this.userRepository.updateData(user_id, {
      refreshToken: hashedRefreshToken,
    });
  }

  async sendOtp(email: string): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const otp = await this.otpHelper.generateOtpForFiveMin(user.user_id);

    await this.emailService.sendOTP(email, otp);

    return { message: `OTP sent to ${otp}` };
  }

  async verifyOtp(email: string, otp: string): Promise<{ message: string }> {

    try{
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      throw new NotFoundException('User not found');
    }
    

          await this.otpHelper.validateOtp(user.user_id, otp);


 
    return { message: 'OTP verified successfully' };

  }
  catch{
          throw new NotFoundException('OTP not found');

  }
  }
  

  async resetPassword(email: string, otp: string, password: string): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      throw new NotFoundException('User not found');
    }
          await this.otpHelper.validateOtp(user.user_id, otp);


    await this.userRepository.update(user.user_id, {
      password: await this.authHelper.encodePassword(password),

    });

    return { message: 'Password reset successfully' };
  }
}
