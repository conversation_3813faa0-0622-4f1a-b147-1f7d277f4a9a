export interface TaxUserByFYInterface {
  tax_fy_records_id: string;
  financial_year: string;
  status: string;
  total_property: number;
  total_property_processed: number;
  tax_property_id?: string;
  bill_no?: string;
  total_tax?: number;
  propertyMaster?: {
    propertyNumber: string;
    firstname: string;
    lastname: string;
    zone: {
      zoneName: string;
    };
  };
}

export interface TaxUserListByFYResponseInterface {
  statusCode: number;
  message: string;
  data: TaxUserByFYInterface[];
}

export interface YearWiseRecordInterface {
  financial_year: string;
  status: string;
  tax_fy_records_id: string;
  total_property: number;
  total_property_processed: number;
}

export interface YearListByFYResponseInterface {
  statusCode: number;
  message: string;
  data: YearWiseRecordInterface[];
}


export interface PublishInterface {
    tax_fy_records_id: string,
    status: string,
    total_property: number,
    total_property_processed: number,
    is_published: boolean,
    createdAt: string,
    updatedAt: string,
    deletedAt: null
}
export interface PublishResponseInterface {
  statusCode: number,
  message: string,
  data : PublishInterface
}
