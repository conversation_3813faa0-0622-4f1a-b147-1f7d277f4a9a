export interface AgeWiseRateInterface {
    tax_fy_records_id: string,
    status: string,
    total_property: number,
    total_property_processed: number,
    is_published: boolean,
    createdAt: string,
    updatedAt: string,
    deletedAt: null
}

export interface DepreciationRateMaster {
    depreciation_rate_id: string;
    from_age: number;
    to_age: number;
    financial_year?: string; // Optional for backward compatibility
    value: number;
    status: string;
    property_type_class_id: {
      property_type_class_id: string;
      property_type_class: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };
    reassessmentRange?: {
      reassessment_range_id: string;
      start_range: string;
      end_range: string;
    };
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  }
  export interface DepreciationRateApi {
    statusCode: number;
    message: string;
    data: DepreciationRateMaster[];
  }

  export interface DepreciationRateCreateApi {
    statusCode: number;
    message: string;
  }
  export interface DepreciationRateUpdateApi {
    financial_year?: string, // Optional for backward compatibility
    reassessment_range_id: string,
    value: number,
    status:string,
    property_type_class_id:string,
    depreciation_rate_id:string,
    from_age: number;
    to_age: number;
  }

  export interface DepreciationRateSendApiObj {
    financial_year?: string; // Optional for backward compatibility
    reassessment_range_id: string;
    value: number;
    status: string;
    property_type_class_id: string;
    from_age: number;
    to_age: number;
    }