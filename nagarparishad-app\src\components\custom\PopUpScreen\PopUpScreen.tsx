import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useTranslation } from "react-i18next";

export interface PopupScreenProps {
  title: string;
  description?: string;
  isOpen: boolean;
  toggle: () => void;
  children: React.ReactNode;
}

const PopUpScreen: React.FC<PopupScreenProps> = ({
  title,
  description,
  isOpen,
  toggle,
  children,
}) => {
  const close = () => {
    toggle();
  };
  const { t } = useTranslation();
  return (
    <Dialog open={isOpen} onOpenChange={close}>
      <DialogContent className="w-full  overflow-scroll  border-2 bg-white">
        <DialogHeader>
          <DialogTitle>{t(title)}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        {children}
      </DialogContent>
    </Dialog>
  );
};

export default PopUpScreen;
