// Interface for Property information
export interface PropertyInfo {
  property_id: string;
  propertyNumber: string;
  old_propertyNumber?: string;
}

// Interface for Financial Year information
export interface FinancialYearInfo {
  financial_year_id: string;
  financial_year_range: string;
  from_date: string;
  to_date: string;
  is_active: string;
  is_current: boolean;
}

// Interface for Paid and Non-Paid Data Entity
export interface PaidAndNonPaidDataInterface {
  paid_and_non_paid_data_id: string;
  financial_year_range: string;
  name: string;
  property_number: string;
  old_property_number?: string;
  total_tax: number;
  paid: number;
  remaining: number;
  is_paid: boolean;
  property: PropertyInfo;
  financial_year: FinancialYearInfo;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
}

// Interface for API Response with pagination
export interface PaidDataApiResponse {
  data: PaidAndNonPaidDataInterface[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  nextPageAvailable: boolean;
  prevPageAvailable: boolean;
}

// Interface for API Response wrapper
export interface PaidDataResponseWrapper {
  message: string;
  success: boolean;
  data: PaidDataApiResponse;
}

// Interface for Generate Data Request
export interface GenerateDataRequest {
  financialYearRange: string;
}

// Interface for Generate Data Response
export interface GenerateDataResponse {
  success: boolean;
  message: string;
  data: {
    financialYear: string;
    totalProperties: number;
    processedCount: number;
    errorCount: number;
    errors: Array<{
      property_id: string;
      property_number: string;
      error: string;
    }>;
  };
}

// Interface for Search Parameters
export interface SearchParams {
  page?: number;
  limit?: number;
  financialYearRange?: string;
  searchValue?: string;
  searchOn?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

// Interface for Financial Year dropdown
export interface FinancialYearOption {
  value: string;
  label: string;
  financial_year_id: string;
  financial_year_range: string;
  is_current: boolean;
}

// Interface for Table Column Configuration
export interface PaidDataTableColumn {
  accessorKey: string;
  header: string;
  cell?: (props: any) => React.ReactNode;
  sortable?: boolean;
}

// Interface for Pagination Props
export interface PaginationProps {
  currentPage: number;
  pageSize: number;
  totalPages: number;
  totalRecords: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

// Interface for Search Column Options
export interface SearchColumnOption {
  value: string;
  label: string;
}

// Interface for Controller State
export interface PaidDataControllerState {
  page: number;
  limit: number;
  searchValue: string;
  searchOn: string;
  financialYearRange: string;
  sortBy: string;
  sortOrder: 'ASC' | 'DESC';
}

// Interface for Controller Actions
export interface PaidDataControllerActions {
  handleSearchKeyChange: (value: string) => void;
  handleSearchValueChange: (value: string) => void;
  handleFinancialYearChange: (value: string) => void;
  handleSortChange: (field: string, order: 'ASC' | 'DESC') => void;
  generateData: (financialYearRange: string) => void;
  refetchPaidUsers: () => void;
  refetchNonPaidUsers: () => void;
  refetchAllData: () => void;
}

// Interface for Dashboard Stats
export interface DashboardStats {
  totalProperties: number;
  totalPaidUsers: number;
  totalNonPaidUsers: number;
  totalTaxAmount: number;
  totalPaidAmount: number;
  totalRemainingAmount: number;
  paymentPercentage: number;
}

// Export all interfaces
export type {
  PropertyInfo,
  FinancialYearInfo,
  PaidAndNonPaidDataInterface,
  PaidDataApiResponse,
  PaidDataResponseWrapper,
  GenerateDataRequest,
  GenerateDataResponse,
  SearchParams,
  FinancialYearOption,
  PaidDataTableColumn,
  PaginationProps,
  SearchColumnOption,
  PaidDataControllerState,
  PaidDataControllerActions,
  DashboardStats,
};
