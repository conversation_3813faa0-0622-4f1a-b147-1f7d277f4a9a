import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateUsageMasterDto {
  @ApiProperty({ type: 'string', required: true, name: 'usage' })
  @IsNotEmpty()
  @IsString()
  usage: string;
}

export class UpdateUsageMasterDto extends PartialType(CreateUsageMasterDto) {}

export class UsageMasterDto {
  @ApiProperty({ type: 'string', required: true, name: 'usage_id' })
  @IsNotEmpty()
  @IsUUID()
  usage_id: string;
}
