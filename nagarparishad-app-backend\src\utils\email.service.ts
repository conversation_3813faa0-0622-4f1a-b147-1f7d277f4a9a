import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { smtpConfig } from './smtp.config';

@Injectable()
export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport(smtpConfig);
    this.transporter.verify((error, success) => {
  if (error) {
    console.error("SMTP connection failed:", error);
  } else {
    console.log("SMTP is ready to take our messages!");
  }
});

  }

  async sendOTP(email: string, otp: string): Promise<void> {
    const mailOptions = {
      from: smtpConfig.auth.user,
      to: email,
      subject: 'Your OTP for Nagarparishad App',
      text: `Your OTP is: ${otp}`,
      html: `<p>Your OTP is: <b>${otp}</b></p>`,
    };

    try {
      console.log("Sending email...", mailOptions)
      await this.transporter.sendMail(mailOptions);
    } catch (error) {
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  async sendEmailWithAttachment(
    to: string,
    subject: string,
    text: string,
    html: string,
    attachments: Array<{ filename: string; content: Buffer }>,
  ): Promise<void> {
    const mailOptions = {
      from: smtpConfig.auth.user,
      to,
      subject,
      text,
      html,
      attachments,
    };

    try {
      console.log('Sending email with attachment...', mailOptions);
      await this.transporter.sendMail(mailOptions);
    } catch (error) {
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }
}
