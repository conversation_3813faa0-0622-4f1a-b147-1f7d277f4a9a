import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaidDataController } from './paid-data.controller';
import { PaidDataService } from './paid-data.service';
import { PaidDataEntity, PaidAndNonPaidDataEntity, PropertyEntity, Financial_year, PaymentInfoEntity, WarshilKarEntity } from 'libs/database/entities';
import { PaidDataRepository, PaidAndNonPaidDataRepository, PropertyMasterRepository, Financial_yearRepository, PaymentInfoRepository, WarshikKarRepository } from 'libs/database/repositories';

@Module({
  imports: [
    TypeOrmModule.forFeature([PaidDataEntity, PaidAndNonPaidDataEntity, PropertyEntity, Financial_year, PaymentInfoEntity, WarshilKarEntity]),
  ],
  controllers: [PaidDataController],
  providers: [PaidDataService, PaidDataRepository, PaidAndNonPaidDataRepository, PropertyMasterRepository, Financial_yearRepository, PaymentInfoRepository, WarshikKarRepository],
  exports: [PaidDataService],
})
export class PaidDataModule {}
