import React from "react";
import SPMSLogo from "@/assets/img/contact-us/spms-img.jpg";
import { MapPin, Mail, Clock3 } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { object, z } from "zod";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import BreadCrumb from "@/components/custom/BreadCrumb";

const ContactUs = () => {
  const { t } = useTranslation();
  const schema = object({
    name: z.string().min(1, { message: t("errorsRequiredField") }),
    email: z
      .string()
      .min(1, { message: t("errorsRequiredField") })
      .email({ message: t("errorsInvalidEmailFormat") }),
    subject: z.string().min(1, { message: t("errorsRequiredField") }),
    message: z.string().min(1, { message: t("errorsRequiredField") }),
  });
  const methods = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: "",
      email: "",
      subject: "",
      message: "",
    },
  });

  const {
    formState: { errors },
  } = methods;

  const onSubmit = (data: any) => {
    console.log(JSON.stringify(data));
  };

  return (
    <>
      <BreadCrumb className={"bg-white"} />
      <div className="max-w-[92rem] mx-auto py-8 sm:px-10 px-4">
        <div className="flex sm:flex-row flex-col justify-around w-full mb-8">
          <div className="text-center sm:w-4/12 w-full">
            <MapPin
              stroke="#54B4EE"
              className="md:w-9 md:h-9 w-8 h-8 min-w-5 my-2 mx-auto"
            />
            <p className="2xl:text-lg text-base max-w-[300px] mx-auto">
              <span className="2xl:text-2xl text-lg font-semibold my-3">
                {t("contactUs.addressTitle")}
              </span>
              <br />
              {t("contactUs.address")}
            </p>
          </div>
          <div className="text-center sm:w-4/12 w-full border-l-2 border-r-2">
            <Mail
              stroke="#54B4EE"
              className="md:w-9 md:h-9 w-8 h-8 min-w-5 my-2 mx-auto"
            />
            <p className="2xl:text-base text-base">
              <span className="2xl:text-2xl text-lg font-semibold my-3">
                {t("contactUs.emailTitle")}
              </span>
              <br />
              {t("contactUs.emailId")}
            </p>
          </div>
          <div className="text-center sm:w-4/12 w-full">
            <Clock3
              stroke="#54B4EE"
              className="md:w-9 md:h-9 w-8 h-8 min-w-5 my-2 mx-auto"
            />
            <p className="2xl:text-lg text-base ">
              <span className="2xl:text-2xl text-lg font-semibold my-3">
                {t("contactUs.openingTitle")}
              </span>
              <br />
              {t("contactUs.opening")}
              <br />
              09:00 AM - 06:00 PM
            </p>
          </div>
        </div>

        <hr />
        <div className="flex items-center py-8">
          <div className="lg:w-4/6 sm:w-7/12 sm:block hidden w-full h-full pr-4">
            <div className="w-full h-[550px]">
              <img
                src={SPMSLogo}
                alt="Shirol Municipal Corporation"
                className="mb-5 max-w-full w-full h-full object-cover rounded-20 overflow-hidden shadow-md"
              />
            </div>
          </div>
          <div className="lg:w-2/6 sm:w-5/12 w-full max-w-4xl mb-8 sm:pl-5 pl-0">
            <h3 className="mb-5 text-2xl font-bold">
              {t("contactUs.leaveCommentTitle")}
            </h3>
            <Form {...methods}>
              <form onSubmit={methods.handleSubmit(onSubmit)}>
                <FormField
                  control={methods.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>{t("contactUs.yourName")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 mb-2"
                          placeholder={t("contactUs.yourNamePlaceholder")}
                          {...field}
                        />
                      </FormControl>
                      {errors.name && (
                        <FormMessage className="ml-1">Error</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
                <FormField
                  control={methods.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>{t("contactUs.yourEmail")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 mb-2"
                          placeholder={t("contactUs.yourEmailPlaceholder")}
                          {...field}
                        />
                      </FormControl>
                      {errors.email && (
                        <FormMessage className="ml-1">Error</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
                <FormField
                  control={methods.control}
                  name="subject"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>{t("contactUs.yourSubject")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 mb-2"
                          placeholder={t("contactUs.yourSubjectPlaceholder")}
                          {...field}
                        />
                      </FormControl>{" "}
                      {errors.subject && (
                        <FormMessage className="ml-1">Error</FormMessage>
                      )}
                    </FormItem>
                  )}
                />
                <FormField
                  control={methods.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>{t("contactUs.yourMessage")}</FormLabel>
                      <FormControl>
                        <Textarea
                          className="mt-1 mb-2 block w-full"
                          placeholder={t("contactUs.yourMessagePlaceholder")}
                          {...field}
                        />
                      </FormControl>{" "}
                      {errors.message && (
                        <FormMessage className="ml-1">Error</FormMessage>
                      )}
                    </FormItem>
                  )}
                />

                <Button className="w-full my-4" type={"submit"}>
                  {t("contactUs.submitMessage")}
                </Button>
              </form>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContactUs;
