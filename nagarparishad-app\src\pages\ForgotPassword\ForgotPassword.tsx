import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import { Eye, EyeOff, Loader2, ArrowLeft, RefreshCw } from "lucide-react";
import logo from "@/assets/img/homepage/logo.png";
import BreadCrumb from "@/components/custom/BreadCrumb";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import AuthController from "@/controller/AuthController";

const ForgotPassword: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1); // 1: email, 2: otp, 3: password
  const [isOtpComplete, setIsOtpComplete] = useState(false);
  const [otpValue, setOtpValue] = useState("");
  const [email, setEmail] = useState("");
  const [loader, setLoader] = useState(false);
  const [resendOtpLoader, setResendOtpLoader] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);

  const handleTogglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setIsConfirmPasswordVisible(!isConfirmPasswordVisible);
  };

  // Email schema for the first step
  const emailSchema = z.object({
    email: z
      .string()
      .refine((email) => email.trim() !== "", {
        message: t("validationss.email.emailRequiredError"),
      })
      .refine((email) => /\S+@\S+\.\S+/.test(email), {
        message: t("validationss.email.emailInvalidError"),
      }),
  });

  // OTP schema for the second step
  const otpSchema = z.object({
    otp: z.string().min(6, {
      message: t("validationss.otp.otpRequiredError"),
    }),
  });

  // Password schema for the final step
  const passwordSchema = z.object({
    password: z
      .string()
      .min(8, { message: t("validationss.password.minLength") })
      .refine((password) => /[A-Z]/.test(password), {
        message: t("validationss.password.uppercase"),
      })
      .refine((password) => /[a-z]/.test(password), {
        message: t("validationss.password.lowercase"),
      })
      .refine((password) => /[0-9]/.test(password), {
        message: t("validationss.password.number"),
      })
      .refine((password) => /[^A-Za-z0-9]/.test(password), {
        message: t("validationss.password.special"),
      }),
    confirmPassword: z.string(),
  }).refine((data) => data.password === data.confirmPassword, {
    message: t("validationss.password.match"),
    path: ["confirmPassword"],
  });

  // Form instances
  const emailForm = useForm<z.infer<typeof emailSchema>>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: "",
    },
  });

  const otpForm = useForm<z.infer<typeof otpSchema>>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      otp: "",
    },
  });

  const passwordForm = useForm<z.infer<typeof passwordSchema>>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  // Timer for resend OTP
  const startResendTimer = () => {
    setResendTimer(30);
    const interval = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Handle email submission
  const onSubmitEmail = (data: z.infer<typeof emailSchema>) => {
    setLoader(true);
    setEmail(data.email);
    
    AuthController.sendPasswordResetOTP(data.email, (response) => {
      if (response.status) {
        setCurrentStep(2);
        setLoader(false);
        startResendTimer();
        toast({
          title: t("passwordResetOtpSent"),
          description: t("passwordResetOtpSentDesc"),
          variant: "success",
        });
      } else {
        setLoader(false);
        toast({
          title: t("error"),
          description: t("emailIsNotRegistered"),
          variant: "destructive",
        });
      }
    });
  };

  // Handle OTP verification
  const onSubmitOtp = (data: z.infer<typeof otpSchema>) => {
    setLoader(true);
    
    AuthController.verifyPasswordResetOTP(email, data.otp, (response) => {
      if (response.status ) {
        setCurrentStep(3);
        setLoader(false);
        toast({
          title: t("otpVerified"),
          description: t("otpVerifiedDesc"),
          variant: "success",
        });
      } else {
        setLoader(false);
        toast({
          title: t("error"),
          description: response.message,
          variant: "destructive",
        });
      }
    });
  };

  // Handle password reset
  const onSubmitPassword = (data: z.infer<typeof passwordSchema>) => {
    setLoader(true);
    
    AuthController.resetPassword(email, otpValue, data.password, (response) => {
      if (response.status ) {
        setLoader(false);
        toast({
          title: t("passwordResetSuccess"),
          description: t("passwordResetSuccessDesc"),
          variant: "success",
        });
        navigate("/login");
      } else {
        setLoader(false);
        toast({
          title: t("error"),
          description: response.message,
          variant: "destructive",
        });
      }
    });
  };

  // Handle resend OTP
  const handleResendOTP = () => {
    if (resendTimer > 0) return;
    
    setResendOtpLoader(true);
    otpForm.resetField("otp");
    setIsOtpComplete(false);
    setOtpValue("");
    
    AuthController.sendPasswordResetOTP(email, (response) => {
      if (response.status === 200) {
        setResendOtpLoader(false);
        startResendTimer();
        toast({
          title: t("otpResent"),
          description: t("otpResentDesc"),
          variant: "success",
        });
      } else {
        setResendOtpLoader(false);
        toast({
          title: t("error"),
          description: response.message,
          variant: "destructive",
        });
      }
    });
  };

  // Handle back button
  const handleBack = () => {
    if (currentStep === 2) {
      setCurrentStep(1);
      setOtpValue("");
      setIsOtpComplete(false);
      otpForm.reset();
    } else if (currentStep === 3) {
      setCurrentStep(2);
      passwordForm.reset();
    }
  };

  const handleNavigateHome = () => {
    navigate("/");
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 1:
        return t("forgotPassword");
      case 2:
        return t("verifyOtp");
      case 3:
        return t("resetPassword");
      default:
        return t("forgotPassword");
    }
  };

  const getStepDescription = () => {
    switch (currentStep) {
      case 1:
        return t("forgotPasswordDesc");
      case 2:
        return t("verifyOtpDesc");
      case 3:
        return t("resetPasswordDesc");
      default:
        return t("forgotPasswordDesc");
    }
  };

  return (
    <>
      <div className="h-fit min-h-[55vh] bg-blue-100 flex items-center justify-center py-12 px-4">
        <div className="bg-white shadow-lg rounded-lg overflow-hidden md:flex sm:w-3/4 w-full max-w-5xl">
          {/* Form Section */}
          <div className="w-full md:w-full sm:p-12 p-8">
            {/* Step indicator */}
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center space-x-2">
                {currentStep > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleBack}
                    className="p-2"
                  >
                    <ArrowLeft size={16} />
                  </Button>
                )}
                <h2 className="text-2xl font-semibold">{getStepTitle()}</h2>
              </div>
              {/* <div className="text-sm text-gray-500">
                {t("step")} {currentStep} {t("of")} 3
              </div> */}
            </div>

            {/* <p className="text-zinc-600 mb-6">{getStepDescription()}</p> */}

            {/* Step 1: Email */}
            {currentStep === 1 && (
              <Form {...emailForm}>
                <form onSubmit={emailForm.handleSubmit(onSubmitEmail)}>
                  <FormField
                    control={emailForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("email")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("emailPlaceholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="my-6">
                    {loader ? (
                      <Button disabled className="w-full">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t("pleaseWait")}
                      </Button>
                    ) : (
                      <Button type="submit" variant="submit" className="w-full">
                        {t("sendOtp")}
                      </Button>
                    )}
                  </div>
                </form>
              </Form>
            )}

            {/* Step 2: OTP */}
            {currentStep === 2 && (
              <Form {...otpForm}>
                <form onSubmit={otpForm.handleSubmit(onSubmitOtp)}>
                  <FormField
                    control={otpForm.control}
                    name="otp"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("loginOTP")}</FormLabel>
                        <FormControl>
                          <InputOTP
                            maxLength={6}
                            value={otpValue}
                            onChange={(value) => {
                              setOtpValue(value);
                              field.onChange(value);
                              setIsOtpComplete(value.length === 6);
                            }}
                          >
                            <InputOTPGroup className="gap-6 w-full pl-1">
                              <InputOTPSlot index={0} className="border-2" />
                              <InputOTPSlot index={1} className="border-2" />
                              <InputOTPSlot index={2} className="border-2" />
                              <InputOTPSlot index={3} className="border-2" />
                              <InputOTPSlot index={4} className="border-2" />
                              <InputOTPSlot index={5} className="border-2" />
                            </InputOTPGroup>
                          </InputOTP>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="my-6">
                    {loader ? (
                      <Button disabled className="w-full">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t("pleaseWait")}
                      </Button>
                    ) : (
                      <Button
                        disabled={!isOtpComplete}
                        type="submit"
                        variant="submit"
                        className="w-full"
                      >
                        {t("verifyOtp")}
                      </Button>
                    )}
                  </div>

<div className="flex justify-end items-center gap-6">
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={handleResendOTP}
                      disabled={resendTimer > 0 || resendOtpLoader}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      {resendOtpLoader ? (
                        <>
                          <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                          {t("resendingOtp")}
                        </>
                      ) : (
                        <>
                          <RefreshCw className="mr-2 h-3 w-3" />
                          {resendTimer > 0 ? `${t("resendOtp")} (${resendTimer})` : t("resendOtp")}
                        </>
                      )}
                    </Button>
                    {/* <Button
                      type="button"
                      variant="ghost"
                      onClick={() => navigate("/login")}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      {t("backToLogin")}
                    </Button> */}
                  </div>
                </form>
              </Form>
            )}

            {/* Step 3: Password Reset */}
            {currentStep === 3 && (
              <Form {...passwordForm}>
                <form onSubmit={passwordForm.handleSubmit(onSubmitPassword)}>
                  <FormField
                    control={passwordForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("newPassword")}</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={isPasswordVisible ? "text" : "password"}
                              placeholder={t("newPasswordPlaceholder")}
                              {...field}
                              transliterate={false}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                              onClick={handleTogglePasswordVisibility}
                            >
                              {isPasswordVisible ? (
                                <Eye className="h-4 w-4" />
                              ) : (
                                <EyeOff className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={passwordForm.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem className="mt-4">
                        <FormLabel>{t("confirmPassword")}</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={isConfirmPasswordVisible ? "text" : "password"}
                              placeholder={t("confirmPasswordPlaceholder")}
                              {...field}
                                                            transliterate={false}

                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                              onClick={handleToggleConfirmPasswordVisibility}
                            >
                              {isConfirmPasswordVisible ? (
                                <Eye className="h-4 w-4" />
                              ) : (
                                <EyeOff className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="my-6">
                    {loader ? (
                      <Button disabled className="w-full">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t("pleaseWait")}
                      </Button>
                    ) : (
                      <Button type="submit" variant="submit" className="w-full">
                        {t("resetPassword")}
                      </Button>
                    )}
                  </div>
                </form>
              </Form>
            )}

{currentStep === 1 && (
  <div className="flex justify-end items-center">
    <Button
      variant="ghost"
      onClick={() => navigate("/login")}
      className="text-sm text-blue-600 hover:text-blue-800"
    >
      {t("backToLogin")}
    </Button>
  </div>
)}

          </div>
          
          {/* Logo Section */}
          <div className="w-full md:w-full bg-blue-50 p-8 flex flex-col items-center justify-center">
            <div className="flex items-center justify-center mb-6 w-40 h-40 cursor-pointer">
              <img
                src={logo}
                alt="Logo"
                className="w-full h-full"
                onClick={handleNavigateHome}
              />
            </div>
            <h3 className="text-3xl !font-bold mb-3 text-center">
              {t("HeaderSectionLogoname")}
            </h3>
            <p className="text-zinc-600 text-sm text-center">
              {t("loginClassicalContentText")}
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default ForgotPassword;