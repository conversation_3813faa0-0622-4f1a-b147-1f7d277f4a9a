import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length,
  Matches,
} from 'class-validator';

export class CreateUserDto {
  @ApiProperty({ description: 'First name of the user', example: '<PERSON>' })
  @IsNotEmpty()
  @IsString()
  @Length(1, 50)
  firstname: string;

  @ApiProperty({ description: 'Last name of the user', example: '<PERSON><PERSON>' })
  @IsNotEmpty()
  @IsString()
  @Length(1, 50)
  lastname: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Password for the user account',
    example: 'Password123!',
  })
  @IsNotEmpty()
  @IsString()
  @Length(6, 50)
  // Use a regex pattern to enforce strong passwords if needed
  @Matches(/(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[\W_]).{6,}/, {
    message:
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
  })
  password: string;

  @ApiProperty({
    description: 'User active status',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Address of the user',
    example: '123 Main St, Anytown, USA',
  })
  @IsNotEmpty()
  @IsString()
  address: string;

  @ApiProperty({
    description: 'Role ID associated with the user',
    example: '1',
  })
  @IsNotEmpty()
  @IsNumber()
  role: number; // Assuming role_id is a string, adjust accordingly if it's a different type

  @ApiProperty({
    description: 'Mobile number of the user',
    example: '+**********',
  })
  @IsNotEmpty()
  @IsString()
  @Matches(/^[6-9]\d{9}$/, {
    message:
      'Mobile number must be a valid Indian mobile number and be exactly 10 digits',
  })
  mobileNumber: string;

  @ApiProperty({
    description: 'Profile picture URL of the user',
    example: 'http://example.com/profile-pic.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  profilePic: string;
}
