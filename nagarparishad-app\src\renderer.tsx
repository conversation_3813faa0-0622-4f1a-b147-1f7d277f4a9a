import React from "react";
import ReactDOM from "react-dom/client";
import App from "./pages";
import { I18nextProvider } from "react-i18next";
import i18n from "@/translation/i18n"; // Your i18next configuration
import { BrowserRouter as Router } from "react-router-dom";
import "./index.css";
import { GlobalContextProvider } from "@/context/GlobalContext";
import { Toaster } from "@/components/ui/toaster";
import { StepProvider } from "./context/WizardContext";
import { PropertyContextProvider } from "@/context/PropertyContext";
import { NotificationProvider } from "@/context/NotificationContext";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { FontSizeProvider } from "./context/FontSizeContext";
import ErrorBoundary from "./components/globalcomponent/ErrorBoundary";
import { PermissionProvider } from "./context/PermissionContext";

const queryClient = new QueryClient();
//hello this is testing

const root = ReactDOM.createRoot(document.getElementById("root"));

root.render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <ErrorBoundary>
        <FontSizeProvider>
          <PermissionProvider>
            <NotificationProvider>
              <GlobalContextProvider>
                <PropertyContextProvider>
                  <StepProvider>
                    <Router>
                      <I18nextProvider i18n={i18n}>
                        <App />
                        <Toaster />
                      </I18nextProvider>
                    </Router>
                  </StepProvider>
                </PropertyContextProvider>
              </GlobalContextProvider>
            </NotificationProvider>
          </PermissionProvider>
        </FontSizeProvider>
      </ErrorBoundary>
    </QueryClientProvider>
  </React.StrictMode>
);
