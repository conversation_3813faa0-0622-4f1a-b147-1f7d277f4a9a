import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddedCronEnitity1749646677755 implements MigrationInterface {
  name = 'AddedCronEnitity1749646677755';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "cron_job_failures" ("failure_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "job_name" character varying(255) NOT NULL, "job_id" character varying(255) NOT NULL, "error_message" text NOT NULL, "attempt_count" integer NOT NULL DEFAULT '1', "last_attempt" TIMESTAMP NOT NULL, "first_failure" TIMESTAMP, "job_data" jsonb, "status" character varying(50) NOT NULL DEFAULT 'failed', "resolution_notes" text, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_3db04726c52f16874663034e267" UNIQUE ("job_id"), CONSTRAINT "PK_dd0839ecd9ba64c58ad43ecba14" PRIMARY KEY ("failure_id"))`,
    );
  }
  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "cron_job_failures"`);
  }
}
