import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateFormMasterDto,
  FormMasterIdDto,
  UpdateFormMasterDto,
} from './dto/form-master.dto';

import { FormMasterRepository } from 'libs/database/repositories';

@Injectable()
export class FormMasterService {
  constructor(private readonly formMasterRepository: FormMasterRepository) {}
  async create(createFormMasterDto: CreateFormMasterDto) {
    try {
      await this.formMasterRepository.saveData(createFormMasterDto);

      return {
        message: 'Record Saved SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }

  async findAll() {
    try {
      const getAllData = await this.formMasterRepository.findAllData();

      if (!getAllData) {
        throw new NotFoundException('Data Not Found');
      }

      return {
        message: 'Records Fetched Success',
        data: getAllData,
      };
    } catch (error) {
      throw error;
    }
  }

  async findOne(formaMasterId: FormMasterIdDto) {
    try {
      const { form_id } = formaMasterId;
      const checkData = await this.formMasterRepository.findById(form_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      return {
        message: 'Data found Sucess',
        data: checkData,
      };
    } catch (error) {
      throw error;
    }
  }

  async update(
    formaMasterId: FormMasterIdDto,
    updateFormMasterDto: UpdateFormMasterDto,
  ) {
    try {
      const { form_id } = formaMasterId;
      const checkData = await this.formMasterRepository.findById(form_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }

      const updateData = await this.formMasterRepository.updateData(
        form_id,
        updateFormMasterDto,
      );

      if (updateData.affected === 0) {
        throw new NotAcceptableException('Failed to update data');
      }

      return {
        message: 'Update Success',
      };
    } catch (error) {
      throw error;
    }
  }

  async remove(formaMasterId: FormMasterIdDto) {
    try {
      const { form_id } = formaMasterId;
      const checkData = await this.formMasterRepository.findById(form_id);
      if (!checkData) {
        throw new NotFoundException('Data Not found');
      }
      const deleteData = await this.formMasterRepository.deleteData(form_id);

      if (deleteData.affected === 0) {
        throw new NotAcceptableException('Failed to delete data');
      }

      return {
        message: 'Data Deleted SuccessFully',
      };
    } catch (error) {
      throw error;
    }
  }
}
