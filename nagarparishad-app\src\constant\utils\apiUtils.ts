import { PROPERTY } from "@/constant/config/api.config";
import exp from "constants";
export const ADD_ZONE = "/v1/zone-master/create";
export const ZONE_LIST = "/v1/zone-master";
export const UPDATE_ZONE = "/v1/zone-master/update?zone_id=";
export const DELETE_ZONE = "/v1/zone-master/delete?zone_id=";

export const WARD_LIST = "/v1/ward-master";
export const UPDATE_WARD = "/v1/ward-master/update?ward_id=";
export const ADD_WARD = "/v1/ward-master/create";
export const DELETE_WARD = "/v1/ward-master/delete?ward_id=";

export const Location_List = "/v1/location-master";
export const ADD_LOCATION = "/v1/location-master/create";
export const UPDATE_LOCATION = "/v1/location-master/update?location_id=";
export const DELETE_LOCATION = "/v1/location-master/delete?location_id=";

export const STREET_List = "/v1/street-master";
export const ADD_STREET = "/v1/street-master";
export const UPDATE_STREET = "/v1/street-master/?streetOrRoadId=";
export const DELETE_STREET = "/v1/street-master/?streetOrRoadId=";

export const ELECTION_BOUNDRY_LIST = "/v1/electionBoundary-master";
export const ADD_ELECTION_BOUNDRY_LIST = "/v1/electionBoundary-master";
export const UPDATE_ELECTION_BOUNDRY_LIST =
  "/v1/electionBoundary-master/?electionBoundary_id=";
export const DELETE_ELECTION_BOUNDRY_LIST =
  "/v1/electionBoundary-master/?electionBoundary_id=";

export const USAGE_LIST = "/v1/usage-master";
export const ADD_USAGE = "/v1/usage-master";
export const UPDATE_USAGE = "/v1/usage-master/?usage_id=";
export const DELETE_USAGE = "/v1/usage-master/?usage_id=";
export const USAGE_LIST_GETONE = "/v1/usage-master/getOne";
export const OWNER_TYPE_LIST = "/v1/owner-type-master/";

export const PROPERTY_CLASS_LIST= "/v1/property-type-class"
export const ADD_PROPERTY_CLASS= "/v1/property-type-class/create"
export const UPDATE_PROPERTY_CLASS= "/v1/property-type-class/update?id="
export const DELETE_PROPERTY_CLASS= "/v1/property-type-class/delete?id="

export const PROPERTY_FLOOR_LIST= "/v1/floor-master"
export const ADD_PROPERTY_FLOOR= "/v1/floor-master/create"
export const UPDATE_PROPERTY_FLOOR= "/v1/floor-master/update?id="
export const DELETE_PROPERTY_FLOOR= "/v1/floor-master/delete?id="

export const USAGE_SUB_LIST = "/v1/usage-sub-master";
export const ADD_USAGE_SUB = "/v1/usage-sub-master";
export const UPDATE_USAGE_SUB = "/v1/usage-sub-master/?usageSub_id=";
export const DELETE_USAGE_SUB = "/v1/usage-sub-master/?usageSub_id=";
export const USAGE_SUB_GET_ONE = "/v1/usage-sub-master/getOne/?usageSub_id=";

export const AREA_List = "/v1/area-master";
export const ADD_AREA = "/v1/area-master";
export const UPDATE_AREA = "/v1/area-master/?area_id=";
export const DELETE_AREA = "/v1/area-master/?area_id=";

export const PROPERTYTYPE_List = "/v1/property-type-master";
export const PROPERTYSTATS = "/v1/dashboard/total-property-type-stats";

export const ADD_PROPERTYTYPE = "/v1/property-type-master";
export const UPDATE_PROPERTYTYPE = "/v1/property-type-master/?propertyType_id=";
export const DELETE_PROPERTYTYPE = "/v1/property-type-master/?propertyType_id=";

export const PROPERTYSUBTYPE_List = "/v1/property-sub-type-master";
export const ADD_PROPERTYSUBTYPE = "/v1/property-sub-type-master";
export const UPDATE_PROPERTYSUBTYPE =
  "/v1/property-sub-type-master/?propertySub_id=";
export const DELETE_PROPERTYSUBTYPE =
  "/v1/property-sub-type-master/?propertySub_id=";

export const CONSTRUCTION_CLASS_LIST = "/v1/construction-class";
export const ADD_CONSTRUCTION_CLASS = "/v1/construction-class";
export const UPDATE_CONSTRUCTION_CLASS =
  "/v1/construction-class/?constructionClass_id=";
export const DELETE_CONSTRUCTION_CLASS =
  "/v1/construction-class/?constructionClass_id=";
export const CONSTRUCTION_CLASS_GET_ONE =
  "/v1/construction-class/getOne/?constructionClass_id=";

export const ADMINISTRATIVE_BOUNDARY_LIST = "/v1/adminstrativeBoundary-master";
export const ADD_ADMINISTRATIVE_BOUNDARY = "/v1/adminstrativeBoundary-master";
export const UPDATE_ADMINISTRATIVE_BOUNDARY =
  "/v1/adminstrativeBoundary-master/?adminstrativeBoundary_id=";
export const DELETE_ADMINISTRATIVE_BOUNDARY =
  "/v1/adminstrativeBoundary-master/?adminstrativeBoundary_id=";
export const ADMINISTRATIVE_BOUNDARY_GET_ONE =
  "/v1/adminstrativeBoundary-master/?adminstrativeBoundary_id=";

export const ADD_PROPERTY_REGISTRATION = "/v1/property-master";
export const UPDATE_PROPERTY_REGISTRATION =
  "/v1/property-master/?property_master_id=";
export const GET_PROPERTY_DETAILS = "/v1/property-master/search?search=";
export const GET_PROPERTY_DETAILS_BYNAME = "/v1/property-master/search?";

export const PROPERTY_LIST = "/v1/property-master/getAll";
export const GET_SINGLE_PROPERTY =
  "/v1/property-master/getOne";
export const DELETE_SINGLE_PROPERTY =
  "/v1/property-master/delete-property?property_Id=";
  export const GET_SINGLE_PROPERTY_BY_NUMBER =
  "/v1/property-master/getOne";

export const GET_ALL_COLLECTOR ="/v1/collector-master"

// Tax Generation APIs
export const GET_REASSESSMENT_RANGE = "/v1/reassessment-range"
export const GET_FINANCIAL_YEARS = "/v1/financialMaster"
export const PROCESS_MILKAT_KAR = "/v1/milkatKar/processMilakatKarAkarni"
export const PROCESS_WARSHIK_KAR = "/v1/warshikKar/processWarshikKarAkarni"

// Add these endpoints for Milkat Kar operations
export const GET_MILKAT_KAR_AKARNI = "/v1/milkatKar/getMilkatKarAkarni";
export const PROCESS_MILKAT_KAR_AKARNI = "/v1/milkatKar/processMilakatKarAkarni";
export const GET_NAMUNA_EIGHT_BILL = "/v1/billing/get-bill-namuna-eight";
export const GET_WARD_WISE_MILKAT_KAR_STATUS = "/v1/milkatKar/getWardWiseMilkatKarStatus";

// Namuna Nine (Varshik Kar Akarni) APIs
export const GET_NAMUNA_NINE = "/v1/annual-kar-akarani/getVarshikKarAkarni";
export const PROCESS_WARSHIK_KAR_AKARNI = "/v1/annual-kar-akarani/processWarshikKarAkarni";
export const UPDATE_WARSHIK_KAR_AKARNI = "/v1/annual-kar-akarani/updateWarshikKarAkarni";
export const GET_WARD_WISE_WARSHIK_KAR_STATUS = "/v1/annual-kar-akarani/getWardWiseWarshikKarStatus";



export const GET_ALL_REGISTERNUMBER_LIST = "/v1/register-number";
