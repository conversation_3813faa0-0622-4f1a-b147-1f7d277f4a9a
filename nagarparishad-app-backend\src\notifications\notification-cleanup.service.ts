import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { NotificationsService } from './notifications.service';

@Injectable()
export class NotificationCleanupService {
  private readonly logger = new Logger(NotificationCleanupService.name);

  constructor(
    private readonly notificationsService: NotificationsService,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupExpiredNotifications() {
    this.logger.log('Starting cleanup of expired notifications...');
    
    try {
      await this.notificationsService.cleanupExpiredNotifications();
      this.logger.log('Expired notifications cleanup completed successfully');
    } catch (error) {
      this.logger.error('Error during expired notifications cleanup:', error);
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async logStorageStats() {
    try {
      const stats = this.notificationsService.getConnectionStats();
      this.logger.log(`Notification system stats: ${JSON.stringify(stats)}`);
    } catch (error) {
      this.logger.error('Error getting notification stats:', error);
    }
  }
}
