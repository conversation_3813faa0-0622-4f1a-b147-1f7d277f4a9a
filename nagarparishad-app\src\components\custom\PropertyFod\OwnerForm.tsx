import React, { useState } from "react";
import { useF<PERSON>, FormProvider } from "react-hook-form";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslation } from "react-i18next";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useOwnerTypeController } from "@/controller/master/OwnerTypeController";

const AddOwnerForm = ({ onClose, onSubmit, ownerDetails }) => {
  const [isMarried, setIsMarried] = useState(false);
  const { t } = useTranslation();
  const methods = useForm();
  const { control, handleSubmit, reset } = methods;

  const onFormSubmit = (data) => {
    // Generate a unique ID for the new owner
    const newOwnerId = `new_owner_${Math.random().toString(36).substr(2, 9)}`;
    const ownerData = {
      ...data,
      property_owner_details_id: newOwnerId,
    };
    onSubmit(ownerData);
    reset();
    onClose();
  };
  const { ownerTypeList } = useOwnerTypeController();

  const ownerTypeMap = Object.fromEntries(
    ownerTypeList.map((ownerType) => [
      ownerType.owner_type_id,
      ownerType.owner_type,
    ])
  );
  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] bg-white">
        <DialogHeader>
          <DialogTitle>{t("Add New Owner")}</DialogTitle>
        </DialogHeader>
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onFormSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-y-3 gap-x-6 px-3 pt-4">
              <div className="grid-cols-subgrid">
                <FormField
                  control={control}
                  name="owner_type"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {" "}
                        {t("assetDetailsForm.ownerType")}
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value);
                          }}
                          value={field.value}
                          {...field}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue
                              placeholder={t("assetDetailsForm.ownerType")}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {ownerTypeList.map((ownerType) => (
                                <SelectItem
                                  key={ownerType.owner_type_id}
                                  value={ownerType.owner_type_id}
                                >
                                  {ownerType.owner_type}
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      {methods.formState.errors.owner_type && (
                        <FormMessage className="ml-1">
                          Select Owner Type
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={control}
                  name="owner_name"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {" "}
                        संपूर्ण नाव{" "}
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="flex items-center mt-1 ">
                          <div className="w-[80px]">
                            <select
                              className="block w-full h-10 px-2 py-1 border border-[#0000006b]  rounded-l-md text-[13px] "
                              onChange={(e) => {
                                // Handle select change
                              }}
                            >
                              <option value="Mr">श्री</option>
                              <option value="Mrs">श्रीमती</option>
                            </select>
                          </div>
                          <Input
                            type="text"
                            className="rounded-l-none"
                            placeholder={t("enterFullName")}
                            {...field}
                          />
                        </div>
                      </FormControl>
                      {methods.formState.errors.owner_name && (
                        <FormMessage className="ml-1">
                          Enter Name
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={control}
                  name="mobile_number"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {" "}
                        {t("assetDetailsForm.mobileNumber")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          transliterate={false}
                          type="text"
                          placeholder={t("assetDetailsForm.mobileNumber")}
                          {...field}
                        />
                      </FormControl>
                      {methods.formState.errors.mobile_number && (
                        <FormMessage className="ml-1">
                          Enter Mobile Number
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={control}
                  name="email_id"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel> {t("assetDetailsForm.email")}</FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          type="text"
                          transliterate={false}
                          placeholder={t("assetDetailsForm.email")}
                          {...field}
                        />
                      </FormControl>
                      {methods.formState.errors.email_id && (
                        <FormMessage className="ml-1">
                          Enter Email ID
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={control}
                  name="aadhar_number"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {t("assetDetailsForm.adharNumber")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          transliterate={false}
                          type="text"
                          placeholder={t("assetDetailsForm.adharNumber")}
                          {...field}
                          onKeyDown={(e) => {
                            if (e.keyCode === 8) {
                              let value = e.target.value?.replace(
                                /\D/g,
                                ""
                              );
                              if (
                                value.length > 1 &&
                                value.length % 4 === 1
                              ) {
                                value = value.substring(
                                  0,
                                  value.length - 1
                                );
                              }
                              field.onChange(value);
                            }
                          }}
                          onChange={(e) => {
                            let value = e.target.value
                              ?.replace(/\D/g, "")
                              .substring(0, 12);
                            const hasDash = value.includes("-");
                            if (hasDash) {
                              value = value
                                ?.replace(/(.{4})/g, "$1-")
                                .slice(0, 14);
                            } else {
                              value = value
                                ?.replace(/(.{4})/g, "$1-")
                                .slice(0, 14);
                            }
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      {methods.formState.errors.aadhar_number && (
                        <FormMessage className="ml-1">
                          Enter Aadhar Number
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid-cols-subgrid">
                <FormField
                  control={control}
                  name="pan_card"
                  render={({ field }) => (
                    <FormItem className=" ">
                      <FormLabel>
                        {" "}
                        {t("assetDetailsForm.panNumber")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="mt-1 block w-full"
                          transliterate={false}
                          type="text"
                          placeholder={t("assetDetailsForm.panNumber")}
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value.toUpperCase();
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      {methods.formState.errors.pan_card && (
                        <FormMessage className="ml-1">
                          Enter PAN Number
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid-cols-subgrid">
                <FormField
                  control={control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem className="">
                      <FormLabel>
                        {" "}
                        {t("assetDetailsForm.gender")}
                        <span className="ml-1 text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={(value) => field.onChange(value)}
                          defaultValue={field.value}
                          {...field}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue
                              placeholder={t("assetDetailsForm.gender")}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="MALE">Male</SelectItem>
                              <SelectItem value="FEMALE">Female</SelectItem>
                              <SelectItem value="OTHER">Other</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      {methods.formState.errors.gender && (
                        <FormMessage className="ml-1">
                          Select Gender
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              {/* <div className="grid-cols-subgrid">
                <FormField
                  control={control}
                  name="marital_status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>विवाह स्थिती</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={(value) => {
                            const married = value === "yes";
                            field.onChange(value);
                            setIsMarried(married);
                            if (!married) {
                              methods.setValue("partner_name", "");
                            }
                          }}
                          value={field.value}
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="yes" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              विवाहित
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="no" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              अविवाहित
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      {methods.formState.errors.marital_status && (
                        <FormMessage className="ml-1">
                          {t("errorsRequiredField")}
                        </FormMessage>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              {isMarried && (
                <div className="grid-cols-subgrid col-span-2">
                  <FormField
                    control={control}
                    name="partner_name"
                    render={({ field }) => (
                      <FormItem className=" ">
                        <FormLabel>पती/पत्नी चे नाव</FormLabel>
                        <FormControl>
                          <div className="flex items-center mt-1">
                            <div className="w-[80px]">
                              <select
                                className="block w-full h-10 px-2 py-1 border border-[#0000006b]  rounded-l-md text-[13px]"
                                onChange={(e) => {
                                  // Handle select change
                                }}
                              >
                                <option value="Mr">श्री</option>
                                <option value="Mrs">श्रीमती</option>
                              </select>
                            </div>
                            <Input
                              type="text"
                              className="rounded-l-none"
                              placeholder={t("enterFullName")}
                              {...field}
                              value={field.value}
                            />
                          </div>
                        </FormControl>
                        {methods.formState.errors.partner_name && (
                          <FormMessage className="ml-1">
                            Enter Name
                          </FormMessage>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
              )} */}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={onClose}>
                {t("cancel")}
              </Button>
              <Button type="submit">{t("submit")}</Button>
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
};

export default AddOwnerForm;
