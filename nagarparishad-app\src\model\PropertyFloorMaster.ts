export interface PropertyFloorMasterObject {
    floor_id: string;
    floor_name: string;
    createdAt?: string;
    updatedAt?: string;
    deletedAt?: string | null;
  }
  
  export interface PropertyFloorMasterListApiOject {
    floor_id: string;
    floor_name: string;
  }
  
  export interface PropertyFloorMasterListAllApi {
    statusCode: number;
    message: string;
    data: PropertyFloorMasterObject[];
  }
  
  export interface PropertyFloorCreateApi {
    statusCode: number; //201
    message: string;
    data: PropertyFloorMasterObject;
  }
  export interface PropertyFloorUpdateApi {
    statusCode: number; //200
    message: string;
  }
  
  export interface PropertyFloorSendApiObj {
    floor_name: string;
  }
  