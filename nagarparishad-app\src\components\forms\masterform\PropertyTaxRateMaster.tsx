import React, { useContext, useRef, useState } from "react";
import WhiteContainer from "@/components/custom/WhiteContainer";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import TanStackTable from "@/components/globalcomponent/tanstacktable";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { MasterObjectColumnDef } from "@/model/zone-master";
import { MASTER } from "@/constant/config/api.config";
// import { usePropertyTaxRateMasterController } from "@/controller/master/PropertyTaxRateMasterController";
import { SquarePen } from "lucide-react";
import { Trash2 } from "lucide-react";
import { GlobalContext } from "@/context/GlobalContext";
import { toast } from "@/components/ui/use-toast";
import Api from "@/services/ApiServices";
import DeletePopUpScreen from "@/components/custom/DeletePopUpScreen/DeletePopUpScreen";
// import PropertyTaxRateMasterForm from "@/components/forms/PropertyTaxRateForm";
import AddNewBtn from "../../globalcomponent/AddNewForm";
import PropertyTaxRateForm from "../PropertyTaxRateForm";
import { usePermissions } from "@/context/PermissionContext";
import { Action, FormName, ModuleName } from "@/constant/enums/permissionEnum";

const PropertyTaxRateMaster = () => {
  const {
    setOpen,
    setRefreshPropertyTaxList,
    refreshPropertyTaxList,
    setMasterComponent,
  } = useContext(GlobalContext);
  const { t } = useTranslation();
  const userRef = useRef(null);

  const { canPerformAction } = usePermissions();
  const canRead = canPerformAction(ModuleName.Master, FormName.TaxRate, Action.CanRead);
  const canUpdate = canPerformAction(ModuleName.Master, FormName.TaxRate, Action.CanUpdate);
  const CanCreate = canPerformAction(ModuleName.Master, FormName.TaxRate, Action.CanCreate);
  const CanDelete = canPerformAction(ModuleName.Master, FormName.TaxRate, Action.CanDelete);

  const columns: ColumnDef<MasterObjectColumnDef>[] = [
    // Define your columns here
  ];

  function handleEdit(item: any): void {
    setOpen(true);
    setMasterComponent(
      <PropertyTaxRateForm
        btnTitle={"propertytaxrate.updateBtn"}
        editData={item && item}
      />,
    );
  }

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any | null>(null);

  function handleDelete(item: any): void {
    setSelectedItem(item);
    setIsDeleteOpen(true);
  }

  const handleConfirmDelete = () => {
    if (selectedItem) {
      // Api.deletePropertyTaxRate(
      //   selectedItem.propertyTaxRate_id,
      //   (response: { status: boolean; data: any }) => {
      //     if (response.status && response.data.statusCode === 200) {
      //       toast({
      //         title: response.data.message,
      //       });
      //       setRefreshPropertyTaxRateList(!refreshPropertyTaxRateList);
      //     } else {
      //       toast({
      //         title: response.data.message,
      //       });
      //     }
      //   }
      // );
    }

    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  const handleCancelDelete = () => {
    setIsDeleteOpen(false);
    setSelectedItem(null);
  };

  // const { propertyTaxRateList } = usePropertyTaxRateMasterController();
  const MasterType: string = MASTER.PROPERTYTAXRATE;

  return (
    <>
      <div className="flex h-fit  ">
        <div className=" w-full mx-auto px-4 sm:px-6 lg:px-5 py-6  ">
          <p className="w-full flex items-center justify-between ml-2  text-[18px] font-semibold mb-2">
            {t("propertytaxrate.formTitle")}
          </p>
          {CanCreate && <WhiteContainer>
            {MasterType && <AddNewBtn masterType={MASTER.PROPERTYTAXRATE} />}
          </WhiteContainer>}
          <WhiteContainer>
            <TanStackTable
              columns={columns}
              data={[]}
              masterType={MASTER.PROPERTYTAXRATE}
              searchKey={"searchPropertyTaxRate"}
              searchColumn={"propertyTaxRateName"}
            />
          </WhiteContainer>
        </div>
      </div>
      {isDeleteOpen && selectedItem && (
        <DeletePopUpScreen
          isOpen={isDeleteOpen}
          toggle={handleCancelDelete}
          itemName={""}
          onDelete={handleConfirmDelete}
        />
      )}
    </>
  );
};

export default PropertyTaxRateMaster;
