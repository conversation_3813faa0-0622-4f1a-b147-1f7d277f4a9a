import { Query } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Property_Usage_Details_Entity } from '../entities';
import { InjectRepository } from '@nestjs/typeorm';

export class Property_Usage_DetailsRepository extends Repository<Property_Usage_Details_Entity> {
  constructor(
    @InjectRepository(Property_Usage_Details_Entity)
    private readonly property_Usage_DetailsRepository: Repository<Property_Usage_Details_Entity>,
  ) {
    super(
      property_Usage_DetailsRepository.target,
      property_Usage_DetailsRepository.manager,
      property_Usage_DetailsRepository.queryRunner,
    );
  }
  async saveData(input) {
    
    // Prepare the input object for insertion
    const finaldata = await this.property_Usage_DetailsRepository
      .createQueryBuilder()
      .insert()
      .into(Property_Usage_Details_Entity) // Specify the entity class
      .values({
        property: input.property, // Ensure foreign keys are set
        propertyType: { propertyType_id: input.propertyType },
        usageType: { usage_type_id: input.usageType },
        usageSubType: { usage_sub_type_master_id: input.usageSubType },
      })
      .returning([
        'property_usage_details_id',
        'authorized',
        'created_at',
        'updated_at',
        'deleted_at',
        'property_id', // Add foreign key fields to returning array
        'propertyType_id',
        'usage_type_id',
        'usage_sub_type_master_id',
      ])
      .execute();

        return finaldata;
  }

  async getFloorTypeName(propertyUsageDetailsId: string) {
    const result = await this.property_Usage_DetailsRepository
      .createQueryBuilder('property_usage_details')
      .leftJoinAndSelect('property_usage_details.floorType', 'floor')
      .select([
        'property_usage_details.property_usage_details_id',
        'floor.floor_name',
      ])
      .where('property_usage_details.property_usage_details_id = :id', {
        id: propertyUsageDetailsId,
      })
      .getOne();

    return result?.floorType?.floor_name || null;
  }

  // async saveDataOne(input){

  //   const data=this.property_Usage_DetailsRepository
  //   property: { property_id: input.property }, // Ensure foreign keys are set
  //   propertyType: { propertyType_id: input.propertyType },
  //   usageType: { usage_type_id: input.usageType },
  //   usageSubType: { usage_sub_type_master_id: input.usageSubType },
  // }

  async findOneById(id: string): Promise<Property_Usage_Details_Entity | null> {
    return await this.property_Usage_DetailsRepository
      .createQueryBuilder('property_usage_details')
      .leftJoinAndSelect('property_usage_details.property', 'property')
      .leftJoinAndSelect('property_usage_details.propertyType', 'propertyType')
      .leftJoinAndSelect('property_usage_details.usageType', 'usageType')
      .leftJoinAndSelect('property_usage_details.usageSubType', 'usageSubType')
      .leftJoinAndSelect('property_usage_details.floorType', 'floor')
      .where('property_usage_details.property_usage_details_id = :id', { id })
      .getOne();
  }

  async findTotalForUsageType(usageType: string): Promise<number> {
    const result = await this.createQueryBuilder('property_usage_details')
      .select(
        'COUNT(property_usage_details.property_usage_details_id)',
        'total',
      )
      .leftJoin('property_usage_details.usageType', 'usageType')
      .where('usageType.usage_type_id = :usageType', { usageType })
      .andWhere('property_usage_details.deleted_at IS NULL')
      .andWhere('property_usage_details.property IS NOT NULL')

      .getRawOne();

    return result ? parseInt(result.total, 10) : 0;
  }

  async findTotalUsageTypeWise(): Promise<
    { usage_type: string; total: number }[]
  > {
    return await this.createQueryBuilder('property_usage_details')
      .select('usageType.usage_type', 'usage_type')
      .addSelect(
        'COUNT(property_usage_details.property_usage_details_id)',
        'total',
      )
      .leftJoin('property_usage_details.usageType', 'usageType')
      .where('property_usage_details.deleted_at IS NULL')
      .andWhere('property_usage_details.property IS NOT NULL')

      .groupBy('usageType.usage_type')
      .getRawMany();
  }
}
