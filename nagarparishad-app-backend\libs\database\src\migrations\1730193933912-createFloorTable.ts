import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateFloorTable1730193933912 implements MigrationInterface {
    name = 'CreateFloorTable1730193933912'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "floor_master" ("floor_id" uuid NOT NULL DEFAULT uuid_generate_v4(), "floor_name" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_2c1320652da1e8e644e8c02329c" PRIMARY KEY ("floor_id"))`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" ADD "floor_id" uuid`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" ADD CONSTRAINT "FK_58106260344086e97920e99a919" FOREIGN KEY ("floor_id") REFERENCES "floor_master"("floor_id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property_usage_details" DROP CONSTRAINT "FK_58106260344086e97920e99a919"`);
        await queryRunner.query(`ALTER TABLE "property_usage_details" DROP COLUMN "floor_id"`);
        await queryRunner.query(`DROP TABLE "floor_master"`);
    }

}
