import { MigrationInterface, QueryRunner } from "typeorm";

export class PropertyOwnerAddColRemark1728139459381 implements MigrationInterface {
    name = 'PropertyOwnerAddColRemark1728139459381'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property_owner_details" ADD "remark" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "property_owner_details" DROP COLUMN "remark"`);
    }

}
